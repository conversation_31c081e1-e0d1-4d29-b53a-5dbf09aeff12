package com.ruoyi;

import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.system.service.antiblock.AntiBlockBizUrlGenerator;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


/**
 * HTTP状态码测试
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@SpringBootTest(classes = RuoYiApplication.class)
public class HttpStatusTest {

    @Autowired
    private AntiBlockBizUrlGenerator antiBlockBizUrlGenerator;

    @Test
    public void testHttpStatusConstants() {
        System.out.println("=== HTTP状态码常量测试 ===");

        // 2xx 成功状态码
        System.out.println("SUCCESS (200): " + HttpStatus.SUCCESS);
        System.out.println("CREATED (201): " + HttpStatus.CREATED);
        System.out.println("ACCEPTED (202): " + HttpStatus.ACCEPTED);
        System.out.println("NO_CONTENT (204): " + HttpStatus.NO_CONTENT);

        // 3xx 重定向状态码
        System.out.println("MOVED_PERM (301): " + HttpStatus.MOVED_PERM);
        System.out.println("FOUND (302): " + HttpStatus.FOUND);
        System.out.println("SEE_OTHER (303): " + HttpStatus.SEE_OTHER);
        System.out.println("NOT_MODIFIED (304): " + HttpStatus.NOT_MODIFIED);
        System.out.println("TEMPORARY_REDIRECT (307): " + HttpStatus.TEMPORARY_REDIRECT);

        // 4xx 客户端错误状态码
        System.out.println("BAD_REQUEST (400): " + HttpStatus.BAD_REQUEST);
        System.out.println("UNAUTHORIZED (401): " + HttpStatus.UNAUTHORIZED);
        System.out.println("FORBIDDEN (403): " + HttpStatus.FORBIDDEN);
        System.out.println("NOT_FOUND (404): " + HttpStatus.NOT_FOUND);
        System.out.println("BAD_METHOD (405): " + HttpStatus.BAD_METHOD);
        System.out.println("CONFLICT (409): " + HttpStatus.CONFLICT);
        System.out.println("UNSUPPORTED_TYPE (415): " + HttpStatus.UNSUPPORTED_TYPE);
        System.out.println("TOO_MANY_REQUESTS (429): " + HttpStatus.TOO_MANY_REQUESTS);

        // 5xx 服务器错误状态码
        System.out.println("ERROR (500): " + HttpStatus.ERROR);
        System.out.println("NOT_IMPLEMENTED (501): " + HttpStatus.NOT_IMPLEMENTED);
        System.out.println("SERVICE_UNAVAILABLE (503): " + HttpStatus.SERVICE_UNAVAILABLE);

        // 验证状态码值
        assert HttpStatus.SUCCESS == 200;
        assert HttpStatus.FOUND == 302;
        assert HttpStatus.TEMPORARY_REDIRECT == 307;
        assert HttpStatus.BAD_REQUEST == 400;
        assert HttpStatus.FORBIDDEN == 403;
        assert HttpStatus.TOO_MANY_REQUESTS == 429;
        assert HttpStatus.ERROR == 500;
        assert HttpStatus.SERVICE_UNAVAILABLE == 503;

        System.out.println("✅ 所有HTTP状态码常量验证通过");
    }

    @Test
    public void testAntiBlockRequiredStatusCodes() {
        System.out.println("\n=== 防风控跳链所需状态码测试 ===");

        // 防风控跳链系统使用的状态码
        int[] requiredCodes = {
            HttpStatus.FOUND,              // 302 - 入口和最终跳转
            HttpStatus.TEMPORARY_REDIRECT, // 307 - 协议切换
            HttpStatus.BAD_REQUEST,        // 400 - 参数错误
            HttpStatus.FORBIDDEN,          // 403 - URL不被允许
            HttpStatus.TOO_MANY_REQUESTS,  // 429 - 访问频率超限
            HttpStatus.ERROR,              // 500 - 内部错误
            HttpStatus.SERVICE_UNAVAILABLE // 503 - 服务不可用
        };

        String[] codeNames = {
            "FOUND (302)",
            "TEMPORARY_REDIRECT (307)",
            "BAD_REQUEST (400)",
            "FORBIDDEN (403)",
            "TOO_MANY_REQUESTS (429)",
            "ERROR (500)",
            "SERVICE_UNAVAILABLE (503)"
        };

        for (int i = 0; i < requiredCodes.length; i++) {
            System.out.println("✅ " + codeNames[i] + ": " + requiredCodes[i]);
        }

        System.out.println("✅ 防风控跳链所需的所有状态码都已定义");
    }

    @Test
    public void test() {
        // 测试新的generateDirectEntryUrl方法（baseUrl从配置文件中自动获取）
        String s = antiBlockBizUrlGenerator.generateDirectEntryUrl("https://mp.weixin.qq.com/s/s2JVwyhbsMQnhMf4cOWnGQ", true
        );
        System.out.println("生成的直接跳转URL: " + s);
    }
}
