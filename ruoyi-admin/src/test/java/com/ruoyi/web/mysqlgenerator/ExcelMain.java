package com.ruoyi.web.mysqlgenerator;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2022/6/1 5:49 下午
 */
public class ExcelMain {
    public static void main(String[] args) {
        // 处理后的Excel文件
        BigExcelWriter writer= ExcelUtil.getBigWriter("未转化样本（数牍）_MD5.xlsx");
        writer.write(Collections.singletonList(Arrays.asList("手机号MD5")));

        // 读取Excel文件，处理并写入新的文件
        ExcelUtil.readBySax(FileUtil.file("/Users/<USER>/Desktop/未转化样本（数牍）.xlsx"), 0,
                (sheetIndex, rowIndex, rows) -> {
                    String phone = rows.get(0).toString().trim();
                    if (StrUtil.isNumeric(phone) && phone.length() == 11) {
                        writer.write(Collections.singletonList(SecureUtil.md5(phone)));
                    }
                });

        writer.close();

    }
}
