package com.ruoyi.web.mysqlgenerator;

//import cn.com.nuohe.generator.MybatisGenerator;
//import cn.com.nuohe.generator.config.GeneratorConfig;
//
//import java.io.File;

/**
 * <AUTHOR>
 * @date 2022/3/7 5:43 下午
 */
public class MyBatisGenerator {
    public static void main(String[] args) throws Exception {
        //数据库配置
//        String DB_URL = "********************************************************************************************************************************************************************************************************";
//        String user = "nh_test_acount";
//        String pwd = "nH_tEst_2023";
//
//        GeneratorConfig config = new GeneratorConfig(DB_URL, user, pwd);
//        config.setAuthorName("xingyan");
//        //mapper文件生成的位置
//        config.setDaoPackage("com.ruoyi.system.mapper");
//        //entity实体生成的位置
//        config.setEntityPackage("com.ruoyi.system.entity");
//        config.setPackageName("system");
//        config.setProjectName("ruoyi");
//        //数据库名称
//        config.setTableSchema("ssp");
//        //表名
//        config.setTableName("sys_config_domain");
//        //表名前缀
//        config.setTablePrefix("tb_");
//        config.setServicePackage("com.ruoyi.system.service");
//
//        config.setXmlLocation( "ruoyi-system"+ File.separator +"src/main/resources/mapper");
//        //设置生成的java文件存放的模块名,如果该代码执行和生成的代码在一个模块下则不需要设置,
//        //比如该执行代码在nh-oa-deploy模块下,需要生成的entity,mapper需要在nh-oa-biz模块下,则需要设置ModuleName的值为nh-oa-biz
//        //如果该执行代码在nh-oa-biz下,并且生成的代码也在nh-oa-biz下则不需要设置ModuleName的值
//
//
//        MybatisGenerator.genCode(config);
    }
}
