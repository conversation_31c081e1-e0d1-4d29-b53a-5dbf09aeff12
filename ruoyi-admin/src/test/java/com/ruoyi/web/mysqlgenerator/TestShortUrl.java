package com.ruoyi.web.mysqlgenerator;

import cn.hutool.core.util.RadixUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;

/**
 * @author: keboom
 * @date: 2025/5/19
 */
public class TestShortUrl {
    private static final String BASE62_ALPHABET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    public static void main(String[] args) {

        // [238328, 14776335]

//        for (long i = 238328; i <= 14776335; i++) {
//            String shortUrl = getShortUrl(i);
//            if (shortUrl.length() != 4) {
//                System.out.println(shortUrl);
//            }
//        }

//        String encode = RadixUtil.encode(BASE62_ALPHABET, 1995303);

        long decode = RadixUtil.decode(BASE62_ALPHABET, "adcsds123sss");

        String s = generateDirectEntryUrl("http://localhost:8778/", "https://mp.weixin.qq.com/s/yeU3dUuRXyJCQVXz1h6Gcw");
        System.out.println(s);
    }


    private static String getShortUrl(Long id) {
//        String shortUrlPrefix = "https://t.chvqk.cn/";
        String shortUrlPrefix = "";
        if (id == null || id < 0) {
            // Or handle as an error, depending on requirements.
            // Returning empty or throwing an exception might be appropriate.
            // For now, let's assume id is always positive for encoding.
            // If id can be 0, RadixUtil.encode handles it (usually results in the first char of alphabet).
        }
        String encodedId = RadixUtil.encode(BASE62_ALPHABET, id);
        return shortUrlPrefix + encodedId;
    }

    public static String generateDirectEntryUrl(String baseUrl, String targetUrl) {
        if (StringUtils.isBlank(baseUrl) || StringUtils.isBlank(targetUrl)) {
            throw new IllegalArgumentException("baseUrl和targetUrl不能为空");
        }

        try {
            // 移除baseUrl末尾的斜杠
            String cleanBaseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;

            // 根据encrypt参数决定是否加密targetUrl
            String processedTargetUrl;
            processedTargetUrl = targetUrl;

            // 构建URL
            String generatedUrl = cleanBaseUrl + "/antiblock/entry?targetUrl=" +
                    java.net.URLEncoder.encode(processedTargetUrl, "UTF-8");
            return generatedUrl;
        } catch (Exception e) {
            throw new RuntimeException("生成跳转链接失败", e);
        }
    }



}
