//package com.ruoyi;
//
//import com.ruoyi.common.core.redis.RedisCache;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.concurrent.TimeUnit;
//
///**
// * Redis缓存方法测试
// *
// * <AUTHOR>
// * @date 2025/07/29
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class RedisCacheMethodTest {
//
//    @Autowired
//    private RedisCache redisCache;
//
//    @Test
//    public void testIncrCacheValue() {
//        String testKey = "test:antiblock:incr:" + System.currentTimeMillis();
//
//        System.out.println("=== Redis自增方法测试 ===");
//        System.out.println("测试键: " + testKey);
//
//        try {
//            // 测试自增方法
//            Long result1 = redisCache.incrCacheValue(testKey);
//            System.out.println("第一次自增结果: " + result1);
//
//            Long result2 = redisCache.incrCacheValue(testKey);
//            System.out.println("第二次自增结果: " + result2);
//
//            Long result3 = redisCache.incrCacheValue(testKey);
//            System.out.println("第三次自增结果: " + result3);
//
//            // 验证结果
//            assert result1 == 1L : "第一次自增应该返回1";
//            assert result2 == 2L : "第二次自增应该返回2";
//            assert result3 == 3L : "第三次自增应该返回3";
//
//            // 测试设置过期时间
//            redisCache.expire(testKey, 10, TimeUnit.SECONDS);
//            Long expireTime = redisCache.getExpire(testKey);
//            System.out.println("设置过期时间: " + expireTime + "秒");
//
//            assert expireTime > 0 && expireTime <= 10 : "过期时间应该在0-10秒之间";
//
//            System.out.println("✅ Redis自增方法测试通过");
//
//        } finally {
//            // 清理测试数据
//            redisCache.deleteObject(testKey);
//            System.out.println("清理测试数据: " + testKey);
//        }
//    }
//
//    @Test
//    public void testAntiBlockRedisOperations() {
//        String prefix = "test:antiblock:" + System.currentTimeMillis() + ":";
//
//        System.out.println("\n=== 防风控跳链Redis操作测试 ===");
//
//        try {
//            // 模拟访问统计
//            String statsKey = prefix + "stats:entry";
//            Long count1 = redisCache.incrCacheValue(statsKey);
//            Long count2 = redisCache.incrCacheValue(statsKey);
//            Long count3 = redisCache.incrCacheValue(statsKey);
//
//            System.out.println("访问统计测试:");
//            System.out.println("  第1次访问: " + count1);
//            System.out.println("  第2次访问: " + count2);
//            System.out.println("  第3次访问: " + count3);
//
//            // 模拟频率限制
//            String rateLimitKey = prefix + "rate:requests:192.168.1.1";
//            Long req1 = redisCache.incrCacheValue(rateLimitKey);
//            Long req2 = redisCache.incrCacheValue(rateLimitKey);
//            redisCache.expire(rateLimitKey, 60, TimeUnit.SECONDS);
//
//            System.out.println("频率限制测试:");
//            System.out.println("  第1次请求: " + req1);
//            System.out.println("  第2次请求: " + req2);
//
//            // 模拟URL计数
//            String urlCountKey = prefix + "rate:urlcount:192.168.1.1";
//            redisCache.setCacheObject(prefix + "rate:url:192.168.1.1:123456", "1", 60, TimeUnit.SECONDS);
//            Long urlCount = redisCache.incrCacheValue(urlCountKey);
//            redisCache.expire(urlCountKey, 60, TimeUnit.SECONDS);
//
//            System.out.println("URL计数测试:");
//            System.out.println("  唯一URL计数: " + urlCount);
//
//            // 验证结果
//            assert count3 == 3L : "访问统计应该为3";
//            assert req2 == 2L : "请求计数应该为2";
//            assert urlCount == 1L : "URL计数应该为1";
//
//            System.out.println("✅ 防风控跳链Redis操作测试通过");
//
//        } finally {
//            // 清理测试数据
//            redisCache.deleteObject(prefix + "stats:entry");
//            redisCache.deleteObject(prefix + "rate:requests:192.168.1.1");
//            redisCache.deleteObject(prefix + "rate:urlcount:192.168.1.1");
//            redisCache.deleteObject(prefix + "rate:url:192.168.1.1:123456");
//            System.out.println("清理测试数据完成");
//        }
//    }
//
//    @Test
//    public void testRedisMethodsExist() {
//        System.out.println("\n=== Redis方法存在性测试 ===");
//
//        // 验证所需的Redis方法都存在
//        try {
//            // 测试incrCacheValue方法
//            String testKey = "test:method:check:" + System.currentTimeMillis();
//            Long result = redisCache.incrCacheValue(testKey);
//            System.out.println("✅ incrCacheValue方法存在，返回值: " + result);
//
//            // 测试expire方法
//            boolean expireResult = redisCache.expire(testKey, 1, TimeUnit.SECONDS);
//            System.out.println("✅ expire方法存在，返回值: " + expireResult);
//
//            // 测试getCacheObject方法
//            Object cacheObject = redisCache.getCacheObject(testKey);
//            System.out.println("✅ getCacheObject方法存在，返回值: " + cacheObject);
//
//            // 测试setCacheObject方法
//            redisCache.setCacheObject(testKey + ":test", "testValue", 1, TimeUnit.SECONDS);
//            System.out.println("✅ setCacheObject方法存在");
//
//            // 测试deleteObject方法
//            boolean deleteResult = redisCache.deleteObject(testKey);
//            System.out.println("✅ deleteObject方法存在，返回值: " + deleteResult);
//
//            System.out.println("✅ 所有必需的Redis方法都存在且可用");
//
//        } catch (Exception e) {
//            System.err.println("❌ Redis方法测试失败: " + e.getMessage());
//            throw e;
//        }
//    }
//}
