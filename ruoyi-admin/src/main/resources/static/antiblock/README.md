# 防风控跳链系统

## 概述

防风控跳链系统是一套通过多域名跳转的方案，用于规避微信或其他平台的风控系统对链接的识别与封禁。系统采用非直链方式，通过多步跳转打散平台对目标内容的识别路径。

## 跳转链路设计

```
用户访问入口地址 → 302跳转 → HTTPS step1 → 307跳转 → HTTP step2 → 302跳转 → 目标链接
```

### 详细流程

1. **入口跳转** (`/antiblock/entry`)
   - 用户访问入口地址 `http://a.com/antiblock/entry`
   - 后端返回 302 状态码，跳转到 `https://b.com/antiblock/step1`

2. **协议切换** (`/antiblock/step1`)
   - 用户访问 `https://b.com/antiblock/step1`（HTTPS 协议）
   - 后端返回 307 临时重定向，跳转到 `http://b.com/antiblock/step2`
   - 使用 307 实现协议从 HTTPS 切换到 HTTP，扰乱平台的中间链路跟踪逻辑

3. **最终跳转** (`/antiblock/step2`)
   - 用户访问 `http://b.com/antiblock/step2`（HTTP 协议）
   - 后端返回 302 跳转至最终的微信文章链接（如 `https://mp.weixin.qq.com/s/xxx`）

## 功能特性

### 核心功能
- ✅ 多步骤跳转，打散识别路径
- ✅ 协议切换（HTTPS ↔ HTTP），增加跟踪难度
- ✅ 支持自定义中间跳转域名
- ✅ 智能域名选择，避免使用相同域名
- ✅ URL 编码/解码处理，确保特殊字符正确传递
- ✅ **AES加密**，目标URL加密传输，显著缩短链接长度
- ✅ **动态密钥**，支持基于时间窗口的密钥轮换

### 安全防护
- ✅ 目标域名白名单/黑名单控制
- ✅ 访问频率限制（IP 级别）
- ✅ 参数验证和安全检查
- ✅ 详细的访问日志记录

### 统计监控
- ✅ 实时访问统计
- ✅ 转化率分析
- ✅ 每小时访问量统计
- ✅ 管理后台查看
- ✅ 加密效果分析（压缩率统计）

### 加密特性
- 🔐 **AES-128加密算法**，确保URL内容安全
- 📏 **显著缩短URL长度**，平均压缩率60-80%
- 🔄 **支持动态密钥**，定期自动更换加密密钥
- 🛡️ **Base64 URL安全编码**，避免特殊字符问题
- ⚡ **高性能加密**，单次加密/解密耗时<1ms

## 使用方法

### 1. 业务代码方式

**固定地址访问：**
```http
# 微信公众号默认文章
GET /antiblock/entry/weixin

# 微信公众号科技文章
GET /antiblock/entry/weixin/tech

# 微信公众号新闻文章
GET /antiblock/entry/weixin/news

# 微信公众号生活方式文章
GET /antiblock/entry/weixin/lifestyle
```

**支持的业务代码：**
- `weixin` - 微信公众号文章

**支持的目标代码（targetCode）：**
- `tech` - 科技文章
- `news` - 新闻文章
- `lifestyle` - 生活方式文章
- `business` - 商业文章
- `education` - 教育文章

**参数说明：**
- `bizCode`: 业务代码，决定跳转到哪个平台（必填）
- `targetCode`: 目标代码，业务可根据此参数选择具体文章（可选）

**业务处理器架构：**
- 每个业务实现自己的处理器（`AntiBlockBizHandler`）
- 处理器根据请求信息（User-Agent、IP、时间等）自主决定跳转目标
- 支持复杂的业务逻辑，如智能推荐、个性化内容等
- 每个处理器可独立配置是否加密

### 2. 直接URL方式

**参数传递访问：**
```http
# 不加密跳转
GET /antiblock/entry?targetUrl=https://mp.weixin.qq.com/s/xxx

# 加密跳转
GET /antiblock/entry?targetUrl=ENC:加密后的内容
```

**参数说明：**
- `targetUrl`: 目标URL（必填，已经是处理过的最终形式）
  - 可以是原始URL：`https://mp.weixin.qq.com/s/xxx`
  - 可以是加密URL：`ENC:加密后的内容`

**智能域名选择：**
- 系统从配置的 `default-step-domains` 列表中自动选择域名
- 避免使用与入口相同的域名，提高防风控效果
- 基于时间窗口的哈希算法，确保域名选择相对稳定但又有变化
- 每5分钟切换一次域名选择策略，增加跟踪难度

### 3. 编程方式生成URL

```java
// 使用工具类生成业务URL（默认文章）
// 基础URL从配置文件的entry-base-url中自动选择
String entryUrl = antiBlockBizUrlGenerator.generateBizEntryUrl(
    "weixin"            // 业务代码
);
// 结果: https://domain1.example.com/antiblock/entry/weixin

// 使用工具类生成业务URL（指定目标代码）
String techUrl = antiBlockBizUrlGenerator.generateBizEntryUrl(
    "weixin",           // 业务代码
    "tech"              // 目标代码
);
// 结果: https://domain2.example.com/antiblock/entry/weixin/tech

// 生成直接URL跳转链接（不加密）
// 基础URL从配置文件的entry-base-url中自动选择
String directUrl = antiBlockBizUrlGenerator.generateDirectEntryUrl(
    "https://mp.weixin.qq.com/s/xxx",         // 目标URL
    false                                     // 不加密
);
// 结果: https://domain3.example.com/antiblock/entry?targetUrl=https%3A%2F%2Fmp.weixin.qq.com%2Fs%2Fxxx

// 生成直接URL跳转链接（加密）
String encryptedDirectUrl = antiBlockBizUrlGenerator.generateDirectEntryUrl(
    "https://mp.weixin.qq.com/s/xxx",         // 目标URL
    true                                      // 加密
);
// 结果: https://domain1.example.com/antiblock/entry?targetUrl=ENC%3A%E5%8A%A0%E5%AF%86%E5%90%8E%E7%9A%84%E5%86%85%E5%AE%B9
```

### 4. 自定义业务处理器

```java
@Component
public class CustomBizHandler implements AntiBlockBizHandler {

    @Override
    public String getBizCode() {
        return "custom";
    }

    @Override
    public String getBizName() {
        return "自定义业务";
    }

    @Override
    public String getTargetUrl(String targetCode, HttpServletRequest request) {
        // 根据targetCode和请求信息决定跳转目标
        String userAgent = request.getHeader("User-Agent");
        String clientIp = getClientIp(request);

        // 根据targetCode选择不同内容
        if (targetCode != null) {
            switch (targetCode) {
                case "mobile":
                    return "https://mobile.example.com/content";
                case "desktop":
                    return "https://www.example.com/content";
                default:
                    return "https://default.example.com/content";
            }
        }

        // 没有targetCode时的默认逻辑
        if (isMobileDevice(userAgent)) {
            return "https://mobile.example.com/content";
        } else {
            return "https://www.example.com/content";
        }
    }

    @Override
    public boolean isEncryptEnabled() {
        return true; // 是否启用加密
    }

    @Override
    public boolean isEnabled() {
        return true; // 是否启用该业务
    }
}
```

### 5. 测试页面

- 访问 `/antiblock/biz-test.html` 可以使用业务代码测试界面
- 访问 `/antiblock/test.html` 可以使用传统测试界面

## 配置说明

### 配置文件

在 `application.yml` 中添加以下配置：

```yaml
antiblock:
  # 是否启用防风控跳链功能
  enabled: true
  
  # 默认的中间跳转域名列表
  default-step-domains:
    - "jump1.example.com"
    - "jump2.example.com"
    - "redirect.example.com"
  
  # 允许的目标域名白名单
  allowed-target-domains:
    - "mp.weixin.qq.com"
    - "weixin.qq.com"
  
  # 禁止的目标域名黑名单
  blocked-target-domains:
    - "malicious.com"
  
  # 访问频率限制
  rate-limit:
    enabled: true
    window-seconds: 60
    max-requests-per-ip: 100
    max-unique-urls-per-ip: 10
  
  # 统计配置
  statistics:
    enabled: true
    retention-seconds: 86400
    detailed-logging: false

  # 加密配置
  encryption:
    enabled: true
    secret-key: "YourSecretKey2025"
    dynamic-key: false
    key-window-minutes: 60
```

### 域名配置

#### 1. 域名准备

**数量建议：**
- 建议准备 3-5 个不同的域名
- 域名越多，防风控效果越好
- 至少需要2个域名（除入口域名外）

**域名要求：**
- 所有域名必须指向同一个后端服务
- 确保域名已正确配置 DNS 解析
- 为 HTTPS 域名配置有效的 SSL 证书
- 建议使用不同的顶级域名（如 .com, .net, .org）

#### 2. 域名选择算法

系统采用智能域名选择算法：
- **避免重复**：自动排除与入口域名相同的域名
- **时间窗口**：基于5分钟时间窗口进行哈希计算
- **相对稳定**：短时间内选择相同域名，避免频繁切换
- **定期变化**：每5分钟自动切换选择策略

#### 3. Nginx 配置示例

```nginx
# 入口域名 a.com
server {
    listen 80;
    server_name a.com;
    
    location /antiblock/ {
        proxy_pass http://backend-server:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

# 中间跳转域名 b.com
server {
    listen 80;
    listen 443 ssl;
    server_name b.com;
    
    # SSL 配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location /antiblock/ {
        proxy_pass http://backend-server:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```



## 加密效果示例

### 压缩效果对比

| 原始URL | 长度 | 加密后 | 长度 | 压缩率 |
|---------|------|--------|------|--------|
| `https://mp.weixin.qq.com/s/abcdefghijklmnopqrstuvwxyz1234567890` | 70字符 | `Kj8xMnQ2Pz9LmNpRsT` | 18字符 | 74.3% |
| `https://weixin.qq.com/r/abc123def456` | 35字符 | `Mn7qR3sT9uVwXy` | 14字符 | 60.0% |
| `https://h5.qzone.qq.com/mqzone/index?param=value` | 49字符 | `Lp2mN8qR5tUvWx` | 14字符 | 71.4% |

### 安全特性

1. **不可逆性**：加密后的字符串无法直接看出原始URL内容
2. **密钥保护**：只有拥有正确密钥才能解密
3. **动态密钥**：支持定期自动更换密钥，提高安全性
4. **URL安全**：使用Base64 URL安全编码，避免特殊字符问题

## 注意事项

### 部署要求
1. 确保所有域名都正确指向同一个后端服务
2. HTTPS 域名需要有效的 SSL 证书
3. 建议使用 CDN 加速，提高访问速度

### 安全建议
1. 定期更换中间跳转域名
2. 监控访问日志，及时发现异常
3. 合理配置频率限制，防止滥用
4. 定期清理过期统计数据

### 性能优化
1. 使用 Redis 缓存提高响应速度
2. 合理设置缓存过期时间
3. 监控系统资源使用情况

## 故障排查

### 常见问题

1. **跳转失败**
   - 检查域名 DNS 解析是否正确
   - 确认 SSL 证书是否有效
   - 查看后端服务日志

2. **访问被拒绝**
   - 检查目标 URL 是否在白名单中
   - 确认是否触发频率限制
   - 查看系统配置是否正确

3. **统计数据异常**
   - 检查 Redis 连接是否正常
   - 确认统计功能是否启用
   - 查看缓存过期时间设置

### 日志查看

系统会记录详细的访问日志，包括：
- 每个步骤的访问情况
- 错误信息和异常堆栈
- 频率限制触发记录
- 配置变更记录

## 版本历史

- **v4.1.2** (2025-07-29)
  - 🔧 **工具类增强**：generateDirectEntryUrl方法重新支持encrypt参数
  - 🔐 **智能加密**：工具类根据encrypt参数自动加密targetUrl
  - 🛠️ **加密集成**：集成AntiBlockCryptoUtils.encryptTargetUrl()进行AES加密
  - 🎨 **界面恢复**：测试页面重新添加加密选项和相关功能
  - 📚 **文档更新**：完善工具类加密功能的使用说明

- **v4.1.0** (2025-07-29)
  - 🔗 **直接URL入口**：新增支持直接从参数接收targetUrl的entry方法
  - 📋 **双重入口模式**：同时支持业务代码方式和直接URL方式
  - 🎛️ **加密控制**：直接URL方式支持encrypt参数控制是否加密
  - 🛠️ **工具类扩展**：新增generateDirectEntryUrl方法生成直接URL
  - 🎨 **界面增强**：测试页面新增直接URL生成和测试功能
  - 📚 **文档完善**：详细说明两种入口方式的使用方法

- **v4.0.0** (2025-07-29)
  - 🎯 **targetCode参数**：在URL路径中添加targetCode参数支持
  - 📝 **路径参数化**：支持 `/entry/{bizCode}/{targetCode}` 格式
  - 🗂️ **内容分类**：微信业务支持tech、news、lifestyle等内容分类
  - 🧹 **架构简化**：删除除微信外的其他业务处理器，专注单一业务
  - 🛠️ **工具类增强**：URL生成工具支持targetCode参数
  - 🎨 **界面优化**：测试页面支持targetCode选择和预览
  - 📚 **文档更新**：完善targetCode使用说明和示例

- **v3.0.0** (2025-07-29)
  - 🏗️ **业务处理器架构**：重新设计为基于业务处理器的架构
  - 🎯 **自主决策机制**：每个业务处理器自主决定跳转目标URL
  - 🔌 **插件化设计**：支持动态注册和管理业务处理器
  - 📱 **智能内容推荐**：根据User-Agent、时间、IP等信息智能推荐内容
  - 🛠️ **处理器管理服务**：`AntiBlockBizHandlerService` 统一管理所有处理器
  - 🎨 **预置处理器**：内置微信、抖音、QQ空间业务处理器
  - 📡 **管理接口优化**：业务处理器信息查看和管理
  - 🧪 **处理器测试**：完整的业务处理器测试用例

- **v2.0.0** (2025-07-29)
  - 🎯 **业务代码架构**：重新设计为基于业务代码的固定地址方案
  - 🏗️ **业务配置系统**：新增业务配置管理，支持多平台配置
  - 🔗 **固定地址生成**：提供固定的跳转地址，不暴露目标链接
  - 📋 **预置业务配置**：内置微信、抖音、QQ空间等常用平台配置
  - 🛠️ **新URL生成工具**：`AntiBlockBizUrlGenerator` 专门处理业务URL
  - 🎨 **新测试界面**：`biz-test.html` 业务代码测试页面
  - 📡 **管理接口扩展**：业务配置查看、URL生成等管理功能
  - 🧪 **完整测试覆盖**：业务功能的全面测试用例

- **v1.1.2** (2025-07-29)
  - 🔧 **修复Redis方法**：将 `increment` 替换为项目标准的 `incrCacheValue` 方法
  - ✅ **方法统一**：确保所有Redis自增操作使用正确的API
  - 🧪 **测试完善**：添加Redis方法使用的单元测试

- **v1.1.1** (2025-07-29)
  - 🔧 **修复HTTP状态码**：统一使用项目自定义HttpStatus常量
  - ➕ **新增状态码**：添加302、307、429、503等防风控跳链所需状态码
  - 🛠️ **代码优化**：移除不存在的HttpServletResponse.SC_TOO_MANY_REQUESTS

- **v1.1.0** (2025-07-29)
  - ✨ **新增URL加密功能**：AES加密算法，显著缩短链接长度
  - 🔄 **智能域名选择**：从配置列表自动选择跳转域名，移除手动指定
  - 🛡️ **动态密钥支持**：基于时间窗口的密钥轮换机制
  - 📊 **加密效果分析**：压缩率统计和性能监控
  - 🎨 **加密演示页面**：可视化展示加密效果和安全特性

- **v1.0.0** (2025-07-29)
  - 初始版本发布
  - 支持基础三步跳转
  - 实现访问统计和管理功能
  - 添加安全防护机制
