<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防风控跳链 - 业务代码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .result {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .biz-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: white;
        }
        .biz-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .biz-card .url {
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            margin: 5px 0;
            word-break: break-all;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>防风控跳链 - 业务代码测试</h1>
        
        <div class="info">
            <strong>支持的入口方式：</strong>
            <ul>
                <li>🎯 <strong>业务代码方式</strong>：通过业务代码和targetCode提供固定地址</li>
                <li>🔗 <strong>直接URL方式</strong>：直接从参数接收目标URL进行跳转</li>
                <li>🏷️ <strong>内容分类</strong>：支持tech、news、lifestyle等内容分类</li>
                <li>🔐 <strong>智能加密</strong>：支持加密控制，保护目标链接</li>
            </ul>
        </div>

        <div class="grid">
            <div class="section">
                <h3>🎯 业务代码URL生成</h3>
                
                <div class="form-group">
                    <label for="baseUrl">基础URL:</label>
                    <input type="text" id="baseUrl" value="http://a.com" placeholder="http://a.com">
                </div>
                
                <div class="form-group">
                    <label for="bizCode">业务代码:</label>
                    <select id="bizCode">
                        <option value="weixin">weixin - 微信公众号</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="targetCode">目标代码（可选）:</label>
                    <select id="targetCode">
                        <option value="">无（默认文章）</option>
                        <option value="tech">tech - 科技文章</option>
                        <option value="news">news - 新闻文章</option>
                        <option value="lifestyle">lifestyle - 生活方式</option>
                        <option value="business">business - 商业文章</option>
                        <option value="education">education - 教育文章</option>
                    </select>
                </div>
                

                
                <button class="btn" onclick="generateSingleUrl()">生成URL</button>
                <button class="btn btn-secondary" onclick="testJump()">测试跳转</button>
                
                <div id="singleResult" class="result" style="display: none;"></div>
            </div>

            <div class="section">
                <h3>🔗 直接URL生成</h3>

                <div class="form-group">
                    <label for="directBaseUrl">基础URL:</label>
                    <input type="text" id="directBaseUrl" value="http://a.com" placeholder="http://a.com">
                </div>

                <div class="form-group">
                    <label for="directTargetUrl">目标URL:</label>
                    <input type="text" id="directTargetUrl" placeholder="https://mp.weixin.qq.com/s/xxx">
                </div>

                <div class="form-group">
                    <label for="directEncrypt">是否加密:</label>
                    <select id="directEncrypt">
                        <option value="true">是（推荐）</option>
                        <option value="false">否</option>
                    </select>
                </div>

                <div class="info" style="margin: 10px 0; padding: 10px; background-color: #e7f3ff; border-left: 4px solid #2196F3;">
                    <strong>说明：</strong> 工具会根据加密选项使用AntiBlockCryptoUtils自动加密目标URL，生成相应的跳转链接
                </div>

                <button class="btn" onclick="generateDirectUrl()">生成直接URL</button>
                <button class="btn btn-secondary" onclick="testDirectJump()">测试跳转</button>

                <div id="directResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="grid">
            <div class="section">
                <h3>📋 所有业务URL生成</h3>
                
                <div class="form-group">
                    <label for="allBaseUrl">基础URL:</label>
                    <input type="text" id="allBaseUrl" value="http://a.com" placeholder="http://a.com">
                </div>
                
                <button class="btn" onclick="generateAllUrls()">生成所有业务URL</button>
                <button class="btn btn-secondary" onclick="loadBizConfigs()">查看业务配置</button>
                
                <div id="allResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="section">
            <h3>📊 业务配置信息</h3>
            <div id="bizConfigsResult"></div>
        </div>

        <div class="section">
            <h3>💡 使用说明</h3>
            <div class="info">
                <h4>业务代码地址示例：</h4>
                <ul>
                    <li><strong>默认文章：</strong> <code>http://a.com/antiblock/entry/weixin</code> → 根据用户环境选择默认文章</li>
                    <li><strong>科技文章：</strong> <code>http://a.com/antiblock/entry/weixin/tech</code> → 跳转到科技类文章</li>
                    <li><strong>新闻文章：</strong> <code>http://a.com/antiblock/entry/weixin/news</code> → 跳转到新闻类文章</li>
                </ul>

                <h4>直接URL地址示例：</h4>
                <ul>
                    <li><strong>不加密：</strong> 工具生成 <code>http://a.com/antiblock/entry?targetUrl=https://mp.weixin.qq.com/s/xxx</code></li>
                    <li><strong>加密：</strong> 工具生成 <code>http://a.com/antiblock/entry?targetUrl=ENC:加密后的内容</code></li>
                </ul>

                <h4>业务流程：</h4>
                <ol>
                    <li>每个业务实现自己的处理器（AntiBlockBizHandler）</li>
                    <li>生成固定的跳转地址</li>
                    <li>用户访问固定地址</li>
                    <li>系统调用对应业务处理器的getTargetUrl方法</li>
                    <li>业务处理器根据请求信息（User-Agent、IP等）决定跳转目标</li>
                    <li>自动进行多步跳转到达目标页面</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时自动加载业务配置
        window.onload = function() {
            loadBizConfigs();
        };

        function generateSingleUrl() {
            const baseUrl = document.getElementById('baseUrl').value;
            const bizCode = document.getElementById('bizCode').value;
            const targetCode = document.getElementById('targetCode').value;

            if (!baseUrl || !bizCode) {
                alert('请填写基础URL和业务代码');
                return;
            }

            try {
                // 前端直接生成URL
                const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
                let generatedUrl = `${cleanBaseUrl}/antiblock/entry/${bizCode}`;

                // 添加targetCode到路径中
                if (targetCode) {
                    generatedUrl += `/${targetCode}`;
                }

                const resultDiv = document.getElementById('singleResult');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ URL生成成功</strong><br><br>
                    <strong>生成的跳转URL：</strong><br>
                    ${generatedUrl}<br><br>
                    <strong>业务代码：</strong> ${bizCode}<br>
                    <strong>目标代码：</strong> ${targetCode || '无（默认文章）'}<br>
                    <strong>说明：</strong> 访问此URL将由微信业务处理器根据targetCode决定跳转目标
                `;
                resultDiv.style.display = 'block';

            } catch (error) {
                console.error('Error:', error);
                const resultDiv = document.getElementById('singleResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ URL生成失败';
                resultDiv.style.display = 'block';
            }
        }

        function testJump() {
            const baseUrl = document.getElementById('baseUrl').value;
            const bizCode = document.getElementById('bizCode').value;
            const targetCode = document.getElementById('targetCode').value;

            if (!baseUrl || !bizCode) {
                alert('请填写基础URL和业务代码');
                return;
            }

            // 构建测试URL
            let testUrl = `${baseUrl}/antiblock/entry/${bizCode}`;
            if (targetCode) {
                testUrl += `/${targetCode}`;
            }

            // 在新窗口打开测试
            window.open(testUrl, '_blank');
        }

        function generateDirectUrl() {
            const baseUrl = document.getElementById('directBaseUrl').value;
            const targetUrl = document.getElementById('directTargetUrl').value;
            const encrypt = document.getElementById('directEncrypt').value === 'true';

            if (!baseUrl || !targetUrl) {
                alert('请填写基础URL和目标URL');
                return;
            }

            try {
                // 前端模拟URL生成（实际加密由后端处理）
                const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

                // 模拟处理后的targetUrl
                let processedTargetUrl = targetUrl;
                if (encrypt && !targetUrl.startsWith('ENC:')) {
                    // 前端模拟加密（实际加密由后端AntiBlockCryptoUtils处理）
                    const simpleEncrypt = btoa(targetUrl).replace(/[+/=]/g, ''); // 简单模拟
                    processedTargetUrl = 'ENC:' + simpleEncrypt;
                }

                let generatedUrl = `${cleanBaseUrl}/antiblock/entry?targetUrl=${encodeURIComponent(processedTargetUrl)}`;

                const resultDiv = document.getElementById('directResult');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ 直接URL生成成功</strong><br><br>
                    <strong>生成的跳转URL：</strong><br>
                    ${generatedUrl}<br><br>
                    <strong>原始目标URL：</strong> ${targetUrl}<br>
                    <strong>处理后URL：</strong> ${processedTargetUrl}<br>
                    <strong>是否加密：</strong> ${encrypt ? '是' : '否'}<br>
                    <strong>说明：</strong> 工具根据加密选项使用AntiBlockCryptoUtils自动处理目标URL
                `;
                resultDiv.style.display = 'block';

            } catch (error) {
                console.error('Error:', error);
                const resultDiv = document.getElementById('directResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ URL生成失败';
                resultDiv.style.display = 'block';
            }
        }

        function testDirectJump() {
            const baseUrl = document.getElementById('directBaseUrl').value;
            const targetUrl = document.getElementById('directTargetUrl').value;
            const encrypt = document.getElementById('directEncrypt').value === 'true';

            if (!baseUrl || !targetUrl) {
                alert('请填写基础URL和目标URL');
                return;
            }

            // 模拟处理后的targetUrl
            let processedTargetUrl = targetUrl;
            if (encrypt && !targetUrl.startsWith('ENC:')) {
                // 前端模拟加密（实际加密由后端AntiBlockCryptoUtils处理）
                const simpleEncrypt = btoa(targetUrl).replace(/[+/=]/g, ''); // 简单模拟
                processedTargetUrl = 'ENC:' + simpleEncrypt;
            }

            // 构建测试URL
            let testUrl = `${baseUrl}/antiblock/entry?targetUrl=${encodeURIComponent(processedTargetUrl)}`;

            // 在新窗口打开测试
            window.open(testUrl, '_blank');
        }

        function generateAllUrls() {
            const baseUrl = document.getElementById('allBaseUrl').value;

            if (!baseUrl) {
                alert('请填写基础URL');
                return;
            }

            try {
                // 前端直接生成微信业务的所有URL
                const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
                const targetCodes = [
                    { code: '', name: '默认文章' },
                    { code: 'tech', name: '科技文章' },
                    { code: 'news', name: '新闻文章' },
                    { code: 'lifestyle', name: '生活方式' },
                    { code: 'business', name: '商业文章' },
                    { code: 'education', name: '教育文章' }
                ];

                let html = `<strong>✅ 生成成功，微信业务共${targetCodes.length}个URL</strong><br><br>`;

                targetCodes.forEach(target => {
                    let fixedUrl = `${cleanBaseUrl}/antiblock/entry/weixin`;
                    if (target.code) {
                        fixedUrl += `/${target.code}`;
                    }

                    html += `
                        <div class="biz-card">
                            <h4>微信公众号 - ${target.name}</h4>
                            <div><strong>地址：</strong></div>
                            <div class="url">${fixedUrl}</div>
                            <div><strong>目标代码：</strong> ${target.code || '无'}</div>
                            <div><strong>加密：</strong> ✅ 启用</div>
                        </div>
                    `;
                });

                const resultDiv = document.getElementById('allResult');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = html;
                resultDiv.style.display = 'block';

            } catch (error) {
                console.error('Error:', error);
                const resultDiv = document.getElementById('allResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ URL生成失败';
                resultDiv.style.display = 'block';
            }
        }

        function loadBizConfigs() {
            // 前端直接显示微信业务处理器信息
            const handler = {
                bizCode: 'weixin',
                bizName: '微信公众号',
                description: '微信公众号文章跳转，根据targetCode和用户环境智能选择文章',
                encryptEnabled: true,
                enabled: true,
                supportedTargetCodes: ['tech', 'news', 'lifestyle', 'business', 'education']
            };

            const resultDiv = document.getElementById('bizConfigsResult');
            let html = `<strong>📋 当前业务处理器（共1个）</strong><br><br>`;

            html += `
                <div class="biz-card">
                    <h4>${handler.bizName} (${handler.bizCode})</h4>
                    <div><strong>描述：</strong> ${handler.description}</div>
                    <div><strong>加密：</strong> ${handler.encryptEnabled ? '✅ 启用' : '❌ 禁用'}</div>
                    <div><strong>状态：</strong> ${handler.enabled ? '✅ 启用' : '❌ 禁用'}</div>
                    <div><strong>支持的目标代码：</strong> ${handler.supportedTargetCodes.join(', ')}</div>
                </div>
            `;

            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
