<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防风控跳链测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .example {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .example h3 {
            margin-top: 0;
            color: #495057;
        }
        .example-link {
            word-break: break-all;
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>防风控跳链测试页面</h1>
        
        <div class="form-group">
            <label for="targetUrl">目标链接（微信文章链接等）:</label>
            <input type="url" id="targetUrl" placeholder="https://mp.weixin.qq.com/s/xxx" 
                   value="https://mp.weixin.qq.com/s/example">
        </div>
        

        
        <div class="form-group">
            <label for="customParam">自定义参数（可选）:</label>
            <input type="text" id="customParam" placeholder="例如: source=test">
        </div>
        
        <button class="btn" onclick="testBasicJump()">测试基础跳转</button>
        <button class="btn btn-secondary" onclick="testEnhancedJump()">测试增强跳转</button>
        <button class="btn btn-secondary" onclick="generateTestLinks()">生成测试链接</button>
        <button class="btn btn-secondary" onclick="testEncryption()">测试URL加密</button>
        <button class="btn btn-secondary" onclick="window.open('/antiblock/crypto-test.html', '_blank')">查看加密演示</button>
        
        <div class="example">
            <h3>跳转链路说明</h3>
            <p><strong>基础跳转流程：</strong></p>
            <ol>
                <li>用户访问入口链接 → 302跳转到step1</li>
                <li>访问step1（HTTPS） → 307跳转到step2（HTTP）</li>
                <li>访问step2（HTTP） → 302跳转到最终目标链接</li>
            </ol>
            
            <p><strong>生成的测试链接：</strong></p>
            <div id="generatedLinks" class="example-link">
                点击"生成测试链接"按钮查看
            </div>
        </div>

        <div class="example">
            <h3>URL加密测试结果</h3>
            <div id="encryptionResult" class="example-link">
                点击"测试URL加密"按钮查看加密效果
            </div>
        </div>
        
        <div class="note">
            <strong>注意事项：</strong>
            <ul>
                <li>确保目标链接是有效的HTTP/HTTPS地址</li>
                <li>中间跳转域名需要正确配置DNS和SSL证书</li>
                <li>测试时请注意查看浏览器开发者工具的网络请求</li>
                <li>生产环境使用时建议配置多个不同的域名</li>
                <li>URL加密功能可以显著缩短链接长度并提高安全性</li>
            </ul>
        </div>
    </div>

    <script>
        function testBasicJump() {
            const targetUrl = document.getElementById('targetUrl').value;

            if (!targetUrl) {
                alert('请输入目标链接');
                return;
            }

            let jumpUrl = '/antiblock/entry?targetUrl=' + encodeURIComponent(targetUrl);

            window.open(jumpUrl, '_blank');
        }
        
        function testEnhancedJump() {
            const targetUrl = document.getElementById('targetUrl').value;
            const customParam = document.getElementById('customParam').value;

            if (!targetUrl) {
                alert('请输入目标链接');
                return;
            }

            let jumpUrl = '/antiblock/entryEnhanced?targetUrl=' + encodeURIComponent(targetUrl);
            if (customParam) {
                jumpUrl += '&customParam=' + encodeURIComponent(customParam);
            }

            window.open(jumpUrl, '_blank');
        }
        
        function generateTestLinks() {
            const targetUrl = document.getElementById('targetUrl').value;
            const customParam = document.getElementById('customParam').value;

            if (!targetUrl) {
                alert('请输入目标链接');
                return;
            }

            const baseUrl = window.location.protocol + '//' + window.location.host;

            let basicUrl = baseUrl + '/antiblock/entry?targetUrl=' + encodeURIComponent(targetUrl);

            let enhancedUrl = baseUrl + '/antiblock/entryEnhanced?targetUrl=' + encodeURIComponent(targetUrl);
            if (customParam) {
                enhancedUrl += '&customParam=' + encodeURIComponent(customParam);
            }

            const linksHtml = `
                <strong>基础跳转链接：</strong><br>
                ${basicUrl}<br><br>
                <strong>增强跳转链接：</strong><br>
                ${enhancedUrl}<br><br>
                <strong>目标链接：</strong><br>
                ${targetUrl}<br><br>
                <strong>说明：</strong><br>
                跳转域名将从配置的域名列表中自动选择，无需手动指定
            `;

            document.getElementById('generatedLinks').innerHTML = linksHtml;
        }
        
        function testEncryption() {
            const targetUrl = document.getElementById('targetUrl').value;

            if (!targetUrl) {
                alert('请输入目标链接');
                return;
            }

            // 前端模拟加密测试（实际加密需要后端处理）
            try {
                const originalLength = targetUrl.length;
                // 模拟加密后的长度（实际压缩率约60-80%）
                const estimatedEncryptedLength = Math.floor(originalLength * 0.3);
                const compressionRatio = ((originalLength - estimatedEncryptedLength) / originalLength * 100).toFixed(1);

                const resultHtml = `
                    <strong>✅ 加密效果预估</strong><br><br>
                    <strong>原始URL：</strong><br>
                    ${targetUrl}<br>
                    <strong>长度：</strong> ${originalLength} 字符<br><br>

                    <strong>加密效果：</strong><br>
                    预估加密长度: ${estimatedEncryptedLength} 字符<br>
                    预估压缩率: ${compressionRatio}%<br><br>

                    <strong>说明：</strong><br>
                    实际加密由业务处理器根据配置自动处理<br>
                    微信和抖音业务启用加密，QQ空间业务不加密
                `;
                document.getElementById('encryptionResult').innerHTML = resultHtml;

            } catch (error) {
                console.error('Error:', error);
                document.getElementById('encryptionResult').innerHTML = '❌ 加密测试失败';
            }
        }

        // 页面加载时生成默认链接
        window.onload = function() {
            generateTestLinks();
        };
    </script>
</body>
</html>
