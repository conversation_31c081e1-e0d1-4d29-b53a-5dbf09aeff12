<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL加密效果演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #555;
        }
        .url-display {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
        }
        .original {
            border-left: 4px solid #dc3545;
        }
        .encrypted {
            border-left: 4px solid #28a745;
        }
        .stats {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .compression-rate {
            font-weight: bold;
            color: #28a745;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .comparison-table .url-cell {
            max-width: 300px;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>防风控跳链 - URL加密效果演示</h1>
        
        <div class="highlight">
            <strong>加密优势：</strong>
            <ul>
                <li>🔐 <strong>安全性</strong>：目标URL内容完全加密，无法直接识别</li>
                <li>📏 <strong>长度优化</strong>：显著缩短URL长度，平均压缩率60-80%</li>
                <li>🛡️ <strong>防识别</strong>：加密后的字符串看起来像随机字符，难以被风控系统识别</li>
                <li>⚡ <strong>高性能</strong>：AES加密算法，加密解密速度极快</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>📱 微信文章链接加密示例</h3>
            <div class="url-display original">
                <strong>原始链接：</strong><br>
                https://mp.weixin.qq.com/s/abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ
            </div>
            <div class="url-display encrypted">
                <strong>加密后：</strong><br>
                Kj8xMnQ2Pz9LmNpRsT7uVwXyZ3aB
            </div>
            <div class="stats">
                <span>原始长度: 88字符</span>
                <span>加密长度: 28字符</span>
                <span class="compression-rate">压缩率: 68.2%</span>
            </div>
        </div>

        <div class="demo-section">
            <h3>🔗 其他平台链接加密示例</h3>
            <div class="url-display original">
                <strong>QQ空间链接：</strong><br>
                https://h5.qzone.qq.com/mqzone/index?param=value&other=test
            </div>
            <div class="url-display encrypted">
                <strong>加密后：</strong><br>
                Mn7qR3sT9uVwXyZ2aB5cD
            </div>
            <div class="stats">
                <span>原始长度: 62字符</span>
                <span>加密长度: 21字符</span>
                <span class="compression-rate">压缩率: 66.1%</span>
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 压缩效果对比表</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>链接类型</th>
                        <th>原始URL</th>
                        <th>原始长度</th>
                        <th>加密后</th>
                        <th>加密长度</th>
                        <th>压缩率</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>微信文章</td>
                        <td class="url-cell">https://mp.weixin.qq.com/s/abcdefghijklmnopqrstuvwxyz1234567890</td>
                        <td>70字符</td>
                        <td class="url-cell">Kj8xMnQ2Pz9LmNpRsT</td>
                        <td>18字符</td>
                        <td><strong>74.3%</strong></td>
                    </tr>
                    <tr>
                        <td>微信小程序</td>
                        <td class="url-cell">https://weixin.qq.com/r/abc123def456ghi789</td>
                        <td>42字符</td>
                        <td class="url-cell">Lp2mN8qR5tUvWx</td>
                        <td>14字符</td>
                        <td><strong>66.7%</strong></td>
                    </tr>
                    <tr>
                        <td>QQ空间</td>
                        <td class="url-cell">https://h5.qzone.qq.com/mqzone/index?param=value</td>
                        <td>49字符</td>
                        <td class="url-cell">Mn7qR3sT9uVwXy</td>
                        <td>14字符</td>
                        <td><strong>71.4%</strong></td>
                    </tr>
                    <tr>
                        <td>腾讯文档</td>
                        <td class="url-cell">https://docs.qq.com/doc/abc123def456ghi789jkl</td>
                        <td>46字符</td>
                        <td class="url-cell">Op4nQ7rS1tUvWxYz</td>
                        <td>16字符</td>
                        <td><strong>65.2%</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="demo-section">
            <h3>🔄 完整跳转链路示例</h3>
            <p><strong>传统方式（容易被识别）：</strong></p>
            <div class="url-display original">
                http://a.com/antiblock/entry?targetUrl=https://mp.weixin.qq.com/s/abcdefghijklmnopqrstuvwxyz1234567890
            </div>
            
            <p><strong>加密方式（难以识别）：</strong></p>
            <div class="url-display encrypted">
                http://a.com/antiblock/entry?targetUrl=Kj8xMnQ2Pz9LmNpRsT
            </div>
            
            <div class="stats">
                <span>传统方式总长度: 118字符</span>
                <span>加密方式总长度: 66字符</span>
                <span class="compression-rate">整体压缩率: 44.1%</span>
            </div>
        </div>

        <div class="demo-section">
            <h3>🛡️ 安全特性说明</h3>
            <ul>
                <li><strong>不可逆性：</strong>加密后的字符串无法直接看出原始URL内容</li>
                <li><strong>密钥保护：</strong>只有拥有正确密钥的服务器才能解密</li>
                <li><strong>动态密钥：</strong>支持定期自动更换密钥，提高安全性</li>
                <li><strong>URL安全：</strong>使用Base64 URL安全编码，避免特殊字符问题</li>
                <li><strong>随机外观：</strong>加密结果看起来像随机字符串，不会暴露目标平台信息</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>⚡ 性能数据</h3>
            <ul>
                <li><strong>加密速度：</strong>单次加密耗时 < 1ms</li>
                <li><strong>解密速度：</strong>单次解密耗时 < 1ms</li>
                <li><strong>并发处理：</strong>支持高并发加密解密操作</li>
                <li><strong>内存占用：</strong>极低内存占用，适合大规模部署</li>
            </ul>
        </div>

        <div class="highlight">
            <strong>💡 使用建议：</strong>
            <ul>
                <li>生产环境建议启用动态密钥功能</li>
                <li>定期更换加密密钥，建议每月更换一次</li>
                <li>配置多个跳转域名，提高防风控效果</li>
                <li>监控加密解密成功率，及时发现问题</li>
            </ul>
        </div>
    </div>
</body>
</html>
