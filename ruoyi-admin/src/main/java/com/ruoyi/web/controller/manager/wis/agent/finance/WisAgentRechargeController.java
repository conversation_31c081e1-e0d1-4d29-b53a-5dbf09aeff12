package com.ruoyi.web.controller.manager.wis.agent.finance;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserRechargeRecordEntity;
import com.ruoyi.system.manager.advertiser.AdvertiserRechargeManager;
import com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeListReq;
import com.ruoyi.system.req.agent.finance.WisAgentRechargeListReq;
import com.ruoyi.system.req.agent.finance.WisAgentRechargeReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserRechargeRecordService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.agent.AgentRechargeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.advertiser.RechargeAuditStatus.APPROVE;

/**
 * [代理商平台]充值记录
 *
 * <AUTHOR>
 * @date 2022/10/27
 */
@RequestMapping("/wis/agent/fiance/recharge")
@RestController
public class WisAgentRechargeController extends BaseController {

    @Autowired
    private AdvertiserRechargeRecordService advertiserRechargeRecordService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AdvertiserRechargeManager advertiserRechargeManager;

    @Autowired
    private AdvertService advertService;

    /**
     * 充值记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<AgentRechargeVO> list(WisAgentRechargeListReq req) {
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return getDataTable(Collections.emptyList());
        }
        // 查询代理商下所有广告主ID
        List<Long> accountIds = new ArrayList<>();
        accountIds.add(user.getCrmAccountId());
        accountIds.addAll(agentService.selectAdvertiserIdsByAgent(user.getCrmAccountId()));
        if (null != req.getAdvertiserId() && !accountIds.contains(req.getAdvertiserId())) {
            return getDataTable(Collections.emptyList());
        }

        AdvertiserRechargeListReq listReq = new AdvertiserRechargeListReq();
        listReq.setStartDate(req.getStartDate());
        listReq.setEndDate(req.getEndDate());
        listReq.setAccountId(req.getAdvertiserId());
        listReq.setAccountIds(accountIds);
        listReq.setRechargeType(req.getRechargeType());
        listReq.setAuditStatus(APPROVE.getStatus());

        TableSupport.startPage();
        List<AdvertiserRechargeRecordEntity> list = advertiserRechargeRecordService.selectListByAccountIdAndDate(listReq);

        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(accountIds);

        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            AgentRechargeVO rechargeVO = BeanUtil.copyProperties(record, AgentRechargeVO.class);
            rechargeVO.setAdvertiserName(advertiserNameMap.get(record.getAccountId()));
            if (Objects.equals(record.getAccountId(), user.getCrmAccountId())) {
                rechargeVO.setAdvertiserName("");
            }
            return rechargeVO;
        }));
    }

    /**
     * 代理商充值
     */
    @PostMapping("recharge")
    public Result<Void> recharge(@RequestBody @Validated WisAgentRechargeReq req) {
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return ResultBuilder.fail("仅限代理商充值");
        }
        req.setAgentId(user.getCrmAccountId());
        advertiserRechargeManager.transfer(req);
        advertService.sendCheckBudgetMsgByAdvertiserId(req.getAdvertiserId());
        return ResultBuilder.success();
    }
}
