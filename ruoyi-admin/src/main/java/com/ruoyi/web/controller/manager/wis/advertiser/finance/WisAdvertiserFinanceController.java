package com.ruoyi.web.controller.manager.wis.advertiser.finance;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.advertiser.finance.WisAdvertiserFinanceBaseInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Optional;

import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * 广告主财务Controller
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@RequestMapping("/wis/fiance")
@RestController
public class WisAdvertiserFinanceController {

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private AdvertiserConsumeRecordService advertiserConsumeRecordService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Autowired
    private AgentService agentService;

    /**
     * 广告主财务基础信息
     */
    @GetMapping(value = "/baseInfo")
    public Result<WisAdvertiserFinanceBaseInfoVO> getBaseInfo() {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();
        // 非广告主/代理商不展示
        if (!isAdvertiser(user.getMainType())) {
            return ResultBuilder.success(new WisAdvertiserFinanceBaseInfoVO());
        }

        WisAdvertiserFinanceBaseInfoVO finance = new WisAdvertiserFinanceBaseInfoVO();
        finance.setAgentName(agentService.selectAgentNameByAdvertiserId(user.getCrmAccountId()));
        finance.setTotalBalance(0);
        finance.setConsumeAmount(0);

        // 查询账户余额
        finance.setTotalBalance(advertiserBalanceService.selectTotalAmountByAccountId(user.getCrmAccountId()));
        // 查询总消费金额
        finance.setConsumeAmount(NumberUtils.defaultInt(advertiserConsumeRecordService.sumConsumeAmount(user.getCrmAccountId())));

        // 离线数据广告主展示昨日余额和消费金额
        boolean isOfflineData = whitelistService.contains(OFFLINE_DATA_ADVERTISER, user.getCrmAccountId());
        if (isOfflineData) {
            Optional.ofNullable(advertiserConsumeRecordService.selectByAccountIdAndDate(user.getCrmAccountId(), DateUtil.beginOfDay(new Date()))).ifPresent(data -> {
                Integer todayConsumeAmount = NumberUtils.defaultInt(data.getConsumeAmount());
                finance.setTotalBalance(finance.getTotalBalance() + todayConsumeAmount);
                finance.setConsumeAmount(finance.getConsumeAmount() - todayConsumeAmount);
            });
        }
        // 离线数据订正后，账户余额和总消费金额的差额计算.offset=订正后的值-原值
        Integer consumeOffset = dspAdvertiserConsumeRecordService.consumeOffset(user.getCrmAccountId());
        finance.setTotalBalance(finance.getTotalBalance() - consumeOffset);
        finance.setConsumeAmount(finance.getConsumeAmount() + consumeOffset);

        return ResultBuilder.success(finance);
    }
}
