package com.ruoyi.web.controller.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.apptag.AppTagRelationEntity;
import com.ruoyi.system.entity.datashow.AppData;
import com.ruoyi.system.entity.tagmanager.TagManagerEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.req.app.AppReq;
import com.ruoyi.system.req.app.AppTagUpdateReq;
import com.ruoyi.system.service.app.AppTagRelationService;
import com.ruoyi.system.service.datasource.AppDataService;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import com.ruoyi.system.vo.app.AppSimpleVO;
import com.ruoyi.system.vo.app.AppVO;
import com.ruoyi.system.vo.tagmanager.TagListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.isCrmUser;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * 媒体应用Controller
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@RestController
@RequestMapping("/manager/app")
public class AppController extends BaseController {

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AppDataService appDataService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private AppTagRelationService appTagRelationService;

    @Autowired
    private TagManagerService tagManagerService;

    @Autowired
    private AccountRelationService accountRelationService;

    /**
     * 查询媒体应用列表
     */
    @GetMapping("/list")
    public TableDataInfo list(AppReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        App param = BeanUtil.copyProperties(req, App.class);
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        // 非CRM用户限制查询账号
        if (!isCrmUser(user.getMainType())) {
            param.setAccountId(user.getCrmAccountId());
        } else {
            // 负责人查询
            if (CollectionUtils.isNotEmpty(req.getManagerIds())) {
                List<Long> accountIds = accountRelationService.selectBySrcAccountIds(req.getManagerIds());
                if (CollectionUtils.isEmpty(accountIds)) {
                    return getDataTable(Collections.emptyList());
                }
                param.setAccountIds(accountIds);
            }
            // 数据权限控制
            DataPermissionBo permission = dataPermissionManager.selectApp();
            if (hasPartialPermission(permission.getType())) {
                if (CollectionUtils.isEmpty(permission.getValues())) {
                    return getDataTable(Collections.emptyList());
                }
                if (null == param.getAppIds()) {
                    param.setAppIds(permission.getValues());
                } else {
                    param.getAppIds().retainAll(permission.getValues());
                }
                if (CollectionUtils.isEmpty(param.getAppIds())) {
                    return getDataTable(Collections.emptyList());
                }
            }
        }

        startPage();
        List<App> apps = appService.selectAppList(param);
        if (CollectionUtils.isEmpty(apps)) {
            return getDataTable(Collections.emptyList());
        }

        List<Long> appIds = apps.stream().map(App::getId).collect(Collectors.toList());
        List<Long> accountIds = apps.stream().map(App::getAccountId).collect(Collectors.toList());
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());

        // 媒体ID-广告位数量映射
        Map<Long, Integer> slotCountMap = slotService.groupByAppId(appIds);
        // 媒体ID-媒体数据映射
        Map<Long, AppData> appDataMap = appDataService.groupByAppId(appIds, yesterday);
        // 账号ID-公司名称映射
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(accountIds);
        // 账号ID-邮箱映射
        Map<Long, String> emailMap = accountService.selectEmailByIds(accountIds);
        // 账号ID-负责人映射
        Map<Long, String> managerNameMap = accountService.selectManagerMap(accountIds);

        return getDataTable(PageInfoUtils.dto2Vo(apps, app -> {
            AppVO appVO = BeanUtil.copyProperties(app, AppVO.class);

            // CRM用户展示更多数据
            if (isCrmUser(user.getMainType())) {
                appVO.setCompanyName(companyNameMap.get(app.getAccountId()));
                appVO.setManagerName(managerNameMap.getOrDefault(app.getAccountId(), ""));
                appVO.setEmail(emailMap.get(app.getAccountId()));
                appVO.setSlotCount(slotCountMap.getOrDefault(app.getId(), 0));
                AppData appData = appDataMap.get(app.getId());
                if (null != appData) {
                    appVO.setYdaySlotReqUv(appData.getSlotRequestUv());
                    appVO.setYdayAppRevenue(appData.getAppRevenue());
                } else {
                    appVO.setYdaySlotReqUv(0);
                    appVO.setYdayAppRevenue(0L);
                }
            }
            return appVO;
        }));
    }

    /**
     * 查询媒体应用列表
     */
    @GetMapping("/listTotal")
    public AjaxResult listTotal() {
        return AjaxResult.success(appService.selectTotalAppList());
    }

    /**
     * 根据ID查询媒体应用列表
     * 注:用于广告定向配置屏蔽媒体时查询
     */
    @GetMapping("/listByIds")
    public TableDataInfo<AppSimpleVO> listByIds(String appSearch) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非CRM用户不展示
        if (!isCrmUser(user.getMainType())) {
            return getDataTable(Collections.emptyList());
        }
        App param = new App();
        param.setSearchValue(appSearch);
        startPage();
        List<App> apps = appService.selectAppList(param);
        return getDataTable(PageInfoUtils.dto2Vo(apps, app -> BeanUtil.copyProperties(app, AppSimpleVO.class)));
    }

    /**
     * 获取媒体应用详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        App app = appService.selectAppById(id);
        if (null == app) {
            return AjaxResult.success();
        }
        // 非CRM用户限制查询本账号的数据
        LoginUser user = SecurityUtils.getLoginUser();
        if (!isCrmUser(user.getMainType()) && !Objects.equals(user.getCrmAccountId(), app.getAccountId())) {
            return AjaxResult.success();
        }
        return AjaxResult.success(app);
    }

    /**
     * 新增媒体应用
     */
    @Log(title = "媒体应用", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody App app) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();
        app.setAccountId(user.getCrmAccountId());
        return toAjax(appService.insertApp(app));
    }

    /**
     * 修改媒体应用
     */
    @Log(title = "媒体应用", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody App req) {
        LoginUser user = SecurityUtils.getLoginUser();
        App app = appService.selectAppById(req.getId());
        if (null == app || !Objects.equals(user.getCrmAccountId(), app.getAccountId())) {
            throw new CustomException("修改媒体异常，请刷新页面后操作");
        }
        return toAjax(appService.updateApp(req));
    }

    /**
     * 根据媒体id查询媒体流量标签列表
     * @param appId
     * @return
     */
    @GetMapping("getAppTagList")
    public Result<List<TagListVO>> getAppTagList(@Validated @NotNull(message = "媒体id不能为空") Long appId){
        List<Long> subTagIds = appTagRelationService.selectTagIdsByAppId(appId);
        List<TagManagerEntity> subTagEntities = tagManagerService.selectByIds(subTagIds);
        Map<Long, List<TagManagerEntity>> tagMap = subTagEntities.stream().sorted(Comparator.comparing(TagManagerEntity::getTagSort)).collect(Collectors.groupingBy(TagManagerEntity::getParentId));

        List<Long> parentIds = subTagEntities.stream().map(TagManagerEntity::getParentId).collect(Collectors.toList());
        List<TagManagerEntity> parentTags = tagManagerService.selectByIds(parentIds);
        List<TagListVO> result = parentTags.stream().map(parent -> {
            TagListVO vo = BeanUtil.copyProperties(parent, TagListVO.class);
            List<TagManagerEntity> sub = tagMap.get(parent.getId());
            List<TagListVO> tagListVOS = BeanUtil.copyToList(sub, TagListVO.class);
            vo.setSubTags(tagListVOS);
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(result);
    }

    /**
     * 更新媒体标签列表
     * @param req
     * @return
     */
    @PostMapping("updateAppTagList")
    public Result<Boolean> updateAppTagList(@RequestBody AppTagUpdateReq req){
        //删除所有标签
        appTagRelationService.deleteByAppId(req.getAppId());
        List<AppTagRelationEntity> insertList = req.getTagIds().stream().map(tagId -> {
            AppTagRelationEntity entity = new AppTagRelationEntity();
            entity.setAppId(req.getAppId());
            entity.setTagId(tagId);
            return entity;
        }).collect(Collectors.toList());
        //新增标签
        return ResultBuilder.success(appTagRelationService.batchInsert(insertList));

    }
}
