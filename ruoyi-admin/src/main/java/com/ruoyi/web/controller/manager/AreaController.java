package com.ruoyi.web.controller.manager;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.area.AreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 行政区划接口
 *
 * <AUTHOR>
 * @date 2021/9/6
 */
@RestController
@RequestMapping("/manager/area")
public class AreaController extends BaseController {

    @Autowired
    private AreaService areaService;

    /**
     * 查询行政区划列表
     */
    @GetMapping("/listProvinceAndCity")
    public AjaxResult listProvinceAndCity() {
        return AjaxResult.success(areaService.queryTotalProvinceAndCity());
    }

    /**
     * 根据关键词查询地域列表
     */
    @GetMapping("/search")
    public AjaxResult search(String searchKey) {
        if (StringUtils.isBlank(searchKey)) {
            return AjaxResult.success();
        }
        return AjaxResult.success(areaService.searchProvinceAndCity(searchKey));
    }
}
