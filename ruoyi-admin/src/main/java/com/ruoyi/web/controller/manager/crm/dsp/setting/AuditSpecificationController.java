package com.ruoyi.web.controller.manager.crm.dsp.setting;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.common.IndustryEntity;
import com.ruoyi.system.entity.common.IndustryQualificationRequireEntity;
import com.ruoyi.system.req.manager.IndustryAddReq;
import com.ruoyi.system.req.manager.IndustryEditReq;
import com.ruoyi.system.req.manager.QualificationRequireAddReq;
import com.ruoyi.system.req.manager.QualificationRequireEditReq;
import com.ruoyi.system.req.manager.QualificationRequireListReq;
import com.ruoyi.system.service.common.IndustryQualificationRequireService;
import com.ruoyi.system.service.common.IndustryService;
import com.ruoyi.system.vo.manager.IndustryVO;
import com.ruoyi.system.vo.manager.QualificationRequireVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * [CRM后台]审核规范
 *
 * <AUTHOR>
 * @date 2023-03-06
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/setting/auditSpecification")
public class AuditSpecificationController extends BaseController {

    @Autowired
    private IndustryService industryService;

    @Autowired
    private IndustryQualificationRequireService industryQualificationRequireService;

    /**
     * 行业管理列表
     */
    @GetMapping("industryList")
    public TableDataInfo<IndustryVO> industryList() {
        startPage();
        List<IndustryEntity> list = industryService.selectList(null);
        return getDataTable(PageInfoUtils.dto2Vo(list, industry -> BeanUtil.copyProperties(industry, IndustryVO.class)));
    }

    /**
     * 新增行业
     */
    @PostMapping("/addIndustry")
    public Result<Void> addIndustry(@RequestBody IndustryAddReq req) {
        if (StringUtils.isBlank(req.getIndustryName())) {
            return ResultBuilder.fail("行业名称不能为空");
        }
        if (industryService.isIndustryNameExist(req.getIndustryName())) {
            return ResultBuilder.fail("该行业名称已存在");
        }
        IndustryEntity industry = new IndustryEntity();
        industry.setIndustryName(req.getIndustryName());
        industry.setEnableStatus(req.getEnableStatus());
        int result = industryService.insert(industry);
        return ResultBuilder.result(result);
    }

    /**
     * 编辑行业
     */
    @PostMapping("/editIndustry")
    public Result<Void> editIndustry(@RequestBody IndustryEditReq req) {
        if (null == req.getId()) {
            return ResultBuilder.fail("行业ID不能为空");
        }
        IndustryEntity industry = new IndustryEntity();
        industry.setId(req.getId());
        industry.setEnableStatus(req.getEnableStatus());
        int result = industryService.updateById(industry);
        return ResultBuilder.result(result);
    }

    /**
     * 行业名称相似校验
     */
    @PostMapping("/industrySimilarCheck")
    public Result<List<String>> industrySimilarCheck(@RequestBody IndustryAddReq req) {
        return ResultBuilder.success(industryService.getSimilarName(req.getIndustryName()));
    }

    /**
     * 资质要求列表
     */
    @GetMapping("qualificationRequireList")
    public TableDataInfo<QualificationRequireVO> qualificationRequireList(QualificationRequireListReq req) {
        startPage();
        List<IndustryQualificationRequireEntity> list = industryQualificationRequireService.selectList(BeanUtil.copyProperties(req, IndustryQualificationRequireEntity.class));
        Map<Long, String> industryNameMap = industryService.selectIndustryNameMap(ListUtils.mapToList(list, IndustryQualificationRequireEntity::getIndustryId));
        return getDataTable(PageInfoUtils.dto2Vo(list, qualification -> {
            QualificationRequireVO qualificationRequireVO = BeanUtil.copyProperties(qualification, QualificationRequireVO.class);
            qualificationRequireVO.setIndustryName(industryNameMap.get(qualification.getIndustryId()));
            return qualificationRequireVO;
        }));
    }

    /**
     * 新增资质要求
     */
    @PostMapping("/addQualificationRequire")
    public Result<Void> addQualificationRequire(@RequestBody QualificationRequireAddReq req) {
        if (StringUtils.isBlank(req.getQualificationName())) {
            return ResultBuilder.fail("资质要求不能为空");
        }
        if (null == industryService.selectById(req.getIndustryId())) {
            return ResultBuilder.fail("请选择行业名称");
        }
        IndustryQualificationRequireEntity qualification = new IndustryQualificationRequireEntity();
        qualification.setIndustryId(req.getIndustryId());
        qualification.setQualificationName(req.getQualificationName());
        qualification.setIsMust(req.getIsMust());
        qualification.setEnableStatus(req.getEnableStatus());
        int result = industryQualificationRequireService.insert(qualification);
        return ResultBuilder.result(result);
    }

    /**
     * 编辑资质要求
     */
    @PostMapping("/editQualificationRequire")
    public Result<Void> editQualificationRequire(@RequestBody QualificationRequireEditReq req) {
        if (null == req.getId()) {
            return ResultBuilder.fail("资质要求ID不能为空");
        }
        IndustryQualificationRequireEntity qualification = new IndustryQualificationRequireEntity();
        qualification.setId(req.getId());
        qualification.setQualificationName(req.getQualificationName());
        qualification.setIsMust(req.getIsMust());
        qualification.setEnableStatus(req.getEnableStatus());
        int result = industryQualificationRequireService.updateById(qualification);
        return ResultBuilder.result(result);
    }
}
