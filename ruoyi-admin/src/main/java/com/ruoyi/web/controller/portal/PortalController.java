package com.ruoyi.web.controller.portal;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.portal.PortalPublishEntity;
import com.ruoyi.system.req.portal.PortalAddReq;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.portal.PortalPublishService;
import com.ruoyi.system.vo.portal.PortalListVO;
import com.ruoyi.system.vo.portal.PortalUserInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * [官网]官网接口
 *
 * <AUTHOR>
 * @date 2022/9/27 1:40 下午
 */
@RestController
@RequestMapping("portal")
public class PortalController extends BaseController {

    @Autowired
    private PortalPublishService portalPublishService;

    @Autowired
    private AccountService accountService;

    /**
     * 官网用户信息
     */
    @GetMapping("userInfo")
    public Result<PortalUserInfoVO> userInfo(){
        PortalUserInfoVO vo = new PortalUserInfoVO();
        vo.setAccountName(SecurityUtils.getUsername());
        return ResultBuilder.success(vo);
    }
    /**
     * 新增发布信息
     */
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody @Validated PortalAddReq req){
        Long userId = SecurityUtils.getLoginUser().getCrmAccountId();
        PortalPublishEntity entity = BeanUtil.copyProperties(req,PortalPublishEntity.class);
        entity.setAccountId(userId);
        return ResultBuilder.success(portalPublishService.insert(entity));
    }

    /**
     * 发布列表
     */
    @GetMapping("list")
    public Result<List<PortalListVO>> list(@Validated @NotNull(message = "类型不能为空") Integer type){
        List<PortalPublishEntity> portalPublishEntities = portalPublishService.listByType(type);
        List<Long> accountIds = portalPublishEntities.stream().map(PortalPublishEntity::getAccountId).collect(Collectors.toList());
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(accountIds);
        List<PortalListVO> resultList = portalPublishEntities.stream().map(entity -> {
            PortalListVO vo = BeanUtil.copyProperties(entity, PortalListVO.class);
            vo.setAccountName(hideName(companyNameMap.getOrDefault(entity.getAccountId(), "")));
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(resultList);
    }

    /**
     * 隐藏名字
     */
    private String hideName(String name){
        if (StringUtils.isBlank(name)){
            return "";
        }
        if(name.length() <= 5){
            return name;
        }
        int hideStart = (name.length() - 4) /2;
        return StrUtil.hide(name,hideStart ,hideStart + 4);
    }
}
