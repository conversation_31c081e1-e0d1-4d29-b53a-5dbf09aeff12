package com.ruoyi.web.controller.open;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.SlotUpService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.AppCacheService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.landpage.ExternalCouponRecordService;
import com.ruoyi.system.service.open.MybCouponService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Set;

import static com.ruoyi.common.enums.InnerLogType.LANDPAGE_CLICK;

/**
 * 蒸蒸接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Slf4j
@RestController
@RequestMapping("/open/zz")
public class ZzController {

    @Autowired
    private StatService statService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private ExternalCouponRecordService externalCouponRecordService;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private MybCouponService mybCouponService;

    @Autowired
    private AppCacheService appCacheService;

    @Autowired
    private SlotUpService slotUpService;

    @Autowired
    private ConvCallbackService convCallbackService;

    /**
     * 蚂蚁保投保成功回调
     */
    @CrossOrigin
    @PostMapping("/notify")
    public String notify(@RequestBody String body) {
        log.info("蒸蒸科技蚂蚁保回调，body={}", body);
        if (!JSONUtil.isTypeJSON(body)) {
            if (body.startsWith("\"")) {
                body = body.replaceAll("\\\\\\\"", "\"");
                body = body.replaceAll("\\\\\"", "\"");
                body = body.substring(1, body.length() -1);
            } else {
                return "fail";
            }
        }
        JSONObject param = JSONObject.parseObject(body);
        if (null == param) {
            return "fail";
        }
        String notifyId = param.getString("notify_id");
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K032.join(notifyId), 3600);
        if (lock == null) {
            return "success";
        }

        JSONObject bizContent = JSON.parseObject(param.getString("biz_content"));
        if (null == bizContent) {
            return "success";
        }
        JSONObject bizData = bizContent.getJSONObject("biz_data");
        if (null == bizData) {
            return "success";
        }
        String orderId = bizData.getString("channel_user_tag");
        Order order = orderService.selectByOrderId(orderId);
        if (null != order) {
            // 广告位参数
            JSONObject slotParam = callbackService.getParameterFromCache(orderId);
            // 转化埋点
            statService.innerLogStatByOrderId(LANDPAGE_CLICK, order.getOrderId());
            // 流量标签
            Set<String> appTags = appCacheService.getAppTagSetByAppId(order.getAppId());
            if (CollUtil.contains(appTags, "捷停车")) {
                // 发放停车券
                grandCoupon(order, slotParam, notifyId);
            } else {
                // 固定收益上报
                slotUpService.slotUpAddCostMulti(order, param.getString("hu"));
                convCallbackService.handleAsync(LANDPAGE_CLICK, null, order);
                // 支付埋点
                statService.convertEvent(order, ConvType.PAY.getType(), null);
            }
        }
        return "success";
    }

    /**
     * 状态查询
     */
    @CrossOrigin
    @GetMapping("/queryStatus")
    public Result<Boolean> queryStatus(HttpServletRequest request, String nadkey) {
        log.info("蒸蒸蚂蚁保状态查询，param={}", JSON.toJSONString(request.getParameterMap()));

        ExternalCouponRecordEntity record = externalCouponRecordService.selectByOrderId(nadkey);
        if (null == record) {
            return ResultBuilder.success(false);
        }
        return ResultBuilder.success(Objects.equals(record.getCouponStatus(), 1));
    }

    /**
     * 埋点并发放优惠券
     */
    private void grandCoupon(Order order, JSONObject slotParam, String notifyId) {
        // 发放优惠券
        JSONObject pageConfig = getPageConfig(order);
        if (null != pageConfig && null != pageConfig.getInteger("productType")) {
            // 幂等处理
            String redisKey = CrmRedisKeyFactory.K032.join("zz", notifyId);
            RedisLock lock = redisAtomicClient.getLock(redisKey, 3600);
            if (null == lock) {
                return;
            }
            // 根据不同产品发放对应优惠券
            boolean isSuccess = mybCouponService.grandCoupon(slotParam, pageConfig, order);
            if (!isSuccess) {
                lock.unlock();
            }
        }
    }

    private JSONObject getPageConfig(Order order) {
        if (null == order) {
            return null;
        }
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
        if (null == adSnapshot) {
            return null;
        }
        return JSON.parseObject(landpageCacheService.selectLandpagePageConfigCache(LandpageUtil.extractLpk(adSnapshot.getOriginLandpageUrl())));
    }
}
