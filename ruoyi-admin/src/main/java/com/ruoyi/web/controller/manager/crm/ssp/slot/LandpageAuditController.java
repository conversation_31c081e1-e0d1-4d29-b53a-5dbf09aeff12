package com.ruoyi.web.controller.manager.crm.ssp.slot;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.entity.manager.LandpageAuditRecord;
import com.ruoyi.system.req.manager.LandpageAuditRecordReq;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.landpage.LandpageAuditService;
import com.ruoyi.system.vo.manager.LandpageAuditRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 * 爱奇艺落地页送审Controller
 *
 * <AUTHOR>
 * @date 2021/9/9
 */
@Deprecated
@Slf4j
@RestController
@RequestMapping("/lp/audit")
public class LandpageAuditController extends BaseController {

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private LandpageAuditService landpageAuditService;

    /**
     * 查询落地页送审记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LandpageAuditRecordReq req) {
        startPage();
        List<LandpageAuditRecord> list = landpageAuditService.selectLandpageAuditRecordList(req);
        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            LandpageAuditRecordVO recordVO = BeanUtil.copyProperties(record, LandpageAuditRecordVO.class);
            Optional.ofNullable(JSON.parseObject(record.getExtInfo())).ifPresent(extInfo -> recordVO.setPreviewUrl(extInfo.getString("previewUrl")));
            return recordVO;
        }));
    }

    /**
     * 导入送审链接
     */
    @PostMapping("/import")
    public AjaxResult importLandpage(MultipartFile file) {
        try {
            ExcelUtil<LandpageAuditRecord> util = new ExcelUtil<>(LandpageAuditRecord.class);
            List<LandpageAuditRecord> urlList = util.importExcel(file.getInputStream());
            int count = 0;
            if (CollectionUtils.isNotEmpty(urlList)) {
                count = landpageAuditService.landpageAudit(urlList);
            }
            return AjaxResult.success("已成功送审" + count + "条链接");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 重新送审
     */
    @PostMapping("/reAudit")
    public AjaxResult reAudit(@RequestBody LandpageAuditRecordReq req) {
        if (null == req.getRecordId()) {
            return AjaxResult.error("无效的ID");
        }
        landpageAuditService.reAudit(req.getRecordId());
        return AjaxResult.success();
    }

    /**
     * 查询爱奇艺送审广告位白名单
     * 注:接口使用，暂无产品化功能
     */
    @GetMapping("/whitelist")
    public AjaxResult whitelist() {
//        return AjaxResult.success(whitelistService.list(WhitelistType.IQIYI_SLOT));
        return AjaxResult.success();
    }

    /**
     * 添加爱奇艺送审广告位白名单
     * 注:接口使用，暂无产品化功能
     */
    @GetMapping("/whitelist/add")
    public AjaxResult addWhitelist(Long slotId) {
        if (null == slotId) {
            return AjaxResult.error("无效的广告位ID");
        }
//        return AjaxResult.success(whitelistService.add(WhitelistType.IQIYI_SLOT, slotId));
        return AjaxResult.success();
    }

    /**
     * 移除爱奇艺送审广告位白名单
     * 注:接口使用，暂无产品化功能
     */
    @GetMapping("/whitelist/remove")
    public AjaxResult removeWhitelist(Long slotId) {
        if (null == slotId) {
            return AjaxResult.error("无效的广告位ID");
        }
//        return AjaxResult.success(whitelistService.remove(WhitelistType.IQIYI_SLOT, slotId));
        return AjaxResult.success();
    }

    /**
     * 爱奇艺落地页送审回调
     */
    @CrossOrigin
    @PostMapping("/callback")
    public AjaxResult callback(@RequestBody String content) {
        logger.info("爱奇艺落地页送审回调:content={}", content);
        landpageAuditService.callback(content);
        return AjaxResult.success();
    }
}
