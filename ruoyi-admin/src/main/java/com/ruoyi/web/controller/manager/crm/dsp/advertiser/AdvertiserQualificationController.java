package com.ruoyi.web.controller.manager.crm.dsp.advertiser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.advertiser.AdvertiserIndustryBo;
import com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity;
import com.ruoyi.system.entity.common.IndustryQualificationRequireEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.req.advertiser.qualification.AdvertiserQualificationAuditReq;
import com.ruoyi.system.req.advertiser.qualification.AdvertiserQualificationReq;
import com.ruoyi.system.service.advertiser.AdvertiserQualificationService;
import com.ruoyi.system.service.common.IndustryQualificationRequireService;
import com.ruoyi.system.service.common.IndustryService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.vo.advertiser.qualification.AdvertiserCheckResultVO;
import com.ruoyi.system.vo.advertiser.qualification.AdvertiserQualificationInfoVO;
import com.ruoyi.system.vo.advertiser.qualification.AdvertiserQualificationRequireVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;
import static com.ruoyi.common.enums.advertiser.RechargeAuditStatus.isReadyToAudit;

/**
 * [CRM后台]广告主资质信息
 *
 * <AUTHOR>
 * @date 2023/03/29
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/advertiser/qualification")
public class AdvertiserQualificationController {

    @Autowired
    private AdvertiserQualificationService advertiserQualificationService;

    @Autowired
    private IndustryQualificationRequireService industryQualificationRequireService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private IndustryService industryService;

    @Autowired
    private AccountService accountService;

    /**
     * 查询广告主资质要求
     */
    @GetMapping("/qualificationRequire")
    public Result<List<AdvertiserQualificationRequireVO>> qualificationRequire(@Validated AdvertiserQualificationReq req) {
        List<AdvertiserIndustryBo> industries = advertiserQualificationService.selectIndustryByAccountId(req.getAccountId());
        Map<Long, List<IndustryQualificationRequireEntity>> qualificationRequireMap = industryQualificationRequireService.selectMapByIndustryIds(ListUtils.mapToList(industries, AdvertiserIndustryBo::getIndustryId));
        return ResultBuilder.success(industries.stream().map(industry -> {
            AdvertiserQualificationRequireVO require = new AdvertiserQualificationRequireVO();
            require.setIndustryName(industry.getIndustryName());
            require.setQualificationList(BeanUtil.copyToList(qualificationRequireMap.get(industry.getIndustryId()), AdvertiserQualificationRequireVO.QualificationRequire.class));
            return require;
        }).collect(Collectors.toList()));
    }

    /**
     * 查询广告主资质列表
     */
    @GetMapping("/qualificationList")
    public Result<List<AdvertiserQualificationInfoVO>> qualificationList(@Validated AdvertiserQualificationReq req) {
        // 数据权限校验
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType()) && !CollUtil.contains(permission.getValues(), req.getAccountId())) {
            return ResultBuilder.fail("无访问权限");
        }

        // 查询资质列表
        List<AdvertiserQualificationEntity> qualificationList = advertiserQualificationService.selectListByAccountId(req.getAccountId());
        Map<Long, String> industryNameMap = industryService.selectIndustryNameMap(ListUtils.mapToList(qualificationList, AdvertiserQualificationEntity::getIndustryId));
        Map<Long, String> auditorNameMap = accountService.selectAccountContactMap(ListUtils.mapToList(qualificationList, AdvertiserQualificationEntity::getAuditor));

        return ResultBuilder.success(qualificationList.stream()
                .map(qualification -> {
                    AdvertiserQualificationInfoVO info = BeanUtil.copyProperties(qualification, AdvertiserQualificationInfoVO.class);
                    info.setQualificationImgs(JSON.parseArray(qualification.getQualificationImg(), String.class));
                    info.setIndustryName(industryNameMap.get(qualification.getIndustryId()));
                    info.setAuditorName(auditorNameMap.get(qualification.getAuditor()));
                    return info;
                }).collect(Collectors.toList()));
    }

    /**
     * 资质审核
     */
    @PostMapping("audit")
    @Log(title = "广告主资质", businessType = BusinessType.UPDATE)
    public Result<Boolean> audit(@RequestBody @Validated AdvertiserQualificationAuditReq req) {
        AdvertiserQualificationEntity qualification = advertiserQualificationService.selectById(req.getId());
        if (null == qualification) {
            return ResultBuilder.fail("未查询到资质信息");
        }
        if (!isReadyToAudit(qualification.getAuditStatus())) {
            return ResultBuilder.fail("该资质已审核");
        }

        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType()) && !CollUtil.contains(permission.getValues(), qualification.getAccountId())) {
            return ResultBuilder.fail("无审核权限");
        }

        AdvertiserQualificationEntity updateQualification = new AdvertiserQualificationEntity();
        updateQualification.setId(req.getId());
        updateQualification.setAuditStatus(req.getAuditStatus());
        updateQualification.setAuditor(SecurityUtils.getLoginUser().getCrmAccountId());
        updateQualification.setAuditReason(req.getAuditReason());
        return ResultBuilder.result(advertiserQualificationService.updateById(updateQualification));
    }

    /**
     * 查看广告主资质校验
     */
    @GetMapping("/qualificationCheck")
    public Result<AdvertiserCheckResultVO> qualificationCheck(Long advertiserId) {
        if (null == advertiserId) {
            return ResultBuilder.fail("无效的广告ID");
        }
        AdvertiserCheckResultVO resultVO = new AdvertiserCheckResultVO();
        resultVO.setAdvertiserId(advertiserId);
        resultVO.setIndustryCheckPass(advertiserQualificationService.isIndustryCheckPass(advertiserId));
        resultVO.setQualificationAuditPass(advertiserQualificationService.isQualificationAuditPass(advertiserId));
        return ResultBuilder.success(resultVO);
    }

}
