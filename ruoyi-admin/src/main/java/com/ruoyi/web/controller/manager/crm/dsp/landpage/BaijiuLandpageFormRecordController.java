package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord;
import com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecordExcel;
import com.ruoyi.system.req.datashow.BaijiuLandpageFromRecordReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.landpage.BaijiuLandpageFormRecordService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.vo.advert.AdvertSelectVO;
import com.ruoyi.system.vo.app.AppSelectVO;
import com.ruoyi.system.vo.datashow.BaijiuLandpageFormRecordVO;
import com.ruoyi.system.vo.landpage.CompanySelectVO;
import com.ruoyi.system.vo.slot.SlotSelectVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.EasyExcelUtils.exportExcel;
import static com.ruoyi.common.utils.ListUtils.mapToList;

/**
 * [CRM后台]白酒转化记录
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
@Slf4j
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/datashow/baijiu/lpForm")
public class BaijiuLandpageFormRecordController extends BaseController {

    private static final String EXPORT_EXCEL_NAME = "白酒落地页表单记录数据";

    @Autowired
    private BaijiuLandpageFormRecordService baijiuLandpageFormRecordService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    /**
     * 广告下拉列表
     */
    @GetMapping("/advertList")
    public Result<List<AdvertSelectVO>> advertList() {
        List<Long> advertIds = baijiuLandpageFormRecordService.selectTotalAdvertIds();
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        return ResultBuilder.success(advertIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(advertId -> new AdvertSelectVO(advertId, advertNameMap.get(advertId))).collect(Collectors.toList()));
    }

    /**
     * 媒体下拉列表
     */
    @GetMapping("/appList")
    public Result<List<AppSelectVO>> appList() {
        List<Long> appIds = baijiuLandpageFormRecordService.selectTotalAppIds();
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        return ResultBuilder.success(appIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(appId -> new AppSelectVO(appId, appNameMap.get(appId))).collect(Collectors.toList()));
    }

    /**
     * 广告位下拉列表
     */
    @GetMapping("/slotList")
    public Result<List<SlotSelectVO>> slotList() {
        List<Long> slotIds = baijiuLandpageFormRecordService.selectTotalSlotIds();
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(slotIds);
        return ResultBuilder.success(slotIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(slotId -> new SlotSelectVO(slotId, slotNameMap.get(slotId))).collect(Collectors.toList()));
    }

    /**
     * 广告主下拉列表
     */
    @GetMapping("/advertiserList")
    public Result<List<CompanySelectVO>> advertiserList() {
        List<Long> advertIds = baijiuLandpageFormRecordService.selectTotalAdvertIds();
        List<Long> advertiserIds = advertService.selectByIds(advertIds, Advert::getAdvertiserId);
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(advertiserIds);
        return ResultBuilder.success(advertiserIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(advertiserId -> new CompanySelectVO(advertiserId, advertiserNameMap.get(advertiserId))).collect(Collectors.toList()));
    }

    /**
     * 查询落地页单记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BaijiuLandpageFromRecordReq req) {
        // 构造查询条件
        BaijiuLandpageFormRecord param = buildQueryParam(req);

        startPage();
        List<BaijiuLandpageFormRecord> list = baijiuLandpageFormRecordService.selectList(param);

        // 补充信息
        Map<Long, String> appNameMap = appService.selectAppNameMap(mapToList(list, BaijiuLandpageFormRecord::getAppId));
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(mapToList(list, BaijiuLandpageFormRecord::getSlotId));
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(mapToList(list, BaijiuLandpageFormRecord::getAdvertId));
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(advertMap.values().stream().map(Advert::getAdvertiserId).collect(Collectors.toList()));
        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            BaijiuLandpageFormRecordVO record = BeanUtil.copyProperties(data, BaijiuLandpageFormRecordVO.class);
            record.setAppName(appNameMap.get(record.getAppId()));
            record.setSlotName(slotNameMap.get(record.getSlotId()));
            Optional.ofNullable(advertMap.get(record.getAdvertId())).ifPresent(advert -> {
                record.setAdvertiserId(advert.getAdvertiserId());
                record.setAdvertiserName(advertiserNameMap.get(advert.getAdvertiserId()));
            });
            return record;
        }));
    }

    /**
     * 导出落地页单记录列表
     */
    @Log(title = "落地页单记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(BaijiuLandpageFromRecordReq req) {
        Long crmAccountId = SecurityUtils.getLoginUser().getCrmAccountId();
        try (RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K033.join(crmAccountId), 10 * 60 * 60)) {
            if (lock == null) {
               return AjaxResult.error("正在导出，请耐心等待");
            }

            // 构造查询条件
            BaijiuLandpageFormRecord param = buildQueryParam(req);

            List<BaijiuLandpageFormRecord> list = baijiuLandpageFormRecordService.selectList(param);
            Map<Long, String> appNameMap = appService.selectAppNameMap(mapToList(list, BaijiuLandpageFormRecord::getAppId));
            Map<Long, String> slotNameMap = slotService.selectSlotNameMap(mapToList(list, BaijiuLandpageFormRecord::getSlotId));
            Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(mapToList(list, BaijiuLandpageFormRecord::getAdvertId));
            Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(advertMap.values().stream().map(Advert::getAdvertiserId).collect(Collectors.toList()));

            // 导出Excel
            List<BaijiuLandpageFormRecordExcel> excelData = list.stream().map(record -> {
                BaijiuLandpageFormRecordExcel data = BeanUtil.copyProperties(record, BaijiuLandpageFormRecordExcel.class);
                data.setAppName(appNameMap.get(record.getAppId()));
                data.setSlotName(slotNameMap.get(record.getSlotId()));
                data.setAmount(NumberUtils.fenToYuanForDouble(record.getAmount()));
                Optional.ofNullable(advertMap.get(record.getAdvertId())).ifPresent(advert -> {
                    data.setAdvertiserId(advert.getAdvertiserId());
                    data.setAdvertiserName(advertiserNameMap.get(advert.getAdvertiserId()));
                });
                return data;
            }).collect(Collectors.toList());
            return AjaxResult.success(exportExcel(EXPORT_EXCEL_NAME, excelData, BaijiuLandpageFormRecordExcel.class));
        } catch (Exception e) {
            log.error("导出落地页单记录列表异常,e:", e);
            return AjaxResult.error("导出失败,请联系系统管理员");
        }
    }

    /**
     * 构造查询条件
     *
     * @param req 参数
     * @return 查询条件
     */
    private BaijiuLandpageFormRecord buildQueryParam(BaijiuLandpageFromRecordReq req) {
        BaijiuLandpageFormRecord param = new BaijiuLandpageFormRecord();
        param.setAdvertIds(req.getAdvertIds());
        param.setLandpageUrl(req.getLandpageUrl());
        param.setStartDate(req.getStartDate());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        param.setSlotIds(req.getSlotIds());
        param.setAppIds(req.getAppIds());
        mergeParamIds(param.getAdvertIds(), advertService.selectAdvertIdsByAdvertiserIds(req.getAdvertiserIds()));
        if (StrUtil.isNumeric(req.getPhone())) {
            param.setPhone(req.getPhone());
        } else {
            param.setName(req.getPhone());
        }
        return param;
    }
}
