package com.ruoyi.web.controller.engine;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.SmsPlatformEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.common.utils.UserAgentUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.youku.Md5Util;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.manager.sms.CodeSmsManager;
import com.ruoyi.system.req.engine.LandPageFormReq;
import com.ruoyi.system.req.landpage.LandpageInfoReq;
import com.ruoyi.system.req.landpage.LpBindReq;
import com.ruoyi.system.req.sms.PhoneCardSmsReq;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.DomainCacheService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.engine.cache.MobileCacheService;
import com.ruoyi.system.service.landpage.LandpageService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.vo.landpage.LandpageInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.InnerLogType.QUICKAPP_FAIL_TO_BROWSER;

/**
 * 落地页(不鉴权)
 *
 * <AUTHOR>
 * @date 2021/8/31
 */
@Slf4j
@RestController
@RequestMapping("/lp")
public class LandpageController {

    @Autowired
    private LandpageService landpageService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private DomainCacheService domainCacheService;

    @Autowired
    private StatService statService;

    @Autowired
    private MobileCacheService mobileCacheService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CodeSmsManager codeSmsManager;

    @Autowired
    private OrderService orderService;


    /**
     * 查询落地页详情
     */
    @CrossOrigin
    @GetMapping("info")
    public Result<LandpageInfoVO> info(LandpageInfoReq req, HttpServletRequest request){
        try{
            // 落地页曝光埋点
            statService.landpageExposure(req.getOrderId());
            // 快应用启动失败埋点
            if (StrUtil.equalsIgnoreCase(req.getClient(), "browser")) {
                statService.innerLogByOrderId(QUICKAPP_FAIL_TO_BROWSER, req.getOrderId());
            }
        }catch (Exception e){
            //保证页面正常展示，不记录埋点
            log.warn("落地页曝光埋点异常,orderId:{},e:",req.getOrderId(),e);
        }

        Landpage landpage = landpageCacheService.selectLandpageCache(req.getKey());
        if(Objects.isNull(landpage)){
            throw new CustomException(ErrorCode.E110002);
        }
        LandpageInfoVO landpageInfoVO = BeanUtil.copyProperties(landpage, LandpageInfoVO.class);
        // 查询备案号和主体
        String domain = UrlUtils.extractDomain(req.getReferer());
        Optional.ofNullable(domainCacheService.selectDomainCache(domain)).ifPresent(domainCache -> {
            landpageInfoVO.setIcp(domainCache.getIcpNo());
            landpageInfoVO.setIcpSubject(domainCache.getIcpSubject());
        });
        // UserAgent解析
        Optional.ofNullable(UserAgentUtils.getModel(request.getHeader("User-Agent"))).ifPresent(model ->
                landpageInfoVO.setMobileBrand(mobileCacheService.getBrandByModel(model))
        );
        // 生成秘钥
        landpageInfoVO.setSecret(Md5Util.MD5(req.getOrderId() + IpUtils.getIpAddr(request)));

        return ResultBuilder.success(landpageInfoVO);

    }
    /**
     * 落地页转化表单提交
     */
    @CrossOrigin
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody LandPageFormReq req) {
        if (StringUtils.isBlank(req.getName())
                || StringUtils.isBlank(req.getPhone()) || StringUtils.isBlank(req.getIdCard())
                || StringUtils.isBlank(req.getAreaNum()) || StringUtils.isBlank(req.getAddress())) {
            return AjaxResult.error("参数异常");
        }
        if (StringUtils.isBlank(req.getOrderId())) {
            return AjaxResult.error("链接已失效");
        }
        if (req.getPhone().length() != 11) {
            return AjaxResult.error("参数异常");
        }
        req.setIdCard(req.getIdCard().toUpperCase());
        if (!IdcardUtil.isValidCard(req.getIdCard())) {
            return AjaxResult.error("请输入有效的身份证号");
        }
        if (req.getAddress().length() < 6) {
            return AjaxResult.error("请输入正确的详细地址信息");
        }
        int age = IdcardUtil.getAgeByIdCard(req.getIdCard(), new Date());
        if (age < 16 || age > 65) {
            return AjaxResult.error(ErrorCode.E110001);
        }
        if (BooleanUtil.isTrue(req.getEnabledSmsCode())){
            // 校验验证码
            if (StringUtils.isBlank(req.getVerificationCode())) {
                return AjaxResult.error("验证码不能为空");
            }
            String code = redisCache.getCacheObject(EngineRedisKeyFactory.K117.join(req.getPhone()));
            if (StringUtils.isBlank(code)) {
                return AjaxResult.error("验证码已失效，请重新获取");
            }
            if (!StrUtil.equals(code, req.getVerificationCode())) {
                return AjaxResult.error("验证码错误");
            }
        }

        // 防刷处理
        String idCardMd5 = Md5Utils.hash(req.getIdCard());

        // 分布式锁限制频次
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K016.join(idCardMd5), 60);
        if (lock == null) {
            return AjaxResult.error("订单已提交，正在处理~");
        }
        try {
            landpageService.formSubmit(req);
        } catch (CustomException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            return AjaxResult.error("订单已提交，正在处理~");
        } finally {
            lock.unlock();
        }
        return AjaxResult.success();
    }

    /**
     * 查询行政区划列表
     */
    @GetMapping("/areaList")
    public AjaxResult areaList(String lpk, String areaNum) {
        String tag = landpageCacheService.getLandpageTag(lpk);
        return AjaxResult.success(areaService.queryAreaListCache(StringUtils.defaultString(tag), StringUtils.defaultString(areaNum)));
    }

    /**
     * 落地页表单行为埋点
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/action/stat")
    public AjaxResult actionStat() {
        // 移除长期未使用的功能，线上前端会调用，故保留接口
        return AjaxResult.success();
    }


    /**
     * 号卡落地页短信发送接口
     * @param req
     * @return
     */
    @PostMapping("/phoneCard/sms")
    public AjaxResult yzxSendCode(@RequestBody PhoneCardSmsReq req){
        if (StringUtils.isBlank(req.getOrderId()) || StringUtils.isBlank(req.getPhone())||!PhoneUtil.isMobile(req.getPhone())) {
            return AjaxResult.success("参数异常", false);
        }
        if (null==orderService.selectByOrderId(req.getOrderId())){
            return AjaxResult.success("无效的订单号",false);
        }
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K118.join(req.getPhone()), 30);
        if (lock == null) {
            Long expire = NumberUtils.defaultLong(redisCache.getExpire(EngineRedisKeyFactory.K118.join(req.getPhone())));
            return AjaxResult.success("验证码已发送，" + expire + "秒后可重新发送", false);
        }
        String timesKey = EngineRedisKeyFactory.K119.join(req.getPhone(), DateUtil.today());
        Long times = NumberUtils.defaultLong(redisAtomicClient.getLong(timesKey));
        if (times >= 10) {
            return AjaxResult.success("该手机号发送验证码次数过多，请换个手机号", false);
        }
        String code = RandomUtil.randomNumbers(4);
        redisCache.setCacheObject(EngineRedisKeyFactory.K117.join(req.getPhone()), code, 15, TimeUnit.MINUTES);
        boolean sendResult=false;
        if (Objects.equals(req.getPlatformType(), SmsPlatformEnum.YUN_ZHI_XIN.getCode())){
            sendResult = codeSmsManager.sendByYunZhiXin(req.getPhone(), code,req.getOrderId());
        }
        if (sendResult) {
            redisAtomicClient.incrBy(timesKey, 1, 1, TimeUnit.HOURS);
            // 前端根据data判断是否操作成功，若data为false则toast展示msg信息
            return AjaxResult.success(true);
        }
        return AjaxResult.success("验证码发送失败", false);
    }

    /**
     * 落地页绑定订单号参数
     */
    @CrossOrigin
    @PostMapping("/bind")
    public Result<Void> bind(@RequestBody LpBindReq req) {
        log.info("落地页绑定订单号参数，req={}", JSON.toJSONString(req));
        if (StringUtils.isNotBlank(req.getOrderId())) {
            if (StringUtils.isNotBlank(req.getYtxOpenId())) {
                redisCache.setCacheObject(EngineRedisKeyFactory.K141.join(req.getOrderId()), req.getYtxOpenId(), 7, TimeUnit.DAYS);
            }
            if (StringUtils.isNotBlank(req.getPhone())) {
                String phone = ReUtil.isMatch("^1[3-9]\\d{9}$", req.getPhone()) ? req.getPhone() : Base64.decodeStr(req.getPhone());
                redisCache.setCacheObject(EngineRedisKeyFactory.K142.join(req.getOrderId()), phone, 7, TimeUnit.DAYS);
            }
        }
        return ResultBuilder.success();
    }
}
