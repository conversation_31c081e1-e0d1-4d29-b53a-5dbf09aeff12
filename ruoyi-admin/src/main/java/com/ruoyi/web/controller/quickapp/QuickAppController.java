package com.ruoyi.web.controller.quickapp;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Validator;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.constant.QuickAppConstants;
import com.ruoyi.common.constant.QuickAppRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.system.entity.quickapp.QuickappContentEntity;
import com.ruoyi.system.entity.quickapp.QuickappDynamicEntity;
import com.ruoyi.system.entity.quickapp.QuickappGoodsEntity;
import com.ruoyi.system.entity.quickapp.QuickappUserEntity;
import com.ruoyi.system.req.quickapp.QuickAppLoginReq;
import com.ruoyi.system.req.quickapp.QuickAppPublishReq;
import com.ruoyi.system.req.quickapp.QuickAppRegisterReq;
import com.ruoyi.system.req.quickapp.QuickAppSendEmailReq;
import com.ruoyi.system.service.quickapp.QuickappContentService;
import com.ruoyi.system.service.quickapp.QuickappDynamicService;
import com.ruoyi.system.service.quickapp.QuickappGoodsService;
import com.ruoyi.system.service.quickapp.QuickappUserService;
import com.ruoyi.system.vo.quickapp.QuickAppDynamicVO;
import com.ruoyi.system.vo.quickapp.QuickAppFlowerListVO;
import com.ruoyi.system.vo.quickapp.QuickAppGoodsVO;
import com.ruoyi.system.vo.quickapp.QuickAppInfoVO;
import com.ruoyi.system.vo.quickapp.QuickAppUserInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.SendEmailType.QUICK_APP_REGISTER;

/**
 * [快应用]快应用接口
 *
 * <AUTHOR>
 * @date 2022/4/1 4:57 下午
 */
@Slf4j
@RestController
@RequestMapping("/quickapp")
public class QuickAppController extends BaseController {

    @Autowired
    private QuickappContentService quickappContentService;
    @Autowired
    private QuickappUserService quickappUserService;
    @Autowired
    private SysLoginService loginService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private QuickappDynamicService quickappDynamicService;
    @Autowired
    private QuickappGoodsService quickappGoodsService;

    /**
     * 花搜索
     */
    @GetMapping("search")
    public Result<QuickAppInfoVO> search(String search){
        QuickappContentEntity quickappContentEntity = quickappContentService.selectByTitle(search);
        if(Objects.isNull(quickappContentEntity)){
            return ResultBuilder.success(null);
        }
        return ResultBuilder.success(BeanUtil.copyProperties(quickappContentEntity,QuickAppInfoVO.class));
    }

    /**
     * 快应用花影列表
     */
    @GetMapping("list")
    public Result<List<QuickAppFlowerListVO>> list(){
        List<QuickappContentEntity> quickappContentEntities = quickappContentService.selectList();
        return ResultBuilder.success(BeanUtil.copyToList(quickappContentEntities, QuickAppFlowerListVO.class));
    }

    /**
     * 首页列表
     */
    @GetMapping("indexList")
    public TableDataInfo<QuickAppDynamicVO> indexList(){
        startPage();
        List<QuickappDynamicEntity> quickappDynamicEntities = quickappDynamicService.selectList();
        List<Long> userIds = quickappDynamicEntities.stream().map(QuickappDynamicEntity::getUserId).collect(Collectors.toList());
        List<QuickappUserEntity> quickappUserEntities = quickappUserService.selectByUserIds(userIds);
        Map<Long, QuickappUserEntity> userEntityMap = quickappUserEntities.stream().collect(Collectors.toMap(QuickappUserEntity::getId, Function.identity(), (v1, v2) -> v1));

        return getDataTable(PageInfoUtils.dto2Vo(quickappDynamicEntities, entity ->{
            QuickAppDynamicVO quickAppDynamicVO = BeanUtil.copyProperties(entity, QuickAppDynamicVO.class);
            QuickappUserEntity userEntity = userEntityMap.get(entity.getUserId());
            if(Objects.nonNull(userEntity)){
                quickAppDynamicVO.setUserAvatar(userEntity.getUserAvatar());
                quickAppDynamicVO.setUserName(userEntity.getUserName());
            }
            return quickAppDynamicVO;
        }));
    }

    /**
     * 商品列表
     */
    @GetMapping("goodsList")
    public TableDataInfo<QuickAppGoodsVO> goodsList(){
        startPage();
        List<QuickappGoodsEntity> quickappGoodsEntitys = quickappGoodsService.selectList();

        return getDataTable(PageInfoUtils.dto2Vo(quickappGoodsEntitys, entity ->{
            QuickAppGoodsVO goodsVO = BeanUtil.copyProperties(entity, QuickAppGoodsVO.class);
            return goodsVO;
        }));
    }

    /**
     * 查询商品详情
     */
    @GetMapping("goodsInfo")
    public Result<QuickAppGoodsVO> goodsInfo(@Validated @NotNull(message = "id不能为空") Long id){
        QuickappGoodsEntity entity = quickappGoodsService.selectById(id);

        return ResultBuilder.success(BeanUtil.copyProperties(entity, QuickAppGoodsVO.class));
    }

    /**
     * 根据动态id查询详情
     */
    @GetMapping("dynamicInfo")
    public Result<QuickAppDynamicVO> dynamicInfo(@Validated @NotNull(message = "id不能为空") Long id){
        QuickappDynamicEntity entity = quickappDynamicService.selectById(id);
        Long userId = entity.getUserId();

        QuickAppDynamicVO quickAppDynamicVO = BeanUtil.copyProperties(entity, QuickAppDynamicVO.class);
        QuickappUserEntity userEntity = quickappUserService.selectById(userId);
        if(Objects.nonNull(userEntity)){
            quickAppDynamicVO.setUserName(userEntity.getUserName());
            quickAppDynamicVO.setUserAvatar(userEntity.getUserAvatar());
        }
        return ResultBuilder.success(quickAppDynamicVO);

    }

    /**
     * 发布动态
     */
    @PostMapping("publish")
    public Result<Boolean> publish(@Validated @RequestBody QuickAppPublishReq req){
        Long userId = redisCache.getCacheObject(QuickAppRedisKeyFactory.K001.join(req.getToken()));
        if(NumberUtils.isNullOrLteZero(userId)){
            throw new CustomException(ErrorCode.E130002);
        }
        QuickappDynamicEntity entity = BeanUtil.copyProperties(req,QuickappDynamicEntity.class);
        entity.setUserId(userId);
        Boolean insert = quickappDynamicService.insert(entity);
        return ResultBuilder.success(insert);
    }

    /**
     * 用户信息
     */
    @GetMapping("userInfo")
    public Result<QuickAppUserInfoVO> userInfo(String token){
        Long userId = redisCache.getCacheObject(QuickAppRedisKeyFactory.K001.join(token));
        if(Objects.isNull(userId)){
            throw new CustomException(ErrorCode.E130002);
        }
        QuickappUserEntity entity = quickappUserService.selectById(userId);
        if(Objects.isNull(entity)){
            throw new CustomException(ErrorCode.E130002);
        }
        QuickAppUserInfoVO quickAppUserInfoVO = BeanUtil.copyProperties(entity, QuickAppUserInfoVO.class);
        quickAppUserInfoVO.setUserId(userId);
        return ResultBuilder.success(quickAppUserInfoVO);
    }

    /**
     * 用户注册
     */
    @PostMapping("register")
    public Result<Boolean> register(@Validated @RequestBody QuickAppRegisterReq req){
        // 校验验证码
        String emailMD5 = Md5Utils.hash(req.getEmail());
        String key = Constants.CAPTCHA_VERIFY_KEY + emailMD5;
        String value = redisCache.getCacheObject(key);
        if (!Objects.equals(value, req.getCode())) {
            throw new CustomException(ErrorCode.E101005);
        }

        QuickappUserEntity quickappUserEntity = quickappUserService.selectByEmail(req.getEmail());
        if(Objects.nonNull(quickappUserEntity)){
            throw new CustomException(ErrorCode.E101004);
        }

        QuickappUserEntity entity = BeanUtil.copyProperties(req, QuickappUserEntity.class);
        entity.setPasswd(Md5Utils.hash(req.getPasswd()));
        //随机头像
        entity.setUserAvatar(QuickAppConstants.AVATARS.get(RandomUtils.nextInt(0,QuickAppConstants.AVATARS.size())));
        Boolean insert = quickappUserService.insert(entity);
        return ResultBuilder.success(insert);
    }

    /**
     * 登陆
     */
    @PostMapping("login")
    public Result<String> login(@Validated @RequestBody QuickAppLoginReq req){
        QuickappUserEntity entity = quickappUserService.selectByEmail(req.getEmail());
        if(Objects.isNull(entity)){
            throw new CustomException(ErrorCode.E130001);
        }

        if(Objects.equals(Md5Utils.hash(entity.getEmail()),entity.getEmail())){
            throw new CustomException(ErrorCode.E130001);
        }
        String token = Md5Utils.hash(req.getEmail());
        redisCache.setCacheObject(QuickAppRedisKeyFactory.K001.join(token), entity.getId(),30, TimeUnit.DAYS);
        return ResultBuilder.success(token);
    }

    /**
     * 发送验证码
     */
    @PostMapping("send")
    public AjaxResult send(@RequestBody @Validated QuickAppSendEmailReq req){
        // 参数校验
        if (!Validator.isEmail(req.getEmail())) {
            return AjaxResult.error("请输入有效的邮箱");
        }

        // 检查邮箱是否已注册
        QuickappUserEntity quickappUserEntity = quickappUserService.selectByEmail(req.getEmail());
        if (Objects.nonNull(quickappUserEntity)) {
            return AjaxResult.error("该邮箱已注册");
        }

        try {
            loginService.sendVerificationEmail(req.getEmail(), QUICK_APP_REGISTER);
        } catch (CustomException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            return AjaxResult.error("获取验证码失败");
        }
        return AjaxResult.success();
    }

    @GetMapping("insert")
    public Result<Boolean> insert(){
        //爬取花百科网站的数据
        List<String> flowerList = Lists.newArrayList("tbzw","lkzw","sszw","qgzw","sgzw","jingtian","xrz","panxing","shier","kqluomo");
        flowerList.forEach(flower ->{
            String response = HttpUtils.sendGet("https://www.huabaike.com/"+flower+"/", null);
            Document document = Jsoup.parse(response);
            Elements elements = document.getElementsByClass("zhiwuImg");
            if(CollectionUtils.isEmpty(elements)){
                return;
            }

            Elements allFlower = elements.get(0).getElementsByAttributeValueStarting("href", "/"+flower);
            Map<String,QuickappContentEntity> resultMap = new HashMap<>();
            Set<String> urls = allFlower.stream().map(element -> {
                String url = element.attr("href");
                if(element.children().size() > 0){
                    Element child = element.child(0);
                    String img = child.attr("src");
                    String title = child.attr("title");
                    System.out.println("标题:"+title+"---img:"+img);
                    QuickappContentEntity entity = new QuickappContentEntity();
                    entity.setImage(img);
                    entity.setTitle(title);
                    resultMap.put(title,entity);
                }
                return url;
            }).collect(Collectors.toSet());

            urls.stream().forEach(url ->{
                String infoResponse = HttpUtils.sendGet("https://www.huabaike.com"+url, null);
                Document infoDocument = Jsoup.parse(infoResponse);
                Elements h1 = infoDocument.getElementsByTag("h1");
                String title = h1.get(0).text();
                Elements content = infoDocument.getElementsByClass("content");
                String result = content.get(0).toString();
                QuickappContentEntity entity = resultMap.get(title);
                entity.setContent(result);
                try {
                    Thread.sleep(2000L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                log.info("新增数据成功：{}",title);
                quickappContentService.insert(entity);
            });
        });

        log.info("数据爬取完成");
        return ResultBuilder.success();
    }
}
