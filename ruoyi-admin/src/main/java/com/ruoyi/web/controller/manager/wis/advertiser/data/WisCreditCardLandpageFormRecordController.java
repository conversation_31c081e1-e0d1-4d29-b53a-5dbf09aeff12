package com.ruoyi.web.controller.manager.wis.advertiser.data;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.entity.landpage.CreditCardLandpageFormRecordEntity;
import com.ruoyi.system.req.datashow.CreditCardLandpageFromRecordReq;
import com.ruoyi.system.req.wis.WisCreditCardLandpageFromRecordReq;
import com.ruoyi.system.service.common.IdCardService;
import com.ruoyi.system.service.landpage.CreditCardLandpageFormRecordService;
import com.ruoyi.system.vo.wis.WisCreditCardLandpageFormRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  [广告主平台]信用卡表单
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@RestController
@RequestMapping("/wis/advertiser/data/form/creditCard")
public class WisCreditCardLandpageFormRecordController extends BaseController {

    @Autowired
    private CreditCardLandpageFormRecordService creditCardLandpageFormRecordService;

    @Autowired
    private IdCardService idCardService;

    /**
     * 查询表单
     */
    @GetMapping("/list")
    public TableDataInfo<WisCreditCardLandpageFormRecordVO> list(WisCreditCardLandpageFromRecordReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非广告主不展示
        if (!AccountMainType.isAdvertiser(user.getMainType())) {
            return getDataTable(Collections.emptyList());
        }

        startPage();
        List<CreditCardLandpageFormRecordEntity> list = creditCardLandpageFormRecordService.selectList(convertParam(req));
        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            WisCreditCardLandpageFormRecordVO recordVO = BeanUtil.copyProperties(record, WisCreditCardLandpageFormRecordVO.class);
            recordVO.setIdCard(DesensitizedUtil.idCardNum(idCardService.decrypt(record.getIdCard()), 14, 0));
            recordVO.setPhone(DesensitizedUtil.mobilePhone(recordVO.getPhone()));
            recordVO.setCommitTime(record.getGmtCreate());
            return recordVO;
        }));
    }

    /**
     * 导出表单
     */
    @Log(title = "广告主后台信用卡表单导出", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(WisCreditCardLandpageFromRecordReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非广告主不展示
        if (!AccountMainType.isAdvertiser(user.getMainType())) {
            return AjaxResult.success();
        }

        List<CreditCardLandpageFormRecordEntity> list = creditCardLandpageFormRecordService.selectList(convertParam(req));
        ExcelUtil<WisCreditCardLandpageFormRecordVO> util = new ExcelUtil<>(WisCreditCardLandpageFormRecordVO.class);
        return util.exportExcel(list.stream().map(record -> {
            WisCreditCardLandpageFormRecordVO recordVO = BeanUtil.copyProperties(record, WisCreditCardLandpageFormRecordVO.class);
            recordVO.setIdCard(idCardService.decrypt(record.getIdCard()));
            recordVO.setCommitTime(record.getGmtCreate());
            return recordVO;
        }).collect(Collectors.toList()), "表单明细");
//        ExcelUtil.encrypt(result.get("msg").toString() , user.getEmail());
    }

    private CreditCardLandpageFromRecordReq convertParam(WisCreditCardLandpageFromRecordReq req) {
        CreditCardLandpageFromRecordReq param = new CreditCardLandpageFromRecordReq();
        param.setAdvertiserIds(Collections.singletonList(SecurityUtils.getLoginUser().getCrmAccountId()));
        param.setStartDate(req.getStartDate());
        if (null != req.getEndDate()) {
            param.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        return param;
    }
}
