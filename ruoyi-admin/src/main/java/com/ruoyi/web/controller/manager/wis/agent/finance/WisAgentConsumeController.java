package com.ruoyi.web.controller.manager.wis.agent.finance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.system.bo.agent.AgentConsumeDataExcelBo;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeRecordEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeListReq;
import com.ruoyi.system.req.agent.finance.WisAgentClientConsumeReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.agent.AgentClientConsumeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.ruoyi.common.core.page.TableSupport.PAGE_NUM;
import static com.ruoyi.common.core.page.TableSupport.PAGE_SIZE;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * [代理商平台]消费记录
 *
 * <AUTHOR>
 * @date 2022/10/27
 */
@RequestMapping("/wis/agent/fiance/consume")
@RestController
public class WisAgentConsumeController extends BaseController {

    @Autowired
    private AdvertiserConsumeRecordService advertiserConsumeRecordService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    /**
     * 查询广告主消费记录
     */
    @GetMapping("/list")
    public TableDataInfo<AgentClientConsumeVO> list(WisAgentClientConsumeReq req) {
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return getDataTable(Collections.emptyList());
        }
        List<AgentClientConsumeVO> dataList = getConsumeList(req, user.getCrmAccountId());
        if (CollectionUtils.isEmpty(dataList)) {
            return getDataTable(Collections.emptyList());
        }
        int total = dataList.size();
        int pageNum = ServletUtils.getParameterToInt(PAGE_NUM);
        int pageSize = ServletUtils.getParameterToInt(PAGE_SIZE);
        dataList = ListUtil.page(pageNum - 1, pageSize, dataList);

        // 构造分页数据
        Page<AgentClientConsumeVO> page = new Page<>(pageNum, pageSize);
        page.setTotal(total);
        PageInfo<AgentClientConsumeVO> result = new PageInfo<>(page);
        result.setList(dataList);
        return getDataTable(result);
    }

    /**
     * 导出消费记录
     */
    @Log(title = "代理商后台消费记录", businessType = BusinessType.EXPORT)
    @GetMapping("export")
    public AjaxResult export(WisAgentClientConsumeReq req) {
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return AjaxResult.error("参数错误");
        }

        List<AgentClientConsumeVO> dataList = getConsumeList(req, user.getCrmAccountId());
        List<AgentConsumeDataExcelBo> consumeExcelBos = dataList.stream().map(data -> {
            AgentConsumeDataExcelBo excelBo = BeanUtil.copyProperties(data, AgentConsumeDataExcelBo.class);
            excelBo.setConsumeAmount(NumberUtils.fenToYuan(data.getConsumeAmount()));
            return excelBo;
        }).collect(Collectors.toList());
        String fileName = UUID.randomUUID().toString() + "_消费记录.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, AgentConsumeDataExcelBo.class).sheet("消费记录").doWrite(consumeExcelBos);
        return AjaxResult.success(fileName);
    }

    /**
     * 查询消费记录
     */
    private List<AgentClientConsumeVO> getConsumeList(WisAgentClientConsumeReq req, Long agentId) {
        // 查询代理商下所有广告主ID
        List<Long> advertiserIds = agentService.selectAdvertiserIdsByAgent(agentId);
        if (CollectionUtils.isEmpty(advertiserIds) || (null != req.getAdvertiserId() && !advertiserIds.contains(req.getAdvertiserId()))) {
            return Collections.emptyList();
        }
        // 消费记录
        AdvertiserConsumeListReq param = new AdvertiserConsumeListReq();
        param.setAccountId(req.getAdvertiserId());
        param.setAccountIds(advertiserIds);
        param.setStartDate(req.getStartDate());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        List<AdvertiserConsumeRecordEntity> list = advertiserConsumeRecordService.selectList(param);

        // 额外数据
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(advertiserIds);
        List<Long> offlineAdvertiserIds = whitelistService.list(OFFLINE_DATA_ADVERTISER, Long.class);
        Map<Long, List<Date>> invisibleDateMap = dspAdvertiserConsumeRecordService.selectInvisibleDateMap(advertiserIds);
        Map<String, DspAdvertiserConsumeRecordEntity> advertiserConsumeRecordMap = dspAdvertiserConsumeRecordService.selectMap(req.getStartDate(), req.getEndDate(), advertiserIds);

        // 构造分页结果
        Date today = DateUtil.beginOfDay(new Date());
        return list.stream()
                .filter(record -> (null == invisibleDateMap.get(record.getAccountId()) || !invisibleDateMap.get(record.getAccountId()).contains(record.getCurDate()))
                        && (!offlineAdvertiserIds.contains(record.getAccountId()) || (record.getCurDate().before(today))))
                .map(record -> {
                    AgentClientConsumeVO consumeVO = new AgentClientConsumeVO();
                    consumeVO.setCurDate(record.getCurDate());
                    consumeVO.setConsumeAmount(record.getConsumeAmount());
                    consumeVO.setAdvertiserName(advertiserNameMap.get(record.getAccountId()));
                    Optional.ofNullable(advertiserConsumeRecordMap.get(DateUtil.formatDate(record.getCurDate()) + "_" + record.getAccountId())).ifPresent(data -> {
                        consumeVO.setConsumeAmount(data.getConsumeAmount());
                    });
                    return consumeVO;
                })
                .sorted((o1, o2) -> {
                    int c = o2.getCurDate().compareTo(o1.getCurDate());
                    return c != 0 ? c : (o2.getConsumeAmount().compareTo(o1.getConsumeAmount()));
                }).collect(Collectors.toList());
    }
}
