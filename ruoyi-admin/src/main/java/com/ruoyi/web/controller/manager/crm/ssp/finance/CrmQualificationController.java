package com.ruoyi.web.controller.manager.crm.ssp.finance;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.req.qualification.QualificationListReq;
import com.ruoyi.system.service.qualification.AccountQualificationService;
import com.ruoyi.system.vo.qualification.QualificationListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * crm管理后台 资质管理
 *
 * <AUTHOR>
 * @date 2021/9/9 5:14 下午
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("manager/crm/qualification")
public class CrmQualificationController extends BaseController {

    @Autowired
    private AccountQualificationService accountQualificationService;

    /**
     * 查询资质列表
     * @param req 请求参数
     * @return 结果
     */
    @GetMapping("list")
    public TableDataInfo<QualificationListVO> list(QualificationListReq req){
        return getDataTable(accountQualificationService.selectQualificationList(req));
    }
}
