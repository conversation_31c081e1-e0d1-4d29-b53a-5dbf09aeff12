package com.ruoyi.web.controller.manager.wis.advertiser.data;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserCpcDataExcelBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserCpcLandpageDataExcelBo;
import com.ruoyi.system.manager.advertiser.AdvertiserDataManager;
import com.ruoyi.system.req.advertiser.AdvertiserCpcDataReq;
import com.ruoyi.system.vo.advertiser.finance.WisAdvertiserCpcDataListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 广告主后台广告主Controller
 *
 * <AUTHOR>
 * @date 2022-04-07
 */
@RestController
@RequestMapping("/wis/advertiser")
public class WisAdvertiserController extends BaseController {

    @Autowired
    private AdvertiserDataManager advertiserDataManager;

    /**
     * 获取cpc数据列表(日期维度)
     */
    @GetMapping("/cpcList")
    public TableDataInfo<WisAdvertiserCpcDataListVO> cpcList(AdvertiserCpcDataReq req){
        req.setAdvertiserId(SecurityUtils.getLoginUser().getCrmAccountId());
        PageInfo<WisAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.selectCpcDataList(req,false);
        return getDataTable(pageInfo);
    }

    /**
     * 获取cpc数据列表(落地页链接维度)
     */
    @GetMapping("/cpcLandpageList")
    public TableDataInfo<WisAdvertiserCpcDataListVO> cpcLandpageList(AdvertiserCpcDataReq req){
        req.setAdvertiserId(SecurityUtils.getLoginUser().getCrmAccountId());
        PageInfo<WisAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.selectCpcLandpageDataList(req,false);
        return getDataTable(pageInfo);
    }

    /**
     * 导出广告主cpc数据(日期维度)
     */
    @Log(title = "广告主后台cpc表单记录", businessType = BusinessType.EXPORT)
    @GetMapping("cpcExport")
    public AjaxResult cpcExport(AdvertiserCpcDataReq req){
        req.setAdvertiserId(SecurityUtils.getLoginUser().getCrmAccountId());
        PageInfo<WisAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.selectCpcDataList(req,true);
        List<AdvertiserCpcDataExcelBo> cpcDataExcelBos = pageInfo.getList().stream().map(data -> {
            AdvertiserCpcDataExcelBo excelBo = BeanUtil.copyProperties(data, AdvertiserCpcDataExcelBo.class);
            excelBo.setConsumeAmount(NumberUtils.fenToYuan(data.getConsumeAmount()));
            return excelBo;
        }).collect(Collectors.toList());
        String fileName = UUID.randomUUID().toString() + "_消费记录数据.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, AdvertiserCpcDataExcelBo.class).sheet("消费记录数据").doWrite(cpcDataExcelBos);
        return AjaxResult.success(fileName);
    }

    /**
     * 导出广告主cpc数据(落地页链接维度)
     */
    @Log(title = "广告主后台cpc表单记录", businessType = BusinessType.EXPORT)
    @GetMapping("cpcLandpageExport")
    public AjaxResult cpcLandpageExport(AdvertiserCpcDataReq req){
        req.setAdvertiserId(SecurityUtils.getLoginUser().getCrmAccountId());
        PageInfo<WisAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.selectCpcLandpageDataList(req, true);
        List<AdvertiserCpcLandpageDataExcelBo> cpcDataExcelBos = pageInfo.getList().stream().map(data -> {
            AdvertiserCpcLandpageDataExcelBo excelBo = BeanUtil.copyProperties(data, AdvertiserCpcLandpageDataExcelBo.class);
            excelBo.setConsumeAmount(NumberUtils.fenToYuan(data.getConsumeAmount()));
            return excelBo;
        }).collect(Collectors.toList());
        String fileName = UUID.randomUUID().toString() + "_消费记录数据.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, AdvertiserCpcLandpageDataExcelBo.class).sheet("消费记录数据").doWrite(cpcDataExcelBos);
        return AjaxResult.success(fileName);
    }
}
