package com.ruoyi.web.controller.open;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import com.google.common.base.Joiner;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.domain.adengine.AppCacheDto;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.req.open.SspRevenueReq;
import com.ruoyi.system.req.open.SspUploadDataReq;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.datasource.SlotAppDataService;
import com.ruoyi.system.service.datasource.SlotConvOpenDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.engine.cache.AppCacheService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.slotdata.SlotMonthDataService;
import com.ruoyi.system.vo.open.SspRevenueVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.hutool.core.util.BooleanUtil.isTrue;
import static com.ruoyi.common.enums.slot.SlotDataVisibleEnum.VISIBLE;

/**
 * 媒体SSP开放接口
 *
 * <AUTHOR>
 * @date 2022-04-18
 */
@Slf4j
@RestController
@RequestMapping("/open/ssp/v1")
public class OpenSspController {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AppCacheService appCacheService;

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private SlotConvOpenDataService slotConvOpenDataService;

    @Autowired
    private SlotMonthDataService slotMonthDataService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private SlotAppDataService slotAppDataService;

    @Autowired
    public TransactionTemplate transactionTemplate;

    /**
     * 广告主回调接口
     */
    @CrossOrigin
    @GetMapping("/data")
    public AjaxResult getRevenueData(SspRevenueReq req) {
        // 参数校验
        String appKey = req.getAppKey();
        if (StringUtils.isBlank(appKey) || appKey.length() > 64 || !StringUtils.isAlphanumeric(appKey)) {
            return AjaxResult.error(ErrorCode.E111005);
        }
        if (null == req.getTimestamp() || (Math.abs(System.currentTimeMillis() - req.getTimestamp()) > 600000)) {
            return AjaxResult.error(ErrorCode.E111003);
        }
        // 解析日期
        int hour = DateUtil.thisHour(true);
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
        Date date = DateUtils.parseDate(req.getDate());
        if (null == date || date.after(yesterday) || date.before(DateUtil.offsetDay(yesterday, -365))) {
            return AjaxResult.error(ErrorCode.E111004);
        }

        // 分布式锁防重复提交
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K029.join(appKey, req.getDate()), 5);
        if (lock == null) {
            return AjaxResult.error(ErrorCode.E111002);
        }

        // 查询应用秘钥
        AppCacheDto app = appCacheService.getAppCache(appKey);
        if (null == app || StringUtils.isBlank(app.getAppSecret())) {
            return AjaxResult.error(ErrorCode.E111005);
        }

        // 签名校验
        if (!checkSignature(req, app.getAppSecret())) {
            return AjaxResult.error(ErrorCode.E111001);
        }

        // 返回结果
        SspRevenueVO revenue = new SspRevenueVO();
        revenue.setAppId(app.getId());
        revenue.setDate(date);

        // 查询数据
        SlotData param = new SlotData();
        param.setAccountId(app.getAccountId());
        param.setIsVisible(VISIBLE.getStatus());
        param.setAppId(app.getId());
        param.setStartDate(date);
        param.setEndDate(date);
        List<SlotData> dataList = slotDataService.selectSlotDataList(param);

        // 查询转化数据
        Map<Long, Integer> convMap = slotConvOpenDataService.selectMap(ListUtils.mapToList(dataList, SlotData::getSlotId), date);

        // 构造结果
        revenue.setSlotRevenueList(dataList.stream().map(data -> {
            SspRevenueVO.SlotRevenueVO slotRevenue = new SspRevenueVO.SlotRevenueVO();
            slotRevenue.setSlotId(data.getSlotId());
            if (hour >= 13 || !DateUtil.isSameDay(yesterday, data.getCurDate())) {
                slotRevenue.setSlotPv(data.getSlotRequestPv());
                slotRevenue.setSlotUv(data.getSlotRequestUv());
                slotRevenue.setRevenue(data.getAppRevenue());
                Optional.ofNullable(convMap.get(data.getSlotId())).ifPresent(slotRevenue::setConv);
            }
            return slotRevenue;
        }).collect(Collectors.toList()));
        return AjaxResult.success(revenue);
    }

    /**
     * 广告主上报接口
     */
    @CrossOrigin
    @PostMapping("/upload")
    public AjaxResult uploadData(@RequestBody SspUploadDataReq req) {
        // 参数校验
        if (null == req.getSlotId()) {
            return AjaxResult.error(ErrorCode.E111006);
        }
        String appKey = req.getAppKey();
        if (StringUtils.isBlank(appKey) || appKey.length() > 64 || !StringUtils.isAlphanumeric(appKey)) {
            return AjaxResult.error(ErrorCode.E111005);
        }
        if (null == req.getTimestamp() || (Math.abs(System.currentTimeMillis() - req.getTimestamp()) > 600000)) {
            return AjaxResult.error(ErrorCode.E111003);
        }
        // 解析日期
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());;
        Date date = DateUtils.parseDate(req.getDate());
        if (null == date || date.after(yesterday) || date.after(new Date())) {
            return AjaxResult.error(ErrorCode.E111004);
        }

        // 分布式锁防重复提交
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K059.join(appKey, req.getDate(), req.getSlotId()), 5);
        if (lock == null) {
            return AjaxResult.error(ErrorCode.E111002);
        }

        // 查询应用秘钥
        AppCacheDto app = appCacheService.getAppCache(appKey);
        if (null == app || StringUtils.isBlank(app.getAppSecret())) {
            return AjaxResult.error(ErrorCode.E111005);
        }

        // 签名校验
        if (!checkSignature(req, app.getAppSecret())) {
            return AjaxResult.error(ErrorCode.E111001);
        }

        // 校验白名单
        if (!whitelistService.contains(WhitelistType.APP_SLOT_EXPOSURE, app.getAccountId())) {
            return AjaxResult.error(ErrorCode.E111008);
        }

        // 校验广告位
        Slot slot = slotService.selectSimpleSlotById(req.getSlotId());
        if (null == slot || !Objects.equals(slot.getAccountId(), app.getAccountId())) {
            return AjaxResult.error(ErrorCode.E111009);
        }

        // 判断月账单数据是否生成
        int slotMonthDataCount = slotMonthDataService.countBySlotIdAndMonth(req.getSlotId(), DateUtils.dateTimeMonth(date));
        if (slotMonthDataCount > 0) {
            return AjaxResult.error(ErrorCode.E111007);
        }

        // 查询数据ID
        SlotData slotData = slotDataService.selectBySlotIdAndDate(req.getSlotId(), date);
        if (null == slotData) {
            return AjaxResult.error(ErrorCode.E111011);
        }
        SlotData updateSlotData = new SlotData();
        updateSlotData.setId(slotData.getId());
        updateSlotData.setAppSlotExposurePv(req.getSlotExposurePv());
        updateSlotData.setAppSlotExposureUv(req.getSlotExposureUv());
        updateSlotData.setAppSlotClickPv(req.getSlotClickPv());
        updateSlotData.setAppSlotClickUv(req.getSlotClickUv());

        // 更新数据
        Boolean result = isTrue(transactionTemplate.execute(status -> {
            slotDataService.updateSlotData(updateSlotData);
            slotAppDataService.updateAppData(slotData.getAppId(), slotData.getCurDate());
            return true;
        }));
        if (!BooleanUtil.isTrue(result)) {
            return AjaxResult.error(ErrorCode.E111010);
        }
        return AjaxResult.success();
    }

    /**
     * 签名校验
     *
     * @param req 参数
     * @return 是否校验通过
     */
    private boolean checkSignature(SspRevenueReq req, String appSecret) {
        List<String> arr = Arrays.asList(req.getAppKey(), req.getDate(), String.valueOf(req.getTimestamp()), appSecret);
        arr.sort(String::compareTo);
        return StringUtils.isNotBlank(req.getSign()) && StringUtils.equalsIgnoreCase(req.getSign(), Md5Utils.hash(Joiner.on("").join(arr)));
    }

    /**
     * 签名校验
     *
     * @param req 参数
     * @return 是否校验通过
     */
    private boolean checkSignature(SspUploadDataReq req, String appSecret) {
        List<String> arr = Arrays.asList(req.getAppKey(), req.getDate(), String.valueOf(req.getTimestamp()), appSecret, String.valueOf(req.getSlotId()));
        arr.sort(String::compareTo);
        return StringUtils.isNotBlank(req.getSign()) && StringUtils.equalsIgnoreCase(req.getSign(), Md5Utils.hash(Joiner.on("").join(arr)));
    }
}
