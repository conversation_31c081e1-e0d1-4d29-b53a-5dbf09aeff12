package com.ruoyi.web.controller.engine;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.blindbox.BlindBoxLandpageEntity;
import com.ruoyi.system.entity.blindbox.BlindBoxLandpageRecordEntity;
import com.ruoyi.system.req.blindbox.BlindBoxLandpageFormReq;
import com.ruoyi.system.req.blindbox.BlindBoxLandpageInitReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.blindbox.BlindBoxLandpageRecordService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.DomainCacheService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.blindbox.BlindBoxLandpageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.InnerLogType.BLIND_BOX_POPUP_CLICK;

/**
 * 盲盒落地页Controller(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/06/13
 */
@Slf4j
@RestController
@RequestMapping("/blindbox")
public class BlindBoxController {

    @Autowired
    private StatService statService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private DomainCacheService domainCacheService;

    @Autowired
    private BlindBoxLandpageRecordService blindBoxLandpageRecordService;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private RedisCache redisCache;


    /**
     * 获取盲盒信息
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/init")
    public Result<BlindBoxLandpageVO> init(@Validated BlindBoxLandpageInitReq req, HttpServletRequest request) {
        // 落地页曝光埋点
        statService.landpageExposure(req.getOrderId());

        BlindBoxLandpageEntity landpage = landpageCacheService.selectBlindBoxLandpageCache(req.getLandpageKey());
        if (Objects.isNull(landpage)) {
            throw new CustomException(ErrorCode.E110002);
        }
        BlindBoxLandpageVO landpageVO = BeanUtil.copyProperties(landpage, BlindBoxLandpageVO.class);

        // 查询备案号和主体
        String domain = UrlUtils.extractDomain(request.getHeader("Referer"));
        Optional.ofNullable(domainCacheService.selectDomainCache(domain)).ifPresent(domainCache -> {
            landpageVO.setIcp(domainCache.getIcpNo());
            landpageVO.setIcpSubject(domainCache.getIcpSubject());
        });
        return ResultBuilder.success(landpageVO);
    }

    /**
     * 表单提交
     */
    @CrossOrigin
    @PostMapping("/submit")
    public Result<Void> submit(@Validated @RequestBody BlindBoxLandpageFormReq req, HttpServletRequest request) {
        if (StrUtil.length(req.getPhone()) > 11) {
            return ResultBuilder.fail("无效的手机号");
        }
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            return ResultBuilder.success();
        }

        BlindBoxLandpageRecordEntity record = new BlindBoxLandpageRecordEntity();
        record.setOrderId(order.getOrderId());
        record.setAdvertId(order.getAdvertId());
        record.setActivityId(order.getActivityId());
        record.setConsumerId(order.getConsumerId());
        record.setAppId(order.getAppId());
        record.setSlotId(order.getSlotId());
        record.setLandpageKey(req.getLandpageKey());
        record.setReferer(request.getHeader("Referer"));
        record.setPhone(req.getPhone());
        record.setIp(IpUtils.getIpAddr(request));
        blindBoxLandpageRecordService.insert(record);

        // 落地页转化埋点
        statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());

        // 监控
        GlobalThreadPool.executorService.submit(() -> {
            Advert advert = advertService.selectAdvertById(order.getAdvertId());
            if (null == advert) {
                return;
            }
            String key = EngineRedisKeyFactory.K049.join(advert.getAdvertiserId());
            long times = redisAtomicClient.incrBy(key, 1, 1, TimeUnit.DAYS);
            long threshold = Convert.toLong(sysConfigService.selectConfigCacheByKey(BizConfigEnum.TAKE_THRESHOLD.getKey()), 50L);
            if (times >= threshold) {
                String sbr = "广告主ID:" + advert.getAdvertiserId() +
                        "\n广告主名称: " + advertiserService.selectAdvertiserName(advert.getAdvertiserId()) +
                        "\n最近" + threshold + "次领取未产生支付";
                DingRobotUtil.sendText(DingWebhookConfig.getOceanAlert(), sbr);
                redisCache.deleteObject(key);
            }
        });
        return ResultBuilder.success();
    }
}
