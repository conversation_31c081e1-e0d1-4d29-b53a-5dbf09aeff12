package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.PlayletPayPlatformEnum;
import com.ruoyi.common.enums.landpage.PlayletTradeStatus;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.bo.landpage.PlayletLandpageFormRecordSelectBo;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.datashow.PlayletLandpageFormRecordExcel;
import com.ruoyi.system.entity.landpage.PlayletLandpageFormRecordEntity;
import com.ruoyi.system.req.datashow.PlayletLandpageFromRecordReq;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.landpage.LandpageLiuziAlipayService;
import com.ruoyi.system.service.landpage.PlayletLandpageFormRecordService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.vo.advert.AdvertSelectVO;
import com.ruoyi.system.vo.app.AppSelectVO;
import com.ruoyi.system.vo.datashow.PlayletLandpageFormRecordVO;
import com.ruoyi.system.vo.datashow.PlayletLandpageFormStatisticVO;
import com.ruoyi.system.vo.slot.SlotSelectVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.EasyExcelUtils.exportExcel;
import static com.ruoyi.common.utils.ListUtils.mapToList;

/**
 * [CRM后台]短剧转化记录
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Slf4j
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/landpage/playlet")
public class PlayletLandpageFormRecordController extends BaseController {

    @Autowired
    private PlayletLandpageFormRecordService playletLandpageFormRecordService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private LandpageLiuziAlipayService landpageLiuziAlipayService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertiserService advertiserService;

    /**
     * 广告下拉列表
     */
    @GetMapping("/advertList")
    public Result<List<AdvertSelectVO>> advertList() {
        List<Long> advertIds = playletLandpageFormRecordService.selectTotalAdvertIds();
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        Map<Long, String> landpageNameMap = landpageLiuziAlipayService.selectLandpageNameMap(advertIds);
        return ResultBuilder.success(advertIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(advertId -> new AdvertSelectVO(advertId, landpageNameMap.getOrDefault(advertId, advertNameMap.get(advertId)))).collect(Collectors.toList()));
    }

    /**
     * 媒体下拉列表
     */
    @GetMapping("/appList")
    public Result<List<AppSelectVO>> appList() {
        List<Long> appIds = playletLandpageFormRecordService.selectTotalAppIds();
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        return ResultBuilder.success(appIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(appId -> new AppSelectVO(appId, appNameMap.get(appId))).collect(Collectors.toList()));
    }

    /**
     * 广告位下拉列表
     */
    @GetMapping("/slotList")
    public Result<List<SlotSelectVO>> slotList() {
        List<Long> slotIds = playletLandpageFormRecordService.selectTotalSlotIds();
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(slotIds);
        return ResultBuilder.success(slotIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(slotId -> new SlotSelectVO(slotId, slotNameMap.get(slotId))).collect(Collectors.toList()));
    }

    /**
     * 查询汇总数据
     */
    @GetMapping("/statistic")
    public Result<PlayletLandpageFormStatisticVO> statistic(PlayletLandpageFromRecordReq req) {
        // 构造参数
        PlayletLandpageFormRecordSelectBo param = buildQueryParam(req);

        // 查询已支付金额
        param.setTradeStatus(PlayletTradeStatus.PAID.getStatus());
        PlayletLandpageFormRecordEntity data = playletLandpageFormRecordService.selectStatistic(param);
        return ResultBuilder.success(BeanUtil.copyProperties(data, PlayletLandpageFormStatisticVO.class));
    }

    /**
     * 查询落地页单记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<PlayletLandpageFormRecordVO> list(PlayletLandpageFromRecordReq req) {
        // 构造参数
        PlayletLandpageFormRecordSelectBo param = buildQueryParam(req);

        // 查询数据
        startPage();
        List<PlayletLandpageFormRecordEntity> list = playletLandpageFormRecordService.selectList(param);

        // 补充信息
        Map<Long, String> appNameMap = appService.selectAppNameMap(mapToList(list, PlayletLandpageFormRecordEntity::getAppId));
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(mapToList(list, PlayletLandpageFormRecordEntity::getSlotId));
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(mapToList(list, PlayletLandpageFormRecordEntity::getAdvertId));
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(mapToList(new ArrayList<>(advertMap.values()), Advert::getAdvertiserId));

        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            PlayletLandpageFormRecordVO record = BeanUtil.copyProperties(data, PlayletLandpageFormRecordVO.class);
            record.setAppName(appNameMap.get(record.getAppId()));
            record.setSlotName(slotNameMap.get(record.getSlotId()));
            Optional.ofNullable(advertMap.get(data.getAdvertId())).ifPresent(advert -> {
                record.setAdvertName(advert.getAdvertName());
                record.setAdvertiserId(advert.getAdvertiserId());
                record.setAdvertiserName(advertiserNameMap.get(advert.getAdvertiserId()));

            });
            return record;
        }));
    }

    /**
     * 导出落地页单记录列表
     */
    @Log(title = "落地页单记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(PlayletLandpageFromRecordReq req) {
        // 构造参数
        PlayletLandpageFormRecordSelectBo param = buildQueryParam(req);

        // 查询数据
        List<PlayletLandpageFormRecordEntity> list = playletLandpageFormRecordService.selectList(param);

        // 补充信息
        Map<Long, String> appNameMap = appService.selectAppNameMap(mapToList(list, PlayletLandpageFormRecordEntity::getAppId));
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(mapToList(list, PlayletLandpageFormRecordEntity::getSlotId));
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(mapToList(list, PlayletLandpageFormRecordEntity::getAdvertId));
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(mapToList(new ArrayList<>(advertMap.values()), Advert::getAdvertiserId));

        // 导出Excel
        List<PlayletLandpageFormRecordExcel> excelData = list.stream().map(data -> {
            PlayletLandpageFormRecordExcel record = BeanUtil.copyProperties(data, PlayletLandpageFormRecordExcel.class);
            record.setAppName(appNameMap.get(record.getAppId()));
            record.setSlotName(slotNameMap.get(record.getSlotId()));
            Optional.ofNullable(advertMap.get(data.getAdvertId())).ifPresent(advert -> {
                record.setAdvertName(advert.getAdvertName());
                record.setAdvertiserId(advert.getAdvertiserId());
                record.setAdvertiserName(advertiserNameMap.get(advert.getAdvertiserId()));
            });
            record.setTradeStatusStr(Objects.equals(data.getTradeStatus(), 1) ? "已支付" : "未支付");
            record.setPayPlatform(PlayletPayPlatformEnum.getDescByType(data.getPayPlatform()));
            return record;
        }).collect(Collectors.toList());
        return AjaxResult.success(exportExcel("短剧落地页表单记录数据", excelData, PlayletLandpageFormRecordExcel.class));
    }

    /**
     * 构造查询条件
     *
     * @param req 参数
     * @return 查询条件
     */
    private PlayletLandpageFormRecordSelectBo buildQueryParam(PlayletLandpageFromRecordReq req) {
        PlayletLandpageFormRecordSelectBo param = BeanUtil.copyProperties(req, PlayletLandpageFormRecordSelectBo.class);
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        return param;
    }
}
