package com.ruoyi.web.controller.open;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.req.open.VivoEventReq;
import com.ruoyi.system.service.open.VivoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Vivo接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/05/23
 */
@Slf4j
@RestController
@RequestMapping("/open/oauth2/vivo")
public class VivoController {

    @Autowired
    private VivoService vivoService;

    /**
     * Vivo事件回调接口
     */
    @CrossOrigin
    @GetMapping("/callback")
    public void callback(VivoEventReq req) {
        log.info("vivo回调，req={}", JSON.toJSONString(req));
        if (StringUtils.isNotBlank(req.getCode())) {
            vivoService.getAndCacheToken(req.getClientId(), req.getCode(), req.getState());
        }
    }
}
