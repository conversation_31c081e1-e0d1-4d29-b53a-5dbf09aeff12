package com.ruoyi.web.controller.manager;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.entity.slot.SlotSpec;
import com.ruoyi.system.service.manager.SlotSpecService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 广告位规格Controller
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@RestController
@RequestMapping("/manager/spec")
public class SlotSpecController extends BaseController {

    @Autowired
    private SlotSpecService slotSpecService;

    /**
     * 查询广告位规格列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SlotSpec slotSpec) {
        startPage();
        List<SlotSpec> list = slotSpecService.selectSlotSpecList(slotSpec);
        return getDataTable(list);
    }

    /**
     * 查询所有广告位规格
     */
    @GetMapping("/listTotal")
    public AjaxResult listTotal(SlotSpec slotSpec) {
        return AjaxResult.success(slotSpecService.selectTotalSlotSpecList(slotSpec));
    }

    /**
     * 获取广告位规格详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(slotSpecService.selectSlotSpecById(id));
    }

    /**
     * 新增广告位规格
     */
    @Log(title = "广告位规格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SlotSpec slotSpec) {
        return toAjax(slotSpecService.insertSlotSpec(slotSpec));
    }

    /**
     * 修改广告位规格
     */
    @Log(title = "广告位规格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SlotSpec slotSpec) {
        return toAjax(slotSpecService.updateSlotSpec(slotSpec));
    }
}
