package com.ruoyi.web.controller.manager.crm.ssp.setting;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.enums.common.BizSwitchEnum;
import com.ruoyi.common.enums.common.MapConfigEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.system.SysConfig;
import com.ruoyi.system.manager.advertiser.AdvertiserManager;
import com.ruoyi.system.req.manager.WhitelistReq;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.manager.WhitelistVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * 白名单管理Controller
 *
 * <AUTHOR>
 * @date 2022-03-02
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/whitelist")
public class WhitelistController {

    /**
     * 配置更新通知模板
     */
    private static final String CONFIG_MODIFY_NOTICE_TEMPLATE = "业务配置更新\n\n配置名称: {}\n配置内容: {}";

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AdvertiserManager advertiserManager;

    /**
     * 查询白名单列表
     */
    @GetMapping("/list")
    public AjaxResult list() {
        Map<String, String> configMap = sysConfigService.selectConfigMap();

        List<WhitelistVO> list = new ArrayList<>();

        // 查询业务配置
        list.addAll(Arrays.stream(BizConfigEnum.values()).map(type -> {
            WhitelistVO vo = new WhitelistVO();
            vo.setKey(type.getKey());
            vo.setDesc(type.getDesc());
            vo.setPlaceholder(type.getPlaceholder());
            vo.setElements(configMap.get(type.getKey()));
            return vo;
        }).collect(Collectors.toList()));

        // 查询白名单配置
        list.addAll(Arrays.stream(WhitelistType.values()).map(type -> {
            WhitelistVO vo = new WhitelistVO();
            vo.setKey(type.getKey());
            vo.setDesc(type.getDesc());
            vo.setPlaceholder(type.getPlaceholder());

            List<String> elements = JSON.parseArray(configMap.get(type.getKey()), String.class);
            if (CollectionUtils.isNotEmpty(elements)) {
                vo.setElements(Joiner.on(",").join(elements));
            }
            return vo;
        }).collect(Collectors.toList()));

        // 查询映射配置
        list.addAll(Arrays.stream(MapConfigEnum.values()).map(type -> {
            WhitelistVO vo = new WhitelistVO();
            vo.setKey(type.getKey());
            vo.setDesc(type.getDesc());
            vo.setPlaceholder(type.getPlaceholder());

            List<String> elements = JSON.parseArray(configMap.get(type.getKey()), String.class);
            if (CollectionUtils.isNotEmpty(elements)) {
                vo.setElements(Joiner.on(",").join(elements));
            }
            return vo;
        }).collect(Collectors.toList()));

        // 查询业务开关
        list.addAll(Arrays.stream(BizSwitchEnum.values()).map(type -> {
            WhitelistVO vo = new WhitelistVO();
            vo.setKey(type.getKey());
            vo.setDesc(type.getDesc());
            vo.setPlaceholder(type.getPlaceholder());
            vo.setElements(redisCache.hasKey(EngineRedisKeyFactory.K015.join(type.getKey())) ? "关" : "开");
            return vo;
        }).collect(Collectors.toList()));
        return AjaxResult.success(list);
    }

    /**
     * 修改白名单
     */
    @Log(title = "白名单", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult update(@RequestBody WhitelistReq req) {
        // 参数校验
        if (StringUtils.isBlank(req.getKey())) {
            return AjaxResult.error(ErrorCode.ARGS);
        }
        req.setElements(StringUtils.defaultString(req.getElements()));

        // 特殊业务逻辑,不走通用更新
        if (Objects.equals(req.getKey(), WhitelistType.ADJUST_DATA_SLOT.getKey())) {
            throw new CustomException("请通过广告位设置修改");
        }
        if (Objects.equals(req.getKey(), MapConfigEnum.SLOT_KS_COST_MAP.getKey())) {
            throw new CustomException("请通过广告位投流设置修改");
        }

        // 优先判断是否是通用配置
        BizConfigEnum bizConfigEnum = BizConfigEnum.getByKey(req.getKey());
        if (null != bizConfigEnum) {
            SysConfig config = sysConfigService.selectByKey(req.getKey());
            if (null == config) {
                config = initConfig(req.getKey(), bizConfigEnum.getDesc());
            }
            config.setConfigValue(req.getElements());
            int result = sysConfigService.updateConfig(config);
            if (result > 0) {
                configModifyNotice(bizConfigEnum.getDesc(), req.getElements());
                return AjaxResult.success();
            }
            return AjaxResult.error();
        }

        // 映射配置更新
        MapConfigEnum mapConfigEnum = MapConfigEnum.getByKey(req.getKey());
        if (null != mapConfigEnum) {
            SysConfig config = sysConfigService.selectByKey(req.getKey());
            if (null == config) {
                config = initConfig(req.getKey(), mapConfigEnum.getDesc());
            }
            if (req.getElements().contains("，")) {
                req.setElements(req.getElements().replaceAll("，", ","));
            }
            List<String> elements = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(req.getElements());
            if (CollectionUtils.isNotEmpty(elements) && !elements.stream().allMatch(ele -> {
                List<String> eles = StrUtil.split(ele, "-", 2, true, false);
                return mapConfigEnum.getKeyPredicate().test(eles.get(0)) && mapConfigEnum.getValuePredicate().test(eles.get(1));
            })) {
                return AjaxResult.error("更新失败，请检查配置信息是否包含无效字符");
            }
            config.setConfigValue(JSON.toJSONString(elements));
            int result = sysConfigService.updateConfig(config);
            if (result > 0) {
                configModifyNotice(mapConfigEnum.getDesc(), req.getElements());
                // 特殊业务逻辑
                if (Objects.equals(mapConfigEnum, MapConfigEnum.ADVERTISER_CONV_LIMIT_MAP)) {
                    String dateStr = DateUtil.today();
                    elements.forEach(ele -> {
                        List<String> eles = StrUtil.split(ele, "-", 2, true, false);
                        redisCache.deleteObject(CrmRedisKeyFactory.K025.join(dateStr, eles.get(0)));
                    });
                }
                return AjaxResult.success();
            }
            return AjaxResult.error();
        }

        // 判断是否是业务配置
        BizSwitchEnum bizSwitchEnum = BizSwitchEnum.getByKey(req.getKey());
        if (null != bizSwitchEnum) {
            if (StrUtil.contains(req.getElements(), "开")) {
                redisCache.deleteObject(EngineRedisKeyFactory.K015.join(bizSwitchEnum.getKey()));
            } else if (StrUtil.contains(req.getElements(), "关")) {
                redisCache.setCacheObject(EngineRedisKeyFactory.K015.join(bizSwitchEnum.getKey()), String.valueOf(System.currentTimeMillis()), 365, TimeUnit.DAYS);
            }
            return AjaxResult.success();
        }

        // 白名单配置更新
        WhitelistType type = WhitelistType.getByKey(req.getKey());
        if (null == type) {
            return AjaxResult.error("无效的白名单类型");
        }

        // 离线广告主白名单特殊处理
        if (Objects.equals(type, OFFLINE_DATA_ADVERTISER)) {
            advertiserManager.updateOfflineList(Splitter.on(",").omitEmptyStrings().trimResults()
                    .splitToStream(StringUtils.defaultString(req.getElements())).map(Long::valueOf).collect(Collectors.toList()));
            return AjaxResult.success();
        }

        boolean result;
        if (StringUtils.isBlank(req.getElements())) {
            result = whitelistService.clear(type);
        } else {
            if (req.getElements().contains("，")) {
                req.setElements(req.getElements().replaceAll("，", ","));
            }
            List<String> elements = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(req.getElements());
            if (null != type.getPredicate() && !elements.stream().allMatch(type.getPredicate())) {
                return AjaxResult.error("更新失败，请检查配置信息是否包含无效字符");
            }
            result = whitelistService.update(type, elements);
        }
        if (result) {
            configModifyNotice(type.getDesc(), req.getElements());
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    /**
     * 配置变动钉钉提醒
     */
    private void configModifyNotice(String desc, String value) {
        DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), StrUtil.format(CONFIG_MODIFY_NOTICE_TEMPLATE, desc, value));
    }

    /**
     * 初始化配置
     */
    private SysConfig initConfig(String key, String desc) {
        SysConfig config = new SysConfig();
        config.setConfigKey(key);
        config.setConfigValue("");
        config.setRemark(desc);
        sysConfigService.insertConfig(config);
        return sysConfigService.selectByKey(key);
    }
}
