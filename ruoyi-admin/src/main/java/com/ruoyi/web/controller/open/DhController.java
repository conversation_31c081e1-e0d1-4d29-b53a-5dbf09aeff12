package com.ruoyi.web.controller.open;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.req.open.DhClickReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * 灯火接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/05/23
 */
@Slf4j
@RestController
@RequestMapping("/open/dh")
public class DhController {

    @Autowired
    private RedisCache redisCache;

    /**
     * 点击回调接口
     */
    @CrossOrigin
    @GetMapping("/click")
    public void click(DhClickReq req) {
        log.info("灯火点击回调，req={}", JSON.toJSONString(req));
        if (StringUtils.isNotBlank(req.getCallback_param())) {
            if (StringUtils.isNotBlank(req.getRequest())) {
                redisCache.setCacheObject(EngineRedisKeyFactory.K121.join(req.getRequest()), req.getCallback_param(), 1, TimeUnit.DAYS);
            }
            if (StringUtils.isNotBlank(req.getIp())) {
                redisCache.setCacheObject(EngineRedisKeyFactory.K121.join(req.getIp()), req.getCallback_param(), 1, TimeUnit.DAYS);
            }
        }
    }
}
