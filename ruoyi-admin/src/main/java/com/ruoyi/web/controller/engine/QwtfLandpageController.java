package com.ruoyi.web.controller.engine;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UserAgentUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.QwtfLandpageFormRecordEntity;
import com.ruoyi.system.req.engine.QwtfLandpageAuthorizePhoneReq;
import com.ruoyi.system.req.engine.QwtfLandpageInitReq;
import com.ruoyi.system.req.engine.QwtfLandpageJumpReq;
import com.ruoyi.system.req.engine.QwtfLandpageQrCodeReq;
import com.ruoyi.system.req.engine.QwtfLandpageTakeReq;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.landpage.QwtfLandpageFormRecordService;
import com.ruoyi.system.service.landpage.QwtfWxService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.vo.engine.QwtfLandpageInitVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.InnerLogType.AUTHORIZATION_POPUP_CLICK;
import static com.ruoyi.common.enums.InnerLogType.BLIND_BOX_POPUP_CLICK;
import static com.ruoyi.common.enums.InnerLogType.QRCODE_CLICK;
import static com.ruoyi.common.enums.InnerLogType.QRCODE_EXPOSURE;

/**
 * 企微囤粉落地页(不鉴权)
 *
 * <AUTHOR>
 * @date 2023/09/22
 */
@Slf4j
@RestController
@RequestMapping("/lp/qwtf")
public class QwtfLandpageController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private QwtfLandpageFormRecordService qwtfLandpageFormRecordService;

    @Autowired
    private StatService statService;

    @Autowired
    private QwtfWxService qwtfWxService;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    /**
     * 跳转小程序
     */
    @CrossOrigin
    @GetMapping("/jump")
    public void jump(QwtfLandpageJumpReq req, HttpServletResponse response) throws IOException {
        String query = StrUtil.format("lpKey={}&nadkey={}", req.getLpKey(), req.getNadkey());
        String scheme = qwtfWxService.getUrlScheme("/pages/liuzi/liuzi", query, 30);
        response.sendRedirect(scheme);
    }

    /**
     * 页面初始化
     */
    @CrossOrigin
    @GetMapping("/init")
    public Result<QwtfLandpageInitVO> init(QwtfLandpageInitReq req) {
        log.info("企微囤粉落地页表单初始化, req={}", JSON.toJSONString(req));

        try {
            // 落地页曝光埋点
            statService.landpageExposure(req.getOrderId());
        } catch (Exception e) {
            log.warn("企微囤粉落地页曝光埋点异常, orderId:{}", req.getOrderId(), e);
        }

        // 查询UnionId并缓存
        String unionId = qwtfWxService.getUnionId(req.getWxCode());
        if (StringUtils.isNotBlank(unionId) && StringUtils.isNotBlank(req.getOrderId())) {
            redisCache.setCacheObject(EngineRedisKeyFactory.K097.join(unionId), req.getOrderId(), 7, TimeUnit.DAYS);
        }

        // 查询落地页配置
        String pageConfig = landpageCacheService.selectLandpagePageConfigCache(req.getLpKey());

        QwtfLandpageInitVO page = new QwtfLandpageInitVO();
        page.setUnionId(unionId);
        page.setPageConfig(pageConfig);
        return ResultBuilder.success(page);
    }

    /**
     * 领取
     */
    @CrossOrigin
    @PostMapping("/take")
    public Result<Void> take(@RequestBody QwtfLandpageTakeReq req, HttpServletRequest request) {
        log.info("企微囤粉落地页表单领取, req={}", JSON.toJSONString(req));

        if (StringUtils.isBlank(req.getOrderId()) && StringUtils.isBlank(req.getUnionId())) {
            return ResultBuilder.success();
        }

        // 防重复点击
        if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K098.join(req.getOrderId()), 3)) {
            return ResultBuilder.success();
        }

        // 领取埋点
        statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());

        // 表单记录
        QwtfLandpageFormRecordEntity record = new QwtfLandpageFormRecordEntity();
        record.setOrderId(req.getOrderId());
        record.setUnionId(req.getUnionId());
        record.setPhone(redisCache.getCacheObject(EngineRedisKeyFactory.K101.join(req.getUnionId())));
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null != order) {
            record.setAdvertId(order.getAdvertId());
            record.setAppId(order.getAppId());
            record.setSlotId(order.getSlotId());
            record.setConsumerId(order.getConsumerId());
            record.setIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
            AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
            if (null != adSnapshot) {
                record.setLandpageUrl(adSnapshot.getLandpageUrl());
            }
        }
        qwtfLandpageFormRecordService.insert(record);

        // 短链归因缓存
        attribute(req.getOrderId(), request);

        return ResultBuilder.success();
    }

    /**
     * 手机号授权
     */
    @CrossOrigin
    @PostMapping("/authorizePhone")
    public Result<Void> authorizePhone(@RequestBody QwtfLandpageAuthorizePhoneReq req) {
        log.info("企微囤粉落地页手机号授权, req={}", JSON.toJSONString(req));

        // 防重复点击
        if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K098.join(req.getOrderId(), req.getWxCode()), 3)) {
            return ResultBuilder.success();
        }

        // 授权手机号埋点
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null != order) {
            statService.innerLogByOrder(AUTHORIZATION_POPUP_CLICK, order, StringUtils.isNotBlank(req.getWxCode()) ? 1 : 2);
            if (StringUtils.isNotBlank(req.getWxCode())) {
                statService.convertEvent(order, ConvType.REGISTER.getType(), null);
            }
        }

        // unionId没有的情况就不换取手机号了，反正存不下来
        if (StringUtils.isBlank(req.getUnionId())) {
            return ResultBuilder.success();
        }

        // 用code换取手机号
        String phone = qwtfWxService.getUserPhoneNumber(req.getWxCode());
        if (StringUtils.isNotBlank(phone)) {
            // 缓存手机号，兼容前端先触发授权手机号的情况
            redisCache.setCacheObject(EngineRedisKeyFactory.K101.join(req.getUnionId()), phone, 5, TimeUnit.MINUTES);
            QwtfLandpageFormRecordEntity record = qwtfLandpageFormRecordService.selectByUnionId(req.getUnionId());
            if (null != record && StringUtils.isBlank(record.getPhone())) {
                QwtfLandpageFormRecordEntity updateRecord = new QwtfLandpageFormRecordEntity();
                updateRecord.setId(record.getId());
                updateRecord.setPhone(phone);
                qwtfLandpageFormRecordService.updateById(updateRecord);
            }
        }

        return ResultBuilder.success();
    }

    /**
     * 二维码曝光
     */
    @CrossOrigin
    @GetMapping("/qrCodeExposure")
    public Result<Void> qrCodeExposure(QwtfLandpageQrCodeReq req) {
        log.info("企微囤粉落地页二维码曝光, req={}", JSON.toJSONString(req));

        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null != order) {
            statService.innerLogByOrder(QRCODE_EXPOSURE, order, null);
        }
        return ResultBuilder.success();
    }

    /**
     * 二维码长按
     */
    @CrossOrigin
    @GetMapping("/qrCodeClick")
    public Result<Void> qrCodeClick(QwtfLandpageQrCodeReq req) {
        log.info("企微囤粉落地页二维码长按, req={}", JSON.toJSONString(req));

        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null != order) {
            statService.innerLogByOrder(QRCODE_CLICK, order, null);
        }
        return ResultBuilder.success();
    }

    /**
     * 短链归因缓存
     */
    private void attribute(String orderId, HttpServletRequest request) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }
        String ip = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        GlobalThreadPool.executorService.submit(() -> {
            UserAgentUtils.UserAgentDevice device = UserAgentUtils.analysisUserAgent(userAgent);
            if (null == device || StringUtils.isBlank(device.getModel())) {
                return;
            }
            String key = EngineRedisKeyFactory.K104.join(Md5Utils.hash(ip + device.getModel() + device.getOsVersion()));
            redisCache.setCacheObject(key, orderId, 3, TimeUnit.DAYS);
        });
    }
}
