package com.ruoyi.web.controller.manager.crm.ssp.privatesphere;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.privatesphere.PrivateSphereKefuDataListBO;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelEntity;
import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelDataEntity;
import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelEntity;
import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuDataEntity;
import com.ruoyi.system.entity.privatesphere.PrivateSphereProductEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.req.privatesphere.channel.PrivateSphereKefuChannelEditReq;
import com.ruoyi.system.req.privatesphere.data.PrivateSphereKefuChannelDataEditReq;
import com.ruoyi.system.req.privatesphere.data.PrivateSphereKefuDataListReq;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.privatesphere.PrivateSphereChannelService;
import com.ruoyi.system.service.privatesphere.PrivateSphereKefuChannelDataService;
import com.ruoyi.system.service.privatesphere.PrivateSphereKefuChannelService;
import com.ruoyi.system.service.privatesphere.PrivateSphereKefuDataService;
import com.ruoyi.system.service.privatesphere.PrivateSphereProductService;
import com.ruoyi.system.vo.privatesphere.PrivateAccountVO;
import com.ruoyi.system.vo.privatesphere.PrivateKefuAccountRelationVO;
import com.ruoyi.system.vo.privatesphere.PrivateKefuChannelRelationVO;
import com.ruoyi.system.vo.privatesphere.PrivateKefuProductRelationVO;
import com.ruoyi.system.vo.privatesphere.PrivateKefuSspAccountRelationVO;
import com.ruoyi.system.vo.privatesphere.PrivateSphereChannelDataListVO;
import com.ruoyi.system.vo.privatesphere.PrivateSphereKefuChannelListVO;
import com.ruoyi.system.vo.privatesphere.PrivateSphereKefuDataListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.common.usermodel.fonts.FontCharset;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * 私域客服渠道数据业务
 *
 * <AUTHOR>
 * @date 2023/2/7 11:39
 */
@PreAuthorize("@ss.hasCrmPermi()")
@RestController
@RequestMapping("/manager/crmPrivate")
public class CrmSspPrivateSphereController extends BaseController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private PrivateSphereProductService privateSphereProductService;

    @Autowired
    private PrivateSphereKefuChannelService privateSphereKefuChannelService;

    @Autowired
    private PrivateSphereKefuChannelDataService privateSphereKefuChannelDataService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private PrivateSphereChannelService privateSphereChannelService;

    @Autowired
    private PrivateSphereKefuDataService privateSphereKefuDataService;

    /**
     * 获取所有私域账号
     */
    @GetMapping("accounts")
    public Result<List<PrivateAccountVO>> accounts() {
        List<Account> accounts = accountService.selectSspPrivateAccountList();
        return ResultBuilder.success(BeanUtil.copyToList(accounts, PrivateAccountVO.class));
    }

    /**
     * 获取产品客服渠道关联列表
     */
    @GetMapping("kefuCompanyRelationList")
    public Result<List<PrivateKefuAccountRelationVO>> kefuCompanyRelationList(){
        //ssp私域账号
        List<Account> sspAccounts = accountService.selectSspPrivateAccountList();
        Map<Long, String> sspAccountMap = sspAccounts.stream().collect(Collectors.toMap(Account::getId, Account::getCompanyName));
        //dsp私域账号
        List<Account> accounts = accountService.selectPrivateAccountList();
        //客服渠道
        List<PrivateSphereKefuChannelEntity> channelEntities = privateSphereKefuChannelService.selectAllList();
        Map<Long, Map<Long, List<PrivateSphereKefuChannelEntity>>> channelMap = channelEntities.stream().collect(Collectors.groupingBy(PrivateSphereKefuChannelEntity::getProductId, Collectors.groupingBy(PrivateSphereKefuChannelEntity::getSspAccountId)));
        //dsp产品
        List<Long> accountIds = accounts.stream().map(Account::getId).collect(Collectors.toList());
        List<PrivateSphereProductEntity> productEntities = privateSphereProductService.selectListByAccountIds(accountIds);
        Map<Long, List<PrivateSphereProductEntity>> productMap = productEntities.stream().collect(Collectors.groupingBy(PrivateSphereProductEntity::getAccountId));

        List<PrivateKefuAccountRelationVO> voList = accounts.stream().map(account -> {
            PrivateKefuAccountRelationVO vo = BeanUtil.copyProperties(account, PrivateKefuAccountRelationVO.class);
            List<PrivateSphereProductEntity> productEntityList = productMap.get(account.getId());
            if (CollectionUtils.isNotEmpty(productEntityList)) {
                List<PrivateKefuProductRelationVO> productList = productEntityList.stream().map(product -> {
                    PrivateKefuProductRelationVO productEntity = new PrivateKefuProductRelationVO();
                    productEntity.setProductName(product.getProductName());
                    productEntity.setProductId(product.getId());

                    Map<Long, List<PrivateSphereKefuChannelEntity>> kefuChannelMap = channelMap.get(product.getId());
                    if(MapUtils.isEmpty(kefuChannelMap)){
                        return productEntity;
                    }
                    List<PrivateKefuSspAccountRelationVO> sspAccountList = new ArrayList<>();
                    kefuChannelMap.forEach((sspAccountId,kefuChannelList) ->{
                        PrivateKefuSspAccountRelationVO sspAccountRelationVO = new PrivateKefuSspAccountRelationVO();
                        sspAccountRelationVO.setSspAccountId(sspAccountId);
                        sspAccountRelationVO.setSspCompanyName(sspAccountMap.get(sspAccountId));
                        List<PrivateKefuChannelRelationVO> channelList = kefuChannelList.stream().map(channel -> {
                            PrivateKefuChannelRelationVO channelRelationVO = new PrivateKefuChannelRelationVO();
                            channelRelationVO.setKefuChannelId(channel.getId());
                            channelRelationVO.setChannel(channel.getChannel());
                            return channelRelationVO;
                        }).collect(Collectors.toList());
                        sspAccountRelationVO.setChannelList(channelList);
                        sspAccountList.add(sspAccountRelationVO);
                    });
                    productEntity.setSspAccountList(sspAccountList);
                    return productEntity;
                }).collect(Collectors.toList());
                vo.setProductList(productList);
            }
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(voList);
    }

    /**
     * 新增编辑客服渠道
     */
    @Log(title = "私域新增编辑客服渠道", businessType = BusinessType.INSERT)
    @PostMapping("editKefuChannel")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> editChannel(@RequestBody @Validated PrivateSphereKefuChannelEditReq req) {
        Long userId = SecurityUtils.getLoginUser().getCrmAccountId();
        req.setChannel(req.getChannel().replaceAll(" ", ""));//去除所有空格
        //判断是否有同名渠道
        PrivateSphereKefuChannelEntity channelEntity = privateSphereKefuChannelService.selectByChannel(req.getChannel());
        if (Objects.nonNull(channelEntity) && !Objects.equals(req.getId(), channelEntity.getId())) {
            throw new CustomException(ErrorCode.E140002);
        }
        PrivateSphereKefuChannelEntity entity = BeanUtil.copyProperties(req,PrivateSphereKefuChannelEntity.class);
        entity.setOperAccountId(userId);
        if (NumberUtils.isNullOrLteZero(req.getId())) {
            return ResultBuilder.success(privateSphereKefuChannelService.insert(entity));

        }
        return ResultBuilder.success(privateSphereKefuChannelService.updateById(entity));
    }

    /**
     * 获取所有客服渠道列表
     */
    @GetMapping("getKefuChannelList")
    public Result<List<PrivateSphereKefuChannelListVO>> getKefuChannelList(){
        List<PrivateSphereKefuChannelEntity> entities = privateSphereKefuChannelService.selectAllList();
        List<Long> companyIds = new ArrayList<>();
        List<Long> productIds = new ArrayList<>();
        entities.forEach(entity ->{
            companyIds.add(entity.getAccountId());
            companyIds.add(entity.getSspAccountId());
            productIds.add(entity.getProductId());
        });
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(companyIds);
        Map<Long, String> productNameMap = privateSphereProductService.selectProductNameMapByIds(productIds);
        List<PrivateSphereKefuChannelListVO> listVOS = entities.stream().map(entity -> {
            PrivateSphereKefuChannelListVO vo = new PrivateSphereKefuChannelListVO();
            vo.setId(entity.getId());
            vo.setChannel(entity.getChannel());
            vo.setKefuCompanyName(companyNameMap.get(entity.getSspAccountId()));
            vo.setProductName(productNameMap.get(entity.getProductId()));
            vo.setCompanyName(companyNameMap.get(entity.getAccountId()));
            vo.setGmtCreate(entity.getGmtCreate());
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(listVOS);
    }

    /**
     * 删除客服渠道
     */
    @PostMapping("deleteKefuChannel")
    public Result<Boolean> deleteKefuChannel(@RequestBody @Validated IdReq req){
        int count = privateSphereKefuDataService.countByKefuChannelId(req.getId());
        if(count > 0){
            throw new CustomException("该渠道已有数据录入，不能删除");
        }

        return ResultBuilder.success(privateSphereKefuChannelService.deleteById(req.getId()));
    }

    /**
     * 新增编辑客服渠道数据
     */
    @Log(title = "私域新增编辑客服渠道数据", businessType = BusinessType.INSERT)
    @PostMapping("editKefuData")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> editKefuData(@RequestBody @Validated PrivateSphereKefuChannelDataEditReq req) {
        //查询数据库key是否唯一
        PrivateSphereKefuDataEntity dataEntity = privateSphereKefuDataService.selectByProductAndKefuChannelId(req.getCurDate(), req.getProductId(), req.getKefuChannelId());
        if (Objects.nonNull(dataEntity) && !Objects.equals(dataEntity.getId(), req.getId())) {
            throw new CustomException(ErrorCode.E140003);
        }

        PrivateSphereKefuChannelEntity channelEntity = privateSphereKefuChannelService.selectById(req.getKefuChannelId());

        PrivateSphereKefuDataEntity editEntity = BeanUtil.copyProperties(req, PrivateSphereKefuDataEntity.class);
        editEntity.setSspAccountId(channelEntity.getSspAccountId());
        if (NumberUtils.isNullOrLteZero(editEntity.getId())) {
            privateSphereKefuDataService.insert(editEntity);
        } else {
            privateSphereKefuDataService.updateById(editEntity);
        }

        List<PrivateSphereKefuChannelDataEntity> channelDataEntities = req.getChannelDataList().stream().map(channelData -> {
            PrivateSphereKefuChannelDataEntity entity = BeanUtil.copyProperties(channelData, PrivateSphereKefuChannelDataEntity.class);
            entity.setKefuDataId(editEntity.getId());
            return entity;
        }).collect(Collectors.toList());
        privateSphereKefuChannelDataService.batchInsertOrUpdate(channelDataEntities);

        return ResultBuilder.success(true);
    }

    /**
     * 私域数据列表查询
     */
    @GetMapping("dataList")
    public TableDataInfo<PrivateSphereKefuDataListVO> dataList(PrivateSphereKefuDataListReq req) {
        PageInfo<PrivateSphereKefuDataListVO> dataListVOS = privateDataList(req, false);

        return getDataTable(dataListVOS);
    }

    /**
     * 导出私域数据
     */
    @GetMapping("exportDataList")
    public AjaxResult exportDataList(PrivateSphereKefuDataListReq req) {
        PageInfo<PrivateSphereKefuDataListVO> dataListVOS = privateDataList(req, true);
        //获取所有渠道
        List<PrivateSphereChannelEntity> channelEntities = privateSphereChannelService.selectListByParam(null);

        String fileName = UUID.randomUUID().toString() + "_私域客服渠道数据.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;

        List<List<Object>> list = new ArrayList<List<Object>>();
        dataListVOS.getList().forEach(data -> {
            list.add(getData(data, channelEntities));
        });

        WriteCellStyle headWriteCellStyle = getWriteCellStyle();

        EasyExcel.write(filePath).head(getHeader(channelEntities)).registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle,new WriteCellStyle())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet().doWrite(list);
        return AjaxResult.success(fileName);
    }

    private WriteCellStyle getWriteCellStyle() {
        // 表头样式策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 是否换行
        headWriteCellStyle.setWrapped(false);
        // 水平对齐方式
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 垂直对齐方式
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 字体策略
        WriteFont writeFont = new WriteFont();
        // 是否加粗/黑体
        writeFont.setBold(false);
        // 字体颜色
        writeFont.setColor(Font.COLOR_NORMAL);
        // 字体名称
        writeFont.setFontName("宋体");
        // 字体大小
        writeFont.setFontHeightInPoints((short) 11);

        // 设置要使用的字符集
        writeFont.setCharset(FontCharset.DEFAULT.getNativeId());
        headWriteCellStyle.setWriteFont(writeFont);
        return headWriteCellStyle;
    }

    /**
     * 私域数据总计
     */
    @GetMapping("statisticsData")
    public Result<PrivateSphereKefuDataListVO> statisticsData(PrivateSphereKefuDataListReq req) {
        PrivateSphereKefuDataListBO bo = getPrivateSphereDataListBO(req);
        if (Objects.isNull(bo)) {
            return ResultBuilder.success(null);
        }
        PrivateSphereKefuDataEntity dataEntity = privateSphereKefuDataService.statisticsByParam(bo);
        if (Objects.isNull(dataEntity)) {
            return ResultBuilder.success(null);
        }

        List<Long> dataIds = privateSphereKefuDataService.selectIdsByParam(bo);
        List<PrivateSphereKefuChannelDataEntity> channelDataEntities = privateSphereKefuChannelDataService.statisticsByDataIds(dataIds);

        return ResultBuilder.success(buildPrivateSphereKefuDataListVO(dataEntity, channelDataEntities, null, null,null,true));

    }

    /**
     * 查询私域数据
     */
    private PageInfo<PrivateSphereKefuDataListVO> privateDataList(PrivateSphereKefuDataListReq req, Boolean isExport) {
        PrivateSphereKefuDataListBO bo = getPrivateSphereDataListBO(req);
        if (Objects.isNull(bo)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }
        if (BooleanUtils.isNotTrue(isExport)) {
            TableSupport.startPage();
        }

        List<PrivateSphereKefuDataEntity> dataEntities = privateSphereKefuDataService.selectListByParam(bo);
        //查询渠道数据
        List<Long> dataIds = dataEntities.stream().map(PrivateSphereKefuDataEntity::getId).collect(Collectors.toList());
        List<PrivateSphereKefuChannelDataEntity> channelDataEntities = privateSphereKefuChannelDataService.selectListByDataIds(dataIds);
        Map<Long, List<PrivateSphereKefuChannelDataEntity>> channelDataMap = channelDataEntities.stream().collect(Collectors.groupingBy(PrivateSphereKefuChannelDataEntity::getKefuDataId));
        //查询公司名称
        List<Long> accountIds = dataEntities.stream().map(PrivateSphereKefuDataEntity::getAccountId).collect(Collectors.toList());
        //查询客服公司名称
        List<Long> sspAccountIds = dataEntities.stream().map(PrivateSphereKefuDataEntity::getSspAccountId).collect(Collectors.toList());
        accountIds.addAll(sspAccountIds);
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(accountIds);
        //查询产品名称
        List<Long> productIds = dataEntities.stream().map(PrivateSphereKefuDataEntity::getProductId).collect(Collectors.toList());
        Map<Long, String> productNameMap = privateSphereProductService.selectProductNameMapByIds(productIds);

        //查询客服渠道名称
        List<Long> kefuChannelIds = dataEntities.stream().map(PrivateSphereKefuDataEntity::getKefuChannelId).collect(Collectors.toList());
        Map<Long, String> kefuChannelNameMap = privateSphereKefuChannelService.selectChannelNameMapByIds(kefuChannelIds);
        return PageInfoUtils.dto2Vo(dataEntities, data -> {
            List<PrivateSphereKefuChannelDataEntity> dataEntityList = channelDataMap.get(data.getId());

            return buildPrivateSphereKefuDataListVO(data, dataEntityList, companyNameMap, productNameMap,kefuChannelNameMap,false);
        });
    }

    /**
     * 构建返回vo
     */
    private PrivateSphereKefuDataListVO buildPrivateSphereKefuDataListVO(PrivateSphereKefuDataEntity data, List<PrivateSphereKefuChannelDataEntity> dataEntityList, Map<Long, String> companyNameMap, Map<Long, String> productNameMap,Map<Long, String> channelNameMap, Boolean isStatistics) {
        PrivateSphereKefuDataListVO vo = BeanUtil.copyProperties(data, PrivateSphereKefuDataListVO.class);
        vo.setConnectRate(NumberUtils.calculatePercent(data.getConnectCount(), data.getCallCount()));
        vo.setIntentRate(NumberUtils.calculatePercent(data.getPersonCount(), data.getConnectCount()));
        vo.setEntryPercent(NumberUtils.calculatePercent(data.getEntryCount(), data.getPersonCount()));
        vo.setEntryRatio(NumberUtils.calculatePercent(data.getEntryCount(), data.getConnectCount()));
        vo.setFeedbackEntryRatio(NumberUtils.calculatePercent(data.getFeedbackEntryCount(),data.getEntryCount()));
        if(BooleanUtils.isTrue(isStatistics)){
            vo.setSumCost(data.getDataCost().intValue() + data.getLineCost() + data.getPersonCost());
        }else{
            vo.setSumCost((int)(data.getDataCost() * data.getDataCostCount()) + data.getLineCost() + data.getPersonCost() * data.getEntryCount());
        }

        vo.setSingleEntryCost(NumberUtils.calculateRate(vo.getSumCost(), data.getEntryCount(),"0"));
        if (MapUtils.isNotEmpty(companyNameMap)) {
            vo.setCompanyName(companyNameMap.get(vo.getAccountId()));
            vo.setSspCompanyName(companyNameMap.get(vo.getSspAccountId()));
        }
        if (MapUtils.isNotEmpty(productNameMap)) {
            vo.setProductName(productNameMap.get(vo.getProductId()));
        }
        if(MapUtils.isNotEmpty(channelNameMap)){
            vo.setChannel(channelNameMap.get(vo.getKefuChannelId()));
        }

        if (CollectionUtils.isEmpty(dataEntityList)) {
            return vo;
        }
        AtomicInteger sumAmount = new AtomicInteger(0);
        AtomicInteger refundAmount = new AtomicInteger(0);
        List<PrivateSphereChannelDataListVO> dataListVOS = dataEntityList.stream().map(channelData -> {
            PrivateSphereChannelDataListVO channelDataVo = BeanUtil.copyProperties(channelData, PrivateSphereChannelDataListVO.class);
            channelDataVo.setRValue(NumberUtils.calculateRate((channelDataVo.getConverAmount() - channelDataVo.getRefundAmount()), channelDataVo.getEntryGroupCount(),"0"));
            sumAmount.addAndGet(channelData.getConverAmount());
            refundAmount.addAndGet(channelData.getRefundAmount());
            return channelDataVo;
        }).collect(Collectors.toList());
        vo.setChannelDataList(dataListVOS);
        vo.setRValue(NumberUtils.calculateRate((sumAmount.get() - refundAmount.get()), data.getEntryCount(),"0"));
        vo.setSumConverAmount(sumAmount.get());
        vo.setSumRefundAmount(refundAmount.get());
        return vo;
    }

    private PrivateSphereKefuDataListBO getPrivateSphereDataListBO(PrivateSphereKefuDataListReq req) {
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectPrivateAccount();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return null;
            }
        }

        List<Long> productIds = null;
        if(StringUtils.isNotBlank(req.getProductName())){
            productIds = privateSphereProductService.selectProductIdsByName(req.getProductName());
            if (CollectionUtils.isEmpty(productIds)) {
                return null;
            }
        }
        List<Long> kefuChannelIds = null;
        if(StringUtils.isNotBlank(req.getChannel())){
            kefuChannelIds = privateSphereKefuChannelService.selectIdListByChannel(req.getChannel());
            if (CollectionUtils.isEmpty(kefuChannelIds)) {
                return null;
            }
        }

        PrivateSphereKefuDataListBO bo = new PrivateSphereKefuDataListBO();
        bo.setKefuChannelIds(kefuChannelIds);
        bo.setProductIds(productIds);
        if (Objects.nonNull(req.getStartDate()) && Objects.nonNull(req.getEndDate())) {
            bo.setStartDate(DateUtil.beginOfDay(req.getStartDate()));
            bo.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        return bo;

    }

    private List<List<String>> getHeader(List<PrivateSphereChannelEntity> privateSphereChannelEntities) {
        List<List<String>> list = new ArrayList<List<String>>();
        list.add(Lists.newArrayList("日期"));
        list.add(Lists.newArrayList("公司名称"));
        list.add(Lists.newArrayList("产品名称"));
        list.add(Lists.newArrayList("客服公司名称"));
        list.add(Lists.newArrayList("客服渠道简称"));
        list.add(Lists.newArrayList("外呼总数"));
        list.add(Lists.newArrayList("接通数"));
        list.add(Lists.newArrayList("接通率"));
        list.add(Lists.newArrayList("进入人工数"));
        list.add(Lists.newArrayList("意向率"));
        list.add(Lists.newArrayList("入群数"));
        list.add(Lists.newArrayList("反馈入群数"));
        list.add(Lists.newArrayList("反馈比"));
        list.add(Lists.newArrayList("成单率"));
        list.add(Lists.newArrayList("成单比"));
        list.add(Lists.newArrayList("数据成本"));
        list.add(Lists.newArrayList("线路成本"));
        list.add(Lists.newArrayList("人工成本"));
        list.add(Lists.newArrayList("总成本"));
        list.add(Lists.newArrayList("单入群成本"));
        list.add(Lists.newArrayList("总转化金额"));
        list.add(Lists.newArrayList("总退款金额"));
        list.add(Lists.newArrayList("R值"));
        privateSphereChannelEntities.forEach(entity -> {
            list.add(Lists.newArrayList(entity.getChannel() + "入群数"));
            list.add(Lists.newArrayList(entity.getChannel() + "转化金额"));
            list.add(Lists.newArrayList(entity.getChannel() + "退款数"));
            list.add(Lists.newArrayList(entity.getChannel() + "R值"));
        });
        return list;
    }

    private List<Object> getData(PrivateSphereKefuDataListVO data, List<PrivateSphereChannelEntity> channelEntities) {
        List<Object> datas = ListUtils.newArrayList();
        datas.add(DateUtil.formatDate(data.getCurDate()));
        datas.add(data.getCompanyName()); //公司名称
        datas.add(data.getProductName()); //产品名称
        datas.add(data.getSspCompanyName()); //客服公司名称
        datas.add(data.getChannel()); //客服渠道简称
        datas.add(data.getCallCount()); //外呼总数
        datas.add(data.getConnectCount()); //接通数
        datas.add(data.getConnectRate()); //接通率
        datas.add(data.getPersonCount()); //进入人工数
        datas.add(data.getIntentRate()); //意向率
        datas.add(data.getEntryCount()); //入群数
        datas.add(data.getFeedbackEntryCount()); //入群数
        datas.add(data.getFeedbackEntryRatio()); //反馈比
        datas.add(data.getEntryPercent()); // 成单率
        datas.add(data.getEntryRatio()); //成单比
        datas.add(NumberUtils.fenToYuanForDouble((int)(data.getDataCost() * data.getDataCostCount())));//数据成本 分转元
        datas.add(NumberUtils.fenToYuanForDouble(data.getLineCost()));//线路成本
        datas.add(NumberUtils.fenToYuanForDouble(data.getPersonCost() * data.getEntryCount()));//人工成本 分转元
        datas.add(NumberUtils.fenToYuanForDouble(data.getSumCost())); //总成本 分转元
        datas.add(NumberUtils.fenToYuanForDouble(Double.valueOf(data.getSingleEntryCost()).intValue()));//单入群成本
        datas.add(NumberUtils.fenToYuanForDouble(data.getSumConverAmount()));//总转化金额 分转元
        datas.add(NumberUtils.fenToYuanForDouble(data.getSumRefundAmount()));//总退款金额 分转元
        datas.add(NumberUtils.fenToYuanForDouble(Double.valueOf(data.getRValue()).intValue()));//R值 （总转化金额-总退款）/总入群
        List<PrivateSphereChannelDataListVO> channelDataList = data.getChannelDataList();
        Map<Long, PrivateSphereChannelDataListVO> channelDataMap = channelDataList.stream().collect(Collectors.toMap(PrivateSphereChannelDataListVO::getChannelId, Function.identity()));
        channelEntities.forEach(channelEntity -> {
            PrivateSphereChannelDataListVO dataListVO = channelDataMap.get(channelEntity.getId());
            if (Objects.isNull(dataListVO)) {
                datas.add(0);//渠道入群数
                datas.add(0);//渠道退款数
                datas.add(0);//渠道转化金额
                datas.add(0);//渠道R值
            } else {
                datas.add(dataListVO.getEntryGroupCount());//渠道入群数
                datas.add(NumberUtils.fenToYuanForDouble(dataListVO.getConverAmount()));//渠道转化金额
                datas.add(NumberUtils.fenToYuanForDouble(dataListVO.getRefundAmount()));//渠道退款数
                datas.add(NumberUtils.fenToYuanForDouble(Double.valueOf(dataListVO.getRValue()).intValue()));//渠道R值
            }
        });
        return datas;
    }
}
