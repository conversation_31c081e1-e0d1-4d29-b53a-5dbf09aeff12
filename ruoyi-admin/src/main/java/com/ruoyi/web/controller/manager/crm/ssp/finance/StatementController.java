package com.ruoyi.web.controller.manager.crm.ssp.finance;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.system.service.slotdata.SlotMonthDataService;
import com.ruoyi.system.vo.datashow.StatementInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 结算单详情
 *
 * <AUTHOR>
 * @date 2021/9/17 2:23 下午
 */
@RestController
@RequestMapping("manager/crm/statement")
public class StatementController extends BaseController {

    @Autowired
    private SlotMonthDataService slotMonthDataService;

    /**
     * 结算单详情
     *
     * @param appMonthDataId 媒体月账单Id
     * @return 详情
     */
    @GetMapping("info")
    public Result<StatementInfoVO> info(@Validated @NotNull(message = "媒体账单id不能为空") Long appMonthDataId){
        return ResultBuilder.success(slotMonthDataService.selectStatementInfoByAppMonthDataId(appMonthDataId));
    }
}
