package com.ruoyi.web.controller.tool;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertOcpcCacheDto;
import com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord;
import com.ruoyi.system.entity.system.SysConfig;
import com.ruoyi.system.job.AdvertStatusResetJob;
import com.ruoyi.system.manager.liuzi.AsyncAlipayLandpageFormDataManager;
import com.ruoyi.system.manager.sms.LiuziSmsManager;
import com.ruoyi.system.manager.sms.QLV2PhoneManager;
import com.ruoyi.system.mapper.system.SysConfigMapper;
import com.ruoyi.system.service.domain.impl.WxDomainServiceImpl;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.service.landpage.LiuziLandpageFormRecordService;
import com.ruoyi.system.service.miniapp.MiniAppService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.service.landpage.article.ArticleService;
import com.ruoyi.system.service.antiblock.AntiBlockBizUrlGenerator;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.mapper.landpage.article.ArticleMapper;
import com.ruoyi.system.bo.landpage.article.ArticleListBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.InnerLogType.ADVERT_BILLING;

/**
 * 用户测试方法
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/test/user")
public class TestController extends BaseController {

    @Autowired
    private AdvertStatusResetJob advertStatusResetJob;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private MiniAppService miniAppService;

    @Autowired
    private LiuziLandpageFormRecordService liuziLandpageFormRecordService;

    @Autowired
    private AsyncAlipayLandpageFormDataManager asyncAlipayLdpageFormDataManager;

    @Autowired
    private QLV2PhoneManager qlPhoneManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private LiuziSmsManager liuziSmsManager;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private SysConfigMapper configMapper;
    @Autowired
    private ArticleService articleService;
    @Autowired
    private AntiBlockBizUrlGenerator antiBlockBizUrlGenerator;
    @Autowired
    private ArticleMapper articleMapper;

    @CrossOrigin
    @RequestMapping("/health")
    public String health() {
        return "ok";
    }

    @GetMapping("/act")
    public String actIdBase62(String id) {
        return StringUtils.isNumeric(id) ? Base62.encode(id) : Base62.decodeStr(id);
    }

    @GetMapping("sendMsg")
    public String sendMsg(Integer type){
        if(type == 1){
            return liuziSmsManager.sendSmsChuanZhen("15728003283","【声音课堂】订单通知：您在支付宝成功报名声音课，请48小时内 https://t.ydns.cn/E5Ad 确定，谨防失效，拒收请回复R");
        }else if (type == 2) {
            return liuziSmsManager.sendSmsGanAn("15728003283", "【喜播声音】订单通知：您在支付宝已成功报名了声音课，请48小时内点击 https://t.ydns.cn/E5Ad 确定，谨防失效，拒收请回复R");
        }else{
            return liuziSmsManager.sendSmsFengXueYun("15728003283", "【喜播声音】订单通知：您在支付宝已成功报名了声音课课，请48小时内 https://t.ydns.cn/E5Ad 确定，谨防失效，拒收请回复R");
        }
    }

    @GetMapping("/resetAdvertStatusJob")
    public String resetAdvertStatusJob() {
        advertStatusResetJob.advertStatusReset();
        return "ok";
    }

    @GetMapping("addFriend")
    public String addFriend(String phone){
        qlPhoneManager.addExtUserByRule(Lists.newArrayList(phone));
        return "success";
    }

    @GetMapping("urlScheme")
    public String urlScheme(){
        return miniAppService.getUrlScheme("pages/direct/direct","lp="+ URLEncoder.encode("http://legotest.ydns.com.cn/land/34UJD203E?nadkey=123456"),30);
    }

    @GetMapping("/validAdverts")
    public List<JSONObject> validAdverts() {
        List<AdvertCacheDto> validAdverts = advertCacheService.queryTotalAdvertCache();
        if (CollectionUtils.isEmpty(validAdverts)) {
            return Collections.emptyList();
        }
        return validAdverts.stream().map(advert -> {
            JSONObject dto = new JSONObject();
            dto.put("id", advert.getAdvertId());
            dto.put("status", advert.getAdvertStatus());
            dto.put("switch", advert.getServingSwitch());
            dto.put("chargeType", advert.getChargeType());
            dto.put("unitPrice", advert.getUnitPrice());
            dto.put("milliUnitPrice", advert.getMilliUnitPrice());
            dto.put("areaTarget", advert.getAreaTargetSet());
            dto.put("bannedApp", advert.getBannedAppIds());
            dto.put("orientApp", advert.getOrientAppIds());
            dto.put("orientSlot", advert.getOrientSlotIds());
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     *
     * @param k
     * @param v
     * @return
     */
    @GetMapping("/kv")
    public AjaxResult map(String k, String v) {
        if (StringUtils.isEmpty(k)) {
            return AjaxResult.error("必填k");
        }
        if (StringUtils.isEmpty(v)) {
            return AjaxResult.success(redisCache.getCacheObject(k));
        }
        redisCache.setCacheObject(k ,v);
        return AjaxResult.success(redisCache.getCacheObject(k));
    }

    @GetMapping("/threadMonitor")
    public JSONArray threadMonitor() {
        Map<String, ExecutorService> map = new HashMap<>();
        map.put("executorService", GlobalThreadPool.executorService);
        map.put("statExecutorService", GlobalThreadPool.statExecutorService);
        map.put("insertExecutorService", GlobalThreadPool.insertExecutorService);
        map.put("longTimeExecutorService", GlobalThreadPool.longTimeExecutorService);
        map.put("wxMonitorExecutor", WxDomainServiceImpl.wxMonitorExecutor);
        map.put("logExecutorService", GlobalThreadPool.logExecutorService);
        map.put("scheduledExecutorService", SpringUtils.getBean("scheduledExecutorService"));
        JSONArray arr = new JSONArray();
        map.forEach((k, v) -> {
            ThreadPoolExecutor te = (ThreadPoolExecutor) v;
            JSONObject jo = new JSONObject();
            jo.put("name", k);
            jo.put("PoolSize", te.getPoolSize());   // 初始线程数
            jo.put("CorePoolSize", te.getCorePoolSize());   // 核心线程数
            jo.put("Active", te.getActiveCount());  // 正在执行的任务数量
            jo.put("Queue", te.getQueue().size());  // 队列里缓存的任务数量
            jo.put("LargestPoolSize", te.getLargestPoolSize());     // 池中存在的最大线程数
            arr.add(jo);
        });
        return arr;
    }

    @GetMapping("test")
    public String test(HttpServletRequest request){
        //获取访问域名
        String domain = request.getServerName();
        //获取http还是https
        String scheme = request.getScheme();
        //获取访问路径
        String path = request.getRequestURI();
        //获取访问参数
        String queryString = request.getQueryString();
        return scheme+"://"+domain+path+"?"+queryString;
    }
    @GetMapping("testSms")
    public String testSms(){
        // 落地页转化记录
        LiuziLandpageFormRecord record = new LiuziLandpageFormRecord();
        record.setPhone("15728003283");
        record.setName("ctc");
        record.setAdvertId(123L);
        record.setOrderId(System.currentTimeMillis()+"");
        record.setConsumerId(0L); //外部留资数据没有用户id
        record.setAppId(0L);
        record.setSlotId(0L);
        record.setIp("");
        record.setReferer("");
        record.setLandpageType(LiuziLandPageTypeEnum.ALIPAY.getType());
        liuziLandpageFormRecordService.insertLandpageFormRecord(record);
        //发送短信
//        asyncAlipayLandpageFormDataManager.sendSmsContent("15728003283", 123L,record.getId());
        return "success";
    }

    /**
     * 刷新发券消耗
     */
    @GetMapping("/refreshLaunchCache")
    public String refreshLaunchCache(Long orientId) {
        AdvertOcpcCacheDto advert = advertCacheService.queryAdvertOcpcCache(orientId);
        if (null == advert) {
            return "0.0";
        }
        String today = DateUtil.today();
        Long consume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K108.join(today, orientId, ADVERT_BILLING.getType())));
        if (consume > 0) {
            String redisKey = EngineRedisKeyFactory.K080.join(today, advert.getAdvertId(), orientId);
            Long tmpConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(redisKey));
            redisAtomicClient.incrBy(redisKey, consume - tmpConsume, 1, TimeUnit.DAYS);
        }
        return String.valueOf(consume);
    }

    @GetMapping("refreshCache")
    public String refreshCache(SysConfig config) {
        log.info("刷新缓存,config:{}",config.toString());
        sysConfigService.updateConfig(config);
        return "success";
    }

    @GetMapping("testRatio")
    public String testRatio(Integer ratio){
        SysConfig config = new SysConfig();
        config.setConfigId(172L);
        config.setConfigValue(ratio.toString());
        configMapper.updateConfig(config);
        redisCache.setCacheObject(Constants.SYS_CONFIG_KEY + "test.redirect.key",ratio.toString());
        return "success";
    }

    /**
     * 批量更新指定时间范围内、指定域名开头的文章jump_url
     *
     * @param startDate 开始日期，格式：2025-07-01
     * @param endDate 结束日期，格式：2025-07-01
     * @param domain 域名，匹配jump_url开头的域名
     * @return 更新结果
     * <AUTHOR>
     */
    @GetMapping("/updateArticleJumpUrl")
    public AjaxResult updateArticleJumpUrl(String startDate, String endDate, String domain) {
        // 参数校验
        if (StringUtils.isBlank(startDate)) {
            return AjaxResult.error("开始日期不能为空，格式：2025-07-01");
        }
        if (StringUtils.isBlank(endDate)) {
            return AjaxResult.error("结束日期不能为空，格式：2025-07-01");
        }
        if (StringUtils.isBlank(domain)) {
            return AjaxResult.error("域名不能为空");
        }


        Date startDateTime = DateUtils.parseDate(startDate);
        Date endDateTime = DateUtils.parseDate(endDate);

        // 结束日期设置为当天的23:59:59
        endDateTime = DateUtil.endOfDay(endDateTime);
        startDateTime = DateUtil.beginOfDay(startDateTime);

        if (startDateTime.after(endDateTime)) {
            return AjaxResult.error("开始日期不能大于结束日期");
        }

        log.info("开始批量更新文章jump_url, startDate={}, endDate={}, domain={}", startDate, endDate, domain);

        try {
            // 查询指定时间范围内的所有文章
            List<ArticleEntity> articles = articleMapper.selectListByDateRange(startDateTime, endDateTime);

            if (CollectionUtils.isEmpty(articles)) {
                return AjaxResult.success("未找到需要更新的文章");
            }

            // 过滤符合条件的文章（域名过滤）
            List<ArticleEntity> targetArticles = articles.stream()
                .filter(article -> {
                    // 域名过滤
                    String jumpUrl = article.getJumpUrl();
                    if (StringUtils.isBlank(jumpUrl)) {
                        return false;
                    }

                    // 检查jump_url是否以指定域名开头
                    return jumpUrl.startsWith("http://" + domain) || jumpUrl.startsWith("https://" + domain);
                })
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(targetArticles)) {
                return AjaxResult.success("未找到符合条件的文章，条件：时间范围[" + startDate + " ~ " + endDate + "]，域名[" + domain + "]");
            }

            log.info("找到符合条件的文章数量：{}", targetArticles.size());

            // 同步批量更新
            int successCount = 0;
            int failCount = 0;

            for (ArticleEntity article : targetArticles) {
                try {
                    // 重新生成jump_url
                    String newJumpUrl = antiBlockBizUrlGenerator.generateDirectEntryUrl(article.getUrl(), false);

                    // 更新文章
                    ArticleEntity updateEntity = new ArticleEntity();
                    updateEntity.setId(article.getId());
                    updateEntity.setJumpUrl(newJumpUrl);

                    boolean updateResult = articleService.updateById(updateEntity);
                    if (updateResult) {
                        successCount++;
                        log.debug("更新文章jump_url成功，文章ID：{}，原jump_url：{}，新jump_url：{}",
                            article.getId(), article.getJumpUrl(), newJumpUrl);
                    } else {
                        failCount++;
                        log.error("更新文章jump_url失败，文章ID：{}", article.getId());
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("更新文章jump_url异常，文章ID：{}，原URL：{}", article.getId(), article.getUrl(), e);
                }
            }

            log.info("批量更新文章jump_url完成，总数：{}，成功：{}，失败：{}",
                targetArticles.size(), successCount, failCount);

            return AjaxResult.success("批量更新完成，总数：" + targetArticles.size() + "，成功：" + successCount + "，失败：" + failCount);

        } catch (Exception e) {
            log.error("批量更新文章jump_url异常", e);
            return AjaxResult.error("批量更新失败：" + e.getMessage());
        }
    }
}
