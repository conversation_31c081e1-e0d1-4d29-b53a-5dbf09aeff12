package com.ruoyi.web.controller.manager.wis.agent.finance;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.req.agent.finance.WisAgentRechargeListReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserRechargeRecordService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.agent.AgentClientBalanceVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.core.page.TableSupport.PAGE_NUM;
import static com.ruoyi.common.core.page.TableSupport.PAGE_SIZE;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * [代理商平台]余额记录
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@RequestMapping("/wis/agent/fiance/balance")
@RestController
public class WisAgentBalanceController extends BaseController {

    @Autowired
    private AdvertiserRechargeRecordService advertiserRechargeRecordService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private AdvertiserConsumeRecordService advertiserConsumeRecordService;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Autowired
    private WhitelistService whitelistService;

    /**
     * 余额记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<AgentClientBalanceVO> list(WisAgentRechargeListReq req) {
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return getDataTable(Collections.emptyList());
        }
        // 查询代理商下所有广告主ID
        List<Long> advertiserIds = agentService.selectAdvertiserIdsByAgent(user.getCrmAccountId());
        if (CollectionUtils.isEmpty(advertiserIds) || (null != req.getAdvertiserId() && !advertiserIds.contains(req.getAdvertiserId()))) {
            return getDataTable(Collections.emptyList());
        }

        Account param = new Account();
        param.setIds(advertiserIds);
        param.setId(req.getAdvertiserId());
        List<Account> list = advertiserService.selectList(param);
        advertiserIds = ListUtils.mapToList(list, Account::getId);

        Map<Long, Integer> rechargeMap = advertiserRechargeRecordService.sumAccountMapBySourceAccountId(user.getCrmAccountId(), advertiserIds);
        Map<Long, Integer> balanceMap = advertiserBalanceService.selectAdvertiserBalanceMap(advertiserIds);
        Map<Long, Integer> consumeMap = advertiserConsumeRecordService.sumConsumeAmountGroupByAccountIds(advertiserIds);
        Map<Long, Integer> consumeOffsetMap = dspAdvertiserConsumeRecordService.advertiserConsumeOffset(advertiserIds);

        // 离线数据广告主剔除今日数据
        Date today = DateUtil.beginOfDay(new Date());
        advertiserIds.retainAll(whitelistService.list(OFFLINE_DATA_ADVERTISER, Long.class));
        Map<Long, Integer> todayConsumeMap = advertiserConsumeRecordService.sumConsumeAmountGroupByAccountIdsAndDate(advertiserIds, today);

        List<AgentClientBalanceVO> balanceList = list.stream().map(account -> {
            AgentClientBalanceVO balanceVO = new AgentClientBalanceVO();
            balanceVO.setId(account.getId());
            balanceVO.setAdvertiserName(account.getCompanyName());
            balanceVO.setRechargeAmount(rechargeMap.getOrDefault(account.getId(), 0));
            balanceVO.setConsumeAmount(consumeMap.getOrDefault(account.getId(), 0));
            balanceVO.setTotalBalance(balanceMap.getOrDefault(account.getId(), 0));
            Optional.ofNullable(consumeOffsetMap.get(account.getId())).ifPresent(offset -> {
                balanceVO.setTotalBalance(balanceVO.getTotalBalance() - offset);
                balanceVO.setConsumeAmount(balanceVO.getConsumeAmount() + offset);
            });
            Optional.ofNullable(todayConsumeMap.get(account.getId())).ifPresent(consume -> {
                balanceVO.setTotalBalance(balanceVO.getTotalBalance() + consume);
                balanceVO.setConsumeAmount(balanceVO.getConsumeAmount() - consume);
            });
            return balanceVO;
        }).sorted((o1, o2) -> {
            if (!Objects.equals(o1.getRechargeAmount(), o2.getRechargeAmount())) {
                return o2.getRechargeAmount().compareTo(o1.getRechargeAmount());
            }
            return o2.getConsumeAmount().compareTo(o1.getConsumeAmount());
        }).collect(Collectors.toList());

        int pageNum = ServletUtils.getParameterToInt(PAGE_NUM);
        int pageSize = ServletUtils.getParameterToInt(PAGE_SIZE);
        balanceList = ListUtil.page(pageNum - 1, pageSize, balanceList);

        // 构造分页数据
        Page<AgentClientBalanceVO> page = new Page<>(pageNum, pageSize);
        page.setTotal(balanceList.size());
        PageInfo<AgentClientBalanceVO> result = new PageInfo<>(page);
        result.setList(balanceList);
        return getDataTable(result);
    }
}
