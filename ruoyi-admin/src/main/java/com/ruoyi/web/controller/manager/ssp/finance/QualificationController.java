package com.ruoyi.web.controller.manager.ssp.finance;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.req.qualification.QualificationReq;
import com.ruoyi.system.service.qualification.AccountQualificationService;
import com.ruoyi.system.vo.qualification.QualificationInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * ssp管理后台 资质管理
 * <AUTHOR>
 * @date 2021/9/9 5:14 下午
 */
@RestController
@RequestMapping("manager/ssp/qualification")
public class QualificationController extends BaseController {

    @Autowired
    private AccountQualificationService accountQualificationService;

    /**
     * 新增编辑资质
     * @param req 请求参数
     * @return 结果
     */
    @Log(title = "资质", businessType = BusinessType.INSERT)
    @PostMapping("insertOrUpdate")
    public Result<Boolean> insertOrUpdate(@RequestBody @Validated QualificationReq req){
        return ResultBuilder.success(accountQualificationService.insertOrUpdate(req));
    }

    /**
     * 获取资质详情
     * @return 结果
     */
    @GetMapping("info")
    public Result<QualificationInfoVO> info(){
        LoginUser user = SecurityUtils.getLoginUser();
        return ResultBuilder.success(accountQualificationService.selectQualificationInfoByAccountId(user.getCrmAccountId()));
    }
}
