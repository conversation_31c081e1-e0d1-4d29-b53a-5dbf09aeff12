package com.ruoyi.web.controller.manager.crm.ssp.setting;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.ConvertUploadStatus;
import com.ruoyi.common.enums.advert.AdvertCostBillingTypeEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.bo.landpage.LandPageSumDataBo;
import com.ruoyi.system.bo.publisher.ConvertUploadSummaryDataBo;
import com.ruoyi.system.entity.advert.AdvertCost;
import com.ruoyi.system.entity.convert.ConvertUploadRecordEntity;
import com.ruoyi.system.entity.datashow.AdvertSlotDayData;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.landpage.LandpageFormFullRecord;
import com.ruoyi.system.mapper.landpage.LandpageFormFullRecordMapper;
import com.ruoyi.system.req.manager.ConvertUploadRecordListReq;
import com.ruoyi.system.service.convert.ConvertUploadRecordService;
import com.ruoyi.system.service.datasource.AdvertSlotDayDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.manager.AdvertCostService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.vo.manager.ConvertUploadRecordExcelVO;
import com.ruoyi.system.vo.manager.ConvertUploadSummaryDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 媒体转化上报数据Controller
 *
 * <AUTHOR>
 * @date 2022-10-19
 */
@Slf4j
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/ssp/setting/convertUploadRecord")
public class ConvertUploadRecordController extends BaseController {

    @Autowired
    private ConvertUploadRecordService convertUploadRecordService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private AdvertCostService advertCostService;

    @Autowired
    private AdvertSlotDayDataService advertSlotDayDataService;

    @Autowired
    private LandpageFormFullRecordMapper landpageFormFullRecordMapper;

    /**
     * 查询汇总数据
     */
    @GetMapping("/list")
    public TableDataInfo<ConvertUploadSummaryDataVO> list(ConvertUploadRecordListReq req) {
        if (null == req.getSlotId()) {
            return getDataTable(Collections.emptyList());
        }
        TableSupport.startPage();
        List<ConvertUploadSummaryDataBo> list = convertUploadRecordService.selectSummaryData(req);

        // 补充信息
        Date today = DateUtil.beginOfDay(new Date());
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(Collections.singletonList(req.getSlotId()));
        Map<Date, SlotData> slotDataMap = getSlotDataMap(req.getSlotId(), req.getStartDate(), req.getEndDate());

        // 构造返回结果
        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            ConvertUploadSummaryDataVO ruleVO = BeanUtil.copyProperties(data, ConvertUploadSummaryDataVO.class);
            ruleVO.setSlotName(slotNameMap.get(data.getSlotId()));
            ruleVO.setConvertPercent(NumberUtils.calculatePercent(data.getConvertCount(), data.getConvertCount() + data.getUnConvertCount()));
            if (data.getCurDate().before(today)) {
                Optional.ofNullable(slotDataMap.get(data.getCurDate())).ifPresent(slotData -> ruleVO.setNhCost(slotData.getNhCost()));
            } else {
                ruleVO.setNhCost(calculateNhCost(req.getSlotId(), today));
            }
            ruleVO.setAppRevenue(NumberUtils.defaultInt(data.getConvertCount()) * 3000);
            Optional.ofNullable(ruleVO.getNhCost()).ifPresent(nhCost -> ruleVO.setRevenuePercent(NumberUtils.calculatePercent(ruleVO.getAppRevenue(), nhCost.intValue())));
            return ruleVO;
        }));
    }

    /**
     * 导出上报记录
     */
    @Log(title = "媒体转化上报", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ConvertUploadRecordListReq req) {
        if (null == req.getSlotId()) {
            return error("无效的广告ID");
        }
        if (null == req.getStartDate() || null == req.getEndDate()) {
            return error("请选择日期范围");
        }

        List<ConvertUploadRecordEntity> list = convertUploadRecordService.selectList(req);

        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(ListUtils.mapToList(list, ConvertUploadRecordEntity::getSlotId));
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(ListUtils.mapToList(list, ConvertUploadRecordEntity::getAdvertId));

        List<ConvertUploadRecordExcelVO> excels = list.stream().map(record -> {
            ConvertUploadRecordExcelVO excelVO = BeanUtil.copyProperties(record, ConvertUploadRecordExcelVO.class);
            excelVO.setUploadStatusStr(ConvertUploadStatus.getDescByStatus(record.getUploadStatus()));
            excelVO.setSlotName(slotNameMap.get(record.getSlotId()));
            excelVO.setAdvertName(advertNameMap.get(record.getAdvertId()));
            return excelVO;
        }).collect(Collectors.toList());

        String fileName = String.format("%s_广告位%s媒体转化上报记录.xlsx", UUID.randomUUID().toString(), req.getSlotId());
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, ConvertUploadRecordExcelVO.class).sheet("媒体转化上报记录").doWrite(excels);
        return success(fileName);
    }

    private Map<Date, SlotData> getSlotDataMap(Long slotId, Date startDate, Date endDate) {
        SlotData param = new SlotData();
        param.setSlotId(slotId);
        param.setStartDate(startDate);
        param.setEndDate(endDate);
        List<SlotData> list = slotDataService.selectSlotDataList(param);
        return list.stream().collect(Collectors.toMap(SlotData::getCurDate, Function.identity(), (v1, v2) -> v2));
    }

    private Long calculateNhCost(Long slotId, Date date) {
        // 结算指标成本列表
        Map<Long, AdvertCost> advertCostMap = advertCostService.selectMapByDateAndType(date, AdvertCostBillingTypeEnum.LANDPAGE_CONVERT.getType());

        // 查询广告-消耗数据
        Map<Long, Integer> advertConsumeMap = queryAdvertConsumeMap(slotId, date);

        LandpageFormFullRecord param = new LandpageFormFullRecord();
        param.setCbStartDate(date);
        param.setCbEndDate(date);
        param.setSlotIds(Collections.singletonList(slotId));
        List<LandPageSumDataBo> landPageSumDataBos = landpageFormFullRecordMapper.selectSumPriceGroupBySlot(param);
        Map<Long, Long> priceSumMap = landPageSumDataBos.stream().collect(Collectors.toMap(LandPageSumDataBo::getSlotId, LandPageSumDataBo::getFormPriceSum));

        // 遍历广告位计算结算款
        try {
            long nhCost = priceSumMap.getOrDefault(slotId, 0L);
            // 所有结算指标为落地页转化的广告
            Set<Long> advertIds = advertCostMap.keySet();
            // 计算结算指标为CPC的广告
            for (Map.Entry<Long, Integer> entry : advertConsumeMap.entrySet()) {
                if (advertIds.contains(entry.getKey())) {
                    continue;
                }
                nhCost = nhCost + entry.getValue();
            }
            return nhCost;
        } catch (Exception e) {
            log.error("calculateNhCost error, slotId={}", slotId, e);
        }
        return 0L;
    }

    private Map<Long, Integer> queryAdvertConsumeMap(Long slotId, Date date) {
        AdvertSlotDayData param = new AdvertSlotDayData();
        param.setCurDate(date);
        param.setSlotId(slotId);
        List<AdvertSlotDayData> list = advertSlotDayDataService.selectAdvertSlotDayDataList(param);
        return list.stream().collect(Collectors.toMap(AdvertSlotDayData::getAdvertId, AdvertSlotDayData::getConsume, (v1, v2) -> v2));
    }
}
