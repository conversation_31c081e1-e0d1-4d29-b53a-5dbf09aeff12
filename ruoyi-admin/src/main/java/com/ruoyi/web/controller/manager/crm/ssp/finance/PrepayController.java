package com.ruoyi.web.controller.manager.crm.ssp.finance;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.PrepaySubjectEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.account.OaStaffBo;
import com.ruoyi.system.bo.invoice.InvoiceUrlBO;
import com.ruoyi.system.entity.account.finance.AccountPrepayApplyRecordEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.account.OaStaffManger;
import com.ruoyi.system.manager.publisher.PublisherManager;
import com.ruoyi.system.manager.publisher.PublisherPrepayManager;
import com.ruoyi.system.req.publisher.prepay.PrepayApplyReq;
import com.ruoyi.system.req.publisher.prepay.PrepayAuditReq;
import com.ruoyi.system.req.publisher.prepay.PrepayRecordListParam;
import com.ruoyi.system.req.publisher.prepay.PrepayRecordListReq;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.publisher.prepay.AccountPrepayApplyRecordService;
import com.ruoyi.system.vo.publisher.prepay.PublisherPrepayRecordVO;
import com.ruoyi.system.vo.publisher.prepay.PublisherPrepayVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.ruoyi.common.constant.OaConstants.FINANCE_AUDITOR_LIST;
import static com.ruoyi.common.constant.OaConstants.PERMISSION_PREPAY_BUSINESS_AUDIT;
import static com.ruoyi.common.constant.OaConstants.PERMISSION_PREPAY_DEPARTMENT_AUDIT;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;
import static com.ruoyi.common.enums.publisher.PrepayAuditStatus.isInAudit;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.CEO_READY;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.FINANCE_READY;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.LEADER_READY;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.getCeoAuditStatus;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.getFinanceAuditStatus;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.getLeaderAuditStatus;

/**
 * 媒体预付款Controller
 *
 * <AUTHOR>
 * @date 2022-07-29
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/ssp/prepay")
public class PrepayController extends BaseController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private PublisherManager publisherManager;

    @Autowired
    private PublisherPrepayManager publisherPrepayManager;

    @Autowired
    private AccountPrepayApplyRecordService accountPrepayApplyRecordService;

    @Autowired
    private OaStaffManger oaStaffManger;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    /**
     * 获取预付款媒体列表
     */
    @GetMapping(value = "getPrepayPublisher")
    public Result<List<PublisherPrepayVO>> getPrepayPublisher() {
        List<Long> accountIds = null;
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return ResultBuilder.success(Collections.emptyList());
            }
            accountIds = permission.getValues();
        }
        List<PublisherPrepayVO> list = publisherManager.getPrepayPublisherForApply(accountIds);
        return ResultBuilder.success(list);
    }

    /**
     * 预付款列表
     */
    @GetMapping("list")
    public TableDataInfo<PublisherPrepayRecordVO> list(PrepayRecordListReq req) {
        PrepayRecordListParam param = BeanUtil.copyProperties(req, PrepayRecordListParam.class);
        // 媒体账号模糊查询
        if (StringUtils.isNotBlank(req.getAccountSearch())) {
            List<Long> accountIds = accountService.selectIdsByIdOrEmailOrCompanyName(req.getAccountSearch());
            if (CollectionUtils.isEmpty(accountIds)) {
                return getDataTable(Collections.emptyList());
            }
            param.setAccountIds(accountIds);
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return getDataTable(Collections.emptyList());
            }
            if (CollectionUtils.isEmpty(param.getAccountIds())) {
                param.setAccountIds(permission.getValues());
            } else {
                param.getAccountIds().retainAll(permission.getValues());
            }
            if (CollectionUtils.isEmpty(param.getAccountIds())) {
                return getDataTable(Collections.emptyList());
            }
        }

        // 查询预付款列表
        startPage();
        List<AccountPrepayApplyRecordEntity> list = accountPrepayApplyRecordService.selectList(param);
        // 补充信息
        List<Long> accountIds = ListUtils.mapToList(list, AccountPrepayApplyRecordEntity::getAccountId);
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(accountIds);
        Map<Long, String> emailMap = accountService.selectEmailByIds(accountIds);
        Map<Long, String> operatorNameMap = accountService.selectAccountContactMap(getOperatorIds(list));
        LoginUser user = SecurityUtils.getLoginUser();
        OaStaffBo staff = oaStaffManger.selectByEmail(user.getEmail());
        String postKey = null != staff.getPost() ? staff.getPost().getPostKey() : "";

        // 构造返回结果
        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            PublisherPrepayRecordVO recordVO = BeanUtil.copyProperties(record, PublisherPrepayRecordVO.class, "invoiceList");
            recordVO.setCompanyName(companyNameMap.get(record.getAccountId()));
            recordVO.setEmail(emailMap.get(record.getAccountId()));
            recordVO.setApplicantName(operatorNameMap.get(record.getApplicantId()));
            recordVO.setAuditorName(operatorNameMap.get(record.getAuditorId()));
            recordVO.setLeaderAuditorName(operatorNameMap.get(record.getLeaderAuditorId()));
            recordVO.setCeoAuditorName(operatorNameMap.get(record.getCeoAuditorId()));
            recordVO.setFinanceAuditorName(operatorNameMap.get(record.getFinanceAuditorId()));
            recordVO.setComplexAuditStatus(NumberUtils.defaultInt(record.getComplexAuditStatus(), record.getAuditStatus()));
            recordVO.setLeaderAuditStatus(getLeaderAuditStatus(recordVO.getComplexAuditStatus()).getStatus());
            recordVO.setCeoAuditStatus(getCeoAuditStatus(recordVO.getComplexAuditStatus()).getStatus());
            recordVO.setFinanceAuditStatus(getFinanceAuditStatus(recordVO.getComplexAuditStatus()).getStatus());
            recordVO.setInvoiceList(JSON.parseArray(record.getInvoiceList(), InvoiceUrlBO.class));

            // 审核权限判断
            if (user.isAdmin() && isInAudit(record.getAuditStatus())
                    || Objects.equals(recordVO.getComplexAuditStatus(), LEADER_READY.getStatus()) && staff.getOaPermissionKeys().contains(PERMISSION_PREPAY_DEPARTMENT_AUDIT)
                    || Objects.equals(recordVO.getComplexAuditStatus(), CEO_READY.getStatus()) && staff.getOaPermissionKeys().contains(PERMISSION_PREPAY_BUSINESS_AUDIT)
                    || Objects.equals(recordVO.getComplexAuditStatus(), FINANCE_READY.getStatus()) && FINANCE_AUDITOR_LIST.contains(postKey)) {
                recordVO.setCanAudit(1);
            } else {
                recordVO.setCanAudit(0);
            }

           // 获取预付款主体名称
            recordVO.setPrepaySubjectNames(!StringUtils.isEmpty(record.getPrepaySubjectList()) ?
                    PrepaySubjectEnum.getNamesByTypeList(JSONObject.parseArray(record.getPrepaySubjectList(), Integer.class)) : "");
            return recordVO;
        }));
    }

    /**
     * 预付款申请
     */
    @Log(title = "媒体预付款申请", businessType = BusinessType.INSERT)
    @PostMapping("prepayApply")
    public Result<Boolean> prepayApply(@RequestBody @Validated PrepayApplyReq req) {
        publisherPrepayManager.prepayApply(req);
        return ResultBuilder.success();
    }

    /**
     * 预付款审核
     */
    @Log(title = "媒体预付款审核", businessType = BusinessType.UPDATE)
    @PostMapping("prepayAudit")
    public Result<Boolean> prepayAudit(@RequestBody @Validated PrepayAuditReq req) {
        publisherPrepayManager.prepayAudit(req);
        return ResultBuilder.success();
    }

    /**
     * 获取申请人/审核人ID列表
     */
    private List<Long> getOperatorIds(List<AccountPrepayApplyRecordEntity> list) {
        List<Long> operatorIds = new ArrayList<>();
        for (AccountPrepayApplyRecordEntity record : list) {
            if (null != record.getApplicantId()) {
                operatorIds.add(record.getApplicantId());
            }
            if (null != record.getAuditorId()) {
                operatorIds.add(record.getAuditorId());
            }
            if (null != record.getLeaderAuditorId()) {
                operatorIds.add(record.getLeaderAuditorId());
            }
            if (null != record.getCeoAuditorId()) {
                operatorIds.add(record.getCeoAuditorId());
            }
            if (null != record.getFinanceAuditorId()) {
                operatorIds.add(record.getFinanceAuditorId());
            }
        }
        return operatorIds;
    }
}
