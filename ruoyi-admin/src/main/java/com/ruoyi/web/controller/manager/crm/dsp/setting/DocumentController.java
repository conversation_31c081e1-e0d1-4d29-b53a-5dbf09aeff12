package com.ruoyi.web.controller.manager.crm.dsp.setting;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.common.DocumentEntity;
import com.ruoyi.system.req.manager.DocumentListReq;
import com.ruoyi.system.req.manager.DocumentReq;
import com.ruoyi.system.service.common.DocumentService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.vo.manager.DocumentVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * [CRM后台]对接文档管理
 *
 * <AUTHOR>
 * @date 2022-08-23
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/setting/document")
public class DocumentController extends BaseController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private DocumentService documentService;

    /**
     * 查询对接文档列表
     */
    @GetMapping("list")
    public TableDataInfo<DocumentVO> list(DocumentListReq req) {
        startPage();
        List<DocumentEntity> list = documentService.selectList(BeanUtil.copyProperties(req, DocumentEntity.class));

        Map<Long, String> operatorNameMap = accountService.selectAccountContactMap(ListUtils.mapToList(list, DocumentEntity::getOperatorId));
        return getDataTable(PageInfoUtils.dto2Vo(list, document -> {
            DocumentVO documentVO = BeanUtil.copyProperties(document, DocumentVO.class);
            documentVO.setOperatorName(operatorNameMap.get(document.getOperatorId()));
            return documentVO;
        }));
    }

    /**
     * 上传文档
     */
    @PostMapping("/add")
    public Result<Void> add(@RequestBody DocumentReq req) {
        if (StringUtils.isBlank(req.getDocumentUrl())) {
            return ResultBuilder.fail("文档地址不能为空");
        }
        if (req.getDocumentUrl().length() > 255) {
            return ResultBuilder.fail("文档地址过长，上传失败");
        }
        if (documentService.existDuplication(null, req.getCompanyName(), req.getDocumentName(), req.getDocumentUrl())) {
            return ResultBuilder.fail("文件/文档地址重复");
        }
        DocumentEntity document = BeanUtil.copyProperties(req, DocumentEntity.class);
        document.setOperateTime(new Date());
        document.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
        int result = documentService.insert(document);
        return ResultBuilder.result(result);
    }

    /**
     * 更新文档
     */
    @PostMapping("/update")
    public Result<Void>  update(@RequestBody DocumentReq req) {
        if (StringUtils.isBlank(req.getDocumentUrl())) {
            return ResultBuilder.fail("文档地址不能为空");
        }
        if (req.getDocumentUrl().length() > 255) {
            return ResultBuilder.fail("文档地址过长，上传失败");
        }
        if (documentService.existDuplication(req.getId(), req.getCompanyName(), req.getDocumentName(), req.getDocumentUrl())) {
            return ResultBuilder.fail("文件/文档地址重复");
        }
        DocumentEntity document = BeanUtil.copyProperties(req, DocumentEntity.class);
        document.setOperateTime(new Date());
        document.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
        int result = documentService.updateById(document);
        return ResultBuilder.result(result);
    }
}
