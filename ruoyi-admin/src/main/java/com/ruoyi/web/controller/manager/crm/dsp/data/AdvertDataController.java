package com.ruoyi.web.controller.manager.crm.dsp.data;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.advert.AdvertStatusEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.bo.advert.AdvertDayDataBo;
import com.ruoyi.system.bo.landpage.AdvertConvDataParamBo;
import com.ruoyi.system.bo.landpage.ConvDataBo;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.datashow.AdvertAppDayData;
import com.ruoyi.system.entity.datashow.AdvertDayData;
import com.ruoyi.system.entity.datashow.AdvertDayStatisticData;
import com.ruoyi.system.entity.datashow.AdvertHourData;
import com.ruoyi.system.entity.datashow.AdvertSlotDayData;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.req.advert.AdvertAppDayDataReq;
import com.ruoyi.system.req.advert.AdvertDayDataReq;
import com.ruoyi.system.req.advert.AdvertHourDataReq;
import com.ruoyi.system.req.advert.AdvertSlotDayDataReq;
import com.ruoyi.system.service.datasource.AdvertAppDayDataService;
import com.ruoyi.system.service.datasource.AdvertDayBudgetDataService;
import com.ruoyi.system.service.datasource.AdvertDayDataService;
import com.ruoyi.system.service.datasource.AdvertHourDataService;
import com.ruoyi.system.service.datasource.AdvertSlotConvDayDataService;
import com.ruoyi.system.service.datasource.AdvertSlotConvHourDataService;
import com.ruoyi.system.service.datasource.AdvertSlotDayDataService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SearchConditionService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.vo.advert.AdvertAppDayDataVO;
import com.ruoyi.system.vo.advert.AdvertDayDataIncludeSlotVO;
import com.ruoyi.system.vo.advert.AdvertDayDataVO;
import com.ruoyi.system.vo.advert.AdvertHourDataVO;
import com.ruoyi.system.vo.advert.AdvertSlotDayDataVO;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.BizConstants.INVALID_ID_LIST;
import static com.ruoyi.common.constant.BizConstants.OUTER_HD_ADVERT;

/**
 * 广告维度日数据Controller
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/datashow/advertData/")
public class AdvertDataController extends BaseController {

    @Autowired
    private AdvertDayDataService advertDayDataService;

    @Autowired
    private AdvertHourDataService advertHourDataService;

    @Autowired
    private AdvertAppDayDataService advertAppDayDataService;

    @Autowired
    private AdvertSlotDayDataService advertSlotDayDataService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertDayBudgetDataService advertDayBudgetDataService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertSlotConvDayDataService advertSlotConvDayDataService;

    @Autowired
    private AdvertSlotConvHourDataService advertSlotConvHourDataService;

    @Autowired
    private SearchConditionService searchConditionService;

    /**
     * 查询广告维度日数据列表
     */
    @GetMapping("/advertDayDataList")
    public TableDataInfo advertDayDataList(AdvertDayDataReq req) {
        PageInfo<AdvertDayDataVO> advertDayDataVOPageInfo = selectAdvertDayData(req);
        return getDataTable(advertDayDataVOPageInfo);
    }

    /**
     * 查询广告维度日数据汇总
     */
    @GetMapping("/statisticAdvertDayData")
    public Result<AdvertDayDataVO> statisticAdvertDayData(AdvertDayDataReq req) {
        return ResultBuilder.success(selectStatisticAdvertDayData(req));
    }

    /**
     * 导出广告维度日数据列表
     */
    @Log(title = "广告维度日数据", businessType = BusinessType.EXPORT)
    @GetMapping("/advertDayDataExport")
    public AjaxResult advertDayDataExport(AdvertDayDataReq req) {
        req.setIsExport(true);
        PageInfo<AdvertDayDataVO> advertDayDataVOPageInfo = selectAdvertDayData(req);
        List<AdvertDayDataVO> list = advertDayDataVOPageInfo.getList();
        ExcelUtil<AdvertDayDataVO> util = new ExcelUtil<>(AdvertDayDataVO.class);
        return util.exportExcel(list, "广告日数据");
    }

    /**
     * 导出广告纬度日数据列表 含广告位
     */
    @Log(title = "广告纬度日数据含广告位",businessType = BusinessType.EXPORT)
    @GetMapping("/advertDayDataIncludeSlotExport")
    public AjaxResult advertDayDataIncludeSlotExport(AdvertDayDataReq req){
        AdvertSlotDayData param = new AdvertSlotDayData();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 广告关键词查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertSearch(param.getAdvertIds(), req.getAdvertSearch()));
        // 广告主/代理商查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertiserOrAgent(param.getAdvertIds(), req.getAdvertiserIds(), req.getAgentIds()));
        // 负责人查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByManagerIds(param.getAdvertIds(), req.getManagerIds()));
        // 数据权限控制
        param.setAdvertIds(searchConditionService.filterAdvertIdsByDatePermission(param.getAdvertIds()));
        if (CollUtil.isEqualList(param.getAdvertIds(), INVALID_ID_LIST)) {
            ExcelUtil<AdvertDayDataIncludeSlotVO> util = new ExcelUtil<>(AdvertDayDataIncludeSlotVO.class);
            return util.exportExcel(Collections.emptyList(), "广告日数据");
        }

        List<AdvertSlotDayData> list = advertSlotDayDataService.selectAdvertSlotDayDataList(param);
        ExcelUtil<AdvertDayDataIncludeSlotVO> util = new ExcelUtil<>(AdvertDayDataIncludeSlotVO.class);
        // 广告位
        List<Long> slotIds = list.stream().map(AdvertSlotDayData::getSlotId).collect(Collectors.toList());
        List<Long> advertIdList = list.stream().map(AdvertSlotDayData::getAdvertId).collect(Collectors.toList());
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(advertIdList);
        Map<Long, Slot> slotMap = slotService.selectSlotAppMapByIds(slotIds);
        // 转化数据
        Map<String, ConvDataBo> convMap = advertSlotConvDayDataService.countByDateAndAdvertIdAndSlotIds(req.getStartDate(), req.getEndDate(), advertIdList, slotIds);
        return util.exportExcel(list.stream().map(data -> convertToAdvertSlotData(data, advertMap, slotMap, convMap)).collect(Collectors.toList()), "广告日数据");
    }

    /**
     * 查询广告维度时段数据列表
     */
    @GetMapping("/advertHourDataList")
    public TableDataInfo advertHourDataList(AdvertHourDataReq req) {
        // 构造搜索条件
        AdvertHourData param = new AdvertHourData();
        param.setAdvertId(req.getAdvertId());
        param.setCurDate(req.getDate());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 广告关键词查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertSearch(param.getAdvertIds(), req.getAdvertSearch()));
        // 广告主/代理商查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertiserOrAgent(param.getAdvertIds(), req.getAdvertiserIds(), req.getAgentIds()));
        // 负责人查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByManagerIds(param.getAdvertIds(), req.getManagerIds()));
        // 数据权限控制
        param.setAdvertIds(searchConditionService.filterAdvertIdsByDatePermission(param.getAdvertIds()));

        startPage();
        List<AdvertHourData> list;
        if (null == req.getAdvertId()) {
            list = advertHourDataService.selectAdvertHourDataListGroupByDateHour(param);
            List<Date> dateList = ListUtils.mapToList(list, AdvertHourData::getCurDate);
            Map<String, ConvDataBo> convMap = advertSlotConvHourDataService.countByDateListAndAdvertIdsGroupByDateHour(dateList, param.getAdvertIds());
            return getDataTable(PageInfoUtils.dto2Vo(list, data -> convertTo(data, convMap, DateUtil.formatDate(data.getCurDate()) + "_" + data.getCurHour())));
        }

        list = advertHourDataService.selectAdvertHourDataList(param);
        List<Date> dateList = ListUtils.mapToList(list, AdvertHourData::getCurDate);
        List<Long> advertIds = ListUtils.mapToList(list, AdvertHourData::getAdvertId);
        Map<String, ConvDataBo> convMap = advertSlotConvHourDataService.countByDateListAndAdvertIds(dateList, advertIds);
        return getDataTable(PageInfoUtils.dto2Vo(list, data -> convertTo(data, convMap, DateUtil.formatDate(data.getCurDate()) + "_" + data.getCurHour() + "_" + data.getAdvertId())));
    }


    /**
     * 查询广告维度时段数据汇总
     */
    @GetMapping("/statisticAdvertHourData")
    public Result<AdvertHourDataVO> statisticAdvertHourData(AdvertHourDataReq req) {
        // 构造搜索条件
        AdvertHourData param = new AdvertHourData();
        param.setAdvertId(req.getAdvertId());
        param.setCurDate(req.getDate());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 广告关键词查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertSearch(param.getAdvertIds(), req.getAdvertSearch()));
        // 广告主/代理商查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertiserOrAgent(param.getAdvertIds(), req.getAdvertiserIds(), req.getAgentIds()));
        // 负责人查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByManagerIds(param.getAdvertIds(), req.getManagerIds()));
        // 数据权限控制
        param.setAdvertIds(searchConditionService.filterAdvertIdsByDatePermission(param.getAdvertIds()));

        // 数据查询
        AdvertHourData data = advertHourDataService.selectStatisticAdvertHourData(param);
        if (null == data) {
            return ResultBuilder.success();
        }
        ConvDataBo convData = advertSlotConvDayDataService.selectStatisticAdvertConvData(BeanUtil.copyProperties(param, AdvertConvDataParamBo.class));

        // 数据处理
        AdvertHourDataVO vo = BeanUtil.copyProperties(data, AdvertHourDataVO.class);
        vo.setConsume(NumberUtils.fenToYuan(data.getConsume()));
        vo.setCtr(NumberUtils.calculatePercent(data.getClickPv(), data.getExposurePv()));
        vo.setCpc(NumberUtils.calculateRate(data.getConsume(), data.getClickPv() * 100));
        vo.setCvr(NumberUtils.calculatePercent(data.getLpClickPv(), data.getClickPv()));
        vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getClickUv()));
        vo.setArpu(NumberUtils.calculateRate(data.getConsume(), data.getAdLaunchPv() * 100));
        vo.setTakeCvr(NumberUtils.calculatePercent(data.getTakeUv(), data.getLpExposureUv()));
        vo.setTakeCost(NumberUtils.calculateRate(data.getConsume(), data.getTakeUv() * 100));
        vo.setLpClickCost(NumberUtils.calculateRate(data.getConsume(), data.getLpClickPv() * 100));
        vo.setLpArriveRate(NumberUtils.calculatePercent(data.getLpExposurePv(), data.getClickPv()));
        vo.setRegister(convData.getRegister());
        vo.setPay(convData.getPay());
        vo.setRefund(convData.getRefund());
        vo.setComplain(convData.getComplain());
        vo.setAppActive(convData.getAppActive());
        vo.setExtPrice(convData.getPayPrice());
        vo.setPayCost(NumberUtils.calculateRate(data.getConsume(), NumberUtils.defaultInt(convData.getPay()) * 100));
        return ResultBuilder.success(vo);
    }

    /**
     * 导出广告维度时段数据列表
     */
    @Log(title = "广告维度时段数据", businessType = BusinessType.EXPORT)
    @GetMapping("/advertHourDataExport")
    public AjaxResult advertHourDataExport(AdvertHourDataReq req) {
        // 构造搜索条件
        AdvertHourData param = new AdvertHourData();
        param.setAdvertId(req.getAdvertId());
        param.setCurDate(req.getDate());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 广告关键词查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertSearch(param.getAdvertIds(), req.getAdvertSearch()));
        // 广告主/代理商查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertiserOrAgent(param.getAdvertIds(), req.getAdvertiserIds(), req.getAgentIds()));
        // 负责人查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByManagerIds(param.getAdvertIds(), req.getManagerIds()));
        // 数据权限控制
        param.setAdvertIds(searchConditionService.filterAdvertIdsByDatePermission(param.getAdvertIds()));

        ExcelUtil<AdvertHourDataVO> util = new ExcelUtil<>(AdvertHourDataVO.class);
        List<AdvertHourData> list;
        if (null == req.getAdvertId()) {
            list = advertHourDataService.selectAdvertHourDataListGroupByDateHour(param);
            List<Date> dateList = ListUtils.mapToList(list, AdvertHourData::getCurDate);
            Map<String, ConvDataBo> convMap = advertSlotConvHourDataService.countByDateListAndAdvertIdsGroupByDateHour(dateList, param.getAdvertIds());
            return util.exportExcel(list.stream().map(data -> convertTo(data, convMap, DateUtil.formatDate(data.getCurDate()) + "_" + data.getCurHour())).collect(Collectors.toList()), "广告时段数据");
        }

        list = advertHourDataService.selectAdvertHourDataList(param);
        List<Date> dateList = ListUtils.mapToList(list, AdvertHourData::getCurDate);
        List<Long> advertIds = ListUtils.mapToList(list, AdvertHourData::getAdvertId);
        Map<String, ConvDataBo> convMap = advertSlotConvHourDataService.countByDateListAndAdvertIds(dateList, advertIds);
        return util.exportExcel(list.stream().map(data -> convertTo(data, convMap, DateUtil.formatDate(data.getCurDate()) + "_" + data.getCurHour() + "_" + data.getAdvertId())).collect(Collectors.toList()), "广告时段数据");
    }

    /**
     * 查询广告媒体维度日数据列表
     */
    @GetMapping("/advertAppDayDataList")
    public TableDataInfo advertAppDayDataList(AdvertAppDayDataReq req) {
        if (null == req.getAdvertId()) {
            return getDataTable(Collections.emptyList());
        }

        AdvertAppDayData param = new AdvertAppDayData();
        param.setAdvertId(req.getAdvertId());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setAppId(req.getAppId());

        // 媒体名称模糊查询
        param.setAppIds(searchConditionService.filterAppIdsByAppName(param.getAppIds(), req.getAppName()));

        startPage();
        List<AdvertAppDayData> list = advertAppDayDataService.selectAdvertAppDayDataList(param);
        // 媒体名称
        List<Long> appIds = list.stream().map(AdvertAppDayData::getAppId).collect(Collectors.toList());
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        // 转化数据
        Map<String, ConvDataBo> convMap = advertSlotConvDayDataService.countByDateAndAdvertIdAndAppIds(req.getStartDate(), req.getEndDate(), req.getAdvertId(), appIds);
        return getDataTable(PageInfoUtils.dto2Vo(list, data -> convertTo(data, appNameMap, convMap)));
    }

    /**
     * 查询广告媒体维度日数据汇总
     */
    @GetMapping("/statisticAdvertAppDayData")
    public Result<AdvertAppDayDataVO> statisticAdvertAppDayData(AdvertAppDayDataReq req) {
        if (null == req.getAdvertId()) {
            return ResultBuilder.success();
        }

        AdvertAppDayData param = new AdvertAppDayData();
        param.setAdvertId(req.getAdvertId());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setAppId(req.getAppId());

        // 媒体名称模糊查询
        param.setAppIds(searchConditionService.filterAppIdsByAppName(param.getAppIds(), req.getAppName()));

        AdvertAppDayData data = advertAppDayDataService.selectStatisticAdvertAppDayData(param);
        if (null == data) {
            return ResultBuilder.success();
        }

        AdvertConvDataParamBo convParam = BeanUtil.copyProperties(param, AdvertConvDataParamBo.class);
        Slot slotParam = new Slot();
        slotParam.setAppId(param.getAppId());
        slotParam.setAppIds(param.getAppIds());
        convParam.setSlotIds(ListUtils.mapToList(slotService.selectList(slotParam), Slot::getId));
        ConvDataBo convData = advertSlotConvDayDataService.selectStatisticAdvertConvData(convParam);

        AdvertAppDayDataVO vo = BeanUtil.copyProperties(data, AdvertAppDayDataVO.class);
        vo.setConsume(NumberUtils.fenToYuan(data.getConsume()));
        vo.setCtr(NumberUtils.calculatePercent(data.getClickPv(), data.getExposurePv()));
        vo.setCpc(NumberUtils.calculateRate(data.getConsume(), data.getClickPv() * 100));
        vo.setCvr(NumberUtils.calculatePercent(data.getLpClickPv(), data.getClickPv()));
        vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getClickUv()));
        vo.setArpu(NumberUtils.calculateRate(data.getConsume(), data.getAdLaunchPv() * 100));
        vo.setTakeCvr(NumberUtils.calculatePercent(data.getTakeUv(), data.getLpExposureUv()));
        vo.setTakeCost(NumberUtils.calculateRate(data.getConsume(), data.getTakeUv() * 100));
        vo.setLpClickCost(NumberUtils.calculateRate(data.getConsume(), data.getLpClickPv() * 100));
        vo.setLpArriveRate(NumberUtils.calculatePercent(data.getLpExposurePv(), data.getClickPv()));
        vo.setRegister(convData.getRegister());
        vo.setPay(convData.getPay());
        vo.setRefund(convData.getRefund());
        vo.setComplain(convData.getComplain());
        vo.setAppActive(convData.getAppActive());
        vo.setExtPrice(convData.getPayPrice());
        vo.setPayCost(NumberUtils.calculateRate(data.getConsume(), NumberUtils.defaultInt(convData.getPay()) * 100));
        return ResultBuilder.success(vo);
    }

    /**
     * 导出广告媒体维度日数据列表
     */
    @Log(title = "广告媒体维度日数据", businessType = BusinessType.EXPORT)
    @GetMapping("/advertAppDayDataExport")
    public AjaxResult advertAppDayDataExport(AdvertAppDayDataReq req) {
        if (null == req.getAdvertId()) {
            return AjaxResult.success();
        }

        AdvertAppDayData param = new AdvertAppDayData();
        param.setAdvertId(req.getAdvertId());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setAppId(req.getAppId());

        // 媒体名称模糊查询
        param.setAppIds(searchConditionService.filterAppIdsByAppName(param.getAppIds(), req.getAppName()));
        if (CollUtil.isEqualList(param.getAppIds(), INVALID_ID_LIST)) {
            return new ExcelUtil<>(AdvertAppDayDataVO.class).exportExcel(null, "媒体数据");
        }

        List<AdvertAppDayData> list = advertAppDayDataService.selectAdvertAppDayDataList(param);
        ExcelUtil<AdvertAppDayDataVO> util = new ExcelUtil<>(AdvertAppDayDataVO.class);
        // 媒体名称
        List<Long> appIds = list.stream().map(AdvertAppDayData::getAppId).collect(Collectors.toList());
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        // 转化数据
        Map<String, ConvDataBo> convMap = advertSlotConvDayDataService.countByDateAndAdvertIdAndAppIds(req.getStartDate(), req.getEndDate(), req.getAdvertId(), appIds);
        return util.exportExcel(list.stream().map(data -> convertTo(data, appNameMap, convMap)).collect(Collectors.toList()), "媒体数据");
    }

    /**
     * 查询广告广告位维度日数据列表
     */
    @GetMapping("/advertSlotDayDataList")
    public TableDataInfo advertSlotDayDataList(AdvertSlotDayDataReq req) {
        if (null == req.getAdvertId()) {
            return getDataTable(Collections.emptyList());
        }

        AdvertSlotDayData param = new AdvertSlotDayData();
        param.setAdvertId(req.getAdvertId());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setSlotIds(new ArrayList<>());

        // 媒体模糊查询
        param.setSlotIds(searchConditionService.filterSlotIdsByAppSearch(param.getSlotIds(), req.getAppSearch()));
        // 广告位模糊查询
        param.setSlotIds(searchConditionService.filterSlotIdsBySlotSearch(param.getSlotIds(), req.getSlotSearch()));
        if (CollUtil.isEqualList(param.getSlotIds(), INVALID_ID_LIST)) {
            return getDataTable(Collections.emptyList());
        }

        startPage();
        List<AdvertSlotDayData> list = advertSlotDayDataService.selectAdvertSlotDayDataList(param);
        // 广告位
        List<Long> slotIds = list.stream().map(AdvertSlotDayData::getSlotId).collect(Collectors.toList());
        Map<Long, Slot> slotMap = slotService.selectSlotAppMapByIds(slotIds);
        // 转化数据
        Map<String, ConvDataBo> convMap = advertSlotConvDayDataService.countByDateAndAdvertIdAndSlotIds(req.getStartDate(), req.getEndDate(), req.getAdvertId(), slotIds);
        return getDataTable(PageInfoUtils.dto2Vo(list, data -> convertTo(data, slotMap, convMap)));
    }

    /**
     * 查询广告广告位维度日数据汇总
     */
    @GetMapping("/statisticAdvertSlotDayData")
    public Result<AdvertSlotDayDataVO> statisticAdvertSlotDayData(AdvertSlotDayDataReq req) {
        if (null == req.getAdvertId()) {
            return ResultBuilder.success();
        }

        AdvertSlotDayData param = new AdvertSlotDayData();
        param.setAdvertId(req.getAdvertId());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 媒体模糊查询
        param.setSlotIds(searchConditionService.filterSlotIdsByAppSearch(param.getSlotIds(), req.getAppSearch()));
        // 广告位模糊查询
        param.setSlotIds(searchConditionService.filterSlotIdsBySlotSearch(param.getSlotIds(), req.getSlotSearch()));
        if (CollUtil.isEqualList(param.getSlotIds(), INVALID_ID_LIST)) {
            return ResultBuilder.success();
        }

        AdvertSlotDayData data = advertSlotDayDataService.selectStatisticAdvertSlotDayData(param);
        if (null == data) {
            return ResultBuilder.success();
        }
        ConvDataBo convData = advertSlotConvDayDataService.selectStatisticAdvertConvData(BeanUtil.copyProperties(param, AdvertConvDataParamBo.class));

        AdvertSlotDayDataVO vo = BeanUtil.copyProperties(data, AdvertSlotDayDataVO.class);
        vo.setConsume(NumberUtils.fenToYuan(data.getConsume()));
        vo.setCtr(NumberUtils.calculatePercent(data.getClickPv(), data.getExposurePv()));
        vo.setCpc(NumberUtils.calculateRate(data.getConsume(), data.getClickPv() * 100));
        vo.setCvr(NumberUtils.calculatePercent(data.getLpClickPv(), data.getClickPv()));
        vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getClickUv()));
        vo.setArpu(NumberUtils.calculateRate(data.getConsume(), data.getAdLaunchPv() * 100));
        vo.setTakeCvr(NumberUtils.calculatePercent(data.getTakeUv(), data.getLpExposureUv()));
        vo.setTakeCost(NumberUtils.calculateRate(data.getConsume(), data.getTakeUv() * 100));
        vo.setLpClickCost(NumberUtils.calculateRate(data.getConsume(), data.getLpClickPv() * 100));
        vo.setLpArriveRate(NumberUtils.calculatePercent(data.getLpExposurePv(), data.getClickPv()));
        vo.setRegister(convData.getRegister());
        vo.setPay(convData.getPay());
        vo.setRefund(convData.getRefund());
        vo.setComplain(convData.getComplain());
        vo.setAppActive(convData.getAppActive());
        vo.setExtPrice(convData.getPayPrice());
        vo.setPayCost(NumberUtils.calculateRate(data.getConsume(), NumberUtils.defaultInt(convData.getPay()) * 100));
        return ResultBuilder.success(vo);
    }

    /**
     * 导出广告广告位维度日数据列表
     */
    @Log(title = "广告广告位维度日数据", businessType = BusinessType.EXPORT)
    @GetMapping("/advertSlotDayDataExport")
    public AjaxResult advertSlotDayDataExport(AdvertSlotDayDataReq req) {
        if (null == req.getAdvertId()) {
            return AjaxResult.success();
        }

        AdvertSlotDayData param = new AdvertSlotDayData();
        param.setAdvertId(req.getAdvertId());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 媒体模糊查询
        param.setSlotIds(searchConditionService.filterSlotIdsByAppSearch(param.getSlotIds(), req.getAppSearch()));
        // 广告位模糊查询
        param.setSlotIds(searchConditionService.filterSlotIdsBySlotSearch(param.getSlotIds(), req.getSlotSearch()));
        if (CollUtil.isEqualList(param.getSlotIds(), INVALID_ID_LIST)) {
            return new ExcelUtil<>(AdvertSlotDayDataVO.class).exportExcel(null, "广告位数据");
        }

        List<AdvertSlotDayData> list = advertSlotDayDataService.selectAdvertSlotDayDataList(param);
        ExcelUtil<AdvertSlotDayDataVO> util = new ExcelUtil<>(AdvertSlotDayDataVO.class);
        // 广告位
        List<Long> slotIds = list.stream().map(AdvertSlotDayData::getSlotId).collect(Collectors.toList());
        Map<Long, Slot> slotMap = slotService.selectSlotAppMapByIds(slotIds);
        // 转化数据
        Map<String, ConvDataBo> convMap = advertSlotConvDayDataService.countByDateAndAdvertIdAndSlotIds(req.getStartDate(), req.getEndDate(), req.getAdvertId(), slotIds);
        return util.exportExcel(list.stream().map(data -> convertTo(data, slotMap, convMap)).collect(Collectors.toList()), "广告位数据");
    }

    /**
     * 查询广告日数据
     */
    public PageInfo<AdvertDayDataVO> selectAdvertDayData(AdvertDayDataReq req){
        // 构造搜索条件
        AdvertDayData param = new AdvertDayData();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setOrderColumn(req.getOrderColumn());
        if (req.getIsAsc() != null) {
            param.setOrderType(req.getIsAsc() ? "ASC" : "DESC");
        }

        // 广告关键词查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertSearch(param.getAdvertIds(), req.getAdvertSearch()));
        // 广告主/代理商查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertiserOrAgent(param.getAdvertIds(), req.getAdvertiserIds(), req.getAgentIds()));
        // 负责人查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByManagerIds(param.getAdvertIds(), req.getManagerIds()));
        // 数据权限控制
        param.setAdvertIds(searchConditionService.filterAdvertIdsByDatePermission(param.getAdvertIds()));
        if (CollUtil.isEqualList(param.getAdvertIds(), INVALID_ID_LIST)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        // 查询广告数据
        if(BooleanUtils.isNotTrue(req.getIsExport())){
            startPage();
        }
        List<AdvertDayDataBo> advertDayDataList = advertDayDataService.selectAdvertDayDataBoList(param);

        // 补充信息
        List<Long> advertIds = advertDayDataList.stream().map(AdvertDayDataBo::getAdvertId).collect(Collectors.toList());
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(advertIds);
        Map<String, Integer> budgetMap = advertDayBudgetDataService.selectBudgetByDateAndAdvertIds(req.getStartDate(), req.getEndDate(), advertIds);
        Map<String, ConvDataBo> convMap = advertSlotConvDayDataService.countByDateAndAdvertIds(req.getStartDate(), req.getEndDate(), advertIds);
        Map<Long, String> managerNameMap = accountService.selectManagerMap(advertService.selectByIds(advertIds, Advert::getAdvertiserId));

        return PageInfoUtils.dto2Vo(advertDayDataList, data -> {
            String dataKey = data.getAdvertId() + "-" + DateUtils.dateTime(data.getCurDate());
            AdvertDayDataVO vo = BeanUtil.copyProperties(data, AdvertDayDataVO.class);
            Advert advert = advertMap.get(data.getAdvertId());
            if (null != advert) {
                vo.setAdvertName(advert.getAdvertName());
                vo.setStatusStr(AdvertStatusEnum.getStatusStr(advert.getAdvertStatus(), advert.getServingSwitch(), advert.getStartServingDate(), advert.getStopServingDate()));
                vo.setManagerName(managerNameMap.getOrDefault(advert.getAdvertiserId(), ""));
            } else if (Objects.equals(data.getAdvertId(), OUTER_HD_ADVERT)) {
                vo.setAdvertName("外部互动广告");
                vo.setStatusStr("-");
            }
            Integer budget = budgetMap.get(dataKey);
            vo.setBudget(null == budget ? "不限" : NumberUtils.fenToYuan(budget));
            Integer consume = NumberUtils.defaultInt(data.getConsume());
            vo.setConsume(NumberUtils.fenToYuan(consume));
            vo.setLpClickCost(NumberUtils.calculateRate(consume, data.getLpClickPv() * 100));
            vo.setLpExposurePVClickPv(NumberUtils.calculatePercent(data.getLpExposurePv(), data.getClickPv()));
            vo.setCtr(NumberUtils.calculatePercent(data.getClickPv(), data.getExposurePv()));
            vo.setCpc(NumberUtils.calculateRate(consume, data.getClickPv() * 100));
            vo.setTakeCvr(NumberUtils.calculatePercent(data.getTakeUv(), data.getLpExposureUv()));
            vo.setTakeCost(NumberUtils.calculateRate(consume, data.getTakeUv() * 100));
            vo.setCvr(NumberUtils.calculatePercent(data.getLpClickPv(), data.getClickPv()));
            vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getClickUv()));
            vo.setArpu(NumberUtils.calculateRate(consume, data.getAdLaunchPv() * 100));
            Optional.ofNullable(convMap.get(dataKey)).ifPresent(conv -> {
                vo.setRegister(conv.getRegister());
                vo.setPay(conv.getPay());
                vo.setRefund(conv.getRefund());
                vo.setComplain(conv.getComplain());
                vo.setAppActive(conv.getAppActive());
                vo.setPayCost(NumberUtils.calculateRate(consume, conv.getPay() * 100));
                vo.setExtPrice(conv.getPayPrice());
            });
            return vo;
        });
    }

    /**
     * 查询广告日数据汇总
     */
    public AdvertDayDataVO selectStatisticAdvertDayData(AdvertDayDataReq req) {
        // 构造搜索条件
        AdvertDayData param = new AdvertDayData();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 广告关键词查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertSearch(param.getAdvertIds(), req.getAdvertSearch()));
        // 广告主/代理商查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertiserOrAgent(param.getAdvertIds(), req.getAdvertiserIds(), req.getAgentIds()));
        // 负责人查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByManagerIds(param.getAdvertIds(), req.getManagerIds()));
        // 数据权限控制
        param.setAdvertIds(searchConditionService.filterAdvertIdsByDatePermission(param.getAdvertIds()));
        if (CollUtil.isEqualList(param.getAdvertIds(), INVALID_ID_LIST)) {
            return null;
        }

        AdvertDayStatisticData data = advertDayDataService.selectStatisticAdvertDayData(param);
        if (null == data) {
            return null;
        }

        ConvDataBo convData = advertSlotConvDayDataService.selectStatisticAdvertConvData(BeanUtil.copyProperties(param, AdvertConvDataParamBo.class));

        AdvertDayDataVO vo = BeanUtil.copyProperties(data, AdvertDayDataVO.class);
        vo.setConsume(NumberUtils.fenToYuan(data.getConsume()));
        vo.setLpClickCost(NumberUtils.calculateRate(data.getConsume(), data.getLpClickPv() * 100));
        vo.setLpExposurePVClickPv(NumberUtils.calculatePercent(data.getLpExposurePv(), data.getClickPv()));
        vo.setCtr(NumberUtils.calculatePercent(data.getClickPv(), data.getExposurePv()));
        vo.setCpc(NumberUtils.calculateRate(data.getConsume(), data.getClickPv() * 100));
        vo.setCvr(NumberUtils.calculatePercent(data.getLpClickPv(), data.getClickPv()));
        vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getClickUv()));
        vo.setArpu(NumberUtils.calculateRate(data.getConsume(), data.getAdLaunchPv() * 100));
        vo.setTakeCvr(NumberUtils.calculatePercent(data.getTakeUv(), data.getLpExposureUv()));
        vo.setTakeCost(NumberUtils.calculateRate(data.getConsume(), data.getTakeUv() * 100));
        vo.setRegister(convData.getRegister());
        vo.setPay(convData.getPay());
        vo.setRefund(convData.getRefund());
        vo.setComplain(convData.getComplain());
        vo.setAppActive(convData.getAppActive());
        vo.setExtPrice(convData.getPayPrice());
        vo.setPayCost(NumberUtils.calculateRate(data.getConsume(), NumberUtils.defaultInt(convData.getPay()) * 100));
        return vo;
    }

    private AdvertHourDataVO convertTo(AdvertHourData data, Map<String, ConvDataBo> convMap, String convKey) {
        AdvertHourDataVO vo = BeanUtil.copyProperties(data, AdvertHourDataVO.class);
        vo.setPeriod(String.format("%d:00~%d:00", data.getCurHour(), data.getCurHour() + 1));
        vo.setConsume(NumberUtils.fenToYuan(data.getConsume()));
        vo.setCtr(NumberUtils.calculatePercent(data.getClickPv(), data.getExposurePv()));
        vo.setCpc(NumberUtils.calculateRate(data.getConsume(), data.getClickPv() * 100));
        vo.setCvr(NumberUtils.calculatePercent(data.getLpClickPv(), data.getClickPv()));
        vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getClickUv()));
        vo.setArpu(NumberUtils.calculateRate(data.getConsume(), data.getAdLaunchPv() * 100));
        vo.setTakeCvr(NumberUtils.calculatePercent(data.getTakeUv(), data.getLpExposureUv()));
        vo.setTakeCost(NumberUtils.calculateRate(data.getConsume(), data.getTakeUv() * 100));
        vo.setLpClickCost(NumberUtils.calculateRate(data.getConsume(), data.getLpClickPv() * 100));
        vo.setLpArriveRate(NumberUtils.calculatePercent(data.getLpExposurePv(), data.getClickPv()));
        Optional.ofNullable(convMap.get(convKey)).ifPresent(conv -> {
            vo.setRegister(conv.getRegister());
            vo.setPay(conv.getPay());
            vo.setRefund(conv.getRefund());
            vo.setComplain(conv.getComplain());
            vo.setAppActive(conv.getAppActive());
            vo.setPayCost(NumberUtils.calculateRate(data.getConsume(), NumberUtils.defaultInt(conv.getPay()) * 100));
            vo.setExtPrice(conv.getPayPrice());
        });
        return vo;
    }

    private AdvertAppDayDataVO convertTo(AdvertAppDayData data, Map<Long, String> appNameMap, Map<String, ConvDataBo> convMap) {
        AdvertAppDayDataVO vo = BeanUtil.copyProperties(data, AdvertAppDayDataVO.class);
        vo.setAppName(appNameMap.get(data.getAppId()));
        vo.setConsume(NumberUtils.fenToYuan(data.getConsume()));
        vo.setCtr(NumberUtils.calculatePercent(data.getClickPv(), data.getExposurePv()));
        vo.setCpc(NumberUtils.calculateRate(data.getConsume(), data.getClickPv() * 100));
        vo.setCvr(NumberUtils.calculatePercent(data.getLpClickPv(), data.getClickPv()));
        vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getClickUv()));
        vo.setArpu(NumberUtils.calculateRate(data.getConsume(), data.getAdLaunchPv() * 100));
        vo.setLpClickCost(NumberUtils.calculateRate(data.getConsume(), data.getLpClickPv() * 100));
        vo.setLpArriveRate(NumberUtils.calculatePercent(data.getLpExposurePv(), data.getClickPv()));
        vo.setTakeCvr(NumberUtils.calculatePercent(data.getTakeUv(), data.getLpExposurePv()));
        vo.setTakeCost(NumberUtils.calculateRate(data.getConsume(), data.getTakeUv() * 100));
        String dataKey = data.getAppId() + "_" + DateUtil.formatDate(data.getCurDate());
        Optional.ofNullable(convMap.get(dataKey)).ifPresent(conv -> {
            vo.setRegister(conv.getRegister());
            vo.setPay(conv.getPay());
            vo.setRefund(conv.getRefund());
            vo.setComplain(conv.getComplain());
            vo.setAppActive(conv.getAppActive());
            vo.setPayCost(NumberUtils.calculateRate(data.getConsume(), conv.getPay() * 100));
            vo.setExtPrice(conv.getPayPrice());
        });
        return vo;
    }

    private AdvertSlotDayDataVO convertTo(AdvertSlotDayData data, Map<Long, Slot> slotMap, Map<String, ConvDataBo> convMap) {
        AdvertSlotDayDataVO vo = BeanUtil.copyProperties(data, AdvertSlotDayDataVO.class);
        Slot slot = slotMap.get(data.getSlotId());
        if (null != slot) {
            vo.setSlotName(slot.getSlotName());
            vo.setAppId(slot.getAppId());
            vo.setAppName(slot.getAppName());
        }

        vo.setConsume(NumberUtils.fenToYuan(data.getConsume()));
        vo.setCtr(NumberUtils.calculatePercent(data.getClickPv(), data.getExposurePv()));
        vo.setCpc(NumberUtils.calculateRate(data.getConsume(), data.getClickPv() * 100));
        vo.setCvr(NumberUtils.calculatePercent(data.getLpClickPv(), data.getClickPv()));
        vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getClickUv()));
        vo.setArpu(NumberUtils.calculateRate(data.getConsume(), data.getAdLaunchPv() * 100));
        vo.setTakeCvr(NumberUtils.calculatePercent(data.getTakeUv(), data.getLpExposureUv()));
        vo.setTakeCost(NumberUtils.calculateRate(data.getConsume(), data.getTakeUv() * 100));
        vo.setLpClickCost(NumberUtils.calculateRate(data.getConsume(), data.getLpClickPv() * 100));
        vo.setLpArriveRate(NumberUtils.calculatePercent(data.getLpExposurePv(), data.getClickPv()));
        String dataKey = data.getSlotId() + "_" + DateUtil.formatDate(data.getCurDate());
        Optional.ofNullable(convMap.get(dataKey)).ifPresent(conv -> {
            vo.setRegister(conv.getRegister());
            vo.setPay(conv.getPay());
            vo.setRefund(conv.getRefund());
            vo.setComplain(conv.getComplain());
            vo.setAppActive(conv.getAppActive());
            vo.setPayCost(NumberUtils.calculateRate(data.getConsume(), conv.getPay() * 100));
            vo.setExtPrice(conv.getPayPrice());
        });
        return vo;
    }

    private AdvertDayDataIncludeSlotVO convertToAdvertSlotData(AdvertSlotDayData data, Map<Long, Advert> advertMap,Map<Long, Slot> slotMap,
                                                               Map<String, ConvDataBo> convMap) {
        AdvertDayDataIncludeSlotVO vo = BeanUtil.copyProperties(data, AdvertDayDataIncludeSlotVO.class);
        Slot slot = slotMap.get(data.getSlotId());
        if (null != slot) {
            vo.setSlotName(slot.getSlotName());
            vo.setSlotId(slot.getId());
        }

        Advert advert = advertMap.get(data.getAdvertId());
        if (Objects.nonNull(advert)) {
            vo.setAdvertName(advert.getAdvertName());
            vo.setStatusStr(AdvertStatusEnum.getStatusStr(advert.getAdvertStatus(), advert.getServingSwitch(), advert.getStartServingDate(), advert.getStopServingDate()));
        } else if (Objects.equals(data.getAdvertId(), OUTER_HD_ADVERT)) {
            vo.setAdvertName("外部互动广告");
            vo.setStatusStr("-");
        }

        vo.setConsume(NumberUtils.fenToYuan(data.getConsume()));
        vo.setCtr(NumberUtils.calculatePercent(data.getClickPv(), data.getExposurePv()));
        vo.setCpc(NumberUtils.calculateRate(data.getConsume(), data.getClickPv() * 100));
        vo.setCvr(NumberUtils.calculatePercent(data.getLpClickPv(), data.getClickPv()));
        vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getClickUv()));
        vo.setArpu(NumberUtils.calculateRate(data.getConsume(), data.getAdLaunchPv() * 100));
        vo.setTakeCvr(NumberUtils.calculatePercent(data.getTakeUv(), data.getLpExposureUv()));
        vo.setTakeCost(NumberUtils.calculateRate(data.getConsume(), data.getTakeUv() * 100));
        vo.setLpClickCost(NumberUtils.calculateRate(data.getConsume(), data.getLpClickPv() * 100));
        vo.setLpExposurePVClickPv(NumberUtils.calculatePercent(data.getLpExposurePv(),data.getClickPv()));
        String dataKey = data.getSlotId() + "_" + data.getAdvertId() +"_"+ DateUtil.formatDate(data.getCurDate());
        Optional.ofNullable(convMap.get(dataKey)).ifPresent(conv -> {
            vo.setRegister(conv.getRegister());
            vo.setPay(conv.getPay());
            vo.setRefund(conv.getRefund());
            vo.setComplain(conv.getComplain());
            vo.setAppActive(conv.getAppActive());
            vo.setPayCost(NumberUtils.calculateRate(data.getConsume(), conv.getPay() * 100));
            vo.setExtPrice(conv.getPayPrice());
        });
        return vo;
    }
}
