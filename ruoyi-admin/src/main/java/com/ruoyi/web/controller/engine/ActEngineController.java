package com.ruoyi.web.controller.engine;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.local.RequestThreadLocal;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.plugin.PluginSwitchEnum;
import com.ruoyi.common.enums.plugin.PluginTypeEnum;
import com.ruoyi.common.utils.InnerLogUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.adengine.IpAreaDto;
import com.ruoyi.system.domain.adengine.SlotCacheDto;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.req.engine.*;
import com.ruoyi.system.service.adengine.AdvertEngineService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.engine.*;
import com.ruoyi.system.service.engine.cache.ActivityCacheService;
import com.ruoyi.system.service.engine.cache.DomainCacheService;
import com.ruoyi.system.service.engine.cache.MaterialCacheService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.vo.activity.ActivityVO;
import com.ruoyi.system.vo.engine.AdvertResp;
import com.ruoyi.system.vo.engine.NhAdvertResp;
import com.ruoyi.system.vo.engine.NhRecordVO;
import com.ruoyi.system.vo.slot.RetConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.BizConstants.CHINA_AREA_NUM;
import static com.ruoyi.common.enums.advert.AdvertFlagEnum.*;

/**
 * 活动投放引擎(缓存+不鉴权)
 *
 * <AUTHOR>
 * @date 2021/7/19
 */
@Slf4j
@Controller
@RequestMapping("/act")
public class ActEngineController {

    @Autowired
    private ActivityCacheService activityCacheService;

    @Autowired
    private AdvertEngineService advertEngineService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private StatService statService;

    @Autowired
    private ActivityJoinService activityJoinService;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DomainCacheService domainCacheService;

    @Autowired
    private MaterialCacheService materialCacheService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    /**
     * 获取活动投放素材、活动参数数据等信息
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/init")
    public AjaxResult init(ActivityInitReq req, HttpServletRequest request) {
        // 解码活动ID
        Long activityId = decodeActivityId(req.getId());
        if (null == activityId) {
            return AjaxResult.success();
        }

        try {
            // 获取页面素材
            ActivityVO activityVO = activityCacheService.getActivityCache(activityId);
            if (null == activityVO) {
                return AjaxResult.success();
            }
            activityVO = (ActivityVO) activityVO.clone();
            // 获取活动剩余参与次数
            Integer remainJoinTimes = activityJoinService.getRemainJoinTimes(activityId);
            activityVO.setRemainJoinTimes(null == remainJoinTimes ? activityVO.getJoinTimes() : remainJoinTimes);
            // 返回挽留设置
            activityVO.setRetConfig(getRetConfig(RequestThreadLocal.get().getSlotId()));
            // 获取备案号
            String icpNo = StringUtils.defaultString(activityVO.getIcpNo());
            String domain = UrlUtils.extractDomain(StringUtils.defaultIfBlank(req.getReferer(), request.getHeader("Referer")));
            if (!icpNo.toUpperCase().contains("ICP")) {
                icpNo += domainCacheService.selectIcpNo(domain);
            }
            activityVO.setIcpNo(icpNo);
            // IP解析
            IpAreaDto ipArea = areaService.ipAnalysis(IpUtils.getIpAddr(request));
            if (null != ipArea) {
                activityVO.setIsp(ipArea.getIsp());
            }
            return AjaxResult.success(activityVO);
        } catch (Exception e) {
            log.error("活动初始化异常, req={}", JSON.toJSONString(req), e);
        }
        return AjaxResult.success();
    }

    @CrossOrigin
    @ResponseBody
    @GetMapping("/nh/getAdvert")
    public AjaxResult getNhAdvert(NhAdvertReq req, HttpServletRequest request, HttpServletResponse response) {
        // 预览模式不出广告
        if (StrUtil.equalsIgnoreCase(ServletUtils.getParameter("mode"), "preview")) {
            return AjaxResult.error("预览模式不出广告");
        }

        String deviceId = RequestThreadLocal.get().getDeviceId();
        Long appId = RequestThreadLocal.get().getAppId();
        Long slotId = RequestThreadLocal.get().getSlotId();
        Long consumerId = RequestThreadLocal.get().getConsumerId();

        if (null == appId || null == slotId || null == consumerId) {
            return AjaxResult.error("参数异常");
        }
        if (StringUtils.isBlank(deviceId)) {
            return AjaxResult.error("deviceId不能为空");
        }

        AdvertResp resp = new AdvertResp();

        try {
            // 校验参与次数
            if (!activityJoinService.canJoin()) {
                return AjaxResult.error(ErrorCode.E103001);
            }

            JSONObject logJson = InnerLogUtils.buildJSON();

            // 活动参与埋点
            statService.activityJoin(logJson);

            // 生成订单号
            String orderId = orderService.generateOrderId(consumerId);
            RequestThreadLocal.get().setOrderId(orderId);

            // 券请求埋点
            statService.advertRequest(logJson);

            // 缓存媒体参数
            callbackService.cacheParameter(request, orderId);

            // 投放诺禾广告
            NhAdvertResp nhAdvert = advertEngineService.getNhAdvert(req, logJson);
            resp.setAdvertFlag(RequestThreadLocal.get().getAdvertFlag());
            if (null != nhAdvert) {
                resp.setOrderId(orderId);
                resp.setAdvertDetail(nhAdvert);
                return AjaxResult.success(resp);
            } else {
                RequestThreadLocal.get().setAdvertFlag(DEGRADE.getType());
                log.info("无可投广告, req={}, orderId={}", JSON.toJSONString(req), orderId);
            }
            return AjaxResult.success(resp);
        } catch (Exception e) {
            log.error("获取广告异常, req={}", JSON.toJSONString(req), e);
        } finally {
            // 参与次数-1
            activityJoinService.minusJoinTimes();
        }
        return AjaxResult.error("获取广告失败");
    }

    @CrossOrigin
    @ResponseBody
    @GetMapping("/nh/stat")
    public AjaxResult nhStat(NhStatReq req) {
        // 预览模式不打埋点
        if (StrUtil.equalsIgnoreCase(ServletUtils.getParameter("mode"), "preview")) {
            return AjaxResult.success();
        }

        InnerLogType type = InnerLogType.getByType(req.getType());
        if (null == type) {
            return AjaxResult.success("参数错误");
        }

        switch (type) {
            case ACTIVITY_REQUEST:
                statService.activityRequest(req);
                break;
            case ACTIVITY_MAIN_LOAD:
                statService.activityMainLoad(req);
                break;
            case ADVERT_EXPOSURE:
                statService.advertExposure(req);
                break;
            case ADVERT_CLICK:
                statService.advertClick(req.getOrderId());
                break;
            case LANDPAGE_EXPOSURE:
                statService.landpageExposure(req.getOrderId());
                break;
            case LANDPAGE_CLICK:
                statService.landpageClick(req.getOrderId());
                break;
            case PLUGIN_EXPOSURE:
                statService.pluginExposure(req);
                break;
            case PRIZE_ICON_CLICK:
            case KEFU_ICON_CLICK:
            case RULE_ICON_CLICK:
                statService.innerLog(type);
                break;
            case MINIPROGRAM_PLUGIN_EXPOSURE:
                statService.innerLogStatTLByOrderId(req);
                // 小程序插件曝光同时也是小程序广告的券曝光
                statService.advertExposure(req);
                break;
            case QUICKAPP_LAUNCH:
            case MINIPROGRAM_PLUGIN_CLICK:
            case MINIPROGRAM_PLUGIN_POPUP_EXPOSURE:
            case MINIPROGRAM_PLUGIN_POPUP_CANCEL:
            case BLIND_BOX_JOIN:
            case BLIND_BOX_POPUP_EXPOSURE:
            case BLIND_BOX_POPUP_CLICK:
            case TRANSFER_PAGE_EXPOSURE:
            case TRANSFER_PAGE_CLICK:
                statService.innerLogStatByOrderId(type, req.getOrderId());
                break;
            case MINIPROGRAM_PLUGIN_POPUP_CONFIRM:
                statService.innerLogStatTLByOrderId(req);
                // 小程序插件允许同时也是小程序广告的券点击
                statService.advertClick(req.getOrderId());
                break;
            default:
                return AjaxResult.success("无效的类型");
        }
        return AjaxResult.success();
    }

    /**
     * 互动广告跳转
     */
    @CrossOrigin
    @GetMapping("/nh/redirect")
    public void nhRedirect(NhRedirectReq req, HttpServletResponse response) throws IOException {
        statService.advertClick(req.getOrderId());
        if (StringUtils.isNotEmpty(req.getUrl())) {
            if (!req.getUrl().startsWith("http") && !req.getUrl().contains("//")) {
                response.sendRedirect("https://" + req.getUrl());
            } else {
                response.sendRedirect(req.getUrl());
            }
        }
    }

    /**
     * 直投广告跳转
     * 注:直投广告的计费已经优化为「出券即计费」
     */
    @Deprecated
    @CrossOrigin
    @GetMapping("/nh/directRedirect")
    public void nhDirectRedirect(NhRedirectReq req, HttpServletResponse response) throws IOException {
        // 防刷限制，一次广告位访问只能有一次券点击
        String nonce = ServletUtils.getRequest().getParameter("nonce");
        if (StringUtils.isBlank(nonce)) {
            return;
        }
        String key = EngineRedisKeyFactory.K038.join(nonce);
        if (null == redisCache.getCacheObject(key)) {
            return;
        }
        statService.directAdvertClick(req.getOrderId());
        redisCache.deleteObject(key);
        if (StringUtils.isNotEmpty(req.getUrl())) {
            if (!req.getUrl().startsWith("http") && !req.getUrl().contains("//")) {
                response.sendRedirect("https://" + req.getUrl());
            } else {
                response.sendRedirect(req.getUrl());
            }
        }
    }

    @CrossOrigin
    @ResponseBody
    @GetMapping("/nh/record")
    public AjaxResult record() {
        Long consumerId = RequestThreadLocal.get().getConsumerId();
        List<Order> orders = orderService.selectByConsumerIdAndDate(consumerId, null);
        if (CollectionUtils.isEmpty(orders)) {
            return AjaxResult.success();
        }
        return AjaxResult.success(orders.stream().map(order -> {
            NhRecordVO record = new NhRecordVO();
            record.setOrderId(order.getOrderId());
            AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
            if (null != adSnapshot) {
                record.setAdvertCategory(adSnapshot.getAdvertCategory());
                record.setThumbnailImg(adSnapshot.getThumbnailImg());
                record.setLandpageUrl(adSnapshot.getLandpageUrl());
                record.setExtInfo(adSnapshot.getExtInfo());
                record.setAdvertTitle(materialCacheService.selectMaterialNameCache(order.getMaterialId()));
            }
            return record;
        }).filter(order -> StringUtils.isNotBlank(order.getThumbnailImg()) && StringUtils.isNotBlank(order.getAdvertTitle()))
        .collect(Collectors.toList()));
    }

    /**
     * 判断是否命中地域定向(需要广告默认配置定向了地域)
     *
     * 注:此功能用于给其他业务(小程序)提供地域判断的能力
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/isHitAreaTarget")
    public AjaxResult isHitAreaTarget(Long advertId, HttpServletRequest request) {
        AdvertOrientation orient = advertOrientationService.selectDefaultOrientationByAdvertId(advertId);
        if (null == orient) {
            return AjaxResult.success(false);
        }
        // 未配置
        Set<String> areaTargetSet = JSON.parseObject(orient.getAreaTarget(), new TypeReference<Set<String>>() {});
        if (CollectionUtils.isEmpty(areaTargetSet)) {
            return AjaxResult.success(false);
        }
        IpAreaDto ipArea = areaService.ipAnalysis(IpUtils.getIpAddr(request));
        return AjaxResult.success(areaTargetSet.contains(CHINA_AREA_NUM)
                || areaTargetSet.contains(StringUtils.defaultString(ipArea.getProvinceAreaNum()))
                || areaTargetSet.contains(StringUtils.defaultString(ipArea.getCityAreaNum())));
    }


    /**
     * 判断用户今日是否触发过返回挽留
     *
     * @return 是否触发过
     */
    private boolean isRetExposed() {
        // 如果用户当日触发过，则不再开启
        String redisKey = EngineRedisKeyFactory.K011.join(DateUtil.today());
        return redisCache.cacheSetContains(redisKey, String.valueOf(RequestThreadLocal.get().getConsumerId()));
    }

    /**
     * 获取返回挽留设置
     *
     * @param slotId 广告位ID
     * @return 返回挽留设置
     */
    private RetConfigVO getRetConfig(Long slotId) {
        SlotCacheDto slot = slotCacheService.getSlotCache(slotId);
        if (null == slot) {
            return null;
        }

        RetConfigVO retConfig = slot.getRetConfig();
        if (null != retConfig && PluginSwitchEnum.isOn(retConfig.getIsOpen())) {
            // 如果用户当日触发过，则不再开启
            if (isRetExposed()) {
                retConfig.setIsOpen(PluginSwitchEnum.OFF.getStatus());
            } else {
                if (Objects.equals(retConfig.getPluginType(), PluginTypeEnum.ACTIVITY.getType())) {
                    retConfig.setUrl(activityCacheService.getRedirectPathCache(retConfig.getActivityId()));
                    if (!retConfig.getUrl().startsWith("/")) {
                        retConfig.setUrl("/" + retConfig.getUrl());
                    }
                }
            }
            return retConfig;
        }
        return null;
    }

    /**
     * 活动ID解码
     */
    private Long decodeActivityId(String idStr) {
        try {
            // 为空判断
            if (StringUtils.isBlank(idStr)) {
                return null;
            }
            // 兼容Long类型活动ID
            if (StringUtils.isNumeric(idStr)) {
                return Long.valueOf(idStr);
            }
            // Base62解码
            String id = Base62.decodeStr(idStr);
            if (StringUtils.isNumeric(id)) {
                return Long.valueOf(id);
            }
            HttpServletRequest request = ServletUtils.getRequest();
            if (null == request) {
                return null;
            }
            String userAgent = request.getHeader("User-Agent");
            if (StrUtil.containsAny(userAgent, "Windows NT", "ANCHASHI-SCAN")) {
                return null;
            }
            String referer = request.getHeader("referer");
            if (StrUtil.containsAny(referer, ".baidu.com")) {
                return null;
            }
            log.error("活动ID解码失败, id={}, slotId={}, referer={}, ip={}, userAgent={}", idStr, RequestThreadLocal.get().getSlotId(),
                    referer, IpUtils.getIpAddr(request), userAgent);
        } catch (Exception e) {
            log.error("活动ID解码失败, id={}", idStr, e);
        }
        return null;
    }
}
