package com.ruoyi.web.controller.manager.crm.ssp.activity;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.entity.activity.Activity;
import com.ruoyi.system.entity.activity.ActivityPlan;
import com.ruoyi.system.req.activity.ActivityPlanReq;
import com.ruoyi.system.req.activity.ActivityPlanStatusReq;
import com.ruoyi.system.vo.activity.ActivityPlanVO;
import com.ruoyi.system.service.manager.ActivityService;
import com.ruoyi.system.service.manager.ActivitySkinService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.manager.ActivityPlanService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 活动推广计划Controller
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/actPlan")
public class ActivityPlanController extends BaseController {

    @Autowired
    private ActivityPlanService activityPlanService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private ActivitySkinService activitySkinService;

    /**
     * 查询活动推广计划列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ActivityPlanReq req) {
        ActivityPlan param = new ActivityPlan();

        // 搜索条件过滤
        if (null != req.getActivityId() || StringUtils.isNotEmpty(req.getActivityName())
                || StringUtils.isNotEmpty(req.getSkinCode())) {
            Activity actParam = new Activity();
            actParam.setId(req.getActivityId());
            actParam.setActivityName(req.getActivityName());
            actParam.setSkinCode(req.getSkinCode());
            List<Activity> activities = activityService.selectActivityList(actParam);
            if (CollectionUtils.isEmpty(activities)) {
                return getDataTable(Collections.emptyList());
            }
            param.setActivityIds(activities.stream().map(Activity::getId).collect(Collectors.toList()));
        }

        startPage();
        List<ActivityPlan> plans = activityPlanService.selectActivityPlanList(param);
        if (CollectionUtils.isEmpty(plans)) {
            return getDataTable(Collections.emptyList());
        }

        // 查询活动工具和活动皮肤
        List<Activity> activities = activityService.selectSimpleActivityList(new Activity());
        Map<Long, Activity> activityMap = activities.stream().collect(Collectors.toMap(Activity::getId, Function.identity(), (oldVal, newVal) -> newVal));
        Map<String, String> skinNameMap = activitySkinService.selectSkinNameMap();

        // 构造返回对象，补充活动工具和皮肤信息
        return getDataTable(PageInfoUtils.dto2Vo(plans, plan -> {
            ActivityPlanVO planVO = new ActivityPlanVO();
            planVO.setId(plan.getId());
            planVO.setActivityId(plan.getActivityId());
            planVO.setStatus(plan.getStatus());

            Activity activity = activityMap.get(plan.getActivityId());
            if (null != activity) {
                planVO.setActivityName(activity.getActivityName());
                planVO.setSkinName(skinNameMap.get(activity.getSkinCode()));
            }
            return planVO;
        }));
    }

    /**
     * 新增活动推广计划
     */
    @Log(title = "活动推广计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ActivityPlanReq req) {
        // 校验活动ID是否存在
        Activity activity = activityService.selectActivityById(req.getActivityId());
        if (null == activity) {
            return AjaxResult.error("无效的活动ID");
        }

        // 校验活动ID是否已被绑定
        ActivityPlan plan = activityPlanService.selectByActivityId(req.getActivityId());
        if (null != plan) {
            return AjaxResult.error("活动已被添加至推广计划");
        }

        return toAjax(activityPlanService.insertActivityPlan(req));
    }

    /**
     * 更新活动推广计划状态
     */
    @Log(title = "活动推广计划", businessType = BusinessType.UPDATE)
    @PostMapping("/status")
    public AjaxResult updateStatus(@RequestBody @Validated ActivityPlanStatusReq req) {
        return toAjax(activityPlanService.updateStatus(req));
    }
}
