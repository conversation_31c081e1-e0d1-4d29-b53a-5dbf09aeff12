package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.bo.advert.AdvertOrientLandpageBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataExcelBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkListParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleCacheBo;
import com.ruoyi.system.bo.landpage.article.ArticleCountBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.bo.landpage.article.ArticleRetConfigBo;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.req.landpage.article.ArticleAggrLinkAddReq;
import com.ruoyi.system.req.landpage.article.ArticleAggrLinkDataExportReq;
import com.ruoyi.system.req.landpage.article.ArticleAggrLinkListReq;
import com.ruoyi.system.req.landpage.article.ArticleAggrLinkUpdateReq;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkHourDataService;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkService;
import com.ruoyi.system.service.landpage.article.ArticleService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.service.fc.FcLinkArticleAggrRelService;
import com.ruoyi.system.vo.advert.AdvertSelectVO;
import com.ruoyi.system.vo.landpage.article.ArticleAggrLinkVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * [CRM后台]文章聚合链接管理
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Slf4j
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/landpage/articleAggrLink")
public class ArticleAggrLinkController extends BaseController {

    @Autowired
    public TransactionTemplate transactionTemplate;

    @Autowired
    private ArticleService articleService;

    @Autowired
    private ArticleAggrLinkService articleAggrLinkService;

    @Autowired
    private ArticleAggrLinkHourDataService articleAggrLinkHourDataService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AccountService accountService;

    @Autowired
    private FcLinkArticleAggrRelService fcLinkArticleAggrRelService;

    /**
     * 广告下拉列表
     */
    @GetMapping("/advertList")
    public Result<List<AdvertSelectVO>> advertList() {
        List<AdvertOrientLandpageBo> list = advertOrientationService.selectByLandpage("/api/wz/");
        return ResultBuilder.success(list.stream().filter(Objects::nonNull)
                .sorted((o1, o2) -> o2.getAdvertId().compareTo(o1.getAdvertId()))
                .map(bo -> new AdvertSelectVO(bo.getAdvertId(), bo.getAdvertName())).collect(Collectors.toList()));
    }

    /**
     * 查询文章聚合链接列表
     */
    @GetMapping("/list")
    public TableDataInfo<ArticleAggrLinkVO> list(ArticleAggrLinkListReq req) {
        ArticleAggrLinkListParamBo param = buildQueryParam(req);
        // 根据广告ID反查
        if (CollectionUtils.isNotEmpty(req.getAdvertIds())) {
            List<AdvertOrientLandpageBo> orientList = advertOrientationService.selectLandpageByAdvertIds(req.getAdvertIds());
            param.setKeys(orientList.stream().map(o -> o.getLandpageUrl().contains("/api/wz/") ? StringUtils.defaultString(ReUtil.getGroup0("(?<=api/wz/)\\w+", o.getLandpageUrl())) : "")
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(param.getKeys())) {
                return getDataTable(Collections.emptyList());
            }
        }
        startPage();
        List<ArticleAggrLinkEntity> list = articleAggrLinkService.selectList(param);

        List<Long> linkIds = ListUtils.mapToList(list, ArticleAggrLinkEntity::getId);
        Map<Long, ArticleCountBo> articleCountMap = articleService.countByLinkId(linkIds);
        Map<Long, ArticleAggrLinkDataBo> dataMap = articleAggrLinkHourDataService.selectTodayDataByLinkIds(linkIds);
        Map<String, List<String>> relateAdvertMap = getRelateAdvertMap();
        List<Long> advertiserIds = list.stream().map(ArticleAggrLinkEntity::getAdvertiserId).collect(Collectors.toList());
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(advertiserIds);

        return getDataTable(PageInfoUtils.dto2Vo(list, entity -> {
            ArticleAggrLinkVO vo = BeanUtil.copyProperties(entity, ArticleAggrLinkVO.class, "retConfig");
            vo.setAdvertiserName(companyNameMap.getOrDefault(entity.getAdvertiserId(), ""));
            ArticleAggrLinkDataBo data = dataMap.get(entity.getId());
            if (null != data) {
                vo.setRequestPv(data.getRequestPv());
                vo.setRequestUv(data.getRequestUv());
            } else {
                vo.setRequestPv(0);
                vo.setRequestUv(0);
            }
            vo.setRelateAdvertIds(relateAdvertMap.get(entity.getKey()));
            ArticleCountBo articleCount = articleCountMap.get(entity.getId());
            if (null != articleCount) {
                vo.setArticleTargetRequestPv(NumberUtils.defaultInt(articleCount.getArticleTargetRequestPv()));
                vo.setArticleCount(String.valueOf(articleCount.getArticleCount()));
                if (articleCount.getArticleCount() > 0) {
                    vo.setArticleCount(vo.getArticleCount() + "(" + articleCount.getOnlineArticleCount() + ")");
                }
            } else {
                vo.setArticleCount("0");
                vo.setArticleTargetRequestPv(0);
            }
            return vo;
        }));
    }

    /**
     * 查询文章聚合链接信息
     */
    @GetMapping(value = "/info")
    public Result<ArticleAggrLinkVO> getInfo(Long id) {
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(id);
        if (null == link) {
            return ResultBuilder.success();
        }

        Account account = accountService.selectAccountById(link.getAdvertiserId());
        ArticleAggrLinkVO articleAggrLinkVO = BeanUtil.copyProperties(link, ArticleAggrLinkVO.class, "retConfig");
        if (Objects.nonNull(account)) {
            articleAggrLinkVO.setAdvertiserName(account.getCompanyName());
        }

        articleAggrLinkVO.setRetConfig(JSON.parseObject(link.getRetConfig(), ArticleRetConfigBo.class));
        articleAggrLinkVO.setTodaySyIncrRequestPv(articleService.selectTodaySyIncrRequestPvSum(id));
        return ResultBuilder.success(articleAggrLinkVO);
    }

    /**
     * 新增文章聚合链接
     */
    @Log(title = "文章聚合链接", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public Result<Void> add(@RequestBody ArticleAggrLinkAddReq req) {
        if (StringUtils.isBlank(req.getName())) {
            return ResultBuilder.fail("投放链接名称不能为空");
        }
        if (NumberUtils.isNullOrLteZero(req.getAdvertiserId())) {
            return ResultBuilder.fail("请选择广告主");
        }
        if (articleAggrLinkService.isNameExist(req.getName(), req.getAdvertiserId(), null)) {
            return ResultBuilder.fail("该名称已存在");
        }
        LoginUser operator = SecurityUtils.getLoginUser();
        String key = articleAggrLinkService.generateKey();
        String url = sysConfigService.selectConfigCacheByKey(BizConfigEnum.DEFAULT_DOMAIN_ARTICLE.getKey()) + "/api/wz/" + key + "?userId=__N_DEVICE__";

        ArticleAggrLinkEntity link = new ArticleAggrLinkEntity();
        link.setKey(key);
        link.setName(req.getName());
        link.setUrl(url);
        link.setOperatorId(operator.getCrmAccountId());
        link.setOperatorName(operator.getUserName());
        link.setAdvertiserId(req.getAdvertiserId());
        link.setCreatorId(operator.getCrmAccountId());
        link.setCreatorName(operator.getUserName());
        articleAggrLinkService.insert(link);
        return ResultBuilder.success();
    }

    /**
     * 更新文章聚合链接
     */
    @Log(title = "文章聚合链接", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public Result<Void> update(@RequestBody ArticleAggrLinkUpdateReq req) {
        if (null != req.getRetSwitch() && !SwitchStatusEnum.isValidStatus(req.getRetSwitch())) {
            return ResultBuilder.fail("无效的返回拦截开关状态");
        }
        if (CollectionUtils.isNotEmpty(req.getRetUrls())) {
            for (String retUrl : req.getRetUrls()) {
                if (!retUrl.startsWith("http")) {
                    return ResultBuilder.fail("无效的返回拦截链接");
                } else if (retUrl.length() > 255) {
                    return ResultBuilder.fail("返回拦截链接长度超出限制");
                }
            }
        }
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(req.getLinkId());
        if (null == link) {
            return ResultBuilder.fail("无效的linkId");
        }

        ArticleAggrLinkEntity updateRecord = new ArticleAggrLinkEntity();
        updateRecord.setId(req.getLinkId());
        // 操作人
        LoginUser operator = SecurityUtils.getLoginUser();
        link.setOperatorId(operator.getCrmAccountId());
        link.setOperatorName(operator.getUserName());
        link.setOperatorTime(new Date());
        // 返回拦截配置
        if (null != req.getRetSwitch() || null != req.getRetUrls()) {
            ArticleRetConfigBo retConfig = JSON.parseObject(link.getRetConfig(), ArticleRetConfigBo.class);
            if (null == retConfig) {
                retConfig = new ArticleRetConfigBo();
            }
            if (null != req.getRetSwitch()) {
                retConfig.setRetSwitch(req.getRetSwitch());
            }
            if (null != req.getRetUrls()) {
                retConfig.setRetUrls(req.getRetUrls());
                retConfig.setRetTimes(req.getRetUrls().size());
            }
            updateRecord.setRetConfig(JSON.toJSONString(retConfig));
        }
        // 私域广告位
        updateRecord.setSySlot(req.getSySlot());
        articleAggrLinkService.updateById(updateRecord);
        redisCache.deleteObject(EngineRedisKeyFactory.K115.join(link.getKey()));
        return ResultBuilder.success();
    }

    /**
     * 移除链接
     */
    @Log(title = "文章聚合链接", businessType = BusinessType.UPDATE)
    @PostMapping("/remove")
    public Result<Void> remove(@RequestBody IdReq req) {
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(req.getId());
        if (null == link || Objects.equals(link.getStatus(), 1)) {
            return ResultBuilder.success();
        }
        Date now = new Date();
        ArticleAggrLinkDataParamBo param = new ArticleAggrLinkDataParamBo();
        param.setStartDate(DateUtil.beginOfDay(DateUtil.offsetDay(now, -30)));
        param.setLinkIds(Collections.singletonList(req.getId()));
        ArticleAggrLinkDataBo data = articleAggrLinkHourDataService.selectSumBy(param);
        if (null != data && data.getRequestPv() > 0) {
            return ResultBuilder.fail("该链接近30天有数据，暂时不能移除");
        }

        // 检查该聚合链接是否在丰巢链接中被使用
        List<String> fcLinkNames = fcLinkArticleAggrRelService.selectFcLinkNamesByArticleAggrLinkId(req.getId());
        if (CollectionUtils.isNotEmpty(fcLinkNames)) {
            String fcLinkNamesStr = String.join("、", fcLinkNames);
            return ResultBuilder.fail("该聚合链接正在被丰巢链接使用，不能移除。关联的丰巢链接：" + fcLinkNamesStr);
        }

        LoginUser operator = SecurityUtils.getLoginUser();
        ArticleAggrLinkEntity updateRecord = new ArticleAggrLinkEntity();
        updateRecord.setId(req.getId());
        updateRecord.setStatus(1);
        updateRecord.setOperatorId(operator.getCrmAccountId());
        updateRecord.setOperatorName(operator.getUserName());
        updateRecord.setOperatorTime(now);
        articleAggrLinkService.updateById(updateRecord);
        return ResultBuilder.success();
    }

    /**
     * 导出文章聚合链接数据
     */
    @Log(title = "文章聚合链接", businessType = BusinessType.EXPORT)
    @GetMapping("/dataExport")
    public AjaxResult dataExport(ArticleAggrLinkDataExportReq req) {
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(req.getLinkId());
        if (null == link) {
            return AjaxResult.error("链接不存在");
        }
        ArticleAggrLinkDataParamBo param = new ArticleAggrLinkDataParamBo();
        param.setLinkIds(Collections.singletonList(req.getLinkId()));
        param.setStartDate(DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -92)));
        List<ArticleAggrLinkDataBo> dataList = articleAggrLinkHourDataService.selectDataBy(param);

        List<ArticleAggrLinkDataExcelBo> excels = dataList.stream().map(data -> {
            ArticleAggrLinkDataExcelBo excelBo = BeanUtil.copyProperties(data, ArticleAggrLinkDataExcelBo.class);
            excelBo.setLinkName(link.getName());
            excelBo.setLinkUrl(link.getUrl());
            return excelBo;
        }).collect(Collectors.toList());
        ExcelUtil<ArticleAggrLinkDataExcelBo> util = new ExcelUtil<>(ArticleAggrLinkDataExcelBo.class);
        return util.exportExcel(excels, "文章聚合链接数据");
    }

    /**
     * 构造查询参数
     *
     * @param req 请求参数
     * @return 查询参数
     */
    private ArticleAggrLinkListParamBo buildQueryParam(ArticleAggrLinkListReq req) {
        ArticleAggrLinkListParamBo param = new ArticleAggrLinkListParamBo();
        param.setSearchKey(StrUtil.trim(req.getSearchKey()));
        return param;
    }

    /**
     * 获取关联广告
     *
     * @return 链接标识-广告配置列表映射
     */
    public Map<String, List<String>> getRelateAdvertMap() {
        List<AdvertOrientLandpageBo> landpageList = advertOrientationService.selectByLandpage("/api/wz/");
        if (CollectionUtils.isEmpty(landpageList)) {
            return Collections.emptyMap();
        }
        Map<String, List<String>> map = new HashMap<>();
        for (AdvertOrientLandpageBo landpage : landpageList) {
            String key = StringUtils.defaultString(ReUtil.getGroup0("(?<=api/wz/)\\w+", landpage.getLandpageUrl()));
            if (!map.containsKey(key)) {
                map.put(key, new ArrayList<>());
            }
            map.get(key).add(landpage.getAdvertId() + "-" + landpage.getOrientId());
        }
        return map;
    }
}
