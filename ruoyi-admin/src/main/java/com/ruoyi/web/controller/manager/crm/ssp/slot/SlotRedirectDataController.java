package com.ruoyi.web.controller.manager.crm.ssp.slot;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.slot.SlotRedirectType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.slot.SlotRedirectHourData;
import com.ruoyi.system.req.slot.data.SlotRedirectDataParam;
import com.ruoyi.system.req.slot.data.SlotRedirectDataReq;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.slot.SlotRedirectHourDataService;
import com.ruoyi.system.vo.slot.data.SlotRedirectDataExcelVO;
import com.ruoyi.system.vo.slot.data.SlotRedirectDataVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 广告位分流数据Controller
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/ssp/custom/slot/slotRedirectData")
public class SlotRedirectDataController extends BaseController {

    @Autowired
    private SlotRedirectHourDataService slotRedirectHourDataService;
    @Autowired
    private AdvertService advertService;

    /**
     * 查询广告位投放数据
     */
    @GetMapping("/list")
    public TableDataInfo<SlotRedirectDataVO> list(SlotRedirectDataReq req) {
        if (null == req.getSlotId()) {
            return getDataTable(Collections.emptyList());
        }

        SlotRedirectDataParam param = convertParam(req);
        startPage();
        List<SlotRedirectHourData> list = slotRedirectHourDataService.groupBy(param);
        List<Long> advertIds = list.stream().filter(data -> Objects.equals(data.getRedirectType(), SlotRedirectType.ADVERT.getType())).map(data -> Long.valueOf(data.getRedirectValue())).collect(Collectors.toList());
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(advertIds);

        // 构造返回对象，补充数据
        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            SlotRedirectDataVO slotRedirectDataVO = BeanUtil.copyProperties(data, SlotRedirectDataVO.class);
            if (Objects.equals(data.getRedirectType(), SlotRedirectType.ADVERT.getType()) && advertMap.containsKey(Long.valueOf(data.getRedirectValue()))) {
                Advert advert = advertMap.get(Long.valueOf(data.getRedirectValue()));
                slotRedirectDataVO.setAdvertName(advert.getAdvertName());
            }
            return slotRedirectDataVO;
        }));
    }

    /**
     * 导出广告位投放数据
     */
    @Log(title = "广告位投放数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(SlotRedirectDataReq req) {
        if (null == req.getSlotId()) {
            return success();
        }

        SlotRedirectDataParam param = convertParam(req);
        List<SlotRedirectHourData> list = slotRedirectHourDataService.groupBy(param);

        // 构造Excel导出对象
        List<SlotRedirectDataExcelVO> excels = list.stream().map(data -> {
            SlotRedirectDataExcelVO excelVO = BeanUtil.copyProperties(data, SlotRedirectDataExcelVO.class);
            excelVO.setRedirectType(SlotRedirectType.getDescByType(data.getRedirectType()));
            return excelVO;
        }).collect(Collectors.toList());

        String fileName = String.format("%s_广告位%s投放数据.xlsx", UUID.randomUUID().toString(), req.getSlotId());
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, SlotRedirectDataExcelVO.class).sheet("广告位投放数据").doWrite(excels);
        return AjaxResult.success(fileName);
    }

    private SlotRedirectDataParam convertParam(SlotRedirectDataReq req) {
        SlotRedirectDataParam param = new SlotRedirectDataParam();
        param.setSlotId(req.getSlotId());
        param.setStartDate(Optional.ofNullable(req.getStartDate()).map(DateUtil::beginOfDay).orElse(null));
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        return param;
    }
}
