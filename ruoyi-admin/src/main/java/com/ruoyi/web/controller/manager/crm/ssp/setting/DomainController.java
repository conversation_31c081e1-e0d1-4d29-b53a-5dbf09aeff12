package com.ruoyi.web.controller.manager.crm.ssp.setting;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.common.EnableStatusEnum;
import com.ruoyi.common.enums.common.SysConfigKeyEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.bo.slot.SlotDomainConfigBo;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.tagmanager.DomainTagRelationEntity;
import com.ruoyi.system.entity.tagmanager.TagManagerEntity;
import com.ruoyi.system.req.manager.DomainListReq;
import com.ruoyi.system.req.manager.DomainReplaceReq;
import com.ruoyi.system.req.manager.DomainReq;
import com.ruoyi.system.req.manager.DomainUpdateStatusReq;
import com.ruoyi.system.req.tag.DomainTagUpdateReq;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.SlotConfigService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.service.tagmanager.DomainTagRelationService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import com.ruoyi.system.vo.manager.DomainVO;
import com.ruoyi.system.vo.tagmanager.TagListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.domain.DomainType.isOtherDomain;

/**
 * 域名Controller
 *
 * <AUTHOR>
 * @date 2021-09-16
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/domain")
public class DomainController extends BaseController {

    @Autowired
    private DomainService domainService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private SlotConfigService slotConfigService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private DomainTagRelationService domainTagRelationService;

    @Autowired
    private TagManagerService tagManagerService;

    /**
     * 查询域名列表
     */
    @GetMapping("/list")
    public TableDataInfo<DomainVO> list(DomainListReq req) {
        // 参数处理
        Domain param = BeanUtil.copyProperties(req, Domain.class);
        param.setDomains(getDomainsBySlotId(req.getSlotId()));

        // 查询域名
        startPage();
        List<Domain> list = domainService.selectDomainList(param);

        // 查询域名关联的广告位/广告
        List<String> domains = ListUtils.mapToList(list, Domain::getDomain);
        Map<String, Set<Long>> domainSlotMap = getDomainSlotMap(domains);
        Map<String, Set<Long>> domainAdvertMap = getDomainAdvertMap(domains);

        // 查询默认域名
        String defaultSlotDomain = UrlUtils.extractDomain(sysConfigService.selectConfigCacheByKey(SysConfigKeyEnum.DEFAULT_SLOT_URL.getKey()));
        String defaultActivityDomain = UrlUtils.extractDomain(sysConfigService.selectConfigCacheByKey(SysConfigKeyEnum.DEFAULT_ACT_URL.getKey()));
        String defaultLandpageDomain = UrlUtils.extractDomain(sysConfigService.selectConfigCacheByKey(SysConfigKeyEnum.DEFAULT_DOMAIN_LANDPAGE.getKey()));

        // 构造返回结果
        return getDataTable(PageInfoUtils.dto2Vo(list, domain -> {
            DomainVO domainVO = BeanUtil.copyProperties(domain, DomainVO.class);
            domainVO.setAdvertIds(domainAdvertMap.get(domain.getDomain()));
            domainVO.setSlotIds(domainSlotMap.get(domain.getDomain()));
            if (EnableStatusEnum.isEnable(domain.getHttpsEnable())) {
                domainVO.setCertExpireTime(domainService.getCertificateExpireTime(domain.getDomain()));
            }
            if (Objects.equals(domain.getDomain(), defaultSlotDomain) && !StrUtil.contains(domain.getRemark(), "广告位默认域名")) {
                domainVO.setRemark("广告位默认域名。" + StringUtils.defaultString(domain.getRemark()));
            }
            if (Objects.equals(domain.getDomain(), defaultActivityDomain) && !StrUtil.contains(domain.getRemark(), "活动默认域名")) {
                domainVO.setRemark("活动默认域名。" + StringUtils.defaultString(domain.getRemark()));
            }
            if (Objects.equals(domain.getDomain(), defaultLandpageDomain) && !StrUtil.contains(domain.getRemark(), "落地页默认域名")) {
                domainVO.setRemark("落地页默认域名。" + StringUtils.defaultString(domain.getRemark()));
            }
            return domainVO;
        }));
    }

    /**
     * 查询有效域名分组列表
     */
    @GetMapping("/listValidByGroup")
    public AjaxResult listValidByGroup() {
        return AjaxResult.success(domainService.listValidByGroup());
    }

    /**
     * 获取域名详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(domainService.selectDomainById(id));
    }

    /**
     * 新增域名
     */
    @Log(title = "域名", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody DomainReq req) {
        if (StringUtils.isBlank(req.getDomain()) || null == req.getDomainType()) {
            return AjaxResult.error("参数错误");
        }
        if (!req.getDomain().contains(".")) {
            return AjaxResult.error("请输入有效的域名");
        }

        Domain domain = domainService.selectDomain(req.getDomain());
        if (null != domain) {
            return AjaxResult.error("域名已存在");
        }

//        int domainStatus = domainService.checkDomainStatus(req.getDomain(), req.getDomainType(), req.getHttpsEnable());
//        if (!Objects.equals(domainStatus, DomainStatus.NORMAL.getStatus())) {
//            return AjaxResult.error("域名无法访问，请联系后端同学检查域名配置后再添加");
//        }

        return toAjax(domainService.insertDomain(req));
    }

    /**
     * 修改域名
     */
    @Log(title = "域名", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody DomainReq req) {
        if (null == req.getId()) {
            return AjaxResult.error("参数错误");
        }
        return toAjax(domainService.updateDomain(req));
    }

    /**
     * 更新支付宝和微信可投状态
     *
     * @param req 参数
     * @return 是否更新成功
     */
    @Log(title = "域名",businessType = BusinessType.UPDATE)
    @PostMapping("updateWxAlipayStatus")
    public Result<Boolean> updateWxAlipayStatus(@RequestBody DomainUpdateStatusReq req){
        return ResultBuilder.success(domainService.updateWxAlipayStatus(req));
    }

    /**
     * 域名替换
     *
     * @param req 请求参数
     * @return 结果
     */
    @Log(title = "域名", businessType = BusinessType.UPDATE)
    @PostMapping("/domainReplace")
    public AjaxResult domainReplace(@RequestBody @Validated DomainReplaceReq req) {
        if (isOtherDomain(req.getDomainType())) {
            return AjaxResult.error("其他域名无法进行域名替换");
        }

        Set<String> failedSlotIds = new HashSet<>();
        List<String> slotIds = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(req.getSlotIds().replaceAll("，", ","));
        for (String slotId : slotIds) {
            if (!StringUtils.isNumeric(slotId)) {
                return AjaxResult.error("请输入正确的广告位ID");
            }
            boolean result = slotService.updateSlotDomainConfig(Long.valueOf(slotId), req.getDomainType(), req.getDomain());
            if (!result) {
                failedSlotIds.add(slotId);
            }
        }
        if (CollectionUtils.isNotEmpty(failedSlotIds)) {
            return AjaxResult.error("广告位 " + Joiner.on(",").join(failedSlotIds) + " 替换域名失败");
        }
        return AjaxResult.success();
    }

    /**
     * 删除域名
     */
    @Log(title = "域名", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        if (null == id) {
            return AjaxResult.error("无效的域名ID");
        }
        return toAjax(domainService.deleteDomainById(id));
    }

    /**
     * 获取广告位使用的域名
     *
     * @param domains 域名列表
     * @return 域名-广告位ID集合映射
     */
    private Map<String, Set<Long>> getDomainSlotMap(List<String> domains) {
        return  slotService.getDomainSlotMapS(domains);
    }

    /**
     * 获取广告/配置使用的域名
     *
     * @param domains 域名列表
     * @return 域名-广告ID集合映射
     */
    private Map<String, Set<Long>> getDomainAdvertMap(List<String> domains) {
        return advertService.getDomainAdvertMapS(domains);
    }

    /**
     * 根据域名id查询域名标签列表
     * @param domainId
     * @return
     */
    @GetMapping("getDomainTagList")
    public Result<List<TagListVO>> getDomainTagList(@Validated @NotNull(message = "域名id不能为空") Long domainId){
        List<Long> subTagIds = domainTagRelationService.selectTagIdsByDomainId(domainId);
        List<TagManagerEntity> subTagEntities = tagManagerService.selectByIds(subTagIds);
        Map<Long, List<TagManagerEntity>> tagMap = subTagEntities.stream().sorted(Comparator.comparing(TagManagerEntity::getTagSort)).collect(Collectors.groupingBy(TagManagerEntity::getParentId));

        List<Long> parentIds = subTagEntities.stream().map(TagManagerEntity::getParentId).collect(Collectors.toList());
        List<TagManagerEntity> parentTags = tagManagerService.selectByIds(parentIds);
        List<TagListVO> result = parentTags.stream().map(parent -> {
            TagListVO vo = BeanUtil.copyProperties(parent, TagListVO.class);
            List<TagManagerEntity> sub = tagMap.get(parent.getId());
            List<TagListVO> tagListVOS = BeanUtil.copyToList(sub, TagListVO.class);
            vo.setSubTags(tagListVOS);
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(result);
    }

    /**
     * 更新域名标签列表
     * @param req
     * @return
     */
    @PostMapping("updateDomainTagList")
    public Result<Boolean> updateAppTagList(@RequestBody DomainTagUpdateReq req){
        //删除所有标签
        domainTagRelationService.deleteByDomainId(req.getDomainId());
        List<DomainTagRelationEntity> insertList = req.getTagIds().stream().map(tagId -> {
            DomainTagRelationEntity entity = new DomainTagRelationEntity();
            entity.setDomainId(req.getDomainId());
            entity.setTagId(tagId);
            return entity;
        }).collect(Collectors.toList());
        //新增标签
        return ResultBuilder.success(domainTagRelationService.batchInsert(insertList));
    }

    /**
     * 查询广告位使用的域名列表
     *
     * @param slotId 广告位ID
     * @return 域名列表
     */
    private List<String> getDomainsBySlotId(Long slotId) {
        if (null == slotId) {
            return Collections.emptyList();
        }
        Slot slot = slotService.selectSimpleSlotById(slotId);
        if (null == slot) {
            return Collections.emptyList();
        }
        SlotDomainConfigBo domainConfig = slotConfigService.selectDomainConfigBySlotId(slotId);
        List<String> domains = new ArrayList<>();

        // 广告位域名
        if (null != domainConfig.getSlotDomain()) {
            domains.add(domainConfig.getSlotDomain());
        } else {
            domains.add(UrlUtils.extractDomain(slot.getSlotUrl()));
        }
        // 活动域名
        if (null != domainConfig.getActivityDomain()) {
            domains.add(domainConfig.getActivityDomain());
        } else {
            domains.add(UrlUtils.extractDomain(sysConfigService.selectConfigCacheByKey(SysConfigKeyEnum.DEFAULT_ACT_URL.getKey())));
        }
        // 落地页域名
        Optional.ofNullable(domainConfig.getLandpageDomain()).ifPresent(domains::add);
        return domains;
    }
}
