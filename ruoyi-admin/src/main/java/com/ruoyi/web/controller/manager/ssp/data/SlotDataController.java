package com.ruoyi.web.controller.manager.ssp.data;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.vo.datashow.SlotDataExportVO;
import com.ruoyi.system.vo.datashow.SlotDataMoreExportVO;
import com.ruoyi.system.vo.datashow.SlotDataVO;
import com.ruoyi.system.service.datasource.SlotDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.ruoyi.common.enums.slot.SlotDataVisibleEnum.VISIBLE;

/**
 * 广告位数据Controller
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@RestController
@RequestMapping("/datashow/slotData")
public class SlotDataController extends BaseController {

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private WhitelistService whitelistService;

    /**
     * 查询广告位数据列表
     */
    @GetMapping("/list")
    public TableDataInfo<SlotDataVO> list(SlotData slotData) {
        slotData.setIsVisible(VISIBLE.getStatus());
        List<SlotData> list = slotDataService.selectSlotDataList(slotData, false);

        boolean showSlotExposure = whitelistService.contains(WhitelistType.APP_SLOT_EXPOSURE, SecurityUtils.getLoginUser().getCrmAccountId());
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
        int hour = DateUtil.thisHour(true);
        return getDataTable(PageInfoUtils.dto2Vo(list, s -> {
            SlotDataVO slotDataVO = BeanUtil.copyProperties(s, SlotDataVO.class);
            // 是否展示媒体反馈的曝光数据
            if (showSlotExposure) {
                slotDataVO.setSlotClickRate(NumberUtils.calculatePercent(s.getAppSlotClickPv(), s.getAppSlotExposurePv()));
            } else {
                slotDataVO.setAppSlotExposurePv(null);
                slotDataVO.setAppSlotExposureUv(null);
                slotDataVO.setAppSlotClickPv(null);
                slotDataVO.setAppSlotClickUv(null);
            }

            // 媒体收益数据13:00之后可见
            if (hour < 13 && DateUtil.isSameDay(yesterday, s.getCurDate())) {
                slotDataVO.setAppRevenue(null);
            }
            return slotDataVO;
        }));
    }

    /**
     * 导出广告位数据列表
     */
    @Log(title = "广告位数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(SlotData slotData) {
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
        int hour = DateUtil.thisHour(true);
        slotData.setIsVisible(VISIBLE.getStatus());
        List<SlotData> list = slotDataService.selectSlotDataList(slotData, true);

        // 是否展示媒体反馈的曝光数据
        boolean showSlotExposure = whitelistService.contains(WhitelistType.APP_SLOT_EXPOSURE, SecurityUtils.getLoginUser().getCrmAccountId());
        if (showSlotExposure) {
            List<SlotDataMoreExportVO> exportList = new ArrayList<>(list.size());
            for (SlotData slot : list) {
                SlotDataMoreExportVO exportVO = new SlotDataMoreExportVO();
                BeanUtils.copyBeanProp(exportVO, slot);
                exportVO.setAppRevenue(NumberUtils.fenToYuan(slot.getAppRevenue(), "-"));
                // 媒体收益数据13:00之后可见
                if (hour < 13 && DateUtil.isSameDay(yesterday, slot.getCurDate())) {
                    exportVO.setAppRevenue("-");
                }

                exportVO.setAppName(String.format("%s（ID:%s）", slot.getAppName(), slot.getAppId()));
                exportVO.setSlotName(String.format("%s（ID:%s）", slot.getSlotName(), slot.getSlotId()));
                exportVO.setSlotClickRate(NumberUtils.calculatePercent(slot.getAppSlotClickPv(), slot.getAppSlotExposurePv()));
                exportList.add(exportVO);
            }

            ExcelUtil<SlotDataMoreExportVO> util = new ExcelUtil<>(SlotDataMoreExportVO.class);
            return util.exportExcel(exportList, "广告位数据");
        }

        List<SlotDataExportVO> exportList = new ArrayList<>(list.size());
        for (SlotData slot : list) {
            SlotDataExportVO exportVO = new SlotDataExportVO();
            BeanUtils.copyBeanProp(exportVO, slot);
            exportVO.setAppRevenue(NumberUtils.fenToYuan(slot.getAppRevenue(), "-"));
            // 媒体收益数据13:00之后可见
            if (hour < 13 && DateUtil.isSameDay(yesterday, slot.getCurDate())) {
                exportVO.setAppRevenue("-");
            }

            exportVO.setAppName(String.format("%s（ID:%s）", slot.getAppName(), slot.getAppId()));
            exportVO.setSlotName(String.format("%s（ID:%s）", slot.getSlotName(), slot.getSlotId()));
            exportList.add(exportVO);
        }

        ExcelUtil<SlotDataExportVO> util = new ExcelUtil<>(SlotDataExportVO.class);
        return util.exportExcel(exportList, "广告位数据");
    }
}
