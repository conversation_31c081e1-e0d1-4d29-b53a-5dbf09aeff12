package com.ruoyi.web.controller.engine;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.CountMonitorTypeEnum;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.enums.common.BizSwitchEnum;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkCacheBo;
import com.ruoyi.system.bo.landpage.article.ArticleCacheBo;
import com.ruoyi.system.bo.landpage.article.ArticleCheckBo;
import com.ruoyi.system.bo.landpage.article.ArticleCheckConfigBo;
import com.ruoyi.system.bo.landpage.article.ArticleDataBo;
import com.ruoyi.system.bo.landpage.article.ArticleDataParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleListBo;
import com.ruoyi.system.bo.landpage.article.ArticleListParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleRetConfigBo;
import com.ruoyi.system.bo.landpage.article.ArticleUrlSchemeBo;
import com.ruoyi.system.domain.adengine.SlotCacheDto;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkHourDataEntity;
import com.ruoyi.system.entity.landpage.article.ArticleCompetitorUrlEntity;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.entity.landpage.article.ArticleHourDataEntity;
import com.ruoyi.system.entity.landpage.article.ArticleRetDataEntity;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.req.landpage.article.ArticleCompetitorUrlReq;
import com.ruoyi.system.req.landpage.article.ArticleRetPageReq;
import com.ruoyi.system.req.landpage.article.ArticleUrlSchemeUpdateReq;
import com.ruoyi.system.service.common.BizConfigService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import com.ruoyi.system.service.fc.FcLinkService;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkHourDataService;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkService;
import com.ruoyi.system.service.landpage.article.ArticleApiService;
import com.ruoyi.system.service.landpage.article.ArticleCompetitorUrlService;
import com.ruoyi.system.service.landpage.article.ArticleHourDataService;
import com.ruoyi.system.service.landpage.article.ArticleRetDataService;
import com.ruoyi.system.service.landpage.article.ArticleService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.util.CountMonitorUtils;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.landpage.article.ArticleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATE_FORMAT;
import static com.ruoyi.common.constant.BizConstants.ARTICLE_RET_PAGE_DOMAIN;
import static com.ruoyi.common.constant.BizConstants.ARTICLE_RET_PAGE_DOMAIN_LIST;

/**
 * API对外接口
 *
 * <AUTHOR>
 * @date 2023/12/05
 */
@Slf4j
@Controller
@RequestMapping("/api/wz")
public class ArticleApiController {

    @Autowired
    private ArticleService articleService;

    @Autowired
    private ArticleAggrLinkService articleAggrLinkService;

    @Autowired
    private ArticleHourDataService articleHourDataService;

    @Autowired
    private ArticleAggrLinkHourDataService articleAggrLinkHourDataService;

    @Autowired
    private ArticleRetDataService articleRetDataService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private BizConfigService bizConfigService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private AppService appService;

    @Autowired
    private ArticleApiService articleApiService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private ArticleCompetitorUrlService articleCompetitorUrlService;
    @Autowired
    private FcLinkService fcLinkService;

    /**
     * 异步操作任务调度线程池
     */
    private final ScheduledExecutorService scheduledExecutor = SpringUtils.getBean("scheduledExecutorService");

    /**
     * 获取文章链接
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/{key}")
    public AjaxResult getArticle(@PathVariable("key") String key, HttpServletRequest request) {
        try {
            ArticleAggrLinkCacheBo link = getLinkCacheByKey(key);
            if (null == link || null == link.getId() || link.getId() < 1) {
                return AjaxResult.success();
            }
            // 获取用户ID
            String userId = getUserId(request);
            String dp = request.getParameter("dp");
            Long linkId = link.getId();
            // 根据权重和目标阅读量获取一篇文章
            ArticleCacheBo article = getArticleByLinkId(linkId, userId, link.getSySlot(), request);
            // 统计链接数据
            linkStatistic(linkId, userId);
            // 无文章可出时钉钉告警
            if (null == article) {
                linkWarning(linkId);
                // 设置了私域广告位的聚合链接不出兜底文章
                if (StringUtils.isNotBlank(link.getSySlot())) {
                    return AjaxResult.success();
                }
                if (Objects.equals(request.getParameter("nocache"), "1")) {
                    return AjaxResult.success();
                }
                // 兜底获取默认文章
                article = getDefaultArticle(linkId);
                if (null == article) {
                    return AjaxResult.success();
                }
            }
            String url = article.getUrl();
            // Url Scheme处理
            if (Objects.equals(dp, "1") && whitelistService.contains(WhitelistType.ARTICLE_SCHEME_LINK, link.getId())) {
                url = redisCache.getCacheObject(EngineRedisKeyFactory.K109.join(article.getId()));
            }
            // 链接处理
            if (StringUtils.isNotBlank(link.getSySlot()) && url.contains("#")) {
                url = StrUtil.subPre(url, url.indexOf("#"));
            }
            // 返回拦截页
            if (null != link.getRetConfigBo() && SwitchStatusEnum.isSwitchOn(link.getRetConfigBo().getRetSwitch())
                    && CollectionUtils.isNotEmpty(link.getRetConfigBo().getRetUrls())
                    && !Objects.equals(request.getParameter("ret"), "1")) {
                List<String> retUrls = link.getRetConfigBo().getRetUrls();
                Map<String, String> param = new HashMap<>();
                param.put("url", UrlUtils.urlEncode(url));
                for (int i = 0; i < retUrls.size(); i++) {
                    String retUrl = retUrls.get(i).trim();
                    if (retUrl.contains("/st/open")) {
                        retUrl = UrlUtils.appendParams(retUrl, MapUtil.of("ret", "1"));
                    }
                    param.put("url" + (i + 1), UrlUtils.urlEncode(retUrl));
                }
                String index = DateUtil.format(new Date(), PURE_DATE_FORMAT) + DateUtil.thisHour(true) + DateUtil.thisMinute() / 20;
                url = UrlUtils.appendParams(StrUtil.format("http://{}." + ARTICLE_RET_PAGE_DOMAIN + "/web-static/static-html/jump.html", index), param);
                // 统计返回拦截数据
                articleRetStatistic(linkId, userId, retUrls);
            }

            // 统计文章数据
            articleStatistic(linkId, article, userId, null);
            // 文章到达率监测
            articleArriveCheck(request, article.getId(), article.getUrl());
            // 构造返回值
            AjaxResult result = AjaxResult.success();
            result.put("url", url);
            return result;
        } catch (Exception e) {
            log.error("获取文章链接异常, key={}", key, e);
        }
        return AjaxResult.success();
    }

    /**
     * 获取文章链接(返回拦截页使用)
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/getByRetPage")
    public AjaxResult getByRetPage(ArticleRetPageReq req, HttpServletRequest request) {
        try {
            ArticleAggrLinkCacheBo link = getLinkCacheByKey(req.getKey());
            if (null == link || null == link.getId() || link.getId() < 1) {
                return AjaxResult.success();
            }
            // 返回拦截总次数
            int retTimes = null != link.getRetConfigBo() ? NumberUtils.defaultInt(link.getRetConfigBo().getRetTimes()) : 0;
            // 当前第几次返回拦截
            int curRetTimes = redisAtomicClient.incrBy(EngineRedisKeyFactory.K116.join(req.getRetNonce()), 1, 15, TimeUnit.MINUTES).intValue() - 1;
            // 超过返回拦截次数，返回空链接
            if (curRetTimes > retTimes) {
                return AjaxResult.success();
            }

            Long linkId = link.getId();
            // 获取用户ID
            String userId = getUserId(request);
            // 根据权重和目标阅读量获取一篇文章
            ArticleCacheBo article = getArticleByLinkId(linkId, userId, link.getSySlot(), request);
            // 统计链接数据
            linkStatistic(linkId, userId);
            // 无文章可出时钉钉告警
            if (null == article) {
                linkWarning(linkId);
                // 兜底获取默认文章(返回拦截不处理)
                if (curRetTimes < 1) {
                    article = getDefaultArticle(linkId);
                }
                if (null == article) {
                    return AjaxResult.success();
                }
            }
            // 统计文章数据
            articleStatistic(linkId, article, userId, null);
            // 文章到达率监测
            articleArriveCheck(request, article.getId(), article.getUrl());
            // 构造返回值
            AjaxResult result = AjaxResult.success();
            result.put("url", article.getUrl());
            return result;
        } catch (Exception e) {
            log.error("获取文章链接(返回拦截页)异常, key={}, retNonce={}", req.getKey(), req.getRetNonce(), e);
        }
        return AjaxResult.success();
    }

    /**
     * 文章全渠道监测开启
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/channelMonitorTurnOn")
    public AjaxResult channelMonitorTurnOn() {
        redisCache.setCacheObjectIfAbsent(EngineRedisKeyFactory.K125.join(DateUtil.formatDate(new Date())), "1", 5, TimeUnit.MINUTES);
        DingRobotUtil.sendText(DingWebhookConfig.getArticleAlert(), "文章到达率全渠道监测开启，监测周期为5分钟。");
        return AjaxResult.success();
    }

    /**
     * 文章域名切换
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/switchDomain")
    public AjaxResult switchDomain() {
        switchArticleRetPageDomain();
        return AjaxResult.success();
    }

    /**
     * 文章阅读API充值
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/apiRecharge")
    public AjaxResult apiRecharge(Integer amount) {
        if (null == amount || amount == 0) {
            return AjaxResult.success();
        }

        Long remainTimes = redisAtomicClient.incrBy(EngineRedisKeyFactory.K139.toString(), amount, 365, TimeUnit.DAYS);
        DingRobotUtil.sendText("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fec93c9a-7217-4863-9499-f3db4f7d4e04",
                StrUtil.format("文章阅读量接口充值\n\n日期: {}\n充值次数: {}\n剩余次数: {}", DateUtil.today(), amount, remainTimes));
        return AjaxResult.success();
    }

    /**
     * 同步文章信息
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/syncArticleProfile")
    public AjaxResult syncArticleProfile() {
        ArticleEntity article = articleService.selectArticleWithoutProfile();
        if (null != article) {
            articleService.updateArticleProfileAsync(article.getId(), article.getUrl());
            return AjaxResult.success(article.getId());
        }
        return AjaxResult.success();
    }

    /**
     * 获取需要更新scheme的文章列表
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/getArticleListForScheme")
    public AjaxResult getArticleListForScheme() {
        Set<Long> linkIds = new HashSet<>(whitelistService.list(WhitelistType.ARTICLE_SCHEME_LINK));
        if (CollectionUtils.isEmpty(linkIds)) {
            return AjaxResult.success();
        }
        ArticleListParamBo param = new ArticleListParamBo();
        param.setStartDate(DateUtil.beginOfDay(new Date()));
        List<ArticleListBo> articles = articleService.selectListWithData(param);
        if (CollectionUtils.isEmpty(articles)) {
            return AjaxResult.success();
        }
        Collections.shuffle(articles);
        return AjaxResult.success(
                articles.stream()
                        .filter(article -> Objects.equals(article.getOnline(), 1) && linkIds.contains(article.getLinkId()))
                        .map(article -> {
                            ArticleVO vo = new ArticleVO();
                            vo.setId(article.getId());
                            vo.setUrl(article.getUrl());
                            return vo;
                        }).collect(Collectors.toList())
        );
    }

    /**
     * 更新文章的scheme
     */
    @CrossOrigin
    @ResponseBody
    @PostMapping("/updateArticleScheme")
    public AjaxResult updateArticleScheme(@RequestBody ArticleUrlSchemeUpdateReq req) {
        if (CollectionUtils.isEmpty(req.getList())) {
            return AjaxResult.success();
        }
        for (ArticleUrlSchemeBo bo : req.getList()) {
            if (null != bo.getArticleId() && StringUtils.isNotBlank(bo.getUrlScheme())) {
                redisCache.setCacheObject(EngineRedisKeyFactory.K109.join(bo.getArticleId()), bo.getUrlScheme(), 8, TimeUnit.MINUTES);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 新增竞品文章链接
     */
    @CrossOrigin
    @ResponseBody
    @PostMapping("/addCompetitorUrl")
    public Result<Void> addCompetitorUrl(@RequestBody ArticleCompetitorUrlReq req) {
        if (StringUtils.isBlank(req.getUrl())) {
            return ResultBuilder.fail("链接不能为空");
        }
        if (!req.getUrl().contains("mp.weixin.qq.com/s")) {
            return ResultBuilder.fail("非公众号文章链接");
        }

        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K144.join(SecureUtil.md5(req.getUrl())), 864000);
        if (null == lock) {
            return ResultBuilder.fail("链接已存在");
        }

        ArticleCompetitorUrlEntity entity = new ArticleCompetitorUrlEntity();
        entity.setUrl(req.getUrl());
        entity.setSource(req.getSource());

        try {
            JSONObject profile = articleService.getArticleProfile(req.getUrl());
            entity.setNickname(profile.getString("nickname"));
            entity.setProvinceName(profile.getString("provinceName"));
            entity.setTitle(profile.getString("title"));
            entity.setCreateTime(DateUtils.parseDate(profile.getString("createTime")));
            if (null == entity.getCreateTime() && null != profile.getLong("ct")) {
                entity.setCreateTime(new Date(profile.getLong("ct") * 1000));
            }
        } catch (Exception e) {
            log.error("addCompetitorUrl.getArticleProfile error, url={}", req.getUrl());
        }

//        try {
//            if (null != req.getCheckRead()) {
//                Integer read = articleApiService.getArticleRealRequestPv(req.getUrl());
//                if (read > 0) {
//                    entity.setRead(read);
//                }
//            }
//        } catch (Exception e) {
//            log.error("addCompetitorUrl.getArticleRealRequestPv error, url={}", req.getUrl());
//        }
        articleCompetitorUrlService.insert(entity);
        return ResultBuilder.success();
    }

    /**
     * 根据链接标识获取链接
     *
     * @param key 链接标识
     * @return 链接ID
     */
    private ArticleAggrLinkCacheBo getLinkCacheByKey(String key) {
        if (StringUtils.isBlank(key) || key.length() > 8) {
            return null;
        }
        try {
            // 可以增加本地缓存优化
            String redisKey = EngineRedisKeyFactory.K115.join(key);
            String value = redisCache.getCacheObject(redisKey);
            if (StringUtils.isNotBlank(value)) {
                return JSON.parseObject(value, ArticleAggrLinkCacheBo.class);
            }

            ArticleAggrLinkEntity link = articleAggrLinkService.selectByKey(key);
            if (null == link) {
                redisCache.setCacheObject(redisKey, "", 5, TimeUnit.MINUTES);
                return null;
            }
            ArticleAggrLinkCacheBo linkCache = new ArticleAggrLinkCacheBo();
            linkCache.setId(link.getId());
            linkCache.setRetConfigBo(JSON.parseObject(link.getRetConfig(), ArticleRetConfigBo.class));
            linkCache.setSySlot(link.getSySlot());
            redisCache.setCacheObject(redisKey, JSON.toJSONString(linkCache), 1, TimeUnit.DAYS);
            return linkCache;
        } catch (Exception e) {
            log.error("getLinkCacheByKey error, key={}", key, e);
        }
        return null;
    }

    /**
     * 根据链接获取文章
     *
     * @param linkId 链接ID
     * @param userId 用户ID
     * @return 文章
     */
    private ArticleCacheBo getArticleByLinkId(Long linkId, String userId, String sySlot, HttpServletRequest request) {
        if (null == linkId || linkId < 1) {
            return null;
        }
        String today = DateUtil.today();
        String redisKey = EngineRedisKeyFactory.K110.join(today, linkId);
        Map<String, ArticleCacheBo> articleMap = redisCache.getCacheMap(redisKey);
        if (MapUtil.isEmpty(articleMap) && !redisCache.hasKey(redisKey)) {
            // 重新构造缓存
            articleMap = initArticleCache(today, linkId);
        }
        if (MapUtil.isEmpty(articleMap)) {
            return null;
        }

        // 全渠道监测
        if (isChannelMonitorSwitchOn(today)) {
            Long appId = getAppId(request);
            if (null != appId) {
                // 获取所有已绑定的媒体和文章
                Map<String, String> appArticleMap = redisCache.getCacheMap(EngineRedisKeyFactory.K126.join(today));
                // 媒体已绑定
                if (appArticleMap.containsKey(String.valueOf(appId))) {
                    // 获取其他已绑定链接并剔除
                    appArticleMap.remove(String.valueOf(appId));
                    for (String boundArticle : appArticleMap.values()) {
                        ArticleCheckBo checkBo = JSON.parseObject(boundArticle, ArticleCheckBo.class);
                        articleMap.remove(String.valueOf(checkBo.getArticleId()));
                    }
                } else {
                    // 媒体未绑定
                    for (String boundArticle : appArticleMap.values()) {
                        ArticleCheckBo checkBo = JSON.parseObject(boundArticle, ArticleCheckBo.class);
                        articleMap.remove(String.valueOf(checkBo.getArticleId()));
                    }
                    // 随机获取文章
                    ArticleCacheBo boundArticle = articleMap.values().stream().findAny().orElse(null);
                    // 绑定媒体和文章
                    bindAppAndArticle(today, appId, boundArticle);
                }
                if (MapUtil.isEmpty(articleMap)) {
                    return null;
                }
            }
        } else {
            // 钉钉群发送监测报告
            channelMonitorReport(today);
        }

        // 过滤重复曝光的文章
        Set<Long> exposureArticleIds = redisCache.getCacheSet(EngineRedisKeyFactory.K113.join(today, userId));
        if (CollectionUtils.isNotEmpty(exposureArticleIds)) {
            List<ArticleCacheBo> tmpArticleList = articleMap.values().stream()
                    .filter(article -> !exposureArticleIds.contains(article.getId()))
                    .collect(Collectors.toList());
            // 随机获取未曝光文章
            if (CollectionUtils.isNotEmpty(tmpArticleList) && tmpArticleList.size() < articleMap.values().size()) {
                return getRandomArticle(tmpArticleList);
            }
            // 私域广告位不重复出文章
            if (CollectionUtils.isEmpty(tmpArticleList) && StringUtils.isNotBlank(sySlot)) {
                return null;
            }
        }
        // 随机获取文章
        return getRandomArticle(articleMap.values());
    }

    /**
     * 链接数据统计
     */
    public void linkStatistic(Long linkId, String userId) {
        try {
            String today = DateUtil.today();
            Date date = DateUtil.beginOfDay(new Date());
            final int hour = DateUtil.thisHour(true);
            GlobalThreadPool.statExecutorService.execute(() -> {
                // 更新链接数据
                Integer linkUv = BizUtils.countUv(EngineRedisKeyFactory.K012.join("ArticleAggrLinkHourDataEntity", today, hour, linkId), userId, 1, TimeUnit.HOURS);
                articleAggrLinkHourDataService.update(getArticleAggrLinkDataId(today, date, hour, linkId), 1, linkUv);
            });
        } catch (Exception e) {
            log.error("linkStatistic error, linkId={}", linkId, e);
        }
    }

    /**
     * 文章数据统计
     */
    public void articleStatistic(Long linkId, ArticleCacheBo article, String userId, String fcLinkKey) {
        String today = DateUtil.today();
        Date date = DateUtil.beginOfDay(new Date());
        final int hour = DateUtil.thisHour(true);
        Long articleId = article.getId();
        Integer targetRequestPv = article.getTargetRequestPv();
        Integer compensateRequestPv = NumberUtils.defaultInt(article.getCompensateRequestPv());
        String url = article.getUrl();

        boolean isArticleFinished = false;
        try {
            // 更新文章请求PV缓存
            String redisKey = EngineRedisKeyFactory.K111.join(today, linkId);
            Long requestPv = redisCache.incrCacheMapValue(redisKey, String.valueOf(articleId), 1);
            redisCache.expire(redisKey, 1, TimeUnit.DAYS);
            // 文章阅读量达标移除缓存
            if (null != requestPv && requestPv >= targetRequestPv + compensateRequestPv) {
                // 移除缓存
                redisCache.deleteObject(EngineRedisKeyFactory.K110.join(today, linkId));
                // 如果丰巢链接有该文章则需要删除丰巢链接的文章缓存
                if ( !StringUtils.isEmpty(fcLinkKey) ) {
                    Map<String, ArticleCacheBo> articleMap = redisCache.getCacheMap(EngineRedisKeyFactory.k149.join(today, fcLinkKey));
                    if (MapUtil.isNotEmpty(articleMap) && articleMap.containsKey(String.valueOf(articleId))) {
                        fcLinkService.deleteFcLinkArticleCache(fcLinkKey);
                    }
                }
                isArticleFinished = true;
            }
            // 缓存用户已曝光文章
            cacheUserExposureArticle(today, userId, articleId);
        } catch (Exception e) {
            log.error("更新文章缓存异常, linkId={}, article={}", linkId, JSON.toJSONString(article), e);
        }
        // 阅读量达标
        if (isArticleFinished) {
            // 延迟3分钟处理
            int delay = DateUtil.hour(new Date(), true) < 23 ? 3 : 0;
            scheduledExecutor.schedule(() -> {
                GlobalThreadPool.longTimeExecutorService.execute(() -> {
                    // 查询真实阅读量并保存
                    getArticleActualRequestPvAndSave(articleId, targetRequestPv + compensateRequestPv, url);
                    // 钉钉告警
                    articleWarning(linkId, articleId);
                    // 自动补量
                    autoCompensateRequest(articleId);
                });
            }, delay, TimeUnit.MINUTES);
        }
        GlobalThreadPool.statExecutorService.execute(() -> {
            // 更新文章数据
            Integer articleUv = BizUtils.countUv(EngineRedisKeyFactory.K012.join("ArticleHourDataEntity", today, hour, articleId), userId, 1, TimeUnit.HOURS);
            articleHourDataService.update(getArticleDataId(today, date, hour, linkId, articleId), 1, articleUv);
        });
        GlobalThreadPool.longTimeExecutorService.execute(() -> {
            // 查询文章初始阅读量
            getArticleInitRequestPvAndSave(articleId);
        });
    }

    /**
     * 通过缓存获取链接数据ID
     */
    private Long getArticleAggrLinkDataId(String dateStr, Date date, Integer hour, Long linkId) {
        String key = EngineRedisKeyFactory.K022.join("ArticleAggrLinkHourDataEntity", dateStr, hour, linkId);
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        ArticleAggrLinkHourDataEntity data = articleAggrLinkHourDataService.selectBy(date, hour, linkId);
        if (null == data) {
            data = new ArticleAggrLinkHourDataEntity();
            data.setCurDate(date);
            data.setCurHour(hour);
            data.setLinkId(linkId);
            articleAggrLinkHourDataService.insert(data);
            data = articleAggrLinkHourDataService.selectBy(date, hour, linkId);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.HOURS);
        return data.getId();
    }

    /**
     * 通过缓存获取链接数据ID
     */
    private Long getArticleDataId(String dateStr, Date date, Integer hour, Long linkId, Long articleId) {
        String key = EngineRedisKeyFactory.K022.join("ArticleHourDataEntity", dateStr, hour, articleId);
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        ArticleHourDataEntity data = articleHourDataService.selectBy(date, hour, linkId, articleId);
        if (null == data) {
            data = new ArticleHourDataEntity();
            data.setCurDate(date);
            data.setCurHour(hour);
            data.setLinkId(linkId);
            data.setArticleId(articleId);
            articleHourDataService.insert(data);
            data = articleHourDataService.selectBy(date, hour, linkId, articleId);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.HOURS);
        return data.getId();
    }

    /**
     * 链接告警
     */
    private void linkWarning(Long linkId) {
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K012.join("ArticleApiController.linkWarning", linkId), 300);
        if (null == lock) {
            return;
        }
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(linkId);
        if (null == link) {
            return;
        }
        DingRobotUtil.sendText(DingWebhookConfig.getArticleOperateAlert(),
                "文章聚合链接无可投文章\n" +
                        "\nAPI名称: " + link.getName() +
                        "\nAPI链接: " + link.getUrl());
    }

    /**
     * 文章告警
     */
    private void articleWarning(Long linkId, Long articleId) {
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K012.join("ArticleApiController.articleWarning", articleId), 300);
        if (null == lock) {
            return;
        }
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(linkId);
        ArticleEntity article = articleService.selectById(articleId);

        String content = "文章达到目标阅读量\n";
        if (null != link) {
            content += "\nAPI名称: " + link.getName() +
                    "\nAPI链接: " + link.getUrl();
        }
        if (null != article) {
            content += "\n文章名称: " + article.getName() +
                    "\n文章链接: " + article.getUrl();
            if (null != article.getTargetRequestPv()) {
                content += "\n目标阅读量: " + article.getTargetRequestPv();
            }
            if (null != article.getCompensateRequestPv() && article.getCompensateRequestPv() > 0) {
                content += "\n补量: " + article.getCompensateRequestPv();
            }
            if (null != article.getInitRequestPv() && null != article.getActualRequestPv()) {
                int actualIncrRequestPv = article.getActualRequestPv() - article.getInitRequestPv();
                content += "\n实际增加阅读量: " + actualIncrRequestPv;
                String arriveRate = NumberUtils.calculatePercent(actualIncrRequestPv, article.getTargetRequestPv());
                if (null != arriveRate) {
                    content += "\n到达率: " + arriveRate;
                }
            }
        }
        List<ArticleListBo> articles = articleService.selectListWithData(new ArticleListParamBo(linkId));
        if (CollectionUtils.isNotEmpty(articles)) {
            List<Long> remainArticleIds = new ArrayList<>();
            int remainRequestPvSum = 0;
            for (ArticleListBo a : articles) {
                if (Objects.equals(a.getOnline(), 1)) {
                    remainArticleIds.add(a.getId());
                    remainRequestPvSum += a.getTargetRequestPv() + a.getCompensateRequestPv() - a.getRequestPv();
                }
            }
            content += "\nAPI剩余文章数: " + remainArticleIds.size() +
                    "\nAPI剩余阅读量: " + remainRequestPvSum;
        }
        DingRobotUtil.sendText(DingWebhookConfig.getArticleAlert(), content);
    }

    /**
     * 随机获取文章
     *
     * @param articleList 文章列表
     * @return 文章
     */
    private ArticleCacheBo getRandomArticle(Collection<ArticleCacheBo> articleList) {
        int weightSum = articleList.stream().mapToInt(ArticleCacheBo::getWeight).sum();
        int nonce = RandomUtil.randomInt(weightSum);
        for (ArticleCacheBo article : articleList) {
            if (nonce < article.getWeight()) {
                return article;
            }
            nonce -= article.getWeight();
        }
        return null;
    }

    /**
     * 获取默认文章(目标阅读量-请求UV最大的文章)
     *
     * @param linkId 链接ID
     * @return 文章
     */
    private ArticleCacheBo getDefaultArticle(Long linkId) {
        String redisKey = EngineRedisKeyFactory.K114.join(linkId);
        String value = redisCache.getCacheObject(redisKey);
        if (StringUtils.isNotBlank(value)) {
            return JSON.parseObject(value, ArticleCacheBo.class);
        }
        List<ArticleListBo> articleList = articleService.selectListWithData(new ArticleListParamBo(linkId));
        if (CollectionUtils.isEmpty(articleList)) {
            return null;
        }
        Date today = DateUtil.beginOfDay(new Date());
        ArticleCacheBo article = articleList.stream()
                .filter(s -> !s.getGmtCreate().before(today))
                .max(Comparator.comparingInt(o -> o.getTargetRequestPv() + o.getCompensateRequestPv() - o.getRequestUv()))
                .map(s -> BeanUtil.copyProperties(s, ArticleCacheBo.class)).orElse(null);
        if (null != article) {
            redisCache.setCacheObject(redisKey, JSON.toJSONString(article), 5, TimeUnit.MINUTES);
        }
        return article;
    }

    /**
     * 初始化文章缓存
     *
     * @param today 日期
     * @param linkId 文章聚合链接ID
     * @return 可投文章
     */
    public Map<String, ArticleCacheBo> initArticleCache(String today, Long linkId) {
        // 查询文章列表
        List<ArticleEntity> articles = articleService.selectListByLinkId(linkId);
        if (CollectionUtils.isEmpty(articles)) {
            return null;
        }
        // 构造文章缓存
        Map<String, Integer> articleRequestMap = MapUtil.defaultIfEmpty(redisCache.getCacheMap(EngineRedisKeyFactory.K111.join(today, linkId)), Collections.emptyMap());
        Map<String, ArticleCacheBo> articleMap = articles.stream()
                .filter(article -> {
                    if (!article.isOnline()) {
                        return false;
                    }
                    Integer requestPv = articleRequestMap.getOrDefault(String.valueOf(article.getId()), 0);
                    return requestPv < article.getTargetRequestPv() + NumberUtils.defaultInt(article.getCompensateRequestPv());
                })
                .collect(Collectors.toMap(article -> String.valueOf(article.getId()), ArticleEntity::getCacheBo, (o, n) -> n));
        String redisKey = EngineRedisKeyFactory.K110.join(today, linkId);
        redisCache.setCacheMap(redisKey, articleMap);
        redisCache.expire(redisKey, 10, TimeUnit.MINUTES);
        return articleMap;
    }

    /**
     * 获取用户ID
     * 获取链接参数userId，否则生成MD5(ip + ua)
     *
     * @return 用户ID
     */
    private String getUserId(HttpServletRequest request) {
        String userId = request.getParameter("userId");
        if (StringUtils.isNotBlank(userId) && !Objects.equals(userId, "__N_DEVICE__")) {
            return userId;
        }
        String ip = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        return Md5Utils.hash(ip + userAgent);
    }

    /**
     * 缓存用户已曝光文章
     *
     * @param today 日期
     * @param userId 用户ID
     * @param articleId 文章ID
     */
    private void cacheUserExposureArticle(String today, String userId, Long articleId) {
        String exposureRedisKey = EngineRedisKeyFactory.K113.join(today, userId);
        redisCache.addCacheSet(exposureRedisKey, articleId);
        redisCache.expire(exposureRedisKey, 1, TimeUnit.DAYS);
    }

    /**
     * 文章到达率监测
     *
     * @param articleId 文章ID
     * @param url 文章链接
     */
    private void articleArriveCheck(HttpServletRequest request, Long articleId, String url) {
        String sid = request.getParameter("sid");
        GlobalThreadPool.longTimeExecutorService.submit(() -> {
            try {
                long timestamp = System.currentTimeMillis();
                Date today = DateUtil.beginOfDay(new Date());
                String dateStr = DateUtil.formatDate(today);

                // 监测配置
                int timeThreshold = 10; // 监测间隔(分钟)
                int pvThreshold = 100; // 监测增量阈值
                int arriveThreshold = 80; // 监测到达率阈值
                ArticleCheckConfigBo config = JSON.parseObject(bizConfigService.getValue(BizConfigEnum.ARTICLE_CHECK_THRESHOLD, String.class), ArticleCheckConfigBo.class);
                if (null != config) {
                    timeThreshold = NumberUtils.defaultInt(config.getTimeThreshold(), 10);
                    pvThreshold = NumberUtils.defaultInt(config.getPvThreshold(), 100);
                    arriveThreshold = NumberUtils.defaultInt(config.getArriveThreshold(), 80);
                }

                // 通过分布式锁控制监测周期
                RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K123.join(dateStr), 60L * timeThreshold + 5);
                if (lock == null) {
                    return;
                }

                // 查询文章信息
                String value = redisCache.getCacheObject(EngineRedisKeyFactory.K124.join(dateStr));
                if (StringUtils.isNotBlank(value)) {
                    // 查询到文章
                    ArticleCheckBo checkBo = JSON.parseObject(value, ArticleCheckBo.class);
                    if (null != checkBo) {
                        // 不满足监测间隔，将锁设置为60秒
                        if (null != checkBo.getTimestamp() && timestamp - checkBo.getTimestamp() < timeThreshold * 60000L) {
                            redisCache.expire(EngineRedisKeyFactory.K124.join(dateStr), 60);
                            return;
                        }
                        // 查询文章当前阅读量，判断是否满足监测数量要求
                        Integer curNhPv = getArticleRequestPv(checkBo.getArticleId(), today);
                        // 阅读量达到监测阈值
                        if (curNhPv - checkBo.getNhPv() >= pvThreshold) {
                            // 调用接口查询阅读量增量，判断到达率
                            int nhPvIncr = curNhPv - checkBo.getNhPv();
                            Integer curRealPv = articleApiService.getArticleRealRequestPv(checkBo.getUrl());
                            int realPvIncr = curRealPv - checkBo.getRealPv();
                            int realArrive = realPvIncr * 100 / nhPvIncr;
                            log.info("文章阅读量监测, 文章ID={}, 诺禾阅读量={}, 实际阅读量={}, 诺禾阅读量增量={}, 实际阅读量增量={}, 到达率={}%",
                                    checkBo.getArticleId(), curNhPv, curRealPv, nhPvIncr, realPvIncr, realArrive);
                            if (realArrive <= arriveThreshold) {
                                // 发送钉钉告警
                                ArticleEntity article = articleService.selectById(checkBo.getArticleId());
                                ArticleAggrLinkEntity link = articleAggrLinkService.selectById(article.getLinkId());

                                String content = "文章到达率告警\n";
                                if (StrUtil.isNumeric(sid)) {
                                    Slot slot = slotService.selectSimpleSlotById(Long.valueOf(sid));
                                    if (null != slot) {
                                        content += "\n媒体ID: " + slot.getAppId()
                                                + "\n广告位ID: " + slot.getId()
                                                + "\n广告位名称: " + slot.getSlotName();
                                    }
                                }

                                content += "\n聚合链接: " + link.getName() +
                                        "\n文章名称: " + article.getName() +
                                        "\n文章链接: " + article.getUrl() +
                                        "\n空白页域名: " + ARTICLE_RET_PAGE_DOMAIN +
                                        "\n链接访问量增加: " + nhPvIncr +
                                        "\n实际阅读量增加: " + realPvIncr +
                                        "\n到达率: " + realArrive + "%";

                                if (SpringEnvironmentUtils.isProd()) {
                                    content += "\n全渠道监测: http://api.actlist.cn/api/wz/channelMonitorTurnOn";
                                    content += "\n域名切换: http://api.actlist.cn/api/wz/switchDomain";
                                } else {
                                    content += "\n全渠道监测: http://actenginetest.ydns.cn/api/wz/channelMonitorTurnOn";
                                    content += "\n域名切换: http://actenginetest.ydns.cn/api/wz/switchDomain";
                                }

                                DingRobotUtil.sendText(DingWebhookConfig.getArticleAlert(), content);

                                // 到达率低于60%切换空白页域名
                                if (realArrive < 60) {
                                    switchArticleRetPageDomain();
                                }
                            }
                        }
                    }
                    // 清除文章信息
                    redisCache.deleteObject(EngineRedisKeyFactory.K124.join(dateStr));
                    // 解锁
                    lock.unlock();
                    return;
                }
                // 未查询到文章，新增当前文章信息
                ArticleCheckBo checkBo = new ArticleCheckBo();
                checkBo.setArticleId(articleId);
                checkBo.setUrl(url);
                checkBo.setTimestamp(timestamp);
                checkBo.setNhPv(getArticleRequestPv(articleId, today));
                checkBo.setRealPv(articleApiService.getArticleRealRequestPv(url));
                redisCache.setCacheObject(EngineRedisKeyFactory.K124.join(dateStr), JSON.toJSONString(checkBo), 1, TimeUnit.DAYS);
            } catch (Exception e) {
                log.error("articleArriveCheck error, articleId={}", articleId, e);
                if (StrUtil.containsAny(e.getMessage(), "余额不足", "金额不足")) {
                    CountMonitorUtils.errorMonitor(CountMonitorTypeEnum.ARTICLE_API, DingWebhookConfig.getCrmBiz(), "");
                }
            }
        });
    }

    /**
     * 查询文章阅读量
     */
    private Integer getArticleRequestPv(Long articleId, Date date) {
        if (null == articleId || null == date) {
            return 0;
        }
        ArticleDataParamBo param = new ArticleDataParamBo();
        param.setArticleIds(Collections.singletonList(articleId));
        param.setStartDate(date);
        param.setEndDate(date);
        ArticleDataBo data = articleHourDataService.selectSumBy(param);
        return null != data ? data.getRequestPv() : 0;
    }

    /**
     * 全渠道监测开关是否开启
     */
    private boolean isChannelMonitorSwitchOn(String today) {
        return null != redisCache.getCacheObject(EngineRedisKeyFactory.K125.join(today));
    }

    /**
     * 获取媒体ID
     */
    private Long getAppId(HttpServletRequest request) {
        try {
            String sid = request.getParameter("sid");
            if (StringUtils.isNumeric(sid)) {
                SlotCacheDto slot = slotCacheService.getSlotCache(Long.valueOf(sid));
                return null != slot ? slot.getAppId() : null;
            }
        } catch (Exception e) {
            log.error("getAppId error", e);
        }
        return null;
    }

    /**
     * 钉钉群发送监测报告
     */
    private void channelMonitorReport(String today) {
        if (!redisCache.hasKey(EngineRedisKeyFactory.K126.join(today))) {
            return;
        }
        GlobalThreadPool.longTimeExecutorService.submit(() -> {
            if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K128.join(today), 60)) {
                return;
            }

            Map<String, String> appArticleMap = redisCache.getCacheMap(EngineRedisKeyFactory.K126.join(today));
            if (MapUtil.isNotEmpty(appArticleMap)) {
                StringBuilder content = new StringBuilder("文章到达率全渠道监测");
                boolean alertFlag = false;

                for (Map.Entry<String, String> appArticle : appArticleMap.entrySet()) {
                    ArticleCheckBo checkBo = JSON.parseObject(appArticle.getValue(), ArticleCheckBo.class);
                    App app = appService.selectAppById(Long.valueOf(appArticle.getKey()));
                    Integer curNhPv = getArticleRequestPv(checkBo.getArticleId(), DateUtil.beginOfDay(new Date()));
                    int nhPvIncr = curNhPv - checkBo.getNhPv();
                    if (nhPvIncr >= 10) {
                        Integer curRealPv = articleApiService.getArticleRealRequestPv(checkBo.getUrl());
                        int realPvIncr = curRealPv - checkBo.getRealPv();
                        int realArrive = realPvIncr * 100 / nhPvIncr;

                        content.append("\n\n媒体ID: ").append(appArticle.getKey())
                                .append("\n媒体名称: ").append(app.getAppName())
                                .append("\n到达率: ").append(realArrive).append("%");
                        alertFlag = true;
                    }
                }
                if (alertFlag) {
                    DingRobotUtil.sendText(DingWebhookConfig.getArticleAlert(), content.toString());
                }
            }
            redisCache.deleteObject(EngineRedisKeyFactory.K126.join(today));
        });
    }

    /**
     * 绑定媒体和文章
     */
    private void bindAppAndArticle(String today, Long appId, ArticleCacheBo article) {
        if (null == article) {
            return;
        }
        GlobalThreadPool.longTimeExecutorService.submit(() -> {
            if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K127.join(today, article.getId()), 30)) {
                return;
            }
            // 查询文章初始阅读量
            ArticleCheckBo checkBo = new ArticleCheckBo();
            checkBo.setArticleId(article.getId());
            checkBo.setUrl(article.getUrl());
            checkBo.setTimestamp(System.currentTimeMillis());
            checkBo.setNhPv(getArticleRequestPv(article.getId(), DateUtil.beginOfDay(new Date())));
            checkBo.setRealPv(articleApiService.getArticleRealRequestPv(article.getUrl()));

            // 成功获取链接并加锁
            redisCache.setCacheMapValue(EngineRedisKeyFactory.K126.join(today), String.valueOf(appId), JSON.toJSONString(checkBo));
            redisCache.expire(EngineRedisKeyFactory.K126.join(today), 1, TimeUnit.HOURS);
        });
    }

    /**
     * 查询文章真实阅读量并保存
     */
    private void getArticleActualRequestPvAndSave(Long articleId, Integer targetRequestPv, String url) {
        try {
            // 同一篇文章，不修改目标阅读量的情况下只查询一次
            RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K129.join(articleId, targetRequestPv), 864000);
            if (null == lock) {
                return;
            }
            // 调用接口查询实际阅读量
            Integer actualRequestPv = articleApiService.getArticleRealRequestPv(url);
            articleService.updateActualRequestPv(articleId, actualRequestPv);
        } catch (Exception e) {
            log.error("查询文章真实阅读量并保存异常, articleId={}, targetRequestPv={}, url={}", articleId, targetRequestPv, url, e);
        }
    }

    /**
     * 查询文章初始阅读量并保存
     */
    private void getArticleInitRequestPvAndSave(Long articleId) {
        try {
            // 同一篇文章只查询一次(锁30天)
            RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K130.join(articleId), 2592000);
            if (null == lock) {
                return;
            }
            // 如果设置了初始阅读量就不查询
            ArticleEntity article = articleService.selectById(articleId);
            if (null == article || null != article.getInitRequestPv() && article.getInitRequestPv() > 0) {
                return;
            }
            // 当日阅读量>20的不查询
            Integer requestPv = redisCache.getCacheMapValue(EngineRedisKeyFactory.K111.join(DateUtil.today(), article.getLinkId()), String.valueOf(articleId));
            if (null != requestPv && requestPv > 20) {
                return;
            }

            // 调用接口查询初始阅读量并保存
            Integer initRequestPv = articleApiService.getArticleRealRequestPv(article.getUrl());
            if (initRequestPv > 0) {
                ArticleEntity updateArticle = new ArticleEntity();
                updateArticle.setId(articleId);
                updateArticle.setInitRequestPv(initRequestPv);
                articleService.updateById(updateArticle);
            }
        } catch (Exception e) {
            log.error("查询文章初始阅读量并保存异常, articleId={}", articleId, e);
        }
    }

    /**
     * 文章返回拦截数据统计
     */
    private void articleRetStatistic(Long linkId, String userId, List<String> retUrls) {
        String today = DateUtil.today();
        Date date = DateUtil.beginOfDay(new Date());

        GlobalThreadPool.statExecutorService.execute(() -> {
            for (String retUrl : retUrls) {
                String retUrlMd5 = Md5Utils.hash(retUrl.trim());
                Integer uv = BizUtils.countUv(EngineRedisKeyFactory.K012.join("ArticleHourDataEntity", today, linkId, retUrlMd5), userId, 1, TimeUnit.DAYS);
                articleRetDataService.update(getArticleDataId(today, date, linkId, retUrl, retUrlMd5), 1, uv);
            }
        });
    }

    /**
     * 通过缓存获取返回拦截链接数据ID
     */
    private Long getArticleDataId(String dateStr, Date date, Long linkId, String retUrl, String retUrlMd5) {
        String key = EngineRedisKeyFactory.K022.join("ArticleRetDataEntity", dateStr, linkId, retUrlMd5);
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        ArticleRetDataEntity data = articleRetDataService.selectBy(date, linkId, retUrlMd5);
        if (null == data) {
            data = new ArticleRetDataEntity();
            data.setCurDate(date);
            data.setLinkId(linkId);
            data.setRetUrlMd5(retUrlMd5);
            data.setRetUrl(retUrl);
            articleRetDataService.insert(data);
            data = articleRetDataService.selectBy(date, linkId, retUrlMd5);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
        return data.getId();
    }

    /**
     * 切换文章空白页域名
     */
    private void switchArticleRetPageDomain() {
        try {
            String key = EngineRedisKeyFactory.K135.toString();
            int value = NumberUtils.defaultLong(redisAtomicClient.getLong(key)).intValue();
            if (!Objects.equals(ARTICLE_RET_PAGE_DOMAIN, ARTICLE_RET_PAGE_DOMAIN_LIST.get(value % ARTICLE_RET_PAGE_DOMAIN_LIST.size()))) {
                ARTICLE_RET_PAGE_DOMAIN = ARTICLE_RET_PAGE_DOMAIN_LIST.get(value % ARTICLE_RET_PAGE_DOMAIN_LIST.size());
                return;
            }
            String originDomain = ARTICLE_RET_PAGE_DOMAIN;
            value = redisAtomicClient.incrBy(key, 1, 365, TimeUnit.DAYS).intValue();
            ARTICLE_RET_PAGE_DOMAIN = ARTICLE_RET_PAGE_DOMAIN_LIST.get(value % ARTICLE_RET_PAGE_DOMAIN_LIST.size());
            DingRobotUtil.sendText(DingWebhookConfig.getArticleAlert(), "空白页域名切换\n\n" + originDomain + " → " + ARTICLE_RET_PAGE_DOMAIN);
        } catch (Exception e) {
            log.error("切换空白页域名异常", e);
        }
    }

    /**
     * 自动补量计算
     */
    private void autoCompensateRequest(Long articleId) {
        if (redisCache.hasKey(EngineRedisKeyFactory.K015.join(BizSwitchEnum.ARTICLE_COMPENSATE.getKey()))) {
            return;
        }
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K067.join("autoCompensateRequest", articleId), 5);
        if (lock == null) {
            return;
        }

        // 判断是否需要补量
        ArticleEntity article = articleService.selectById(articleId);
        if (null == article || null == article.getActualRequestPv() || null == article.getInitRequestPv()) {
            return;
        }
        int targetRequestPv = article.getTargetRequestPv();
        if (article.getActualRequestPv() - article.getInitRequestPv() >= targetRequestPv) {
            return;
        }

        // 判断补量次数(上限为两次)
        long times = redisAtomicClient.incrBy(EngineRedisKeyFactory.K143.join(articleId, DateUtil.today()), 1, 1, TimeUnit.DAYS);
        if (times > 2) {
            return;
        }

        // 查询当前请求数
        Map<String, Integer> articleRequestMap = MapUtil.defaultIfEmpty(redisCache.getCacheMap(EngineRedisKeyFactory.K111.join(DateUtil.today(), article.getLinkId())), Collections.emptyMap());
        Integer requestPv = articleRequestMap.getOrDefault(String.valueOf(article.getId()), 0);

        // 当前剩下多少阅读量能跑
        int remainRequestPv = Math.max(article.getTargetRequestPv() + article.getCompensateRequestPv() - requestPv, 0);

        // 实际需要多少真实阅读量=目标阅读量-(当前真实阅读量-初始阅读量)
        int needActualRequestPv = targetRequestPv - (article.getActualRequestPv() - article.getInitRequestPv());

        // 计算补量
        int compensateRequestPv = Math.min((int) (needActualRequestPv * 0.9) - remainRequestPv, (int) (targetRequestPv * 0.3));
        log.info("自动补量计算, articleId={}, targetRequestPv={}, requestPv={}, remainRequestPv={}, needActualRequestPv={}",
                articleId, targetRequestPv, requestPv, remainRequestPv, needActualRequestPv);
        if (compensateRequestPv > 0) {
            ArticleEntity updateArticle = new ArticleEntity();
            updateArticle.setId(articleId);
            updateArticle.setCompensateRequestPv(NumberUtils.defaultInt(article.getCompensateRequestPv()) + compensateRequestPv);
            articleService.updateById(updateArticle);

            redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), article.getLinkId()));

            DingRobotUtil.sendText(DingWebhookConfig.getArticleOperateAlert(),
                    "文章自动补量\n" +
                            "\n链接ID: " + article.getLinkId() +
                            "\n文章ID: " + article.getId() +
                            "\n文章名称: " + article.getName() +
                            "\n文章链接: " + article.getUrl() +
                            "\n目标阅读量: " + article.getTargetRequestPv() +
                            "\n实际增加阅读量: " + (article.getActualRequestPv() - article.getInitRequestPv()) +
                            "\n本次计划补量: " + compensateRequestPv +
                            "\n自动补量次数: " + times);
        }
    }
}
