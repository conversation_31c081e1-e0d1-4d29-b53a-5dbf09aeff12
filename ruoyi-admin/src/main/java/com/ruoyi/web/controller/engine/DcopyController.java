package com.ruoyi.web.controller.engine;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.adengine.IpAreaDto;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.engine.DCopyService;
import com.ruoyi.system.service.engine.impl.PLg24oAspService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 获取远程信息
 *
 * <AUTHOR>
 * @date 2021/7/19
 */
@Slf4j
@Controller
@RequestMapping("/atest")
public class DcopyController {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DCopyService dCopyService;

    private static String K_UK = "K_UK_COUNT";

    @CrossOrigin
    @ResponseBody
    @GetMapping("/nh/stat")
    public AjaxResult stat(String slotId) throws Exception {
        String text = "";
        try {
            text = dCopyService.printLog(slotId, text);
        } catch (Exception e) {
            log.info("stat e", e);
        }
        return AjaxResult.success(text);
    }

    /**
     * 获取调用统计：------------忽略-------------
     * http://localhost:8778/atest/nh/count
     *
     * @return
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/nh/count")
    public AjaxResult count() {
        Map<String, String> map = new HashMap<>();
        for (int i = 1; i < 3; i++) {
            String k = "k" + i;
            String key1 = k + K_UK + DateUtil.formatDate(DateUtil.date());
            String count1 = redisCache.getCacheObject(key1) + "";
            String key2 = k + K_UK + DateUtil.formatDate(DateUtil.yesterday());
            String count2 = redisCache.getCacheObject(key2) + "";
            map.put(k + "今天：", count1);
            map.put(k + "昨天：", count2);
        }
        return AjaxResult.success("ok", map);
    }

}






