package com.ruoyi.web.controller.manager.crm.ssp.finance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum;
import com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberToCN;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.req.withdraw.WithdrawFileReq;
import com.ruoyi.system.req.withdraw.WithdrawListReq;
import com.ruoyi.system.req.withdraw.WithdrawStatusReq;
import com.ruoyi.system.service.finance.WithdrawRecordService;
import com.ruoyi.system.vo.withdraw.WithdrawInfoVO;
import com.ruoyi.system.vo.withdraw.WithdrawListExportVO;
import com.ruoyi.system.vo.withdraw.WithdrawListVO;
import com.ruoyi.system.vo.withdraw.WithdrawQualificationVO;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 提现相关管理 crm接口
 * <AUTHOR>
 * @date 2021/9/14 5:37 下午
 */
@RestController
@RequestMapping("manager/crm/withdraw")
public class CrmWithdrawController extends BaseController {

    @Autowired
    private WithdrawRecordService withdrawRecordService;

    /**
     * 提现列表
     *
     * @param req 请求参数
     * @return 结果
     */
    @GetMapping("list")
    public TableDataInfo<WithdrawListVO> list(WithdrawListReq req){
        if(Objects.isNull(req)){
            throw new CustomException(ErrorCode.ARGS);
        }
        return getDataTable(withdrawRecordService.selectWithdrawList(req,false));
    }

    /**
     * 提现审核
     * @param withdrawStatusReq 请求参数
     * @return
     */
    @Log(title = "提现审核", businessType = BusinessType.UPDATE)
    @PostMapping("checkWithdraw")
    public Result<Boolean> checkWithdraw(@RequestBody @Validated WithdrawStatusReq withdrawStatusReq){
        return ResultBuilder.success(withdrawRecordService.checkWithdraw(withdrawStatusReq));
    }

    /**
     * 提现单详情
     * @param withdrawId
     * @return 结果
     */
    @GetMapping("withdrawInfo")
    public Result<WithdrawInfoVO> withdrawInfo(@Validated @NotNull Long withdrawId){
        return ResultBuilder.success(withdrawRecordService.withdrawInfo(withdrawId));
    }

    /**
     * 上传提现单
     * @param req 请求参数
     * @return 更新结果
     */
    @Log(title = "上传提现单", businessType = BusinessType.IMPORT)
    @PostMapping("uploadWithdraw")
    public Result uploadWithdraw(@RequestBody @Validated WithdrawFileReq req){
        return ResultBuilder.success(withdrawRecordService.updateWithdrawFile(req));
    }

    /**
     * 提现记录导出
     * @param req 请求参数
     * @return
     */
    @Log(title = "提现记录", businessType = BusinessType.EXPORT)
    @GetMapping("export")
    public Result export(WithdrawListReq req){
        if(Objects.isNull(req)){
            throw new CustomException(ErrorCode.ARGS);
        }
        PageInfo<WithdrawListVO> withdrawListVOPageInfo = withdrawRecordService.selectWithdrawList(req, true);
        List<WithdrawListExportVO> exportList = withdrawListVOPageInfo.getList().stream().map(data -> convertExportVO(data)).collect(Collectors.toList());
        com.ruoyi.common.utils.poi.ExcelUtil<WithdrawListExportVO> util = new com.ruoyi.common.utils.poi.ExcelUtil<>(WithdrawListExportVO.class);
        return util.exportExcelResult(exportList, "提现记录");
    }

    /**
     * 下载
     * @param withdrawId 提现单id
     * @return 结果
     */
    @GetMapping("download")
    public Result download(@Validated @NotNull(message = "媒体账单id不能为空") Long withdrawId){
        WithdrawInfoVO vo = withdrawRecordService.withdrawInfo(withdrawId);
        if(Objects.isNull(vo)){
            throw new CustomException(ErrorCode.E106001);
        }
        String fileName = ExcelUtil.encodingFilename("提现单");
        //通过工具类创建writer
        ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter(ExcelUtil.getAbsoluteFile(fileName));

        try {
            setColumnWidth(writer);
            WithdrawQualificationVO qualificationInfo = vo.getQualificationInfo();
            AtomicInteger currentRow = new AtomicInteger(0);
            writer.merge(currentRow.get(),currentRow.get(),0,3,getCompanyName(qualificationInfo)+"&杭州诺禾网络科技有限公司合作提现单",false);
            writer.passCurrentRow();
            String appName = vo.getDataLists().get(0).getAppName();
            List<String> row1 = CollUtil.newArrayList("媒体名称（甲方）", "杭州诺禾网络科技有限公司", "项目名称/合同编号", "");
            List<String> row2 = CollUtil.newArrayList("媒体名称（乙方）", getCompanyName(qualificationInfo), "诺禾平台媒体名称", appName);
            List<String> row3 = CollUtil.newArrayList("结算时间段");
            List<List<String>> rows = CollUtil.newArrayList(row1, row2,row3);
            //一次性写出内容，强制输出标题
            writer.write(rows, true);;
            //结算时间段
            writer.merge(currentRow.addAndGet(rows.size()),currentRow.get(),1,3,"",false);
            //开票信息
            invoiceExcel(writer,currentRow);
            //发票快递信息
            expressInfoExcel(writer,currentRow);
            //收款信息
            payeeInfoExcel(writer, qualificationInfo, currentRow);
            writer.setCurrentRow(currentRow.addAndGet(1));
            List<List<String>> infoList = new ArrayList<>();
            AtomicInteger totalRevenue = new AtomicInteger();
            infoList.add(CollUtil.newArrayList("结算周期", "媒体名称", "媒体ID", "结算金额"));
            vo.getDataLists().forEach(data ->{
                infoList.add(CollUtil.newArrayList(data.getMonthDate().toString(),data.getAppName(),data.getAppId().toString(),"¥"+new BigDecimal(data.getAppRevenue()).divide(new BigDecimal(100)).toString()));
                totalRevenue.addAndGet(data.getAppRevenue());
            });
            BigDecimal divide = new BigDecimal(totalRevenue.get()).divide(new BigDecimal(100));
            infoList.add(CollUtil.newArrayList("", "总计", "", "¥"+divide.toString()));
            infoList.add(CollUtil.newArrayList("结算款总计"));
            infoList.add(CollUtil.newArrayList("结算款总计大写"));


            //一次性写出内容，强制输出标题
            writer.write(infoList, false);
            currentRow.addAndGet(infoList.size() -2);
            writer.merge(currentRow.get(),currentRow.get(),1,3,"¥"+divide.toString(),false);
            writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3, NumberToCN.number2CNMontrayUnit(divide),false);

            writer.merge(currentRow.addAndGet(1),currentRow.addAndGet(1),0,3, "请在收到付款结算单后进行核查，确认无误后盖章（一式两份）与发票一起快递至杭州诺禾网络科技有限公司。本公司收到发票及本付款结算单后，于3个工作日内付款至合作公司。",false);
            CellStyle cellStyle = writer.getCellStyle();
            cellStyle.setWrapText(true);
            writer.setColumnStyle(currentRow.get()-1,cellStyle);

            writer.merge(currentRow.addAndGet(1),currentRow.get(),0,3, "",false);
            writer.merge(currentRow.addAndGet(1),currentRow.get(),0,3, "          甲方（公章）：                                                     乙方（公章）：",false);
            writer.merge(currentRow.addAndGet(1),currentRow.get(),0,3, "          日期：                                                                   日期：",false);

            CellStyle cellStyle1 = writer.getOrCreateCellStyle(currentRow.get(),0);
            cellStyle1.setAlignment(HorizontalAlignment.LEFT);
            writer.setColumnStyle(currentRow.get(),cellStyle1);

            return ResultBuilder.success(fileName);
        }catch (Exception e){
            throw new CustomException(ErrorCode.SYSTEM);
        }finally {
            //关闭writer，释放内存
            writer.close();
        }

    }

    /**
     * 优先获取公司开户名，若无开户名则展示公司名称
     * @param vo
     * @return
     */
    private String getCompanyName(WithdrawQualificationVO vo){
        if(StringUtils.isNotBlank(vo.getBankAccountName())){
            return vo.getBankAccountName();
        }
        return vo.getCompanyName();
    }

    /**
     * 写入收款信息
     * @param writer
     * @param qualificationInfo
     * @param currentRow
     */
    private void payeeInfoExcel(ExcelWriter writer, WithdrawQualificationVO qualificationInfo, AtomicInteger currentRow) {
        writer.merge(currentRow.addAndGet(1),currentRow.get()+4,0,0,"收款信息",false);
        writer.merge(currentRow.get(),currentRow.get(),1,3,"开户行："+qualificationInfo.getBankName(),false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"银行账户："+qualificationInfo.getBankAccount(),false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"收款人："+getCompanyName(qualificationInfo),false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"开卡手机号：暂无",false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"地址：",false);
    }

    /**
     * 写入快递信息
     * @param writer
     * @param currentRow
     */
    private void expressInfoExcel(ExcelWriter writer,AtomicInteger currentRow) {
        writer.merge(currentRow.addAndGet(1),currentRow.get()+3,0,0,"发票快递信息",false);
        writer.merge(currentRow.get(),currentRow.get(),1,3,"地址：浙江省杭州市西湖区文二路391号西湖国际科技大厦B3-1111室",false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"邮编：310000",false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"收件人：媒介-小北",false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"电话：19157956599",false);
    }

    /**
     * 设置列宽度
     * @param writer
     */
    private void setColumnWidth(ExcelWriter writer) {
        writer.setColumnWidth(0,22);
        writer.setColumnWidth(1,29);
        writer.setColumnWidth(2,29);
        writer.setColumnWidth(3,25);
    }

    /**
     * 写入发票信息
     * @param writer
     */
    private void invoiceExcel(ExcelWriter writer,AtomicInteger currentRow) {
        writer.merge(currentRow.addAndGet(1),currentRow.get()+5,0,0,"开票信息",false);
        writer.merge(currentRow.get(),currentRow.get(),1,3,"杭州诺禾网络科技有限公司",false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"公司地址电话：浙江省杭州市西湖区文二路391号西湖国际科技大厦B3-1111室",false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"纳税人识别号：91330104MA2AX3RY88",false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"开户行：杭州银行股份有限公司留下支行",false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"银行账户：3301040160019579740",false);
        writer.merge(currentRow.addAndGet(1),currentRow.get(),1,3,"发票要求：信息技术服务费（增值税专用发票）",false);
    }

    /**
     * 数据转换导出vo
     * @param vo 提现记录
     * @return exportVo
     */
    private WithdrawListExportVO convertExportVO(WithdrawListVO vo){
        WithdrawListExportVO exportVO = new WithdrawListExportVO();

        BeanUtils.copyBeanProp(exportVO, vo);
        if (null != vo.getWithdrawAmount()) {
            exportVO.setWithdrawAmount(NumberUtils.fenToYuan(vo.getWithdrawAmount()));
        } else {
            exportVO.setWithdrawAmount("0");
        }

        exportVO.setEmail(String.format("%s（ID:%s）", vo.getEmail(), vo.getAccountId()));
        if (null != vo.getComplexAuditStatus()) {
            exportVO.setStatus(WithdrawComplexAuditStatus.getDescByStatus(vo.getComplexAuditStatus()));
        } else {
            exportVO.setStatus(WithdrawCheckStatusEnum.getDescByStatus(vo.getWithdrawStatus()));
        }
        return exportVO;
    }
}
