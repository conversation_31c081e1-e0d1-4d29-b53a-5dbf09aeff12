package com.ruoyi.web.controller.manager.wis.advertiser.data;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.req.wis.WisAdvertiserAdvertCloseReq;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advert.AdvertService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.common.WhitelistType.ADVERT_SWITCH_ADVERTISER;

/**
 *  [广告主平台]账户开关
 *
 * <AUTHOR>
 * @date 2023-06-28
 */
@RestController
@RequestMapping("/wis/advertiser/data/advertSwitch")
public class WisAdvertSwitchController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    /**
     * 查询开关状态
     */
    @GetMapping("/getStatus")
    public Result<Boolean> getStatus() {
        Long accountId = SecurityUtils.getLoginUser().getCrmAccountId();
        Account account = accountService.selectAccountById(accountId);
        if (null == account || !isAdvertiser(account.getMainType())) {
            return ResultBuilder.success(false);
        }

        Advert param = new Advert();
        param.setServingSwitch(1);
        param.setAdvertiserId(accountId);
        List<Advert> adverts = advertService.selectAdvertList(param);
        return ResultBuilder.success(CollectionUtils.isNotEmpty(adverts));
    }

    /**
     * 账户开关操作
     */
    @Log(title = "账户开关操作", businessType = BusinessType.UPDATE)
    @PostMapping("/switchToggle")
    public Result<Boolean> switchToggle(@RequestBody WisAdvertiserAdvertCloseReq req) {
        Long accountId = SecurityUtils.getLoginUser().getCrmAccountId();
        Account account = accountService.selectAccountById(accountId);
        if (null == account || !isAdvertiser(account.getMainType())) {
            return ResultBuilder.fail("无操作权限");
        }
        if (!SwitchStatusEnum.isValidStatus(req.getStatus())) {
            return ResultBuilder.fail("参数异常");
        }
        if (SwitchStatusEnum.isSwitchOn(req.getStatus())) {
            return ResultBuilder.fail("开启帐户计划请联系诺禾运营人员");
        }
        if (!whitelistService.contains(ADVERT_SWITCH_ADVERTISER, accountId)) {
            return ResultBuilder.fail("无操作权限");
        }

        // 查询需要开关的广告ID
        List<Long> advertIds = advertService.selectAllOpenOrCloseAdvertNameByAdvertiserId(accountId, SwitchStatusEnum.toggle(req.getStatus()));
        // 更新广告开关
        advertService.updateSwitchByIds(advertIds,req.getStatus());
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertCacheMsg(advertIds);
        return ResultBuilder.success(true);
    }
}
