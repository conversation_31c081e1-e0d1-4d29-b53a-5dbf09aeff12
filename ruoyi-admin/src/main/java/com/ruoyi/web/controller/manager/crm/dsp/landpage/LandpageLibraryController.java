package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.TagManagerTypeEnum;
import com.ruoyi.common.enums.advert.LandpageTypeEnum;
import com.ruoyi.common.enums.domain.DomainStatus;
import com.ruoyi.common.enums.landpage.LandPageSkinTypeEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.blindbox.BlindBoxLandpageEntity;
import com.ruoyi.system.entity.datashow.LandpageDayData;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.entity.tagmanager.TagManagerEntity;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.req.landpage.LandpageDataReq;
import com.ruoyi.system.req.landpage.library.LandpageDomainUpdateReq;
import com.ruoyi.system.req.landpage.library.LandpageLibraryListReq;
import com.ruoyi.system.req.landpage.library.LandpageLibraryReq;
import com.ruoyi.system.req.landpage.library.NewLandpageReq;
import com.ruoyi.system.req.landpage.library.TargetLandpageEditReq;
import com.ruoyi.system.service.blindbox.BlindBoxLandpageService;
import com.ruoyi.system.service.datasource.LandpageDayDataService;
import com.ruoyi.system.service.domain.DomainReplaceService;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.landpage.LandpageLibraryService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.util.LandpageUtil;
import com.ruoyi.system.vo.landpage.LandpageTagSelectVO;
import com.ruoyi.system.vo.landpage.LandpageUrlVO;
import com.ruoyi.system.vo.landpage.LandpageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.hutool.core.util.BooleanUtil.isTrue;
import static com.ruoyi.common.enums.domain.DomainType.isLandpageDomain;

/**
 * [CRM后台]落地页库
 *
 * <AUTHOR>
 * @date 2022-02-10
 */
@Slf4j
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/landpage")
public class LandpageLibraryController extends BaseController {

    @Autowired
    private LandpageLibraryService landpageLibraryService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private BlindBoxLandpageService blindBoxLandpageService;

    @Autowired
    private LandpageDayDataService landpageDayDataService;

    @Autowired
    private DomainService domainService;

    @Autowired
    public TransactionTemplate transactionTemplate;

    @Autowired
    private DomainReplaceService domainReplaceService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private TagManagerService tagManagerService;

    /**
     * 标签下拉列表
     */
    @GetMapping("/tagList")
    public Result<List<LandpageTagSelectVO>> tagList() {
        List<TagManagerEntity> list = tagManagerService.selectAllTagByType(TagManagerTypeEnum.LANDPAGE_TAG.getType());
        Map<Long, List<TagManagerEntity>> tagMap = new HashMap<>();
        List<TagManagerEntity> parentTags = new ArrayList<>();

        for (TagManagerEntity tag : list) {
            if (Objects.equals(tag.getParentId(), 0L)) {
                parentTags.add(tag);
            } else {
                if (!tagMap.containsKey(tag.getParentId())) {
                    tagMap.put(tag.getParentId(), new ArrayList<>());
                }
                tagMap.get(tag.getParentId()).add(tag);
            }
        }

        return ResultBuilder.success(parentTags.stream().sorted(Comparator.comparing(TagManagerEntity::getTagSort)).map(parentTag -> {
            LandpageTagSelectVO tagVO = new LandpageTagSelectVO(parentTag.getId(), parentTag.getTagName());
            tagVO.setChildTags(
                    tagMap.getOrDefault(parentTag.getId(), new ArrayList<>()).stream()
                            .sorted(Comparator.comparing(TagManagerEntity::getTagSort))
                            .map(childTag -> new LandpageTagSelectVO(childTag.getId(), childTag.getTagName()))
                            .collect(Collectors.toList())
            );
            return tagVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 查询落地页列表
     */
    @GetMapping("/list")
    public TableDataInfo<LandpageVO> list(LandpageLibraryListReq req) {
        startPage();
        List<Landpage> list = landpageLibraryService.selectList(buildQueryParam(req));
        // 前置落地页
        Map<String, BlindBoxLandpageEntity> blindBoxMap = blindBoxLandpageService.selectMap();

        List<Advert> adverts = advertService.selectAdvertList(new Advert());
        List<AdvertOrientation> orients = advertOrientationService.selectAdvertOrientationList(null);

        return getDataTable(PageInfoUtils.dto2Vo(list, lp -> {
            LandpageVO vo = BeanUtil.copyProperties(lp, LandpageVO.class);
            if (StringUtils.isNotBlank(lp.getKey())) {
                vo.setRelateAdvertIds(Joiner.on(",").join(queryAdvertIdsByLpk(lp.getKey(), adverts, orients)));
                Optional.ofNullable(blindBoxMap.get(lp.getKey())).ifPresent(blindBox -> {
                    vo.setTargetLandpage(blindBox.getTargetLandpage());
                    vo.setFooter(blindBox.getFooter());
                });
            }
            // 展示当前在用的域名
            if (NumberUtils.isNonNullAndGtZero(lp.getSkinType())) {
                vo.setDomain(ReUtil.getGroup0("([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,6}", lp.getUrl()));
            }
            // 企微囤粉皮肤的落地页链接特殊处理
            if (Objects.equals(LandPageSkinTypeEnum.QWTF.getType(), lp.getSkinType())) {
                vo.setUrl(StrUtil.replace(vo.getUrl(), "/land/render/","/lp/qwtf/jump?lpKey="));
            }
            return vo;
        }));
    }

    /**
     * 获取落地页详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(landpageLibraryService.selectById(id));
    }

    /**
     * 新增落地页
     */
    @Log(title = "落地页", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody @Validated LandpageLibraryReq req) {
        if (landpageLibraryService.isLandpageExist(req.getUrl())) {
            return AjaxResult.error("落地页已添加");
        }
        // 非号卡落地页不允许使用lego.zidg.com域名
        if (req.getUrl().contains("lego.zidg.com") && !StrUtil.containsAny(req.getTag(), "移动", "联通", "电信")) {
            return AjaxResult.error("lego.zidg.com域名仅限号卡使用");
        }
        Landpage param = BeanUtil.copyProperties(req, Landpage.class);
        param.setKey(LandpageUtil.extractLpk(param.getUrl()));
        param.setPageConfig(StringUtils.defaultString(param.getPageConfig()));
        int result = landpageLibraryService.insertLandpage(param);
        refreshCacheService.sendRefreshLandpageCacheMsg(param.getKey());
        return toAjax(result);
    }

    /**
     * 根据皮肤新增/编辑落地页
     */
    @Log(title = "落地页", businessType = BusinessType.INSERT)
    @PostMapping("/addOrUpdateBySkin")
    public Result<Boolean> addOrUpdateBySkin(@RequestBody @Validated NewLandpageReq req) {
        // 非号卡落地页不允许使用lego.zidg.com域名
        if (req.getDomain().contains("lego.zidg.com") && !StrUtil.containsAny(req.getTag(), "移动", "联通", "电信")) {
            return ResultBuilder.fail("lego.zidg.com域名仅限号卡使用");
        }
        // 校验跳转链接是否合法
        if (!validateTargetLandpage(req.getPageConfig())) {
            return ResultBuilder.fail("跳转链接不能包含nadkey=");
        }
        if(Objects.isNull(req.getId())){
            int result = landpageLibraryService.insertLandpageBySkin(BeanUtil.copyProperties(req, Landpage.class));
            return ResultBuilder.success(result > 0);
        }

        int result = landpageLibraryService.updateLandpageBySkin(BeanUtil.copyProperties(req, Landpage.class));
        return ResultBuilder.success(result > 0);
    }

    /**
     * 复制落地页
     */
    @Log(title = "落地页", businessType = BusinessType.INSERT)
    @PostMapping("copy")
    public Result<Boolean> copy(@RequestBody @Validated IdReq req){
        Landpage landpage = landpageLibraryService.selectById(req.getId());
        if(Objects.isNull(landpage)){
            throw new CustomException(ErrorCode.E110002);
        }
        landpage.setName(landpage.getName()+"-复制");
        int result = landpageLibraryService.insertLandpageBySkin(landpage);
        return ResultBuilder.success(result > 0);
    }

    /**
     * 修改落地页
     */
    @Log(title = "落地页", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody @Validated LandpageLibraryReq req) {
        // 非号卡落地页不允许使用lego.zidg.com域名
        if (req.getUrl().contains("lego.zidg.com") && !StrUtil.containsAny(req.getTag(), "移动", "联通", "电信")) {
            return AjaxResult.error("lego.zidg.com域名仅限号卡使用");
        }
        Landpage param = BeanUtil.copyProperties(req, Landpage.class);
        param.setKey(LandpageUtil.extractLpk(param.getUrl()));
        param.setPageConfig(StringUtils.defaultString(param.getPageConfig()));
        int result = landpageLibraryService.updateLandpage(param);
        refreshCacheService.sendRefreshLandpageCacheMsg(param.getKey());
        return toAjax(result);
    }

    /**
     * 修改目标落地页
     */
    @Log(title = "落地页", businessType = BusinessType.UPDATE)
    @PostMapping("/editTargetLandpage")
    public AjaxResult editTargetLandpage(@RequestBody @Validated TargetLandpageEditReq req) {
        BlindBoxLandpageEntity landpage = blindBoxLandpageService.selectByLandpageKey(req.getKey());
        if (null == landpage) {
            String tag = landpageLibraryService.getLandpageTag(req.getKey());
            if (StrUtil.containsAny(tag, "权益卡","神奇卡", "盲盒")) {
                BlindBoxLandpageEntity blindBoxLandpage = new BlindBoxLandpageEntity();
                blindBoxLandpage.setLandpageKey(req.getKey());
                blindBoxLandpage.setTargetLandpage(req.getTargetLandpage());
                blindBoxLandpage.setFooter(req.getFooter());
                Boolean result = blindBoxLandpageService.insert(blindBoxLandpage);
                return toAjax(result);
            }
            return AjaxResult.error("该落地页不支持修改跳转链接");
        }
        BlindBoxLandpageEntity updateLandpage = new BlindBoxLandpageEntity();
        updateLandpage.setId(landpage.getId());
        updateLandpage.setTargetLandpage(req.getTargetLandpage());
        updateLandpage.setFooter(req.getFooter());
        return toAjax(blindBoxLandpageService.updateById(updateLandpage));
    }

    /**
     * 修改落地页域名
     */
    @Log(title = "落地页", businessType = BusinessType.UPDATE)
    @PostMapping("/updateLandpageDomain")
    public AjaxResult updateLandpageDomain(@RequestBody @Validated LandpageDomainUpdateReq req) {
        Landpage landpage = landpageLibraryService.selectByKey(req.getKey());
        if (null == landpage) {
            return AjaxResult.error("未查询到该落地页");
        }
        Domain domain = domainService.selectDomain(req.getDomain());
        if (!isLandpageDomain(domain.getDomainType()) || !DomainStatus.isNormal(domain.getDomainStatus())) {
            return AjaxResult.error("请选择可用的落地页域名");
        }

        // 替换域名后的链接
        String url = domainReplaceService.doReplaceDomain(landpage.getUrl(), req.getDomain());

        Set<Long> advertIds = new HashSet<>();
        Boolean success = transactionTemplate.execute(status -> {
            try {
                // 更新落地页库链接
                landpageLibraryService.updateLandpageUrl(landpage.getId(), landpage.getKey(), url);

                // 更新广告的落地页
                Advert advertParam = new Advert();
                advertParam.setLandpageUrl(req.getKey());
                List<Advert> adverts = advertService.selectAdvertList(advertParam);
                if (CollectionUtils.isNotEmpty(adverts)) {
                    adverts.forEach(advert -> {
                        // 忽略小程序链接、快应用链接、微信防封链接
                        if (StrUtil.containsAnyIgnoreCase(advert.getLandpageUrl(), "weixin://", "ifr://", "hapjs", "quickapp", "ifr.html", "myqcloud")) {
                            return;
                        }
                        // 仅替换自建站落地页
                        if (Objects.equals(LandpageUtil.extractLpk(advert.getLandpageUrl()), req.getKey())) {
                            String landpageUrl = domainReplaceService.doReplaceDomain(advert.getLandpageUrl(), req.getDomain());
                            advertService.updateLandPageUrl(advert.getId(), landpageUrl);
                            advertIds.add(advert.getId());
                        }
                    });
                }
                // 更新广告配置的落地页
                AdvertOrientation orientParam = new AdvertOrientation();
                orientParam.setLandpageUrl(req.getKey());
                List<AdvertOrientation> orients = advertOrientationService.selectAdvertOrientationList(orientParam);
                if (CollectionUtils.isNotEmpty(orients)) {
                    orients.forEach(orient -> {
                        // 忽略非自定义落地页的配置
                        if (!LandpageTypeEnum.isCustom(orient.getLandpageType())) {
                            return;
                        }
                        // 忽略小程序链接、快应用链接、微信防封链接
                        if (StrUtil.containsAnyIgnoreCase(orient.getLandpageUrl(), "weixin://", "ifr://", "hapjs", "quickapp", "ifr.html", "myqcloud")) {
                            return;
                        }
                        // 仅替换自建站落地页
                        if (Objects.equals(LandpageUtil.extractLpk(orient.getLandpageUrl()), req.getKey())) {
                            String landpageUrl = domainReplaceService.doReplaceDomain(orient.getLandpageUrl(), req.getDomain());
                            advertOrientationService.updateLandpageUrl(orient.getId(), landpageUrl);
                            advertIds.add(orient.getAdvertId());
                        }
                    });
                }
                return true;
            } catch (Exception e) {
                log.error("更新落地页域名异常, key={}, domain={}", req.getKey(), req.getDomain(), e);
                status.setRollbackOnly();
                return false;
            }
        });
        if (isTrue(success)) {
            refreshCacheService.sendRefreshLandpageCacheMsg(req.getKey());
            advertIds.forEach(advertId -> refreshCacheService.sendRefreshAdvertCacheMsg(advertId));

            GlobalThreadPool.executorService.submit(() -> {
                String sbr = "落地页域名更改\n" +
                        "\n落地页名称: " + landpage.getName() +
                        "\n原链接: " + landpage.getUrl() +
                        "\n更改后的链接: " + url +
                        "\n同步更改的广告ID: " + Joiner.on(",").join(advertIds);
                DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sbr);
            });
            return AjaxResult.success();
        }
        return AjaxResult.error("更新落地页域名失败");
    }

    /**
     * 标记落地页无效
     */
    @Log(title = "落地页", businessType = BusinessType.UPDATE)
    @PostMapping("/invalidateLandpage")
    public AjaxResult invalidateLandpage(@RequestBody IdReq req) {
        Landpage landpage = landpageLibraryService.selectById(req.getId());
        if (null == landpage) {
            return AjaxResult.error("无效的落地页ID");
        }

        Advert advertParam = new Advert();
        advertParam.setIsInvalid(0);
        List<Advert> adverts = advertService.selectAdvertList(advertParam);
        List<AdvertOrientation> orients = advertOrientationService.selectAdvertOrientationList(null);
        Set<Long> advertIds = queryAdvertIdsByLpk(landpage.getKey(), adverts, orients);
        if (CollectionUtils.isNotEmpty(advertIds)) {
            return AjaxResult.error(StrUtil.format("落地页正在被广告{}使用，暂时不能隐藏", Joiner.on(',').join(advertIds)));
        }
        // 检查近30天是否有数据
        LandpageDataReq param = new LandpageDataReq();
        param.setLandpageKeyList(Collections.singletonList(landpage.getKey()));
        param.setStartDate(DateUtil.offsetDay(new Date(), -30));
        List<LandpageDayData> list = landpageDayDataService.selectList(param);
        if (CollectionUtils.isNotEmpty(list)) {
            return AjaxResult.error("落地页近30天有数据，暂时不能隐藏");
        }
        return toAjax(landpageLibraryService.invalidateLandpage(req.getId()));
    }

    /**
     * 查询前置落地页
     */
    @GetMapping("/getLandpageUrlByTargetLandpage")
    public Result<List<LandpageUrlVO>> getLandpageUrlByTargetLandpage(String targetLandpage) {
        if (StringUtils.isBlank(targetLandpage)) {
            return ResultBuilder.success();
        }
        List<Landpage> landpageList = new ArrayList<>();
        // 查询盲盒落地页
        List<String> keys = blindBoxLandpageService.selectByTargetLandpage(targetLandpage);
        CollUtil.addAll(landpageList, landpageLibraryService.selectByKeys(keys));
        // 查询自建落地页
        CollUtil.addAll(landpageList, landpageLibraryService.selectByTargetLandpage(targetLandpage));
        // 构造返回结果
        return ResultBuilder.success(landpageList.stream().map(landpage -> BeanUtil.copyProperties(landpage, LandpageUrlVO.class)).collect(Collectors.toList()));
    }

    /**
     * 查询落地页在用的广告ID列表
     *
     * @param lpk 落地页标识
     * @return 广告ID列表
     */
    private Set<Long> queryAdvertIdsByLpk(String lpk, List<Advert> adverts, List<AdvertOrientation> orients) {
        Set<Long> advertIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(adverts)) {
            advertIds.addAll(adverts.stream().filter(ad -> StrUtil.equalsIgnoreCase(LandpageUtil.extractLpk(ad.getLandpageUrl()), lpk))
                    .map(Advert::getId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(orients)) {
            advertIds.addAll(orients.stream().filter(o -> StrUtil.equalsIgnoreCase(LandpageUtil.extractLpk(o.getLandpageUrl()), lpk))
                    .map(AdvertOrientation::getAdvertId).collect(Collectors.toList()));
        }
        return advertIds;
    }

    /**
     * 构造查询参数
     *
     * @param req 请求参数
     * @return 查询参数
     */
    private Landpage buildQueryParam(LandpageLibraryListReq req) {
        Landpage param = new Landpage();
        param.setName(StrUtil.trim(req.getName()));
        param.setTag(req.getTag());
        param.setId(req.getId());
        param.setIsInvalid(0);
        param.setSkinType(req.getSkinType());
        return param;
    }

    /**
     * 校验跳转链接是否合法
     */
    private boolean validateTargetLandpage(String pageConfig) {
        JSONObject config = JSON.parseObject(pageConfig);
        if (null == config) {
            return true;
        }
        String targetLandpage = config.getString("targetLandpage");
        return StringUtils.isBlank(targetLandpage) || !StrUtil.containsAny(targetLandpage, "nadkey=");
    }
}
