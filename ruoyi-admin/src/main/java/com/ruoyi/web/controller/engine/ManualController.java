package com.ruoyi.web.controller.engine;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.domain.DomainType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.domain.adengine.IpAreaDto;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.entity.system.SysConfigDomainEntity;
import com.ruoyi.system.req.account.AdvertiserAppRetConfigReq;
import com.ruoyi.system.req.engine.DomainReq;
import com.ruoyi.system.req.engine.MockIpReq;
import com.ruoyi.system.req.slot.SlotChannelConfigReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.landpage.LandpageService;
import com.ruoyi.system.service.manager.SlotConfigService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.system.SysConfigDomainService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 手动执行接口（不鉴权）
 *
 * <AUTHOR>
 * @date 2021/9/6
 */
@Slf4j
@RestController
@RequestMapping("/manual")
public class ManualController {

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private SysConfigDomainService sysConfigDomainService;

    @Autowired
    private SlotService slotService;

    @Autowired
    public RedisCache redisCache;

    @Autowired
    private LandpageService landpageService;

    @Autowired
    private SlotConfigService slotConfigService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private DomainService domainService;

    @Autowired
    private AreaService areaService;


    /**
     * 异步操作任务调度线程池
     */
    private final ScheduledExecutorService scheduledExecutor = SpringUtils.getBean("scheduledExecutorService");

    /**
     * 广告主回传媒体信息配置
     */
    @PostMapping("/advertiserAppRetConfig")
    public AjaxResult advertiserAppRetConfig(@RequestBody AdvertiserAppRetConfigReq req) {
        if (null == req.getAdvertiserId() || null == req.getAppRet()) {
            return AjaxResult.error("无效的参数");
        }

        advertiserService.advertiserAppRetConfig(req.getAdvertiserId(), req.getAppRet());
        return AjaxResult.success();
    }

    /**
     * 广告位对应渠道配置
     */
    @PostMapping("/slotChannelConfig")
    public AjaxResult slotChannelConfig(@RequestBody SlotChannelConfigReq req) {
        if (null == req.getChannel() || CollectionUtils.isEmpty(req.getSlotIds())) {
            return AjaxResult.error("无效的参数");
        }
        req.getSlotIds().forEach(slotId -> slotService.updateSlotChannel(slotId, req.getChannel()));

        String sbr = "广告位渠道配置更新\n" +
                "\n渠道: " + req.getChannel() +
                "\n广告位: " + Joiner.on(",").join(req.getSlotIds());
        DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sbr);

        return AjaxResult.success();
    }

    /**
     * 停止回传落地页任务job
     */
    @CrossOrigin
    @RequestMapping("/stopLandpageManualAssignJob")
    public AjaxResult stopLandpageManualAssignJob() {
        redisCache.deleteObject(CrmRedisKeyFactory.K013.toString());
        return AjaxResult.success(redisCache.countCacheZSet(CrmRedisKeyFactory.K013.toString()));
    }

    /**
     * 落地页失败表单重传
     */
    @GetMapping("/reCallbackForm")
    public AjaxResult reCallbackForm(String ids) {
        if (StringUtils.isBlank(ids)) {
            return AjaxResult.error("无效的参数");
        }
        List<String> recordIds = Splitter.on(",").omitEmptyStrings().splitToList(ids);
        int delay = 3;
        for (String recordId : recordIds) {
            if (StringUtils.isNumeric(recordId)) {
                scheduledExecutor.schedule(() -> {
                    try {
                        landpageService.reCallbackForm(Long.valueOf(recordId));
                    } catch (Exception e) {
                        log.error("落地页失败表单重传异常, recordId={}", recordId, e);
                    }
                }, delay, TimeUnit.SECONDS);
                delay += 3;
            }
        }
        return AjaxResult.success();
    }

    /**
     * 模拟IP解析为特定地域
     */
    @CrossOrigin
    @GetMapping("/mockIp")
    public AjaxResult mockIp(MockIpReq req) {
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        if (StringUtils.isBlank(ip)) {
            return AjaxResult.error("未解析到IP");
        }
        String redisKey = Constants.IP_ANALYSIS + ip;
        if (StringUtils.isNotBlank(req.getProvince()) || StringUtils.isNotBlank(req.getCity())) {
            JSONObject result = new JSONObject();
            result.put("resultcode", "200");
            result.put("error_code", "200");
            result.put("reason", "查询成功");
            result.put("result", new JSONObject());
            result.getJSONObject("result").put("Country", "中国");
            result.getJSONObject("result").put("Province", StringUtils.defaultString(req.getProvince()));
            result.getJSONObject("result").put("City", StringUtils.defaultString(req.getCity()));
            result.getJSONObject("result").put("District", StringUtils.defaultString(req.getDistrict()));
            result.getJSONObject("result").put("Isp", StringUtils.defaultString(req.getIsp()));
            redisCache.setCacheObject(redisKey, result.toString(), 10, TimeUnit.MINUTES);
        }
        return AjaxResult.success(redisCache.getCacheObject(redisKey));
    }

    /**
     * 身份证校验重试
     */
    @CrossOrigin
    @GetMapping("/retryIdCardAudit")
    public AjaxResult retryIdCardAudit(Integer limit) {
        return AjaxResult.success(landpageService.retryIdCardAudit(limit));
    }

    /**
     * 屏蔽IP访问
     */
    @CrossOrigin
    @GetMapping("/blockIP")
    public AjaxResult blockIP(String ip, Integer seconds) {
        if (!Validator.isIpv4(ip)) {
            return AjaxResult.error("无效IP");
        }
        String key = EngineRedisKeyFactory.K039.toString();
        redisCache.addCacheSet(key, ip);
        int expires = Math.max(NumberUtils.defaultInt(seconds, 3600), 60);
        scheduledExecutor.schedule(() -> {
            try {
                redisCache.deleteCacheSet(key, ip);
            } catch (Exception e) {
                log.error("IP黑名单移除失败, ip={}", ip, e);
            }
        }, expires, TimeUnit.SECONDS);
        return AjaxResult.success(redisCache.getCacheSet(key));
    }

    /**
     * 广告位流量下降告警沉默
     */
    @CrossOrigin
    @GetMapping("/slotDataMonitorSilent")
    public AjaxResult slotDataMonitorSilent(Long slotId) {
        if (null == slotId) {
            return AjaxResult.error("参数错误");
        }
        String key = CrmRedisKeyFactory.K003.join(DateUtil.today(), slotId);
        boolean hasKey = redisCache.hasKey(key);
        redisCache.setCacheObject(key, "1", 1, TimeUnit.DAYS);
        if (!hasKey) {
            DingRobotUtil.sendText(DingWebhookConfig.getBizAlert(), StrUtil.format("广告位{}今日暂停提醒", slotId));
        }
        return AjaxResult.success();
    }

    /**
     * 广告位活动同域名
     */
    @CrossOrigin
    @GetMapping("/sameDomain")
    public AjaxResult sameDomain(Long slotId) {
        if (null == slotId) {
            return AjaxResult.error("参数错误");
        }
        SlotConfig slotConfig = slotConfigService.selectBySlotId(slotId);
        if (null == slotConfig) {
            return AjaxResult.error("请先替换广告位域名");
        }
        JSONObject domainConfig = JSON.parseObject(slotConfig.getDomainConfig());
        if (null == domainConfig || !domainConfig.containsKey(DomainType.SLOT_DOMAIN.getKey())) {
            return AjaxResult.error("请先替换广告位域名");
        }
        String slotDomain = domainConfig.getString(DomainType.SLOT_DOMAIN.getKey());
        domainConfig.put(DomainType.ACTIVITY_DOMAIN.getKey(), slotDomain);

        SlotConfig updateConfig = new SlotConfig();
        updateConfig.setSlotId(slotId);
        updateConfig.setDomainConfig(JSON.toJSONString(domainConfig));
        slotConfigService.updateSlotConfig(updateConfig);

        refreshCacheService.sendRefreshSlotCacheMsg(slotId);

        String sbr = "广告位替换活动域名\n" +
                "\n广告位ID: " + slotId +
                "\n活动域名: " + slotDomain;
        DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sbr);
        return AjaxResult.success(slotDomain);
    }

    /**
     * 根据域名查询dataJSON
     */
    @CrossOrigin
    @GetMapping("/dataJson")
    public AjaxResult dataJson(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return AjaxResult.error("1.参数缺失：domain");
        }
        SysConfigDomainEntity entity = sysConfigDomainService.selectByKey(domain);
        if (null == entity) {
            return AjaxResult.error("2.结果不存在，domain：" + domain);
        }
        String dataJson = entity.getDataJson();
        if (StringUtils.isEmpty(dataJson)) {
            return AjaxResult.error("3.数据库中dataJson为空");
        }
        JSONObject jsonObject;
        try {
            jsonObject = JSON.parseObject(dataJson);
        } catch (Exception e) {
            return AjaxResult.error("4.dataJson非Json：" + dataJson);
        }
        entity.setDataJson(JSON.toJSONString(jsonObject));
        return AjaxResult.success(entity);
    }

    /**
     * 根据域名查询dataJSON
     */
    @CrossOrigin
    @PostMapping("/dataJson/add")
    public AjaxResult dataJson(@RequestBody DomainReq req) {
        String domain = req.getDomain();
        String dataJson = req.getDataJson();
        if (StringUtils.isEmpty(domain)) {
            return AjaxResult.error("1.参数缺失：domain");
        }
        if (StringUtils.isEmpty(dataJson)) {
            return AjaxResult.error("2.数据库中dataJson为空");
        }
        dataJson = UrlUtils.urlDecode(dataJson);
        try {
            JSONObject jsonObject = JSON.parseObject(dataJson);
        } catch (Exception e) {
            return AjaxResult.error("3.dataJson非Json：" + dataJson);
        }
        SysConfigDomainEntity entity = new SysConfigDomainEntity();
        entity.setConfigKey(domain);
        entity.setDataJson(dataJson);
        Boolean insert = sysConfigDomainService.insert(entity);
        return AjaxResult.success(insert);
    }

    /**
     * 查询域名的备案号
     */
    @CrossOrigin
    @RequestMapping("/getIcpByDomain")
    public Result<String> getIcpByDomain(String domain) {
        Domain domainDO = domainService.selectDomain(domain);
        if (null != domainDO) {
            return ResultBuilder.success(domainDO.getIcpNo());
        }
        return ResultBuilder.success();
    }

    /**
     * IP解析
     */
    @CrossOrigin
    @RequestMapping("/ip")
    public Result<IpAreaDto> ipAnalysis(String ip, String key) {
        if (StringUtils.isBlank(ip) || StringUtils.isBlank(key)
                || !Objects.equals(key, SecureUtil.md5("nh" + ip))) {
            return ResultBuilder.fail("参数错误");
        }
        try {
            return ResultBuilder.success(areaService.ipAnalysis(ip));
        } catch (Exception e) {
            log.error("IP解析异常, ip={}", ip, e);
        }
        return ResultBuilder.success();
    }
}
