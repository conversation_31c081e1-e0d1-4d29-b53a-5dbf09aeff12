package com.ruoyi.web.controller.engine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Joiner;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.bo.landpage.ExternalLandpageFormInfoBo;
import com.ruoyi.system.entity.account.AdvertiserAccess;
import com.ruoyi.system.req.engine.ExternalLandPageFormReq;
import com.ruoyi.system.service.engine.cache.AdvertiserCacheService;
import com.ruoyi.system.service.landpage.ExternalLandpageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 外部落地页表单(不鉴权)
 *
 * <AUTHOR>
 * @date 2023/04/06
 */
@Slf4j
@RestController
@RequestMapping("/lp/external")
public class ExternalLandpageController {

    @Autowired
    private ExternalLandpageService externalLandpageService;

    @Autowired
    private AdvertiserCacheService advertiserCacheService;

    /**
     * 外部表单提交
     */
    @CrossOrigin
    @PostMapping("/submit")
    public Result<Void> submit(@RequestBody ExternalLandPageFormReq req) {
        log.info("外部表单提交, accessKey={}, timestamp={}, sign={}, count={}", req.getAccessKey(), req.getTimestamp(), req.getSign(), CollUtil.size(req.getList()));

        if (StringUtils.isBlank(req.getAccessKey())) {
            return ResultBuilder.fail(ErrorCode.E112001);
        }
        long timestamp = System.currentTimeMillis();
        if (null == req.getTimestamp() || !(timestamp - req.getTimestamp() <= 600000 && timestamp - req.getTimestamp() >= 0)) {
            return ResultBuilder.fail(ErrorCode.E112002);
        }
        if (CollectionUtils.isEmpty(req.getList())) {
            return ResultBuilder.fail(ErrorCode.E112003);
        }
        for (ExternalLandpageFormInfoBo form : req.getList()) {
            if (StringUtils.isBlank(form.getExternalNo())) {
                return ResultBuilder.fail(ErrorCode.E112004);
            }
            if (StringUtils.isBlank(form.getName()) || StringUtils.isBlank(form.getProvince()) || StringUtils.isBlank(form.getCity())
                    || StringUtils.isBlank(form.getDistrict()) || StringUtils.isBlank(form.getAddress())) {
                return ResultBuilder.fail(ErrorCode.E112005);
            }
            if (StrUtil.length(form.getPhone()) != 11) {
                return ResultBuilder.fail(ErrorCode.E112008);
            }
            if (!IdcardUtil.isValidCard(form.getIdCard())) {
                return ResultBuilder.fail(ErrorCode.E112009);
            }
        }

        // 查询广告主秘钥
        AdvertiserAccess access = advertiserCacheService.queryAdvertiserAccess(req.getAccessKey());
        if (null == access || null == access.getAdvertiserId()) {
            return ResultBuilder.fail(ErrorCode.E112001);
        }
        // 签名校验
        if (!checkSignature(req, access.getSecretKey())) {
            return ResultBuilder.fail(ErrorCode.E112006);
        }
        try {
            int result = externalLandpageService.submit(access.getAdvertiserId(), req.getList());
            log.info("外部表单提交保存成功, accessKey={}, timestamp={}, sign={}, count={}, success={}", req.getAccessKey(), req.getTimestamp(), req.getSign(), CollUtil.size(req.getList()), result);
            return ResultBuilder.success();
        } catch (Exception e) {
            log.error("外部表单提交异常, accessKey={}, timestamp={}, sign={}, count={}", req.getAccessKey(), req.getTimestamp(), req.getSign(), CollUtil.size(req.getList()), e);
        }
        return ResultBuilder.fail(ErrorCode.E112007);
    }

    /**
     * 校验签名
     */
    private boolean checkSignature(ExternalLandPageFormReq req, String secretKey) {
        List<String> arr = Arrays.asList(req.getAccessKey(), secretKey, String.valueOf(req.getTimestamp()));
        arr.sort(String::compareTo);
        return StringUtils.isNotBlank(req.getSign()) && Objects.equals(req.getSign(), Md5Utils.hash(Joiner.on("").join(arr)));
    }
}
