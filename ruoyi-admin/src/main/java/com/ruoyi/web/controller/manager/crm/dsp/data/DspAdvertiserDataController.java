package com.ruoyi.web.controller.manager.crm.dsp.data;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.advertiser.finance.CrmAdvertiserCpcDataExcelBo;
import com.ruoyi.system.bo.advertiser.finance.CrmAdvertiserCpcLandpageDataExcelBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.advertiser.AdvertiserDataManager;
import com.ruoyi.system.req.advertiser.data.CrmAdvertiserCpcDataReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.advertiser.finance.CrmAdvertiserCpcDataListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * [CRM后台]广告主数据
 *
 * <AUTHOR>
 * @date 2022-10-13
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/data/advertiserData")
public class DspAdvertiserDataController extends BaseController {

    @Autowired
    private AdvertiserDataManager advertiserDataManager;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private AgentService agentService;

    /**
     * 获取cpc数据列表(日期维度)
     */
    @GetMapping("/cpcList")
    public TableDataInfo<CrmAdvertiserCpcDataListVO> cpcList(CrmAdvertiserCpcDataReq req){
        req.setAdvertiserIds(getAdvertiserIds(req));
        PageInfo<CrmAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.batchSelectCpcDataList(req,false);
        return getDataTable(pageInfo);
    }

    /**
     * 获取cpc数据列表(落地页链接维度)
     */
    @GetMapping("/cpcLandpageList")
    public TableDataInfo<CrmAdvertiserCpcDataListVO> cpcLandpageList(CrmAdvertiserCpcDataReq req){
        req.setAdvertiserIds(getAdvertiserIds(req));
        PageInfo<CrmAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.batchSelectCpcLandpageDataList(req,false);
        return getDataTable(pageInfo);
    }

    /**
     * 导出广告主cpc数据(日期维度)
     */
    @Log(title = "广告主后台cpc表单记录", businessType = BusinessType.EXPORT)
    @GetMapping("cpcExport")
    public AjaxResult cpcExport(CrmAdvertiserCpcDataReq req){
        req.setAdvertiserIds(getAdvertiserIds(req));
        PageInfo<CrmAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.batchSelectCpcDataList(req,true);
        List<CrmAdvertiserCpcDataExcelBo> cpcDataExcelBos = pageInfo.getList().stream().map(data -> {
            CrmAdvertiserCpcDataExcelBo excelBo = BeanUtil.copyProperties(data, CrmAdvertiserCpcDataExcelBo.class);
            excelBo.setConsumeAmount(NumberUtils.fenToYuan(data.getConsumeAmount()));
            return excelBo;
        }).collect(Collectors.toList());
        String fileName = UUID.randomUUID().toString() + "_消费记录数据.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, CrmAdvertiserCpcDataExcelBo.class).sheet("消费记录数据").doWrite(cpcDataExcelBos);
        return AjaxResult.success(fileName);
    }

    /**
     * 导出广告主cpc数据(落地页链接维度)
     */
    @Log(title = "广告主后台cpc表单记录", businessType = BusinessType.EXPORT)
    @GetMapping("cpcLandpageExport")
    public AjaxResult cpcLandpageExport(CrmAdvertiserCpcDataReq req){
        req.setAdvertiserIds(getAdvertiserIds(req));
        PageInfo<CrmAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.batchSelectCpcLandpageDataList(req, true);
        List<CrmAdvertiserCpcLandpageDataExcelBo> cpcDataExcelBos = pageInfo.getList().stream().map(data -> {
            CrmAdvertiserCpcLandpageDataExcelBo excelBo = BeanUtil.copyProperties(data, CrmAdvertiserCpcLandpageDataExcelBo.class);
            excelBo.setConsumeAmount(NumberUtils.fenToYuan(data.getConsumeAmount()));
            return excelBo;
        }).collect(Collectors.toList());
        String fileName = UUID.randomUUID().toString() + "_消费记录数据.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, CrmAdvertiserCpcLandpageDataExcelBo.class).sheet("消费记录数据").doWrite(cpcDataExcelBos);
        return AjaxResult.success(fileName);
    }

    private List<Long> getAdvertiserIds(CrmAdvertiserCpcDataReq req) {
        List<Long> advertiserIds = new ArrayList<>();
        // 广告主查询
        if (CollectionUtils.isNotEmpty(req.getAdvertiserIds())) {
            advertiserIds.addAll(req.getAdvertiserIds());
        }
        // 代理商查询
        if (CollectionUtils.isNotEmpty(req.getAgentIds())) {
            advertiserIds = mergeParamIds(advertiserIds, agentService.selectAdvertiserIdsByAgentIds(req.getAgentIds()));
            if (CollectionUtils.isEmpty(advertiserIds)) {
                return advertiserIds;
            }
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            advertiserIds = mergeParamIds(advertiserIds, permission.getValues());
            if (CollectionUtils.isEmpty(advertiserIds)) {
                return advertiserIds;
            }
        }
        // 查询所有广告主ID
        if (CollectionUtils.isEmpty(advertiserIds)) {
            advertiserIds = advertiserService.selectTotalAdvertiserList().stream().map(Account::getId).collect(Collectors.toList());
        }
        return advertiserIds;
    }
}
