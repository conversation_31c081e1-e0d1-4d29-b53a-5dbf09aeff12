package com.ruoyi.web.controller.engine;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.landpage.PlayletTradeStatus;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.PlayletLandpageFormRecordEntity;
import com.ruoyi.system.req.engine.PlayletLandpageOrderReq;
import com.ruoyi.system.req.engine.PlayletLandpagePayReq;
import com.ruoyi.system.req.engine.PlayletLandpageRefundReq;
import com.ruoyi.system.service.landpage.PlayletLandpageFormRecordService;
import com.ruoyi.system.service.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 短剧落地页(不鉴权)
 *
 * <AUTHOR>
 * @date 2023/08/03
 */
@Slf4j
@RestController
@RequestMapping("/lp/playlet")
public class PlayletLandpageController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private PlayletLandpageFormRecordService playletLandpageFormRecordService;

    /**
     * 下单
     */
    @CrossOrigin
    @PostMapping("/order")
    public Result<Void> order(@RequestBody PlayletLandpageOrderReq req) {
        log.info("短剧落地页表单回传, req={}", JSON.toJSONString(req));
        if (null == req.getTradeAmount()) {
            return ResultBuilder.fail("支付金额不能为空");
        }
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            return ResultBuilder.fail("无效的订单号");
        }
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);

        PlayletLandpageFormRecordEntity record = new PlayletLandpageFormRecordEntity();
        record.setOrderId(req.getOrderId());
        record.setTradeNo(req.getTradeNo());
        record.setTradeAmount(req.getTradeAmount());
        record.setPayPlatform(req.getPayPlatform());
        record.setAdvertId(order.getAdvertId());
        record.setConsumerId(order.getConsumerId());
        record.setAppId(order.getAppId());
        record.setSlotId(order.getSlotId());
        if (null != adSnapshot) {
            record.setLandpageUrl(adSnapshot.getLandpageUrl());
        }
        playletLandpageFormRecordService.insert(record);
        return ResultBuilder.success();
    }

    /**
     * 支付
     */
    @CrossOrigin
    @PostMapping("/pay")
    public Result<Void> pay(@RequestBody PlayletLandpagePayReq req) {
        log.info("短剧落地页支付回传, req={}", JSON.toJSONString(req));
        PlayletLandpageFormRecordEntity record = playletLandpageFormRecordService.selectByTradeNo(req.getTradeNo());
        if (null == record) {
            return ResultBuilder.fail("未查询到支付订单号");
        }
        PlayletLandpageFormRecordEntity updateRecord = new PlayletLandpageFormRecordEntity();
        updateRecord.setId(record.getId());
        updateRecord.setOpenid(req.getOpenid());
        updateRecord.setTradeTime(req.getTradeTime());
        updateRecord.setTradeStatus(PlayletTradeStatus.PAID.getStatus());
        updateRecord.setTransactionId(req.getTransactionId());
        playletLandpageFormRecordService.updateById(updateRecord);
        return ResultBuilder.success();
    }

    /**
     * 退款
     */
    @CrossOrigin
    @PostMapping("/refund")
    public Result<Void> refund(@RequestBody PlayletLandpageRefundReq req) {
        log.info("短剧落地页退款回传, req={}", JSON.toJSONString(req));
        PlayletLandpageFormRecordEntity record = playletLandpageFormRecordService.selectByTradeNo(req.getTradeNo());
        if (null == record) {
            return ResultBuilder.fail("未查询到支付订单号");
        }
        PlayletLandpageFormRecordEntity updateRecord = new PlayletLandpageFormRecordEntity();
        updateRecord.setId(record.getId());
        updateRecord.setTradeStatus(PlayletTradeStatus.REFUND.getStatus());
        playletLandpageFormRecordService.updateById(updateRecord);
        return ResultBuilder.success();
    }
}
