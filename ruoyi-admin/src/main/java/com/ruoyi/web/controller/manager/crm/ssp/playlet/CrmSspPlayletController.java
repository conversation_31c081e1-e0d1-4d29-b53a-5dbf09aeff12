package com.ruoyi.web.controller.manager.crm.ssp.playlet;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.vo.playlet.PlayletAccountVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 短剧账号管理
 * <AUTHOR>
 * @date 2023/7/7 16:51
 */
@RestController
@RequestMapping("playlet")
public class CrmSspPlayletController {

    @Autowired
    private AccountService accountService;

    /**
     * 获取所有短剧账号列表
     */
    @GetMapping("getPlayletAccountList")
    public Result<List<PlayletAccountVO>> getPlayletAccountList() {
        List<Account> accounts = accountService.selectSspPlayletAccountList();
        return ResultBuilder.success(BeanUtil.copyToList(accounts, PlayletAccountVO.class));
    }
}
