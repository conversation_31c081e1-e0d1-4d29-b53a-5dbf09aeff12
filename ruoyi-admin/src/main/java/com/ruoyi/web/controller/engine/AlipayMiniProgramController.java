package com.ruoyi.web.controller.engine;

import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.service.domain.AlipayDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 支付宝小程序接口(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/01/22
 */
@Slf4j
@RestController
@RequestMapping("/alipay/miniProgram")
public class AlipayMiniProgramController {

    @Autowired
    private AlipayDomainService alipayDomainService;

    /**
     * 支付宝环境监控
     */
    @CrossOrigin
    @GetMapping("/stat")
    public String stat() {
        try {
            String referer = ServletUtils.getRequest().getHeader("referer");
            String userAgent = ServletUtils.getRequest().getHeader("User-Agent");
            String domain = UrlUtils.extractDomain(referer);

            if (alipayDomainService.isAlipayEnvironment(userAgent)) {
                alipayDomainService.updateDomainRecentlyRequestTime(domain);
            }
        } catch (Exception ignored) {}
        return "";
    }
}
