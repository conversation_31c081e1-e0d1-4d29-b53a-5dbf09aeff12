package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.QwtfFriendStatusEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.bo.landpage.QwtfLandpageFormRecordSelectBo;
import com.ruoyi.system.entity.datashow.QwtfLandpageFormRecordExcel;
import com.ruoyi.system.entity.landpage.QwtfLandpageFormRecordEntity;
import com.ruoyi.system.req.datashow.QwtfLandpageFromRecordReq;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.landpage.QwtfLandpageFormRecordService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.vo.advert.AdvertSelectVO;
import com.ruoyi.system.vo.app.AppSelectVO;
import com.ruoyi.system.vo.datashow.QwtfLandpageFormRecordVO;
import com.ruoyi.system.vo.slot.SlotSelectVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.EasyExcelUtils.exportExcel;

/**
 * [CRM后台]企微囤粉转化记录
 *
 * <AUTHOR>
 * @date 2023-09-22
 */
@Slf4j
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/landpage/qwtf")
public class QwtfLandpageFormRecordController extends BaseController {

    @Autowired
    private QwtfLandpageFormRecordService qwtfLandpageFormRecordService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertService advertService;

    /**
     * 广告下拉列表
     */
    @GetMapping("/advertList")
    public Result<List<AdvertSelectVO>> advertList() {
        List<Long> advertIds = qwtfLandpageFormRecordService.selectTotalAdvertIds();
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        return ResultBuilder.success(advertIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(advertId -> new AdvertSelectVO(advertId, advertNameMap.get(advertId))).collect(Collectors.toList()));
    }

    /**
     * 媒体下拉列表
     */
    @GetMapping("/appList")
    public Result<List<AppSelectVO>> appList() {
        List<Long> appIds = qwtfLandpageFormRecordService.selectTotalAppIds();
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        return ResultBuilder.success(appIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(appId -> new AppSelectVO(appId, appNameMap.get(appId))).collect(Collectors.toList()));
    }

    /**
     * 广告位下拉列表
     */
    @GetMapping("/slotList")
    public Result<List<SlotSelectVO>> slotList() {
        List<Long> slotIds = qwtfLandpageFormRecordService.selectTotalSlotIds();
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(slotIds);
        return ResultBuilder.success(slotIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(slotId -> new SlotSelectVO(slotId, slotNameMap.get(slotId))).collect(Collectors.toList()));
    }

    /**
     * 查询落地页单记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<QwtfLandpageFormRecordVO> list(QwtfLandpageFromRecordReq req) {
        // 查询列表
        QwtfLandpageFormRecordSelectBo param = buildQueryParam(req);
        startPage();
        List<QwtfLandpageFormRecordEntity> list = qwtfLandpageFormRecordService.selectList(param);

        // 补充信息
        Map<Long, String> appNameMap = appService.selectAppNameMap(ListUtils.mapToList(list, QwtfLandpageFormRecordEntity::getAppId));
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(ListUtils.mapToList(list, QwtfLandpageFormRecordEntity::getSlotId));
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(ListUtils.mapToList(list, QwtfLandpageFormRecordEntity::getAdvertId));

        // 构造结果
        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            QwtfLandpageFormRecordVO dataVo = BeanUtil.copyProperties(record, QwtfLandpageFormRecordVO.class);
            dataVo.setAppName(appNameMap.get(record.getAppId()));
            dataVo.setSlotName(slotNameMap.get(record.getSlotId()));
            dataVo.setAdvertName(advertNameMap.get(record.getAdvertId()));
            return dataVo;
        }));
    }

    /**
     * 导出落地页单记录列表
     */
    @Log(title = "落地页单记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(QwtfLandpageFromRecordReq req) {
        // 查询列表
        QwtfLandpageFormRecordSelectBo param = buildQueryParam(req);
        List<QwtfLandpageFormRecordEntity> list = qwtfLandpageFormRecordService.selectList(param);

        // 补充信息
        Map<Long, String> appNameMap = appService.selectAppNameMap(ListUtils.mapToList(list, QwtfLandpageFormRecordEntity::getAppId));
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(ListUtils.mapToList(list, QwtfLandpageFormRecordEntity::getSlotId));
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(ListUtils.mapToList(list, QwtfLandpageFormRecordEntity::getAdvertId));

        // 导出Excel
        List<QwtfLandpageFormRecordExcel> excelData = list.stream().map(record -> {
            QwtfLandpageFormRecordExcel dataVo = BeanUtil.copyProperties(record, QwtfLandpageFormRecordExcel.class);
            dataVo.setAppName(appNameMap.get(record.getAppId()));
            dataVo.setSlotName(slotNameMap.get(record.getSlotId()));
            dataVo.setAdvertName(advertNameMap.get(record.getAdvertId()));
            dataVo.setFriendStatusStr(QwtfFriendStatusEnum.getDescByStatus(record.getFriendStatus()));
            return dataVo;
        }).collect(Collectors.toList());
        return AjaxResult.success(exportExcel("落地页表单", excelData, QwtfLandpageFormRecordExcel.class));
    }

    /**
     * 构造查询条件
     *
     * @param req 参数
     * @return 查询条件
     */
    private QwtfLandpageFormRecordSelectBo buildQueryParam(QwtfLandpageFromRecordReq req) {
        QwtfLandpageFormRecordSelectBo param = new QwtfLandpageFormRecordSelectBo();
        param.setAdvertIds(req.getAdvertIds());
        param.setStartDate(req.getStartDate());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        param.setPhone(req.getPhone());
        param.setLandpageUrl(req.getLandpageUrl());
        param.setFriendStatus(req.getFriendStatus());
        param.setSlotIds(req.getSlotIds());
        param.setAppIds(req.getAppIds());
        return param;
    }
}
