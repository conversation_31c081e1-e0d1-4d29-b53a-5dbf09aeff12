package com.ruoyi.web.controller.manager.crm.ssp.publisher;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.enums.contract.ContractStatusEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.account.OaStaffBo;
import com.ruoyi.system.bo.invoice.InvoiceSumBO;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.finance.AccountRevenueEntity;
import com.ruoyi.system.entity.accounttag.AccountTagRelationEntity;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.contract.ContractEntity;
import com.ruoyi.system.entity.datashow.AppData;
import com.ruoyi.system.entity.tagmanager.TagManagerEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.account.OaStaffManger;
import com.ruoyi.system.req.account.AccountMangerReq;
import com.ruoyi.system.req.account.AccountReq;
import com.ruoyi.system.req.account.AccountTagUpdateReq;
import com.ruoyi.system.service.accounttag.AccountTagRelationService;
import com.ruoyi.system.service.contract.ContractService;
import com.ruoyi.system.service.datasource.AppDataService;
import com.ruoyi.system.service.finance.AccountRevenueService;
import com.ruoyi.system.service.invoice.InvoiceService;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import com.ruoyi.system.vo.account.AccountVO;
import com.ruoyi.system.vo.account.PrepayAccountVO;
import com.ruoyi.system.vo.tagmanager.TagListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.OaConstants.PERMISSION_ASSIGN_MANAGER;
import static com.ruoyi.common.enums.account.AccountRelationType.BD_MANAGER;
import static com.ruoyi.common.enums.account.AccountRelationType.OPERATION_MANAGER;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * 账号Controller
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/account")
public class AccountController extends BaseController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AppDataService appDataService;

    @Autowired
    private AccountRelationService accountRelationService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private OaStaffManger oaStaffManger;

    @Autowired
    private AccountRevenueService accountRevenueService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private ContractService contractService;

    @Autowired
    private AccountTagRelationService accountTagRelationService;

    @Autowired
    private TagManagerService tagManagerService;

    /**
     * 入库客户管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(AccountReq req) {
        Account param = BeanUtil.copyProperties(req, Account.class);
        param.setMainType(AccountMainType.PUBLISHER.getType());
        param.setSearchValue(req.getAppSearch());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));

        // 媒体模糊查询
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            App app = new App();
            app.setSearchValue(param.getSearchValue());
            List<Long> accountIds = appService.selectAccountIdList(app);
            if (CollectionUtils.isEmpty(accountIds)) {
                return getDataTable(Collections.emptyList());
            }
            param.setIds(accountIds);
        }
        // 负责人查询
        if (CollectionUtils.isNotEmpty(req.getManagerIds())) {
            List<Long> accountIds = accountRelationService.selectBySrcAccountIds(req.getManagerIds());
            if (CollectionUtils.isEmpty(accountIds)) {
                return getDataTable(Collections.emptyList());
            }
            param.setIds(accountIds);
        }
        //合同状态条件查询
        if(NumberUtils.isNonNullAndGtZero(req.getContractStatus())){
            List<Long> accountIds = contractService.selectAccountIdsByLatestContractStatus(req.getContractStatus());
            if(CollectionUtils.isEmpty(accountIds)){
                return getDataTable(Collections.emptyList());
            }
            if (CollectionUtils.isEmpty(param.getIds())) {
                param.setIds(accountIds);
            } else {
                param.getIds().retainAll(accountIds);
            }
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return getDataTable(Collections.emptyList());
            }
            if (null == param.getIds()) {
                param.setIds(permission.getValues());
            } else {
                param.getIds().retainAll(permission.getValues());
            }
            if (CollectionUtils.isEmpty(param.getIds())) {
                return getDataTable(Collections.emptyList());
            }
        }

        startPage();
        List<Account> accounts = accountService.selectAccountList(param);
        if (CollectionUtils.isEmpty(accounts)) {
            return getDataTable(Collections.emptyList());
        }

        List<Long> accountIds = accounts.stream().map(Account::getId).collect(Collectors.toList());
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());

        // 媒体账号-媒体数量映射
        Map<Long, Integer> appCountMap = appService.groupByAccountId(accountIds);
        // 媒体账号-广告位数量映射
        Map<Long, Integer> slotCountMap = slotService.groupByAccountId(accountIds);
        // 媒体账号-媒体数据汇总映射
        Map<Long, AppData> appDataMap = appDataService.groupByAccountId(accountIds, yesterday);
        // 媒体账号-负责人映射
        Map<Long, String> managerNameMap = accountService.selectManagerMap(accountIds);
        Map<Long, Map<Integer, List<Long>>> managerMap = accountRelationService.selectMapByDestAccountIds(accountIds);
        //查询最新发票信息
        List<ContractEntity> contractEntities = contractService.selectLatestContractByAccountIds(accountIds);
        Map<Long, ContractEntity> contractEntityMap = contractEntities.stream().collect(Collectors.toMap(ContractEntity::getAccountId, Function.identity()));
        // 当前用户信息
        LoginUser user = SecurityUtils.getLoginUser();
        OaStaffBo staff = oaStaffManger.selectByEmail(user.getEmail());
        // 指派负责人权限
        Integer canAssign = user.isAdmin() || staff.getOaPermissionKeys().contains(PERMISSION_ASSIGN_MANAGER) ? 1 : 0;

        // 构造返回列表
        return getDataTable(PageInfoUtils.dto2Vo(accounts, account -> {
            Long accountId = account.getId();

            AccountVO accountVO = new AccountVO();
            accountVO.setId(accountId);
            accountVO.setEmail(account.getEmail());
            accountVO.setCompanyName(account.getCompanyName());
            accountVO.setAppCount(appCountMap.getOrDefault(accountId, 0));
            accountVO.setSlotCount(slotCountMap.getOrDefault(accountId, 0));
            accountVO.setManagerName(managerNameMap.getOrDefault(accountId, ""));
            accountVO.setGmtCreate(account.getGmtCreate());

            AppData appData = appDataMap.get(accountId);
            if (null != appData) {
                accountVO.setYdaySlotReqUv(appData.getSlotRequestUv());
                accountVO.setYdayAppRevenue(appData.getAppRevenue());
            } else {
                accountVO.setYdaySlotReqUv(0);
                accountVO.setYdayAppRevenue(0L);
            }
            // 负责人
            Map<Integer, List<Long>> map = managerMap.getOrDefault(accountId, Collections.emptyMap());
            accountVO.setBdManagerIds(map.getOrDefault(BD_MANAGER.getType(),Collections.emptyList()));
            accountVO.setOperationManagerIds(map.getOrDefault(OPERATION_MANAGER.getType(),Collections.emptyList()));
            ContractEntity contractEntity = contractEntityMap.get(accountId);
            if(Objects.nonNull(contractEntity)){
                accountVO.setContractStatus(ContractStatusEnum.getContractStatusByDate(contractEntity.getEndDate()));
                if(Objects.equals(accountVO.getContractStatus(),ContractStatusEnum.ENDING.getStatus())){
                    accountVO.setDateDiff((int)DateUtil.between(new Date(), contractEntity.getEndDate(), DateUnit.DAY));
                }
            }
            // 指派负责人权限
            accountVO.setCanAssign(canAssign);

            return accountVO;
        }));
    }

    /**
     * 获取账号详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(accountService.selectAccountById(id));
    }

    /**
     * 指派负责人
     */
    @PostMapping(value = "/assignManager")
    public AjaxResult assignManager(@RequestBody @Validated AccountMangerReq req) {
        OaStaffBo staff = oaStaffManger.selectByEmail(SecurityUtils.getLoginUser().getEmail());
        if (!SecurityUtils.isAdmin() && !staff.getOaPermissionKeys().contains(PERMISSION_ASSIGN_MANAGER)) {
            return AjaxResult.error("暂无操作权限");
        }
        if (CollectionUtils.isEmpty(req.getBdManagerIds()) || CollectionUtils.isEmpty(req.getOperationManagerIds())) {
            return AjaxResult.error("参数错误");
        }
        accountRelationService.batchUpdateRelation(req.getBdManagerIds(), req.getAccountId(), BD_MANAGER.getType());
        accountRelationService.batchUpdateRelation(req.getOperationManagerIds(), req.getAccountId(), OPERATION_MANAGER.getType());
        return AjaxResult.success();
    }

    /**
     * 获取CRM账户列表
     */
    @GetMapping(value = "/getCrmAccountList")
    public AjaxResult getCrmAccountList() {
        return AjaxResult.success(accountService.getCrmAccountList());
    }

    /**
     * 更新账号标签
     * @param req
     * @return
     */
    @PostMapping("updateTag")
    public Result<Boolean> updateTag(@RequestBody @Validated AccountTagUpdateReq req){
        //删除所有标签
        accountTagRelationService.deleteByAccountIdAndType(req.getAccountId(), req.getTagType());
        List<AccountTagRelationEntity> insertList = req.getTagIds().stream().map(tagId -> {
            AccountTagRelationEntity entity = new AccountTagRelationEntity();
            entity.setTagId(tagId);
            entity.setAccountId(req.getAccountId());
            entity.setTagType(req.getTagType());
            return entity;
        }).collect(Collectors.toList());
        //新增标签关联
        return ResultBuilder.success(accountTagRelationService.batchInsert(insertList));
    }

    /**
     * 获取账号标签列表
     * @param accountId
     * @param tagType
     * @return
     */
    @GetMapping("tagList")
    public Result<List<TagListVO>> tagList(@Validated @NotNull(message = "账号id不能为空") Long accountId,@NotNull(message = "标签类型不能为空") Integer tagType){
        List<Long> tagIds = accountTagRelationService.selectTagIdsByAccountType(accountId, tagType);
        List<TagManagerEntity> subTagEntities = tagManagerService.selectByIds(tagIds);
        Map<Long, List<TagManagerEntity>> tagMap = subTagEntities.stream().sorted(Comparator.comparing(TagManagerEntity::getTagSort)).collect(Collectors.groupingBy(TagManagerEntity::getParentId));

        List<Long> parentIds = subTagEntities.stream().map(TagManagerEntity::getParentId).collect(Collectors.toList());
        List<TagManagerEntity> parentTags = tagManagerService.selectByIds(parentIds);
        List<TagListVO> result = parentTags.stream().map(parent -> {
            TagListVO vo = BeanUtil.copyProperties(parent, TagListVO.class);
            List<TagManagerEntity> sub = tagMap.get(parent.getId());
            List<TagListVO> tagListVOS = BeanUtil.copyToList(sub, TagListVO.class);
            vo.setSubTags(tagListVOS);
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(result);
    }

    /**
     * 预付款账号列表
     */
    @GetMapping("prepayAccount")
    public Result<List<PrepayAccountVO>> prepayAccount(){
        List<Long> accountIds = null;
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return ResultBuilder.success(Collections.emptyList());
            }
            accountIds = permission.getValues();
        }
        List<AccountRevenueEntity> accountRevenueEntities = accountRevenueService.selectPrepayAccountRevenue(accountIds);

        accountIds = accountRevenueEntities.stream().map(AccountRevenueEntity::getAccountId).collect(Collectors.toList());
        Map<Long, String> accountCompanyNameMap = accountService.selectCompanyNameMap(accountIds);
        List<InvoiceSumBO> invoiceSumBOS = invoiceService.sumInvoice(accountIds);
        Map<Long, Integer> accountInvoiceMap = invoiceSumBOS.stream().filter(Objects::nonNull).collect(Collectors.toMap(InvoiceSumBO::getAccountId, InvoiceSumBO::getInvoiceAmountSum));

        List<PrepayAccountVO> accountVOList = accountRevenueEntities.stream().map(account -> {
            PrepayAccountVO vo = BeanUtil.copyProperties(account, PrepayAccountVO.class);
            vo.setCompanyName(accountCompanyNameMap.getOrDefault(account.getAccountId(), ""));
            vo.setDebtAmount(account.getPrepayAmount() - accountInvoiceMap.getOrDefault(account.getAccountId(), 0));
            return vo;
        }).collect(Collectors.toList());

        return ResultBuilder.success(accountVOList);
    }
}
