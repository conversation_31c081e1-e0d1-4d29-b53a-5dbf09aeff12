package com.ruoyi.web.controller.manager.crm.dsp.traffic;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.traffic.TrafficPackageListBo;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.traffic.TrafficPackageEntity;
import com.ruoyi.system.entity.traffic.TrafficPackageItemEntity;
import com.ruoyi.system.req.traffic.TrafficPackageListReq;
import com.ruoyi.system.req.traffic.TrafficPackageReq;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.traffic.AdvertOrientTrafficService;
import com.ruoyi.system.service.traffic.TrafficPackageItemService;
import com.ruoyi.system.service.traffic.TrafficPackageService;
import com.ruoyi.system.vo.advert.AdvertBaseInfoVo;
import com.ruoyi.system.vo.advert.AdvertSelectVO;
import com.ruoyi.system.vo.app.AppSelectVO;
import com.ruoyi.system.vo.slot.SlotBaseInfoVO;
import com.ruoyi.system.vo.traffic.TrafficPackageListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流量包管理Controller
 *
 * <AUTHOR>
 * @date 2022-08-23
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/traffic/trafficPackage")
public class TrafficPackageController extends BaseController {

    @Autowired
    private TrafficPackageService trafficPackageService;

    @Autowired
    private TrafficPackageItemService trafficPackageItemService;

    @Autowired
    private AdvertOrientTrafficService advertOrientTrafficService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    /**
     * 媒体下拉列表
     */
    @GetMapping("/appList")
    public Result<List<AppSelectVO>> appList() {
        List<Long> appIds = trafficPackageItemService.selectTotalAppIds();
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        return ResultBuilder.success(appIds.stream().sorted(Comparator.reverseOrder()).map(appId -> new AppSelectVO(appId, appNameMap.get(appId))).collect(Collectors.toList()));
    }

    /**
     * 广告下拉列表
     */
    @GetMapping("/advertList")
    public Result<List<AdvertSelectVO>> advertList() {
        List<Long> advertIds = advertOrientTrafficService.selectTotalAdvertIds();
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        return ResultBuilder.success(advertIds.stream().sorted(Comparator.reverseOrder()).map(advertId -> new AdvertSelectVO(advertId, advertNameMap.get(advertId))).collect(Collectors.toList()));
    }

    /**
     * 查询流量包列表
     */
    @GetMapping("list")
    public TableDataInfo<TrafficPackageListVO> list(TrafficPackageListReq req) {
        // 媒体查询
        if (CollectionUtils.isNotEmpty(req.getAppIds())) {
            req.setIds(mergeParamIds(req.getIds(), trafficPackageItemService.selectTrafficPackageIdsByAppIds(req.getAppIds())));
            if (CollectionUtils.isEmpty(req.getIds())) {
                return getDataTable(Collections.emptyList());
            }
        }
        // 广告查询
        if (CollectionUtils.isNotEmpty(req.getAdvertIds())) {
            req.setIds(mergeParamIds(req.getIds(), advertOrientTrafficService.selectTrafficPackageIdsByAdvertIds(req.getAdvertIds())));
            if (CollectionUtils.isEmpty(req.getIds())) {
                return getDataTable(Collections.emptyList());
            }
        }

        startPage();
        List<TrafficPackageListBo> list = trafficPackageService.selectList(req);

        List<Long> trafficPackageIds = ListUtils.mapToList(list, TrafficPackageListBo::getId);
        Map<Long, Integer> trafficAdvertMap = advertOrientTrafficService.countByTrafficPackageId(trafficPackageIds);
        Map<Long, List<Long>> trafficSlotMap = trafficPackageItemService.selectTrafficSlotMap(trafficPackageIds);
        Map<Long, String> operatorNameMap = accountService.selectAccountContactMap(ListUtils.mapToList(list, TrafficPackageListBo::getOperatorId));
        return getDataTable(PageInfoUtils.dto2Vo(list, pkg -> {
            TrafficPackageListVO pkgVO = BeanUtil.copyProperties(pkg, TrafficPackageListVO.class);
            pkgVO.setAdvertCount(trafficAdvertMap.getOrDefault(pkg.getId(), 0));
            pkgVO.setSlotIds(trafficSlotMap.get(pkg.getId()));
            pkgVO.setOperatorName(operatorNameMap.get(pkg.getOperatorId()));
            return pkgVO;
        }));
    }

    /**
     * 新增流量包
     */
    @Log(title = "流量包", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TrafficPackageReq req) {
        if (StringUtils.isBlank(req.getName())) {
            return AjaxResult.error("流量包名称不能为空");
        }
        if (trafficPackageService.isNameDuplicate(req.getName())) {
            return AjaxResult.error("流量包名称重复");
        }
        if (CollectionUtils.isEmpty(req.getSlotIds())) {
            return AjaxResult.error("广告位不能为空");
        }
        return toAjax(trafficPackageService.insert(req));
    }

    /**
     * 更新流量包
     */
    @Log(title = "流量包", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult update(@RequestBody TrafficPackageReq req) {
        if (CollectionUtils.isEmpty(req.getSlotIds())) {
            return AjaxResult.error("广告位不能为空");
        }
        TrafficPackageEntity pkg = trafficPackageService.selectById(req.getId());
        if (null == pkg) {
            return AjaxResult.error("未查询到该流量包");
        }
        boolean result = trafficPackageService.update(req);
        refreshAdvertCache(req.getId());
        return toAjax(result);
    }

    /**
     * 查询定向了该流量的广告信息
     */
    @GetMapping("getAdvert")
    public TableDataInfo<AdvertBaseInfoVo> getAdvert(@RequestParam("trafficPackageId") Long trafficPackageId) {
        if (null == trafficPackageId) {
            return getDataTable(Collections.emptyList());
        }
        startPage();
        List<Long> advertIds = advertOrientTrafficService.selectAdvertIdByTrafficPackageId(trafficPackageId);
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        return getDataTable(PageInfoUtils.dto2Vo(advertIds, advertId -> {
            AdvertBaseInfoVo advert = new AdvertBaseInfoVo();
            advert.setAdvertId(advertId);
            advert.setAdvertName(advertNameMap.get(advertId));
            return advert;
        }));
    }

    /**
     * 查询流量包的广告位信息
     */
    @GetMapping("getSlot")
    public TableDataInfo<SlotBaseInfoVO> getSlot(@RequestParam("trafficPackageId") Long trafficPackageId) {
        if (null == trafficPackageId) {
            return getDataTable(Collections.emptyList());
        }
        startPage();
        List<TrafficPackageItemEntity> list = trafficPackageItemService.selectList(trafficPackageId);
        Map<Long, Slot> slotMap = slotService.selectSlotAppMapByIds(ListUtils.mapToList(list, TrafficPackageItemEntity::getSlotId));
        return getDataTable(PageInfoUtils.dto2Vo(list, item -> BeanUtil.copyProperties(slotMap.get(item.getSlotId()), SlotBaseInfoVO.class)));
    }

    /**
     * 刷新流量包对应的广告缓存
     */
    private void refreshAdvertCache(Long trafficPackageId) {
        if (null == trafficPackageId) {
            return;
        }
        List<Long> advertIds = advertOrientTrafficService.selectAdvertIdByTrafficPackageId(trafficPackageId);
        refreshCacheService.sendRefreshAdvertCacheMsg(advertIds);
    }
}
