package com.ruoyi.web.controller.manager.wis.agent.finance;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserRechargeRecordService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.agent.AgentClientFinanceBaseInfoVO;
import com.ruoyi.system.vo.agent.AgentFinanceBaseInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * [代理商平台]财务信息
 *
 * <AUTHOR>
 * @date 2022/10/27
 */
@RequestMapping("/wis/agent/fiance")
@RestController
public class WisClientFinanceController {

    @Autowired
    private AgentService agentService;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private AdvertiserRechargeRecordService advertiserRechargeRecordService;

    @Autowired
    private AdvertiserConsumeRecordService advertiserConsumeRecordService;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Autowired
    private WhitelistService whitelistService;

    /**
     * 代理商财务信息
     */
    @GetMapping(value = "/agentFianceInfo")
    public Result<AgentFinanceBaseInfoVO> getAgentFianceInfo() {
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return ResultBuilder.success(new AgentFinanceBaseInfoVO());
        }

        AgentFinanceBaseInfoVO finance = new AgentFinanceBaseInfoVO();
        finance.setTotalBalance(advertiserBalanceService.selectTotalAmountByAccountId(user.getCrmAccountId()));
        finance.setTransferAmount(advertiserRechargeRecordService.sumBySourceAccountId(user.getCrmAccountId()));
        return ResultBuilder.success(finance);
    }

    /**
     * 广告主财务信息
     */
    @GetMapping(value = "/advertiserBalance")
    public Result<AgentClientFinanceBaseInfoVO> getAdvertiserBalance() {
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return ResultBuilder.success(new AgentClientFinanceBaseInfoVO());
        }

        List<Long> advertiserIds = agentService.selectAdvertiserIdsByAgent(user.getCrmAccountId());
        // 调整前的总余额
        Integer totalBalance = advertiserBalanceService.sumTotalAmountByAccountIds(advertiserIds);
        // 调整前的消费总金额
        Integer consumeAmount = advertiserConsumeRecordService.sumConsumeAmountByAccountIds(advertiserIds);
        // 离线数据订正后的差额总和 offset=订正后的值-原值
        Integer consumeOffset = dspAdvertiserConsumeRecordService.advertiserConsumeOffsetSum(advertiserIds);

        AgentClientFinanceBaseInfoVO finance = new AgentClientFinanceBaseInfoVO();
        finance.setTotalBalance(totalBalance - consumeOffset);
        finance.setConsumeAmount(consumeAmount + consumeOffset);

        // 离线数据广告主剔除今日数据
        Date today = DateUtil.beginOfDay(new Date());
        advertiserIds.retainAll(whitelistService.list(OFFLINE_DATA_ADVERTISER, Long.class));
        advertiserIds.forEach(advertiserId -> {
            Optional.ofNullable(advertiserConsumeRecordService.selectByAccountIdAndDate(advertiserId, today)).ifPresent(data -> {
                Integer todayConsumeAmount = NumberUtils.defaultInt(data.getConsumeAmount());
                finance.setTotalBalance(finance.getTotalBalance() + todayConsumeAmount);
                finance.setConsumeAmount(finance.getConsumeAmount() - todayConsumeAmount);
            });
        });
        return ResultBuilder.success(finance);
    }
}
