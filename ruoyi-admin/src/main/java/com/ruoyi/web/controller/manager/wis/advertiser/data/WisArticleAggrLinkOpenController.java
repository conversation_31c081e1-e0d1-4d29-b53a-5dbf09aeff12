package com.ruoyi.web.controller.manager.wis.advertiser.data;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.common.MapConfigEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.bo.landpage.article.ArticleListBo;
import com.ruoyi.system.bo.landpage.article.ArticleListParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.entity.system.SysConfig;
import com.ruoyi.system.req.landpage.article.ArticleAddReq;
import com.ruoyi.system.req.landpage.article.ArticleBatchAddReq;
import com.ruoyi.system.req.wis.article.WisArticleBatchEditReq;
import com.ruoyi.system.req.wis.article.WisArticleEditReq;
import com.ruoyi.system.req.wis.article.WisArticleListReq;
import com.ruoyi.system.req.wis.article.WisArticleStopReq;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkService;
import com.ruoyi.system.service.landpage.article.ArticleApiService;
import com.ruoyi.system.service.landpage.article.ArticleEditHistoryService;
import com.ruoyi.system.service.landpage.article.ArticleRefreshRecordService;
import com.ruoyi.system.service.landpage.article.ArticleService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.vo.landpage.article.ArticleBatchAddResultItemVO;
import com.ruoyi.system.vo.landpage.article.ArticleBatchAddResultVO;
import com.ruoyi.system.vo.wis.WisArticleEditVO;
import com.ruoyi.system.vo.wis.WisArticleStatisticDataVO;
import com.ruoyi.system.vo.wis.WisArticleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * [DSP后台]文章聚合链接管理(不登录)
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/open/wis/article")
public class WisArticleAggrLinkOpenController extends BaseController {

    @Autowired
    public TransactionTemplate transactionTemplate;

    @Autowired
    private ArticleService articleService;

    @Autowired
    private ArticleAggrLinkService articleAggrLinkService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ArticleEditHistoryService articleEditHistoryService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ArticleRefreshRecordService articleRefreshRecordService;

    @Autowired
    private ArticleApiService articleApiService;

    @Autowired
    private MapConfigService mapConfigService;

    @GetMapping("/whitelist/add")
    public Result<Void> addWhitelist(Long linkId) {
        if (null == linkId) {
            return ResultBuilder.fail("失败");
        }
        Map<Long, String> map = mapConfigService.getMap(MapConfigEnum.ARTICLE_OPEN_LINK, Long.class, String.class);
        if (map.containsKey(linkId)) {
            return ResultBuilder.success();
        }
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(linkId);
        if (null == link) {
            return ResultBuilder.fail("添加失败");
        }
        List<String> list = mapConfigService.getList(MapConfigEnum.ARTICLE_OPEN_LINK);
        list.add(linkId + "-" + getSignKey(link));

        SysConfig config = sysConfigService.selectByKey(MapConfigEnum.ARTICLE_OPEN_LINK.getKey());
        if (null == config) {
            config = new SysConfig();
            config.setConfigName(MapConfigEnum.ARTICLE_OPEN_LINK.getDesc());
            config.setConfigKey(MapConfigEnum.ARTICLE_OPEN_LINK.getKey());
            config.setConfigType("N");
            config.setConfigValue(JSON.toJSONString(list));
            sysConfigService.insertConfig(config);
            return ResultBuilder.success();
        }
        config.setConfigValue(JSON.toJSONString(list));
        sysConfigService.updateConfig(config);
        return ResultBuilder.success();
    }

    @GetMapping("/whitelist/remove")
    public Result<Void> remove(Long linkId) {
        if (null == linkId) {
            return ResultBuilder.fail("失败");
        }
        Map<Long, String> map = mapConfigService.getMap(MapConfigEnum.ARTICLE_OPEN_LINK, Long.class, String.class);
        if (!map.containsKey(linkId)) {
            return ResultBuilder.success();
        }
        mapConfigService.remove(MapConfigEnum.ARTICLE_OPEN_LINK, String.valueOf(linkId));
        return ResultBuilder.success();
    }

    /**
     * 查询文章列表
     */
    @GetMapping("/articleList")
    public TableDataInfo<WisArticleVO> articleList(WisArticleListReq req) {
        ArticleAggrLinkEntity link = getLink();
        if (null == link) {
            return getDataTable(Collections.emptyList());
        }
        req.setLinkId(link.getId());

        startPage();
        List<ArticleListBo> list = articleService.selectListWithData(buildArticleListQueryParam(req));
        return getDataTable(PageInfoUtils.dto2Vo(list, entity -> {
            WisArticleVO vo = BeanUtil.copyProperties(entity, WisArticleVO.class);
            vo.setIsStop(entity.getWeight() > 0 ? 0 : 1);
            return vo;
        }));
    }

    /**
     * 查询文章汇总数据
     */
    @GetMapping("/articleStatistics")
    public Result<WisArticleStatisticDataVO> articleStatistics(WisArticleListReq req) {
        ArticleAggrLinkEntity link = getLink();
        if (null == link) {
            return ResultBuilder.success();
        }
        req.setLinkId(link.getId());

        ArticleListParamBo param = buildArticleListQueryParam(req);
        ArticleListBo data = articleService.selectStatisticData(param);
        WisArticleStatisticDataVO result = BeanUtil.copyProperties(data, WisArticleStatisticDataVO.class);
        if (null != result) {
            result.setArticleRefreshTimes(articleRefreshRecordService.countBy(param));
        }
        return ResultBuilder.success(result);
    }

    /**
     * 新增文章
     */
    @PostMapping("/addArticle")
    public Result<Void> addArticle(@RequestBody ArticleAddReq req) {
        if (StringUtils.isBlank(req.getName())) {
            return ResultBuilder.fail("名称不能为空");
        }
        if (StringUtils.isBlank(req.getUrl())) {
            return ResultBuilder.fail("链接不能为空");
        }
        if (!StrUtil.startWith(req.getUrl(), "http")) {
            return ResultBuilder.fail("无效的链接格式");
        }
        if (null != req.getInitRequestPv() && req.getInitRequestPv() < 0) {
            return ResultBuilder.fail("初始阅读量不能为负数");
        }
        log.info("文章闪动, 新增文章, req={}", JSON.toJSONString(req));

        ArticleAggrLinkEntity link = getLink();
        if (null == link) {
            return ResultBuilder.fail("无效的页面");
        }
        req.setLinkId(link.getId());

        // 2024.2.6上线需求，放开广告主的链接的限制
        if (link.getGmtCreate().before(DateUtil.parseDate("2024-02-06"))) {
            return ResultBuilder.fail("历史创建的链接不可新增");
        }
        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(req.getLinkId());
        for (ArticleEntity article : existArticleList) {
            if (Objects.equals(article.getName(), req.getName())) {
                return ResultBuilder.fail("该名称已存在");
            }
            if (Objects.equals(article.getUrl(), req.getUrl())) {
                return ResultBuilder.fail("该文章链接已存在");
            }
        }

        ArticleEntity article = new ArticleEntity();
        article.setLinkId(req.getLinkId());
        article.setName(req.getName());
        article.setUrl(req.getUrl());
        article.setTargetRequestPv(NumberUtils.defaultInt(req.getTargetRequestPv()));
        article.setWeight(1); //默认1
        article.setInitRequestPv(req.getInitRequestPv());
        article.setOperatorId(0L);
        article.setOperatorName("");
        articleService.insert(article);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), req.getLinkId()));
        articleService.updateArticleProfileAsync(article.getId(), article.getUrl());
        return ResultBuilder.success();
    }

    /**
     * 编辑文章
     */
    @PostMapping("/editArticle")
    public Result<Void> editArticle(@RequestBody WisArticleEditReq req) {
        if (Objects.isNull(req.getId())) {
            return ResultBuilder.fail("文章ID不能为空");
        }
        if (StringUtils.isBlank(req.getName())) {
            return ResultBuilder.fail("名称不能为空");
        }
        if (null != req.getInitRequestPv() && req.getInitRequestPv() < 0) {
            return ResultBuilder.fail("初始阅读量不能为负数");
        }

        log.info("文章闪动, 编辑文章, req={}", JSON.toJSONString(req));

        ArticleAggrLinkEntity link = getLink();
        if (null == link) {
            return ResultBuilder.fail("无效的页面");
        }
        // 2024.2.6上线需求，放开广告主的链接的限制
        if (link.getGmtCreate().before(DateUtil.parseDate("2024-02-06"))) {
            return ResultBuilder.fail("历史创建的链接不可修改");
        }
        ArticleEntity articleEntity = articleService.selectById(req.getId());
        if (Objects.isNull(articleEntity) || !Objects.equals(articleEntity.getLinkId(), link.getId())) {
            return ResultBuilder.fail("文章不存在");
        }
        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(articleEntity.getLinkId());
        for (ArticleEntity article : existArticleList) {
            if (Objects.equals(article.getName(), req.getName()) && !Objects.equals(article.getId(), req.getId())) {
                return ResultBuilder.fail("该名称已存在");
            }
        }
        ArticleListParamBo bo = new ArticleListParamBo();
        bo.setIds(Lists.newArrayList(articleEntity.getId()));
        bo.setLinkId(articleEntity.getLinkId());
        List<ArticleListBo> articleListBos = articleService.selectListWithData(bo);
        if (CollectionUtils.isEmpty(articleListBos)) {
            return ResultBuilder.fail("文章不存在");
        }
        for (ArticleListBo articleListBo : articleListBos) {
            if (Objects.equals(articleListBo.getId(), req.getId()) && articleListBo.getRequestPv() >= req.getTargetRequestPv()) {
                return ResultBuilder.fail("该文章阅读数超出目标数，无法保存");
            }
        }

        ArticleEntity article = new ArticleEntity();
        article.setId(req.getId());
        article.setName(req.getName());
        article.setTargetRequestPv(NumberUtils.defaultInt(req.getTargetRequestPv()));
        article.setInitRequestPv(req.getInitRequestPv());
        article.setOperatorId(0L);
        article.setOperatorName("");
        articleService.updateById(article);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), articleEntity.getLinkId()));
        return ResultBuilder.success();
    }

    /**
     * 批量导入文章
     */
    @PostMapping("/batchAddArticle")
    public Result<ArticleBatchAddResultVO> batchAddArticle(@RequestBody ArticleBatchAddReq req) {
        log.info("文章闪动, 批量导入文章, req={}", JSON.toJSONString(req));
        ArticleAggrLinkEntity link = getLink();
        if (null == link) {
            return ResultBuilder.fail("无效的页面");
        }
        req.setLinkId(link.getId());
        // 2024.2.6上线需求，放开广告主的链接的限制
        if (link.getGmtCreate().before(DateUtil.parseDate("2024-02-06"))) {
            return ResultBuilder.fail("历史创建的链接不可批量导入文章");
        }
        if (CollectionUtils.isEmpty(req.getArticles())) {
            return ResultBuilder.fail("文章列表不能为空");
        }

        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(req.getLinkId());
        Set<String> existArticleNameSet = existArticleList.stream().map(ArticleEntity::getName).collect(Collectors.toSet());
        Set<String> existArticleUrlSet = existArticleList.stream().map(ArticleEntity::getUrl).collect(Collectors.toSet());
        Set<String> addArticleNameSet = new HashSet<>();
        Set<String> addArticleUrlSet = new HashSet<>();

        List<ArticleBatchAddResultItemVO> errList = new ArrayList<>();
        List<ArticleEntity> insertList = new ArrayList<>();
        for (ArticleAddReq article : req.getArticles()) {
            String errMsg = null;
            if (StringUtils.isBlank(article.getName())) {
                errMsg = "名称不能为空";
            } else if (StringUtils.isBlank(article.getUrl())) {
                errMsg = "链接不能为空";
            } else if (!StrUtil.startWith(article.getUrl(), "http")) {
                errMsg = "无效的链接格式";
            } else if (existArticleNameSet.contains(article.getName())) {
                errMsg = "名称已存在";
            } else if (existArticleUrlSet.contains(article.getUrl())) {
                errMsg = "文章链接已存在";
            } else if (addArticleNameSet.contains(article.getName())) {
                errMsg = "名称已存在";
            } else if (addArticleUrlSet.contains(article.getUrl())) {
                errMsg = "文章链接已存在";
            }
            if (StringUtils.isBlank(errMsg)) {
                ArticleEntity entity = BeanUtil.copyProperties(article, ArticleEntity.class);
                entity.setWeight(1);
                entity.setTargetRequestPv(NumberUtils.defaultInt(entity.getTargetRequestPv()));
                entity.setOperatorId(0L);
                entity.setOperatorName("");
                insertList.add(entity);
                addArticleNameSet.add(entity.getName());
                addArticleUrlSet.add(entity.getUrl());
            } else {
                ArticleBatchAddResultItemVO errItem = BeanUtil.copyProperties(article, ArticleBatchAddResultItemVO.class);
                errItem.setErrMsg(errMsg);
                errList.add(errItem);
            }
        }
        articleService.batchInsert(insertList);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), req.getLinkId()));
        return ResultBuilder.success(new ArticleBatchAddResultVO(insertList.size(), errList.size(), errList));
    }

    /**
     * 批量修改
     */
    @PostMapping("/batchEditArticle")
    public Result<List<WisArticleEditVO>> batchEditArticle(@RequestBody WisArticleBatchEditReq req) {
        log.info("文章闪动, 批量修改, req={}", JSON.toJSONString(req));
        ArticleAggrLinkEntity link = getLink();
        if (null == link) {
            return ResultBuilder.fail("无效的页面");
        }
        req.setLinkId(link.getId());
        if (CollectionUtils.isEmpty(req.getArticles())) {
            return ResultBuilder.fail("文章列表不能为空");
        }
        List<String> articleNameList = req.getArticles().stream().map(WisArticleEditReq::getName).filter(Objects::nonNull).collect(Collectors.toList());
        if (articleNameList.size() != new HashSet<>(articleNameList).size()) {
            return ResultBuilder.fail("文章名称不能重复");
        }

        Date now = new Date();
        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(req.getLinkId());
        Map<String, Long> articleNameMap = existArticleList.stream().collect(Collectors.toMap(ArticleEntity::getName, ArticleEntity::getId, (v1, v2) -> v2));
        List<Long> articleIds = req.getArticles().stream().filter(article -> Objects.nonNull(article.getId())).map(WisArticleEditReq::getId).collect(Collectors.toList());
        if (articleIds.size() != req.getArticles().size()) {
            return ResultBuilder.fail("文章ID不能为空");
        }
        //校验文章是否到达目标阅读量
        ArticleListParamBo bo = new ArticleListParamBo();
        bo.setIds(articleIds);
        bo.setLinkId(req.getLinkId());
        List<ArticleListBo> articleListBos = articleService.selectListWithData(bo);
        Map<Long, Integer> articleIdAndRequestPvMap = articleListBos.stream().collect(Collectors.toMap(ArticleListBo::getId, ArticleListBo::getRequestPv, (v1, v2) -> v2));

        if (CollectionUtils.isEmpty(articleListBos)) {
            return ResultBuilder.fail("文章不存在");
        }

        List<WisArticleEditVO> vos = new ArrayList<>();
        for (WisArticleEditReq article : req.getArticles()) {
            Integer requestPv = NumberUtils.defaultInt(articleIdAndRequestPvMap.get(article.getId()), 0);
            if (null != article.getTargetRequestPv() && article.getTargetRequestPv() <= requestPv) {
                WisArticleEditVO vo = new WisArticleEditVO();
                vo.setId(article.getId());
                vo.setErrMessage("该文章阅读数超出目标数，无法保存");
                vos.add(vo);
            } else if (null != article.getInitRequestPv() && article.getInitRequestPv() < 0) {
                WisArticleEditVO vo = new WisArticleEditVO();
                vo.setId(article.getId());
                vo.setErrMessage("该文章初始阅读量为负数，无法保存");
                vos.add(vo);
            }
        }
        if(CollectionUtils.isNotEmpty(vos)){
            return ResultBuilder.success(vos);
        }

        for (WisArticleEditReq article : req.getArticles()) {
            if (StringUtils.isNotBlank(article.getName()) && articleNameMap.containsKey(article.getName())
                    && !Objects.equals(articleNameMap.get(article.getName()), article.getId())) {
                return ResultBuilder.fail("文章「" + article.getName() + "」已存在");
            }
            ArticleEntity updateRecord = new ArticleEntity();
            updateRecord.setId(article.getId());
            updateRecord.setName(article.getName());
            updateRecord.setInitRequestPv(article.getInitRequestPv());
            updateRecord.setTargetRequestPv(article.getTargetRequestPv());
            updateRecord.setOperatorId(0L);
            updateRecord.setOperatorName("");
            updateRecord.setOperatorTime(now);
            articleEditHistoryService.add(updateRecord);
            articleService.updateById(updateRecord);
        }
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), req.getLinkId()));
        return ResultBuilder.success();
    }

    /**
     * 文章撤单
     */
    @PostMapping("/articleStop")
    public Result<Boolean> articleStop(@RequestBody WisArticleStopReq req) {
        log.info("文章闪动, 文章撤单, req={}", JSON.toJSONString(req));
        ArticleAggrLinkEntity link = getLink();
        if (null == link) {
            return ResultBuilder.fail("无效的页面");
        }

        ArticleEntity article = articleService.selectById(req.getArticleId());
        if (null == req.getArticleId() || !Objects.equals(link.getId(), article.getLinkId())) {
            return ResultBuilder.fail("未查询到该文章");
        }
        ArticleEntity updateArticle = new ArticleEntity();
        updateArticle.setId(article.getId());
        updateArticle.setWeight(0);
        articleService.updateById(updateArticle);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), article.getLinkId()));

        // 查询当前阅读量
        GlobalThreadPool.longTimeExecutorService.execute(() -> {
            Integer requestPv = articleApiService.getArticleRealRequestPv(article.getUrl());
            articleService.updateActualRequestPv(article.getId(), requestPv);
        });
        return ResultBuilder.success(true);
    }

    /**
     * 构造查询参数
     *
     * @param req 请求参数
     * @return 查询参数
     */
    private ArticleListParamBo buildArticleListQueryParam(WisArticleListReq req) {
        ArticleListParamBo param = new ArticleListParamBo(req.getLinkId());
//        param.setName(req.getName());
//        param.setUrl(req.getUrl());
        param.setSearchKey(req.getName());
        param.setStartDate(req.getStartDate());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        return param;
    }

    /**
     * 获取链接ID,同时校验链接是否有效
     */
    private ArticleAggrLinkEntity getLink() {
        HttpServletRequest request = ServletUtils.getRequest();
        if (null == request) {
            return null;
        }
        Long linkId = Convert.toLong(request.getHeader("Link-Id"));  // 链接ID
        String key = request.getHeader("Key");  // 加密字符串

        if (null == linkId || StringUtils.isBlank(key)) {
            return null;
        }
        // 白名单校验
        if (!mapConfigService.getKeySet(MapConfigEnum.ARTICLE_OPEN_LINK, Long.class).contains(linkId)) {
            return null;
        }
        // 获取链接
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(linkId);
        if (!Objects.equals(key, getSignKey(link))) {
            return null;
        }
        return link;
    }

    /**
     * 获取加密Key
     */
    private String getSignKey(ArticleAggrLinkEntity link) {
        if (null == link) {
            return "";
        }
        return Md5Utils.hash(link.getId() + link.getKey() + "2024").substring(0, 17);
    }
}
