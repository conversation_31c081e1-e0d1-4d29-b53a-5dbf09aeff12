package com.ruoyi.web.controller.alipay;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayTradeCreateRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayTradeCreateResponse;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.req.alipay.AlipayCreateReq;
import com.ruoyi.system.req.alipay.AlipayTradeStatusCallbackReq;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.landpage.BaijiuLandpageFormRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Objects;

/**
 * 支付宝支付 controller
 *
 *
 * <AUTHOR>
 * @date 2023/4/24 10:58
 */
@Slf4j
@RestController
@RequestMapping("alipay")
public class AlipayController {

    @Autowired
    private AlipayClient alipayClient;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private BaijiuLandpageFormRecordService baijiuLandpageFormRecordService;

    @Autowired
    private StatService statService;

    /**
     * 创建订单
     */
    @PostMapping("create")
    public Result<String> create(@RequestBody @Validated AlipayCreateReq req) {
        //实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.trade.create.
        AlipayTradeCreateRequest request = new AlipayTradeCreateRequest();
        Landpage landpage = landpageCacheService.selectLandpageCache(req.getLpk());
        if(Objects.isNull(landpage)){
            throw new CustomException(ErrorCode.E110002);
        }
        JSONObject pageConfig = JSONObject.parseObject(landpage.getPageConfig());
        if(Objects.isNull(pageConfig)){
            throw new CustomException(ErrorCode.E110002);
        }
        //SDK 已经封装掉了公共参数，这里只需要传入业务参数。
        JSONObject bizContent = new JSONObject();
        //查询落地页配置，支付金额，支付商品
        bizContent.put("out_trade_no",StringUtils.join(DatePattern.PURE_DATETIME_FORMAT.format(new Date()), StringUtils.randomNumber(14)));
        bizContent.put("total_amount",pageConfig.getFloatValue("payMoney"));
        bizContent.put("subject",pageConfig.getString("orderTitle"));
        bizContent.put("buyer_id",req.getUserId());

        if(SpringEnvironmentUtils.isProd()){
            request.setNotifyUrl("http://land02.gamingtime.cn/alipay/callback");
        }else{
            request.setNotifyUrl("http://land02-test.gamingtime.cn/api/alipay/callback");
        }
        request.setBizContent(bizContent.toJSONString());
        try {
            AlipayTradeCreateResponse response = alipayClient.execute(request);
            //绑定订单号
            baijiuLandpageFormRecordService.updateTradeNo(req.getId(),response.getTradeNo());
            return ResultBuilder.success(response.getTradeNo());// 获取返回的tradeNO。
        } catch (AlipayApiException e) {
            log.error("创建支付宝订单异常,e:",e);
            throw new CustomException(ErrorCode.E152001);
        }
    }

    /**
     * 获取小程序用户id
     */
    @GetMapping("getUserId")
    public Result<String> getUserId(String code){
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setGrantType("authorization_code");
        request.setCode(code);
        try {
            AlipaySystemOauthTokenResponse response = alipayClient.execute(request);
            if(response.isSuccess()){
                return ResultBuilder.success(response.getUserId());
            }
        } catch (AlipayApiException e) {
            log.error("获取小程序userId失败,e:",e);
        }
        throw new CustomException(ErrorCode.E152002);
    }

    /**
     * 支付结果回调
     */
    @PostMapping("callback")
    public String callback( AlipayTradeStatusCallbackReq req){
        log.info("支付宝支付结果回调,req:{}",JSONObject.toJSONString(req));
        //订单号
        String tradeNo = req.getTrade_no();
        BaijiuLandpageFormRecord formRecord = baijiuLandpageFormRecordService.selectByTradeNo(tradeNo);

        //支付状态
        String tradeStatus = req.getTrade_status();
        int totalAmount = (int)(req.getTotal_amount() * 100); //分
        Date gmtPayment = req.getGmt_payment();
        BaijiuLandpageFormRecord record = new BaijiuLandpageFormRecord();
        record.setTradeNo(tradeNo);
        record.setTradeStatus(tradeStatus);
        record.setPayTime(gmtPayment);
        record.setAmount(totalAmount);
        Boolean success = baijiuLandpageFormRecordService.updateTradeStatus(record);

        if(Objects.isNull(formRecord)){
            return "success";
        }
        //上报支付数据
        if(BooleanUtils.isTrue(success) && !Objects.equals(formRecord.getTradeStatus(),"TRADE_SUCCESS") && Objects.equals(tradeStatus,"TRADE_SUCCESS")){
            String orderId = formRecord.getOrderId();
            statService.convertEvent(orderId, ConvType.PAY.getType());
        }

        return "success";
    }
}

