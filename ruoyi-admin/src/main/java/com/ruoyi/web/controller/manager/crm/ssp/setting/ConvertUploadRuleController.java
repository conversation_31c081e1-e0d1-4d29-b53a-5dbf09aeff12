package com.ruoyi.web.controller.manager.crm.ssp.setting;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.convert.ConvertUploadRuleEntity;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.req.manager.ConvertUploadRuleListReq;
import com.ruoyi.system.req.manager.ConvertUploadRuleReq;
import com.ruoyi.system.service.convert.ConvertUploadRuleService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.vo.manager.ConvertUploadRuleHistoryVO;
import com.ruoyi.system.vo.manager.ConvertUploadRuleVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 媒体转化上报规则Controller
 *
 * <AUTHOR>
 * @date 2022-10-19
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/ssp/setting/convertUploadRule")
public class ConvertUploadRuleController extends BaseController {

    @Autowired
    private SlotService slotService;

    @Autowired
    private ConvertUploadRuleService convertUploadRuleService;

    @Autowired
    private AccountService accountService;

    /**
     * 查询规则列表
     */
    @GetMapping("/list")
    public TableDataInfo<ConvertUploadRuleVO> list(ConvertUploadRuleListReq req) {
        // 广告位模糊查询
        if (StringUtils.isNotBlank(req.getSlotSearch())) {
            List<Long> slotIds = slotService.selectSlotIdsBySearchValue(req.getSlotSearch());
            if (CollectionUtils.isEmpty(slotIds)) {
                return getDataTable(Collections.emptyList());
            }
            req.setSlotIds(slotIds);
        }

        TableSupport.startPage();
        List<ConvertUploadRuleEntity> list = convertUploadRuleService.selectList(req);

        // 补充信息
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(ListUtils.mapToList(list, ConvertUploadRuleEntity::getSlotId));
        Map<Long, String> creatorNameMap = accountService.selectAccountContactMap(ListUtils.mapToList(list, ConvertUploadRuleEntity::getCreatorId));

        // 构造返回结果
        return getDataTable(PageInfoUtils.dto2Vo(list, rule -> {
            ConvertUploadRuleVO ruleVO = BeanUtil.copyProperties(rule, ConvertUploadRuleVO.class);
            ruleVO.setSlotName(slotNameMap.get(rule.getSlotId()));
            ruleVO.setCreatorName(creatorNameMap.get(rule.getCreatorId()));
            return ruleVO;
        }));
    }

    /**
     * 查询历史修改记录
     */
    @GetMapping("/history")
    public Result<List<ConvertUploadRuleHistoryVO>> history(Long slotId) {
        List<ConvertUploadRuleEntity> list = convertUploadRuleService.selectHistory(slotId);
        Map<Long, String> operatorNameMap = accountService.selectAccountContactMap(ListUtils.mapToList(list, ConvertUploadRuleEntity::getOperatorId));

        return ResultBuilder.success(list.stream().map(rule -> {
            ConvertUploadRuleHistoryVO ruleVO = BeanUtil.copyProperties(rule, ConvertUploadRuleHistoryVO.class);
            ruleVO.setOperatorName(operatorNameMap.get(rule.getOperatorId()));
            return ruleVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 新增规则
     */
    @Log(title = "媒体转化上报", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public Result<Void> add(@RequestBody ConvertUploadRuleReq req) {
        if (null == req.getRatio() || req.getRatio() < 0 || req.getRatio() > 100) {
            return ResultBuilder.fail("请输入有效的配置比例[0,100]");
        }
        Slot slot = slotService.selectSimpleSlotById(req.getSlotId());
        if (null == slot) {
            return ResultBuilder.fail("请输入有效的广告位ID");
        }
        ConvertUploadRuleEntity rule = convertUploadRuleService.selectBySlotId(req.getSlotId());
        if (null != rule) {
            return ResultBuilder.fail("该广告位已配置比例");
        }
        rule = new ConvertUploadRuleEntity();
        rule.setSlotId(req.getSlotId());
        rule.setRatio(req.getRatio());
        rule.setCreatorId(SecurityUtils.getLoginUser().getCrmAccountId());
        rule.setCreateTime(new Date());
        rule.setOperatorId(rule.getCreatorId());
        rule.setOperateTime(rule.getCreateTime());
        return ResultBuilder.result(convertUploadRuleService.insert(rule));
    }

    /**
     * 修改规则
     */
    @Log(title = "媒体转化上报", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public Result<Void> edit(@RequestBody ConvertUploadRuleReq req) {
        return ResultBuilder.result(convertUploadRuleService.update(req));
    }
}
