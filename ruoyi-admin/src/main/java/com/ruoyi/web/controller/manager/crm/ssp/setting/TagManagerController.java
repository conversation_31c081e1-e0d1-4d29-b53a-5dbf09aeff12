package com.ruoyi.web.controller.manager.crm.ssp.setting;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.TagManagerTypeEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.tagmanager.TagManagerEntity;
import com.ruoyi.system.req.tag.TagEditReq;
import com.ruoyi.system.req.tag.TagManagerReq;
import com.ruoyi.system.service.app.AppTagRelationService;
import com.ruoyi.system.service.landpage.LandpageLibraryService;
import com.ruoyi.system.service.tagmanager.DomainTagRelationService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import com.ruoyi.system.vo.tagmanager.TagListVO;
import com.ruoyi.system.vo.tagmanager.TagManagerVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 标签管理
 *
 * <AUTHOR>
 * @date 2022/9/23 11:05 上午
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/tag")
public class TagManagerController extends BaseController {

    @Autowired
    private TagManagerService tagManagerService;
    @Autowired
    private AppTagRelationService appTagRelationService;
    @Autowired
    private DomainTagRelationService domainTagRelationService;
    @Autowired
    private LandpageLibraryService landpageLibraryService;

    /**
     * 标签列表
     *
     * @return
     */
    @GetMapping("list")
    public Result<List<TagManagerVO>> list() {
        List<TagManagerEntity> appTagEntities = tagManagerService.selectAllTag();
        //<tagtype,<parentId,list>>
        Map<Integer, Map<Long, List<TagManagerEntity>>> tagMap = appTagEntities.stream().sorted(Comparator.comparing(TagManagerEntity::getTagSort)).collect(Collectors.groupingBy(TagManagerEntity::getTagType, Collectors.groupingBy(TagManagerEntity::getParentId)));
        List<TagManagerVO> resultList = new ArrayList<>();
        for (TagManagerTypeEnum typeEnum : TagManagerTypeEnum.values()) {
            TagManagerVO vo = new TagManagerVO();
            vo.setTagType(typeEnum.getType());
            vo.setTagTypeName(typeEnum.getDesc());
            vo.setTagList(Collections.emptyList());
            Map<Long, List<TagManagerEntity>> parentTagMap = tagMap.get(typeEnum.getType());
            if (MapUtils.isNotEmpty(parentTagMap)) {
                //0表示一级标签
                List<TagManagerEntity> parentTags = parentTagMap.get(0L);
                List<TagListVO> tagListVOS = parentTags.stream().map(parent -> {
                    TagListVO parentVo = BeanUtil.copyProperties(parent, TagListVO.class);
                    List<TagManagerEntity> subTags = parentTagMap.get(parent.getId());
                    if(CollectionUtils.isNotEmpty(subTags)){
                        List<TagListVO> subTagManagerVos = BeanUtil.copyToList(subTags, TagListVO.class);
                        parentVo.setSubTags(subTagManagerVos);
                    }

                    return parentVo;
                }).collect(Collectors.toList());
                vo.setTagList(tagListVOS);
            }
            resultList.add(vo);

        }

        return ResultBuilder.success(resultList);
    }

    /**
     * 根据类型获取标签列表
     * @param type
     * @see TagManagerTypeEnum
     * @return
     */
    @GetMapping("listByType")
    public Result<List<TagListVO>> listByType(@Validated @NotNull(message = "类型不能为空") Integer type){
        List<TagManagerEntity> tagManagerEntities = tagManagerService.selectAllTagByType(type);
        if(CollectionUtils.isEmpty(tagManagerEntities)){
            return ResultBuilder.success(Collections.EMPTY_LIST);
        }
        Map<Long, List<TagManagerEntity>> tagMap = tagManagerEntities.stream().sorted(Comparator.comparing(TagManagerEntity::getTagSort)).collect(Collectors.groupingBy(TagManagerEntity::getParentId));
        List<TagListVO> result = tagMap.get(0L).stream().map(parentTag -> {
            TagListVO vo = BeanUtil.copyProperties(parentTag, TagListVO.class);
            List<TagManagerEntity> subTags = tagMap.get(parentTag.getId());
            if(CollectionUtils.isNotEmpty(subTags)){
                List<TagListVO> tagListVOS = BeanUtil.copyToList(subTags, TagListVO.class);
                vo.setSubTags(tagListVOS);
            }

            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(result);
    }

    /**
     * 编辑标签
     *
     * @param req
     * @return
     */
    @PostMapping("edit")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> edit(@RequestBody @Validated TagManagerReq req) {

        checkAndDeleteTags(req);
        //新增二级标签
        List<TagManagerEntity> insertList = new ArrayList<>();
        List<TagManagerEntity> updateList = new ArrayList<>();
        //批量新增一级标签
        if(CollectionUtils.isEmpty(req.getTabList())){
            return ResultBuilder.success(true);
        }
        req.getTabList().forEach(tag->{
            TagManagerEntity entity = new TagManagerEntity();
            entity.setTagName(tag.getTagName());
            entity.setTagSort(0);
            entity.setTagType(req.getTagType());
            entity.setParentId(0L);
            if(NumberUtils.isNullOrLteZero(tag.getId())){
                //新增
                tagManagerService.insert(entity);
                if(CollectionUtils.isNotEmpty(tag.getSubTags())){
                    for(int index = 0;index<tag.getSubTags().size();index++){
                        TagEditReq subTag = tag.getSubTags().get(index);
                        TagManagerEntity subTagEntity = new TagManagerEntity();
                        subTagEntity.setParentId(entity.getId());
                        subTagEntity.setTagSort(index);
                        subTagEntity.setTagName(subTag.getTagName());
                        subTagEntity.setTagType(req.getTagType());
                        insertList.add(subTagEntity);
                    }
                }

            }else{
                //编辑
                entity.setId(tag.getId());
                updateList.add(entity);
                if(CollectionUtils.isNotEmpty(tag.getSubTags())){
                    for(int index = 0;index<tag.getSubTags().size();index++){
                        TagEditReq subTag = tag.getSubTags().get(index);

                        TagManagerEntity subTagEntity = new TagManagerEntity();
                        subTagEntity.setParentId(entity.getId());
                        subTagEntity.setTagSort(index);
                        subTagEntity.setTagName(subTag.getTagName());
                        subTagEntity.setTagType(req.getTagType());
                        if(Objects.nonNull(subTag.getId())){
                            subTagEntity.setId(subTag.getId());
                            updateList.add(subTagEntity);
                        }else{
                            insertList.add(subTagEntity);
                        }

                    }
                }

            }

        });

        tagManagerService.batchUpdate(updateList);
        tagManagerService.batchInsert(insertList);
        return ResultBuilder.success(true);

    }

    /**
     * 判断是否标签被占用
     * @param id
     * @param tagType 标签类型
     * @see TagManagerTypeEnum
     * @return
     */
    @GetMapping("checkUse")
    public Result<Boolean> checkUse(Long id,Integer tagType){
        int count = 0;
        if(Objects.equals(TagManagerTypeEnum.APP_TAG.getType(),tagType)){
            count = appTagRelationService.countByTagId(id);
        }else if(Objects.equals(TagManagerTypeEnum.DOMAIN_TAG.getType(),tagType)){
            count = domainTagRelationService.countByTagId(id);
        } else if (Objects.equals(TagManagerTypeEnum.LANDPAGE_TAG.getType(), tagType)) {
            List<Long> usedLandpageTagIds = getUsedLandpageTagIds();
            count = usedLandpageTagIds.contains(id) ? 1 : 0;
        }

        return ResultBuilder.success(count>0);
    }

    /**
     * 校验并删除未被使用的标签
     * @param req
     */
    private void checkAndDeleteTags(TagManagerReq req) {
        //查询该类型下所有的标签
        List<TagManagerEntity> tagManagerEntities = tagManagerService.selectAllTagByType(req.getTagType());
        if(CollectionUtils.isEmpty(tagManagerEntities)){
            return;
        }
        Map<Long, String> tagMap = tagManagerEntities.stream().collect(Collectors.toMap(TagManagerEntity::getId, TagManagerEntity::getTagName));
        List<Long> existTagIds = new ArrayList<>(tagMap.keySet());
        List<Long> updateTagIds = new ArrayList<>();
        req.getTabList().stream().filter(tag -> NumberUtils.isNonNullAndGtZero(tag.getId())).forEach(parentTag -> {
            updateTagIds.add(parentTag.getId());
            if (CollectionUtils.isNotEmpty(parentTag.getSubTags())) {
                parentTag.getSubTags().stream().filter(subTag -> NumberUtils.isNonNullAndGtZero(subTag.getId())).forEach(subTag -> {
                    updateTagIds.add(subTag.getId());
                });
            }
        });
        //过滤出需要删除的标签id列表
        existTagIds.removeAll(updateTagIds);
        //批量判断要删除的标签是否被使用
        List<Long> usedTagIds = new ArrayList<>();
        if(Objects.equals(TagManagerTypeEnum.APP_TAG.getType(),req.getTagType())){
            usedTagIds = appTagRelationService.selectTagIdsByTagIds(existTagIds);
        }else if(Objects.equals(TagManagerTypeEnum.DOMAIN_TAG.getType(),req.getTagType())){
            usedTagIds = domainTagRelationService.selectTagIdsByTagIds(existTagIds);
        } else if (Objects.equals(TagManagerTypeEnum.LANDPAGE_TAG.getType(), req.getTagType())) {
            List<Long> usedLandpageTagIds = getUsedLandpageTagIds();
            usedTagIds = (List<Long>) CollUtil.intersection(usedLandpageTagIds, existTagIds);
        }

        if (CollectionUtils.isNotEmpty(usedTagIds)) {
            //被使用则禁止操作
            List<String> tagNameList = usedTagIds.stream().map(tagId -> tagMap.getOrDefault(tagId, "")).collect(Collectors.toList());
            String errMsg = String.join(",", tagNameList) +"标签已被使用不可删除";
            throw new CustomException(errMsg);
        }
        //批量删除
        tagManagerService.deleteByIds(existTagIds);
    }

    /**
     * 获取已使用的落地页标签ID列表
     *
     * @return 已使用的落地页标签ID列表
     */
    private List<Long> getUsedLandpageTagIds() {
        List<String> tags = landpageLibraryService.getLandpageTags();
        return tagManagerService.selectTagIdsBySecondTagNames(TagManagerTypeEnum.LANDPAGE_TAG.getType(), tags);
    }
}
