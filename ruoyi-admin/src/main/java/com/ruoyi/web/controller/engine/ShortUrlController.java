package com.ruoyi.web.controller.engine;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.ShortUrlTypeEnum;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UserAgentUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.engine.cache.ShortUrlCacheService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.service.shorturl.ShortUrlDataService;
import com.ruoyi.system.util.ShortUrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

import static com.ruoyi.common.enums.InnerLogType.SHORT_URL_REQUEST;

/**
 * 短链接口
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
@Slf4j
@Controller
@RequestMapping("/su")
public class ShortUrlController {

    @Autowired
    private ShortUrlCacheService shortUrlCacheService;

    @Autowired
    private ShortUrlDataService shortUrlDataService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private OrderService orderService;

    @Autowired
    private ConvCallbackService convCallbackService;

    @CrossOrigin
    @GetMapping("/{idStr}")
    public void index(@PathVariable("idStr") String idStr, HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 查询布隆过滤器
//        if (!shortUrlCacheService.isShortUrlExist(idStr)) {
//            return;
//        }
        boolean isNumeric = StringUtils.isNumeric(String.valueOf(ShortUrlUtils.shortUrlUrlToId(idStr)));
        if (!isNumeric){
            return;
        }

        // 查询缓存
        Pair<Integer, String> url = shortUrlCacheService.selectShortUrlCache(idStr);
        if (null == url || StringUtils.isBlank(url.getValue())) {
            return;
        }

        // 数据统计
        shortUrlDataService.statistics(idStr);

        // 归因
        if (Objects.equals(url.getKey(), ShortUrlTypeEnum.ATTRIBUTION.getType())) {
            attribute(request);
        }

        // 重定向
        response.sendRedirect(url.getValue());
    }

    /**
     * 归因上报
     */
    private void attribute(HttpServletRequest request) {
        String ip = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");

        GlobalThreadPool.executorService.submit(() -> {
            UserAgentUtils.UserAgentDevice device = UserAgentUtils.analysisUserAgent(userAgent);
            if (null == device || StringUtils.isBlank(device.getModel())) {
                return;
            }
            // 通过缓存查询订单号
            String key = EngineRedisKeyFactory.K104.join(Md5Utils.hash(ip + device.getModel() + device.getOsVersion()));
            String orderId = redisCache.getCacheObject(key);

            // 查询订单号
            Order order = orderService.selectByOrderId(orderId);
            if (null == order) {
                return;
            }

            // 上报广点通
            convCallbackService.directCallback(SHORT_URL_REQUEST, null, order, CallbackProcessorTypeEnum.QQ, MapUtil.of("actionType", "ADD_GROUP"));
        });
    }
}