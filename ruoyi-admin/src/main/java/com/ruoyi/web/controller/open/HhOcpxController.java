package com.ruoyi.web.controller.open;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.bo.open.HhOcpxEventBo;
import com.ruoyi.system.entity.open.HhOcpxRecordEntity;
import com.ruoyi.system.req.open.HhOcpxEventReq;
import com.ruoyi.system.req.open.HhOcpxStatReq;
import com.ruoyi.system.service.open.HhOcpxRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 辉煌OCPX接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2024/09/18
 */
@Slf4j
@RestController
@RequestMapping("/open/hh/ocpx")
public class HhOcpxController {

    @Autowired
    private HhOcpxRecordService hhOcpxRecordService;

    /**
     * 曝光点击监测接口
     */
    @CrossOrigin
    @GetMapping("/stat")
    public Result<Void> stat(HhOcpxStatReq req) {
        log.info("辉煌OCPX曝光点击监测，param={}", JSON.toJSONString(ServletUtils.getRequest().getParameterMap()));

        HhOcpxRecordEntity record = BeanUtil.copyProperties(req, HhOcpxRecordEntity.class);
        record.setCurDate(DateUtil.beginOfDay(new Date()));
        if (null != req.getTms()) {
            record.setTms(new Date(req.getTms()));
        }
        hhOcpxRecordService.insert(record);

        // 回调链接处理
        if (SpringEnvironmentUtils.isProd()) {
            req.setCallbackUrl(URLUtil.encode("https://actengine.ydns.cn/open/hh/ocpx/event?id=" + record.getId()));
        } else {
            req.setCallbackUrl(URLUtil.encode("https://actenginetest.ydns.cn/open/hh/ocpx/event?id=" + record.getId()));
        }

        // 上报给辉煌
        String resp = "";
        JSONObject param = JSON.parseObject(JSON.toJSONString(req));
        param.remove("type");
        if (Objects.equals(req.getType(), 1)) {
            resp = HttpUtil.get("https://hh-exp.yoqu.net/v1/dsp/monitor/onlyexp/" + req.getChainCode(), param);
        } else if (Objects.equals(req.getType(), 2)) {
            resp = HttpUtil.get("https://hhmt-cog.yoqu.net/cpa/media", param);
        }
        log.info("辉煌OCPX曝光点击监测上报, param={}, resp={}", param, resp);

        return ResultBuilder.success();
    }

    /**
     * 转化接口
     */
    @CrossOrigin
    @GetMapping("/event")
    public Result<Void> event(HhOcpxEventReq req) {
        log.info("辉煌OCPX转化监测，param={}", JSON.toJSONString(ServletUtils.getRequest().getParameterMap()));

        HhOcpxRecordEntity record = hhOcpxRecordService.selectById(req.getId());
        if (null == record) {
            return ResultBuilder.success();
        }

        // 更新转化事件
        HhOcpxRecordEntity updateRecord = new HhOcpxRecordEntity();
        updateRecord.setId(record.getId());
        updateRecord.setEventList(addEventList(record.getEventList(), req.getEvent_type()));
        hhOcpxRecordService.updateById(updateRecord);

        // 上报给辉煌
        String url = UrlUtils.appendParams(record.getCallbackUrl(), MapUtil.of("event_type", req.getEvent_type().toString()));
        String resp = HttpUtil.get(url);
        log.info("辉煌OCPX转化上报，url={}, resp={}", url, resp);

        return ResultBuilder.success();
    }

    private String addEventList(String eventListStr, Integer eventType) {
        List<HhOcpxEventBo> eventList = JSON.parseArray(eventListStr, HhOcpxEventBo.class);
        if (null == eventList) {
            eventList = new ArrayList<>();
        }
        eventList.add(new HhOcpxEventBo(eventType, DateUtil.now()));
        return JSON.toJSONString(eventList);
    }
}
