package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.google.common.base.Splitter;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.landpage.LandpageFormAreaCountExcelBo;
import com.ruoyi.system.entity.landpage.LandpageFormAreaCount;
import com.ruoyi.system.req.landpage.LandpageFormAreaAnalysisReq;
import com.ruoyi.system.service.landpage.LandpageFormRecordService;
import com.ruoyi.system.vo.landpage.LandpageFormAreaCountVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 号卡地域分析Controller
 *
 * <AUTHOR>
 * @date 2022-08-08
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/landpageFormAreaAnalysis")
public class LandpageFormAreaAnalysisController extends BaseController {

    @Autowired
    private LandpageFormRecordService landpageFormRecordService;

    /**
     * 号卡地域分析列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LandpageFormAreaAnalysisReq req) {
        startPage();
        Date endDate = Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null);
        List<String> areaList = filterArea(req.getAreaSearch());
        List<LandpageFormAreaCount> list = landpageFormRecordService.selectFormCountGroupByArea(req.getAdvertiserIds(), req.getStartDate(), endDate,areaList);
        return getDataTable(PageInfoUtils.dto2Vo(list, this::convert));
    }

    /**
     * 号卡地域分析汇总数据
     */
    @GetMapping("/summary")
    public AjaxResult summary(LandpageFormAreaAnalysisReq req) {
        Date endDate = Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null);
        List<String> areaList = filterArea(req.getAreaSearch());
        LandpageFormAreaCount summary = landpageFormRecordService.selectFormCountSummary(req.getAdvertiserIds(), req.getStartDate(), endDate,areaList);
        return AjaxResult.success(convert(summary));
    }

    /**
     * 导出号卡地域分析列表
     */
    @Log(title = "号卡地域分析", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LandpageFormAreaAnalysisReq req) {
        Date endDate = Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null);
        List<String> areaList = filterArea(req.getAreaSearch());
        List<LandpageFormAreaCount> list = landpageFormRecordService.selectFormCountGroupByArea(req.getAdvertiserIds(), req.getStartDate(), endDate,areaList);
        LandpageFormAreaCount summary = landpageFormRecordService.selectFormCountSummary(req.getAdvertiserIds(), req.getStartDate(), endDate, areaList);
        summary.setProvince("总计");
        list.add(0, summary);

        List<LandpageFormAreaCountExcelBo> excels = list.stream().map(data -> {
            LandpageFormAreaCountExcelBo excelBo = BeanUtil.copyProperties(data, LandpageFormAreaCountExcelBo.class);
            excelBo.setRegister(NumberUtils.defaultInt(excelBo.getRegister()));
            excelBo.setPay(NumberUtils.defaultInt(excelBo.getPay()));
            excelBo.setActiveRate(NumberUtils.calculatePercent(excelBo.getRegister(), data.getFormCount()));
            excelBo.setRegisterCost(NumberUtils.calculateRate(NumberUtils.defaultInt(data.getConsume()), excelBo.getRegister() * 100));
            excelBo.setPayCost(NumberUtils.calculateRate(NumberUtils.defaultInt(data.getConsume()), excelBo.getPay() * 100));
            return excelBo;
        }).collect(Collectors.toList());
        String fileName = UUID.randomUUID().toString() + "_号卡地域分析.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, LandpageFormAreaCountExcelBo.class).sheet("号卡地域分析").doWrite(excels);
        return AjaxResult.success(fileName);
    }

    private LandpageFormAreaCountVO convert(LandpageFormAreaCount data) {
        LandpageFormAreaCountVO vo = BeanUtil.copyProperties(data, LandpageFormAreaCountVO.class);
        vo.setRegister(NumberUtils.defaultInt(vo.getRegister()));
        vo.setPay(NumberUtils.defaultInt(vo.getPay()));
        vo.setActiveRate(NumberUtils.calculatePercent(vo.getRegister(), data.getFormCount()));
        vo.setRegisterCost(NumberUtils.calculateRate(NumberUtils.defaultInt(data.getConsume()), vo.getRegister() * 100));
        vo.setPayCost(NumberUtils.calculateRate(NumberUtils.defaultInt(data.getConsume()), vo.getPay() * 100));
        return vo;
    }

    /**
     * 过滤地域省等
     * @param areaSearch
     * @return
     */
    private List<String> filterArea(String areaSearch){
        List<String> areaList = null;
        if(StringUtils.isNotBlank(areaSearch)){
            areaList = Splitter.on(",").omitEmptyStrings().splitToStream(areaSearch)
                    .map(area -> StrUtil.removeAny(area, "省", "壮族自治区", "回族自治区", "维吾尔自治区", "自治区"))
                    .collect(Collectors.toList());
            if (areaList.contains("文山壮族苗族自治州")) {
                areaList.add("文山州");
            }
        }
        return areaList;
    }
}
