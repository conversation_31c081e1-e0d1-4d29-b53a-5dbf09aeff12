package com.ruoyi.web.controller.manager.crm.ssp.slot;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.common.EnableStatusEnum;
import com.ruoyi.common.enums.common.MapConfigEnum;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.SlotBiddingConfigDto;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slot.SlotBiddingConfigEntity;
import com.ruoyi.system.req.slot.SlotBiddingConfigReq;
import com.ruoyi.system.req.slot.SlotBiddingConfigSwitchReq;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.slot.SlotBiddingConfigService;
import com.ruoyi.system.vo.slot.SlotBiddingConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * [CRM后台]广告位投流配置
 *
 * <AUTHOR>
 * @date 2023/9/7
 */
@Slf4j
@RestController
@RequestMapping("/crm/ssp/slot/bidding/config")
public class SlotBiddingConfigController {

    @Autowired
    private SlotService slotService;

    @Autowired
    private SlotBiddingConfigService slotBiddingConfigService;

    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private SlotCacheService slotCacheService;

    /**
     * 获取投流配置
     */
    @GetMapping("getBySlotId")
    public Result<SlotBiddingConfigVO> getBySlotId(@RequestParam("slotId") Long slotId) {
        SlotBiddingConfigEntity config = slotBiddingConfigService.selectBySlotId(slotId);
        if (null != config) {
            return ResultBuilder.success(BeanUtil.copyProperties(config, SlotBiddingConfigVO.class));
        }
        // 查询白名单配置,后面移除
        SlotBiddingConfigDto configDto = new SlotBiddingConfigDto();
        configDto.setSlotId(slotId);
        String mapConfig = mapConfigService.getMap(MapConfigEnum.SLOT_KS_COST_MAP, Long.class, String.class).get(slotId);
        if (StringUtils.isNotBlank(mapConfig)) {
            configDto.setIsEnable(EnableStatusEnum.ENABLE.getStatus());
            configDto.setColdStart(1);
            if (mapConfig.contains("_")) {
                configDto.setConsumeType(2);
                configDto.setConvPrice(Integer.parseInt(mapConfig.split("_")[0]) * 100);
            } else {
                configDto.setConsumeType(1);
                configDto.setConvPrice(Integer.parseInt(mapConfig) * 100);
            }
        }
        return ResultBuilder.success(BeanUtil.copyProperties(configDto, SlotBiddingConfigVO.class));
    }

    /**
     * 更新广告位投流配置
     */
    @Log(title = "广告位投流设置", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result<Boolean> update(@RequestBody SlotBiddingConfigReq req) {
        Slot slot = slotService.selectSimpleSlotById(req.getSlotId());
        if (null == slot) {
            return ResultBuilder.fail("无效的广告位ID");
        }
        int isSuccess;
        LoginUser user = SecurityUtils.getLoginUser();
        SlotBiddingConfigEntity config = slotBiddingConfigService.selectBySlotId(req.getSlotId());
        if (null == config) {
            config = new SlotBiddingConfigEntity();
            config.setSlotId(req.getSlotId());
            config.setConvPrice(req.getConvPrice());
            config.setColdStart(req.getColdStart());
            config.setConsumeType(req.getConsumeType());
            config.setIsEnable(req.getIsEnable());
            config.setOperatorId(user.getCrmAccountId());
            config.setOperatorName(user.getUserName());
            isSuccess = slotBiddingConfigService.insert(config);
        } else {
            SlotBiddingConfigEntity updateConfig = new SlotBiddingConfigEntity();
            updateConfig.setId(config.getId());
            updateConfig.setConvPrice(req.getConvPrice());
            updateConfig.setColdStart(req.getColdStart());
            updateConfig.setConsumeType(req.getConsumeType());
            updateConfig.setIsEnable(req.getIsEnable());
            updateConfig.setOperatorId(user.getCrmAccountId());
            updateConfig.setOperatorName(user.getUserName());
            isSuccess = slotBiddingConfigService.updateById(updateConfig);
        }
        if (isSuccess > 0) {
            // 移除白名单配置
            mapConfigService.remove(MapConfigEnum.SLOT_KS_COST_MAP, String.valueOf(req.getSlotId()));
            // 刷新缓存
            refreshCacheService.sendRefreshSlotCacheMsg(req.getSlotId());
        }
        return ResultBuilder.result(isSuccess);
    }

    /**
     * 广告位投流开关
     */
    @Log(title = "广告位投流设置", businessType = BusinessType.UPDATE)
    @PostMapping("/toggleSwitch")
    public Result<Boolean> toggleSwitch(@RequestBody SlotBiddingConfigSwitchReq req) {
        LoginUser user = SecurityUtils.getLoginUser();
        SlotBiddingConfigEntity config = slotBiddingConfigService.selectBySlotId(req.getSlotId());
        if (null == config) {
            // 后面白名单移除后修改
            SlotBiddingConfigDto configDto = slotCacheService.getSlotBiddingConfigCache(req.getSlotId());
            if (null == configDto.getConvPrice() || null == configDto.getConsumeType()) {
                return ResultBuilder.fail("请先配置再操作开关");
            }
            config = new SlotBiddingConfigEntity();
            config.setSlotId(req.getSlotId());
            config.setConvPrice(configDto.getConvPrice());
            config.setColdStart(configDto.getColdStart());
            config.setConsumeType(configDto.getConsumeType());
            config.setIsEnable(req.getIsEnable());
            config.setOperatorId(user.getCrmAccountId());
            config.setOperatorName(user.getUserName());
            int isSuccess = slotBiddingConfigService.insert(config);
            if (isSuccess > 0) {
                // 移除白名单配置
                mapConfigService.remove(MapConfigEnum.SLOT_KS_COST_MAP, String.valueOf(req.getSlotId()));
                // 刷新缓存
                refreshCacheService.sendRefreshSlotCacheMsg(req.getSlotId());
            }
            return ResultBuilder.result(isSuccess);
        }
        SlotBiddingConfigEntity updateConfig = new SlotBiddingConfigEntity();
        updateConfig.setId(config.getId());
        updateConfig.setIsEnable(req.getIsEnable());
        updateConfig.setOperatorId(user.getCrmAccountId());
        updateConfig.setOperatorName(user.getUserName());
        int result = slotBiddingConfigService.updateById(updateConfig);
        refreshCacheService.sendRefreshSlotCacheMsg(req.getSlotId());
        return ResultBuilder.result(result);
    }
}
