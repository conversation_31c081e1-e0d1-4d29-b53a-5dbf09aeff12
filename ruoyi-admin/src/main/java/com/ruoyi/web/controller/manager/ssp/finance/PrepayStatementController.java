package com.ruoyi.web.controller.manager.ssp.finance;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.manager.publisher.SspPrepayStatementManager;
import com.ruoyi.system.vo.ssp.finance.PrepayStatementInfoVO;
import com.ruoyi.system.vo.ssp.finance.PrepayStatementVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * SSP后台预付款Controller
 *
 * <AUTHOR>
 * @date 2022/08/03
 */
@RestController
@RequestMapping("/manager/ssp/prepayStatement")
public class PrepayStatementController extends BaseController {

    @Autowired
    private SspPrepayStatementManager sspPrepayStatementManager;

    /**
     * 预付款结算单列表
     */
    @GetMapping("list")
    public TableDataInfo<PrepayStatementVO> list() {
        Long accountId = SecurityUtils.getLoginUser().getCrmAccountId();
        return getDataTable(sspPrepayStatementManager.selectPrepayStatementList(accountId));
    }

    /**
     * 预付款结算单列表详情
     */
    @GetMapping("info")
    public Result<PrepayStatementInfoVO> info(@Validated @NotNull(message = "结算单号不能为空") Long statementId) {
        Long accountId = SecurityUtils.getLoginUser().getCrmAccountId();
        return ResultBuilder.success(sspPrepayStatementManager.getPrepayStatementInfo(accountId, statementId));
    }
}
