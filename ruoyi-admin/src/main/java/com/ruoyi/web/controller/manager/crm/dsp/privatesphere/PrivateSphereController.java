package com.ruoyi.web.controller.manager.crm.dsp.privatesphere;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.privatesphere.PrivateSphereChannelListBO;
import com.ruoyi.system.bo.privatesphere.PrivateSphereDataListBO;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelDataEntity;
import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelEntity;
import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelNumberEntity;
import com.ruoyi.system.entity.privatesphere.PrivateSphereDataEntity;
import com.ruoyi.system.entity.privatesphere.PrivateSphereProductEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.req.privatesphere.channel.PrivateSphereChannelEditReq;
import com.ruoyi.system.req.privatesphere.channel.PrivateSphereChannelListReq;
import com.ruoyi.system.req.privatesphere.data.PrivateSphereDataEditReq;
import com.ruoyi.system.req.privatesphere.data.PrivateSphereDataListReq;
import com.ruoyi.system.req.privatesphere.product.PrivateSphereProductAddReq;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.privatesphere.PrivateSphereChannelDataService;
import com.ruoyi.system.service.privatesphere.PrivateSphereChannelNumberService;
import com.ruoyi.system.service.privatesphere.PrivateSphereChannelService;
import com.ruoyi.system.service.privatesphere.PrivateSphereDataService;
import com.ruoyi.system.service.privatesphere.PrivateSphereProductService;
import com.ruoyi.system.vo.privatesphere.PrivateAccountRelationVO;
import com.ruoyi.system.vo.privatesphere.PrivateAccountVO;
import com.ruoyi.system.vo.privatesphere.PrivateProductRelationVO;
import com.ruoyi.system.vo.privatesphere.PrivateSphereChannelDataListVO;
import com.ruoyi.system.vo.privatesphere.PrivateSphereChannelListVO;
import com.ruoyi.system.vo.privatesphere.PrivateSphereDataListVO;
import com.ruoyi.system.vo.privatesphere.PrivateSphereProductListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.common.usermodel.fonts.FontCharset;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * [CRM后台]私域营销
 *
 * <AUTHOR>
 * @date 2023/2/7 11:39
 */
@PreAuthorize("@ss.hasCrmPermi()")
@RestController
@RequestMapping("/manager/private")
public class PrivateSphereController extends BaseController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private PrivateSphereProductService privateSphereProductService;

    @Autowired
    private PrivateSphereChannelService privateSphereChannelService;

    @Autowired
    private PrivateSphereChannelNumberService privateSphereChannelNumberService;

    @Autowired
    private PrivateSphereDataService privateSphereDataService;

    @Autowired
    private PrivateSphereChannelDataService privateSphereChannelDataService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    /**
     * 获取所有私域账号
     */
    @GetMapping("accounts")
    public Result<List<PrivateAccountVO>> accounts() {
        List<Account> accounts = accountService.selectPrivateAccountList();
        return ResultBuilder.success(BeanUtil.copyToList(accounts, PrivateAccountVO.class));
    }

    /**
     * 新增产品
     */
    @Log(title = "私域新增产品", businessType = BusinessType.INSERT)
    @PostMapping("addProduct")
    public Result<Boolean> addProduct(@RequestBody @Validated PrivateSphereProductAddReq req) {
        //判断是否有同名产品
        PrivateSphereProductEntity existEntity = privateSphereProductService.selectByAccountIdAndName(req.getAccountId(), req.getProductName());
        if (Objects.nonNull(existEntity)) {
            throw new CustomException(ErrorCode.E140001);
        }
        PrivateSphereProductEntity entity = BeanUtil.copyProperties(req, PrivateSphereProductEntity.class);
        return ResultBuilder.success(privateSphereProductService.insert(entity));
    }

    /**
     * 新增编辑渠道
     */
    @Log(title = "私域新增编辑渠道", businessType = BusinessType.INSERT)
    @PostMapping("editChannel")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> editChannel(@RequestBody @Validated PrivateSphereChannelEditReq req) {
        Long userId = SecurityUtils.getLoginUser().getCrmAccountId();
        //判断是否有同名渠道
        PrivateSphereChannelEntity channelEntity = privateSphereChannelService.selectByProductAndChannelName(req.getProductId(), req.getChannel());
        if (Objects.nonNull(channelEntity) && !Objects.equals(req.getId(), channelEntity.getId())) {
            throw new CustomException(ErrorCode.E140002);
        }
        Long channelId = req.getId();
        PrivateSphereChannelEntity entity = new PrivateSphereChannelEntity();
        entity.setChannel(req.getChannel());
        entity.setAccountId(req.getAccountId());
        entity.setProductId(req.getProductId());
        entity.setOperAccountId(userId);
        if (NumberUtils.isNullOrLteZero(req.getId())) {
            privateSphereChannelService.insert(entity);
            channelId = entity.getId();
        } else {
            entity.setId(req.getId());
            privateSphereChannelService.updateById(entity);
        }

        //插入渠道号
        Long finalChannelId = channelId;
        List<PrivateSphereChannelNumberEntity> insertEntities = req.getChannelNumber().stream().map(number -> {
            PrivateSphereChannelNumberEntity numberEntity = new PrivateSphereChannelNumberEntity();
            numberEntity.setChannelNumber(number);
            numberEntity.setChannelId(finalChannelId);
            return numberEntity;
        }).collect(Collectors.toList());
        privateSphereChannelNumberService.batchInsertOrUpdate(insertEntities);
        return ResultBuilder.success(true);
    }

    /**
     * 获取所有产品列表
     */
    @GetMapping("getProductList")
    public Result<List<PrivateSphereProductListVO>> getProductList(){
        List<PrivateSphereProductEntity> entities = privateSphereProductService.selectProductList();
        List<Long> companyIds = entities.stream().map(PrivateSphereProductEntity::getAccountId).collect(Collectors.toList());
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(companyIds);
        List<PrivateSphereProductListVO> listVOS = entities.stream().map(entity -> {
            PrivateSphereProductListVO vo = BeanUtil.copyProperties(entity, PrivateSphereProductListVO.class);
            vo.setCompanyName(companyNameMap.get(entity.getAccountId()));
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(listVOS);
    }

    /**
     * 删除产品
     */
    @PostMapping("deleteProduct")
    public Result<Boolean> deleteProduct(@RequestBody @Validated IdReq req){
        int count = privateSphereDataService.countByProductId(req.getId());
        if(count > 0){
            throw new CustomException("该产品已有数据录入，不能删除");
        }

        return ResultBuilder.success(privateSphereProductService.deleteById(req.getId()));
    }

    /**
     * 查询渠道列表
     */
    @GetMapping("channelList")
    public TableDataInfo<PrivateSphereChannelListVO> channelList(PrivateSphereChannelListReq req) {
        List<Long> accountIds = accountService.selectIdsByIdOrEmailAndCompany(null, req.getCompanyName());
        PrivateSphereChannelListBO bo = new PrivateSphereChannelListBO();
        bo.setProductIds(req.getProductIds());
        bo.setAccountIds(accountIds);
        bo.setChannel(req.getChannel());
        if (Objects.nonNull(req.getStartDate()) && Objects.nonNull(req.getEndDate())) {
            bo.setStartDate(DateUtil.beginOfDay(req.getStartDate()));
            bo.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        TableSupport.startPage();
        List<PrivateSphereChannelEntity> channelEntities = privateSphereChannelService.selectListByParam(bo);
        List<Long> companyIds = channelEntities.stream().map(PrivateSphereChannelEntity::getAccountId).collect(Collectors.toList());
        List<Long> operAccountIds = channelEntities.stream().map(PrivateSphereChannelEntity::getOperAccountId).collect(Collectors.toList());
        //查询公司信息
        Map<Long, String> companyMap = accountService.selectCompanyNameMap(companyIds);
        //查询联系人信息
        Map<Long, String> contactMap = accountService.selectAccountContactMap(operAccountIds);
        //查询渠道号列表
        List<Long> channelIds = channelEntities.stream().map(PrivateSphereChannelEntity::getId).collect(Collectors.toList());
        List<PrivateSphereChannelNumberEntity> numberEntities = privateSphereChannelNumberService.selectByChannelIds(channelIds);
        Map<Long, List<PrivateSphereChannelNumberEntity>> channelNumberMap = numberEntities.stream().collect(Collectors.groupingBy(PrivateSphereChannelNumberEntity::getChannelId));
        //查询产品名称
        List<Long> productIds = channelEntities.stream().map(PrivateSphereChannelEntity::getProductId).collect(Collectors.toList());
        Map<Long, String> productNameMap = privateSphereProductService.selectProductNameMapByIds(productIds);

        return getDataTable(PageInfoUtils.dto2Vo(channelEntities, channel -> {
            PrivateSphereChannelListVO vo = BeanUtil.copyProperties(channel, PrivateSphereChannelListVO.class);
            vo.setCompanyName(companyMap.get(channel.getAccountId()));
            vo.setProductName(productNameMap.get(channel.getProductId()));
            vo.setOperName(contactMap.get(channel.getOperAccountId()));
            List<PrivateSphereChannelNumberEntity> channelNumberEntities = channelNumberMap.get(channel.getId());
            if (CollectionUtils.isNotEmpty(channelNumberEntities)) {
                List<String> numbers = channelNumberEntities.stream().map(PrivateSphereChannelNumberEntity::getChannelNumber).collect(Collectors.toList());
                vo.setChannelNumbers(numbers);
            }
            return vo;
        }));
    }

    /**
     * 公司产品关联列表
     */
    @GetMapping("companyRelationList")
    public Result<List<PrivateAccountRelationVO>> companyRelationList() {
        List<Account> accounts = accountService.selectPrivateAccountList();
        List<Long> accountIds = accounts.stream().map(Account::getId).collect(Collectors.toList());

        List<PrivateSphereProductEntity> productEntities = privateSphereProductService.selectListByAccountIds(accountIds);
        Map<Long, List<PrivateSphereProductEntity>> productMap = productEntities.stream().collect(Collectors.groupingBy(PrivateSphereProductEntity::getAccountId));

        List<PrivateAccountRelationVO> voList = accounts.stream().map(account -> {
            PrivateAccountRelationVO vo = BeanUtil.copyProperties(account, PrivateAccountRelationVO.class);
            List<PrivateSphereProductEntity> productEntityList = productMap.get(account.getId());
            if (CollectionUtils.isNotEmpty(productEntityList)) {
                List<PrivateProductRelationVO> productList = productEntityList.stream().map(product -> {
                    PrivateProductRelationVO productEntity = new PrivateProductRelationVO();
                    productEntity.setProductName(product.getProductName());
                    productEntity.setProductId(product.getId());
                    return productEntity;
                }).collect(Collectors.toList());
                vo.setProductList(productList);
            }
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(voList);
    }

    /**
     * 新增编辑渠道数据
     */
    @Log(title = "私域新增编辑渠道数据", businessType = BusinessType.INSERT)
    @PostMapping("editData")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> editData(@RequestBody @Validated PrivateSphereDataEditReq req) {
        //查询数据库key是否唯一
        PrivateSphereDataEntity dataEntity = privateSphereDataService.selectByDateAndProduct(req.getCurDate(), req.getAccountId(), req.getProductId());
        if (Objects.nonNull(dataEntity) && !Objects.equals(dataEntity.getId(), req.getId())) {
            throw new CustomException(ErrorCode.E140003);
        }
        PrivateSphereDataEntity editEntity = BeanUtil.copyProperties(req, PrivateSphereDataEntity.class);
        if (NumberUtils.isNullOrLteZero(editEntity.getId())) {
            privateSphereDataService.insert(editEntity);
        } else {
            privateSphereDataService.updateById(editEntity);
        }

        List<PrivateSphereChannelDataEntity> channelDataEntities = req.getChannelDataList().stream().map(channelData -> {
            PrivateSphereChannelDataEntity entity = BeanUtil.copyProperties(channelData, PrivateSphereChannelDataEntity.class);
            entity.setDataId(editEntity.getId());
            return entity;
        }).collect(Collectors.toList());
        privateSphereChannelDataService.batchInsertOrUpdate(channelDataEntities);

        return ResultBuilder.success(true);
    }

    /**
     * 私域数据列表查询
     */
    @GetMapping("dataList")
    public TableDataInfo<PrivateSphereDataListVO> dataList(PrivateSphereDataListReq req) {
        PageInfo<PrivateSphereDataListVO> dataListVOS = privateDataList(req, false);
        return getDataTable(dataListVOS);
    }

    /**
     * 导出私域数据
     */
    @GetMapping("exportDataList")
    public AjaxResult exportDataList(PrivateSphereDataListReq req) {
        PageInfo<PrivateSphereDataListVO> dataListVOS = privateDataList(req, true);
        //获取所有渠道
        PrivateSphereChannelListBO channelParam = new PrivateSphereChannelListBO();
        channelParam.setProductIds(req.getProductIds());
        List<PrivateSphereChannelEntity> channelEntities = privateSphereChannelService.selectListByParam(channelParam);

        String fileName = UUID.randomUUID().toString() + "_私域数据.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;

        List<List<Object>> list = new ArrayList<List<Object>>();
        dataListVOS.getList().forEach(data -> {
            list.add(getData(data, channelEntities));
        });

        WriteCellStyle headWriteCellStyle = getWriteCellStyle();

        EasyExcel.write(filePath).head(getHeader(channelEntities)).registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle,new WriteCellStyle())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet().doWrite(list);
        return AjaxResult.success(fileName);
    }

    private WriteCellStyle getWriteCellStyle() {
        // 表头样式策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 是否换行
        headWriteCellStyle.setWrapped(false);
        // 水平对齐方式
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 垂直对齐方式
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 字体策略
        WriteFont writeFont = new WriteFont();
        // 是否加粗/黑体
        writeFont.setBold(false);
        // 字体颜色
        writeFont.setColor(Font.COLOR_NORMAL);
        // 字体名称
        writeFont.setFontName("宋体");
        // 字体大小
        writeFont.setFontHeightInPoints((short) 11);

        // 设置要使用的字符集
        writeFont.setCharset(FontCharset.DEFAULT.getNativeId());
        headWriteCellStyle.setWriteFont(writeFont);
        return headWriteCellStyle;
    }

    /**
     * 私域数据总计
     */
    @GetMapping("statisticsData")
    public Result<PrivateSphereDataListVO> statisticsData(PrivateSphereDataListReq req) {
        PrivateSphereDataListBO bo = getPrivateSphereDataListBO(req);
        if (Objects.isNull(bo)) {
            return ResultBuilder.success(null);
        }
        PrivateSphereDataEntity dataEntity = privateSphereDataService.statisticsByParam(bo);
        if (Objects.isNull(dataEntity)) {
            return ResultBuilder.success(null);
        }

        List<Long> dataIds = privateSphereDataService.selectIdsByParam(bo);
        List<PrivateSphereChannelDataEntity> channelDataEntities = privateSphereChannelDataService.statisticsByDataIds(dataIds);

        return ResultBuilder.success(buildPrivateSphereDataListVO(dataEntity, channelDataEntities, null, null,true));
    }

    /**
     * 查询私域数据
     */
    private PageInfo<PrivateSphereDataListVO> privateDataList(PrivateSphereDataListReq req, Boolean isExport) {
        PrivateSphereDataListBO bo = getPrivateSphereDataListBO(req);
        if (Objects.isNull(bo)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }
        if (BooleanUtils.isNotTrue(isExport)) {
            TableSupport.startPage();
        }

        List<PrivateSphereDataEntity> dataEntities = privateSphereDataService.selectListByParam(bo);
        //查询渠道数据
        List<Long> dataIds = dataEntities.stream().map(PrivateSphereDataEntity::getId).collect(Collectors.toList());
        List<PrivateSphereChannelDataEntity> channelDataEntities = privateSphereChannelDataService.selectListByDataIds(dataIds);
        Map<Long, List<PrivateSphereChannelDataEntity>> channelDataMap = channelDataEntities.stream().collect(Collectors.groupingBy(PrivateSphereChannelDataEntity::getDataId));
        //查询公司名称
        List<Long> accountIds = dataEntities.stream().map(PrivateSphereDataEntity::getAccountId).collect(Collectors.toList());
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(accountIds);
        //查询产品名称
        List<Long> productIds = dataEntities.stream().map(PrivateSphereDataEntity::getProductId).collect(Collectors.toList());
        Map<Long, String> productNameMap = privateSphereProductService.selectProductNameMapByIds(productIds);
        return PageInfoUtils.dto2Vo(dataEntities, data -> {
            List<PrivateSphereChannelDataEntity> dataEntityList = channelDataMap.get(data.getId());

            return buildPrivateSphereDataListVO(data, dataEntityList, companyNameMap, productNameMap,false);
        });
    }

    /**
     * 构建返回vo
     */
    private PrivateSphereDataListVO buildPrivateSphereDataListVO(PrivateSphereDataEntity data, List<PrivateSphereChannelDataEntity> dataEntityList, Map<Long, String> companyNameMap, Map<Long, String> productNameMap,Boolean isStatistics) {
        PrivateSphereDataListVO vo = BeanUtil.copyProperties(data, PrivateSphereDataListVO.class);
        vo.setConnectRate(NumberUtils.calculatePercent(data.getConnectCount(), data.getCallCount()));
        vo.setIntentRate(NumberUtils.calculatePercent(data.getPersonCount(), data.getConnectCount()));
        vo.setEntryPercent(NumberUtils.calculatePercent(data.getEntryCount(), data.getPersonCount()));
        vo.setEntryRatio(NumberUtils.calculatePercent(data.getEntryCount(), data.getConnectCount()));
        if(BooleanUtils.isTrue(isStatistics)){
            vo.setSumCost(data.getDataCost().intValue() + data.getLineCost() + data.getPersonCost());
        }else{
            vo.setSumCost((int)(data.getDataCost() * data.getDataCostCount()) + data.getLineCost() + data.getPersonCost() * data.getEntryCount());
        }

        vo.setSingleEntryCost(NumberUtils.calculateRate(vo.getSumCost(), data.getEntryCount(),"0"));
        if (MapUtils.isNotEmpty(companyNameMap)) {
            vo.setCompanyName(companyNameMap.get(vo.getAccountId()));
        }
        if (MapUtils.isNotEmpty(productNameMap)) {
            vo.setProductName(productNameMap.get(vo.getProductId()));
        }

        if (CollectionUtils.isEmpty(dataEntityList)) {
            return vo;
        }
        AtomicInteger sumAmount = new AtomicInteger(0);
        AtomicInteger refundAmount = new AtomicInteger(0);
        List<PrivateSphereChannelDataListVO> dataListVOS = dataEntityList.stream().map(channelData -> {
            PrivateSphereChannelDataListVO channelDataVo = BeanUtil.copyProperties(channelData, PrivateSphereChannelDataListVO.class);
            channelDataVo.setRValue(NumberUtils.calculateRate((channelDataVo.getConverAmount() - channelDataVo.getRefundAmount()), channelDataVo.getEntryGroupCount(),"0"));
            sumAmount.addAndGet(channelData.getConverAmount());
            refundAmount.addAndGet(channelData.getRefundAmount());
            return channelDataVo;
        }).collect(Collectors.toList());
        vo.setChannelDataList(dataListVOS);
        vo.setRValue(NumberUtils.calculateRate((sumAmount.get() - refundAmount.get()), data.getEntryCount(),"0"));
        vo.setSumConverAmount(sumAmount.get());
        vo.setSumRefundAmount(refundAmount.get());
        return vo;
    }

    private PrivateSphereDataListBO getPrivateSphereDataListBO(PrivateSphereDataListReq req) {
        // 公司查询
        List<Long> accountIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(req.getAccountIds())) {
            accountIds.addAll(req.getAccountIds());
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectPrivateAccount();
        if (hasPartialPermission(permission.getType())) {
            accountIds = mergeParamIds(accountIds, permission.getValues());
            if (CollectionUtils.isEmpty(accountIds)) {
                return null;
            }
        }

        PrivateSphereDataListBO bo = new PrivateSphereDataListBO();
        bo.setAccountIds(accountIds);
        bo.setProductIds(req.getProductIds());
        if (Objects.nonNull(req.getStartDate()) && Objects.nonNull(req.getEndDate())) {
            bo.setStartDate(DateUtil.beginOfDay(req.getStartDate()));
            bo.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        return bo;
    }

    private List<List<String>> getHeader(List<PrivateSphereChannelEntity> privateSphereChannelEntities) {
        List<List<String>> list = new ArrayList<List<String>>();
        list.add(Lists.newArrayList("日期"));
        list.add(Lists.newArrayList("公司名称"));
        list.add(Lists.newArrayList("产品名称"));
        list.add(Lists.newArrayList("外呼总数"));
        list.add(Lists.newArrayList("接通数"));
        list.add(Lists.newArrayList("接通率"));
        list.add(Lists.newArrayList("进入人工数"));
        list.add(Lists.newArrayList("意向率"));
        list.add(Lists.newArrayList("入群数"));
        list.add(Lists.newArrayList("成单率"));
        list.add(Lists.newArrayList("成单比"));
        list.add(Lists.newArrayList("数据成本"));
        list.add(Lists.newArrayList("线路成本"));
        list.add(Lists.newArrayList("人工成本"));
        list.add(Lists.newArrayList("总成本"));
        list.add(Lists.newArrayList("单入群成本"));
        list.add(Lists.newArrayList("总转化金额"));
        list.add(Lists.newArrayList("总退款金额"));
        list.add(Lists.newArrayList("R值"));
        privateSphereChannelEntities.forEach(entity -> {
            list.add(Lists.newArrayList(entity.getChannel() + "入群数"));
            list.add(Lists.newArrayList(entity.getChannel() + "转化金额"));
            list.add(Lists.newArrayList(entity.getChannel() + "退款数"));
            list.add(Lists.newArrayList(entity.getChannel() + "R值"));
        });
        return list;
    }

    private List<Object> getData(PrivateSphereDataListVO data, List<PrivateSphereChannelEntity> channelEntities) {
        List<Object> datas = ListUtils.newArrayList();
        datas.add(DateUtil.formatDate(data.getCurDate()));
        datas.add(data.getCompanyName()); //公司名称
        datas.add(data.getProductName()); //产品名称
        datas.add(data.getCallCount()); //外呼总数
        datas.add(data.getConnectCount()); //接通数
        datas.add(data.getConnectRate()); //接通率
        datas.add(data.getPersonCount()); //进入人工数
        datas.add(data.getIntentRate()); //意向率
        datas.add(data.getEntryCount()); //入群数
        datas.add(data.getEntryPercent()); // 成单率
        datas.add(data.getEntryRatio()); //成单比
        datas.add(NumberUtils.fenToYuanForDouble((int)(data.getDataCost() * data.getDataCostCount())));//数据成本 分转元
        datas.add(NumberUtils.fenToYuanForDouble(data.getLineCost()));//线路成本
        datas.add(NumberUtils.fenToYuanForDouble(data.getPersonCost() * data.getEntryCount()));//人工成本 分转元
        datas.add(NumberUtils.fenToYuanForDouble(data.getSumCost())); //总成本 分转元
        datas.add(NumberUtils.fenToYuanForDouble(Double.valueOf(data.getSingleEntryCost()).intValue()));//单入群成本
        datas.add(NumberUtils.fenToYuanForDouble(data.getSumConverAmount()));//总转化金额 分转元
        datas.add(NumberUtils.fenToYuanForDouble(data.getSumRefundAmount()));//总退款金额 分转元
        datas.add(NumberUtils.fenToYuanForDouble(Double.valueOf(data.getRValue()).intValue()));//R值 （总转化金额-总退款）/总入群
        List<PrivateSphereChannelDataListVO> channelDataList = data.getChannelDataList();
        Map<Long, PrivateSphereChannelDataListVO> channelDataMap = channelDataList.stream().collect(Collectors.toMap(PrivateSphereChannelDataListVO::getChannelId, Function.identity()));
        channelEntities.forEach(channelEntity -> {
            PrivateSphereChannelDataListVO dataListVO = channelDataMap.get(channelEntity.getId());
            if (Objects.isNull(dataListVO)) {
                datas.add(0);//渠道入群数
                datas.add(0);//渠道退款数
                datas.add(0);//渠道转化金额
                datas.add(0);//渠道R值
            } else {
                datas.add(dataListVO.getEntryGroupCount());//渠道入群数
                datas.add(NumberUtils.fenToYuanForDouble(dataListVO.getConverAmount()));//渠道转化金额
                datas.add(NumberUtils.fenToYuanForDouble(dataListVO.getRefundAmount()));//渠道退款数
                datas.add(NumberUtils.fenToYuanForDouble(Double.valueOf(dataListVO.getRValue()).intValue()));//渠道R值
            }
        });
        return datas;
    }
}
