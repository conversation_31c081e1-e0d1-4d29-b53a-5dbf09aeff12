package com.ruoyi.web.controller.manager.wis.advertiser.finance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.advertiser.finance.WisAdvertiserConsumeDataExcelBo;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeRecordEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeListReq;
import com.ruoyi.system.req.advertiser.finance.WisAdvertiserConsumeListReq;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.vo.advertiser.finance.WisAdvertiserConsumeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * [广告主平台]消费记录
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@RequestMapping("/wis/fiance/consume")
@RestController
public class WisAdvertiserConsumeController extends BaseController {

    @Autowired
    private AdvertiserConsumeRecordService advertiserConsumeRecordService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    /**
     * 消费记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<WisAdvertiserConsumeVO> list(WisAdvertiserConsumeListReq req){
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非广告主/代理商不展示
        if (null == user || null == user.getCrmAccountId()) {
            return getDataTable(Collections.emptyList());
        }
        if (!Objects.equals(user.getMainType(), AccountMainType.ADVERTISER.getType())
                && !Objects.equals(user.getMainType(), AccountMainType.AGENT.getType()) ) {
            return getDataTable(Collections.emptyList());
        }

        AdvertiserConsumeListReq listReq = BeanUtil.copyProperties(req, AdvertiserConsumeListReq.class);
        listReq.setAccountId(user.getCrmAccountId());
        if (null != req.getEndDate()) {
            listReq.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        // 离线广告主不展示当日数据
        boolean isOfflineData = whitelistService.contains(OFFLINE_DATA_ADVERTISER, user.getCrmAccountId());
        if (isOfflineData) {
            Date today = DateUtil.beginOfDay(new Date());
            if (null == req.getEndDate() || !req.getEndDate().before(today)) {
                listReq.setEndDate(DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -1)));
            }
        }
        // 剔除不展示的日期
        List<Date> dateList = dspAdvertiserConsumeRecordService.selectInvisibleDateList(user.getCrmAccountId());
        listReq.setInvisibleDateList(dateList);

        TableSupport.startPage();
        List<AdvertiserConsumeRecordEntity> list = advertiserConsumeRecordService.selectList(listReq);

        // 离线数据广告主展示离线余额和消费金额
        Map<Date, DspAdvertiserConsumeRecordEntity> consumeRecordMap = dspAdvertiserConsumeRecordService.selectVisibleDataMap(req.getStartDate(), req.getEndDate(), user.getCrmAccountId());

        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            WisAdvertiserConsumeVO consumeVO = BeanUtil.copyProperties(record, WisAdvertiserConsumeVO.class);
            Optional.ofNullable(consumeRecordMap.get(record.getCurDate())).ifPresent(data -> {
                consumeVO.setConsumeAmount(data.getConsumeAmount());
            });
            return consumeVO;
        }));
    }

    /**
     * 导出消费记录
     */
    @Log(title = "广告主后台消费记录", businessType = BusinessType.EXPORT)
    @GetMapping("export")
    public AjaxResult export(WisAdvertiserConsumeListReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非广告主/代理商不展示
        if (null == user || null == user.getCrmAccountId()) {
            return AjaxResult.error("导出失败");
        }
        if (!isAdvertiser(user.getMainType()) && !isAgent(user.getMainType())) {
            return AjaxResult.error("导出失败");
        }

        AdvertiserConsumeListReq listReq = BeanUtil.copyProperties(req, AdvertiserConsumeListReq.class);
        listReq.setAccountId(user.getCrmAccountId());
        if (null != req.getEndDate()) {
            listReq.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        // 离线广告主不展示当日数据
        boolean isOfflineData = whitelistService.contains(OFFLINE_DATA_ADVERTISER, user.getCrmAccountId());
        if (isOfflineData) {
            Date today = DateUtil.beginOfDay(new Date());
            if (null == req.getEndDate() || !req.getEndDate().before(today)) {
                listReq.setEndDate(DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -1)));
            }
        }
        // 剔除不展示的日期
        List<Date> dateList = dspAdvertiserConsumeRecordService.selectInvisibleDateList(user.getCrmAccountId());
        listReq.setInvisibleDateList(dateList);

        TableSupport.startPage();
        List<AdvertiserConsumeRecordEntity> list = advertiserConsumeRecordService.selectList(listReq);

        // 离线数据广告主展示离线余额和消费金额
        Map<Date, DspAdvertiserConsumeRecordEntity> consumeRecordMap = dspAdvertiserConsumeRecordService.selectVisibleDataMap(req.getStartDate(), req.getEndDate(), user.getCrmAccountId());

        List<WisAdvertiserConsumeDataExcelBo> consumeExcelBos = list.stream().map(record -> {
            WisAdvertiserConsumeDataExcelBo excelBo = BeanUtil.copyProperties(record, WisAdvertiserConsumeDataExcelBo.class);
            excelBo.setConsumeAmount(NumberUtils.fenToYuan(record.getConsumeAmount()));
            Optional.ofNullable(consumeRecordMap.get(record.getCurDate())).ifPresent(data -> {
                excelBo.setConsumeAmount(NumberUtils.fenToYuan(data.getConsumeAmount()));
            });
            return excelBo;
        }).collect(Collectors.toList());
        String fileName = UUID.randomUUID().toString() + "_消费记录.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, WisAdvertiserConsumeDataExcelBo.class).sheet("消费记录").doWrite(consumeExcelBos);
        return AjaxResult.success(fileName);
    }
}
