package com.ruoyi.web.controller.manager.wis.agent.client;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Joiner;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity;
import com.ruoyi.system.req.agent.client.AgentClientAddReq;
import com.ruoyi.system.req.agent.client.AgentClientEditReq;
import com.ruoyi.system.req.agent.client.AgentClientListReq;
import com.ruoyi.system.req.agent.client.AgentClientPwdReq;
import com.ruoyi.system.req.agent.client.AgentClientStatusReq;
import com.ruoyi.system.service.advertiser.AdvertiserQualificationService;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.advertiser.AdvertiserSelectVO;
import com.ruoyi.system.vo.advertiser.qualification.AdvertiserQualificationVO;
import com.ruoyi.system.vo.agent.AgentClientVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.ADVERTISER;
import static com.ruoyi.common.enums.account.AccountMainType.AGENT;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.account.AccountStatusEnum.isAgentDisable;
import static com.ruoyi.common.enums.account.AccountStatusEnum.isNormal;

/**
 * [代理商平台]客户管理
 *
 * <AUTHOR>
 * @date 2022-10-26
 */
@Slf4j
@RestController
@RequestMapping("/wis/agent/client/manager")
public class WisAgentClientManagerController extends BaseController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AdvertiserQualificationService advertiserQualificationService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    /**
     * 获取广告主下拉列表
     */
    @GetMapping("/advertiserList")
    public Result<List<AdvertiserSelectVO>> advertiserList() {
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return ResultBuilder.success(Collections.emptyList());
        }
        List<Long> advertiserIds = agentService.selectAdvertiserIdsByAgent(user.getCrmAccountId());
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return ResultBuilder.success(Collections.emptyList());
        }

        Account param = new Account();
        param.setIds(advertiserIds);
        List<Account> list = advertiserService.selectList(param);

        Map<Long, Integer> balanceMap = advertiserBalanceService.selectAdvertiserBalanceMap(advertiserIds);
        return ResultBuilder.success(ListUtils.mapToList(list, advertiser -> {
            AdvertiserSelectVO vo = new AdvertiserSelectVO();
            vo.setId(advertiser.getId());
            vo.setCompanyName(advertiser.getCompanyName());
            vo.setTotalBalance(balanceMap.get(advertiser.getId()));
            return vo;
        }));
    }

    /**
     * 客户列表
     */
    @GetMapping("/list")
    public TableDataInfo<AgentClientVO> list(AgentClientListReq req) {
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return getDataTable(Collections.emptyList());
        }
        // 查询代理商下所有广告主ID
        List<Long> advertiserIds = agentService.selectAdvertiserIdsByAgent(user.getCrmAccountId());
        if (CollectionUtils.isEmpty(advertiserIds) || (null != req.getAdvertiserId() && !advertiserIds.contains(req.getAdvertiserId()))) {
            return getDataTable(Collections.emptyList());
        }
        // 查询广告主列表
        Account param = new Account();
        param.setStartDate(req.getStartDate());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        param.setIds(advertiserIds);
        param.setId(req.getAdvertiserId());
        startPage();
        List<Account> list = advertiserService.selectList(param);

        // 资质信息
        Map<Long, List<AdvertiserQualificationEntity>> qualificationMap = advertiserQualificationService.selectMapByAccountIds(ListUtils.mapToList(list, Account::getId));

        // 构造返回列表
        return getDataTable(PageInfoUtils.dto2Vo(list, advertiser -> {
            // 广告主信息
            AgentClientVO clientVO = BeanUtil.copyProperties(advertiser, AgentClientVO.class);
            // 资质信息
            Optional.ofNullable(qualificationMap.get(advertiser.getId())).ifPresent(qualificationList ->
                clientVO.setQualificationList(qualificationList.stream().map(qualification -> {
                    AdvertiserQualificationVO qualificationVO = BeanUtil.copyProperties(qualification, AdvertiserQualificationVO.class);
                    qualificationVO.setQualificationImgs(JSONArray.parseArray(qualification.getQualificationImg(),String.class));
                    return qualificationVO;
                }).collect(Collectors.toList()))
            );
            return clientVO;
        }));
    }

    /**
     * 新增客户
     */
    @PostMapping("/add")
    public Result<Void> add(@Validated @RequestBody AgentClientAddReq req) {
        // 参数校验
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return ResultBuilder.fail("无操作权限");
        }
        if (StringUtils.isBlank(req.getPasswd()) || !Objects.equals(req.getPasswd(), req.getRepeatPasswd())) {
            return ResultBuilder.fail("两次密码不一致");
        }
        if (req.getPasswd().length() < 6 || req.getPasswd().length() > 20) {
            return ResultBuilder.fail("密码为6-20位英文和数字组合");
        }
        if (StringUtils.isNotBlank(req.getPhone()) && !StringUtils.isNumeric(req.getPhone())) {
            return ResultBuilder.fail("请输入有效的手机号");
        }

        // 广告主去掉了联系人手机号不能重复的限制，联系人手机号也不能用来登录
        int licenseUnique = accountService.checkBusinessLicenseUnique(req.getBusinessLicense());
        // 广告主/代理商不能有相同邮箱和公司名
        int companyNameExist = accountService.checkCompanyNameUnique(req.getCompanyName(), ADVERTISER.getType()) | accountService.checkCompanyNameUnique(req.getCompanyName(), AGENT.getType());
        int emailExist = accountService.checkEmailUnique(req.getEmail(), ADVERTISER.getType()) | accountService.checkEmailUnique(req.getEmail(), AGENT.getType());

        if (UserConstants.EXIST.equals(companyNameExist)) {
            return ResultBuilder.fail(ErrorCode.E101001);
        } else if (UserConstants.EXIST.equals(emailExist)) {
            return ResultBuilder.fail(ErrorCode.E101004);
        } else if (UserConstants.EXIST.equals(licenseUnique)) {
            return ResultBuilder.fail(ErrorCode.E101006);
        }

        RedisLock lock = redisAtomicClient.getLock(Constants.REGISTER_REPEAT_KEY + Md5Utils.hash(req.getEmail()), 5);
        if (lock == null) {
            return ResultBuilder.fail("请勿重复提交");
        }
        Long advertiserId = null;
        try {
            advertiserId = advertiserService.insertByAgent(user.getCrmAccountId(), req);
        } catch (Exception e) {
            log.error("代理商新增广告主异常, agentId={}, company={}, email={}, phone={}", user.getCrmAccountId(), req.getCompanyName(), req.getEmail(), req.getPhone(), e);
            return ResultBuilder.fail("注册失败");
        } finally {
            lock.unlock();
        }
        // 新增成功后发送钉钉提醒
        if (null != advertiserId) {
            accountService.registerNoticeAsync(advertiserId, user.getCrmAccountId());
        }
        return ResultBuilder.success();
    }

    /**
     * 修改客户
     */
    @PostMapping("/edit")
    public Result<Void> edit(@Validated @RequestBody AgentClientEditReq req) {
        // 参数校验
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return ResultBuilder.fail("无操作权限");
        }
        Long agentId = agentService.selectAgentIdByAdvertiserId(req.getAdvertiserId());
        if (!Objects.equals(agentId, user.getCrmAccountId())) {
            return ResultBuilder.fail("无权限修改");
        }

        Account advertiser = accountService.selectAccountById(req.getAdvertiserId());
        if (null == advertiser) {
            return ResultBuilder.fail("未查询到该广告主");
        }
        AccountExtInfo extInfo = Optional.ofNullable(JSON.parseObject(advertiser.getExtInfo(), AccountExtInfo.class)).orElse(new AccountExtInfo());

        RedisLock lock = redisAtomicClient.getLock(Constants.REGISTER_REPEAT_KEY + Md5Utils.hash(advertiser.getEmail()), 5);
        if (lock == null) {
            return ResultBuilder.fail("请勿重复提交");
        }

        Account updateAdvertiser = new Account();
        updateAdvertiser.setId(req.getAdvertiserId());

        if (!Objects.equals(req.getEmail(), advertiser.getEmail())) {
            if (UserConstants.EXIST.equals(accountService.checkEmailUnique(req.getEmail(), ADVERTISER.getType()) | accountService.checkEmailUnique(req.getEmail(), AGENT.getType()))) {
                return ResultBuilder.fail(ErrorCode.E101004);
            }
            updateAdvertiser.setEmail(req.getEmail());
        }
        if (!Objects.equals(req.getPhone(), advertiser.getPhone())) {
            if (UserConstants.EXIST.equals(accountService.checkPhoneUnique(req.getPhone(), ADVERTISER.getType()))) {
                return ResultBuilder.fail(ErrorCode.E101003);
            }
            updateAdvertiser.setPhone(req.getPhone());
        }
        if (!Objects.equals(req.getCompanyName(), advertiser.getCompanyName())) {
            if (UserConstants.EXIST.equals(accountService.checkCompanyNameUnique(req.getCompanyName(), ADVERTISER.getType()) | accountService.checkCompanyNameUnique(req.getCompanyName(), AGENT.getType()))) {
                return ResultBuilder.fail(ErrorCode.E101001);
            }
            updateAdvertiser.setCompanyName(req.getCompanyName());
        }
        if (!Objects.equals(req.getContact(), advertiser.getContact())) {
            updateAdvertiser.setContact(req.getContact());
        }
        if (!Objects.equals(req.getAddress(), extInfo.getAddress())) {
            extInfo.setAddress(req.getAddress());
            updateAdvertiser.setExtInfo(JSON.toJSONString(extInfo));
        }
        accountService.updateAccount(updateAdvertiser);
        return ResultBuilder.success();
    }

    /**
     * 重置密码
     */
    @PostMapping("/resetPwd")
    public Result<Void> resetPwd(@RequestBody AgentClientPwdReq req) {
        // 参数校验
        if (StringUtils.isBlank(req.getPasswd()) || !Objects.equals(req.getPasswd(), req.getRepeatPasswd())) {
            return ResultBuilder.fail("两次密码不一致");
        }
        if (req.getPasswd().length() < 6 || req.getPasswd().length() > 20) {
            return ResultBuilder.fail("密码为6-20位英文和数字组合");
        }
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return ResultBuilder.fail("无操作权限");
        }
        Long agentId = agentService.selectAgentIdByAdvertiserId(req.getAdvertiserId());
        if (!Objects.equals(agentId, user.getCrmAccountId())) {
            return ResultBuilder.fail("无权限重置该广告主密码");
        }
        // 更新密码
        String password = SecurityUtils.encryptPassword(req.getPasswd());
        return ResultBuilder.result(accountService.updatePassword(req.getAdvertiserId(), password));
    }

    /**
     * 开启/禁用
     */
    @PostMapping("/toggleStatus")
    public Result<Void> toggleStatus(@RequestBody AgentClientStatusReq req) {
        // 参数校验
        if (null == req.getStatus() || (!isNormal(req.getStatus()) && !isAgentDisable(req.getStatus()))) {
            return ResultBuilder.fail("参数错误");
        }
        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return ResultBuilder.fail("无操作权限");
        }
        Account advertiser = accountService.selectAccountById(req.getAdvertiserId());
        if (null == advertiser) {
            return ResultBuilder.fail("无效的广告主ID");
        }
        if (Objects.equals(advertiser.getStatus(), req.getStatus())) {
            if (isNormal(req.getStatus())) {
                return ResultBuilder.fail("已启用，无需重复操作");
            } else if (isAgentDisable(req.getStatus())) {
                return ResultBuilder.fail("已禁用，无需重复操作");
            }
        }

        Long agentId = agentService.selectAgentIdByAdvertiserId(req.getAdvertiserId());
        if (!Objects.equals(agentId, user.getCrmAccountId())) {
            return ResultBuilder.fail("无权限更新该广告主状态");
        }
        // 更新状态
        Account param = new Account();
        param.setId(req.getAdvertiserId());
        param.setStatus(req.getStatus());
        int result = accountService.updateAccount(param);
        if (result > 0) {
            Advert advertParam = new Advert();
            advertParam.setAdvertiserId(req.getAdvertiserId());
            List<Long> advertIds = advertService.selectAdvertIds(advertParam);

            if (isNormal(req.getStatus())) {
                // 恢复广告主的所有广告
                advertService.enableAdvertByAdvertiserId(req.getAdvertiserId());

                // 发送钉钉提醒
                StringBuilder sbr = new StringBuilder();
                sbr.append("代理商启用广告主\n")
                        .append("\n代理商ID: ").append(user.getCrmAccountId())
                        .append("\n代理商名称: ").append(user.getUserName())
                        .append("\n启用广告主ID: ").append(req.getAdvertiserId())
                        .append("\n启用广告主名称: ").append(advertiser.getCompanyName());
                DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sbr.toString());
            } else if (isAgentDisable(req.getStatus())) {
                // 关闭广告主的所有广告
                advertService.disableAdvertByAdvertiserId(req.getAdvertiserId());

                // 发送钉钉提醒
                StringBuilder sbr = new StringBuilder();
                sbr.append("代理商禁用广告主\n")
                        .append("\n代理商ID: ").append(user.getCrmAccountId())
                        .append("\n代理商名称: ").append(user.getUserName())
                        .append("\n被禁用广告主ID: ").append(req.getAdvertiserId())
                        .append("\n被禁用广告主名称: ").append(advertiser.getCompanyName())
                        .append("\n关闭广告: ").append(Joiner.on(",").join(advertIds));
                DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sbr.toString());
            }
            advertIds.forEach(advertId -> refreshCacheService.sendRefreshAdvertCacheMsg(advertId));
        }
        return ResultBuilder.result(result);
    }
}
