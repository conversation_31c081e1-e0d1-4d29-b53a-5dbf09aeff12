package com.ruoyi.web.controller.sms;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.antgroup.antchain.openapi.riskplus.Client;
import com.antgroup.antchain.openapi.riskplus.models.BackflowEventRecord;
import com.antgroup.antchain.openapi.riskplus.models.BackflowEventRecordProperty;
import com.antgroup.antchain.openapi.riskplus.models.Config;
import com.antgroup.antchain.openapi.riskplus.models.PushUmktBackflowEventRequest;
import com.antgroup.antchain.openapi.riskplus.models.PushUmktBackflowEventResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord;
import com.ruoyi.system.req.phone.Mayi2cReq;
import com.ruoyi.system.service.landpage.LiuziLandpageFormRecordService;
import com.ruoyi.system.service.phone.PhoneManager;
import com.ruoyi.system.util.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 私域蚂蚁授权回流管理
 *
 * <AUTHOR>
 * @date 2024/11/26 11:25
 */
@Slf4j
@RequestMapping("sms/mayi")
@RestController
public class SmsMayiController {
    @Autowired
    private LiuziLandpageFormRecordService liuziLandpageFormRecordService;
    @Autowired
    private PhoneManager phoneManager;

    @PostMapping("push")
    public String push(Mayi2cReq req) {
        //读取文件
        Client client = buildClient();
        if (Objects.isNull(req.getFile()) || StringUtils.isBlank(req.getTaskId()) || NumberUtils.isNullOrLteZero(req.getEventInstanceId())) {
            return "参数错误";
        }
        log.info("蚂蚁回流开始");
        CsvReader reader = CsvUtil.getReader();
        //从文件中读取CSV数据
        File file = FileUtils.convert(req.getFile());
        CsvData data = reader.read(file);
        List<CsvRow> rows = data.getRows();
        List<String> phones = rows.stream().map(row ->{
            if(row.get(0).length() == 32){
                return row.get(0);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        //判断是否已有记录

        Long eventInstanceId = req.getEventInstanceId();
        //只回传95%的数据
        Collections.shuffle(phones);
        phones = phones.subList(0, (int) (phones.size() * 0.95));
        Lists.partition(phones, 100).stream().forEach(phoneList -> {
            //解密md5号码
            Map<String, String> phoneMd5Map = phoneManager.decodeMd5Phone(phoneList);
            List<String> selectPhones = phoneMd5Map.values().stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            List<LiuziLandpageFormRecord> existList = liuziLandpageFormRecordService.selectListByPhone(selectPhones);
            List<String> existPhones = existList.stream().map(LiuziLandpageFormRecord::getPhone).collect(Collectors.toList());
            selectPhones.removeAll(existPhones);
            //无记录生成记录
            List<LiuziLandpageFormRecord> insertList = selectPhones.stream().map(phone -> {
                List<String> subDomain = Lists.newArrayList("a", "b", "c", "d", "e", "f", "g", "h", "i", "j");
                List<String> domain = Lists.newArrayList("miaojie.net", "actfinkland.cn", "xiaocong.com.cn");
                //表单时间范围 21年12月1日15:00--23年10月30日00:00
                Date startDate = DateUtil.parse("2021-12-01 15:00:00", "yyyy-MM-dd HH:mm:ss");
                Date endDate = DateUtil.parse("2023-10-30 00:00:00", "yyyy-MM-dd HH:mm:ss");
                Date curDate = randomDate(startDate, endDate);
                String baseUrl = StrUtil.format("https://wssya{}.{}/land/render/S3X91GLD?timestamp={}", subDomain.get(RandomUtil.randomInt(0, subDomain.size())), domain.get(RandomUtil.randomInt(0, domain.size())), curDate.getTime());

                LiuziLandpageFormRecord record = new LiuziLandpageFormRecord();
                record.setAdvertId(RandomUtil.randomBoolean() ? 8853L : 8854L);
                record.setOrderId(RandomUtil.randomNumbers(9));
                record.setConsumerId(Long.valueOf(RandomUtil.randomNumbers(9)));
                record.setLandpageUrl(baseUrl);
                record.setName("");
                record.setGmtCreate(curDate);
                record.setGmtModified(curDate);
                record.setAppId(1474L);
                record.setSlotId(854435L);
                record.setOrigin(1);
                record.setPhone(phone);
                existList.add(record);
                return record;
            }).collect(Collectors.toList());
            liuziLandpageFormRecordService.batchInsert(insertList);
            //回流蚂蚁
            //2.请求request对象构建
            PushUmktBackflowEventRequest request = new PushUmktBackflowEventRequest();
            //2.1回流事件实例id，事件接⼊⽂档⽣成后给到业务⽅
            request.setEventId(eventInstanceId);
            //2.2构建回流事件记录列表，每条记录需要上报记录包含的属性列表内容和要求均会在事件接⼊⽂档中给到
            //构建回流事件列表
            request.setEventRecords(buildBackflowEventRecords(existList, req.getTaskId()));
            //3.请求下发
            try {
                PushUmktBackflowEventResponse response = client.pushUmktBackflowEvent(request);
                log.info("蚂蚁回流结果,resp:{}", JSONObject.toJSONString(response));
            } catch (Exception e) {
                log.error("蚂蚁数据回流异常,e:", e);
            }
        });
        log.info("蚂蚁回流完成");
        return "蚂蚁回流成功";

    }


    private static Date randomDate(Date start, Date end) {
        long random = RandomUtil.randomLong(start.getTime(), end.getTime());
        // 用户表单提交时间范围：8:00到21:00点占比70%，其他时间占比30%
        Date date = new Date(random);
        //8-21算50%，其他时间范围在选择20%的时间记到8-21
        if (date.getHours() >= 8 && date.getHours() <= 21) {
            return date;
        }
        if (RandomUtil.randomInt(0, 5) < 2) {
            return DateUtils.addHours(date, 12);
        }
        return date;
    }


    /**
     * 构建openapi调⽤client
     *
     * @return
     */
    public static Client buildClient() {//线上//预发
        Config config = new Config().setAccessKeyId("ACuRiwegsTxj3ona").setAccessKeySecret("Bs9qK3xntQuW4PixtdQmeMduonA5FcSy");
        if (SpringEnvironmentUtils.isProd()) {
            // 线上⽹关地址
            config.setEndpoint("openapi.antchain.antgroup.com")
                    // 线下⽤http，线上与预发⽤https
                    .setProtocol("HTTPS");
        } else {
            config.setEndpoint("openapi.antchain.antgroup.com")
                    // 线下⽤http，线上与预发⽤https
                    .setProtocol("HTTP");
        }
        Client client = null;
        try {
            client = new Client(config);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return client;
    }


    /**
     * 构建回流事件记录列表，每个事件记录均有松散的数据结构，具体的kv对列表包含属性key、valu
     * e等信息均会在事件接⼊⽂档中体现
     *
     * @return
     */
    private static List<BackflowEventRecord> buildBackflowEventRecords(List<LiuziLandpageFormRecord> list, String taskId) {
        //所有需要传递的记录列表
        //1.构建1个事件记录
        //依据给定接⼝⽂档进⾏准备，例如，事件接⼊⽂档会告知需要上传的属性列表、key、value及必填性
        //举例，事件属性表单需要上传⼿机号、是否注册、是否授信，这3个数据--具体也会体现在事件接⼊⽂档中
        List<BackflowEventRecord> records = list.stream().map(record -> {
            Map<String, String> properties = Maps.newHashMap();
            //此处仅作为举例使⽤，具体的key和value取值范围等均会体现在事件接⼊⽂档中
            properties.put("mobile_md5", SecureUtil.md5(record.getPhone()));
            properties.put("2c_authorize_time", DateUtil.formatDateTime(record.getGmtCreate()));
            properties.put("2c_agreement_link", record.getLandpageUrl());
            properties.put("2c_taskid", taskId);

            //构建事件记录
            BackflowEventRecord eventRecord = buildOneRecord(properties);
            return eventRecord;
        }).collect(Collectors.toList());

        return records;
    }

    /**
     * 创建1个事件记录，可以上传多个BackflowEventRecord，此处仅以1个记录为例
     *
     * @param properties 记录所包含的属性kv对,例如业务属性组需要上传属性列表为：⼿机号、
     *                   是否注册、是否授信, value取值举例:例如-明⽂⼿机号/md5，例如-1/true,-0/false,--均会
     *                   体现在具体的接⼝⽂档中
     * @return
     */
    private static BackflowEventRecord buildOneRecord(Map<String, String> properties) {
        //构建事件记录
        BackflowEventRecord eventRecord = new BackflowEventRecord();
        //属性列表构建
        List<BackflowEventRecordProperty> recordProperties = Lists.newArrayList();
        for (Map.Entry<String, String> property : properties.entrySet()) {
            recordProperties.add(buildOneProperty(property.getKey(), property.getValue()));//--value取值，例如-明⽂⼿机号/md5，例如-1/true,-0/false（枚举类型的取值列表会在接⼝⽂档中进⾏限定，按需传递即可）
        }
        //属性列表赋值
        eventRecord.setProperties(recordProperties);
        return eventRecord;
    }

    /**
     * 构建属性对象
     *
     * @param key
     * @param value
     * @return
     */
    private static BackflowEventRecordProperty buildOneProperty(String key, String value) {
        //属性对象构建
        BackflowEventRecordProperty property = new BackflowEventRecordProperty();
        property.setKey(key);
        property.setValue(value);

        return property;
    }
}
