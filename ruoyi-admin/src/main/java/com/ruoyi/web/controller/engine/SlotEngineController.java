package com.ruoyi.web.controller.engine;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CookieConstant;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.CountMonitorTypeEnum;
import com.ruoyi.common.enums.advert.AdvertCategory;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.enums.common.MapConfigEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.enums.domain.DomainType;
import com.ruoyi.common.utils.CookieUtils;
import com.ruoyi.common.utils.DeviceIdUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.InnerLogUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.bo.slot.SlotShuntTaskBO;
import com.ruoyi.system.domain.adengine.AppCacheDto;
import com.ruoyi.system.domain.adengine.IpAreaDto;
import com.ruoyi.system.domain.adengine.SlotCacheDto;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.domain.slot.AreaTargetRedirectItem;
import com.ruoyi.system.domain.slot.ShuntRedirectItem;
import com.ruoyi.system.message.rocketmq.producer.LogMqProducer;
import com.ruoyi.system.req.engine.ManualParamReq;
import com.ruoyi.system.service.adengine.AdvertEngineService;
import com.ruoyi.system.service.advert.AdvertMauRepeatFilterService;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.consumer.ConsumerService;
import com.ruoyi.system.service.datasource.DataCollectService;
import com.ruoyi.system.service.domain.DomainReplaceService;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.DataHandleService;
import com.ruoyi.system.service.engine.DeviceUidService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.ActivityCacheService;
import com.ruoyi.system.service.engine.cache.AppCacheService;
import com.ruoyi.system.service.engine.cache.DomainCacheService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import com.ruoyi.system.service.engine.cache.WxIfrUrlCacheService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.service.slot.SlotRedirectService;
import com.ruoyi.system.service.slot.SlotShuntDataService;
import com.ruoyi.system.service.slot.SlotShuntTaskService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.util.CountMonitorUtils;
import com.ruoyi.system.util.ShuntUtils;
import com.ruoyi.system.vo.slot.SlotServingVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.constant.BizConstants.ARTICLE_RET_PAGE_DOMAIN;
import static com.ruoyi.common.enums.InnerLogType.SLOT_REQUEST;
import static com.ruoyi.common.enums.common.BizConfigEnum.DEFAULT_ACT_URL;
import static com.ruoyi.common.enums.slot.SlotRedirectType.ACTIVITY_ARPU;
import static com.ruoyi.common.enums.slot.SlotRedirectType.SHUNT;
import static com.ruoyi.common.enums.slot.SlotRedirectType.isAreaTargetRedirect;
import static com.ruoyi.common.enums.slot.SlotRedirectType.isCompositeRedirect;
import static com.ruoyi.common.enums.slot.SlotRedirectType.isShuntRedirect;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToActivity;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToAdvert;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToAdvertOrient;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToText;
import static com.ruoyi.common.enums.slot.SlotStatusEnum.isSlotOpen;

/**
 * 广告位引擎(缓存+不鉴权)
 *
 * <AUTHOR>
 * @date 2021/7/19
 */
@Slf4j
@Controller
@RequestMapping("/st")
public class SlotEngineController {

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private AppCacheService appCacheService;

    @Autowired
    private ActivityCacheService activityCacheService;

    @Autowired
    private DataHandleService dataHandleService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private DomainReplaceService domainReplaceService;

    @Autowired
    private AdvertEngineService advertEngineService;

    @Autowired
    private ConsumerService consumerService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private DomainCacheService domainCacheService;

    @Autowired
    private SlotShuntTaskService slotShuntTaskService;

    @Autowired
    private SlotShuntDataService slotShuntDataService;

    @Autowired
    private SlotRedirectService slotRedirectService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private LogMqProducer logMqProducer;

    @Autowired
    private DataCollectService dataCollectService;

    @Autowired
    private DeviceUidService deviceUidService;

    @Autowired
    private WxIfrUrlCacheService wxIfrUrlCacheService;

    @Autowired
    private StatService statService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private AdvertMauRepeatFilterService advertMauRepeatFilterService;

    @Autowired
    private WhitelistService whitelistService;


    private static int count = 0;

    /**
     * 活动访问：http://localhost:8778/st/open?appKey=123&sid=123
     * <p>
     * 活动链接：String activityUrl = "http://localhost:9998/act/turntable/n?appKey=&slotId=&srid=&deviceId=";
     */
    @CrossOrigin
    @GetMapping("/open")
    public void index(ManualParamReq req, HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 0.参数校验
        if (StringUtils.isBlank(req.getAppKey()) || StringUtils.isBlank(req.getSid())) {
            return;
        }
        SlotCacheDto slot = slotCacheService.getSlotCache(Convert.convertQuietly(Long.class, req.getSid()));
        if (null == slot || !isSlotOpen(slot.getStatus())) {
            return;
        }
        AppCacheDto app = appCacheService.getAppCache(req.getAppKey());
        if (null == app || !Objects.equals(slot.getAppId(), app.getId())) {
            return;
        }

        Date now = new Date();
        Date today = DateUtil.beginOfDay(now);
        int hour = DateUtil.thisHour(true);
        Long slotId = slot.getId();
        Long appId = app.getId();
        String appKey = req.getAppKey();
        String ip = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        String referer = request.getHeader("referer");
        String srid = generateSrid();
        Integer redirectType = slot.getRedirectType();
        String redirectValue = slot.getRedirectValue();
        //默认关闭前置空白页
        Boolean useEmptyPage = false;
        String deviceId = getOrGenerateDeviceId(request, response);
        boolean isWxEnv = StrUtil.containsAnyIgnoreCase(userAgent, "micromessenger");

        // 1.记录广告位请求日志
        JSONObject json = new JSONObject();
        json.put("slotId", slotId);
        json.put("appId", appId);
        json.put("srid", srid);
        json.put("ip", ip);
        json.put("deviceId", deviceId);
        json.put("userAgent", userAgent);
        json.put("referer", StringUtils.defaultString(referer));
        InnerLogUtils.log(SLOT_REQUEST, json);
        logMqProducer.sendMsg(SLOT_REQUEST, json);

        // 2.广告位访问数据统计
        dataHandleService.addSlotRequestData(app.getAccountId(), appId, slotId, deviceId);

        // 3.广告位切量计划
        SlotShuntTaskBO shuntTask = slotShuntTaskService.shunt(appId, slotId, deviceId);
        if (null != shuntTask && null != shuntTask.getRedirectType() && null != shuntTask.getRedirectValue()) {
            redirectType = shuntTask.getRedirectType();
            redirectValue = shuntTask.getRedirectValue();

            // 广告位切量数据统计
            slotShuntDataService.statistics(slotId, deviceId, shuntTask.getId(), shuntTask.getShuntType(), today);
        }

        // 4.广告投放地域定向和分流的处理
        if (isCompositeRedirect(redirectType)) {
            List<ShuntRedirectItem> shuntRedirectItems = null;
            // 地域定向跳转
            if (isAreaTargetRedirect(redirectType)) {
                AreaTargetRedirectItem redirectItem = redirectByArea(slot.getRedirectItems(), ip);
                if (null == redirectItem) {
                    log.error("广告位地域定向跳转异常, appId={}, slotId={}, deviceId={}", appId, slotId, deviceId);
                    return;
                }
                redirectType = SHUNT.getType();
                shuntRedirectItems = redirectItem.getRedirectValue();
            }
            // 分流跳转处理
            if (isShuntRedirect(redirectType)) {
                if (CollectionUtils.isEmpty(shuntRedirectItems)) {
                    log.error("广告位分流跳转异常, appId={}, slotId={}, deviceId={}", appId, slotId, deviceId);
                    return;
                }
                Integer hash = Math.abs((deviceId + appId).hashCode());
                ShuntRedirectItem redirectItem = ShuntUtils.shunt(shuntRedirectItems, hash);
                if (null == redirectItem) {
                    log.error("广告位分流跳转异常, appId={}, slotId={}, deviceId={}", appId, slotId, deviceId);
                    return;
                }
                redirectType = redirectItem.getRedirectType();
                redirectValue = redirectItem.getRedirectValue();
                useEmptyPage = redirectItem.getUseEmptyPage();
            }
        }

        // 5.广告位投放数据统计
        slotRedirectService.statistics(slotId, today, hour, deviceId, redirectType, redirectValue);

        // 缓存真实投放的广告位链接
        callbackService.cacheActualSlotUrl(request, srid);

        // 广告位点击监测
        slotRedirectService.clickCallback(request);

        // 6. 广告位最终投放
        if (null != redirectType && null != redirectValue) {
            // 微信环境下广告位访问数据统计
            dataCollectService.slotRequestInWechat(slotId, isWxEnv);
            // 获取链接上的所有参数
            Map<String, String> paramMap = ServletUtil.getParamMap(request);
            // 缓存设备唯一标识(IMEI/OAID/IDFA)
            deviceUidService.cacheParameter(paramMap, appId, deviceId);

            if (redirectToActivity(redirectType)) {
                // 活动参数处理
                paramMap.put("appKey", appKey);
                paramMap.put("slotId", String.valueOf(slotId));
                paramMap.put("srid", srid);
                paramMap.put("deviceId", deviceId);

                // ARPU排序标识
                if (Objects.equals(redirectType, ACTIVITY_ARPU.getType())) {
                    paramMap.put("sortType", "1");
                }

                paramMap.remove("sid");
                activityParamHandle(request, paramMap);

                // 生成活动链接
                String activityUrl = generateActivityUrl(redirectValue, paramMap);
                // 活动域名替换
                if (null != slot.getDomainConfig()) {
                    activityUrl = domainReplaceService.doReplaceActivityDomain(request, activityUrl, slot.getDomainConfig());
                    String activityProtocol = slot.getDomainConfig().getString("activityProtocol");
                    if (null != activityProtocol && Objects.equals(activityProtocol, "http")) {
                        activityUrl = activityUrl.replace("https", "http");
                    }
                }
                // 微信环境下跳转活动的广告位访问数据统计
                dataCollectService.slotRequestRedirectActivityInWechat(activityUrl, isWxEnv);

                // 如果存在指定标签，则域名选择微信可用域名
                if (slotCacheService.isTagExistBySlotId(slot.getId(), "活动微信防封")) {
                    activityUrl = handleIfrActivityUrl(activityUrl, slotId, redirectValue);
                }

                response.sendRedirect(activityUrl);
            } else if (redirectToAdvert(redirectType)) {
                // 这块废弃了，不再维护添加新功能，以后面的广告配置为准
                // IP地址解析
                IpAreaDto ipArea = areaService.ipAnalysis(ip);
                // 生成订单号
                Long consumerId = consumerService.getOrCreateConsumer(appId, deviceId);
                String orderId = orderService.generateOrderId(consumerId);
                // 广告MAU去重
                JSONObject extParam = new JSONObject();
                extParam.put("openId", advertMauRepeatFilterService.codeToOpenId(paramMap.get("mmCode"), consumerId, paramMap.get("mmOpenId")));

                Pair<Order, String> advertResp;
                // 单广告
                if (StringUtils.isNumeric(redirectValue)) {
                    advertResp = advertEngineService.getNhDirectAdvert(orderId, consumerId, app.getAccountId(), appId, slotId, deviceId, srid, Long.valueOf(redirectValue), ip, userAgent, ipArea, extParam, false);
                }
                // 多广告
                else {
                    advertResp = advertEngineService.getNhDirectAdvert(orderId, consumerId, app.getAccountId(), appId, slotId, deviceId, srid, StrSplitter.split(redirectValue, ',', -1, true, Long::valueOf), ip, userAgent, ipArea, extParam);
                }
                if (null == advertResp || StringUtils.isBlank(advertResp.getValue())) {
                    // 使用兜底广告
                    List<Long> degradedOrientIds = slotCacheService.getDegradedDirectAdvertOrientIds(slotId);
                    advertResp = advertEngineService.getNhDirectAdvertOrientByDegraded(orderId, consumerId, app.getAccountId(), appId, slotId, deviceId, srid, degradedOrientIds, ip, userAgent, ipArea, extParam);
                }
                if (null == advertResp || StringUtils.isBlank(advertResp.getValue())) {
                    String msg = "广告位: " + slotId
                            + "\n直投广告ID: " + redirectValue
                            + "\n省市: " + ipArea.getProvince() + "," + ipArea.getCity();
                    CountMonitorUtils.errorMonitor(CountMonitorTypeEnum.DIRECT_ADVERT, DingWebhookConfig.getDataAlert(), msg);
                    return;
                }
                // 空白页跳转
                String landpageUrl = generateBlankRedirectUrl(slotId, advertResp.getValue(), useEmptyPage);
                //支付宝小程序特殊处理
                if(paramMap.containsKey("authCode")){
                    HashMap<String, String> authCodeMap = new HashMap<>();
                    authCodeMap.put("authCode",paramMap.get("authCode"));
                    landpageUrl = UrlUtils.appendParams(landpageUrl, authCodeMap);
                }
                // 缓存媒体参数
                callbackService.cacheParameter(request, orderId);
                // 券点击埋点
                statService.directAdvertClick(now, app.getAccountId(), advertResp.getKey(), json);
                // 跳转落地页
                response.sendRedirect(landpageUrl);
            } else if (redirectToAdvertOrient(redirectType)) {
                // IP地址解析
                IpAreaDto ipArea = areaService.ipAnalysis(ip);
                // 生成订单号
                Long consumerId = consumerService.getOrCreateConsumer(appId, deviceId);
                String orderId = orderService.generateOrderId(consumerId);
                // 广告MAU去重
                JSONObject extParam = new JSONObject();
                extParam.put("openId", advertMauRepeatFilterService.codeToOpenId(paramMap.get("mmCode"), consumerId, paramMap.get("mmOpenId")));

                Pair<Order, String> advertResp = advertEngineService.getNhDirectAdvertOrient(orderId, consumerId, app.getAccountId(), appId, slotId, deviceId, srid, StrSplitter.split(redirectValue, ',', -1, true, Long::valueOf), ip, userAgent, ipArea, extParam);
                if (null == advertResp || null == advertResp.getKey()) {
                    // 使用兜底广告
                    List<Long> degradedOrientIds = slotCacheService.getDegradedDirectAdvertOrientIds(slotId);
                    advertResp = advertEngineService.getNhDirectAdvertOrientByDegraded(orderId, consumerId, app.getAccountId(), appId, slotId, deviceId, srid, degradedOrientIds, ip, userAgent, ipArea, extParam);
                }
                if (null == advertResp || null == advertResp.getKey()) {
                    String msg = "广告位: " + slotId
                            + "\n直投广告ID: " + redirectValue
                            + "\n省市: " + ipArea.getProvince() + "," + ipArea.getCity();
                    CountMonitorUtils.errorMonitor(CountMonitorTypeEnum.DIRECT_ADVERT, DingWebhookConfig.getDataAlert(), msg);
                    return;
                }
                // mau广告检查
                mauAdvertCheck(advertResp.getKey(), extParam.getString("openId"));
                // 空白页跳转
                String landpageUrl = generateBlankRedirectUrl(slotId, advertResp.getValue(), useEmptyPage);
                if (StringUtils.isNotEmpty(landpageUrl) && landpageUrl.contains("weixin")) {
                    count++;
                    log.info("testld，" + count + "：" + landpageUrl);
                    if (count > 100000) {
                        count = 0;
                    }
                }
                //支付宝小程序特殊处理
                if(paramMap.containsKey("authCode")){
                    HashMap<String, String> authCodeMap = new HashMap<>();
                    authCodeMap.put("authCode",paramMap.get("authCode"));
                    landpageUrl = UrlUtils.appendParams(landpageUrl, authCodeMap);
                }
                // 缓存媒体参数
                callbackService.cacheParameter(request, orderId);
                // 券点击埋点
                statService.directAdvertClick(now, app.getAccountId(), advertResp.getKey(), json);
                // 文章插件
                Map<Long, Integer> articlePluginMap = mapConfigService.getMap(MapConfigEnum.SLOT_ARTICLE_PLUGIN_MAP, Long.class, Integer.class);
                if (articlePluginMap.containsKey(slotId) || StringUtils.isNotBlank(paramMap.get("mmCode")) || StringUtils.isNotBlank(paramMap.get("mmOpenId"))) {
                    JSONObject articleResult = new JSONObject();
                    articleResult.put("returnTimes", articlePluginMap.get(slotId));
                    articleResult.put("jumpUrl", landpageUrl);
                    ServletUtils.renderString(response, articleResult.toString());
                }
                // 文本形式返回落地页和appId/appPath
                else if (Objects.equals(paramMap.get("format"), "json")){
                    JSONObject joResult = new JSONObject();
                    joResult.put("url", landpageUrl);

                    AdSnapshot adSnapshot = JSON.parseObject(advertResp.getKey().getAdSnapshot(), AdSnapshot.class);
                    if (null != adSnapshot && null != adSnapshot.getExtInfo()) {
                        if (Objects.equals(adSnapshot.getAdvertCategory(), AdvertCategory.MINI_PROGRAM.getType())) {
                            joResult.put("appId", adSnapshot.getExtInfo().getAppId());
                            joResult.put("appPath", adSnapshot.getExtInfo().getAppPath());
                        }
                    }
                    ServletUtils.renderString(response, joResult.toString());
                } else {
                    // 跳转落地页
                    response.sendRedirect(landpageUrl);
                }
            } else if (redirectToText(redirectType)) {
                try {
                    response.setStatus(200);
                    response.setContentType("text/plain");
                    response.setCharacterEncoding("utf-8");
                    response.getWriter().print(redirectValue);

                } catch (IOException e) {
                    log.error("广告位跳转文本异常, slotId={}", req.getSid(), e);
                }
            } else {
                paramMap.remove("sid");
                paramMap.remove("appKey");
                response.sendRedirect(UrlUtils.appendParams(redirectValue, paramMap));
            }
        }
    }


    /**
     * 获取广告位链接
     */
    @CrossOrigin
    @ResponseBody
    @RequestMapping(value = "/serving", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<SlotServingVO> serving(ManualParamReq req, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (StringUtils.isBlank(req.getAppKey()) || !StringUtils.isNumeric(req.getSid())) {
            return ResultBuilder.fail("参数不能为空");
        }
        SlotCacheDto slot = slotCacheService.getSlotCache(Long.valueOf(req.getSid()));
        if (null == slot || !isSlotOpen(slot.getStatus())) {
            return ResultBuilder.fail("无效的sid");
        }
        AppCacheDto app = appCacheService.getAppCache(req.getAppKey());
        if (null == app || !Objects.equals(slot.getAppId(), app.getId())) {
            return ResultBuilder.fail("无效的appKey");
        }

        String slotUrl = slot.getSlotUrl();

        // 域名替换
        JSONObject domainConfig = slot.getDomainConfig();
        if (null != domainConfig) {
            slotUrl = domainReplaceService.doReplaceDomain(slot.getSlotUrl(), domainConfig.getString("slotDomain"));
        }

        // 如果存在指定标签，则域名选择微信可用域名
        if (slotCacheService.isTagExistBySlotId(slot.getId(), "微信自动替换")) {
            Set<String> domainPool = domainCacheService.selectWechatValidDomainListCache(DomainType.SLOT_DOMAIN.getType());
            if (CollectionUtils.isNotEmpty(domainPool)) {
                String originDomain = UrlUtils.extractDomain(slotUrl);
                if (!domainPool.contains(originDomain)) {
                    slotUrl = domainReplaceService.doReplaceDomain(slotUrl, domainPool.iterator().next());
                }
            }
        }

        SlotServingVO slotServingVO = new SlotServingVO();
        slotServingVO.setSlotUrl(slotUrl);
        return ResultBuilder.success(slotServingVO);
    }

    /**
     * 广告位地域定向过滤
     */
    private AreaTargetRedirectItem redirectByArea(List<AreaTargetRedirectItem> redirectItems, String ip) {
        if (CollectionUtils.isEmpty(redirectItems)) {
            return null;
        }

        // 只有默认地域
        if (redirectItems.size() == 1 && CollectionUtils.isEmpty(redirectItems.get(0).getTargetArea())) {
            return redirectItems.get(0);
        }

        // 根据地域定向
        IpAreaDto ipArea = areaService.ipAnalysis(ip);
        AreaTargetRedirectItem defaultItem = null;
        AreaTargetRedirectItem backupItem = null;
        for (AreaTargetRedirectItem item : redirectItems) {
            if (CollectionUtils.isEmpty(item.getTargetArea())) {
                defaultItem = item;
            }
            if (null == ipArea) {
                continue;
            }
            if (item.getTargetArea().contains(ipArea.getCityAreaNum())) {
                return item;
            }
            if (item.getTargetArea().contains(ipArea.getProvinceAreaNum())) {
                backupItem = item;
            }
        }
        if (null != backupItem) {
            return backupItem;
        }
        // 兜底
        if (null == ipArea || StringUtils.isBlank(ipArea.getCityAreaNum())) {
            for (AreaTargetRedirectItem item : redirectItems) {
                if (Objects.equals(item.getDesc(), "解析IP兜底")) {
                    return item;
                }
            }
        }
        return defaultItem;
    }

    /**
     * 生成广告位跳转的请求标识
     */
    private String generateSrid() {
        return IdUtils.fastSimpleUUID();
    }

    /**
     * 生成设备号
     */
    private String getOrGenerateDeviceId(HttpServletRequest request, HttpServletResponse response) {
        String deviceId = CookieUtils.getCookieByName(request, CookieConstant.DEVICE_ID);
        if (StringUtils.isBlank(deviceId)) {
            deviceId = DeviceIdUtils.getOrGenerate(request);
            CookieUtils.setCookie(response, CookieConstant.DEVICE_ID, deviceId);
        }
        return deviceId;
    }

    /**
     * 活动参数处理
     *
     * @param request 请求
     * @param paramMap 参数映射
     */
    private void activityParamHandle(ServletRequest request, Map<String, String> paramMap) {
        // callback参数Base64处理,喜马拉雅上报时使用
        Optional.ofNullable(request.getParameter("callback")).ifPresent(callback -> {
            paramMap.put("callbackBase64", Base64.encode(callback));
            paramMap.remove("callback");
        });
        // callbackUrl参数Base64处理,趣头条上报时使用
        Optional.ofNullable(request.getParameter("callback_url")).ifPresent(callback -> {
            paramMap.put("qttCallbackBase64", Base64.encode(callback));
            paramMap.remove("callback_url");
        });
        // Sigmob参数处理
        if (StrUtil.startWithIgnoreCase(request.getParameter("get_callback"), "http")) {
            String getCallback = request.getParameter("get_callback");
            String getCallbackMd5 = SecureUtil.md5(getCallback);
            paramMap.put("get_callback_md5", getCallbackMd5);
            paramMap.remove("get_callback");
            redisCache.setCacheObject(EngineRedisKeyFactory.K122.join(getCallbackMd5), getCallback, 1, TimeUnit.DAYS);
        }
    }

    /**
     * 生成活动链接
     *
     * @param activityIdStr 活动ID
     * @param paramMap 活动链接上拼接的参数
     * @return 活动链接
     */
    private String generateActivityUrl(String activityIdStr, Map<String, String> paramMap) {
        if (!StringUtils.isNumeric(activityIdStr)) {
            return "";
        }

        // 活动皮肤对应的路径
        String redirectPath = activityCacheService.getRedirectPathCache(Long.valueOf(activityIdStr));
        // 活动链接域名
        String activityUrl = sysConfigService.selectConfigCacheByKey(DEFAULT_ACT_URL.getKey());
        // 拼接参数
        return UrlUtils.appendParams(activityUrl + redirectPath, paramMap);
    }

    /**
     * 生成空白页跳转链接
     *
     * @param slotId 广告位ID
     * @param url 原链接
     * @return 空白页跳转链接
     */
    private String generateBlankRedirectUrl(Long slotId, String url,Boolean useEmptyPage) {
        if (null == slotId || StringUtils.isBlank(url)) {
            return url;
        }
        try {
            //广告位直投空白页开关查询
            if(BooleanUtils.isNotTrue(useEmptyPage)){
                return url;
            }
            // 文章返回拦截来的不再经过空白页
            if (Objects.equals(ServletUtils.getParameter("ret"), "1")) {
                return url;
            }
            // 公众号文章定制空白页
            // 其他场景别用，域名被封功能就gg了
            if (url.startsWith("https://mp.weixin.qq.com")) {
                return advertEngineService.cdnRedirect(url);
            }
            // 文章已经使用定制空白页，不用特殊处理
            if (url.contains(ARTICLE_RET_PAGE_DOMAIN)) {
                return url;
            }

            // 空白页默认链接
            String emptyPageUrl = sysConfigService.selectConfigCacheByKey(BizConfigEnum.DEFAULT_DOMAIN_EMPTYPAGE.getKey());
            if (StringUtils.isBlank(emptyPageUrl)) {
                return url;
            }
            // 返回自动跳过空白页的空白页
            if (Objects.equals(slotId, 853317L)) {
                emptyPageUrl = emptyPageUrl.replace("redirect.html", "redirectv2.html");
            }
            // 拼接空白页链接
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("url", UrlUtils.urlEncode(url));
            return UrlUtils.appendParams(emptyPageUrl, paramMap);
        } catch (Exception e) {
            log.error("直投广告生成空白页链接异常, slotId={}, url={}", slotId, url, e);
        }
        return url;
    }

    /**
     * 处理跳转微信防封的活动链接
     *
     * @param originUrl 活动原链接
     * @param slotId 广告位ID
     * @param activityIdStr 活动ID
     * @return 微信防封处理后的活动链接
     */
    private String handleIfrActivityUrl(String originUrl, Long slotId, String activityIdStr) {
        // 非微信环境下不处理
        if (!StrUtil.containsIgnoreCase(ServletUtils.getUserAgent(), "MicroMessenger")) {
            return originUrl;
        }
        // 获取微信防封链接
        String ifrUrl = wxIfrUrlCacheService.getWxIfrUrl(slotId + "-" + activityIdStr);
        if (StringUtils.isBlank(ifrUrl)) {
            return originUrl;
        }
        // 使用防封链接添加标识
        return (originUrl.startsWith("http://") ? "http://" : "https://") + ifrUrl + UrlUtils.urlEncode(originUrl);
    }

    /**
     * mau广告重复出券检查
     */
    private void mauAdvertCheck(Order order, String openId) {
        if (null == order || StringUtils.isBlank(openId)) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            Long advertId = order.getAdvertId();
            if (!whitelistService.contains(WhitelistType.MAU_REPEAT_FILTER_ADVERT, advertId)) {
                return;
            }
            String key = EngineRedisKeyFactory.K148.join(advertId, openId);
            if (redisCache.hasKey(key)) {
                log.error("MAU广告重复出券, advertId={}, openId={}, orderId={}, 上次发券时间={}", advertId, openId, order.getOrderId(), redisCache.getCacheObject(key));
                return;
            }
            redisCache.setCacheObject(key, DateUtil.now(), 30, TimeUnit.DAYS);
        });
    }
}
