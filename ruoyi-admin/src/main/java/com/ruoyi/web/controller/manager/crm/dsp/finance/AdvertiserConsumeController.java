package com.ruoyi.web.controller.manager.crm.dsp.finance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeExcelBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeOffsetBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserDaySumDataBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserFianceStatisticsRecordEntity;
import com.ruoyi.system.entity.landpage.AdvertiserFormDataEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.advertiser.AdvertiserConsumeRecordManagerService;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeUpdateReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordListReq;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserFianceStatisticsRecordService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.datasource.AdvertDayDataService;
import com.ruoyi.system.service.landpage.AdvertiserFormDataService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.advertiser.finance.AdvertiserConsumeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;
import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * 广告主消费记录Controller
 *
 * <AUTHOR>
 * @date 2021-08-19
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/fiance/consume")
public class AdvertiserConsumeController extends BaseController {

    @Autowired
    private AdvertiserFianceStatisticsRecordService advertiserFianceStatisticsRecordService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private AdvertDayDataService advertDayDataService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private AdvertiserConsumeRecordManagerService advertiserConsumeRecordManagerService;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AdvertiserFormDataService advertiserFormDataService;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    @Autowired
    private AdvertiserConsumeRecordService advertiserConsumeRecordService;

    /**
     * 查询广告主消费记录
     */
    @GetMapping("/list")
    public TableDataInfo list(AdvertiserConsumeReq req) {
        // 参数处理
        AdvertiserFianceStatisticsRecordListReq param = convertTo(req);
        if (CollUtil.contains(param.getAccountIds(), -1L)) {
            return getDataTable(Collections.emptyList());
        }
        // 查询数据
        startPage();
        List<AdvertiserFianceStatisticsRecordEntity> list = advertiserFianceStatisticsRecordService.selectList(param);
        if (CollectionUtils.isEmpty(list)) {
            return getDataTable(Collections.emptyList());
        }

        Date today = DateUtil.beginOfDay(new Date());
        List<Long> accountIds = ListUtils.mapToList(list, AdvertiserFianceStatisticsRecordEntity::getAccountId);
        Map<String, AdvertiserFormDataEntity> formDataMap = advertiserFormDataService.selectMapByDateAndAdvertiserIds(req.getStartDate(), req.getEndDate(), accountIds);
        Map<String, AdvertiserDaySumDataBo> advertiserDayDataMap = advertDayDataService.groupByDateAndAdvertiserId(req.getStartDate(), req.getEndDate(), accountIds);
        List<Long> editableAdvertiserIds = whitelistService.list(OFFLINE_DATA_ADVERTISER, Long.class);
        Map<String, DspAdvertiserConsumeRecordEntity> advertiserConsumeRecordMap = dspAdvertiserConsumeRecordService.selectMap(req.getStartDate(), req.getEndDate(), accountIds);
        Map<Long, String> operatorNameMap = accountService.selectAccountContactMap(advertiserConsumeRecordMap.values().stream().map(DspAdvertiserConsumeRecordEntity::getOperatorId).distinct().collect(Collectors.toList()));
        Map<Long, List<AdvertiserConsumeOffsetBo>> consumeOffsetMap = dspAdvertiserConsumeRecordService.batchConsumeOffset(req.getEndDate(), accountIds);
        Map<Long, Account> agentMap = agentService.selectAgentMap(accountIds);
        Map<Long, Account> accountMap = accountService.selectMapByIds(accountIds);

        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            AdvertiserConsumeVO consumerVO = BeanUtil.copyProperties(record, AdvertiserConsumeVO.class);
            Optional.ofNullable(accountMap.get(record.getAccountId())).ifPresent(account -> {
                consumerVO.setMainType(account.getMainType());
                consumerVO.setCompanyName(account.getCompanyName());
            });
            Optional.ofNullable(agentMap.get(record.getAccountId())).ifPresent(agent -> {
                consumerVO.setAgentId(agent.getId());
                consumerVO.setAgentName(agent.getCompanyName());
            });
            consumerVO.setIsEditable(editableAdvertiserIds.contains(record.getAccountId()) && record.getCurDate().before(today) ? 1 : 0);
            consumerVO.setIsVisible((!editableAdvertiserIds.contains(record.getAccountId()) || record.getCurDate().before(today)) ? 1 : 0);

            String formCountKey = DateUtil.formatDate(record.getCurDate()) + "_" + record.getAccountId();
            consumerVO.setFormCount(0);
            consumerVO.setTotalFormCount(0);
            Optional.ofNullable(formDataMap.get(formCountKey)).ifPresent(data -> {
                consumerVO.setFormCount(data.getSuccessFormCount());
                consumerVO.setTotalFormCount(data.getFormCount());
            });
            Optional.ofNullable(advertiserDayDataMap.get(formCountKey)).ifPresent(data -> {
                consumerVO.setBillingClickPv(data.getBillingClickPv());
                consumerVO.setBillingClickUv(data.getBillingClickUv());
            });
            Optional.ofNullable(advertiserConsumeRecordMap.get(formCountKey)).ifPresent(data -> {
                consumerVO.setBillingClickPv(data.getBillingClickPv());
                consumerVO.setBillingClickUv(data.getBillingClickUv());
                consumerVO.setConsumeAmount(data.getConsumeAmount());
                if (data.getIsVisible() == 1) {
                    consumerVO.setOperatorName(operatorNameMap.get(data.getOperatorId()));
                    consumerVO.setOperatorTime(data.getGmtModified());
                } else {
                    consumerVO.setIsVisible(0);
                }
            });
            Optional.ofNullable(consumeOffsetMap.get(record.getAccountId())).ifPresent(offsetList -> {
                int offset = offsetList.stream()
                        .filter(bo -> !bo.getCurDate().after(record.getCurDate()) && bo.getOffset() != null)
                        .mapToInt(AdvertiserConsumeOffsetBo::getOffset).sum();
                consumerVO.setBalanceAmount(consumerVO.getBalanceAmount() - offset);
            });
            return consumerVO;
        }));
    }

    /**
     * 导出广告主消费记录
     */
    @Log(title = "广告主消费记录列表", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(AdvertiserConsumeReq req) {
        String fileName = UUID.randomUUID().toString() + "_广告主消费记录.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;

        // 参数处理
        AdvertiserFianceStatisticsRecordListReq param = convertTo(req);
        if (CollUtil.contains(param.getAccountIds(), -1L)) {
            EasyExcel.write(filePath, AdvertiserConsumeExcelBo.class).sheet().doWrite(Collections.emptyList());
            return AjaxResult.success(fileName);
        }

        // 查询数据
        List<AdvertiserFianceStatisticsRecordEntity> list = advertiserFianceStatisticsRecordService.selectList(param);
        // 补充数据
        Date today = DateUtil.beginOfDay(new Date());
        List<Long> advertiserIds = ListUtils.mapToList(list, AdvertiserFianceStatisticsRecordEntity::getAccountId);
        Map<String, AdvertiserFormDataEntity> formDataMap = advertiserFormDataService.selectMapByDateAndAdvertiserIds(req.getStartDate(), req.getEndDate(), advertiserIds);
        Map<String, AdvertiserDaySumDataBo> advertiserDayDataMap = advertDayDataService.groupByDateAndAdvertiserId(req.getStartDate(), req.getEndDate(), advertiserIds);
        Map<String, DspAdvertiserConsumeRecordEntity> advertiserConsumeRecordMap = dspAdvertiserConsumeRecordService.selectMap(req.getStartDate(), req.getEndDate(), advertiserIds);
        Map<Long, String> operatorNameMap = accountService.selectAccountContactMap(advertiserConsumeRecordMap.values().stream().map(DspAdvertiserConsumeRecordEntity::getOperatorId).distinct().collect(Collectors.toList()));
        Map<Long, List<AdvertiserConsumeOffsetBo>> consumeOffsetMap = dspAdvertiserConsumeRecordService.batchConsumeOffset(req.getEndDate(), advertiserIds);
        List<Long> editableAdvertiserIds = whitelistService.list(OFFLINE_DATA_ADVERTISER, Long.class);
        Map<Long, Account> agentMap = agentService.selectAgentMap(advertiserIds);
        Map<Long, Account> accountMap = accountService.selectMapByIds(advertiserIds);
        Map<Long, String> managerNameMap = accountService.selectManagerMap(advertiserIds);
        // 构造结果
        List<AdvertiserConsumeExcelBo> recordExcelList = list.stream().map(record -> {
            AdvertiserConsumeExcelBo excelBo = BeanUtil.copyProperties(record, AdvertiserConsumeExcelBo.class);
            excelBo.setManagerName(managerNameMap.getOrDefault(record.getAccountId(), ""));
            Optional.ofNullable(agentMap.get(record.getAccountId())).ifPresent(agent -> {
                excelBo.setAgentId(agent.getId());
                excelBo.setAgentName(agent.getCompanyName());
            });
            Optional.ofNullable(accountMap.get(record.getAccountId())).ifPresent(account -> {
                if (isAgent(account.getMainType())) {
                    excelBo.setMainTypeStr("代理商");
                    excelBo.setAgentId(account.getId());
                    excelBo.setAgentName(account.getCompanyName());
                } else if (isAdvertiser(account.getMainType())) {
                    excelBo.setAdvertiserId(account.getId());
                    excelBo.setAdvertiserName(account.getCompanyName());
                    excelBo.setMainTypeStr(null != excelBo.getAgentId() ? "子广告主" : "直客");
                }
            });
            excelBo.setIsVisible((!editableAdvertiserIds.contains(record.getAccountId()) || record.getCurDate().before(today)) ? "已同步" : "未同步");

            String formCountKey = DateUtil.formatDate(record.getCurDate()) + "_" + record.getAccountId();
            excelBo.setFormCount(0);
            excelBo.setTotalFormCount(0);
            Optional.ofNullable(formDataMap.get(formCountKey)).ifPresent(data -> {
                excelBo.setFormCount(data.getSuccessFormCount());
                excelBo.setTotalFormCount(data.getFormCount());
            });
            Optional.ofNullable(advertiserDayDataMap.get(formCountKey)).ifPresent(data -> {
                excelBo.setBillingClickPv(data.getBillingClickPv());
                excelBo.setBillingClickUv(data.getBillingClickUv());
            });
            Optional.ofNullable(advertiserConsumeRecordMap.get(formCountKey)).ifPresent(data -> {
                excelBo.setBillingClickPv(data.getBillingClickPv());
                excelBo.setBillingClickUv(data.getBillingClickUv());
                excelBo.setConsumeAmount(data.getConsumeAmount());
                if (data.getIsVisible() == 1) {
                    excelBo.setOperatorName(operatorNameMap.get(data.getOperatorId()));
                    excelBo.setOperatorTime(data.getGmtModified());
                } else {
                    excelBo.setIsVisible("未同步");
                }
            });
            Optional.ofNullable(consumeOffsetMap.get(record.getAccountId())).ifPresent(offsetList -> {
                int offset = offsetList.stream()
                        .filter(bo -> !bo.getCurDate().after(record.getCurDate()) && bo.getOffset() != null)
                        .mapToInt(AdvertiserConsumeOffsetBo::getOffset).sum();
                excelBo.setBalanceAmount(excelBo.getBalanceAmount() - offset);
            });
            return excelBo;
        }).collect(Collectors.toList());

        EasyExcel.write(filePath, AdvertiserConsumeExcelBo.class).sheet().doWrite(recordExcelList);
        return AjaxResult.success(fileName);
    }

    /**
     * 修改广告主消费数据
     */
    @PostMapping("updateAdvertiserConsumeData")
    @Log(title = "广告主消费记录", businessType = BusinessType.UPDATE)
    public Result<Boolean> updateAdvertiserConsumeData(@RequestBody @Validated AdvertiserConsumeUpdateReq req) {
        if (!whitelistService.contains(OFFLINE_DATA_ADVERTISER, req.getAdvertiserId())) {
            return ResultBuilder.fail("非白名单广告主不支持修改");
        }
        Date today = DateUtil.beginOfDay(new Date());
        if (!req.getCurDate().before(today)) {
            return ResultBuilder.fail("不允许修改今天的数据");
        }
        advertiserConsumeRecordManagerService.updateConsumeRecord(req);
        advertService.sendCheckBudgetMsgByAdvertiserId(req.getAdvertiserId());
        // 如果没有消费记录，则进行初始化
        advertiserConsumeRecordService.update(req.getAdvertiserId(), req.getCurDate(), 0);
        return ResultBuilder.success(true);
    }

    /**
     * 查询消费汇总
     */
    @GetMapping("/statisticConsume")
    public Result<AdvertiserConsumeVO> statisticConsume(AdvertiserConsumeReq req) {
        // 参数处理
        AdvertiserFianceStatisticsRecordListReq param = convertTo(req);
        param.setExcludeAccountIds(advertiserTagService.getAdvertiserByTag("测试账号"));
        // 查询数据
        List<AdvertiserFianceStatisticsRecordEntity> list = advertiserFianceStatisticsRecordService.selectList(param);
        if (CollectionUtils.isEmpty(list)) {
            return ResultBuilder.success();
        }
        // 补充数据
        List<Long> accountIds = ListUtils.mapToList(list, AdvertiserFianceStatisticsRecordEntity::getAccountId);
        AdvertiserFormDataEntity formData = advertiserFormDataService.sumFormDataByDateAndAdvertiserIds(req.getStartDate(), req.getEndDate(), accountIds);
        Map<String, AdvertiserDaySumDataBo> advertiserDayDataMap = advertDayDataService.groupByDateAndAdvertiserId(req.getStartDate(), req.getEndDate(), accountIds);
        Map<String, DspAdvertiserConsumeRecordEntity> advertiserConsumeRecordMap = dspAdvertiserConsumeRecordService.selectMap(req.getStartDate(), req.getEndDate(), accountIds);
        // 构造结果
        AdvertiserConsumeVO consumerVO = new AdvertiserConsumeVO();
        consumerVO.setFormCount(formData.getSuccessFormCount());
        consumerVO.setTotalFormCount(formData.getFormCount());
        for (AdvertiserFianceStatisticsRecordEntity record : list) {
            AdvertiserConsumeVO tmp = new AdvertiserConsumeVO();
            tmp.setConsumeAmount(record.getConsumeAmount());
            String formCountKey = DateUtil.formatDate(record.getCurDate()) + "_" + record.getAccountId();
            Optional.ofNullable(advertiserDayDataMap.get(formCountKey)).ifPresent(data -> {
                tmp.setBillingClickPv(data.getBillingClickPv());
                tmp.setBillingClickUv(data.getBillingClickUv());
            });
            Optional.ofNullable(advertiserConsumeRecordMap.get(formCountKey)).ifPresent(data -> {
                tmp.setBillingClickPv(data.getBillingClickPv());
                tmp.setBillingClickUv(data.getBillingClickUv());
                tmp.setConsumeAmount(data.getConsumeAmount());
            });
            consumerVO.setBillingClickPv(NumberUtils.defaultInt(consumerVO.getBillingClickPv()) + NumberUtils.defaultInt(tmp.getBillingClickPv()));
            consumerVO.setBillingClickUv(NumberUtils.defaultInt(consumerVO.getBillingClickUv()) + NumberUtils.defaultInt(tmp.getBillingClickUv()));
            consumerVO.setConsumeAmount(NumberUtils.defaultInt(consumerVO.getConsumeAmount()) + NumberUtils.defaultInt(tmp.getConsumeAmount()));
        }
        return ResultBuilder.success(consumerVO);
    }

    /**
     * 查询条件转换
     */
    private AdvertiserFianceStatisticsRecordListReq convertTo(AdvertiserConsumeReq req) {
        AdvertiserFianceStatisticsRecordListReq param = new AdvertiserFianceStatisticsRecordListReq();
        param.setStartDate(req.getStartDate());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        param.setAccountIds(req.getAdvertiserIds());

        // 代理商查询
        if (CollectionUtils.isNotEmpty(req.getAgentIds())) {
            List<Long> accountIds = new ArrayList<>(req.getAgentIds());
            accountIds.addAll(agentService.selectAdvertiserIdsByAgentIds(req.getAgentIds()));
            param.setAccountIds(mergeParamIds(param.getAccountIds(), accountIds));
            if (CollectionUtils.isEmpty(param.getAccountIds())) {
                param.setAccountIds(Collections.singletonList(-1L));
                return param;
            }
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            param.setAccountIds(mergeParamIds(param.getAccountIds(), permission.getValues()));
            if (CollectionUtils.isEmpty(param.getAccountIds())) {
                param.setAccountIds(Collections.singletonList(-1L));
            }
        }
        return param;
    }
}
