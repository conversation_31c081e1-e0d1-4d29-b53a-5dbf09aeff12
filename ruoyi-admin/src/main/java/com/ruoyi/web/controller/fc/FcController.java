package com.ruoyi.web.controller.fc;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.BizConstants;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.*;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.bo.fc.FcArticleStatusCountBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkListParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleCacheBo;
import com.ruoyi.system.bo.landpage.article.ArticleCountBo;
import com.ruoyi.system.entity.datashow.FcLinkDayData;
import com.ruoyi.system.entity.fc.FcLinkDayDataEntity;
import com.ruoyi.system.entity.fc.FcLinkEntity;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.req.fc.*;
import com.ruoyi.system.req.landpage.article.ArticleAggrLinkListReq;
import com.ruoyi.system.service.datasource.FcLinkDayDataService;
import com.ruoyi.system.service.fc.FcLinkArticleAggrRelService;
import com.ruoyi.system.service.fc.FcLinkService;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkHourDataService;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkService;
import com.ruoyi.system.service.landpage.article.ArticleService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.fc.FcArticleVo;
import com.ruoyi.system.vo.fc.FcLinkVo;
import com.ruoyi.system.vo.landpage.article.ArticleAggrLinkVO;
import com.ruoyi.web.controller.engine.ArticleApiController;
import com.ruoyi.web.controller.manager.crm.dsp.landpage.ArticleAggrLinkController;
import com.ruoyi.system.vo.fc.FcArticleAggrLinkVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.ruoyi.common.core.controller.BaseController;


/**
 * @ClassName FcLinkController
 * @Description 丰巢相关接口
 * <AUTHOR>
 * @Date 2025/6/5 15:47
 */
@Slf4j
@RestController
@RequestMapping("/fc")
public class FcController extends BaseController{

    @Autowired
    private FcLinkService fcLinkService;
    @Autowired
    private FcLinkArticleAggrRelService fcLinkArticleAggrRelService;
    @Autowired
    private ArticleApiController articleApiController;
    @Autowired
    private ArticleAggrLinkService articleAggrLinkService;
    @Autowired
    private ArticleService articleService;
    @Autowired
    private AccountService accountService;
    @Autowired
    private ArticleAggrLinkHourDataService articleAggrLinkHourDataService;
    @Autowired
    private ArticleAggrLinkController articleAggrLinkController;
    @Autowired
    private FcLinkDayDataService fcLInkDayDataService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RedisAtomicClient redisAtomicClient;
    /**
     * 新增丰巢链接
     * @param name
     * @return
     */
    @PostMapping("/addFcLink")
    public Result addFcLink(String name) {
        if ( StringUtils.isEmpty(name) ) {
            return ResultBuilder.fail("链接名称不能为空");
        }

        return fcLinkService.addFcLink(name);
    }


    /**
     * 移除丰巢链接
     * @param id
     * @return
     */
    @PostMapping("/removeFcLink")
    public Result removeFcLink(Long id) {
        if ( Objects.isNull(id) ) {
            return ResultBuilder.fail("id不能为空");
        }
        return fcLinkService.removeFcLink(id);
    }

    /**
     * 添加文章聚合链接
     * @param  addArticleAggrLinkReq
     * @return
     */
    @PostMapping("/addArticleAggrLink")
    public Result addArticleAggrLink(@RequestBody AddArticleAggrLinkReq addArticleAggrLinkReq) {

        return fcLinkArticleAggrRelService.addArticleAggrLink(addArticleAggrLinkReq);
    }

    /**
     * 移除文章聚合链接
     * @param fcLinkKey
     * @param articleAggrLinkId
     * @return
     */
    @PostMapping("/removeArticleAggrLink")
    public Result removeArticleAggrLink(String fcLinkKey, Long articleAggrLinkId) {
        return fcLinkArticleAggrRelService.removeArticleAggrLink(fcLinkKey, articleAggrLinkId);
    }

    /**
     * 丰巢链接获取文章
     * @param key
     * @param obtainArticleReq
     * @param request
     * @return
     */
    @CrossOrigin
    @ResponseBody
    @PostMapping("/wz/{key}")
    public Map<String, Object> getArticle(@PathVariable("key") String key,  @RequestBody ObtainArticleReq obtainArticleReq, HttpServletRequest request ) {
        HashMap<String, Object> map = new HashMap<>();
        log.info("丰巢文章获取请求，body={}", JSONObject.toJSONString(obtainArticleReq));
        // 获取第一个Imp对象，避免重复判断和空指针风险
        Imp firstImp = getFirstImp(obtainArticleReq);
        if ( Objects.isNull(firstImp) || !Objects.equals(firstImp.getDealId(), "250618") ) {
            map.put("message", "暂无权限");
            return map;
        }
        String userId = getUserId(obtainArticleReq, request);
        ArticleCacheBo article = getArticle(key, userId);
        if ( Objects.isNull(article) ) {
            return null;
        }


        map.put("dealId", firstImp != null ? firstImp.getDealId() : null);
        map.put("url", article.getUrl());
        map.put("materialCode", BizConstants.FC_MATERIAL_ID_PREFIX + article.getId());
        map.put("settleType", firstImp != null && firstImp.getSettleType() != null ? firstImp.getSettleType() : 1);
        map.put("price", firstImp != null  ? firstImp.getPrice() : null);
        return map;

    }

    /**
     * 获取文章聚合链接列表
     * @param req
     * @return
     */
    @GetMapping("/getArticleAggrLinkList")
    public TableDataInfo<ArticleAggrLinkVO> getArticleAggrLinkList(ArticleAggrLinkListReq req) {
        if ( StringUtils.isEmpty(req.getFcLinkKey()) ) {
            throw new CustomException("参数异常");
        }
        Set<Long> idList = fcLinkArticleAggrRelService.selectAggrIdsByFcLinkKey(req.getFcLinkKey());
        ArticleAggrLinkListParamBo param = new ArticleAggrLinkListParamBo();
        param.setNotInLinkIds(new ArrayList<>(idList));
        param.setAdvertiserId(CollectionUtils.isEmpty(req.getAdvertIds()) ? null : req.getAdvertIds().get(0));
        param.setFcSearchKey(req.getSearchKey());

        startPage();
        List<ArticleAggrLinkEntity> list = articleAggrLinkService.selectList(param);

        List<Long> linkIds = ListUtils.mapToList(list, ArticleAggrLinkEntity::getId);
        Map<Long, ArticleCountBo> articleCountMap = articleService.countByLinkId(linkIds);
        List<Long> advertiserIds = list.stream().map(ArticleAggrLinkEntity::getAdvertiserId).collect(Collectors.toList());
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(advertiserIds);

        return getDataTable(PageInfoUtils.dto2Vo(list, entity -> {
            ArticleAggrLinkVO vo = BeanUtil.copyProperties(entity, ArticleAggrLinkVO.class, "retConfig");
            vo.setAdvertiserName(companyNameMap.getOrDefault(entity.getAdvertiserId(), ""));


            ArticleCountBo articleCount = articleCountMap.get(entity.getId());
            if (null != articleCount) {
                vo.setArticleTargetRequestPv(NumberUtils.defaultInt(articleCount.getArticleTargetRequestPv()));
                vo.setArticleCount(String.valueOf(articleCount.getArticleCount()));
                if (articleCount.getArticleCount() > 0) {
                    vo.setArticleCount(vo.getArticleCount() + "(" + articleCount.getOnlineArticleCount() + ")");
                }
            } else {
                vo.setArticleCount("0");
                vo.setArticleTargetRequestPv(0);
            }
            return vo;
        }));
    }

    /**
     * 获取丰巢链接已添加的聚合链接
     * @return
     */
    @GetMapping("/getFcArticleAggrLinkList")
    public TableDataInfo<FcArticleAggrLinkVO> getFcArticleAggrLinkList(ArticleAggrLinkListReq req) {
        if ( StringUtils.isEmpty(req.getFcLinkKey()) ) {
            throw new CustomException("参数异常");
        }
        Set<Long> set = fcLinkArticleAggrRelService.selectAggrIdsByFcLinkKey(req.getFcLinkKey());
        if ( CollectionUtils.isEmpty(set) ) {
            return getDataTable(PageInfoUtils.buildReturnList(new ArrayList<>()));
        }
        ArticleAggrLinkListParamBo param = new ArticleAggrLinkListParamBo();
        param.setLinkIds(new ArrayList<>(set));
        param.setAdvertiserId(CollectionUtils.isEmpty(req.getAdvertIds()) ? null : req.getAdvertIds().get(0));
        param.setFcSearchKey(req.getSearchKey());
        startPage();
        List<ArticleAggrLinkEntity> list = articleAggrLinkService.selectList(param);

        List<Long> linkIds = ListUtils.mapToList(list, ArticleAggrLinkEntity::getId);
        Map<Long, ArticleCountBo> articleCountMap = articleService.countByLinkId(linkIds);
        Map<Long, FcArticleStatusCountBo> fcArticleStatusCountMap = articleService.countFcStatusByLinkIds(linkIds);
        Map<Long, ArticleAggrLinkDataBo> dataMap = articleAggrLinkHourDataService.selectTodayDataByLinkIds(linkIds);
        Map<String, List<String>> relateAdvertMap = articleAggrLinkController.getRelateAdvertMap();
        List<Long> advertiserIds = list.stream().map(ArticleAggrLinkEntity::getAdvertiserId).collect(Collectors.toList());
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(advertiserIds);

        return getDataTable(PageInfoUtils.dto2Vo(list, entity -> {
            FcArticleAggrLinkVO vo = BeanUtil.copyProperties(entity, FcArticleAggrLinkVO.class, "retConfig");
            vo.setAdvertiserName(companyNameMap.getOrDefault(entity.getAdvertiserId(), ""));
            ArticleAggrLinkDataBo data = dataMap.get(entity.getId());
            if (null != data) {
                vo.setRequestPv(data.getRequestPv());
                vo.setRequestUv(data.getRequestUv());
            } else {
                vo.setRequestPv(0);
                vo.setRequestUv(0);
            }
            vo.setRelateAdvertIds(relateAdvertMap.get(entity.getKey()));
            ArticleCountBo articleCount = articleCountMap.get(entity.getId());
            if (null != articleCount) {
                vo.setArticleTargetRequestPv(NumberUtils.defaultInt(articleCount.getArticleTargetRequestPv()));
                vo.setArticleCount(String.valueOf(articleCount.getArticleCount()));
                if (articleCount.getArticleCount() > 0) {
                    vo.setArticleCount(vo.getArticleCount() + "(" + articleCount.getOnlineArticleCount() + ")");
                }
            } else {
                vo.setArticleCount("0");
                vo.setArticleTargetRequestPv(0);
            }
            FcArticleStatusCountBo fcArticleStatusCount = fcArticleStatusCountMap.get(entity.getId());
            if (null != fcArticleStatusCount) {
                vo.setApprovedCount(fcArticleStatusCount.getApprovedCount());
                vo.setRejectedCount(fcArticleStatusCount.getRejectedCount());
                vo.setSyncedCount(fcArticleStatusCount.getSyncedCount());
            } else {
                vo.setApprovedCount(0);
                vo.setRejectedCount(0);
                vo.setSyncedCount(0);
            }
            return vo;
        }));
    }

    /**
     * 获取文章列表
     * @param req
     * @return
     */
    @GetMapping("/getArticleList")
    public TableDataInfo<FcArticleVo> getArticleList(GetArticleListReq req) {
        startPage();
        List<FcArticleVo> list = fcLinkService.getArticleByStatusAndTime(req);
        return getDataTable(PageInfoUtils.buildReturnList(list));

    }

    /**
     * 丰巢文章审核
     * @param fcCheckArticleReq
     * @return
     */
    @PostMapping("/fcCheckArticle")
    public Result fcCheckArticle(@RequestBody FcCheckArticleReq fcCheckArticleReq) {
        return fcLinkService.fcCheckArticle(fcCheckArticleReq);
    }

    /**
     * 获取丰巢链接列表
     * @par
     * @return
     */
    @GetMapping("/getFcLinkList")
    public TableDataInfo<FcLinkVo> getFcLinkList(String searchKey) {
        startPage();
        List<FcLinkVo> fcLinklist = fcLinkService.getFcLinkListOrderByIdDesc(searchKey);
        if ( CollectionUtils.isEmpty(fcLinklist) ) {
            return getDataTable(PageInfoUtils.buildReturnList(Collections.emptyList()));
        }
        List<String> fcLinkKeyList = fcLinklist.stream().map(FcLinkVo::getFcLinkKey).collect(Collectors.toList());
        List<Long> articleAggrLinkIds = fcLinkArticleAggrRelService.selectAggrIdsByFcLinkKeys(fcLinkKeyList);
        if ( CollectionUtils.isEmpty(articleAggrLinkIds) ) {
            return getDataTable(PageInfoUtils.buildReturnList(Collections.emptyList()));
        }
        List<Long> fcLinkIdList = fcLinklist.stream().map(FcLinkVo::getFcLinkId).collect(Collectors.toList());
        List<FcLinkVo> list = fcLinkService.getFcLinkInfoByArticleAggrLinkIdList(articleAggrLinkIds, fcLinkIdList);

        Set<Long> accountIds = list.stream()
                .flatMap(vo -> Stream.of(vo.getCreatorId(), vo.getOperatorId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<Long, String> accountContactMap = accountService.selectAccountContactMap(new ArrayList<>(accountIds));

        Date date = new Date();
        for (FcLinkVo fcLinkVo : list) {
            FcLinkDayDataEntity fcLinkDayDataEntity = fcLInkDayDataService.getFcLinkDayData(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, date), fcLinkVo.getFcLinkKey());
            if ( Objects.nonNull(fcLinkDayDataEntity) ) {
                fcLinkVo.setPv(fcLinkDayDataEntity.getPv());
                fcLinkVo.setUv(fcLinkDayDataEntity.getUv());
            }
            if (Objects.nonNull(fcLinkVo.getCreatorId())) {
                fcLinkVo.setCreateBy(accountContactMap.get(fcLinkVo.getCreatorId()));
            }
            if (Objects.nonNull(fcLinkVo.getOperatorId())) {
                fcLinkVo.setUpdateBy(accountContactMap.get(fcLinkVo.getOperatorId()));
            }
        }
        return getDataTable(PageInfoUtils.buildReturnList(list));
    }

    /**
     * 获取用户id
     * @param obtainArticleReq
     * @param request
     * @return
     */
    private String getUserId(ObtainArticleReq obtainArticleReq, HttpServletRequest request) {
        if ( Objects.nonNull(obtainArticleReq) && Objects.nonNull(obtainArticleReq.getUser()) && !StringUtils.isEmpty(obtainArticleReq.getUser().getUserId()) ) {
            return obtainArticleReq.getUser().getUserId();
        }else {
            String ip = IpUtils.getIpAddr(request);
            String userAgent = request.getHeader("User-Agent");
            return Md5Utils.hash(ip + userAgent);
        }
    }

    /**
     * 安全获取第一个Imp对象
     * <AUTHOR>
     * @param obtainArticleReq 请求对象
     * @return 第一个Imp对象，如果不存在则返回null
     */
    private Imp getFirstImp(ObtainArticleReq obtainArticleReq) {
        if (Objects.isNull(obtainArticleReq) || CollectionUtils.isEmpty(obtainArticleReq.getImp())) {
            return null;
        }
        Imp firstImp = obtainArticleReq.getImp().get(0);
        return firstImp; // 即使第一个元素为null也直接返回，由调用方处理
    }


    /**
     * 获取文章
     * @param fcLinkKey
     * @return
     */
    private ArticleCacheBo getArticle(String fcLinkKey, String userId) {

        ArticleCacheBo article = fcLinkService.getArticleByfcLinkKey(userId, fcLinkKey);


        if ( Objects.isNull(article) ) {
            article = getFcLinkDefaultArticle(fcLinkKey);
            fcLinkWarning(fcLinkKey);
        }

        if ( Objects.isNull(article) ) {
            return null;
        }
        // 统计丰巢链接数据
        fcLinkService.fcLinkStatistic(fcLinkKey, userId);
        // 统计聚合链接数据
        articleApiController.linkStatistic(article.getLinkId(), userId);
        // 统计文章数据
        ArticleCacheBo finalArticle = article;
        GlobalThreadPool.statExecutorService.execute(() -> {
            try {
                articleApiController.articleStatistic(finalArticle.getLinkId(), finalArticle, userId, fcLinkKey);
            } catch (Exception e) {
                log.error("文章统计异常, linkId={}, articleId={}, fcLinkKey={}", finalArticle.getLinkId(), finalArticle.getId(), fcLinkKey, e);
            }
        });

        return article;
    }

    /**
     * 获取丰巢默认文章（目标阅读量最大的文章）
     * @param fcLinkKey 丰巢链接key
     * @return 目标阅读量最大的文章缓存对象
     * <AUTHOR>
     */
    private ArticleCacheBo getFcLinkDefaultArticle(String fcLinkKey) {
        if (StringUtils.isEmpty(fcLinkKey)) {
            return null;
        }

        String today = DateUtil.today();
        // 先从缓存中获取默认文章
        String cacheKey = EngineRedisKeyFactory.k153.join(today, fcLinkKey);
        ArticleCacheBo cachedArticle = redisCache.getCacheObject(cacheKey);
        if (cachedArticle != null) {
            return cachedArticle;
        }

        // 获取丰巢链接关联的文章聚合链接ID列表
        Set<Long> articleAggrLinkIdSet = fcLinkArticleAggrRelService.selectAggrIdsByFcLinkKey(fcLinkKey);
        if (CollectionUtils.isEmpty(articleAggrLinkIdSet)) {
            return null;
        }

        List<Long> articleAggrLinkIdList = new ArrayList<>(articleAggrLinkIdSet);

        // 查询文章列表
        List<ArticleEntity> articles = articleService.selectListByLinkIds(articleAggrLinkIdList);
        if (CollectionUtils.isEmpty(articles)) {
            return null;
        }

        // 筛选符合条件的文章并找到目标阅读量最大的文章
        Date todayBegin = DateUtil.beginOfDay(new Date());
        ArticleEntity defaultArticle = articles.stream()
                .filter(article -> !article.getGmtCreate().before(todayBegin) && Objects.equals(article.getFcSyncStatus(), 1)) // 必须是今天创建的
                .max(Comparator.comparingInt(article -> NumberUtils.defaultInt(article.getTargetRequestPv())))
                .orElse(null);

        ArticleCacheBo result = defaultArticle != null ? defaultArticle.getCacheBo() : null;

        // 将结果缓存30分钟
        if (result != null) {
            redisCache.setCacheObject(cacheKey, result, 5, TimeUnit.MINUTES);
        }

        return result;
    }

    /**
     * 丰巢链接告警
     * <AUTHOR>
     */
    private void fcLinkWarning(String fcLinkKey) {
        GlobalThreadPool.longTimeExecutorService.submit(() -> {
            RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K012.join("fcLinkWarning", fcLinkKey), 300);
            if (null == lock) {
                return;
            }
            // 根据fcLinkKey获取丰巢链接
            FcLinkEntity link = fcLinkService.queryByKey(fcLinkKey);
            if (null == link) {
                return;
            }
            DingRobotUtil.sendText(DingWebhookConfig.getArticleOperateAlert(),
                    "丰巢链接无可投文章\n" +
                            "\nAPI名称: " + link.getName() +
                            "\nAPI链接: " + link.getUrl());
        });
    }
}
