package com.ruoyi.web.controller.manager.crm.dsp.finance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum;
import com.ruoyi.common.enums.advertiser.RechargeAuditStatus;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.account.OaStaffBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserRechargeExcelBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserRechargeRecordEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.account.OaStaffManger;
import com.ruoyi.system.manager.advertiser.AdvertiserRechargeManager;
import com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeApplyReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeAuditReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeListReq;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserRechargeRecordService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.advertiser.finance.AdvertiserRechargeStatisticVO;
import com.ruoyi.system.vo.advertiser.finance.AdvertiserRechargeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.OaConstants.FINANCE_AUDITOR_LIST;
import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;
import static com.ruoyi.common.enums.advertiser.RechargeAuditStatus.isAuditApprove;
import static com.ruoyi.common.enums.advertiser.RechargeAuditStatus.isAuditRefuse;
import static com.ruoyi.common.enums.advertiser.RechargeAuditStatus.isReadyToAudit;

/**
 * 广告主充值Controller
 *
 * <AUTHOR>
 * @date 2022/3/21 1:55 下午
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/fiance/recharge")
public class AdvertiserRechargeController extends BaseController {

    @Autowired
    private AdvertiserRechargeManager advertiserRechargeManager;

    @Autowired
    private AdvertiserRechargeRecordService advertiserRechargeRecordService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private AgentService agentService;

    @Autowired
    private OaStaffManger oaStaffManger;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    /**
     * 充值记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<AdvertiserRechargeVO> list(AdvertiserRechargeListReq req) {
        // 参数处理
        handleQueryParam(req);
        if (CollUtil.contains(req.getAccountIds(), -1L)) {
            return getDataTable(Collections.emptyList());
        }
        // 查询数据
        TableSupport.startPage();
        List<AdvertiserRechargeRecordEntity> list = advertiserRechargeRecordService.selectListByAccountIdAndDate(req);
        // 补充数据
        List<Long> accountIds = ListUtils.mapToList(list, AdvertiserRechargeRecordEntity::getAccountId);
        List<Long> operatorIds = CollUtil.unionAll(ListUtils.mapToList(list, AdvertiserRechargeRecordEntity::getOperatorId), ListUtils.mapToList(list, AdvertiserRechargeRecordEntity::getAuditorId));
        Map<Long, String> operatorNameMap = accountService.selectAccountContactMap(operatorIds);
        Map<Long, Account> agentMap = agentService.selectAgentMap(accountIds);
        Map<Long, Account> accountMap = accountService.selectMapByIds(accountIds);
        LoginUser user = SecurityUtils.getLoginUser();
        OaStaffBo staff = oaStaffManger.selectByEmail(user.getEmail());
        String postKey = null != staff.getPost() ? staff.getPost().getPostKey() : "";
        // 构造结果
        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            AdvertiserRechargeVO rechargeVO = BeanUtil.copyProperties(record, AdvertiserRechargeVO.class);
            Optional.ofNullable(accountMap.get(record.getAccountId())).ifPresent(account -> {
                rechargeVO.setMainType(account.getMainType());
                rechargeVO.setCompanyName(account.getCompanyName());
            });
            Optional.ofNullable(agentMap.get(record.getAccountId())).ifPresent(agent -> {
                rechargeVO.setAgentId(agent.getId());
                rechargeVO.setAgentName(agent.getCompanyName());
            });
            rechargeVO.setOperatorName(operatorNameMap.get(record.getOperatorId()));
            rechargeVO.setAuditorName(operatorNameMap.get(record.getAuditorId()));
            if (isReadyToAudit(record.getAuditStatus()) && (user.isAdmin() || FINANCE_AUDITOR_LIST.contains(postKey))) {
                rechargeVO.setCanAudit(1);
            } else {
                rechargeVO.setCanAudit(0);
            }
            return rechargeVO;
        }));
    }

    /**
     * 广告主充值申请
     */
    @PostMapping("apply")
    @Log(title = "广告主充值", businessType = BusinessType.INSERT)
    public Result<Boolean> apply(@RequestBody @Validated AdvertiserRechargeApplyReq req) {
        return ResultBuilder.result(advertiserRechargeManager.apply(req));
    }

    /**
     * 充值审核
     */
    @PostMapping("audit")
    @Log(title = "广告主充值", businessType = BusinessType.UPDATE)
    public Result<Boolean> audit(@RequestBody @Validated AdvertiserRechargeAuditReq req) {
        LoginUser user = SecurityUtils.getLoginUser();
        OaStaffBo staff = oaStaffManger.selectByEmail(user.getEmail());
        String postKey = null != staff.getPost() ? staff.getPost().getPostKey() : "";
        if (!user.isAdmin() && !FINANCE_AUDITOR_LIST.contains(postKey)) {
            return ResultBuilder.fail("无审核权限");
        }

        if (isAuditApprove(req.getAuditStatus())) {
            advertiserRechargeManager.auditApprove(req.getId(), req.getAuditReason());
            sendCheckBudgetMsg(req.getId());
        } else if (isAuditRefuse(req.getAuditStatus())) {
            advertiserRechargeManager.auditRefuse(req.getId(), req.getAuditReason());
        }
        return ResultBuilder.success(true);
    }

    /**
     * 导出充值记录
     */
    @Log(title = "广告主充值记录", businessType = BusinessType.EXPORT)
    @GetMapping("export")
    public AjaxResult export(AdvertiserRechargeListReq req) {
        String fileName = UUID.randomUUID().toString() + "_广告主充值记录.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;

        // 参数处理
        handleQueryParam(req);
        if (CollUtil.contains(req.getAccountIds(), -1L)) {
            EasyExcel.write(filePath, AdvertiserRechargeExcelBo.class).sheet().doWrite(Collections.emptyList());
            return AjaxResult.success(fileName);
        }
        // 查询数据
        TableSupport.startPage();
        List<AdvertiserRechargeRecordEntity> list = advertiserRechargeRecordService.selectListByAccountIdAndDate(req);
        // 补充数据
        List<Long> accountIds = ListUtils.mapToList(list, AdvertiserRechargeRecordEntity::getAccountId);
        List<Long> operatorIds = CollUtil.unionAll(ListUtils.mapToList(list, AdvertiserRechargeRecordEntity::getOperatorId), ListUtils.mapToList(list, AdvertiserRechargeRecordEntity::getAuditorId));
        Map<Long, String> operatorNameMap = accountService.selectAccountContactMap(operatorIds);
        Map<Long, Account> agentMap = agentService.selectAgentMap(accountIds);
        Map<Long, Account> accountMap = accountService.selectMapByIds(accountIds);
        // 构造结果
        List<AdvertiserRechargeExcelBo> excelBos = list.stream().map(record -> {
            AdvertiserRechargeExcelBo excelBo = BeanUtil.copyProperties(record, AdvertiserRechargeExcelBo.class);
            Optional.ofNullable(agentMap.get(record.getAccountId())).ifPresent(agent -> {
                excelBo.setAgentId(agent.getId());
                excelBo.setAgentName(agent.getCompanyName());
            });
            Optional.ofNullable(accountMap.get(record.getAccountId())).ifPresent(account -> {
                if (isAgent(account.getMainType())) {
                    excelBo.setMainTypeStr("代理商");
                    excelBo.setAgentId(account.getId());
                    excelBo.setAgentName(account.getCompanyName());
                } else if (isAdvertiser(account.getMainType())) {
                    excelBo.setAdvertiserId(account.getId());
                    excelBo.setAdvertiserName(account.getCompanyName());
                    excelBo.setMainTypeStr(null != excelBo.getAgentId() ? "子广告主" : "直客");
                }
            });
            excelBo.setRechargeTypeStr(AdvertiserRechargeTypeEnum.getDescByType(record.getRechargeType()));
            excelBo.setAuditStatusStr(RechargeAuditStatus.getDescByStatus(record.getAuditStatus()));
            excelBo.setOperatorName(operatorNameMap.get(record.getOperatorId()));
            excelBo.setAuditorName(operatorNameMap.get(record.getAuditorId()));
            return excelBo;
        }).collect(Collectors.toList());
        EasyExcel.write(filePath, AdvertiserRechargeExcelBo.class).sheet().doWrite(excelBos);
        return AjaxResult.success(fileName);
    }

    /**
     * 查询充值汇总
     */
    @GetMapping("/statisticRecharge")
    public Result<AdvertiserRechargeStatisticVO> statisticRecharge(AdvertiserRechargeListReq req) {
        // 参数处理
        handleQueryParam(req);
        if (CollUtil.contains(req.getAccountIds(), -1L)) {
            return ResultBuilder.success();
        }
        req.setExcludeAccountIds(advertiserTagService.getAdvertiserByTag("测试账号"));
        // 查询数据
        Long rechargeAmount = advertiserRechargeRecordService.selectStatisticRecharge(req);
        // 构造结果
        AdvertiserRechargeStatisticVO rechargeVO = new AdvertiserRechargeStatisticVO();
        rechargeVO.setRechargeAmount(rechargeAmount);
        return ResultBuilder.success(rechargeVO);
    }

    /**
     * 重新检查广告预算
     *
     * @param rechargeId 充值记录ID
     */
    private void sendCheckBudgetMsg(Long rechargeId) {
        AdvertiserRechargeRecordEntity record = advertiserRechargeRecordService.selectById(rechargeId);
        if (null != record) {
            advertService.sendCheckBudgetMsgByAdvertiserId(record.getAccountId());
        }
    }

    /**
     * 处理充值记录列表请求参数
     *
     * @param req 请求参数
     */
    private void handleQueryParam(AdvertiserRechargeListReq req) {
        List<Long> accountIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(req.getAdvertiserIds())) {
            accountIds.addAll(req.getAdvertiserIds());
        }
        // 代理商查询
        if (CollectionUtils.isNotEmpty(req.getAgentIds())) {
            List<Long> tmpAccountIds = new ArrayList<>(req.getAgentIds());
            tmpAccountIds.addAll(agentService.selectAdvertiserIdsByAgentIds(req.getAgentIds()));
            accountIds = mergeParamIds(accountIds, tmpAccountIds);
            if (CollectionUtils.isEmpty(accountIds)) {
                req.setAccountIds(Collections.singletonList(-1L));
                return;
            }
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            accountIds = mergeParamIds(accountIds, permission.getValues());
            if (CollectionUtils.isEmpty(accountIds)) {
                req.setAccountIds(Collections.singletonList(-1L));
                return;
            }
        }
        req.setAccountIds(accountIds);
    }
}
