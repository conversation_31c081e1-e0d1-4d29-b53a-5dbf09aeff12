package com.ruoyi.web.controller.manager.crm.dsp.setting;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.datashow.LandpageFormSendRuleEntity;
import com.ruoyi.system.req.advertiser.AdvertiserFormPriceReq;
import com.ruoyi.system.req.advertiser.AdvertiserLpCallbackUrlReq;
import com.ruoyi.system.req.advertiser.AdvertiserLpCallbackRuleReq;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.landpage.LandpageFormSendRuleService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.account.FormAdvertiserVO;
import com.ruoyi.system.vo.advertiser.AdvertiserLpCallbackRuleVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import static com.ruoyi.common.enums.account.AccountStatusEnum.NORMAL;

/**
 * 表单广告主Controller
 *
 * <AUTHOR>
 * @date 2022/03/11
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/formAdvertiser")
public class FormAdvertiserController extends BaseController {

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    @Autowired
    private LandpageFormSendRuleService landpageFormSendRuleService;

    @Autowired
    private AreaService areaService;

    /**
     * 表单广告主列表
     */
    @GetMapping("/list")
    public TableDataInfo list() {
        // 广告主标签
        Map<Long, List<String>> tagMap = advertiserTagService.getMap();

        Account param = new Account();
        param.setIds(getFormAdvertiserIds(tagMap));
        param.setStatus(NORMAL.getStatus());

        startPage();
        List<Account> advertiserList = advertiserService.selectList(param);

        return getDataTable(PageInfoUtils.dto2Vo(advertiserList, advertiser -> {
            FormAdvertiserVO advertiserVO = new FormAdvertiserVO();
            advertiserVO.setId(advertiser.getId());
            advertiserVO.setAdvertiserName(advertiser.getCompanyName());
            advertiserVO.setStatus(advertiser.getStatus());
            advertiserVO.setGmtCreate(advertiser.getGmtCreate());
            advertiserVO.setTags(tagMap.get(advertiser.getId()));
            advertiserVO.setPriceType(0);
            AccountExtInfo extInfo = JSON.parseObject(advertiser.getExtInfo(), AccountExtInfo.class);
            if (null != extInfo) {
                advertiserVO.setLpCallbackUrl(extInfo.getLpCallbackUrl());
                advertiserVO.setFormPrice(extInfo.getFormPrice());
                advertiserVO.setPriceType(NumberUtils.defaultInt(extInfo.getPriceType()));
                advertiserVO.setWeight(NumberUtils.defaultDouble(extInfo.getFormWeight(), 1d));
                advertiserVO.setConsumeType(extInfo.getConsumeType());
            }
            return advertiserVO;
        }));
    }

    /**
     * 设置表单价格
     */
    @PostMapping("/updateFormPrice")
    public AjaxResult updateFormPrice(@RequestBody @Validated AdvertiserFormPriceReq req) {
        boolean result = advertiserService.updateFormPrice(req.getAdvertiserId(), req.getFormPrice(), req.getPriceType(), req.getWeight());
        return result ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 设置API回传接口
     */
    @PostMapping("/updateLpCallbackUrl")
    public AjaxResult updateLpCallbackUrl(@RequestBody @Validated AdvertiserLpCallbackUrlReq req) {
        if (StringUtils.isBlank(req.getLpCallbackUrl()) || !req.getLpCallbackUrl().startsWith("http")) {
            return AjaxResult.error("请输入http/https的有效回传链接");
        }

        boolean result = advertiserService.updateLpCallbackUrl(req.getAdvertiserId(), req.getLpCallbackUrl().trim());
        return result ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 查询落地页回传规则配置
     */
    @GetMapping(value = "/getLpCallbackRule")
    public AjaxResult getLpCallbackRule(Long advertiserId) {
        if (null == advertiserId) {
            return AjaxResult.error(ErrorCode.ARGS);
        }

        LandpageFormSendRuleEntity rule = landpageFormSendRuleService.selectByAdvertiserId(advertiserId);
        if (null == rule) {
            AdvertiserLpCallbackRuleVO ruleVO = new AdvertiserLpCallbackRuleVO();
            ruleVO.setAdvertiserId(advertiserId);
            ruleVO.setAreaList(Collections.emptyList());
            return AjaxResult.success(ruleVO);
        }

        AdvertiserLpCallbackRuleVO ruleVO = BeanUtil.copyProperties(rule, AdvertiserLpCallbackRuleVO.class);

        // 转化地域为编码
        List<String> areaList = JSON.parseArray(rule.getArea(), String.class);
        if (CollectionUtils.isNotEmpty(areaList) && areaList.contains("中国")) {
            areaList = areaService.queryTotalProvince();
        }
        ruleVO.setAreaList(areaService.queryAreaNumByName(areaList));
        return AjaxResult.success(ruleVO);
    }

    /**
     * 落地页回传规则配置
     */
    @CrossOrigin
    @PostMapping("/updateLpCallbackRule")
    public AjaxResult updateLpCallbackRule(@RequestBody @Validated AdvertiserLpCallbackRuleReq req) {
        Long advertiserId = req.getAdvertiserId();

        // 参数校验
        if (null == req.getAreaList()) {
            req.setAreaList(Collections.emptyList());
        }
        if (null != req.getDailyLimit() && req.getDailyLimit() < 0) {
            return AjaxResult.error("每日表单量上限不能为负数");
        }
        if (null != req.getAgeMin() && req.getAgeMin() < 0
                || null != req.getAgeMax() && req.getAgeMax() < 0) {
            return AjaxResult.error("年龄不能为负数");
        }

        // 检查广告主信息
        Account account = accountService.selectAccountById(advertiserId);
        if (null == account || !AccountMainType.isAdvertiser(account.getMainType())) {
            return AjaxResult.error("账号" + advertiserId + "非广告主账号");
        }
        AccountExtInfo extInfo = JSON.parseObject(account.getExtInfo(), AccountExtInfo.class);
        if (null == extInfo || StringUtils.isBlank(extInfo.getLpCallbackUrl())) {
            return AjaxResult.error(account.getCompanyName() + "(" + advertiserId + ")未配置落地页回传链接");
        }

        // 全选所有省的处理
        if (req.getAreaList().contains("中国")) {
            req.setAreaList(areaService.queryTotalProvince());
        }

        // 查询旧配置
        LandpageFormSendRuleEntity oldRule = landpageFormSendRuleService.selectByAdvertiserId(advertiserId);

        // 更新配置
        LandpageFormSendRuleEntity rule = BeanUtil.copyProperties(req, LandpageFormSendRuleEntity.class);
        rule.setArea(JSON.toJSONString(req.getAreaList()));
        int result = landpageFormSendRuleService.update(rule);

        // 检查新旧配置差异并通知
        Optional.ofNullable(landpageFormSendRuleService.selectByAdvertiserId(advertiserId)).ifPresent(newRule -> diffRuleAndNotice(account, oldRule, newRule));
        return toAjax(result);
    }

    /**
     * 查询表单广告主列表
     */
    private List<Long> getFormAdvertiserIds(Map<Long, List<String>> tagMap) {
        List<Long> advertiserIds = new ArrayList<>();
        tagMap.forEach((advertiserId, tags) -> {
            if (tags.contains("移动") || tags.contains("联通") || tags.contains("电信") || tags.contains("白酒")) {
                advertiserIds.add(advertiserId);
            }
        });
        return advertiserIds;
    }

    /**
     * 检查新旧配置差异并通知
     *
     * @param account 广告主账号
     * @param oldRule 旧配置
     * @param newRule 新配置
     */
    private void diffRuleAndNotice(Account account, LandpageFormSendRuleEntity oldRule, LandpageFormSendRuleEntity newRule) {
        boolean isDiff = false;
        StringBuilder sb = new StringBuilder("落地页实时回传配置更新\n")
                .append("\n广告主ID: ").append(account.getId())
                .append("\n广告主名称: ").append(account.getCompanyName());
        if (null == oldRule) {
            sb.append("\n地域: ").append(Joiner.on(",").join(JSON.parseArray(newRule.getArea(), String.class)))
                    .append("\n年龄: ").append(NumberUtils.toStr(newRule.getAgeMin(), "*")).append("-").append(NumberUtils.toStr(newRule.getAgeMax(), "*"))
                    .append("\n每日表单量上限: ").append(NumberUtils.toStr(newRule.getDailyLimit(), "不限"));
            isDiff = true;
        } else {
            List<String> oldArea = CollUtil.defaultIfEmpty(JSON.parseArray(oldRule.getArea(), String.class), new ArrayList<>());
            List<String> newArea = CollUtil.defaultIfEmpty(JSON.parseArray(newRule.getArea(), String.class), new ArrayList<>());
            List<String> addArea = ListUtils.removeAll(newArea, oldArea);
            List<String> minusArea = ListUtils.removeAll(oldArea, newArea);
            if (CollectionUtils.isNotEmpty(addArea)) {
                sb.append("\n新增地域: ").append(Joiner.on(",").join(addArea));
                isDiff = true;
            }
            if (CollectionUtils.isNotEmpty(minusArea)) {
                sb.append("\n移除地域: ").append(Joiner.on(",").join(minusArea));
                isDiff = true;
            }
            if (!Objects.equals(oldRule.getAgeMin(), newRule.getAgeMin()) || !Objects.equals(oldRule.getAgeMax(), newRule.getAgeMax())) {
                sb.append("\n年龄: ").append(NumberUtils.toStr(oldRule.getAgeMin(), "*")).append("-").append(NumberUtils.toStr(oldRule.getAgeMax(), "*"))
                        .append(" → ")
                        .append(NumberUtils.toStr(newRule.getAgeMin(), "*")).append("-").append(NumberUtils.toStr(newRule.getAgeMax(), "*"));
                isDiff = true;
            }
            if (!Objects.equals(oldRule.getDailyLimit(), newRule.getDailyLimit())) {
                sb.append("\n每日表单量上限: ").append(NumberUtils.toStr(oldRule.getDailyLimit(), "不限"))
                        .append(" → ")
                        .append(NumberUtils.toStr(newRule.getDailyLimit(), "不限"));
                isDiff = true;
            }
        }
        if (isDiff) {
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb.toString());
        }
    }
}
