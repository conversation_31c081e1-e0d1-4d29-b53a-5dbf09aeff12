package com.ruoyi.web.controller.engine;

import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.system.req.wx.WxJsConfigReq;
import com.ruoyi.system.service.wx.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 微信相关接口(不鉴权)
 *
 * <AUTHOR>
 * @date 2023/03/22
 */
@Slf4j
@RestController
@RequestMapping("/wx")
public class WxController {

    @Autowired
    private WxPayService wxPayService;

    /**
     * 微信JSAPI配置接口
     */
    @CrossOrigin
    @PostMapping(value = "/jsConfig")
    public Result<Map<String, String>> jsConfig(@RequestBody WxJsConfigReq req) {
        // 获取并缓存openid
        wxPayService.getOpenid(req.getCode());
        return ResultBuilder.success(wxPayService.getJSConfig(req.getUrl()));
    }
}
