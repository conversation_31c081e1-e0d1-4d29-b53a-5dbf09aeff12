package com.ruoyi.web.controller.engine;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.system.entity.ai.AiLiuziEntity;
import com.ruoyi.system.req.ai.AiLiuziReq;
import com.ruoyi.system.service.ai.AiLiuziService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * ai官网留资
 * <AUTHOR>
 * @date 2024/1/2 14:26
 */
@RestController
@RequestMapping("/api/ai")
public class AiLiuziController extends BaseController {

    @Autowired
    private AiLiuziService aiLiuziService;
    /**
     * 留资表单提交
     * @return
     */
    @PostMapping("liuzi")
    public Result<Boolean> liuzi(@RequestBody @Valid AiLiuziReq req){
        return ResultBuilder.success(aiLiuziService.insert(BeanUtil.copyProperties(req, AiLiuziEntity.class)));
    }

}
