package com.ruoyi.web.controller.manager.ssp.withdraw;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.req.withdraw.WithdrawApplyReq;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.finance.WithdrawRecordService;
import com.ruoyi.system.service.qualification.AccountQualificationService;
import com.ruoyi.system.vo.withdraw.SspWithdrawListVO;
import com.ruoyi.system.vo.withdraw.WithdrawApplyInfoVO;
import com.ruoyi.system.vo.withdraw.WithdrawQualificationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * ssp提现管理
 *
 * <AUTHOR>
 * @date 2021/9/15 5:02 下午
 */
@RestController
@RequestMapping("/manager/ssp/withdraw")
public class WithdrawController extends BaseController {

    @Autowired
    private AppMonthDataService appMonthDataService;
    @Autowired
    private WithdrawRecordService withdrawRecordService;
    @Autowired
    private AccountQualificationService accountQualificationService;

    /**
     * 提现列表
     */
    @GetMapping("list")
    public TableDataInfo<SspWithdrawListVO> list() {
        LoginUser user = SecurityUtils.getLoginUser();
        return getDataTable(withdrawRecordService.selectWithdrawListForSsp(user.getCrmAccountId()));
    }

    /**
     * 申请提现数据详情
     *
     * @param withdrawId 提现记录id
     * @return 结果
     */
    @GetMapping("withdrawInfo")
    public Result<WithdrawApplyInfoVO> withdrawInfo(Long withdrawId){
        LoginUser user = SecurityUtils.getLoginUser();
        WithdrawApplyInfoVO vo = new WithdrawApplyInfoVO();
        vo.setAppDataListVOS(appMonthDataService.selectNoWithdrawList(user.getCrmAccountId(),withdrawId));
        vo.setWithdrawQualificationVO(BeanUtil.copyProperties(accountQualificationService.selectByAccountId(user.getCrmAccountId()), WithdrawQualificationVO.class));
        return ResultBuilder.success(vo);
    }

    /**
     * 申请提现
     *
     * @param req 请求参数
     * @return 结果
     */
    @Log(title = "提现", businessType = BusinessType.INSERT)
    @PostMapping("withdrawApply")
    public Result<Long> withdrawApply(@RequestBody WithdrawApplyReq req){
        return ResultBuilder.success(withdrawRecordService.withdrawApply(req));
    }
}
