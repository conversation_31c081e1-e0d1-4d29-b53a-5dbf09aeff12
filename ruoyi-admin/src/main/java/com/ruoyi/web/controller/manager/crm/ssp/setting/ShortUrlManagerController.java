package com.ruoyi.web.controller.manager.crm.ssp.setting;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base62;
import cn.hutool.core.util.RadixUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.bo.shorturl.ShortUrlDataExcelBO;
import com.ruoyi.system.entity.shorturl.ShortUrlDataEntity;
import com.ruoyi.system.entity.shorturl.ShortUrlEntity;
import com.ruoyi.system.param.shorturl.ShortUrlDataParam;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.req.shorturl.ShortUrlBatchGenerateReq;
import com.ruoyi.system.req.shorturl.ShortUrlDataListReq;
import com.ruoyi.system.req.shorturl.ShortUrlGenerateReq;
import com.ruoyi.system.req.shorturl.ShortUrlListReq;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.shorturl.ShortUrlDataService;
import com.ruoyi.system.service.shorturl.ShortUrlService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.util.ShortUrlUtils;
import com.ruoyi.system.vo.shorturl.ExternalShortUrlVo;
import com.ruoyi.system.vo.shorturl.ShortUrlDataVO;
import com.ruoyi.system.vo.shorturl.ShortUrlVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.ShortUrlStatus.DISABLE;
import static com.ruoyi.common.enums.ShortUrlStatus.isEnable;
import static com.ruoyi.common.enums.common.BizConfigEnum.DEFAULT_SHORT_URL;
import static com.ruoyi.common.enums.common.BizConfigEnum.DEFAULT_SHORT_URL_BATCH;

/**
 * 短链管理Controller
 *
 * <AUTHOR>
 * @date 2022-10-08
 */
@RestController
@RequestMapping("/crm/ssp/setting/shorturl")
public class ShortUrlManagerController extends BaseController {



    @Autowired
    private ShortUrlService shortUrlService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private ShortUrlDataService shortUrlDataService;

    /**
     * 查询短链列表
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @GetMapping("/list")
    public TableDataInfo<ShortUrlVO> list(ShortUrlListReq req) {
        startPage();
        ShortUrlEntity param = BeanUtil.copyProperties(req, ShortUrlEntity.class);
        List<ShortUrlEntity> list = shortUrlService.selectList(param);

        // 构造返回结果
        return getDataTable(PageInfoUtils.dto2Vo(list, shortUrl -> {
            ShortUrlVO shortUrlVO = BeanUtil.copyProperties(shortUrl, ShortUrlVO.class);
            // 不想另外写接口刷数据，这里投机取巧处理一下历史数据
            if (StringUtils.isBlank(shortUrlVO.getShortUrl())) {
                String newShortUrl = getShortUrl(shortUrl.getId());
                redundantShortUrl(shortUrl.getId(), newShortUrl);
                shortUrlVO.setShortUrl(newShortUrl);
            }
            return shortUrlVO;
        }));
    }

    /**
     * 查询短链数据列表
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @GetMapping("/dataList")
    public TableDataInfo<ShortUrlDataVO> dataList(ShortUrlDataListReq req) {
        return getDataTable(getDataList(req,false));
    }

    /**
     * 短链数据导出
     * @param req
     * @return
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @GetMapping("export")
    public AjaxResult export(ShortUrlDataListReq req){
        PageInfo<ShortUrlDataVO> dataList = getDataList(req, true);
        List<ShortUrlDataExcelBO> resultList = dataList.getList().stream().map(data -> BeanUtil.copyProperties(data, ShortUrlDataExcelBO.class)).collect(Collectors.toList());

        String fileName = UUID.randomUUID().toString() + "_短链数据.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, ShortUrlDataExcelBO.class).sheet("短链数据").doWrite(resultList);
        return AjaxResult.success(fileName);
    }

    /**
     * 生成短链
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @Log(title = "短链", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public Result<String> generate(@RequestBody ShortUrlGenerateReq req) {
        if (StringUtils.isBlank(req.getOriginUrl())) {
            return ResultBuilder.fail("参数错误");
        }
        String originUrlMd5 = Md5Utils.hash(req.getOriginUrl());

        // 查询是否已生成过短链
        ShortUrlEntity shortUrl = shortUrlService.selectByOriginUrlMd5(originUrlMd5);
        if (null != shortUrl && isEnable(shortUrl.getUrlStatus())) {
            return ResultBuilder.fail("原链接已生成短链 " + shortUrl.getShortUrl());
        }

        // 生成短链
        shortUrl = new ShortUrlEntity();
        shortUrl.setOriginUrl(req.getOriginUrl());
        shortUrl.setOriginUrlMd5(originUrlMd5);
        shortUrl.setUrlType(req.getUrlType());
        shortUrlService.insert(shortUrl);

        // 冗余短链
        String newShortUrl = getShortUrl(shortUrl.getId());
        redundantShortUrl(shortUrl.getId(), newShortUrl);

        // 刷新缓存
        refreshCacheService.sendRefreshShortUrlCacheMsg(shortUrl.getId());
        return ResultBuilder.success(newShortUrl);
    }
    /**
     * 批量生成短链,无需登录
     */
    @PostMapping("/generate/batch")
    public Result<List<ExternalShortUrlVo>> generateBatch(@RequestBody ShortUrlBatchGenerateReq req) {
        List<String> originUrlList = req.getOriginUrlList();
        if (CollectionUtils.isEmpty(originUrlList)) {
            return ResultBuilder.fail("参数错误");
        }
        List<String> originUrlMd5List = originUrlList.stream().map(Md5Utils::hash).collect(Collectors.toList());
        // 查询是否已生成过短链
        List<ShortUrlEntity> shortUrlEntityList = shortUrlService.selectByOriginUrlMd5List(originUrlMd5List);
        List<String> existUrlList = shortUrlEntityList.stream().map(ShortUrlEntity::getOriginUrl).collect(Collectors.toList());
        originUrlList.removeAll(existUrlList);
        List<ShortUrlEntity> shortUrlEntities = originUrlList.stream().map(originUrl -> {
            ShortUrlEntity shortUrl = new ShortUrlEntity();
            shortUrl.setOriginUrl(originUrl);
            shortUrl.setOriginUrlMd5(Md5Utils.hash(originUrl));
            shortUrl.setUrlType(req.getUrlType());
            return shortUrl;
        }).collect(Collectors.toList());
        // 生成短链
        shortUrlService.insertBatch(shortUrlEntities);
        String shortUrlPrefix = sysConfigService.selectConfigCacheByKey(DEFAULT_SHORT_URL_BATCH.getKey());
        shortUrlEntities.forEach(shortUrlEntity -> {
            String encodedId = ShortUrlUtils.shortUrlIdToUrl(shortUrlEntity.getId());
            String newShortUrl = shortUrlPrefix + encodedId;
            shortUrlEntity.setShortUrl(newShortUrl);
        });
        shortUrlService.updateBatchById(shortUrlEntities);
        shortUrlEntities.addAll(shortUrlEntityList);
        List<ExternalShortUrlVo> externalShortUrlVos = shortUrlEntities.stream().map(shortUrlEntity -> {
            ExternalShortUrlVo vo = new ExternalShortUrlVo();
            vo.setOriginUrl(shortUrlEntity.getOriginUrl());
            vo.setShortUrl(shortUrlEntity.getShortUrl());
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(externalShortUrlVos);
    }

    /**
     * 禁用短链
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @Log(title = "短链", businessType = BusinessType.UPDATE)
    @PostMapping("disable")
    public Result<Void> disable(@RequestBody IdReq req) {
        ShortUrlEntity shortUrl = shortUrlService.selectById(req.getId());
        if (null == shortUrl) {
            return ResultBuilder.fail("未查询到短链");
        }
        // 更新状态
        ShortUrlEntity updateEntity = new ShortUrlEntity();
        updateEntity.setId(shortUrl.getId());
        updateEntity.setUrlStatus(DISABLE.getStatus());
        shortUrlService.updateById(updateEntity);

        // 刷新缓存
        refreshCacheService.sendRefreshShortUrlCacheMsg(shortUrl.getId());
        return ResultBuilder.success();
    }

    /**
     * 获取短链链接
     *
     * @param id 短链ID
     * @return 短链链接
     */
    private String getShortUrl(Long id) {
        String shortUrlPrefix = sysConfigService.selectConfigCacheByKey(DEFAULT_SHORT_URL.getKey());
        if (id == null || id < 0) {
            // Or handle as an error, depending on requirements.
            // Returning empty or throwing an exception might be appropriate.
            // For now, let's assume id is always positive for encoding.
            // If id can be 0, RadixUtil.encode handles it (usually results in the first char of alphabet).
            throw new RuntimeException("id is null or id < 0");
        }
        String encodedId = ShortUrlUtils.shortUrlIdToUrl(id);
        return shortUrlPrefix + encodedId;
    }

    private PageInfo<ShortUrlDataVO> getDataList(ShortUrlDataListReq req,Boolean isExport){
        ShortUrlDataParam param = BeanUtil.copyProperties(req,ShortUrlDataParam.class);

        // 短链模糊查询
        if (StringUtils.isNotBlank(req.getShortUrl())) {
            param.setShortUrlIds(getIdsByShortUrl(req.getShortUrl()));
            if (CollectionUtils.isEmpty(param.getShortUrlIds())) {
                return PageInfoUtils.buildReturnList(Collections.emptyList());
            }
        }

        if(BooleanUtils.isNotTrue(isExport)){
            startPage();
        }

        List<ShortUrlDataEntity> shortUrlDataEntities = shortUrlDataService.selectListByParam(param);
        List<Long> urlIds = shortUrlDataEntities.stream().map(ShortUrlDataEntity::getShortUrlId).collect(Collectors.toList());
        List<ShortUrlEntity> entities = shortUrlService.selectListByIds(urlIds);
        Map<Long, ShortUrlEntity> urlMap = entities.stream().collect(Collectors.toMap(ShortUrlEntity::getId, Function.identity(), (v1, v2) -> v1));
        return PageInfoUtils.dto2Vo(shortUrlDataEntities,entity->{
            ShortUrlDataVO vo = BeanUtil.copyProperties(entity,ShortUrlDataVO.class);
            ShortUrlEntity shortUrlEntity = urlMap.get(entity.getShortUrlId());
            if(Objects.nonNull(shortUrlEntity)){
                vo.setShortUrl(shortUrlEntity.getShortUrl());
                vo.setOriginUrl(shortUrlEntity.getOriginUrl());
            }
            return vo;
        });
    }

    /**
     * 冗余短链到数据库
     */
    private void redundantShortUrl(Long id, String newShortUrl) {
        if (null == id || StringUtils.isBlank(newShortUrl)) {
            return;
        }
        ShortUrlEntity updateEntity = new ShortUrlEntity();
        updateEntity.setId(id);
        updateEntity.setShortUrl(newShortUrl);
        shortUrlService.updateById(updateEntity);
    }

    /**
     * 根据短链模糊查询短链ID
     *
     * @param shortUrl 短链
     * @return 短链ID列表
     */
    private List<Long> getIdsByShortUrl(String shortUrl) {
        ShortUrlEntity param = new ShortUrlEntity();
        param.setShortUrl(shortUrl.trim());
        List<ShortUrlEntity> list = shortUrlService.selectList(param);
        return list.stream().map(ShortUrlEntity::getId).collect(Collectors.toList());
    }
}
