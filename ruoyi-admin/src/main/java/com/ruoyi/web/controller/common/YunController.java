package com.ruoyi.web.controller.common;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.file.OSSUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

import static com.ruoyi.common.constant.BizConstants.OSS_URL;

/**
 * 云相关的接口
 *
 * <AUTHOR>
 * @date 2021/7/19
 */
@RestController
public class YunController {

    private static final Logger log = LoggerFactory.getLogger(YunController.class);

    /**
     * 通用上传请求(保存到OSS)
     */
    @PostMapping("/yun/upload")
    public AjaxResult uploadFile(MultipartFile file) {
        try {
            String fileName = OSSUtils.upload(file);
            if (StringUtils.isNotEmpty(fileName)) {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("url", OSS_URL + fileName);
                return ajax;
            }
        } catch (Exception e) {
            log.error("OSS文件上传异常", e);
        }
        return AjaxResult.error("上传失败");
    }

    /**
     * 文档上传请求(保存到OSS)
     */
    @PostMapping("/yun/uploadDocument")
    public AjaxResult uploadDocument(MultipartFile file) {
        try {
            String fileName = OSSUtils.uploadDocument(file);
            if (StringUtils.isNotEmpty(fileName)) {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("url", OSS_URL + fileName);
                return ajax;
            }
        } catch (Exception e) {
            log.error("OSS文件上传异常", e);
        }
        return AjaxResult.error("上传失败");
    }

    /**
     * 通用上传请求(保存到OSS) 不用登录
     */
    @PostMapping("/yun/common/upload")
    public AjaxResult commonUploadFile(MultipartFile file, HttpServletRequest request) {
        try {
            String auth = request.getHeader("auth");
            if(!Objects.equals(auth,"common")){
                return AjaxResult.error("上传失败");
            }

            String fileName = OSSUtils.upload(file);
            if (StringUtils.isNotEmpty(fileName)) {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("url", OSS_URL + fileName);
                return ajax;
            }
        } catch (Exception e) {
            log.error("OSS文件上传异常", e);
        }
        return AjaxResult.error("上传失败");
    }

    /**
     * 通用上传请求(保存到OSS)，无登录限制
     * 限制referer:
     *  nuohe.com.cn, ydns.com.cn, ydns.cn
     */
    @PostMapping("/yun/upload/noauth")
    public AjaxResult uploadFileNoAuth(MultipartFile file) {
        // 限制referer 或者 内网访问
        String referer = ServletUtils.getRequest().getHeader("referer");
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        if (StringUtils.isBlank(referer) || StringUtils.isBlank(ip) ||
                (!referer.contains("nuohe.com.cn") && !referer.contains("ydns.com.cn") && !referer.contains("wanshione.cn")
                        && !referer.contains("ydns.cn") && !IpUtils.internalIp(ip) && !referer.contains("192.168"))) {
            return AjaxResult.error("无访问权限");
        }
        return uploadFile(file);
    }
}
