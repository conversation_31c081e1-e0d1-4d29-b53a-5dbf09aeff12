package com.ruoyi.web.controller.engine;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.annotation.ApiLog;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.enums.redpacket.TransferStatusEnum;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.bo.redpacket.RedPacketRecordBO;
import com.ruoyi.system.entity.landpage.LandpageFormFullRecord;
import com.ruoyi.system.entity.open.AdvertiserCallbackRecord;
import com.ruoyi.system.entity.redpacket.PhoneRedPacketRecordEntity;
import com.ruoyi.system.req.redpacket.RandomRedPacketReq;
import com.ruoyi.system.req.redpacket.TransferApplyReq;
import com.ruoyi.system.service.landpage.LandpageFormRecordService;
import com.ruoyi.system.service.open.AdvertiserCallbackRecordService;
import com.ruoyi.system.service.redpacket.PhoneRedPacketRecordService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.vo.redpacket.RedPacketVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 表单用户领红包接口（不鉴权）
 *
 * <AUTHOR>
 * @date 2022/05/26
 */
@Slf4j
@RestController
@RequestMapping("/redpacket")
public class RedPacketController {

    /**
     * 默认红包金额1元
     */
    private static final int DEFAULT_RED_PACKET_AMOUNT = 100;

    /**
     * 限制最大红包金额20元
     */
    private static final int MAX_RED_PACKET_AMOUNT = 2000;

    @Autowired
    private PhoneRedPacketRecordService phoneRedPacketRecordService;

    @Autowired
    private LandpageFormRecordService landpageFormRecordService;

    @Autowired
    private AdvertiserCallbackRecordService advertiserCallbackRecordService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 领取红包
     */
    @CrossOrigin
    @PostMapping("/randomRedPacket")
    @ApiLog(title = "表单用户领红包-领取红包")
    public AjaxResult randomRedPacket(@RequestBody @Validated RandomRedPacketReq req) {
        String phone = req.getPhone();
        if (!Objects.equals(phone.length(), 11)) {
            return AjaxResult.error("请输入正确的手机号");
        }

        // 风控校验
        if (riskCheck(phone)) {
            return AjaxResult.error("红包领取失败，请稍后重试");
        }

        // 分布式锁限制手机号重复提交
        try (RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K035.join(phone), 30)) {
            if (lock == null) {
                return AjaxResult.error("正在处理，请勿重复提交");
            }

            // 查询是否提交信息
            PhoneRedPacketRecordEntity record = phoneRedPacketRecordService.selectByPhone(phone);
            if (null != record) {
                // 判断是否已经处理
                if (Objects.equals(record.getTransferStatus(), TransferStatusEnum.NO_TRANSFER.getStatus()) && StringUtils.isNotBlank(record.getAlipayAccount())) {
                    return AjaxResult.error("您的红包提现申请已提交，预计1个工作日内到账");
                }
                if (Objects.equals(record.getTransferStatus(), TransferStatusEnum.TRANSFER.getStatus())) {
                    return AjaxResult.error("您已领取过红包，敬请期待下次机会咯～");
                }
                // 无效信息可以重复申请 产品需求

            } else {
                LandpageFormFullRecord formRecord = selectRecordByPhone(phone);
                if (null == formRecord) {
                    return AjaxResult.error(ErrorCode.E110003);
                }
                record = new PhoneRedPacketRecordEntity();
                record.setPhone(phone);
                record.setName(formRecord.getName());
                record.setAmount(randomAmount());
                record.setOrderId(formRecord.getOrderId());
                if (phoneRedPacketRecordService.insert(record) < 1) {
                    return AjaxResult.error("红包领取失败，请稍后重试");
                }
            }

            // 生成并缓存验证码
            String code = RandomUtil.randomString(6);
            redisCache.setCacheObject(EngineRedisKeyFactory.K036.join(code), phone, 1, TimeUnit.HOURS);

            // 构造返回结果
            RedPacketVO redPacket = new RedPacketVO();
            redPacket.setAmount(record.getAmount());
            redPacket.setPhone(phone);
            redPacket.setCode(code);
            return AjaxResult.success(redPacket);
        } catch (Exception e) {
            log.error("表单用户领红包-领取红包异常, phone={}", phone, e);
        }
        return AjaxResult.error("红包领取失败，请稍后重试");
    }

    /**
     * 提交收款信息
     */
    @CrossOrigin
    @PostMapping("/transferApply")
    @ApiLog(title = "表单用户领红包-提交收款信息")
    public AjaxResult transferApply(@RequestBody @Validated TransferApplyReq req) {
        log.info("表单用户领红包-提交收款信息, req={}", JSON.toJSONString(req));

        if (!Objects.equals(req.getPhone().length(), 11)) {
            return AjaxResult.error("无效的手机号");
        }

        // 验证码校验
        if (!Objects.equals(req.getPhone(), redisCache.getCacheObject(EngineRedisKeyFactory.K036.join(req.getCode())))) {
            return AjaxResult.error("提交失败，请退出重新领取红包");
        }

        // 分布式锁限制手机号重复提交
        try (RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K035.join(req.getPhone()), 30)) {
            if (lock == null) {
                return AjaxResult.error("正在处理，请勿重复提交");
            }

            // 查询是否提交信息
            PhoneRedPacketRecordEntity record = phoneRedPacketRecordService.selectByPhone(req.getPhone());
            if (null != record) {
                // 判断是否已经处理
                if (Objects.equals(record.getTransferStatus(), TransferStatusEnum.NO_TRANSFER.getStatus()) && StringUtils.isNotBlank(record.getAlipayAccount())) {
                    return AjaxResult.error("您的红包提现申请已提交，预计1个工作日内到账");
                }
                // 已填写收款信息
                if (Objects.equals(record.getTransferStatus(), TransferStatusEnum.TRANSFER.getStatus())) {
                    return AjaxResult.error("您已领取过红包，敬请期待下次机会咯～");
                }
            } else {
                return AjaxResult.error("收款信息提交失败，请稍后重试");
            }
            //新增信息提交记录
            RedPacketRecordBO bo = buildRedpacketRecord(req);
            // 更新记录
            PhoneRedPacketRecordEntity updateRecord = new PhoneRedPacketRecordEntity();
            updateRecord.setId(record.getId());
            updateRecord.setAlipayAccount(req.getAlipayAccount());
            updateRecord.setAlipayName(req.getAlipayName());
            updateRecord.setTransferStatus(TransferStatusEnum.NO_TRANSFER.getStatus());
            updateRecord.setIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
            updateRecord.setUserAgent(StringUtils.defaultString(ServletUtils.getRequest().getHeader("User-Agent")));
            updateRecord.setExtInfo(record.getExtInfo()+JSON.toJSONString(bo));
            if (phoneRedPacketRecordService.updateById(updateRecord) < 1) {
                return AjaxResult.error("收款信息提交失败，请稍后重试");
            }
            return AjaxResult.success();
        } catch (Exception e) {
            log.error("表单用户领红包-提交收款信息异常, req={}", JSON.toJSONString(req), e);
        }
        return AjaxResult.error("收款信息提交失败，请稍后重试");
    }

    /**
     * 构建红包记录
     * @param req
     * @return
     */
    private RedPacketRecordBO buildRedpacketRecord(TransferApplyReq req) {
        RedPacketRecordBO bo = new RedPacketRecordBO();
        bo.setIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        bo.setAlipayAccount(req.getAlipayAccount());
        bo.setAlipayName(req.getAlipayName());
        bo.setUserAgent(StringUtils.defaultString(ServletUtils.getRequest().getHeader("User-Agent")));
        bo.setLogTime(new Date());
        return bo;
    }

    /**
     * 根据手机号查询表单记录
     *
     * @param phone 手机号
     * @return 表单记录
     */
    private LandpageFormFullRecord selectRecordByPhone(String phone) {
        // 查询表单
        LandpageFormFullRecord formParam = new LandpageFormFullRecord();
        formParam.setPhone(phone);
        formParam.setTargetAdvertiserId(84L);
        formParam.setIsSuccess(1);
        if (SpringEnvironmentUtils.isTest()) {
            formParam.setTargetAdvertiserId(null);
            formParam.setIsSuccess(null);
        }


        List<LandpageFormFullRecord> list = landpageFormRecordService.selectList(formParam);
        LandpageFormFullRecord formRecord = CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
        // 检查手机号是否充值
        if (null != formRecord) {
            AdvertiserCallbackRecord callbackParam = new AdvertiserCallbackRecord();
            callbackParam.setOrderNo(formRecord.getOrderId());
            callbackParam.setStatus(2);
            callbackParam.setAdvertiserId(84L);
            if (SpringEnvironmentUtils.isTest()) {
                callbackParam.setAdvertiserId(null);
            }

            AdvertiserCallbackRecord callbackRecord = advertiserCallbackRecordService.selectBy(callbackParam);
            if (null != callbackRecord) {
                return formRecord;
            }
        }
        return null;
    }

    /**
     * 风控校验
     *
     * @param phone 手机号
     * @return 是否通过
     */
    private boolean riskCheck(String phone) {
        String key = EngineRedisKeyFactory.K037.join(IpUtils.getIpAddr(ServletUtils.getRequest()));
        redisCache.addCacheSet(key, phone);
        redisCache.expire(key, 1, TimeUnit.HOURS);
        Long size = redisCache.countCacheSet(key);
        return null != size && size > 5;
    }

    /**
     * 获取红包金额(目前是配置)
     */
    private Integer randomAmount() {
        String amount = sysConfigService.selectConfigCacheByKey(BizConfigEnum.FORM_RED_PACKET_AMOUNT.getKey());
        if (StringUtils.isNotBlank(amount)) {
            try {
                return Math.min((int) (Double.parseDouble(amount) * 100), MAX_RED_PACKET_AMOUNT);
            } catch (Exception e) {
                log.error("表单用户领红包，获取红包金额异常, amount={}", amount, e);
            }
        }
        return DEFAULT_RED_PACKET_AMOUNT;
    }
}
