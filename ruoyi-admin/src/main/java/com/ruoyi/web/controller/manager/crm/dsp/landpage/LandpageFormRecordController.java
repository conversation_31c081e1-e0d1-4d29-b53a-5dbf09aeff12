package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.IdCardAuditStatus;
import com.ruoyi.common.enums.IdCardAuditApiType;
import com.ruoyi.common.enums.landpage.AssignStatus;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.landpage.LandpageFormFullRecord;
import com.ruoyi.system.entity.datashow.LandpageFormRecordExcel;
import com.ruoyi.system.req.datashow.LandpageFromRecordReq;
import com.ruoyi.system.req.landpage.LpManualAssignReq;
import com.ruoyi.system.service.landpage.LandpageFormRecordService;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.landpage.LandpageFormSendRecordService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.open.AdvertiserCallbackRecordService;
import com.ruoyi.system.vo.advert.AdvertSelectVO;
import com.ruoyi.system.vo.app.AppSelectVO;
import com.ruoyi.system.vo.datashow.LandpageFormRecordVO;
import com.ruoyi.system.vo.landpage.CompanySelectVO;
import com.ruoyi.system.vo.slot.SlotSelectVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.landpage.AssignStatus.ASSIGNED_FAILED;
import static com.ruoyi.common.enums.landpage.AssignStatus.ASSIGNED_SUCCESS;

/**
 * [CRM后台]号卡转化记录
 *
 * <AUTHOR>
 * @date 2021-08-31
 */
@RestController
@Slf4j
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/datashow/lpForm")
public class LandpageFormRecordController extends BaseController {

    @Autowired
    private LandpageFormRecordService landpageFormRecordService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AdvertiserCallbackRecordService advertiserCallbackRecordService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private LandpageFormSendRecordService landpageFormSendRecordService;

    @Autowired
    private AccountService accountService;
    @Autowired
    private RedisAtomicClient redisAtomicClient;
    /**
     * 广告下拉列表
     */
    @GetMapping("/advertList")
    public Result<List<AdvertSelectVO>> advertList() {
        List<Long> advertIds = landpageFormRecordService.selectTotalAdvertIds();
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        return ResultBuilder.success(advertIds.stream().sorted(Comparator.reverseOrder()).map(advertId -> new AdvertSelectVO(advertId, advertNameMap.get(advertId))).collect(Collectors.toList()));
    }

    /**
     * 媒体下拉列表
     */
    @GetMapping("/appList")
    public Result<List<AppSelectVO>> appList() {
        List<Long> appIds = landpageFormRecordService.selectTotalAppIds();
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        return ResultBuilder.success(appIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(appId -> new AppSelectVO(appId, appNameMap.get(appId))).collect(Collectors.toList()));
    }

    /**
     * 广告位下拉列表
     */
    @GetMapping("/slotList")
    public Result<List<SlotSelectVO>> slotList() {
        List<Long> slotIds = landpageFormRecordService.selectTotalSlotIds();
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(slotIds);
        return ResultBuilder.success(slotIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(slotId -> new SlotSelectVO(slotId, slotNameMap.get(slotId))).collect(Collectors.toList()));
    }

    /**
     * 广告主下拉列表
     */
    @GetMapping("/advertiserList")
    public Result<List<CompanySelectVO>> advertiserList() {
        List<Long> advertiserIds = landpageFormSendRecordService.selectTotalTargetAdvertiserIds();
        Map<Long, String> advertiserNameMap = accountService.selectCompanyNameMap(advertiserIds);
        return ResultBuilder.success(advertiserIds.stream().sorted(Comparator.reverseOrder()).map(advertiserId -> new CompanySelectVO(advertiserId, advertiserNameMap.get(advertiserId))).collect(Collectors.toList()));
    }

    /**
     * 查询落地页单记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LandpageFromRecordReq req) {
        LandpageFormFullRecord param = convertReqToParam(req);
        startPage();
        List<LandpageFormFullRecord> list = landpageFormRecordService.selectList(param);

        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            LandpageFormRecordVO recordVO = BeanUtil.copyProperties(data, LandpageFormRecordVO.class);
            // 脱敏
            recordVO.setPhone(DesensitizedUtil.mobilePhone(recordVO.getPhone()));
            recordVO.setIdCard(DesensitizedUtil.idCardNum(recordVO.getIdCard(), 14, 0));
            recordVO.setIdCard(recordVO.getIdCard() + " (" + data.getAge() + ")");

            // 分配状态
            if (null != data.getCallbackTime()) {
                recordVO.setTargetAdvertiserId(data.getTargetAdvertiserId());
                recordVO.setAssignTime(data.getCallbackTime());
                recordVO.setAssignStatus(Objects.equals(data.getIsSuccess(), 1) ? ASSIGNED_SUCCESS.getStatus() : ASSIGNED_FAILED.getStatus());
                if (null != data.getChannel() && data.getChannel() > 0) {
                    recordVO.setChannel(data.getChannel());
                }
                try {
                    JSONObject resp = JSON.parseObject(StringUtils.defaultString(data.getResp()));
                    if (null != resp) {
                        Integer code = resp.getInteger("code");
                        Boolean success = resp.getBoolean("success");
                        if (null != code && !Objects.equals(code, 0) && !Objects.equals(code, 200)
                                || null != success && !success) {
                            String msg = StringUtils.defaultString(resp.getString("msg"));
                            recordVO.setReason(msg);
                        }
                    }
                } catch (Exception ignore) {
                }
            } else {
                if (Objects.equals(data.getIsRepeated(), 1)) {
                    recordVO.setReason("用户重复表单");
                    recordVO.setAssignStatus(AssignStatus.UNASSIGNED_INVALID.getStatus());
                } else {
                    recordVO.setAssignStatus(AssignStatus.UNASSIGNED_VALID.getStatus());
                }
            }
            return recordVO;
        }));
    }

    /**
     * 导出落地页单记录列表
     */
    @Log(title = "落地页单记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LandpageFromRecordReq req) {
        Long crmAccountId = SecurityUtils.getLoginUser().getCrmAccountId();
        try (RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K033.join(crmAccountId), 10 * 60 * 60)) {
            if (lock == null) {
                return AjaxResult.error("正在导出，请耐心等待");
            }
            LandpageFormFullRecord param = convertReqToParam(req);
            List<LandpageFormFullRecord> list = landpageFormRecordService.selectList(param);
            Set<Long> advertiserIds = new HashSet<>();

            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(record -> {
                    record.setIdCardAuditStr(IdCardAuditStatus.getDescByStatus(record.getIdCardAudit()));
                    record.setAuditApiTypeStr(IdCardAuditApiType.getDescByType(record.getAuditApiType()));

                    // 分配状态
                    if (null != record.getCallbackTime()) {
                        record.setCallbackStatus("已分配");
                        if (null != record.getTargetAdvertiserId()) {
                            advertiserIds.add(record.getTargetAdvertiserId());
                        }
                        try {
                            JSONObject resp = JSON.parseObject(StringUtils.defaultString(record.getResp()));
                            if (null != resp) {
                                Integer code = resp.getInteger("code");
                                Boolean success = resp.getBoolean("success");
                                if (null != code && !Objects.equals(code, 0) && !Objects.equals(code, 200)
                                        || null != success && !success) {
                                    String msg = StringUtils.defaultString(resp.getString("msg"));
                                    record.setCallbackStatus(record.getCallbackStatus() + "（报错）：" + msg);
                                }
                            }
                        } catch (Exception e) {
                            record.setCallbackStatus(record.getCallbackStatus() + "（报错）");
                        }
                    } else {
                        if (Objects.equals(record.getIsRepeated(), 1)) {
                            record.setCallbackStatus("不可分配（重复表单）");
                        } else {
                            record.setCallbackStatus("未分配");
                        }
                    }
                });
            }
            List<LandpageFormRecordExcel> landpageFormRecordExcels = BeanUtil.copyToList(list, LandpageFormRecordExcel.class);
            // 补充上报广告主名称
            Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(new ArrayList<>(advertiserIds));
            // 后端转化
            Map<String, String> advertiserCallbackMap = advertiserCallbackRecordService.selectMapByOrderNo(ListUtils.mapToList(list, LandpageFormFullRecord::getOrderId));
            for (LandpageFormRecordExcel record : landpageFormRecordExcels) {
                if (null != record.getTargetAdvertiserId()) {
                    record.setTargetAdvertiserName(advertiserNameMap.get(record.getTargetAdvertiserId()));
                }
                record.setAdvertiserCallback(advertiserCallbackMap.get(record.getOrderId()));
            }

            String fileName = UUID.randomUUID().toString() + "_落地页表单记录数据.xlsx";
            String filePath = RuoYiConfig.getDownloadPath() + fileName;
            // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为模板 然后文件流会自动关闭
            EasyExcel.write(filePath, LandpageFormRecordExcel.class).sheet("落地页表单记录数据").doWrite(landpageFormRecordExcels);
            return AjaxResult.success(fileName);
        } catch (Exception e) {
            log.error("导出落地页号卡记录列表异常,e:", e);
            return AjaxResult.error("导出失败,请联系系统管理员");
        }

    }


    /**
     * 手动分配落地页表单
     */
    @PostMapping("/manualAssign")
    public AjaxResult manualAssign(@RequestBody LpManualAssignReq req) {
        // 参数校验
        if (CollectionUtils.isEmpty(req.getRecordIds()) || CollectionUtils.isEmpty(req.getList()) || null == req.getMin()
                || null == req.getMax() || req.getMin() < 0 || req.getMax() < 0) {
            return AjaxResult.error("参数错误");
        }
        if (req.getMin() >= req.getMax()) {
            req.setMax(req.getMin() + 1);
        }
        if (req.getList().stream().mapToInt(LpManualAssignReq.AdvertiserRate::getRate).sum() != 100) {
            return AjaxResult.error("百分比之和不为100%");
        }
        for (LpManualAssignReq.AdvertiserRate advertiser : req.getList()) {
            if (null == advertiser.getAdvertiserId()) {
                return AjaxResult.error("请选择广告主");
            }
            if (null == advertiser.getRate()) {
                advertiser.setRate(100);
            }
            if (advertiser.getRate() < 0 || advertiser.getRate() > 100) {
                return AjaxResult.error("请输入有效的百分比");
            }
            if (null != advertiser.getFormPrice() && advertiser.getFormPrice() < 0) {
                return AjaxResult.error("表单价格不能为负数");
            }
        }
        landpageFormRecordService.manualAssign(req);
        return AjaxResult.success();
    }

    private LandpageFormFullRecord convertReqToParam(LandpageFromRecordReq req) {
        LandpageFormFullRecord param = new LandpageFormFullRecord();
        param.setAdvertIds(req.getAdvertIds());
        param.setLandpageUrl(req.getLandpageUrl());
        param.setStartDate(req.getStartDate());
        param.setCbStartDate(req.getCbStartDate());
        param.setAssignStatus(req.getAssignStatus());
        param.setAdvertiserIds(req.getTargetAdvertiserIds());
        param.setLandpageTag(req.getLandpageTag());
        param.setAppIds(req.getAppIds());
        param.setSlotIds(req.getSlotIds());
        // 年龄限制
        if (null != req.getAgeMin()) {
            param.setEndBirth(DateUtil.offset(DateUtil.beginOfDay(new Date()), DateField.YEAR, -req.getAgeMin()));
        }
        if (null != req.getAgeMax()) {
            param.setStartBirth(DateUtil.offset(DateUtil.beginOfDay(new Date()), DateField.YEAR, -req.getAgeMax() - 1));
            param.setStartBirth(DateUtil.offsetDay(param.getStartBirth(), 1));
        }
        // 留个特殊查询的入口
        req.setName(StrUtil.trim(req.getName()));
        if (StringUtils.isNumeric(req.getName())) {
            // 手机号查询
            if (req.getName().startsWith("1") && req.getName().length() == 11) {
                param.setPhone(req.getName());
            }
            // 订单号查询
            else {
                param.setOrderId(req.getName());
            }
        } else {
            param.setName(req.getName());
        }
        // 日期处理
        if (null != req.getEndDate()) {
            param.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        if (null != req.getCbEndDate()) {
            param.setCbEndDate(DateUtil.endOfDay(req.getCbEndDate()));
        }
        // 地域列表
        if (StringUtils.isNotBlank(req.getAreaSearch())) {
            param.setAreaList(Splitter.on(",").omitEmptyStrings().splitToStream(req.getAreaSearch())
                    .map(area -> StrUtil.removeAny(area, "省", "壮族自治区", "回族自治区", "维吾尔自治区", "自治区"))
                    .collect(Collectors.toList()));
            if (param.getAreaList().contains("文山壮族苗族自治州")) {
                param.getAreaList().add("文山州");
            }
        }
        return param;
    }
}
