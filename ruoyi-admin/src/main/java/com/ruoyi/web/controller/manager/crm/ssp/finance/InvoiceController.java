package com.ruoyi.web.controller.manager.crm.ssp.finance;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.account.DataPermissionType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.invoice.InvoiceSumListBO;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.invoice.InvoiceEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.req.invoice.InvoiceAddReq;
import com.ruoyi.system.req.invoice.InvoiceEditReq;
import com.ruoyi.system.req.invoice.InvoiceListReq;
import com.ruoyi.system.service.invoice.InvoiceService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.vo.invoice.InvoiceExportVO;
import com.ruoyi.system.vo.invoice.InvoiceInfoListVO;
import com.ruoyi.system.vo.invoice.InvoiceListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发票相关controller
 *
 * <AUTHOR>
 * @date 2022/10/20 2:34 下午
 */
@RestController
@RequestMapping("manager/crm/invoice")
public class InvoiceController extends BaseController {

    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private AccountService accountService;
    @Autowired
    private DataPermissionManager dataPermissionManager;

    /**
     * 新增发票
     */
    @Log(title = "发票", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public Result<Boolean> add(@RequestBody @Validated InvoiceAddReq req){
        Long userId = SecurityUtils.getLoginUser().getCrmAccountId();
        InvoiceEntity entity = BeanUtil.copyProperties(req, InvoiceEntity.class);
        entity.setOperatorId(userId);
        Boolean success = invoiceService.insert(entity);
        return ResultBuilder.success(success);
    }

    /**
     * 修改发票
     */
    @Log(title = "发票", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public Result<Boolean> edit(@RequestBody @Validated InvoiceEditReq req){
        Long userId = SecurityUtils.getLoginUser().getCrmAccountId();
        //校验金额不能为负
        InvoiceEntity entity = BeanUtil.copyProperties(req,InvoiceEntity.class);
        entity.setOperatorId(userId);
        return ResultBuilder.success(invoiceService.updateById(entity));
    }

    /**
     * 根据账号id查询发票信息列表
     */
    @GetMapping("getInvoiceInfoListByAccountId")
    public TableDataInfo<InvoiceInfoListVO> getInvoiceInfoListByAccountId(@Validated @NotNull(message = "账号id不能为空") Long accountId){
        startPage();
        List<InvoiceEntity> entities = invoiceService.getInvoiceInfoListByAccountId(accountId);
        //操作人账号id
        List<Long> accountIds = entities.stream().map(InvoiceEntity::getOperatorId).collect(Collectors.toList());
        //媒体账号id
        accountIds.add(accountId);
        List<Account> accountList = accountService.selectListByIds(accountIds);
        Map<Long, Account> accountMap = accountList.stream().collect(Collectors.toMap(Account::getId, Function.identity()));
        Account account = accountMap.get(accountId);

        return getDataTable(PageInfoUtils.dto2Vo(entities, entity ->{
            InvoiceInfoListVO vo = BeanUtil.copyProperties(entity, InvoiceInfoListVO.class);
            Account operator = accountMap.get(entity.getOperatorId());
            vo.setOperatorName(operator.getContact());
            vo.setAccountId(accountId);
            vo.setCompanyName(account.getCompanyName());
            vo.setEmail(account.getEmail());
            return vo;
        }));
    }

    /**
     * 查询发票列表
     */
    @GetMapping("list")
    public TableDataInfo<InvoiceListVO> list(InvoiceListReq req){
        List<InvoiceSumListBO> invoiceSumListBOS = list(req, false);
        List<Long> accountIds = invoiceSumListBOS.stream().map(InvoiceSumListBO::getAccountId).collect(Collectors.toList());
        List<Account> accounts = accountService.selectListByIds(accountIds);
        Map<Long, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getId, Function.identity()));

        return getDataTable(PageInfoUtils.dto2Vo(invoiceSumListBOS,bo ->{
            InvoiceListVO vo = BeanUtil.copyProperties(bo,InvoiceListVO.class);
            Account account = accountMap.get(bo.getAccountId());
            if(Objects.nonNull(account)){
                vo.setEmail(account.getEmail());
                vo.setCompanyName(account.getCompanyName());
            }
            return vo;
        }));
    }

    /**
     * 发票列表导出
     */
    @Log(title = "发票数据导出", businessType = BusinessType.EXPORT)
    @GetMapping("export")
    public AjaxResult export(InvoiceListReq req){
        List<InvoiceSumListBO> invoiceSumListBOS = list(req, true);
        List<Long> accountIds = invoiceSumListBOS.stream().map(InvoiceSumListBO::getAccountId).collect(Collectors.toList());
        List<Account> accounts = accountService.selectListByIds(accountIds);
        Map<Long, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getId, Function.identity()));
        List<InvoiceExportVO> resultList = invoiceSumListBOS.stream().map(bo -> {
            InvoiceExportVO vo = BeanUtil.copyProperties(bo, InvoiceExportVO.class);
            Account account = accountMap.get(bo.getAccountId());
            if (Objects.nonNull(account)) {
                vo.setEmail(account.getEmail());
                vo.setCompanyName(account.getCompanyName());
            }
            vo.setInvoiceAmountSum(NumberUtils.fenToYuan(bo.getInvoiceAmountSum()));
            vo.setDebtAmount(NumberUtils.fenToYuan(bo.getPrepayAmount() - bo.getInvoiceAmountSum()));
            vo.setStatus(bo.getPrepayAmount() - bo.getInvoiceAmountSum() > 0 ?"欠票":"已结算");
            return vo;
        }).collect(Collectors.toList());
        ExcelUtil<InvoiceExportVO> util = new ExcelUtil<>(InvoiceExportVO.class);
        return util.exportExcel(resultList, "发票数据");
    }

    /**
     * 查询发票列表
     */
    public List<InvoiceSumListBO> list(InvoiceListReq req,Boolean isExport){
        List<Long> accountIds = new ArrayList<>();
        if(StringUtils.isNotBlank(req.getAccountSearch()) || StringUtils.isNotBlank(req.getCompanyName())){
            accountIds = accountService.selectIdsByIdOrEmailAndCompany(req.getAccountSearch(),req.getCompanyName());
            if(CollectionUtils.isEmpty(accountIds)){
                return Collections.emptyList();
            }
        }
        if(Objects.nonNull(req.getStartDate()) && Objects.nonNull(req.getEndDate())){
            //查询指定日期有更新过发票的媒体账号
            List<InvoiceEntity> invoiceEntities = invoiceService.selectListByGmtModified(req.getStartDate(), req.getEndDate());
            if(CollectionUtils.isEmpty(invoiceEntities)){
                return Collections.emptyList();
            }
            List<Long> accountList = invoiceEntities.stream().map(InvoiceEntity::getAccountId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(accountIds)){
                accountIds = accountList;
            }else{
                accountIds.retainAll(accountList);
                if(CollectionUtils.isEmpty(accountIds)){
                    return Collections.emptyList();
                }
            }
        }

        //数据权限判断
        DataPermissionBo dataPermissionBo = dataPermissionManager.selectAccount();
        List<Long> permissionAccountIds = dataPermissionBo.getValues();
        if(!Objects.equals(dataPermissionBo.getType(), DataPermissionType.FULL.getType())){
            if(CollectionUtils.isEmpty(permissionAccountIds)){
                return Collections.emptyList();
            }
            if(CollectionUtils.isEmpty(accountIds)){
                accountIds = permissionAccountIds;
            }else{
                accountIds.retainAll(permissionAccountIds);
                if(CollectionUtils.isEmpty(accountIds)){
                    return Collections.emptyList();
                }
            }
        }
        if(!isExport){
            startPage();
        }

        //根据条件查询发票列表
        return invoiceService.selectListByAccountIds(accountIds, req.getStatus());
    }
}
