package com.ruoyi.web.controller.open;

import com.alibaba.fastjson.JSON;
import com.ruoyi.system.req.open.WeiboOAuthReq;
import com.ruoyi.system.service.open.WeiboService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 微博接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/05/23
 */
@Slf4j
@RestController
@RequestMapping("/open/oauth2/weibo")
public class WeiboController {

    @Autowired
    private WeiboService weiboService;

    /**
     * 微博事件回调接口
     */
    @CrossOrigin
    @GetMapping("/callback")
    public void callback(WeiboOAuthReq req) {
        log.info("微博回调，req={}", JSON.toJSONString(req));
        weiboService.getAndCacheToken(req.getCode(), req.getState());
    }
}
