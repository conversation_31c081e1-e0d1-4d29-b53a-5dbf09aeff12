package com.ruoyi.web.controller.manager.crm.dsp.traffic;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.common.MobileDataBo;
import com.ruoyi.system.entity.common.MobileEntity;
import com.ruoyi.system.req.traffic.MobileListReq;
import com.ruoyi.system.req.traffic.MobileReq;
import com.ruoyi.system.service.common.MobileService;
import com.ruoyi.system.vo.traffic.MobileListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 设备信息Controller
 *
 * <AUTHOR>
 * @date 2022-11-11
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/traffic/device")
public class MobileController extends BaseController {

    @Autowired
    private MobileService mobileService;

    /**
     * 查询设备列表
     */
    @GetMapping("list")
    public TableDataInfo<MobileListVO> list(MobileListReq req) {
        startPage();
        List<MobileDataBo> list = mobileService.selectList(req);
        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            MobileListVO mobile = BeanUtil.copyProperties(data, MobileListVO.class);
            mobile.setHapLaunchRate(NumberUtils.calculatePercent(data.getHapLaunchPv(), data.getAdClickPv()));
            mobile.setLpArriveRate(NumberUtils.calculatePercent(data.getLpExposurePv(), data.getAdClickPv()));
            return mobile;
        }));
    }

    /**
     * 更新设备信息
     */
    @Log(title = "设备信息", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public Result<Void> update(@RequestBody MobileReq req) {
        if (StringUtils.isBlank(req.getModel()) || StringUtils.isBlank(req.getBrand())) {
            return ResultBuilder.fail("参数错误");
        }
        MobileEntity mobile = new MobileEntity();
        mobile.setModel(req.getModel());
        mobile.setBrand(req.getBrand().toUpperCase());
        mobile.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
        return ResultBuilder.result(mobileService.updateByModel(mobile));
    }
}
