package com.ruoyi.web.controller.open;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.ConvertUploadStatus;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.enums.landpage.LandPageSkinTypeEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.account.AdvertiserAccess;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.entity.open.AdvertiserCallbackRecord;
import com.ruoyi.system.manager.common.CheckManager;
import com.ruoyi.system.req.open.AppAdvertiserCallbackReq;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.DeviceUidService;
import com.ruoyi.system.service.engine.SlotUpService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.AdvertiserCacheService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.open.AdvertiserCallbackRecordService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.ruoyi.common.enums.InnerLogType.CONVERT_EVENT;
import static com.ruoyi.common.enums.advert.ConvType.PAY;
import static com.ruoyi.common.enums.advert.ConvType.isPay;
import static com.ruoyi.common.enums.common.MapConfigEnum.ADVERTISER_CONV_MAP;

/**
 * APP广告主接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2023/06/09
 */
@Slf4j
@RestController
@RequestMapping("/open/app/advertiser")
public class OpenAppAdvertiserController {

    @Autowired
    private AdvertiserCacheService advertiserCacheService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private OrderService orderService;

    @Autowired
    private DeviceUidService deviceUidService;

    @Autowired
    private StatService statService;

    @Autowired
    private AdvertiserCallbackRecordService advertiserCallbackRecordService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private CheckManager checkManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SlotUpService slotUpService;

    @Autowired
    private ConvCallbackService convCallbackService;

    @Autowired
    private LandpageCacheService landpageCacheService;

    /**
     * 广告主回调接口
     */
    @CrossOrigin
    @PostMapping("/callback")
    public AjaxResult callback(@RequestBody AppAdvertiserCallbackReq req) {
        AjaxResult result;
        try {
            result = handleCallback(req);
        } catch (Exception e) {
            log.error("APP广告主回调异常, req={}", JSON.toJSONString(req), e);
            result = AjaxResult.error("系统异常");
        }
        log.info("APP广告主回调记录: req={}, result={}", JSON.toJSONString(req), JSON.toJSONString(result));
        return result;
    }

    /**
     * 从参数出解析设备唯一标识列表
     */
    private List<String> getUids(AppAdvertiserCallbackReq req) {
        return Arrays.asList(StringUtils.defaultString(req.getIdfaMd5()), StringUtils.defaultString(req.getImeiMd5()), StringUtils.defaultString(req.getOaidMd5()));
    }

    /**
     * 处理后端转化
     */
    private AjaxResult handleCallback(AppAdvertiserCallbackReq req) {
        // 参数校验
        if (null == req.getStatus()) {
            return AjaxResult.error("无效的转化类型");
        }
        if (null == req.getTimestamp() || req.getTimestamp() < System.currentTimeMillis() - 600000) {
            return AjaxResult.error("无效的时间戳");
        }

        // 查询广告主秘钥
        AdvertiserAccess access = advertiserCacheService.queryAdvertiserAccess(req.getAccessKey());
        if (null == access || null == access.getAdvertiserId()) {
            return AjaxResult.error("无效的广告主标识");
        }

        // 根据设备唯一标识匹配订单号
        HttpServletRequest request = ServletUtils.getRequest();
        String ip = StrUtil.blankToDefault(req.getIp(), IpUtils.getIpAddr(request));
        String userAgent = StrUtil.blankToDefault(req.getUserAgent(), null != request ? request.getHeader("User-Agent") : "");
        String orderId = StrUtil.isNumeric(req.getOrderNo()) ? req.getOrderNo() : deviceUidService.matchOrderId(access.getAdvertiserId(), ip, userAgent, getUids(req));
        if (StringUtils.isBlank(orderId)) {
            orderId = deviceUidService.matchOrderId(access.getAdvertiserId(), ip, req.getOsType(), req.getOsVersion(), req.getMobileModel());
        }
        if (StringUtils.isBlank(orderId)) {
            return AjaxResult.error("未匹配到有效订单");
        }

        // 测试订单
        if (StrUtil.equalsAny(orderId, "123456", "888888")) {
            log.info("APP广告主后端转化上报测试, orderId={}, accessKey={}, status={}", orderId, req.getAccessKey(), req.getStatus());
            return AjaxResult.success("测试成功");
        }

        // 分布式锁防重复提交(5分钟)
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K013.join(orderId, req.getStatus()), 300);
        if (lock == null) {
            return AjaxResult.error("后端回传重复上报");
        }

        // 查询订单
        Order order = orderService.selectByOrderId(orderId);

        // 落地页皮肤类型
        Integer landpageSkinType = getLandpageSkinType(order);

        // 是否是落地页转化
        boolean isLandpageClick = isLandpageClick(access.getAdvertiserId(), landpageSkinType, req.getStatus());

        // 支付成功的状态，记录落地页转化埋点
        try {
            if (isLandpageClick) {
                statService.landpageClick(orderId);
            }
        } catch (Exception e) {
            log.error("APP广告主回调, 落地页转化埋点统计异常, req={}", JSON.toJSONString(req), e);
        }

        try {
            statService.convertEvent(orderId, req.getStatus());
        } catch (Exception e) {
            log.error("APP广告主回调, 后端转化埋点统计异常, req={}", JSON.toJSONString(req), e);
        }

        try {
            AdvertiserCallbackRecord record = new AdvertiserCallbackRecord();
            if (null != order) {
                record.setSlotId(order.getSlotId());
                record.setAdvertId(order.getAdvertId());
                record.setConsumerId(order.getConsumerId());
                record.setActivityId(order.getActivityId());
                record.setOrientId(order.getOrientId());
            }
            record.setOrderNo(orderId);
            record.setAdvertiserId(access.getAdvertiserId());
            record.setStatus(req.getStatus());
            record.setSubmitTime(new Date(req.getTimestamp()));
            record.setCurDate(DateUtil.beginOfDay(record.getSubmitTime()));
            advertiserCallbackRecordService.insert(record);
        } catch (Exception e) {
            log.error("APP广告主回调, 新增回调记录异常, req={}", JSON.toJSONString(req), e);
        }

//        try {
//            if (null != order) {
//                if (ConvType.isAppActive(req.getStatus())) {
//                    callbackService.callbackOceanEngine(order, "form");
//                }
//            }
//        } catch (Exception e) {
//            log.error("APP广告主回调, 上报媒体异常, req={}", JSON.toJSONString(req), e);
//        }

        // 获取广告位参数
        JSONObject param = callbackService.getParameterFromCache(orderId);

        // [固定收益上报]缓存转化个数和理论消耗
        if (isLandpageClick) {
            slotUpService.slotUpAddCostMulti(order, param.getString("hu"));
        }

        // 上报媒体
        if (null != order) {
            log.info("APP广告主回调, 上报媒体广告位{}, orderId={}, param={}", order.getSlotId(), orderId, JSON.toJSONString(param));
        }
        mediaCallback(req.getStatus(), order, isLandpageClick);

        // 广告主转化上限检查
        checkAdvertiserConvLimit();

        return AjaxResult.success("成功");
    }

    /**
     * 上报媒体
     */
    private void mediaCallback(Integer status, Order order, boolean isLandpageClick) {
        if (null == order) {
            return;
        }

        if (isLandpageClick) {
            // 上报巨量【付费事件后面上传的时候要注意同步修改：active_pay】
//                callbackService.callbackOceanEngine(order.getOrderId(), order.getSlotId(), "active_pay");
//            callbackService.callbackOceanEngine(order, "form");

            // 异步上报且自增广告位转化数据
            callbackService.callbackAndIncrData(order, req -> {
                // 上报返利
                if (StringUtils.isNotBlank(req.getValue().getString("click_id"))) {
                    return callbackService.callbackFanli(req.getKey(), req.getValue());
                }
                // 上报极准
                if (StringUtils.isNotBlank(req.getValue().getString("jcid"))) {
                    return callbackService.callbackJizhun(req.getKey(), req.getValue());
                }
                // 其他情况默认上报成功
                return Pair.of(ConvertUploadStatus.SUCCESS.getStatus(), "");
            });

            convCallbackService.handleAsync(CONVERT_EVENT, PAY, order);

            // 移除监控
            GlobalThreadPool.executorService.submit(() -> {
                Optional.ofNullable(advertService.selectAdvertById(order.getAdvertId())).ifPresent(advert -> {
                    String key = EngineRedisKeyFactory.K049.join(advert.getAdvertiserId());
                    redisCache.deleteObject(key);
                });
            });
        }
    }

    /**
     * 检查广告主转化上限
     */
    private void checkAdvertiserConvLimit() {
        GlobalThreadPool.executorService.submit(() -> {
            try {
                checkManager.checkAdvertiserConvLimit();
            } catch (Exception e) {
                log.error("checkAdvertiserConvLimit error", e);
            }
        });
    }

    /**
     * 根据订单号查询落地页皮肤类型
     *
     * @param order 订单
     * @return 落地页皮肤类型
     */
    private Integer getLandpageSkinType(Order order) {
        if (null == order || null == order.getAdSnapshot()) {
            return null;
        }
        try {
            AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
            if (null != adSnapshot) {
                String originLandpageUrl = adSnapshot.getOriginLandpageUrl();
                Landpage landpage = landpageCacheService.selectLandpageCache(LandpageUtil.extractLpk(originLandpageUrl));
                return null != landpage ? landpage.getSkinType() : null;
            }
        } catch (Exception e) {
            log.error("getLandpageSkinType error, orderId={}", order.getOrderId(), e);
        }
        return null;
    }

    /**
     * 判断本次上报是否落地页转化
     *
     * @param advertiserId 广告主ID
     * @param landpageSkinType 落地页皮肤类型
     * @param status 转化类型
     * @return 是否落地页转化
     */
    private boolean isLandpageClick(Long advertiserId, Integer landpageSkinType, Integer status) {
        // 广告主落地页转化对应的后端转化类型
        Integer lpClickType = mapConfigService.getValue(ADVERTISER_CONV_MAP, advertiserId, Integer.class);

        // 判断是否号卡
        boolean isHaoKa = Objects.equals(LandPageSkinTypeEnum.PHONE_CARD.getType(), landpageSkinType);

        // 支付&&非号卡 || 指定转化类型
        return (null == lpClickType && isPay(status) && !isHaoKa) || (null != lpClickType && Objects.equals(lpClickType, status));
    }
}
