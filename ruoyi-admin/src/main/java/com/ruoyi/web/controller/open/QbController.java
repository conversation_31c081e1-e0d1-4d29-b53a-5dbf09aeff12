package com.ruoyi.web.controller.open;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity;
import com.ruoyi.system.req.open.QbCallbackReq;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.landpage.ExternalCouponRecordService;
import com.ruoyi.system.service.open.MybCouponService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

import static com.ruoyi.common.enums.InnerLogType.LANDPAGE_CLICK;

/**
 * 启奔接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Slf4j
@RestController
@RequestMapping("/open/qb")
public class QbController {

    @Autowired
    private StatService statService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private ExternalCouponRecordService externalCouponRecordService;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private MybCouponService mybCouponService;

    /**
     * 启奔回调
     */
    @CrossOrigin
    @PostMapping("/callback")
    public String callback(HttpServletRequest request, QbCallbackReq req) {
        log.info("启奔回调，param={}", JSON.toJSONString(request.getParameterMap()));

        if (Objects.equals(req.getProType(), 1) && StringUtils.isNotBlank(req.getParam())) {
            RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K032.join(req.getProType(), req.getOrderNo(), req.getParam()), 3600);
            if (lock != null) {
                String orderId = req.getParam();
                JSONObject slotParam = callbackService.getParameterFromCache(orderId);
                statAndGrandCoupon(orderId, slotParam, req);
            }
        }
        return "success";
    }

    /**
     * 状态查询
     */
    @CrossOrigin
    @GetMapping("/queryStatus")
    public Result<Boolean> queryStatus(HttpServletRequest request, String nadkey) {
        log.info("启奔状态查询，param={}", JSON.toJSONString(request.getParameterMap()));

        ExternalCouponRecordEntity record = externalCouponRecordService.selectByOrderId(nadkey);
        if (null == record) {
            return ResultBuilder.success(false);
        }
        return ResultBuilder.success(Objects.equals(record.getCouponStatus(), 1));
    }

    /**
     * 埋点并发放优惠券
     */
    private void statAndGrandCoupon(String orderId, JSONObject slotParam, QbCallbackReq req) {
        // 转化埋点
        statService.innerLogStatByOrderId(LANDPAGE_CLICK, orderId);

        // 发放优惠券
        Order order = orderService.selectByOrderId(orderId);
        JSONObject pageConfig = getPageConfig(order);
        if (null != order && null != pageConfig && null != pageConfig.getInteger("productType")) {
            // 幂等处理
            String redisKey = CrmRedisKeyFactory.K032.join("qb", req.getProType(), req.getOrderNo(), req.getParam());
            RedisLock lock = redisAtomicClient.getLock(redisKey, 3600);
            if (null == lock) {
                return;
            }
            // 根据不同产品发放对应优惠券
            boolean isSuccess = mybCouponService.grandCoupon(slotParam, pageConfig, order);
            if (!isSuccess) {
                lock.unlock();
            }
        }
    }

    private JSONObject getPageConfig(Order order) {
        if (null == order) {
            return null;
        }
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
        if (null == adSnapshot) {
            return null;
        }
        return JSON.parseObject(landpageCacheService.selectLandpagePageConfigCache(LandpageUtil.extractLpk(adSnapshot.getOriginLandpageUrl())));
    }
}
