package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.landpage.LandpageSkinEntity;
import com.ruoyi.system.req.landpage.LandpageSkinReq;
import com.ruoyi.system.service.landpage.LandpageSkinService;
import com.ruoyi.system.vo.landpage.LandpageSkinVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * [CRM后台]落地页皮肤
 *
 * <AUTHOR>
 * @date 2022-02-10
 */
@Slf4j
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/landpageSkin")
public class LandpageSkinController {

    @Autowired
    private LandpageSkinService landpageSkinService;

    /**
     * 皮肤列表
     */
    @GetMapping("/list")
    public Result<List<LandpageSkinVO>> list(LandpageSkinReq req) {
        List<LandpageSkinEntity> list = landpageSkinService.selectList(BeanUtil.copyProperties(req, LandpageSkinEntity.class));
        return ResultBuilder.success(BeanUtil.copyToList(list, LandpageSkinVO.class));

    }

    /**
     * 皮肤信息
     */
    @GetMapping(value = "/{id}")
    public Result<LandpageSkinVO> info(@PathVariable("id") Long id) {
        LandpageSkinEntity skin = landpageSkinService.selectById(id);
        return ResultBuilder.success(BeanUtil.copyProperties(skin, LandpageSkinVO.class));
    }

    /**
     * 新增皮肤
     */
    @Log(title = "落地页皮肤", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody LandpageSkinReq req) {
        if (StringUtils.isBlank(req.getSkinName())) {
            return ResultBuilder.fail("皮肤名称不能为空");
        }
        if (!JSONUtil.isTypeJSON(req.getSkinConfig())) {
            return ResultBuilder.fail("皮肤配置不是标准的JSON");
        }
        int result = landpageSkinService.insert(BeanUtil.copyProperties(req, LandpageSkinEntity.class));
        return ResultBuilder.success(result > 0);
    }

    /**
     * 修改皮肤
     */
    @Log(title = "落地页皮肤", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public Result<Boolean>  update(@RequestBody LandpageSkinReq req) {
        if (null == req.getId()) {
            return ResultBuilder.fail("皮肤ID不能为空");
        }
        if (StringUtils.isBlank(req.getSkinName())) {
            return ResultBuilder.fail("皮肤名称不能为空");
        }
        if (!JSONUtil.isTypeJSON(req.getSkinConfig())) {
            return ResultBuilder.fail("皮肤配置不是标准的JSON");
        }
        LandpageSkinEntity updateSkin = new LandpageSkinEntity();
        updateSkin.setId(req.getId());
        updateSkin.setSkinName(req.getSkinName());
        updateSkin.setThumbnailImage(req.getThumbnailImage());
        updateSkin.setSkinConfig(req.getSkinConfig());
        int result = landpageSkinService.updateById(updateSkin);
       return ResultBuilder.success(result > 0);
    }
}
