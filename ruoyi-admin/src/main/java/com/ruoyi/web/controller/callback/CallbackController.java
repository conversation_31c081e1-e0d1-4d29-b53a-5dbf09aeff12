package com.ruoyi.web.controller.callback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.QwFriendStatusEnum;
import com.ruoyi.system.req.callback.QLCallbackEventParam;
import com.ruoyi.system.service.landpage.LiuziLandpageFormRecordService;
import com.ruoyi.system.util.AesTools;
import com.ruoyi.system.util.Md5Tools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Objects;

/**
 * 回调相关接口
 * <AUTHOR>
 * @date 2022/4/15 4:07 下午
 */
@Slf4j
@RestController
@RequestMapping("callback")
public class CallbackController {

    /**
     * 圈量(简约版)参数
     */
    private static final String ENCODING_AES_KEY = "30a40de286cf4cc284a9bfed2f109a71";
    private static final String TOKEN = "4455b70db2ad4788ba7c35feca64d3e6";

    /**
     * 圈量(SAAS版)参数
     */
    private static final String ENCODING_AES_KEY_V2 = "dba21e794fc74cf7adb42d217f954199";
    private static final String TOKEN_V2 = "ba47f1372f574ab6b1f0a1d52c70053d";

    @Autowired
    private LiuziLandpageFormRecordService liuziLandpageFormRecordService;

    /**
     * 圈量事件回调接口(简约版)
     */
    @CrossOrigin
    @PostMapping("/ql")
    public JSONObject callback(@RequestBody QLCallbackEventParam param) {
        log.info("圈量回调事件,param:{},content:{}", JSON.toJSONString(param), decryptContent(param.getEncodingContent(), ENCODING_AES_KEY));
        JSONObject result = new JSONObject();
        result.put("success", false);

        // 签名校验
        if (!validateSignature(param, TOKEN)) {
            result.put("message", "无效的签名");
            return result;
        }

        try {
            // 解密内容
            JSONObject json = decryptContent(param.getEncodingContent(), ENCODING_AES_KEY);
            if (null == json) {
                result.put("message", "解密失败");
                return result;
            }

            // 好友申请执行成功回调
            if (Objects.equals(json.getString("event_type"), "add.contact.by.phone")) {
                String phone = json.getJSONObject("data").getString("phone");
                log.info("好友申请执行成功回调，phone:{}",phone);
            }
            // 成为好友结果回调
            else if (Objects.equals(json.getString("event_type"), "become.contact")) {
                String phone = json.getJSONObject("data").getString("phone");
                log.info("成为好友结果回调，phone:{}",phone);
                //更新加好友结果状态
                liuziLandpageFormRecordService.updateFriendStatus(phone, QwFriendStatusEnum.BECOME_FRIEND.getStatus());
            }

            result.put("success", true);
            result.put("message", "操作成功");
        } catch (Exception e) {
            result.put("message", "操作失败");
            log.error("圈量回调处理异常, req={}", JSON.toJSONString(param), e);
        }
        log.info("圈量回调返回, signature={}, result={}", param.getSignature(), result.toString());
        return result;
    }

    /**
     * 圈量事件回调接口(SAAS版)
     */
    @CrossOrigin
    @PostMapping("/ql/v2")
    public JSONObject qlCallbackV2(@RequestBody QLCallbackEventParam param) {
        log.info("圈量V2回调事件,param:{},content:{}", JSON.toJSONString(param), decryptContent(param.getEncodingContent(), ENCODING_AES_KEY_V2));
        JSONObject result = new JSONObject();
        result.put("success", false);

        // 签名校验
        if (!validateSignature(param, TOKEN_V2)) {
            result.put("message", "无效的签名");
            return result;
        }

        try {
            // 解密内容
            JSONObject json = decryptContent(param.getEncodingContent(), ENCODING_AES_KEY_V2);
            if (null == json) {
                result.put("message", "解密失败");
                return result;
            }

            // 好友申请执行成功回调
            if (Objects.equals(json.getString("event_type"), "40028")) {
                if (json.getBooleanValue("is_sent")) {
                    String phone = json.getString("ext_user_phone");
                    log.info("好友申请执行成功回调V2，phone:{}", phone);
                }
            }
            // 成为好友结果回调
            else if (Objects.equals(json.getString("event_type"), "20001")) {
                String phone = json.getJSONObject("profile").getString("phone");
                log.info("成为好友结果回调V2，phone:{}",phone);
                //更新加好友结果状态
                liuziLandpageFormRecordService.updateFriendStatus(phone, QwFriendStatusEnum.BECOME_FRIEND.getStatus());
            }

            result.put("success", true);
            result.put("message", "操作成功");
        } catch (Exception e) {
            result.put("message", "操作失败");
            log.error("圈量回调处理异常, req={}", JSON.toJSONString(param), e);
        }
        log.info("圈量V2回调返回, signature={}, result={}", param.getSignature(), result.toString());
        return result;
    }

    /**
     * 签名校验
     *
     * @param req 参数
     * @return 是否校验通过
     */
    private boolean validateSignature(QLCallbackEventParam req, String token) {
        if (null == req.getAppKey() || null == req.getToken() || null == req.getNonce()
                || null == req.getTimestamp() || null == req.getEncodingContent()) {
            return false;
        }
        try {
            String signature = Md5Tools.GenMd5Signature(Arrays.asList(req.getAppKey(), token, req.getNonce(),
                    req.getTimestamp(), req.getEncodingContent()));
            return Objects.equals(signature, req.getSignature());
        } catch (NoSuchAlgorithmException e) {
            return false;
        }
    }

    /**
     * 解密内容
     *
     * @param encodingContent 加密的内容
     * @return 解密后的内容
     */
    private static JSONObject decryptContent(String encodingContent, String encodingAESKey) {
        if (StringUtils.isBlank(encodingContent)) {
            return null;
        }

        String data = AesTools.decryptCBC(encodingContent, encodingAESKey);
        return JSON.parseObject(data);
    }
}
