package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.IdCardAuditApiType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.entity.datashow.CreditCardLandpageFormRecordExcel;
import com.ruoyi.system.entity.landpage.CreditCardLandpageFormRecordEntity;
import com.ruoyi.system.req.datashow.CreditCardLandpageFromRecordReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.common.IdCardService;
import com.ruoyi.system.service.landpage.CreditCardLandpageFormRecordService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.vo.advert.AdvertSelectVO;
import com.ruoyi.system.vo.app.AppSelectVO;
import com.ruoyi.system.vo.datashow.CreditCardLandpageFormRecordVO;
import com.ruoyi.system.vo.landpage.CompanySelectVO;
import com.ruoyi.system.vo.slot.SlotSelectVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.EasyExcelUtils.exportExcel;
import static com.ruoyi.common.utils.ListUtils.mapToList;

/**
 * [CRM后台]信用卡转化记录
 *
 * <AUTHOR>
 * @date 2023-06-02
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/datashow/creditCard/lpForm")
public class CreditCardLandpageFormRecordController extends BaseController {

    @Autowired
    private CreditCardLandpageFormRecordService creditCardLandpageFormRecordService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private IdCardService idCardService;

    /**
     * 广告下拉列表
     */
    @GetMapping("/advertList")
    public Result<List<AdvertSelectVO>> advertList() {
        List<Long> advertIds = creditCardLandpageFormRecordService.selectTotalAdvertIds();
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        return ResultBuilder.success(advertIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(advertId -> new AdvertSelectVO(advertId, advertNameMap.get(advertId))).collect(Collectors.toList()));
    }

    /**
     * 广告下拉列表
     */
    @GetMapping("/advertiserList")
    public Result<List<CompanySelectVO>> advertiserList() {
        List<Long> advertiserIds = creditCardLandpageFormRecordService.selectTotalAdvertiserIds();
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(advertiserIds);
        return ResultBuilder.success(advertiserIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(advertiserId -> new CompanySelectVO(advertiserId, companyNameMap.get(advertiserId))).collect(Collectors.toList()));
    }

    /**
     * 媒体下拉列表
     */
    @GetMapping("/appList")
    public Result<List<AppSelectVO>> appList() {
        List<Long> appIds = creditCardLandpageFormRecordService.selectTotalAppIds();
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        return ResultBuilder.success(appIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(appId -> new AppSelectVO(appId, appNameMap.get(appId))).collect(Collectors.toList()));
    }

    /**
     * 广告位下拉列表
     */
    @GetMapping("/slotList")
    public Result<List<SlotSelectVO>> slotList() {
        List<Long> slotIds = creditCardLandpageFormRecordService.selectTotalSlotIds();
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(slotIds);
        return ResultBuilder.success(slotIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(slotId -> new SlotSelectVO(slotId, slotNameMap.get(slotId))).collect(Collectors.toList()));
    }

    /**
     * 查询表单
     */
    @GetMapping("/list")
    public TableDataInfo<CreditCardLandpageFormRecordVO> list(CreditCardLandpageFromRecordReq req) {
        startPage();
        req.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        List<CreditCardLandpageFormRecordEntity> list = creditCardLandpageFormRecordService.selectList(req);

        // 补充信息
        Map<Long, String> appNameMap = appService.selectAppNameMap(mapToList(list, CreditCardLandpageFormRecordEntity::getAppId));
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(mapToList(list, CreditCardLandpageFormRecordEntity::getSlotId));
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(mapToList(list, CreditCardLandpageFormRecordEntity::getAdvertiserId));

        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            CreditCardLandpageFormRecordVO record = BeanUtil.copyProperties(data, CreditCardLandpageFormRecordVO.class);
            record.setAppName(appNameMap.get(record.getAppId()));
            record.setSlotName(slotNameMap.get(record.getSlotId()));
            record.setAdvertiserName(advertiserNameMap.get(record.getAdvertiserId()));
            record.setIdCard(DesensitizedUtil.idCardNum(idCardService.decrypt(record.getIdCard()), 14, 0));
            record.setPhone(DesensitizedUtil.mobilePhone(record.getPhone()));
            return record;
        }));
    }

    /**
     * 导出表单
     */
    @Log(title = "信用卡表单", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(CreditCardLandpageFromRecordReq req) {
        req.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        List<CreditCardLandpageFormRecordEntity> list = creditCardLandpageFormRecordService.selectList(req);
        Map<Long, String> appNameMap = appService.selectAppNameMap(mapToList(list, CreditCardLandpageFormRecordEntity::getAppId));
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(mapToList(list, CreditCardLandpageFormRecordEntity::getSlotId));
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(mapToList(list, CreditCardLandpageFormRecordEntity::getAdvertiserId));

        // 导出Excel
        List<CreditCardLandpageFormRecordExcel> excelData = list.stream().map(record -> {
            CreditCardLandpageFormRecordExcel data = BeanUtil.copyProperties(record, CreditCardLandpageFormRecordExcel.class);
            data.setAppName(appNameMap.get(record.getAppId()));
            data.setSlotName(slotNameMap.get(record.getSlotId()));
            data.setAdvertiserName(advertiserNameMap.get(record.getAdvertiserId()));
            data.setIdCard(idCardService.decrypt(record.getIdCard()));
            data.setAuditApiTypeStr(IdCardAuditApiType.getDescByType(record.getAuditApiType()));
            return data;
        }).collect(Collectors.toList());
        return AjaxResult.success(exportExcel("信用卡表单", excelData, CreditCardLandpageFormRecordExcel.class));
    }
}
