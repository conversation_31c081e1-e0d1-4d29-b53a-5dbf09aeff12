package com.ruoyi.web.controller.manager.crm.ssp.slot;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Pair;
import com.google.common.collect.Lists;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.req.datashow.UpdateSlotDataReq;
import com.ruoyi.system.req.slot.SlotDailyTaskReq;
import com.ruoyi.system.req.slot.SlotDataUpdateReq;
import com.ruoyi.system.service.datasource.SlotAppDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.slotdata.SlotMonthDataService;
import com.ruoyi.system.vo.slot.SlotDailyTaskVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 广告位运营每日任务Controller
 *
 * <AUTHOR>
 * @date 2022-03-04
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/slot/dailyTask")
public class SlotDailyTaskController extends BaseController {

    @Autowired
    private SlotService slotService;

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotMonthDataService slotMonthDataService;

    @Autowired
    private SlotAppDataService slotAppDataService;

    /**
     * 运营每日任务
     */
    @GetMapping("/list")
    public TableDataInfo list(SlotDailyTaskReq req) {
        SlotData param = new SlotData();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setAppIds(new ArrayList<>());
        param.setIsEditable(1);
        param.setIsEdited(req.getIsEdited());
        param.setSlotIds(new ArrayList<>());
        // 媒体模糊查询
        if (StringUtils.isNotBlank(req.getAppSearch())) {
            List<Long> appIds = appService.selectAppIdsBySearchValue(req.getAppSearch());
            if (CollectionUtils.isEmpty(appIds)) {
                return getDataTable(Collections.emptyList());
            }
            List<Long> slotIds = slotService.selectSlotIdsByAppIds(appIds);
            if (CollectionUtils.isEmpty(slotIds)) {
                return getDataTable(Collections.emptyList());
            }
            param.setSlotIds(slotIds);
        }
        // 广告位模糊查询
        if (StringUtils.isNotBlank(req.getSlotSearch())) {
            List<Long> slotIds = slotService.selectSlotIdsBySearchValue(req.getSlotSearch());
            if (CollectionUtils.isEmpty(slotIds)) {
                return getDataTable(Collections.emptyList());
            }
            param.getSlotIds().addAll(slotIds);
        }

        TableSupport.startPage();
        List<SlotData> list = slotDataService.selectSlotDataList(param);

        List<Long> slotIds = ListUtils.mapToList(list, SlotData::getSlotId);
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(slotIds);
        List<Long> appIds = ListUtils.mapToList(list, SlotData::getAppId);
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);

        // 月账单数据是否生成
        Set<Integer> months = list.stream().map(s -> DateUtils.dateTimeMonth(s.getCurDate())).collect(Collectors.toSet());
        Set<Pair<Long, Integer>> monthDataSet = slotMonthDataService.existBySlotIdsAndMonths(slotIds, Lists.newArrayList(months));

        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            SlotDailyTaskVO taskVO = BeanUtil.copyProperties(data, SlotDailyTaskVO.class);
            taskVO.setSlotName(slotNameMap.get(data.getSlotId()));
            taskVO.setAppName(appNameMap.get(data.getAppId()));
            // 若生成月账单了，修改按钮隐藏
            taskVO.setCanEdit(monthDataSet.contains(Pair.of(data.getSlotId(), DateUtils.dateTimeMonth(data.getCurDate()))) ? 0 : 1);
            return taskVO;
        }));
    }

    /**
     * 更新运营每日任务
     */
    @Log(title = "运营每日任务修改", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public Result update(@RequestBody @Validated UpdateSlotDataReq req) {
        SlotDataUpdateReq updateReq = BeanUtil.copyProperties(req, SlotDataUpdateReq.class);
        updateReq.setSource(1);
        slotAppDataService.updateSlotAppData(updateReq);
        return ResultBuilder.success();
    }
}
