package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.landpage.LandpageDataExcelBo;
import com.ruoyi.system.entity.datashow.LandpageDayData;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.req.landpage.LandpageDataReq;
import com.ruoyi.system.service.datasource.LandpageDayDataService;
import com.ruoyi.system.service.landpage.LandpageLibraryService;
import com.ruoyi.system.vo.landpage.LandpageDataVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 落地页数据Controller
 *
 * <AUTHOR>
 * @date 2022-09-22
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/landpage/data")
public class LandpageDataController extends BaseController {

    @Autowired
    private LandpageDayDataService landpageDayDataService;

    @Autowired
    private LandpageLibraryService landpageLibraryService;

    /**
     * 查询落地页数据列表
     */
    @GetMapping("/list")
    public TableDataInfo<LandpageDataVO> list(LandpageDataReq req) {
        Map<String, Landpage> landpageMap = landpageLibraryService.selectKeyMap();

        // 模糊查询
        if (StringUtils.isNotBlank(req.getLandpageSearch())) {
            String landpageSearch = StrUtil.trim(req.getLandpageSearch());
            req.setLandpageKeyList(landpageMap.values().stream().filter(landpage ->
                    StrUtil.containsIgnoreCase(landpage.getName(), landpageSearch)
                    || StrUtil.containsIgnoreCase(landpage.getUrl(), landpageSearch)
                    || Objects.equals(landpage.getId(), NumberUtils.parseLong(landpageSearch))
            ).map(Landpage::getKey).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(req.getLandpageKeyList())) {
                return getDataTable(Collections.emptyList());
            }
        }

        startPage();
        // 限制了只展示落地页曝光PV>0的数据
        List<LandpageDayData> list = landpageDayDataService.selectList(req);
        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            LandpageDataVO vo = BeanUtil.copyProperties(data, LandpageDataVO.class);
            vo.setCvrPv(NumberUtils.calculatePercent(data.getLpClickPv(), data.getLpExposurePv()));
            vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getLpExposureUv()));
            Optional.ofNullable(landpageMap.get(data.getLandpageKey())).ifPresent(landpage -> {
                vo.setLandpageName(landpage.getName());
                vo.setLandpageUrl(landpage.getUrl());
            });
            return vo;
        }));
    }

    /**
     * 导出落地页数据
     */
    @Log(title = "落地页数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LandpageDataReq req) {
        List<LandpageDayData> list = null;
        Map<String, Landpage> landpageMap = landpageLibraryService.selectKeyMap();

        // 模糊查询
        if (StringUtils.isNotBlank(req.getLandpageSearch())) {
            String landpageSearch = StrUtil.trim(req.getLandpageSearch());
            req.setLandpageKeyList(landpageMap.values().stream().filter(landpage ->
                    StrUtil.containsIgnoreCase(landpage.getName(), landpageSearch)
                            || StrUtil.containsIgnoreCase(landpage.getUrl(), landpageSearch)
                            || Objects.equals(landpage.getId(), NumberUtils.parseLong(landpageSearch))
            ).map(Landpage::getKey).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(req.getLandpageKeyList())) {
                list = Collections.emptyList();
            }
        }
        // 限制了只展示落地页曝光PV>0的数据
        if (null == list) {
            list = landpageDayDataService.selectList(req);
        }

        List<LandpageDataExcelBo> excels = list.stream().map(data -> {
            LandpageDataExcelBo vo = BeanUtil.copyProperties(data, LandpageDataExcelBo.class);
            vo.setCvrPv(NumberUtils.calculatePercent(data.getLpClickPv(), data.getLpExposurePv()));
            vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getLpExposureUv()));
            Optional.ofNullable(landpageMap.get(data.getLandpageKey())).ifPresent(landpage -> {
                vo.setLandpageName(landpage.getName());
                vo.setLandpageUrl(landpage.getUrl());
            });
            return vo;
        }).collect(Collectors.toList());
        String fileName = UUID.randomUUID().toString() + "_落地页数据.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, LandpageDataExcelBo.class).sheet("落地页数据").doWrite(excels);
        return AjaxResult.success(fileName);
    }
}
