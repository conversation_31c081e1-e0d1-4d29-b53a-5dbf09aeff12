package com.ruoyi.web.controller.manager.crm.dsp.advertiser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Joiner;
import com.google.common.collect.Sets;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.advertiser.AdvertiserConsumeType;
import com.ruoyi.common.enums.contract.ContractStatusEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.account.OaStaffBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity;
import com.ruoyi.system.entity.contract.ContractEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.account.OaStaffManger;
import com.ruoyi.system.req.advertiser.AdvertiserBudgetReq;
import com.ruoyi.system.req.advertiser.AdvertiserConsumeTypeReq;
import com.ruoyi.system.req.advertiser.AdvertiserReq;
import com.ruoyi.system.req.advertiser.AdvertiserTagReq;
import com.ruoyi.system.service.advertiser.AdvertiserBudgetService;
import com.ruoyi.system.service.advertiser.AdvertiserQualificationService;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.contract.ContractService;
import com.ruoyi.system.service.datasource.AdvertiserConsumeDataService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.account.AdvertiserVO;
import com.ruoyi.system.vo.advertiser.AdvertiserSelectVO;
import com.ruoyi.system.vo.advertiser.qualification.AdvertiserQualificationVO;
import com.ruoyi.system.vo.landpage.CompanySelectVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.OaConstants.PERMISSION_ASSIGN_MANAGER;
import static com.ruoyi.common.enums.account.AccountMainType.ADVERTISER;
import static com.ruoyi.common.enums.account.AccountMainType.AGENT;
import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.account.AccountRelationType.ADVERT_AGENT;
import static com.ruoyi.common.enums.account.AccountRelationType.BD_MANAGER;
import static com.ruoyi.common.enums.account.AccountRelationType.OPERATION_MANAGER;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * 广告主Controller
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@RestController
@RequestMapping("/manager/advertiser")
public class AdvertiserController extends BaseController {

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    @Autowired
    private AdvertiserQualificationService advertiserQualificationService;

    @Autowired
    private AccountRelationService accountRelationService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private ContractService contractService;

    @Autowired
    private OaStaffManger oaStaffManger;

    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private AdvertiserBudgetService advertiserBudgetService;

    @Autowired
    private AdvertiserConsumeDataService advertiserConsumeDataService;

    @Autowired
    private AdvertService advertService;

    /**
     * 广告主下拉列表(通用)
     */
    @GetMapping("/advertiserList")
    public Result<List<CompanySelectVO>> advertiserList() {
        List<Account> advertisers = advertiserService.selectTotalAdvertiserList();

        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return ResultBuilder.success();
            }
            advertisers = advertisers.stream().filter(account -> permission.getValues().contains(account.getId())).collect(Collectors.toList());
        }
        return ResultBuilder.success(advertisers.stream().map(advertiser -> new CompanySelectVO(advertiser.getId(), advertiser.getCompanyName())).collect(Collectors.toList()));
    }

    /**
     * 代理商下拉列表(通用)
     */
    @GetMapping("/agentList")
    public Result<List<CompanySelectVO>> agentList() {
        List<Account> agents = agentService.selectTotalAgentList();

        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return ResultBuilder.success();
            }
            agents = agents.stream().filter(account -> permission.getValues().contains(account.getId())).collect(Collectors.toList());
        }
        return ResultBuilder.success(agents.stream().map(agent -> new CompanySelectVO(agent.getId(), agent.getCompanyName())).collect(Collectors.toList()));
    }

    /**
     * 查询广告主列表
     */
    @GetMapping("/listTotal")
    public Result<List<AdvertiserSelectVO>> listTotal() {
        List<Account> advertisers = advertiserService.selectTotalAdvertiserList();
        Map<Long, List<String>> tagMap = advertiserTagService.getMap();
        String referer = StringUtils.defaultString(ServletUtils.getRequest().getHeader("referer"));
        Map<Long, String> agentNameMap = new HashMap<>(advertisers.size());

        // 新建/复制广告和充值记录的广告主列表限制权限
        if (StrUtil.containsAnyIgnoreCase(referer, "dsp/advert/list", "dsp/financing/recharge")) {
            // 数据权限控制
            DataPermissionBo permission = dataPermissionManager.selectAccount();
            if (hasPartialPermission(permission.getType())) {
                if (CollectionUtils.isEmpty(permission.getValues())) {
                    return ResultBuilder.success();
                }
                advertisers = advertisers.stream().filter(advertiser -> permission.getValues().contains(advertiser.getId())).collect(Collectors.toList());
            }
            // 查询代理商
            if (StrUtil.containsAnyIgnoreCase(referer, "dsp/advert/list")) {
                agentNameMap.putAll(agentService.selectAgentNameMap(ListUtils.mapToList(advertisers, Account::getId)));
            }
        }

        return ResultBuilder.success(advertisers.stream().map(advertiser -> {
            AdvertiserSelectVO vo = BeanUtil.copyProperties(advertiser, AdvertiserSelectVO.class);
            List<String> tags = tagMap.get(advertiser.getId());
            // 广告主充值时补充标签，后面再优化
            if (CollectionUtils.isNotEmpty(tags) && referer.contains("recharge")) {
                vo.setCompanyName(vo.getCompanyName() + "（" + Joiner.on(",").join(tags) + "）");
            }
            vo.setAgentName(agentNameMap.get(advertiser.getId()));
            return vo;
        }).collect(Collectors.toList()));
    }

    /**
     * 查询广告主/列表列表
     */
    @GetMapping("/listForRecharge")
    public Result<List<AdvertiserSelectVO>> listForRecharge() {
        List<Account> accounts = accountService.selectAgentAdvertiserList(new Account());
        Map<Long, List<String>> tagMap = advertiserTagService.getMap();

        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return ResultBuilder.success();
            }
            accounts = accounts.stream().filter(account -> permission.getValues().contains(account.getId())).collect(Collectors.toList());
        }
        // 过滤掉代理商的子广告主
        Set<Long> subAdvertiserIds = accountRelationService.selectDestAccountIdByRelationType(ADVERT_AGENT.getType());

        return ResultBuilder.success(accounts.stream()
                .filter(account -> !subAdvertiserIds.contains(account.getId()))
                .map(account -> {
                    AdvertiserSelectVO vo = BeanUtil.copyProperties(account, AdvertiserSelectVO.class);
                    List<String> tags = tagMap.get(account.getId());
                    if (CollectionUtils.isNotEmpty(tags)) {
                        vo.setCompanyName(vo.getCompanyName() + "（" + Joiner.on(",").join(tags) + "）");
                    }
                    return vo;
                }).collect(Collectors.toList()));
    }

    /**
     * 查询已对接的广告主列表列表
     */
    @GetMapping("/listCallbackTotal")
    public Result<List<AdvertiserSelectVO>> listCallbackTotal() {
        List<Account> advertisers = advertiserService.selectTotalAdvertiserList();
        Map<Long, List<String>> tagMap = advertiserTagService.getMap();
        return ResultBuilder.success(advertisers.stream().filter(advertiser -> {
            if (StringUtils.isBlank(advertiser.getExtInfo())) {
                return false;
            }
            AccountExtInfo extInfo = JSON.parseObject(advertiser.getExtInfo(), AccountExtInfo.class);
            return null != extInfo && StringUtils.isNotBlank(extInfo.getLpCallbackUrl())
                    && CollectionUtils.isNotEmpty(tagMap.get(advertiser.getId()));
        }).map(advertiser -> {
            AdvertiserSelectVO vo = BeanUtil.copyProperties(advertiser, AdvertiserSelectVO.class);
            List<String> tags = tagMap.get(advertiser.getId());
            if (CollectionUtils.isNotEmpty(tags)) {
                vo.setCompanyName(vo.getCompanyName() + "（" + Joiner.on(",").join(tags) + "）");
            }
            return vo;
        }).collect(Collectors.toList()));
    }

    /**
     * 账号管理列表
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasCrmPermi()")
    public TableDataInfo<AdvertiserVO> list(AdvertiserReq req) {
        Account param = new Account();
        param.setStartDate(req.getStartDate());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        param.setAdvertiserIds(req.getAdvertiserIds());
        param.setAgentIds(req.getAgentIds());
        param.setEmail(req.getEmail());

        // 代理商查询
        if (CollectionUtils.isNotEmpty(req.getAgentIds())) {
            List<Long> advertiserIds = agentService.selectAdvertiserIdsByAgentIds(req.getAgentIds());
            if (CollectionUtils.isNotEmpty(advertiserIds)) {
                param.setAdvertiserIds(mergeParamIds(param.getAdvertiserIds(), advertiserIds));
            }
        }
        // 负责人查询
        if (CollectionUtils.isNotEmpty(req.getManagerIds())) {
            List<Long> accountIds = accountRelationService.selectBySrcAccountIds(req.getManagerIds());
            param.setAdvertiserIds(mergeParamIds(param.getAdvertiserIds(), accountIds));
            param.setAgentIds(mergeParamIds(param.getAgentIds(), accountIds));
            if (CollectionUtils.isEmpty(param.getAdvertiserIds()) && CollectionUtils.isEmpty(param.getAgentIds())) {
                return getDataTable(Collections.emptyList());
            }
        }
        //合同状态条件查询
        if(NumberUtils.isNonNullAndGtZero(req.getContractStatus())){
            List<Long> accountIds = contractService.selectAccountIdsByLatestContractStatus(req.getContractStatus());
            param.setAdvertiserIds(mergeParamIds(param.getAdvertiserIds(), accountIds));
            param.setAgentIds(mergeParamIds(param.getAgentIds(), accountIds));
            if (CollectionUtils.isEmpty(param.getAdvertiserIds()) && CollectionUtils.isEmpty(param.getAgentIds())) {
                return getDataTable(Collections.emptyList());
            }
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            param.setAdvertiserIds(mergeParamIds(param.getAdvertiserIds(), permission.getValues()));
            param.setAgentIds(mergeParamIds(param.getAgentIds(), permission.getValues()));
            if (CollectionUtils.isEmpty(param.getAdvertiserIds()) && CollectionUtils.isEmpty(param.getAgentIds())) {
                return getDataTable(Collections.emptyList());
            }
        }
        // 账号类型筛选
        if (null != req.getAccountType()) {
            if (req.getAccountType() == 1) {
                param.setMainType(ADVERTISER.getType());
                param.setExcludeIds(new ArrayList<>(accountRelationService.selectDestAccountIdByRelationType(ADVERT_AGENT.getType())));
            } else if (req.getAccountType() == 2) {
                param.setMainType(AGENT.getType());
            } else if (req.getAccountType() == 3) {
                // 子广告主查询
                List<Long> subAdvertiserIds = new ArrayList<>(accountRelationService.selectDestAccountIdByRelationType(ADVERT_AGENT.getType()));
                param.setAdvertiserIds(mergeParamIds(param.getAdvertiserIds(), subAdvertiserIds));
                if (CollectionUtils.isEmpty(param.getAdvertiserIds())) {
                    return getDataTable(Collections.emptyList());
                }
            }
        }
        // 资质审核状态筛选
        if (null != req.getQualificationAuditStatus()) {
            Map<Long, Integer> qualificationAuditStatusMap = advertiserQualificationService.selectAdvertiserAuditStatusMap(param.getAdvertiserIds());
            List<Long> tmpAdvertiserIds = qualificationAuditStatusMap.entrySet().stream().filter(e -> Objects.equals(e.getValue(), req.getQualificationAuditStatus())).map(Map.Entry::getKey).collect(Collectors.toList());
            param.setAdvertiserIds(mergeParamIds(param.getAdvertiserIds(), tmpAdvertiserIds));
            if (CollectionUtils.isEmpty(param.getAdvertiserIds())) {
                return getDataTable(Collections.emptyList());
            }
        }

        startPage();
        List<Account> accounts = accountService.selectAgentAdvertiserList(param);
        if (CollectionUtils.isEmpty(accounts)) {
            return getDataTable(Collections.emptyList());
        }

        List<Long> accountIds = accounts.stream().map(Account::getId).collect(Collectors.toList());

        // 广告主ID-代理商映射
        Map<Long, Account> agentMap = agentService.selectAgentMap(accountIds);
        // 广告主标签
        Map<Long, List<String>> tagMap = advertiserTagService.getMap();
        // 资质
        Map<Long, List<AdvertiserQualificationEntity>> qualificationEntityMap = advertiserQualificationService.selectMapByAccountIds(accountIds);
        // 行业
        Map<Long, List<String>> industryMap = advertiserQualificationService.selectIndustryMapByAccountIds(accountIds);
        // 广告主ID-负责人映射
        Map<Long, String> managerNameMap = accountService.selectManagerMap(accountIds);
        Map<Long, Map<Integer, List<Long>>> managerMap = accountRelationService.selectMapByDestAccountIds(accountIds);
        //查询最新的合同状态
        List<ContractEntity> contractEntities = contractService.selectLatestContractByAccountIds(accountIds);
        Map<Long, ContractEntity> contractEntityMap = contractEntities.stream().collect(Collectors.toMap(ContractEntity::getAccountId, Function.identity()));
        // 当前用户信息
        LoginUser user = SecurityUtils.getLoginUser();
        OaStaffBo staff = oaStaffManger.selectByEmail(user.getEmail());
        // 指派负责人权限
        Integer canAssign = user.isAdmin() || staff.getOaPermissionKeys().contains(PERMISSION_ASSIGN_MANAGER) ? 1 : 0;
        // 广告主预算
        Map<Long, Long> advertiserBudgetMap = advertiserBudgetService.selectBudgetMapByAccountIds(accountIds);
        Map<Long, Long> advertiserConsumeMap = advertiserConsumeDataService.selectConsumeMapByAdvertiserIdAndDate(accountIds, DateUtil.beginOfDay(new Date()));

        // 构造返回列表
        return getDataTable(PageInfoUtils.dto2Vo(accounts, account -> {
            Long accountId = account.getId();

            AdvertiserVO advertiserVO = new AdvertiserVO();
            advertiserVO.setId(accountId);
            advertiserVO.setMainType(account.getMainType());
            advertiserVO.setEmail(account.getEmail());
            if (isAdvertiser(account.getMainType())) {
                advertiserVO.setAdvertiserName(account.getCompanyName());
                // 子广告主完善代理商ID和名称
                Optional.ofNullable(agentMap.get(accountId)).ifPresent(agent -> {
                    advertiserVO.setAgentId(agent.getId());
                    advertiserVO.setAgentName(agent.getCompanyName());
                });
            } else {
                advertiserVO.setAgentName(account.getCompanyName());
            }
            advertiserVO.setStatus(account.getStatus());
            advertiserVO.setGmtCreate(account.getGmtCreate());

            AccountExtInfo extInfo = JSON.parseObject(account.getExtInfo(), AccountExtInfo.class);
            if (null != extInfo) {
                // 广告主公钥秘钥
                advertiserVO.setAccessKey(extInfo.getAccessKey());
                advertiserVO.setSecretKey(extInfo.getSecretKey());
                // 广告主结算类型
                advertiserVO.setConsumeType(extInfo.getConsumeType());
            }
            // 广告主标签
            advertiserVO.setTags(tagMap.getOrDefault(accountId, Collections.emptyList()));
            if (null == advertiserVO.getConsumeType()) {
                advertiserVO.setConsumeType(AdvertiserConsumeType.CPC.getType());
                advertiserVO.getTags().remove("CPC结算");
            }
            // 资质
            if(qualificationEntityMap.containsKey(accountId)){
                List<AdvertiserQualificationEntity> qualificationList = qualificationEntityMap.get(accountId);
                List<AdvertiserQualificationVO> qualificationVOS = qualificationList.stream().map(qualification ->{
                    AdvertiserQualificationVO qualificationVO = BeanUtil.copyProperties(qualification, AdvertiserQualificationVO.class);
                    qualificationVO.setQualificationImgs(JSONArray.parseArray(qualification.getQualificationImg(),String.class));
                    return qualificationVO;
                }).collect(Collectors.toList());
                advertiserVO.setQualificationList(qualificationVOS);
            }
            // 行业
            advertiserVO.setIndustryList(industryMap.get(accountId));
            // 负责人
            Map<Integer, List<Long>> map = managerMap.getOrDefault(accountId, Collections.emptyMap());
            advertiserVO.setBdManagerIds(map.getOrDefault(BD_MANAGER.getType(),Collections.emptyList()));
            advertiserVO.setOperationManagerIds(map.getOrDefault(OPERATION_MANAGER.getType(),Collections.emptyList()));
            advertiserVO.setManagerName(managerNameMap.getOrDefault(accountId, ""));
            ContractEntity contractEntity = contractEntityMap.get(accountId);
            if(Objects.nonNull(contractEntity)){
                advertiserVO.setContractStatus(ContractStatusEnum.getContractStatusByDate(contractEntity.getEndDate()));
                if(Objects.equals(advertiserVO.getContractStatus(),ContractStatusEnum.ENDING.getStatus())){
                    advertiserVO.setDateDiff((int)DateUtil.between(new Date(), contractEntity.getEndDate(), DateUnit.DAY));
                }
            }
            // 指派负责人权限
            advertiserVO.setCanAssign(canAssign);
            // 日预算
            advertiserVO.setBudget(advertiserBudgetMap.get(accountId));
            advertiserVO.setConsume(advertiserConsumeMap.getOrDefault(accountId, 0L));

            return advertiserVO;
        }));
    }

    /**
     * 设置广告主标签
     */
    @PostMapping("/updateTag")
    @Log(title = "广告主标签", businessType = BusinessType.UPDATE)
    public AjaxResult updateTag(@RequestBody @Validated AdvertiserTagReq req) {
        boolean result = advertiserTagService.update(req.getAdvertiserId(), Sets.newHashSet(req.getTags()));
        if (result) {
            refreshCacheService.sendRefreshAdvertiserCacheMsg(req.getAdvertiserId());
        }
        return result ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 设置广告主结算类型
     */
    @PostMapping("/updateConsumeType")
    @Log(title = "广告主结算类型", businessType = BusinessType.UPDATE)
    public AjaxResult updateConsumeType(@RequestBody @Validated AdvertiserConsumeTypeReq req) {
        boolean result = advertiserService.updateConsumeType(req.getAdvertiserId(), req.getConsumeType());
        if (result) {
            refreshCacheService.sendRefreshAdvertiserCacheMsg(req.getAdvertiserId());
        }
        return result ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 设置广告主预算
     */
    @PostMapping("/updateAdvertiserBudget")
    @Log(title = "广告主预算", businessType = BusinessType.UPDATE)
    public Result<Void> updateAdvertiserBudget(@RequestBody @Validated AdvertiserBudgetReq req) {
        if (null != req.getBudget() && req.getBudget() < 0) {
            return ResultBuilder.fail("预算不能为负数");
        }
        Account account = accountService.selectAccountById(req.getAdvertiserId());
        if (null == account || !isAdvertiser(account.getMainType())) {
            return ResultBuilder.fail("未查询到该广告主");
        }
        // 更新预算
        advertiserBudgetService.updateAdvertiserBudget(req.getAdvertiserId(), req.getBudget());
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertiserCacheMsg(req.getAdvertiserId());
        advertService.sendCheckBudgetMsgByAdvertiserId(req.getAdvertiserId());
        return ResultBuilder.success();
    }
}
