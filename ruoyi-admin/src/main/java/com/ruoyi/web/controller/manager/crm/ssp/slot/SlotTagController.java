package com.ruoyi.web.controller.manager.crm.ssp.slot;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.system.entity.tagmanager.TagManagerEntity;
import com.ruoyi.system.req.slot.SlotTagUpdateReq;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.slot.SlotTagRelationService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import com.ruoyi.system.vo.tagmanager.TagListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * [CRM后台]广告位标签
 *
 * <AUTHOR>
 * @date 2023/5/9
 */
@Slf4j
@RestController
@RequestMapping("/crm/ssp/slot/tag")
public class SlotTagController {

    @Autowired
    private SlotTagRelationService slotTagRelationService;

    @Autowired
    private TagManagerService tagManagerService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    /**
     * 广告位标签列表
     */
    @GetMapping("getSlotTagList")
    public Result<List<TagListVO>> getSlotTagList(@Validated @NotNull(message = "广告位ID不能为空") Long slotId) {
        List<Long> subTagIds = slotTagRelationService.selectTagIdsBySlotId(slotId);
        List<TagManagerEntity> subTags = tagManagerService.selectByIds(subTagIds);
        Map<Long, List<TagManagerEntity>> tagMap = subTags.stream().sorted(Comparator.comparing(TagManagerEntity::getTagSort)).collect(Collectors.groupingBy(TagManagerEntity::getParentId));
        List<Long> parentIds = subTags.stream().map(TagManagerEntity::getParentId).collect(Collectors.toList());
        List<TagManagerEntity> parentTags = tagManagerService.selectByIds(parentIds);
        List<TagListVO> result = parentTags.stream().map(parent -> {
            TagListVO vo = BeanUtil.copyProperties(parent, TagListVO.class);
            List<TagManagerEntity> sub = tagMap.get(parent.getId());
            List<TagListVO> tagListVOS = BeanUtil.copyToList(sub, TagListVO.class);
            vo.setSubTags(tagListVOS);
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(result);
    }

    /**
     * 更新媒体标签列表
     */
    @PostMapping("updateSlotTagList")
    public Result<Boolean> updateSlotTagList(@RequestBody SlotTagUpdateReq req) {
        if (null == req.getSlotId()) {
            return ResultBuilder.fail("广告位ID不能为空");
        }
        Boolean result = slotTagRelationService.updateTags(req.getSlotId(), req.getTagIds());
        refreshCacheService.sendRefreshSlotCacheMsg(req.getSlotId());
        return ResultBuilder.success(result);
    }
}
