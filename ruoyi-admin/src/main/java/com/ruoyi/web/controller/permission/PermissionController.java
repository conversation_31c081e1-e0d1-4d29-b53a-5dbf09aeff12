package com.ruoyi.web.controller.permission;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.oa.post.PostEntity;
import com.ruoyi.system.entity.oa.staff.StaffInfoEntity;
import com.ruoyi.system.entity.oa.user.UserEntity;
import com.ruoyi.system.entity.permission.PermissionEntity;
import com.ruoyi.system.entity.permission.PostPermissionRelationEntity;
import com.ruoyi.system.req.permission.PermissionPostInfoParam;
import com.ruoyi.system.req.permission.PermissionPostParam;
import com.ruoyi.system.req.permission.UpdatePostPermissionParam;
import com.ruoyi.system.service.oa.department.DepartmentService;
import com.ruoyi.system.service.oa.post.PostService;
import com.ruoyi.system.service.oa.staff.StaffInfoService;
import com.ruoyi.system.service.oa.user.UserService;
import com.ruoyi.system.service.permission.SspPermissionService;
import com.ruoyi.system.service.permission.PostPermissionRelationService;
import com.ruoyi.system.vo.permission.PermissionPostInfoVO;
import com.ruoyi.system.vo.permission.PermissionPostListVO;
import com.ruoyi.system.vo.permission.PermissionVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 权限controller
 *
 * <AUTHOR>
 * @date 2022/6/23 5:05 下午
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("permission")
public class PermissionController {

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private PostService postService;

    @Autowired
    private UserService userService;

    @Autowired
    private StaffInfoService staffInfoService;

    @Autowired
    private SspPermissionService sspPermissionService;

    @Autowired
    private PostPermissionRelationService postPermissionRelationService;

    /**
     * 查询所有职位列表
     *
     * @return 职位列表
     */
    @RequestMapping("postList")
    public Result<List<PermissionPostListVO>> postList(PermissionPostParam param) {
        List<Long> postIds = new ArrayList<>();
        //有筛选条件时的处理方式
        if (StringUtils.isNotBlank(param.getEmail())) {
            //查询用户所在的职位id
            List<UserEntity> userEntities = userService.selectByLikeEmail(param.getEmail());
            if (CollectionUtils.isEmpty(userEntities)) {
                return ResultBuilder.success(Collections.emptyList());
            }
            List<Long> userIds = userEntities.stream().map(UserEntity::getId).collect(Collectors.toList());
            List<StaffInfoEntity> staffInfoEntities = staffInfoService.selectByUserIds(userIds);
            List<Long> selectPostIds = staffInfoEntities.stream().map(StaffInfoEntity::getPostId).collect(Collectors.toList());
            postIds.addAll(selectPostIds);
        }
        if (StringUtils.isNotBlank(param.getPostName())) {
            List<PostEntity> postEntities = postService.selectByPostName(param.getPostName());
            if (CollectionUtils.isEmpty(postEntities)) {
                return ResultBuilder.success(Collections.emptyList());
            }
            List<Long> tempPostIds = postEntities.stream().map(PostEntity::getId).collect(Collectors.toList());
            //取职位id的交集
            if (CollectionUtils.isNotEmpty(postIds)) {
                postIds = postIds.stream().filter(tempPostIds::contains).collect(Collectors.toList());
            }else{
                postIds = tempPostIds;
            }
            if (CollectionUtils.isEmpty(postIds)) {
                return ResultBuilder.success(Collections.emptyList());
            }
        }
        List<PostEntity> postEntities;
        if (CollectionUtils.isEmpty(postIds)) {
            //查询所有职位
            postEntities = postService.selectAllPost();
        } else {
            //根据筛选条件查询职位列表
            postEntities = postService.selectPostListByIds(postIds);
        }

        Map<Long, List<PostEntity>> departmentMap = postEntities.stream().collect(Collectors.groupingBy(PostEntity::getDepartmentId));
        // 查询部门ID-部门名称映射
        Map<Long, String> departmentNameMap = departmentService.selectDepartmentNameMap(Lists.newArrayList(departmentMap.keySet()));
        // 查询职位ID-员工数量映射
        Map<Long, Integer> postStaffNumMap = staffInfoService.selectPostStaffNumMap(ListUtils.mapToList(postEntities, PostEntity::getId));
        // 构建返回结果
        List<PermissionPostListVO> result = new ArrayList<>();
        departmentMap.forEach((departmentId, postList) -> {
            result.addAll(postList.stream().map(post -> {
                PermissionPostListVO permissionPostListVO = new PermissionPostListVO();
                permissionPostListVO.setPostId(post.getId());
                permissionPostListVO.setPostName(post.getPostName());
                permissionPostListVO.setDepartmentName(departmentNameMap.get(departmentId));
                permissionPostListVO.setStaffNum(postStaffNumMap.getOrDefault(post.getId(), 0));
                return permissionPostListVO;
            }).collect(Collectors.toList()));
        });
        return ResultBuilder.success(result);
    }

    /**
     * 查询所有权限
     *
     * @return 权限列表
     */
    @GetMapping("getAllPermission")
    public Result<List<PermissionVO>> getAllPermission() {
        List<PermissionEntity> permissionEntities = sspPermissionService.selectAllPermission();

        //模块分组
        Map<Long, List<PermissionEntity>> permissionGroupMap = permissionEntities.stream().collect(Collectors.groupingBy(PermissionEntity::getParentId));

        //系统权限
        List<PermissionVO> permissionList = permissionGroupMap.get(0L).stream().map(permission -> {
            PermissionVO vo = BeanUtil.copyProperties(permission, PermissionVO.class);
            //模块权限列表
            List<PermissionEntity> modelPermissionList = permissionGroupMap.get(permission.getId());
            List<PermissionVO> modelPermissionVo = modelPermissionList.stream().map(modelPermission -> {
                PermissionVO modelVo = BeanUtil.copyProperties(modelPermission, PermissionVO.class);
                List<PermissionEntity> subPermissionList = permissionGroupMap.get(modelPermission.getId());
                List<PermissionVO> subPermissionVo = BeanUtil.copyToList(subPermissionList, PermissionVO.class);
                modelVo.setSubPermissionList(subPermissionVo);
                return modelVo;
            }).collect(Collectors.toList());
            vo.setSubPermissionList(modelPermissionVo);
            return vo;
        }).collect(Collectors.toList());
        return ResultBuilder.success(permissionList);
    }

    /**
     * 职位权限详情
     *
     * @param param 请求参数
     * @return 职位权限详情
     */
    @GetMapping("getPermissionByPostId")
    public Result<PermissionPostInfoVO> getPermissionByPostId(@Validated PermissionPostInfoParam param){
        // 查询职位
        PostEntity postEntity = postService.selectById(param.getPostId());
        if(Objects.isNull(postEntity)){
            return ResultBuilder.fail(ErrorCode.E150001);
        }
        // 查询职位对应的权限
        List<Long> permissionList = postPermissionRelationService.selectPermissionIdsByPostId(param.getPostId());

        PermissionPostInfoVO vo = new PermissionPostInfoVO();
        vo.setPostName(postEntity.getPostName());
        vo.setPermissionIds(permissionList);
        return ResultBuilder.success(vo);
    }

    /**
     * 更新职位权限
     *
     * @param param 请求参数
     * @return 是否更新成功
     */
    @PostMapping("updatePostPermission")
    public Result<Boolean> updatePostPermission(@RequestBody @Validated UpdatePostPermissionParam param) {
        // 查询已有权限
        List<Long> existPermissionIds = postPermissionRelationService.selectPermissionIdsByPostId(param.getPostId());

        // 查询系统下的所有权限
        List<PermissionEntity> permissions = sspPermissionService.selectAllPermission();
        Map<Long, List<PermissionEntity>> permissionGroupMap = permissions.stream().collect(Collectors.groupingBy(PermissionEntity::getParentId));
        List<Long> permissionIds = permissionGroupMap.get(param.getParentId()).stream()
                .flatMap(s -> permissionGroupMap.get(s.getId()).stream()).map(PermissionEntity::getId).collect(Collectors.toList());

        // 计算保留的权限
        List<Long> updatePermissionIds = new ArrayList<>(existPermissionIds);
        updatePermissionIds.removeAll(permissionIds);
        Optional.ofNullable(param.getPermissionIds()).ifPresent(updatePermissionIds::addAll);

        // 更新权限
        PostPermissionRelationEntity entity = new PostPermissionRelationEntity();
        entity.setOaPostId(param.getPostId());
        entity.setPermissionIds(JSON.toJSONString(updatePermissionIds));
        return ResultBuilder.success(postPermissionRelationService.updateOrInsertPermissionByPostId(entity));
    }
}
