package com.ruoyi.web.controller.manager.crm.dsp.finance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserBalanceExcelBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserBalanceStatisticBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserRechargeExcelBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserBalanceEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.req.advertiser.finance.AdvertiserBalanceListReq;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.advertiser.finance.AdvertiserBalanceBaseInfoVO;
import com.ruoyi.system.vo.advertiser.finance.AdvertiserBalanceStatisticVO;
import com.ruoyi.system.vo.advertiser.finance.AdvertiserBalanceVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * 广告主余额Controller
 *
 * <AUTHOR>
 * @date 2022/3/21 1:55 下午
 */
@Validated
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/fiance/balance")
@RestController
public class AdvertiserBalanceController extends BaseController {

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    /**
     * 余额列表
     */
    @GetMapping("/list")
    public TableDataInfo<AdvertiserBalanceVO> list(AdvertiserBalanceListReq req) {
        // 参数处理
        List<Long> accountIds = handleQueryParam(req);
        if (CollUtil.contains(accountIds, -1L)) {
            return getDataTable(Collections.emptyList());
        }
        // 查询数据
        TableSupport.startPage();
        List<AdvertiserBalanceEntity> list =  advertiserBalanceService.selectList(accountIds);
        // 补充数据
        accountIds = ListUtils.mapToList(list, AdvertiserBalanceEntity::getAccountId);
        Map<Long, Integer> consumeOffsetMap = dspAdvertiserConsumeRecordService.advertiserConsumeOffsetForCrm(accountIds);
        Map<Long, Account> agentMap = agentService.selectAgentMap(accountIds);
        Map<Long, Account> accountMap = accountService.selectMapByIds(accountIds);
        // 构造结果
        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            AdvertiserBalanceVO balance = BeanUtil.copyProperties(data, AdvertiserBalanceVO.class);
            Optional.ofNullable(accountMap.get(data.getAccountId())).ifPresent(account -> {
                balance.setMainType(account.getMainType());
                balance.setCompanyName(account.getCompanyName());
            });
            Optional.ofNullable(agentMap.get(data.getAccountId())).ifPresent(agent -> {
                balance.setAgentId(agent.getId());
                balance.setAgentName(agent.getCompanyName());
            });
            Optional.ofNullable(consumeOffsetMap.get(data.getAccountId())).ifPresent(offset -> {
                balance.setOffsetAmount(-offset);
                balance.setTotalAmount(balance.getTotalAmount() - offset);
            });
            return balance;
        }));
    }

    /**
     * 导出余额记录
     */
    @Log(title = "广告主余额记录", businessType = BusinessType.EXPORT)
    @GetMapping("export")
    public AjaxResult export(AdvertiserBalanceListReq req) {
        String fileName = UUID.randomUUID().toString() + "_广告主余额记录.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;

        // 参数处理
        List<Long> accountIds = handleQueryParam(req);
        if (CollUtil.contains(accountIds, -1L)) {
            EasyExcel.write(filePath, AdvertiserRechargeExcelBo.class).sheet().doWrite(Collections.emptyList());
            return AjaxResult.success(fileName);
        }
        // 查询数据
        TableSupport.startPage();
        List<AdvertiserBalanceEntity> list =  advertiserBalanceService.selectList(accountIds);
        // 补充数据
        accountIds = ListUtils.mapToList(list, AdvertiserBalanceEntity::getAccountId);
        Map<Long, Integer> consumeOffsetMap = dspAdvertiserConsumeRecordService.advertiserConsumeOffsetForCrm(accountIds);
        Map<Long, Account> agentMap = agentService.selectAgentMap(accountIds);
        Map<Long, Account> accountMap = accountService.selectMapByIds(accountIds);
        Set<Long> excludeAccountIds = new HashSet<>(advertiserTagService.getAdvertiserByTag("测试账号"));
        // 构造结果
        List<AdvertiserBalanceExcelBo> excelBos = list.stream().map(record -> {
            AdvertiserBalanceExcelBo excelBo = BeanUtil.copyProperties(record, AdvertiserBalanceExcelBo.class);
            Optional.ofNullable(agentMap.get(record.getAccountId())).ifPresent(agent -> {
                excelBo.setAgentId(agent.getId());
                excelBo.setAgentName(agent.getCompanyName());
            });
            Optional.ofNullable(accountMap.get(record.getAccountId())).ifPresent(account -> {
                if (isAgent(account.getMainType())) {
                    excelBo.setMainTypeStr("代理商");
                    excelBo.setAgentId(account.getId());
                    excelBo.setAgentName(account.getCompanyName());
                } else if (isAdvertiser(account.getMainType())) {
                    excelBo.setAdvertiserId(account.getId());
                    excelBo.setAdvertiserName(account.getCompanyName());
                    excelBo.setMainTypeStr(null != excelBo.getAgentId() ? "子广告主" : "直客");
                }
            });
            Optional.ofNullable(consumeOffsetMap.get(record.getAccountId())).ifPresent(offset -> {
                excelBo.setOffsetAmount(-offset);
                excelBo.setTotalAmount(excelBo.getTotalAmount() - offset);
            });
            if (excludeAccountIds.contains(record.getAccountId())) {
                excelBo.setTag("测试账号");
            }
            return excelBo;
        }).collect(Collectors.toList());
        EasyExcel.write(filePath, AdvertiserBalanceExcelBo.class).sheet().doWrite(excelBos);
        return AjaxResult.success(fileName);
    }

    /**
     * 查询余额汇总
     */
    @GetMapping("/statisticBalance")
    public Result<AdvertiserBalanceStatisticVO> statisticBalance(AdvertiserBalanceListReq req) {
        // 参数处理
        List<Long> accountIds = handleQueryParam(req);
        if (CollUtil.contains(accountIds, -1L)) {
            return ResultBuilder.success();
        }
        List<Long> excludeAccountIds = advertiserTagService.getAdvertiserByTag("测试账号");

        // 查询数据
        AdvertiserBalanceStatisticBo data = advertiserBalanceService.selectStatisticBalance(accountIds, excludeAccountIds);
        Long consumeOffset = dspAdvertiserConsumeRecordService.sumAdvertiserConsumeOffsetForCrm(accountIds, excludeAccountIds);
        // 构造结果
        AdvertiserBalanceStatisticVO balanceVO = new AdvertiserBalanceStatisticVO();
        balanceVO.setTotalAmount(data.getTotalAmount() - consumeOffset);
        balanceVO.setCashAmount(data.getCashAmount());
        balanceVO.setRebateAmount(data.getRebateAmount());
        balanceVO.setOffsetAmount(-consumeOffset);
        return ResultBuilder.success(balanceVO);
    }

    /**
     * 查询广告主账户余额基础信息
     *
     * @param accountId 广告主ID
     * @return 广告主账户余额
     */
    @GetMapping("balanceInfo")
    public Result<AdvertiserBalanceBaseInfoVO> balanceInfo(@NotNull(message = "账号id不能为空") Long accountId){
        AdvertiserBalanceEntity balanceEntity = advertiserBalanceService.selectByAccountId(accountId);
        AdvertiserBalanceBaseInfoVO baseInfoVO = BeanUtil.copyProperties(balanceEntity,AdvertiserBalanceBaseInfoVO.class);
        if (null == baseInfoVO || null == baseInfoVO.getTotalAmount()) {
            return ResultBuilder.success();
        }

        // 离线数据广告主计算运营差额,账户余额和总消费金额的差额计算.offset=订正后的值-原值
        Integer consumeOffset = dspAdvertiserConsumeRecordService.consumeOffsetForCrm(accountId);
        baseInfoVO.setTotalAmount(baseInfoVO.getTotalAmount() - consumeOffset);
        return ResultBuilder.success(baseInfoVO);
    }

    /**
     * 处理余额记录列表请求参数
     *
     * @param req 请求参数
     * @return 账号ID列表
     */
    private List<Long> handleQueryParam(AdvertiserBalanceListReq req) {
        List<Long> accountIds = req.getAdvertiserIds();
        // 代理商查询
        if (CollectionUtils.isNotEmpty(req.getAgentIds())) {
            List<Long> tmpAccountIds = new ArrayList<>(req.getAgentIds());
            tmpAccountIds.addAll(agentService.selectAdvertiserIdsByAgentIds(req.getAgentIds()));
            accountIds = mergeParamIds(accountIds, tmpAccountIds);
            if (CollectionUtils.isEmpty(accountIds)) {
                return Collections.singletonList(-1L);
            }
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            accountIds = mergeParamIds(accountIds, permission.getValues());
            if (CollectionUtils.isEmpty(accountIds)) {
                return Collections.singletonList(-1L);
            }
        }
        return accountIds;
    }
}
