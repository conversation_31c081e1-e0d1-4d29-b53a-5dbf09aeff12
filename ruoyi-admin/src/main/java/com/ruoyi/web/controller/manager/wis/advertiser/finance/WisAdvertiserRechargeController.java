package com.ruoyi.web.controller.manager.wis.advertiser.finance;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserRechargeRecordEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeListReq;
import com.ruoyi.system.req.advertiser.finance.WisAdvertiserRechargeListReq;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserRechargeRecordService;
import com.ruoyi.system.vo.advertiser.finance.WisAdvertiserRechargeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.ruoyi.common.enums.advertiser.RechargeAuditStatus.APPROVE;

/**
 * 广告主充值记录Controller
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@RequestMapping("/wis/fiance/recharge")
@RestController
public class WisAdvertiserRechargeController extends BaseController {

    @Autowired
    private AdvertiserRechargeRecordService advertiserRechargeRecordService;

    /**
     * 充值记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<WisAdvertiserRechargeVO> list(WisAdvertiserRechargeListReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非广告主/代理商不展示
        if (null == user || null == user.getCrmAccountId()) {
            return getDataTable(Collections.emptyList());
        }
        if (!Objects.equals(user.getMainType(), AccountMainType.ADVERTISER.getType())
                && !Objects.equals(user.getMainType(), AccountMainType.AGENT.getType()) ) {
            return getDataTable(Collections.emptyList());
        }

        AdvertiserRechargeListReq listReq = BeanUtil.copyProperties(req, AdvertiserRechargeListReq.class);
        listReq.setAccountId(user.getCrmAccountId());
        listReq.setAuditStatus(APPROVE.getStatus());

        TableSupport.startPage();
        List<AdvertiserRechargeRecordEntity> list = advertiserRechargeRecordService.selectListByAccountIdAndDate(listReq);

        return getDataTable(PageInfoUtils.dto2Vo(list, record -> BeanUtil.copyProperties(record, WisAdvertiserRechargeVO.class)));
    }
}
