package com.ruoyi.web.controller.engine;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ijpay.core.enums.SignType;
import com.ijpay.core.enums.TradeType;
import com.ijpay.core.kit.HttpKit;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.model.OrderQueryModel;
import com.ijpay.wxpay.model.UnifiedOrderModel;
import com.ruoyi.common.config.WxPayConfig;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.WxPayOrderStatus;
import com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord;
import com.ruoyi.system.entity.pay.WxPayOrderEntity;
import com.ruoyi.system.mapper.landpage.LiuziLandpageFormRecordMapper;
import com.ruoyi.system.req.wx.WxOrderReq;
import com.ruoyi.system.req.wx.WxOrderResultReq;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.landpage.LiuziLandpageService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.service.wx.WxPayOrderService;
import com.ruoyi.system.service.wx.WxPayService;
import com.ruoyi.system.vo.engine.WxOrderResultVO;
import com.ruoyi.system.vo.engine.WxOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.ruoyi.common.enums.InnerLogType.BLIND_BOX_POPUP_CLICK;

/**
 * 落地页支付接口(不鉴权)
 *
 * <AUTHOR>
 * @date 2023/01/16
 */
@Slf4j
@RestController
@RequestMapping("/lp/pay")
public class LandpagePayController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private WxPayOrderService wxPayOrderService;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private LiuziLandpageService liuziLandpageService;

    @Autowired
    private LiuziLandpageFormRecordMapper liuziLandpageFormRecordMapper;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private StatService statService;

    @Autowired
    private WxPayService wxPayService;

    /**
     * 微信支付下单接口(H5)
     * 注意：必须再web页面中发起支付且域名已添加到开发配置中
     */
    @CrossOrigin
    @GetMapping(value = "/wxOrder")
    public Result<WxOrderVO> wxOrder(WxOrderReq req, HttpServletRequest request) {
        // 参数校验
        if (StrUtil.isBlank(req.getLpk())) {
            return ResultBuilder.fail("参数错误");
        }

        // 限制请求频率
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K054.join(req.getLpk(), req.getOrderId(), req.getPhone()), 5);
        if (lock == null) {
            return ResultBuilder.fail("支付失败，请重试");
        }

        // 查询支付金额
        Integer fee = landpageCacheService.selectLandpagePayMoneyCache(req.getLpk());
        if (fee <= 0) {
            return ResultBuilder.fail("支付金额错误");
        }

        // 支付参数构造
        String tradeType = StringUtils.defaultString(req.getTradeType(), TradeType.MWEB.getTradeType());
        String ip = IpUtils.getIpAddr(request);
        String referer = request.getHeader("referer");
        String userAgent = request.getHeader("User-Agent");
        String outTradeNo = WxPayKit.generateStr();
        boolean isJsApi = Objects.equals(tradeType, TradeType.JSAPI.getTradeType());
        String openid = isJsApi ? wxPayService.getOpenid(req.getCode()) : null;
        String payAppId = isJsApi ? WxPayConfig.getGzhAppId() : WxPayConfig.getAppId();
        Map<String, String> params = UnifiedOrderModel
                .builder()
                .appid(payAppId)
                .mch_id(WxPayConfig.getMchId())
                .nonce_str(WxPayKit.generateStr())
                .body("直播课程-" + StringUtils.defaultString(req.getOrderId()))
                .out_trade_no(outTradeNo)
                .total_fee(String.valueOf(fee))
                .spbill_create_ip(ip)
                .notify_url(WxPayConfig.getNotifyUrl())
                .trade_type(tradeType)
                .openid(openid)
                .build()
                .createSign(WxPayConfig.getPartnerKey(), SignType.HMACSHA256);

        // 支付下单
        String xmlResult = WxPayApi.pushOrder(params);
        log.info("微信支付下单, xml={}", xmlResult);
        Map<String, String> result = WxPayKit.xmlToMap(xmlResult);

        // 下单结果处理
        if (!WxPayKit.codeIsOk(result.get("return_code"))) {
            return ResultBuilder.fail(result.get("return_msg"));
        }
        if (!WxPayKit.codeIsOk(result.get("result_code"))) {
            return ResultBuilder.fail(result.get("err_code_des"));
        }
        // 以下字段在return_code和result_code都为SUCCESS的时候有返回
        String retTradeType = result.get("trade_type");
        String prepayId = result.get("prepay_id");
        String webUrl = result.get("mweb_url");

        // 支付订单落库
        GlobalThreadPool.executorService.submit(() -> {
            WxPayOrderEntity payOrder = new WxPayOrderEntity();
            payOrder.setOutTradeNo(outTradeNo);
            payOrder.setLandpageKey(req.getLpk());
            payOrder.setNhOrderId(req.getOrderId());
            Optional.ofNullable(orderService.selectByOrderId(req.getOrderId())).ifPresent(order -> {
                payOrder.setNhAppId(order.getAppId());
                payOrder.setNhSlotId(order.getSlotId());
                payOrder.setNhConsumerId(order.getConsumerId());
            });
            if (Validator.isMobile(req.getPhone())) {
                payOrder.setPhone(req.getPhone());
            }
            if (StringUtils.isNotBlank(req.getName())) {
                payOrder.setName(req.getName());
            }
            payOrder.setAppId(payAppId);
            payOrder.setMchId(WxPayConfig.getMchId());
            payOrder.setTradeType(retTradeType);
            payOrder.setTotalFee(fee);
            payOrder.setIp(ip);
            payOrder.setReferer(StrUtil.subPre(referer, 255));
            payOrder.setUserAgent(StrUtil.subPre(userAgent, 255));
            wxPayOrderService.insert(payOrder);

            // 增加留资订单
            Optional.ofNullable(orderService.selectByOrderId(req.getOrderId())).ifPresent(order -> {
                AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
                LiuziLandpageFormRecord record = new LiuziLandpageFormRecord();
                record.setLandpageType(LiuziLandPageTypeEnum.NUO_HE.getType());
                record.setPhone(req.getPhone());
                record.setName(req.getName());
                record.setAdvertId(order.getAdvertId());
                record.setOrderId(order.getOrderId());
                record.setConsumerId(order.getConsumerId());
                record.setAppId(order.getAppId());
                record.setSlotId(order.getSlotId());
                record.setIp(StrUtil.subPre(ip, 32));
                record.setReferer(StrUtil.subPre(referer, 255));
                record.setOutTradeNo(outTradeNo);
                if (null != adSnapshot) {
                    record.setLandpageUrl(adSnapshot.getLandpageUrl());
                }
                liuziLandpageFormRecordMapper.insertLandpageFormRecord(record);

                // 埋点-领取
                statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());
            });
        });

        WxOrderVO wxOrderVO = new WxOrderVO();
        wxOrderVO.setTradeNo(outTradeNo);
        wxOrderVO.setPrepayId(prepayId);
        wxOrderVO.setUrl(webUrl);
        // JSAPI加密参数
        if (isJsApi) {
            wxOrderVO.setJsParams(wxPayService.getJSParams(payAppId, prepayId));
        }
        lock.unlock();
        return ResultBuilder.success(wxOrderVO);
    }

    /**
     * 微信支付查询结果接口
     */
    @CrossOrigin
    @GetMapping(value = "/wxOrderResult")
    public Result<WxOrderResultVO> wxOrderResult(WxOrderResultReq req, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (StrUtil.isBlank(req.getOutTradeNo())) {
            return ResultBuilder.fail("参数错误");
        }
        WxPayOrderEntity payOrder = wxPayOrderService.selectByOutTradeNo(req.getOutTradeNo());
        if (null == payOrder) {
            return ResultBuilder.fail("未查询到支付记录");
        }

        WxOrderResultVO orderResult = new WxOrderResultVO();
        orderResult.setOutTradeNo(payOrder.getOutTradeNo());
        orderResult.setStatus(payOrder.getStatus());

        if (WxPayOrderStatus.isReady(payOrder.getStatus())) {
            // 手动查询订单
            Map<String, String> params = OrderQueryModel.builder()
                    .appid(WxPayConfig.getAppId())
                    .mch_id(WxPayConfig.getMchId())
                    .nonce_str(WxPayKit.generateStr())
                    .out_trade_no(payOrder.getOutTradeNo())
                    .build()
                    .createSign(WxPayConfig.getPartnerKey(), SignType.MD5);

            String orderQuery = WxPayApi.orderQuery(params);
            log.info(orderQuery);
            Map<String, String> result = WxPayKit.xmlToMap(orderQuery);
            log.info("微信支付订单查询, {}", JSON.toJSONString(result));

            // 更新订单状态
            if (Objects.equals(result.get("result_code"), "SUCCESS")) {
                if (Objects.equals(result.get("trade_state"), "SUCCESS")) {
                    orderResult.setStatus(WxPayOrderStatus.PAID.getStatus());
                    GlobalThreadPool.executorService.submit(() -> {
                        wxPayOrderService.finishPay(result);
                        liuziLandpageService.formPaySubmit(params.get("out_trade_no"), params.get("total_fee"), params.get("time_end"));
                    });
                } else {
                    orderResult.setStatus(WxPayOrderStatus.CLOSED.getStatus());
                }
            }
        }

        return ResultBuilder.success(orderResult);
    }

    /**
     * 微信支付回调接口回调
     */
    @CrossOrigin
    @PostMapping("/callback")
    public Map<String, String> callback(HttpServletRequest request) {
        String xmlMsg = HttpKit.readData(request);
        log.info("微信支付回调通知，xml={}", xmlMsg);

        // xml解析为map
        Map<String, String> xml = new HashMap<>(2);
        Map<String, String> params = WxPayKit.xmlToMap(xmlMsg);
        if (MapUtils.isEmpty(params)) {
            xml.put("return_code", "FAIL");
            xml.put("return_msg", "参数错误");
            return xml;
        }

        // 通知校验
//        if (!WxPayKit.verifyNotify(params, WxPayConfig.getPartnerKey())) {
//            xml.put("return_code", "FAIL");
//            xml.put("return_msg", "校验失败");
//            return xml;
//        }

        // 注意重复通知的情况，同一订单号可能收到多次通知，请注意一定先判断订单状态
        String resultCode = params.get("result_code");
        if (WxPayKit.codeIsOk(resultCode)) {
            GlobalThreadPool.executorService.submit(() -> {
                wxPayOrderService.finishPay(params);
                liuziLandpageService.formPaySubmit(params.get("out_trade_no"), params.get("total_fee"), params.get("time_end"));
            });
        }
        xml.put("return_code", "SUCCESS");
        xml.put("return_msg", "OK");
        return xml;
    }
}
