package com.ruoyi.web.controller.manager.crm.dsp.advert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.advert.AssessTypeEnum;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.advert.AdvertExtInfo;
import com.ruoyi.system.domain.slot.ShuntRedirectItem;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertCost;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.advertiser.AdvertiserOperateTimerEntity;
import com.ruoyi.system.entity.datashow.AdvertDayData;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.req.advert.AdvertCopyReq;
import com.ruoyi.system.req.advert.AdvertCpcModifyReq;
import com.ruoyi.system.req.advert.AdvertCreateReq;
import com.ruoyi.system.req.advert.AdvertDayBudgetModifyReq;
import com.ruoyi.system.req.advert.AdvertListReq;
import com.ruoyi.system.req.advert.AdvertModifyReq;
import com.ruoyi.system.req.advert.AdvertSelectReq;
import com.ruoyi.system.req.advert.AdvertServingSwitchReq;
import com.ruoyi.system.req.advert.AdvertWeightModifyReq;
import com.ruoyi.system.req.advert.AdvertiserAdvertCloseReq;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.service.advertiser.AdvertiserOperateTimerService;
import com.ruoyi.system.service.advertiser.AdvertiserQualificationService;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.datasource.AdvertDayDataService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.AdvertCostService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.service.manager.SlotConfigService;
import com.ruoyi.system.service.validate.SlotRedirectValidateService;
import com.ruoyi.system.vo.advert.AdvertBaseInfoVo;
import com.ruoyi.system.vo.advert.AdvertInfoVO;
import com.ruoyi.system.vo.advert.AdvertOrientSelectPageVO;
import com.ruoyi.system.vo.advert.AdvertVO;
import com.ruoyi.system.vo.advertiser.timer.AdvertiserOperateTimerSimpleVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountStatusEnum.isAgentDisable;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;
import static com.ruoyi.common.enums.advert.AdvertCategory.MINI_PROGRAM;
import static com.ruoyi.common.enums.common.SwitchStatusEnum.isSwitchOn;
import static com.ruoyi.common.enums.slot.SlotRedirectType.isAreaTargetRedirect;
import static com.ruoyi.common.enums.slot.SlotRedirectType.isShuntRedirect;

/**
 * [CRM后台]广告列表
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/advert")
public class AdvertController extends BaseController {

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private AdvertCostService advertCostService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private AdvertDayDataService advertDayDataService;

    @Autowired
    private SlotConfigService slotConfigService;

    @Autowired
    private SlotRedirectValidateService slotRedirectValidateService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertiserQualificationService advertiserQualificationService;

    @Autowired
    private AdvertiserOperateTimerService advertiserOperateTimerService;

    /**
     * 查询广告列表
     */
    @GetMapping("/list")
    public TableDataInfo<AdvertVO> list(AdvertListReq req) {
        // 搜索条件查询
        Advert param = new Advert();
        param.setServingSwitch(req.getServingSwitch());
        param.setSearchValue(req.getAdvertSearch());
        param.setOperatorName(req.getOperatorName());
        param.setGmtCreateStart(Optional.ofNullable(req.getGmtCreateStart()).map(DateUtil::beginOfDay).orElse(null));
        param.setGmtCreateEnd(Optional.ofNullable(req.getGmtCreateEnd()).map(DateUtil::endOfDay).orElse(null));
        param.setAdvertiserIds(req.getAdvertiserIds());
        // 代理商查询
        if (CollectionUtils.isNotEmpty(req.getAgentIds())) {
            List<Long> advertiserIds = agentService.selectAdvertiserIdsByAgentIds(req.getAgentIds());
            param.setAdvertiserIds(mergeParamIds(param.getAdvertiserIds(), advertiserIds));
            if (CollectionUtils.isEmpty(param.getAdvertiserIds())) {
                return getDataTable(Collections.emptyList());
            }
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            param.setAdvertiserIds(mergeParamIds(param.getAdvertiserIds(), permission.getValues()));
            if (CollectionUtils.isEmpty(param.getAdvertiserIds())) {
                return getDataTable(Collections.emptyList());
            }
        }

        // 查询广告列表
        startPage();
        List<Advert> list = advertService.selectAdvertList(param);
        if (CollectionUtils.isEmpty(list)) {
            return getDataTable(Collections.emptyList());
        }
        List<Long> advertIds = list.stream().map(Advert::getId).collect(Collectors.toList());
        // 查询默认配置
        List<AdvertOrientation> advertOrientations = advertOrientationService.selectDefaultByAdvertIds(advertIds);
        Map<Long, AdvertOrientation> advertOrientationMap = advertOrientations.stream().collect(Collectors.toMap(AdvertOrientation::getAdvertId, Function.identity()));
        // 补充额外信息
        List<Long> advertiserIds = list.stream().map(Advert::getAdvertiserId).distinct().collect(Collectors.toList());
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(advertiserIds);
        Map<Long, String> agentNameMap = agentService.selectAgentNameMap(advertiserIds);
        Map<Long, List<String>> industryMap = advertiserQualificationService.selectIndustryMapByAccountIds(advertiserIds);
        Map<Long, Boolean> qualificationRequireCheckMap = advertiserQualificationService.industryQualificationRequireCheck(advertiserIds);
        Map<Long, Boolean> qualificationAuditCheckMap = advertiserQualificationService.industryQualificationAuditCheck(advertiserIds);
        Map<Long, List<AdvertiserOperateTimerEntity>> advertiserTimerMap = advertiserOperateTimerService.selectPlanMapByAdvertiserIds(advertiserIds);
        Map<Long, Integer> orientCountMap = advertOrientationService.selectCountMapByAdvertIds(advertIds);

        return getDataTable(PageInfoUtils.dto2Vo(list, advert -> {
            AdvertVO advertVO = BeanUtil.copyProperties(advert, AdvertVO.class);
            advertVO.setAdvertiserName(advertiserNameMap.get(advert.getAdvertiserId()));
            advertVO.setAgentName(agentNameMap.get(advert.getAdvertiserId()));
            advertVO.setIndustryList(industryMap.get(advert.getAdvertiserId()));
            advertVO.setQualificationRequireCheck(qualificationRequireCheckMap.get(advert.getAdvertiserId()));
            advertVO.setQualificationAuditCheck(qualificationAuditCheckMap.get(advert.getAdvertiserId()));
            advertVO.setTimerList(BeanUtil.copyToList(advertiserTimerMap.get(advert.getAdvertiserId()), AdvertiserOperateTimerSimpleVO.class));
            advertVO.setOrientCount(orientCountMap.get(advert.getId()));
            Optional.ofNullable(advertOrientationMap.get(advert.getId())).ifPresent(orient -> {
                advertVO.setUnitPrice(orient.getUnitPrice());
                advertVO.setMilliUnitPrice(orient.getMilliUnitPrice());
                advertVO.setWeight(orient.getWeight());
            });
            return advertVO;
        }));
    }

    /**
     * 广告下拉列表(分页)
     * 使用的地方：广告位配置直投广告、广告/配置数据
     */
    @GetMapping("/listForSelect")
    public TableDataInfo<AdvertOrientSelectPageVO> listForSelect(AdvertSelectReq req) {
        AdvertOrientation param = new AdvertOrientation();
        param.setOrientSearch(req.getOrientSearch());
        param.setAdvertId(req.getAdvertId());

        // 广告查询
        if (StringUtils.isNotBlank(req.getAdvertSearch())) {
            Advert advertParam = new Advert();
            advertParam.setSearchValue(req.getAdvertSearch());
            List<Advert> adverts = advertService.selectAdvertList(advertParam);
            if (CollectionUtils.isEmpty(adverts)) {
                return getDataTable(Collections.emptyList());
            }
            param.setAdvertIds(ListUtils.mapToList(adverts, Advert::getId));
        }

        startPage();
        List<AdvertOrientation> list = advertOrientationService.selectAdvertOrientationList(param);

        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(ListUtils.mapToList(list, AdvertOrientation::getAdvertId));

        return getDataTable(PageInfoUtils.dto2Vo(list, orient -> {
            AdvertOrientSelectPageVO orientVO = new AdvertOrientSelectPageVO();
            orientVO.setId(orient.getAdvertId());
            orientVO.setOrientId(orient.getId());
            orientVO.setOrientName(orient.getOrientName());
            orientVO.setServingSwitch(orient.getServingSwitch());
            Optional.ofNullable(advertMap.get(orient.getAdvertId())).ifPresent(advert -> {
                orientVO.setAdvertName(advert.getAdvertName());
                orientVO.setServingSwitch(orient.getServingSwitch() & advert.getServingSwitch());
            });
            return orientVO;
        }));
    }

    /**
     * 查询广告基础信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        Advert advert = advertService.selectAdvertById(id);
        if (null == advert) {
            return AjaxResult.success();
        }
        AdvertInfoVO advertVO = BeanUtil.copyProperties(advert, AdvertInfoVO.class, "extInfo");
        advertVO.setExtInfo(JSON.parseObject(advert.getExtInfo(), AdvertExtInfo.class));

        //广告结算信息
        AdvertCost advertCost = advertCostService.selectById(id);
        if(Objects.nonNull(advertCost)){
            advertVO.setCost(advertCost.getCost());
            advertVO.setBillingType(advertCost.getBillingType());
        }
        return AjaxResult.success(advertVO);
    }

    /**
     * 新增广告
     */
    @Log(title = "广告", businessType = BusinessType.INSERT)
    @PostMapping
    public Result<Long> add(@RequestBody AdvertCreateReq req) {
        // 出价校验
        if (null == req.getMilliUnitPrice() && null == req.getUnitPrice()) {
            return ResultBuilder.fail("广告出价不能为空");
        }
        if (null != req.getMilliUnitPrice() && req.getMilliUnitPrice() < 0
                || null != req.getUnitPrice() && req.getUnitPrice() < 0) {
            return ResultBuilder.fail("请输入合规价格");
        }
        // 小程序校验
        if (Objects.equals(req.getAdvertCategory(), MINI_PROGRAM.getType())) {
            if (null == req.getExtInfo() || null == req.getExtInfo().getAppId() || null == req.getExtInfo().getAppPath()) {
                return ResultBuilder.fail("小程序AppID/AppPath不能为空");
            }
        } else {
            // 落地页校验
            if (StringUtils.isBlank(req.getLandpageUrl())) {
                return ResultBuilder.fail("落地页链接不能为空");
            }
            if (!req.getLandpageUrl().contains("//")) {
                return ResultBuilder.fail("请输入合法的落地页链接");
            }
        }
        // 点击监测链接校验
        if (StringUtils.isNotEmpty(req.getClickCallbackUrl())
                && !req.getClickCallbackUrl().startsWith("https://") && !req.getClickCallbackUrl().startsWith("http://")) {
            return ResultBuilder.fail("请输入合法的点击监测链接");
        }

        // 新增广告
        Long advertId = advertService.insertAdvert(req);
        return ResultBuilder.success(advertId);
    }

    /**
     * 修改广告
     */
    @Log(title = "广告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdvertModifyReq req) {
        // 小程序校验
        if (Objects.equals(req.getAdvertCategory(), MINI_PROGRAM.getType())) {
            if (null == req.getExtInfo() || null == req.getExtInfo().getAppId() || null == req.getExtInfo().getAppPath()) {
                return AjaxResult.error("小程序AppID/AppPath不能为空");
            }
        } else {
            // 落地页校验
            if (StringUtils.isBlank(req.getLandpageUrl())) {
                return AjaxResult.error("落地页链接不能为空");
            }
            if (!req.getLandpageUrl().contains("//")) {
                return AjaxResult.error("请输入合法的落地页链接");
            }
        }
        // 点击监测链接校验
        if (StringUtils.isNotEmpty(req.getClickCallbackUrl())
                && !req.getClickCallbackUrl().startsWith("https://") && !req.getClickCallbackUrl().startsWith("http://")) {
            return AjaxResult.error("请输入合法的点击监测链接");
        }

        Advert advert = new Advert();
        advert.setId(req.getId());
        advert.setAdvertName(req.getAdvertName());
        advert.setAdvertCategory(req.getAdvertCategory());
        advert.setDailyBudget(req.getDailyBudget());
        advert.setStartServingDate(req.getStartServingDate());
        advert.setStopServingDate(req.getStopServingDate());
        advert.setAssessType(req.getAssessType());
        advert.setAssessCost(req.getAssessCost());
        advert.setLandpageUrl(StringUtils.defaultString(req.getLandpageUrl()).trim());
        advert.setClickCallbackUrl(StringUtils.defaultString(req.getClickCallbackUrl()).trim());
        advert.setThumbnailImg(req.getThumbnailImg());
        if (null != req.getExtInfo()) {
            advert.setExtInfo(JSON.toJSONString(req.getExtInfo()));
        }

        if(Objects.nonNull(req.getBillingType())){
            //更新当日结算指标和成本
            AdvertCost advertCost = new AdvertCost();
            advertCost.setAdvertId(req.getId());
            advertCost.setCost(req.getCost());
            advertCost.setBillingType(req.getBillingType());
            advertCostService.insertOrUpdateAdvertCost(advertCost);
        }

        // 更新广告
        int result = advertService.updateAdvert(advert);
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertCacheMsg(advert.getId());
        advertService.sendCheckBudgetMsg(advert.getId());
        return toAjax(result);
    }

    /**
     * 投放开关
     */
    @Log(title = "广告", businessType = BusinessType.UPDATE)
    @PostMapping("/toggleServingSwitch")
    public AjaxResult servingSwitch(@RequestBody AdvertServingSwitchReq req) {
        // 校验参数
        if (null == req.getId() || null == req.getServingSwitch()) {
            return AjaxResult.error("参数异常");
        }
        // 检查广告主是否被禁用
        if (SwitchStatusEnum.isSwitchOn(req.getServingSwitch())) {
            Advert advert = advertService.selectAdvertById(req.getId());
            if (null == advert) {
                return AjaxResult.error("无效的广告ID");
            }
            Account advertiser = accountService.selectAccountById(advert.getAdvertiserId());
            if (null != advertiser && isAgentDisable(advertiser.getStatus())) {
                return AjaxResult.error("该广告的广告主已被代理商禁用，无法开启广告");
            }
        }
        // 更新投放开关
        Advert param = new Advert();
        param.setId(req.getId());
        param.setServingSwitch(req.getServingSwitch());
        return toAjax(advertService.updateAdvertStatus(param));
    }

    /**
     * 广告批量开启关闭
     */
    @Log(title = "广告批量开启关闭", businessType = BusinessType.UPDATE)
    @PostMapping("/openOrCloseAdvertiserAdvert")
    public AjaxResult openOrCloseAdvertiserAdvert(@RequestBody AdvertiserAdvertCloseReq req) {
        // 校验参数
        if (null == req.getAdvertiserId() || !SwitchStatusEnum.isValidStatus(req.getStatus())) {
            return AjaxResult.error("参数异常");
        }
        // 检查广告主是否被禁用
        if (SwitchStatusEnum.isSwitchOn(req.getStatus())) {
            Account advertiser = accountService.selectAccountById(req.getAdvertiserId());
            if (null != advertiser && isAgentDisable(advertiser.getStatus())) {
                return AjaxResult.error("该广告的广告主已被代理商禁用，无法开启广告");
            }
        }
        // 指定时间
        if (null != req.getPlanTime()) {
            if (req.getPlanTime().before(new Date())) {
                return AjaxResult.error("时间不能早于当前时间");
            }
            AdvertiserOperateTimerEntity timer = new AdvertiserOperateTimerEntity();
            timer.setAdvertiserId(req.getAdvertiserId());
            timer.setOperType(SwitchStatusEnum.isSwitchOff(req.getStatus()) ? 1 : 2);
            timer.setPlanTime(req.getPlanTime());
            timer.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
            return toAjax(advertiserOperateTimerService.add(timer));
        }

        //查询广告主所有的广告id
        List<Long> advertIds = advertService.selectAllOpenOrCloseAdvertNameByAdvertiserId(req.getAdvertiserId(),SwitchStatusEnum.toggle(req.getStatus()));
        // 更新投放开关
        int result = advertService.updateSwitchByIds(advertIds, req.getStatus());
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertCacheMsg(advertIds);
        return toAjax(result);
    }

    /**
     * 获取要开启关闭的广告名称列表
     */
    @GetMapping("getOpenOrCloseAdvert")
    public Result<List<String>> getOpenOrCloseAdvert(@Validated @NotNull(message = "广告主id不能为空") Long advertiserId,@Validated @NotNull(message = "状态不能为空") Integer status){
        //查询广告主所有的广告id
        return ResultBuilder.success(advertService.selectAllOpenAdvertNameByAdvertiserId(advertiserId,status));
    }

    /**
     * 考核指标列表
     */
    @GetMapping("/assessTypeList")
    public AjaxResult listAssessTypeTotal() {
        return AjaxResult.success(AssessTypeEnum.toJSONArray());
    }

    /**
     * 获取广告基础信息
     *
     * @param id 广告id
     * @return 广告基础信息
     */
    @GetMapping("baseInfo")
    public Result<AdvertBaseInfoVo> baseInfo(@Validated @NotNull Long id){
        // 查询广告信息
        Advert advert = advertService.selectAdvertById(id);
        if (null == advert) {
            return ResultBuilder.success();
        }

        // 查询广告主和代理商名称
        AdvertBaseInfoVo baseInfo = new AdvertBaseInfoVo();
        baseInfo.setAdvertId(id);
        baseInfo.setAdvertName(advert.getAdvertName());
        baseInfo.setAdvertiserId(advert.getAdvertiserId());
        baseInfo.setAdvertiserName(advertiserService.selectAdvertiserName(advert.getAdvertiserId()));
        baseInfo.setAgentId(agentService.selectAgentIdByAdvertiserId(advert.getAdvertiserId()));
        baseInfo.setAgentName(agentService.selectAgentNameByAgentId(baseInfo.getAgentId()));
        return ResultBuilder.success(baseInfo);
    }

    /**
     * 复制广告
     */
    @Log(title = "复制广告", businessType = BusinessType.INSERT)
    @PostMapping("copyAdvert")
    public Result<Long> copyAdvert(@RequestBody @Validated AdvertCopyReq req) {
        Long advertId = advertService.copyAdvert(req.getId(), req.getAdvertiserId());
        return ResultBuilder.success(advertId);
    }

    /**
     * 修改广告默认配置CPC价格
     */
    @Log(title = "广告", businessType = BusinessType.UPDATE)
    @PostMapping("modifyCpc")
    public Result<Void> modifyCpc(@RequestBody AdvertCpcModifyReq req) {
        if (null == req.getMilliUnitPrice() && null == req.getUnitPrice()) {
            return ResultBuilder.fail("计费单价不能为空");
        }
        int milliUnitPrice = NumberUtils.defaultInt(req.getMilliUnitPrice(), NumberUtils.defaultInt(req.getUnitPrice()) * 100);
        if (milliUnitPrice < 0) {
            return ResultBuilder.fail("计费单价不能为负数");
        }

        AdvertOrientation orient = advertOrientationService.selectDefaultOrientationByAdvertId(req.getId());
        if (null == orient) {
            return ResultBuilder.fail("无效的广告ID");
        }
        if (!Objects.equals(orient.getChargeType(), 1)) {
            return ResultBuilder.fail("广告的计费类型不为CPC");
        }

        AdvertOrientation updateOrient = new AdvertOrientation();
        updateOrient.setId(orient.getId());
        updateOrient.setMilliUnitPrice(milliUnitPrice);
        updateOrient.setUnitPrice(milliUnitPrice / 100);
        advertOrientationService.updateAdvertOrientation(updateOrient);
        refreshCacheService.sendRefreshAdvertCacheMsg(orient.getAdvertId());
        return ResultBuilder.success();
    }

    /**
     * 修改广告默认配置权重
     */
    @Log(title = "广告", businessType = BusinessType.UPDATE)
    @PostMapping("modifyWeight")
    public Result<Void> modifyWeight(@RequestBody @Validated AdvertWeightModifyReq req) {
        AdvertOrientation orient = advertOrientationService.selectDefaultOrientationByAdvertId(req.getId());
        if (null == orient) {
            return ResultBuilder.fail("无效的广告ID");
        }
        AdvertOrientation updateOrient = new AdvertOrientation();
        updateOrient.setId(orient.getId());
        updateOrient.setWeight(req.getWeight());
        advertOrientationService.updateAdvertOrientation(updateOrient);
        refreshCacheService.sendRefreshAdvertOrientationCacheMsg(orient.getId());
        return ResultBuilder.success();
    }

    /**
     * 修改广告每日预算
     */
    @Log(title = "广告", businessType = BusinessType.UPDATE)
    @PostMapping("modifyDailyBudget")
    public Result<Void> modifyDailyBudget(@RequestBody AdvertDayBudgetModifyReq req) {
        Advert advert = advertService.selectAdvertById(req.getId());
        if (null == advert) {
            return ResultBuilder.fail("无效的广告ID");
        }
        // 更新广告权重
        Advert updateAdvert = new Advert();
        updateAdvert.setId(req.getId());
        updateAdvert.setDailyBudget(req.getDailyBudget());
        advertService.updateAdvert(updateAdvert);
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertCacheMsg(req.getId());
        advertService.sendCheckBudgetMsg(req.getId());
        return ResultBuilder.success();
    }

    /**
     * 标记广告无效
     */
    @Log(title = "广告", businessType = BusinessType.UPDATE)
    @PostMapping("/invalidateAdvert")
    public AjaxResult invalidateAdvert(@RequestBody IdReq req) {
        Advert advert = advertService.selectAdvertById(req.getId());
        if (null == advert) {
            return AjaxResult.error("无效的广告ID");
        }
        if (isSwitchOn(advert.getServingSwitch())) {
            return AjaxResult.error("请检查广告开关是否已关闭");
        }
        // 检查是否被作为直投广告
        List<SlotConfig> slotConfigs = slotConfigService.selectSlotConfigList(new SlotConfig());
        if (CollectionUtils.isNotEmpty(slotConfigs)) {
            List<Long> slotIds = new ArrayList<>();
            String advertIdStr = String.valueOf(req.getId());
            List<String> orientIds = ListUtils.mapToList(advertOrientationService.selectIdsByAdvertId(req.getId()), Object::toString);
            for (SlotConfig slotConfig : slotConfigs) {
                if (isShuntRedirect(slotConfig.getRedirectType()) && slotRedirectValidateService.isContainsAdvert(JSON.parseArray(slotConfig.getRedirectValue(), ShuntRedirectItem.class), advertIdStr)
                        || isAreaTargetRedirect(slotConfig.getRedirectType()) && slotRedirectValidateService.isContainsAdvert(slotConfig.getRedirectValue(), advertIdStr)) {
                    slotIds.add(slotConfig.getSlotId());
                    continue;
                }
                for (String orientId : orientIds) {
                    if (isShuntRedirect(slotConfig.getRedirectType()) && slotRedirectValidateService.isContainsAdvertOrient(JSON.parseArray(slotConfig.getRedirectValue(), ShuntRedirectItem.class), orientId)
                            || isAreaTargetRedirect(slotConfig.getRedirectType()) && slotRedirectValidateService.isContainsAdvertOrient(slotConfig.getRedirectValue(), orientId)) {
                        slotIds.add(slotConfig.getSlotId());
                        break;
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(slotIds)) {
                return AjaxResult.error(Joiner.on(",").join(slotIds) + "等广告位配置了该直投广告，暂时不能标记无效");
            }
        }
        // 检查近30天是否有数据
        AdvertDayData param = new AdvertDayData();
        param.setAdvertId(req.getId());
        param.setStartDate(DateUtil.offsetDay(new Date(), -30));
        List<AdvertDayData> list = advertDayDataService.selectAdvertDayDataList(param);
        if (CollectionUtils.isNotEmpty(list)) {
            return AjaxResult.error("广告近30天有投放记录，暂时不能标记无效");
        }
        return toAjax(advertService.invalidateAdvert(req.getId()));
    }
}
