package com.ruoyi.web.controller.open;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.ServletUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 七猫接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/07/28
 */
@Slf4j
@RestController
@RequestMapping("/open/qimao")
public class QimaoController {

    /**
     * 七猫点击监测接口
     */
    @CrossOrigin
    @GetMapping("/click")
    public void click() {
        log.info("七猫回调，param={}", JSON.toJSONString(ServletUtils.getRequest().getParameterMap()));
    }
}
