package com.ruoyi.web.controller.open;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.system.service.engine.DeviceUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 百度接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2023/06/12
 */
@Slf4j
@RestController
@RequestMapping("/open/baidu")
public class BaiduController {

    @Autowired
    private DeviceUidService deviceUidService;

    /**
     * 百度回调接口
     */
    @CrossOrigin
    @GetMapping("/event")
    public void event() {
        HttpServletRequest request = ServletUtils.getRequest();
        if (null == request) {
            return;
        }
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        log.info("百度回调，param={}", JSON.toJSONString(paramMap));

        deviceUidService.cacheParameter(paramMap);
    }
}
