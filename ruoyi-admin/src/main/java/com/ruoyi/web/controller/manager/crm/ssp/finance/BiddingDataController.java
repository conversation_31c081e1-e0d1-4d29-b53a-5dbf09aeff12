package com.ruoyi.web.controller.manager.crm.ssp.finance;

import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.slot.BiddingDayDataEntity;
import com.ruoyi.system.req.datashow.BiddingDataImportReq;
import com.ruoyi.system.service.slot.BiddingDayDataService;
import com.ruoyi.system.vo.datashow.BiddingDataExcelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 投流数据
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Slf4j
@RestController
@RequestMapping("/crm/ssp/data/bidding")
public class BiddingDataController {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private BiddingDayDataService biddingDayDataService;

    /**
     * EXCEL导入投流账户消耗
     */
    @PostMapping("importByExcel")
    public Result<List<BiddingDataExcelVO>> importByExcel(BiddingDataImportReq req) {
        if (Objects.isNull(req.getFile())) {
            return ResultBuilder.fail(ErrorCode.ARGS);
        }
        if (null == req.getSlotId()) {
            return ResultBuilder.fail("广告位ID不能为空");
        }

        LoginUser user = SecurityUtils.getLoginUser();
        Long operatorId = user.getCrmAccountId();
        String operatorName = user.getUserName();
        List<BiddingDataExcelVO> list = new ArrayList<>();

        try {
            ExcelUtil.readBySax(req.getFile().getInputStream(), 0, (sheetIndex, rowIndex, rows) -> {
                if (Objects.equals(rowIndex, 0L) || Objects.isNull(rows.get(0))) {
                    return;
                }
                String curDate = String.valueOf(rows.get(0));
                String advertiserId = Objects.isNull(rows.get(1)) ? "" : String.valueOf(rows.get(1));
                String consume = Objects.isNull(rows.get(3)) ? "0" : String.valueOf(rows.get(3));
                Long slotId = NumberUtils.defaultLong(redisCache.getCacheObject(EngineRedisKeyFactory.K088.join(advertiserId)), req.getSlotId());

                BiddingDataExcelVO excelVO = new BiddingDataExcelVO();
                excelVO.setCurDate(curDate);
                excelVO.setAdvertiserId(advertiserId);
                excelVO.setSlotId(slotId);
                excelVO.setConsume(consume);

                try {
                    // 更新投流消耗数据
                    BiddingDayDataEntity param = new BiddingDayDataEntity();
                    param.setCurDate(DateUtil.parseDate(curDate));
                    param.setAdvertiserId(advertiserId);
                    param.setSlotId(slotId);
                    param.setConsume(new BigDecimal(consume).multiply(new BigDecimal(100)).longValue());
                    param.setOperatorId(operatorId);
                    param.setOperatorName(operatorName);
                    excelVO.setSuccess(biddingDayDataService.updateConsume(param));
                } catch (Exception e) {
                    log.error("更新投流消耗数据异常, rows={}", JSON.toJSONString(rows), e);
                    excelVO.setSuccess(false);
                }
                list.add(excelVO);
            });
        } catch (Exception e) {
            log.error("解析excel异常,e:", e);
            return ResultBuilder.fail("Excel解析异常");
        }
        return ResultBuilder.success(list);
    }
}
