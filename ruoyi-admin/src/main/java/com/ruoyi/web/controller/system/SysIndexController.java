package com.ruoyi.web.controller.system;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页
 *
 * <AUTHOR>
 */
@RestController
public class SysIndexController {

    /**
     * 停机标识
     */
    private static boolean isStop = false;

    /**
     * 访问首页，提示语
     */
    @RequestMapping("/")
    public String index() {
        return StringUtils.format("Hello World!");
    }

    /**
     * 健康检查
     */
    @RequestMapping(value = "/health", method = RequestMethod.HEAD)
    public ResponseEntity<Void> health() {
        if (isStop) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
        return ResponseEntity.ok().build();
    }

    /**
     * 设置停机标识
     */
    @RequestMapping("/stop")
    public boolean stop() {
        return isStop = true;
    }
}
