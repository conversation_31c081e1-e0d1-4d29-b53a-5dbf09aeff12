package com.ruoyi.web.controller.manager.crm.ssp.activity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.codec.Base62;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.activity.skin.JsTemplate;
import com.ruoyi.system.domain.manager.Prize;
import com.ruoyi.system.entity.activity.Activity;
import com.ruoyi.system.entity.activity.ActivitySkin;
import com.ruoyi.system.req.activity.ActivityIdReq;
import com.ruoyi.system.req.activity.ActivityReq;
import com.ruoyi.system.service.manager.ActivityService;
import com.ruoyi.system.service.manager.ActivitySkinService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.vo.activity.ActivityCustomerVO;
import com.ruoyi.system.vo.activity.ActivityVO;
import com.ruoyi.system.vo.activity.JsTemplateVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.constant.BizConstants.OSS_URL;

/**
 * [CRM后台]活动工具
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/activity")
public class ActivityController extends BaseController {

    /**
     * 规则默认背景图
     */
    private static final String DEFAULT_RULE_BG = OSS_URL + "2021/iqiyi/rule_bg.png";

    @Autowired
    private ActivityService activityService;

    @Autowired
    private ActivitySkinService activitySkinService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询活动工具列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ActivityReq req) {
        startPage();
        Activity param = BeanUtil.copyProperties(req, Activity.class);
        List<Activity> activities = activityService.selectActivityList(param);
        if (CollectionUtils.isEmpty(activities)) {
            return getDataTable(Collections.emptyList());
        }

        // 查询皮肤映射
        Map<String, ActivitySkin> skinMap = activitySkinService.selectSkinMap();
        // 查询活动默认域名
        String activityUrl = sysConfigService.selectConfigByKey(BizConfigEnum.DEFAULT_ACT_URL.getKey());

        // 构造返回列表
        return getDataTable(PageInfoUtils.dto2Vo(activities, activity -> {
            ActivityVO activityVO = new ActivityVO();
            activityVO.setId(activity.getId());
            activityVO.setActivityName(activity.getActivityName());
            activityVO.setSkinCode(activity.getSkinCode());
            activityVO.setAutoJoin(activity.getAutoJoin());
            activityVO.setOperatorId(activity.getOperatorId());
            activityVO.setOperatorName(activity.getOperatorName());
            activityVO.setGmtCreate(activity.getGmtCreate());
            Optional.ofNullable(skinMap.get(activity.getSkinCode())).ifPresent(skin -> {
                activityVO.setSkinName(skin.getSkinName());
                activityVO.setPreviewUrl(activityUrl + skin.getRedirectPath() + "/" + Base62.encode(String.valueOf(activity.getId())) + "?mode=preview");
            });
            return activityVO;
        }));
    }

    /**
     * 获取活动工具详细信息
     */
    @GetMapping(value = "/{id}")
    public Result<ActivityVO> getInfo(@PathVariable("id") Long id) {
        Activity activity = activityService.selectActivityById(id);
        if (null == activity) {
            return ResultBuilder.success(null);
        }

        ActivityVO activityVO = new ActivityVO();
        activityVO.setId(activity.getId());
        activityVO.setActivityName(activity.getActivityName());
        activityVO.setSkinCode(activity.getSkinCode());
        activityVO.setSkinName(activitySkinService.selectSkinNameBySkinCode(activity.getSkinCode()));
        activityVO.setAutoJoin(activity.getAutoJoin());
        activityVO.setRuleDesc(activity.getRuleDesc());
        activityVO.setIcpNo(activity.getIcpNo());
        activityVO.setCustomerConfig(JSON.parseObject(activity.getCustomerConfig(), ActivityCustomerVO.class));
        if (StringUtils.isNotBlank(activity.getPrizes())) {
            activityVO.setPrizeList(JSON.parseArray(activity.getPrizes(), Prize.class));
        }
        if (StringUtils.isNotBlank(activity.getJsTemplate())) {
            activityVO.setJsTemplateVO(JSON.parseObject(activity.getJsTemplate(), JsTemplate.class));
        } else {
            ActivitySkin skin = activitySkinService.selectBySkinCode(activity.getSkinCode());
            activityVO.setJsTemplateVO(JSON.parseObject(skin.getJsTemplate(), JsTemplate.class));
        }
        if (null != activityVO.getJsTemplateVO() && StringUtils.isBlank(activityVO.getJsTemplateVO().getRuleBg())) {
            activityVO.getJsTemplateVO().setRuleBg(DEFAULT_RULE_BG);
        }
        return ResultBuilder.success(activityVO);
    }

    /**
     * 获取活动工具基础信息
     */
    @GetMapping(value = "/simple/{id}")
    public Result<ActivityVO> getSimpleInfo(@PathVariable("id") Long id) {
        Activity activity = activityService.selectActivityById(id);
        if (null == activity) {
            return ResultBuilder.success(null);
        }

        ActivityVO activityVO = new ActivityVO();
        activityVO.setId(activity.getId());
        activityVO.setActivityName(activity.getActivityName());
        return ResultBuilder.success(activityVO);
    }

    /**
     * 获取活动皮肤模板
     */
    @GetMapping(value = "/getJsTemplate")
    public Result<JsTemplateVO> getJsTemplate(String skinCode) {
        ActivitySkin skin = activitySkinService.selectBySkinCode(skinCode);
        if (null != skin && StringUtils.isNotBlank(skin.getJsTemplate())) {
            return ResultBuilder.success(JSON.parseObject(skin.getJsTemplate(), JsTemplateVO.class));
        }
        return ResultBuilder.success(new JsTemplateVO());
    }

    /**
     * 新增活动工具
     */
    @Log(title = "活动工具", businessType = BusinessType.INSERT)
    @PostMapping
    public Result<Boolean> add(@RequestBody ActivityReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        Activity activity = new Activity();
        activity.setSkinCode(req.getSkinCode());
        activity.setActivityName(req.getActivityName());
        activity.setAutoJoin(req.getAutoJoin());
        activity.setRuleDesc(req.getRuleDesc());
        activity.setOperatorId(user.getCrmAccountId());
        activity.setOperatorName(user.getUserName());
        activity.setIcpNo(req.getIcpNo());
        if (CollectionUtils.isNotEmpty(req.getPrizeList())) {
            activity.setPrizes(JSON.toJSONString(req.getPrizeList()));
        }
        activity.setCustomerConfig(JSON.toJSONString(req.getCustomerConfig()));

        // 活动皮肤
        ActivitySkin skin = activitySkinService.selectBySkinCode(activity.getSkinCode());
        if (null != skin && null != req.getJsTemplate()) {
            if (StringUtils.isBlank(req.getJsTemplate().getRuleBg())) {
                req.getJsTemplate().setRuleBg(null);
            }
            JsTemplate jsTemplate = JSON.parseObject(skin.getJsTemplate(), JsTemplate.class);
            if (null == jsTemplate) {
                jsTemplate = new JsTemplate();
            }
            BeanUtil.copyProperties(req.getJsTemplate(), jsTemplate, CopyOptions.create().ignoreNullValue().ignoreError());
            activity.setJsTemplate(JSON.toJSONString(jsTemplate));
        }

        // 新增活动
        boolean result = activityService.insertActivity(activity) > 0;
        return ResultBuilder.success(result);
    }

    /**
     * 复制活动工具
     */
    @Log(title = "活动工具", businessType = BusinessType.INSERT)
    @PostMapping("copy")
    public Result<Long> copy(@RequestBody @Validated ActivityIdReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        Activity activity = activityService.selectActivityById(req.getId());
        if (null == activity) {
            return ResultBuilder.fail("无效的活动ID");
        }
        String skinName = activitySkinService.selectSkinNameBySkinCode(activity.getSkinCode());
        if (StrUtil.contains(skinName, "已废弃")) {
            return ResultBuilder.fail("活动皮肤已废弃，请勿使用和复制该活动");
        }
        Activity newActivity = BeanUtil.copyProperties(activity, Activity.class);
        newActivity.setActivityName(StrUtil.sub(activity.getActivityName() + "-复制", 0, 32));
        newActivity.setOperatorId(user.getCrmAccountId());
        newActivity.setOperatorName(user.getUserName());
        activityService.insertActivity(newActivity);
        return ResultBuilder.success(newActivity.getId());
    }

    /**
     * 修改活动工具
     */
    @Log(title = "活动工具", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result<Boolean> edit(@RequestBody ActivityReq req) {
        Activity activity = new Activity();
        activity.setId(req.getId());
        activity.setActivityName(req.getActivityName());
        activity.setAutoJoin(req.getAutoJoin());
        activity.setRuleDesc(req.getRuleDesc());
        activity.setIcpNo(req.getIcpNo());
        if (CollectionUtils.isNotEmpty(req.getPrizeList())) {
            activity.setPrizes(JSON.toJSONString(req.getPrizeList()));
        }

        activity.setCustomerConfig(JSON.toJSONString(req.getCustomerConfig()));
        // 活动皮肤模板
        if (null != req.getJsTemplate()) {
            JsTemplate jsTemplate = null;
            Activity existActivity = activityService.selectActivityById(req.getId());
            if (null != existActivity && StringUtils.isNotBlank(existActivity.getJsTemplate())) {
                jsTemplate = JSON.parseObject(existActivity.getJsTemplate(), JsTemplate.class);
            }
            if (null == jsTemplate) {
                ActivitySkin skin = activitySkinService.selectBySkinCode(req.getSkinCode());
                if (null != skin && StringUtils.isNotBlank(skin.getJsTemplate())) {
                    jsTemplate = JSON.parseObject(skin.getJsTemplate(), JsTemplate.class);
                }
            }
            if (null == jsTemplate) {
                return ResultBuilder.fail("获取活动皮肤模板失败");
            }
            if (StringUtils.isBlank(req.getJsTemplate().getRuleBg())) {
                req.getJsTemplate().setRuleBg(null);
            }
            BeanUtil.copyProperties(req.getJsTemplate(), jsTemplate, CopyOptions.create().ignoreNullValue().ignoreError());
            activity.setJsTemplate(JSON.toJSONString(jsTemplate));
        }

        // 更新活动
        boolean result = activityService.updateActivity(activity) > 0;
        return ResultBuilder.success(result);
    }

    /**
     * 生成活动预览链接
     */
    @Log(title = "活动工具", businessType = BusinessType.UPDATE)
    @PostMapping("/generatePreviewUrl")
    public Result<String>  generatePreviewUrl(@RequestBody ActivityReq req) {
        // 查询活动皮肤
        ActivitySkin skin = activitySkinService.selectBySkinCode(req.getSkinCode());
        if (null == skin) {
            return ResultBuilder.fail("活动皮肤不存在");
        }

        // 随机生成一个不存在的活动ID
        Long tmpActivityId = RandomUtil.randomLong(100000, 999999);

        // 构造预览数据
        ActivityVO activityVO = new ActivityVO();
        activityVO.setId(tmpActivityId);
        activityVO.setActivityName(req.getActivityName());
        activityVO.setSkinCode(req.getSkinCode());
        activityVO.setAutoJoin(req.getAutoJoin());
        activityVO.setJoinTimes(8);
        activityVO.setRuleDesc(req.getRuleDesc());
        activityVO.setIcpNo(req.getIcpNo());
        activityVO.setPrizeList(req.getPrizeList());
        if (null != req.getJsTemplate()) {
            if (StringUtils.isBlank(req.getJsTemplate().getRuleBg())) {
                req.getJsTemplate().setRuleBg(null);
            }
            JSONObject jsTemplate = JSON.parseObject(skin.getJsTemplate());
            if (null == jsTemplate) {
                jsTemplate = new JSONObject();
            }
            BeanUtil.copyProperties(req.getJsTemplate(), jsTemplate, CopyOptions.create().ignoreNullValue().ignoreError());
            activityVO.setJsTemplate(JSON.toJSONString(jsTemplate));
        }

        // 缓存预览活动，1小时
        String key = EngineRedisKeyFactory.K064.join(tmpActivityId);
        redisCache.setCacheObject(key, JSON.toJSONString(activityVO), 1, TimeUnit.HOURS);

        // 构造预览链接
        String activityUrl = sysConfigService.selectConfigByKey(BizConfigEnum.DEFAULT_ACT_URL.getKey());
        String previewUrl = activityUrl + skin.getRedirectPath() + "/" + Base62.encode(String.valueOf(tmpActivityId)) + "?mode=preview";
        return ResultBuilder.success(previewUrl);
    }
}
