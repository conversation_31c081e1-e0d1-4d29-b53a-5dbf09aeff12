package com.ruoyi.web.controller.manager.wis.agent.data;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.agent.AgentClientCpcDataExcelBo;
import com.ruoyi.system.bo.agent.AgentClientCpcLandpageDataExcelBo;
import com.ruoyi.system.manager.advertiser.AdvertiserDataManager;
import com.ruoyi.system.req.advertiser.data.CrmAdvertiserCpcDataReq;
import com.ruoyi.system.req.agent.data.AgentClientCpcDataReq;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.vo.advertiser.finance.CrmAdvertiserCpcDataListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.isAgent;

/**
 * [代理商平台]数据查看
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@Slf4j
@RestController
@RequestMapping("/wis/agent/data")
public class WisAgentDataController extends BaseController {

    @Autowired
    private AdvertiserDataManager advertiserDataManager;

    @Autowired
    private AgentService agentService;
    @Autowired
    private RedisAtomicClient redisAtomicClient;

    /**
     * 获取cpc数据列表(日期维度)
     */
    @GetMapping("/cpcList")
    public TableDataInfo<CrmAdvertiserCpcDataListVO> cpcList(AgentClientCpcDataReq req) {
        CrmAdvertiserCpcDataReq dataReq = convertTo(req);
        PageInfo<CrmAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.batchSelectCpcDataList(dataReq,false);
        return getDataTable(pageInfo);
    }

    /**
     * 获取cpc数据列表(落地页链接维度)
     */
    @GetMapping("/cpcLandpageList")
    public TableDataInfo<CrmAdvertiserCpcDataListVO> cpcLandpageList(AgentClientCpcDataReq req) {
        CrmAdvertiserCpcDataReq dataReq = convertTo(req);
        PageInfo<CrmAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.batchSelectCpcLandpageDataList(dataReq,false);
        return getDataTable(pageInfo);
    }

    /**
     * 导出广告主cpc数据(日期维度)
     */
    @Log(title = "代理商后台cpc表单记录", businessType = BusinessType.EXPORT)
    @GetMapping("cpcExport")
    public AjaxResult cpcExport(AgentClientCpcDataReq req) {
        CrmAdvertiserCpcDataReq dataReq = convertTo(req);
        PageInfo<CrmAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.batchSelectCpcDataList(dataReq,true);
        List<AgentClientCpcDataExcelBo> cpcDataExcelBos = pageInfo.getList().stream().map(data -> {
            AgentClientCpcDataExcelBo excelBo = BeanUtil.copyProperties(data, AgentClientCpcDataExcelBo.class);
            excelBo.setConsumeAmount(NumberUtils.fenToYuan(data.getConsumeAmount()));
            return excelBo;
        }).collect(Collectors.toList());
        String fileName = UUID.randomUUID().toString() + "_消费记录数据.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, AgentClientCpcDataExcelBo.class).sheet("消费记录数据").doWrite(cpcDataExcelBos);
        return AjaxResult.success(fileName);
    }

    /**
     * 导出广告主cpc数据(落地页链接维度)
     */
    @Log(title = "代理商后台cpc表单记录", businessType = BusinessType.EXPORT)
    @GetMapping("cpcLandpageExport")
    public AjaxResult cpcLandpageExport(AgentClientCpcDataReq req) {
        Long crmAccountId = SecurityUtils.getLoginUser().getCrmAccountId();
        try (RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K033.join(crmAccountId), 10 * 60 * 60)) {
            if (lock == null) {
                return AjaxResult.error("正在导出，请耐心等待");
            }
            CrmAdvertiserCpcDataReq dataReq = convertTo(req);
            PageInfo<CrmAdvertiserCpcDataListVO> pageInfo = advertiserDataManager.batchSelectCpcLandpageDataList(dataReq, true);
            List<AgentClientCpcLandpageDataExcelBo> cpcDataExcelBos = pageInfo.getList().stream().map(data -> {
                AgentClientCpcLandpageDataExcelBo excelBo = BeanUtil.copyProperties(data, AgentClientCpcLandpageDataExcelBo.class);
                excelBo.setConsumeAmount(NumberUtils.fenToYuan(data.getConsumeAmount()));
                return excelBo;
            }).collect(Collectors.toList());
            String fileName = UUID.randomUUID().toString() + "_消费记录数据.xlsx";
            String filePath = RuoYiConfig.getDownloadPath() + fileName;
            EasyExcel.write(filePath, AgentClientCpcLandpageDataExcelBo.class).sheet("消费记录数据").doWrite(cpcDataExcelBos);
            return AjaxResult.success(fileName);
        } catch (Exception e) {
            log.error("代理商后台cpc表单记录导出异常,e:", e);
            return AjaxResult.error("导出失败,请联系系统管理员");
        }

    }

    private CrmAdvertiserCpcDataReq convertTo(AgentClientCpcDataReq req) {
        CrmAdvertiserCpcDataReq dataReq = new CrmAdvertiserCpcDataReq();
        dataReq.setStartDate(req.getStartDate());
        dataReq.setEndDate(req.getEndDate());

        LoginUser user = SecurityUtils.getLoginUser();
        if (null == user || !isAgent(user.getMainType())) {
            return dataReq;
        }
        List<Long> advertiserIds = agentService.selectAdvertiserIdsByAgent(user.getCrmAccountId());
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return dataReq;
        }
        if (null != req.getAdvertiserId()) {
            if (!advertiserIds.contains(req.getAdvertiserId())) {
                return dataReq;
            }
            dataReq.setAdvertiserIds(Collections.singletonList(req.getAdvertiserId()));
        } else {
            dataReq.setAdvertiserIds(advertiserIds);
        }
        return dataReq;
    }
}
