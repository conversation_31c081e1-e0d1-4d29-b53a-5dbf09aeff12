package com.ruoyi.web.controller.manager.crm.dsp.advert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.advert.ServingHourEnum;
import com.ruoyi.common.utils.IntListUtils;
import com.ruoyi.system.bo.advert.AdvertOrientBatchUpdateParam;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.req.advert.AdvertOrientationBatchModifyReq;
import com.ruoyi.system.req.advert.AdvertiserOrientationBatchModifyReq;
import com.ruoyi.system.req.advert.BatchUpdateAreaTargetByAdvertReq;
import com.ruoyi.system.req.advert.BatchUpdateAreaTargetByAdvertiserReq;
import com.ruoyi.system.req.advert.BatchUpdateAreaTargetByOrientReq;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * [CRM后台]广告批量操作
 *
 * <AUTHOR>
 * @date 2023-05-10
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/advert/batch")
public class AdvertBatchOperateController {

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private AdvertService advertService;

    /**
     * 地域批量复制(广告)
     */
    @Log(title = "地域批量复制", businessType = BusinessType.UPDATE)
    @PostMapping("batchUpdateAreaTargetByAdvert")
    public Result<Boolean> batchUpdateAreaTargetByAdvert(@RequestBody BatchUpdateAreaTargetByAdvertReq req) {
        if (CollectionUtils.isEmpty(req.getAdvertIds())) {
            return ResultBuilder.fail("广告ID列表不能为空");
        }
        batchUpdateAreaTargetByAdvertIds(req.getAdvertIds(), req.getAreaTarget());
        refreshCacheService.sendRefreshAdvertCacheMsg(req.getAdvertIds());
        return ResultBuilder.success(true);
    }

    /**
     * 地域批量复制(广告主)
     */
    @Log(title = "地域批量复制", businessType = BusinessType.UPDATE)
    @PostMapping("batchUpdateAreaTargetByAdvertiser")
    public Result<Boolean> batchUpdateAreaTargetByAdvertiser(@RequestBody BatchUpdateAreaTargetByAdvertiserReq req) {
        if (CollectionUtils.isEmpty(req.getAdvertiserIds())) {
            return ResultBuilder.fail("广告主ID列表不能为空");
        }
        Advert param = new Advert();
        param.setAdvertiserIds(req.getAdvertiserIds());
        param.setIsInvalid(0);
        List<Long> advertIds = advertService.selectAdvertIds(param);
        batchUpdateAreaTargetByAdvertIds(advertIds, req.getAreaTarget());
        refreshCacheService.sendRefreshAdvertCacheMsg(advertIds);
        return ResultBuilder.success(true);
    }

    /**
     * 地域批量复制(配置)
     */
    @Log(title = "地域批量复制", businessType = BusinessType.UPDATE)
    @PostMapping("batchUpdateAreaTargetByOrient")
    public Result<Boolean> batchUpdateAreaTargetByOrient(@RequestBody BatchUpdateAreaTargetByOrientReq req) {
        if (CollectionUtils.isEmpty(req.getOrientIds())) {
            return ResultBuilder.fail("配置ID列表不能为空");
        }
        advertOrientationService.batchUpdateAreaTarget(req.getOrientIds(), req.getAreaTarget());
        refreshCacheService.sendRefreshAdvertOrientationCacheMsg(req.getOrientIds());
        return ResultBuilder.success(true);
    }

    private void batchUpdateAreaTargetByAdvertIds(List<Long> advertIds, Set<String> areaTarget) {
        List<Long> orientIds = advertOrientationService.selectIdsWithAreaTargetByAdvertIds(advertIds);
        if (CollectionUtils.isEmpty(orientIds)) {
            return;
        }
        advertOrientationService.batchUpdateAreaTarget(orientIds, areaTarget);
        refreshCacheService.sendRefreshAdvertCacheMsg(advertIds);
    }

    /**
     * 批量修改广告定向配置(广告)
     *
     * @param req 广告定向投放配置批量修改请求参数
     * @return 是否成功
     */
    @PostMapping("batchUpdateOrientByAdvert")
    @Log(title = "定向配置批量复制", businessType = BusinessType.UPDATE)
    public Result<Boolean> batchUpdateOrientByAdvert(@RequestBody AdvertOrientationBatchModifyReq req) {
        if (Objects.isNull(req) || CollectionUtils.isEmpty(req.getAdvertIds())) {
            return ResultBuilder.fail("参数不能为空");
        }
        // 投放时段校验
        if (!ServingHourEnum.validate(req.getServingHours())) {
            return ResultBuilder.fail("请输入选择有效的投放时段");
        }
        Boolean update = updateAdvertOrientByAdvertIds(req);
        return ResultBuilder.success(update);
    }

    /**
     * 批量修改广告定向配置(广告主)
     *
     * @param req 广告定向投放配置批量修改请求参数
     * @return 是否成功
     */
    @PostMapping("batchUpdateOrientByAdvertiser")
    @Log(title = "定向配置批量复制", businessType = BusinessType.UPDATE)
    public Result<Boolean> batchUpdateOrientByAdvertiser(@RequestBody AdvertiserOrientationBatchModifyReq req) {
        if (Objects.isNull(req) || CollectionUtils.isEmpty(req.getAdvertiserIds())) {
            return ResultBuilder.fail("参数不能为空");
        }
        // 投放时段校验
        if (!ServingHourEnum.validate(req.getServingHours())) {
            return ResultBuilder.fail("请输入选择有效的投放时段");
        }
        //判断广告主id列表是不是空
        //不是空 查询出来广告主下的所有的广告id 根据广告id查询对应的配置的id
        Advert advert = new Advert();
        advert.setAdvertiserIds(req.getAdvertiserIds());
        advert.setIsInvalid(0);
        List<Long> advertIds = advertService.selectAdvertIds(advert);
        //根据广告id批量修改广告定向配置
        AdvertOrientationBatchModifyReq advertOrientationBatchModifyReq = BeanUtil.copyProperties(req, AdvertOrientationBatchModifyReq.class);
        advertOrientationBatchModifyReq.setAdvertIds(advertIds);
        Boolean updated = updateAdvertOrientByAdvertIds(advertOrientationBatchModifyReq);
        return ResultBuilder.success(updated);
    }

    /**
     * 批量修改广告定向配置(配置)
     *
     * @param req 广告定向投放配置批量修改请求参数
     * @return 是否成功
     */
    @PostMapping("batchUpdateOrientByOrient")
    @Log(title = "定向配置批量复制", businessType = BusinessType.UPDATE)
    public Result<Boolean> batchUpdateOrientByOrient(@RequestBody AdvertOrientationBatchModifyReq req) {
        if (Objects.isNull(req) || CollectionUtils.isEmpty(req.getOrientIds())) {
            return ResultBuilder.fail("参数不能为空");
        }
        // 投放时段校验
        if (!ServingHourEnum.validate(req.getServingHours())) {
            return ResultBuilder.fail("请输入选择有效的投放时段");
        }
        Boolean update = updateAdvertOrientByAdvertIds(req);
        return ResultBuilder.success(update);
    }

    /**
     * 根据广告id批量修改广告定向配置
     *
     * @param req
     * @return 是否修改成功
     */
    private Boolean updateAdvertOrientByAdvertIds(AdvertOrientationBatchModifyReq req) {
        //要修改的配置的列表
        List<Long> updateConfigIds = CollUtil.defaultIfEmpty(req.getOrientIds(), advertOrientationService.selectIdsByAdvertIds(req.getAdvertIds()));
        if (CollectionUtils.isEmpty(updateConfigIds)) {
            return true;
        }
        //转换成对应的数字
        AdvertOrientBatchUpdateParam param = new AdvertOrientBatchUpdateParam();
        param.setIds(updateConfigIds);
        param.setFlowTarget(listToInteger(req.getFlowTargets()));
        param.setDeviceTarget(listToInteger(req.getDeviceTargets()));
        param.setOsTarget(listToInteger(req.getOsTargets()));
        param.setIspTarget(listToInteger(req.getIspTargets()));
        if (Objects.nonNull(req.getServingHours())) {
            param.setServingHour(ServingHourEnum.convertToInteger(req.getServingHours()));
        }
        if (Objects.nonNull(req.getAreaTarget())) {
            param.setAreaTarget(JSON.toJSONString(CollUtil.defaultIfEmpty(req.getAreaTarget(), Collections.emptySet())));
        }
        //修改对应的配置信息
        Boolean update = advertOrientationService.batchUpdateAdvertOrientation(param);
        refreshCacheService.sendRefreshAdvertOrientationCacheMsg(updateConfigIds);
        return update;
    }

    /**
     * list不为空就转成对应的int
     *
     * @param list
     * @return
     */
    private Integer listToInteger(List<Integer> list) {
        if (Objects.isNull(list)) {
            return null;
        } else {
            return IntListUtils.convertToInteger(8, list);
        }
    }
}
