package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.landpage.ImportTypeEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.landpage.ExternalLandpageRecordBo;
import com.ruoyi.system.bo.landpage.ExternalLandpageRecordSelectBo;
import com.ruoyi.system.req.landpage.CrmExternalLandpageRecordExportReq;
import com.ruoyi.system.req.landpage.CrmExternalLandpageRecordReq;
import com.ruoyi.system.service.common.IdCardService;
import com.ruoyi.system.service.landpage.ExternalLandpageFormSendRecordService;
import com.ruoyi.system.service.landpage.ExternalLandpageRecordHistoryService;
import com.ruoyi.system.service.landpage.ExternalLandpageRecordService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.vo.datashow.CrmExternalLandpageRecordHistoryVO;
import com.ruoyi.system.vo.datashow.CrmExternalLandpageRecordVO;
import com.ruoyi.system.vo.landpage.CompanySelectVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.common.usermodel.fonts.FontCharset;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.landpage.ExportColumnTypeEnum.isAllColumn;

/**
 *  [CRM后台]外部落地页表单
 *
 * <AUTHOR>
 * @date 2021-10-09
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/landpage/external")
public class CrmExternalLandpageRecordController extends BaseController {

    @Autowired
    private ExternalLandpageRecordService externalLandpageRecordService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private IdCardService idCardService;

    @Autowired
    private ExternalLandpageRecordHistoryService externalLandpageRecordHistoryService;

    @Autowired
    private ExternalLandpageFormSendRecordService externalLandpageFormSendRecordService;

    /**
     * 广告主下拉列表
     */
    @GetMapping("/advertiserList")
    public Result<List<CompanySelectVO>> advertiserList() {
        Set<Long> advertiserIds = new HashSet<>();
        advertiserIds.addAll(externalLandpageRecordService.selectAdvertiserIds());
        advertiserIds.addAll(externalLandpageFormSendRecordService.selectAdvertiserIds());
        Map<Long, String> advertiserNameMap = accountService.selectCompanyNameMap(new ArrayList<>(advertiserIds));
        return ResultBuilder.success(advertiserIds.stream().sorted(Comparator.reverseOrder()).map(advertiserId -> new CompanySelectVO(advertiserId, advertiserNameMap.get(advertiserId))).collect(Collectors.toList()));
    }

    /**
     * 代理商下拉列表
     */
    @GetMapping("/agentList")
    public Result<List<CompanySelectVO>> agentList() {
        List<Long> agentIds = externalLandpageRecordService.selectAgentIds();
        Map<Long, String> advertiserNameMap = accountService.selectCompanyNameMap(agentIds);
        return ResultBuilder.success(agentIds.stream().sorted(Comparator.reverseOrder()).map(agentId -> new CompanySelectVO(agentId, advertiserNameMap.get(agentId))).collect(Collectors.toList()));
    }

    /**
     * 查询列表
     */
    @GetMapping("/list")
    public TableDataInfo<CrmExternalLandpageRecordVO> list(CrmExternalLandpageRecordReq req) {
        TableSupport.startPage();
        List<ExternalLandpageRecordBo> list = externalLandpageRecordService.selectList(BeanUtil.copyProperties(req, ExternalLandpageRecordSelectBo.class));

        List<Long> accountIds = getAccountIds(list);
        List<Long> recordIds = ListUtils.mapToList(list, ExternalLandpageRecordBo::getId);
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(accountIds);
        Map<Long, List<CrmExternalLandpageRecordHistoryVO>> historyMap = externalLandpageRecordHistoryService.selectMapByOriginRecordId(recordIds);

        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            CrmExternalLandpageRecordVO recordVO = BeanUtil.copyProperties(record, CrmExternalLandpageRecordVO.class);
            record.setIdCard(idCardService.decrypt(record.getIdCard()));
            recordVO.setIdCard(StrUtil.hide(DesensitizedUtil.idCardNum(recordVO.getIdCard(), 6, 4), recordVO.getIdCard().length() - 2, recordVO.getIdCard().length()));
            recordVO.setPhone(DesensitizedUtil.mobilePhone(recordVO.getPhone()));
            recordVO.setAdvertiserName(companyNameMap.get(record.getAdvertiserId()));
            recordVO.setAgentName(companyNameMap.get(record.getAgentId()));
            recordVO.setSendStatus(null == record.getIsSuccess() ? 0 : (1 == record.getIsSuccess() ? 1 : 2));
            recordVO.setSendMsg(getMsgByResp(record.getResp()));
            recordVO.setSendAdvertiserName(companyNameMap.get(record.getSendAdvertiserId()));
            recordVO.setHistory(historyMap.get(record.getId()));
            return recordVO;
        }));
    }

    /**
     * 导出记录
     */
    @Log(title = "外部落地页表单记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(CrmExternalLandpageRecordExportReq req) {
        List<ExternalLandpageRecordBo> list = externalLandpageRecordService.selectList(BeanUtil.copyProperties(req, ExternalLandpageRecordSelectBo.class));

        List<Long> accountIds = getAccountIds(list);
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(accountIds);
        String fileName = UUID.randomUUID().toString() + "_客户数据.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;

        List<List<Object>> dataList = list.stream().map(record -> {
            return getData(record, companyNameMap, req.getColumnType());
        }).collect(Collectors.toList());

        WriteCellStyle headWriteCellStyle = getWriteCellStyle();

        EasyExcel.write(filePath).head(getHeader(req.getColumnType())).registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle,new WriteCellStyle())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet().doWrite(dataList);
        //标记记录已导出
        externalLandpageRecordService.batchUpdateExportStatus(ListUtils.mapToList(list, ExternalLandpageRecordBo::getId));
        return AjaxResult.success(fileName);
    }

    private List<Object> getData(ExternalLandpageRecordBo record, Map<Long, String> companyNameMap, Integer columnType) {
        List<Object> datas = new ArrayList<>();
        if (isAllColumn(columnType)) {
            datas.add(record.getAdvertiserId());
            datas.add(companyNameMap.get(record.getAdvertiserId()));
            datas.add(record.getAgentId() > 0 ? record.getAgentId() : null);
            datas.add(companyNameMap.get(record.getAgentId()));
            datas.add(ImportTypeEnum.getDescByType(record.getImportType()));
            datas.add(record.getExternalNo());
            datas.add(record.getSubmitDate());
            datas.add(record.getName());
            datas.add(idCardService.decrypt(record.getIdCard()));
            datas.add(record.getPhone());
            datas.add(record.getProvince());
            datas.add(record.getCity());
            datas.add(record.getDistrict());
            datas.add(record.getAddress());
            return datas;
        }
        datas.add(record.getExternalNo());
        datas.add(record.getName());
        datas.add(record.getPhone());
        datas.add(StringUtils.join(record.getProvince(),record.getCity(),record.getDistrict(),record.getAddress()));
        return datas;
    }

    private WriteCellStyle getWriteCellStyle() {
        // 表头样式策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 是否换行
        headWriteCellStyle.setWrapped(false);
        // 水平对齐方式
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 垂直对齐方式
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 字体策略
        WriteFont writeFont = new WriteFont();
        // 是否加粗/黑体
        writeFont.setBold(false);
        // 字体颜色
        writeFont.setColor(Font.COLOR_NORMAL);
        // 字体名称
        writeFont.setFontName("宋体");
        // 字体大小
        writeFont.setFontHeightInPoints((short) 11);

        // 设置要使用的字符集
        writeFont.setCharset(FontCharset.DEFAULT.getNativeId());
        headWriteCellStyle.setWriteFont(writeFont);
        return headWriteCellStyle;
    }

    private List<List<String>> getHeader(Integer columnType){
        List<List<String>> list = new ArrayList<>();
        if (isAllColumn(columnType)) {
            //导出全部字段
            list.add(Lists.newArrayList("广告主ID"));
            list.add(Lists.newArrayList("广告主名称"));
            list.add(Lists.newArrayList("代理商ID"));
            list.add(Lists.newArrayList("代理商名称"));
            list.add(Lists.newArrayList("导入类型"));
            list.add(Lists.newArrayList("客户单号"));
            list.add(Lists.newArrayList("接收时间"));
            list.add(Lists.newArrayList("姓名"));
            list.add(Lists.newArrayList("身份证号"));
            list.add(Lists.newArrayList("手机号"));
            list.add(Lists.newArrayList("省份"));
            list.add(Lists.newArrayList("市"));
            list.add(Lists.newArrayList("区"));
            list.add(Lists.newArrayList("详细地址"));
            return list;
        }
        list.add(Lists.newArrayList("客户单号"));
        list.add(Lists.newArrayList("姓名"));
        list.add(Lists.newArrayList("手机号"));
        list.add(Lists.newArrayList("详细地址"));
        return list;
    }

    private List<Long> getAccountIds(List<ExternalLandpageRecordBo> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        Set<Long> accountIds = new HashSet<>();
        records.forEach(record -> {
            accountIds.add(record.getAdvertiserId());
            Optional.ofNullable(record.getAgentId()).ifPresent(accountIds::add);
            Optional.ofNullable(record.getSendAdvertiserId()).ifPresent(accountIds::add);
        });
        return new ArrayList<>(accountIds);
    }

    private String getMsgByResp(String resp) {
        try {
            JSONObject result = JSON.parseObject(StringUtils.defaultString(resp));
            if (null != resp) {
                Integer code = result.getInteger("code");
                Boolean success = result.getBoolean("success");
                if (null != code && !Objects.equals(code, 0) && !Objects.equals(code, 200) || null != success && !success) {
                    return result.getString("msg");
                }
            }
        } catch (Exception ignore) {}
        return null;
    }
}
