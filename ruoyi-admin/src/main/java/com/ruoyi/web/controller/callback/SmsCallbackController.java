package com.ruoyi.web.controller.callback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.SmsChannelEnum;
import com.ruoyi.common.enums.SmsStatusEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity;
import com.ruoyi.system.entity.sms.SmsTemplateEntity;
import com.ruoyi.system.manager.sms.LiuziSmsManager;
import com.ruoyi.system.req.callback.SmsCallbackBaiWuReq;
import com.ruoyi.system.req.callback.SmsCallbackChuanZhenReq;
import com.ruoyi.system.req.callback.SmsCallbackFengXueYunReq;
import com.ruoyi.system.req.callback.SmsCallbackGanAnReq;
import com.ruoyi.system.req.callback.SmsCallbackReq;
import com.ruoyi.system.req.callback.SmsCallbackRuiZhuoReq;
import com.ruoyi.system.req.callback.SmsCallbackZhangRongReq;
import com.ruoyi.system.service.sms.LiuziSmsSendRecordService;
import com.ruoyi.system.service.sms.SmsTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 短信回调
 *
 * <AUTHOR>
 * @date 2022/12/7 11:26
 */
@Slf4j
@RestController
@RequestMapping("sms")
public class SmsCallbackController {

    @Autowired
    private LiuziSmsSendRecordService liuziSmsSendRecordService;

    @Autowired
    private LiuziSmsManager liuziSmsManager;

    @Autowired
    private SmsTemplateService smsTemplateService;

    /**
     * 助通短信回调
     *
     * @param reqs
     * @return
     */
    @PostMapping("callback")
    public String callback(@RequestBody List<SmsCallbackReq> reqs) {
        log.info("助通短信回调内容,msg:{}", JSON.toJSONString(reqs));
        if (CollectionUtils.isEmpty(reqs)) {
            return "SUCCESS";
        }
        reqs.forEach(req -> {
            Integer status = Objects.equals(req.getCode(), "DELIVRD") ? SmsStatusEnum.SUCCESS.getStatus() : SmsStatusEnum.FAIL.getStatus();
            if (!Objects.equals(status, SmsStatusEnum.SUCCESS.getStatus())) {
                log.info("短信状态回调,mobile:{},code:{},msg:{}", req.getMobile(), req.getCode(), req.getMsg());
            }
            Long id = Long.parseLong(req.getExtend());
            if (NumberUtils.isNullOrLteZero(id)) {
                log.error("短信状态回调id为空,extend:{}", req.getExtend());
                return;
            }
            liuziSmsSendRecordService.updateSmsStatusById(id, status);


        });
        return "SUCCESS";
    }

    /**
     * 枫雪云短信回调
     *
     * @param reqs
     * @return
     */
    @PostMapping("callbackFXY")
    public String callbackFXY(@RequestBody List<SmsCallbackFengXueYunReq> reqs) {
        log.info("枫雪云短信回调内容,msg:{}", JSON.toJSONString(reqs));
        if (CollectionUtils.isEmpty(reqs)) {
            return "OK";
        }
        reqs.stream().forEach(req -> {
            Integer status = Objects.equals(req.getStat(), "DELIVRD") ? SmsStatusEnum.SUCCESS.getStatus() : SmsStatusEnum.FAIL.getStatus();
            liuziSmsSendRecordService.updateSmsStatusByMsgIdAndType(SmsChannelEnum.FENG_XUE_YUN.getType(), req.getMid(), status);
        });

        return "SUCCESS";
    }
    /**
     * 赣安短信回调
     *
     * @param reqs
     * @return
     */
    @PostMapping("callbackGA")
    public String callbackGA(@RequestBody List<SmsCallbackGanAnReq> reqs) {
        log.info("赣安短信回调内容,msg:{}", JSON.toJSONString(reqs));
        if (CollectionUtils.isEmpty(reqs)) {
            return "SUCCESS";
        }
        reqs.stream().forEach(req -> {
            Integer status = Objects.equals(req.getCode(), "DELIVRD") ? SmsStatusEnum.SUCCESS.getStatus() : SmsStatusEnum.FAIL.getStatus();
            liuziSmsSendRecordService.updateSmsStatusByMsgIdAndType(SmsChannelEnum.GAN_AN.getType(), req.getTaskid(), status);
            if (!Objects.equals(status, SmsStatusEnum.SUCCESS.getStatus())) {
                log.info("赣安短信发送失败,mobile:{},code:{},msg:{}", req.getMobile(), req.getCode(), req.getMsg());
                checkAndSendOtherChannel(SmsChannelEnum.FENG_XUE_YUN,req.getMobile(),SmsChannelEnum.GAN_AN.getType(),req.getTaskid());
            }
        });

        return "SUCCESS";
    }

    /**
     * 传臻短信回调
     *
     * @param req
     * @return
     */
    @PostMapping("callbackCZ")
    public String callbackCZ(@RequestBody SmsCallbackChuanZhenReq req) {
        log.info("传臻短信回调内容,msg:{}", JSON.toJSONString(req));
        if (CollectionUtils.isEmpty(req.getReports())) {
            return "succ";
        }
        Set<String> taskIds = new HashSet<>();
        req.getReports().stream().forEach(report -> {
            String msgId = report.getTaskid();
            if(taskIds.contains(msgId)){
                return;
            }
            taskIds.add(msgId);
            Integer status = Objects.equals(report.getStat(), "DELIVRD") ? SmsStatusEnum.SUCCESS.getStatus() : SmsStatusEnum.FAIL.getStatus();
            liuziSmsSendRecordService.updateSmsStatusByMsgIdAndType(SmsChannelEnum.CHUAN_ZHEN.getType(), msgId, status);
            if(!Objects.equals(status,SmsStatusEnum.SUCCESS.getStatus())){
                log.info("传臻短信发送失败,mobile:{},stat:{},taskid:{}",report.getMobile(),report.getStat(),report.getTaskid());
                checkAndSendOtherChannel(SmsChannelEnum.GAN_AN,report.getMobile(),SmsChannelEnum.CHUAN_ZHEN.getType(),report.getTaskid());
            }
        });
        return "succ";
    }

    /**
     * 掌榕短信回调
     *
     * @param req
     * @return
     */
    @PostMapping("callbackZR")
    public String callbackZR(SmsCallbackZhangRongReq req) {
        log.info("掌榕短信回调内容,msg:{}", JSON.toJSONString(req));
        if (Objects.isNull(req)) {
            return "SUCCESS";
        }

        Integer status = Objects.equals(req.getDeliverResult(), "DELIVRD") ? SmsStatusEnum.SUCCESS.getStatus() : SmsStatusEnum.FAIL.getStatus();
        String msgId = req.getBatchId();
        liuziSmsSendRecordService.updateSmsStatusByMsgIdAndType(SmsChannelEnum.ZHANG_RONG.getType(), msgId, status);
        if (!Objects.equals(status, SmsStatusEnum.SUCCESS.getStatus())) {
            log.info("掌榕短信发送失败,mobile:{},deliverResult:{}", req.getMobile(), req.getDeliverResult());

        }
        JSONObject object = new JSONObject();
        object.put("code", 0);
        object.put("msg", "SUCCESS");
        return object.toJSONString();
    }

    /**
     * 瑞濯短信回调
     *
     * @param reqs
     * @return
     */
    @PostMapping("callbackRZ")
    public String callbackRZ(@RequestBody List<SmsCallbackRuiZhuoReq> reqs) {

        log.info("瑞濯短信回调内容,msg:{}", JSON.toJSONString(reqs));
        if (CollectionUtils.isEmpty(reqs)) {
            return "OK";
        }
        reqs.stream().filter(req -> Objects.equals(req.getFlag(), 1)).forEach(req -> {
            Integer status = Objects.equals(req.getStat(), "DELIVRD") ? SmsStatusEnum.SUCCESS.getStatus() : SmsStatusEnum.FAIL.getStatus();

            String msgId = req.getMid();
            liuziSmsSendRecordService.updateSmsStatusByMsgIdAndType(SmsChannelEnum.RUI_ZHUO.getType(), msgId, status);
            if (!Objects.equals(status, SmsStatusEnum.SUCCESS.getStatus())) {
                log.info("瑞濯短信发送失败,mobile:{},stat:{},mid:{}", req.getMobile(), req.getStat(), req.getMid());
                checkAndSendZhuTong(req.getMobile(), SmsChannelEnum.RUI_ZHUO.getType(), req.getMid());
            }
        });
        return "OK";
    }

    /**
     * 百悟短信回调
     *
     * @param reqs
     * @return
     */
    @PostMapping("callbackBW")
    public String callbackBW(@RequestBody SmsCallbackBaiWuReq reqs) {

        log.info("百悟短信回调内容,msg:{}", JSON.toJSONString(reqs));
        if (CollectionUtils.isEmpty(reqs.getReports())) {
            return "OK";
        }
        reqs.getReports().stream().forEach(req -> {
            Integer status = Objects.equals(req.getErrorCode(), "0") ? SmsStatusEnum.SUCCESS.getStatus() : SmsStatusEnum.FAIL.getStatus();

            Long id = Long.parseLong(req.getUuid());
            liuziSmsSendRecordService.updateSmsStatusById(id, status);
            if (!Objects.equals(status, SmsStatusEnum.SUCCESS.getStatus())) {
                log.info("百悟短信发送失败,mobile:{},info:{}", req.getMobile(), JSON.toJSONString(req));
                checkAndSendZhuTong(req.getMobile(), SmsChannelEnum.BAI_WU.getType(), reqs.getTransactionId());
            }
        });
        return "OK";
    }

    /**
     * 检查短信是否发送成功，如果都失败，则通过助通发短信
     *
     * @param mobile
     * @param type
     * @param msgId
     */
    private void checkAndSendZhuTong(String mobile, Integer type, String msgId) {
        //查询短信内容
        LiuziSmsSendRecordEntity recordEntity = liuziSmsSendRecordService.selectByTypeAndMsgId(type, msgId);
        if (Objects.isNull(recordEntity)) {
            return;
        }
        String content = recordEntity.getContent();
        Long liuziRecordId = recordEntity.getLiuziRecordId();
        List<LiuziSmsSendRecordEntity> recordEntities = liuziSmsSendRecordService.selectListByLiuziRecordId(liuziRecordId);
        List<LiuziSmsSendRecordEntity> successList = recordEntities.stream().filter(entity -> Objects.equals(entity.getResult(), SmsStatusEnum.SUCCESS.getStatus()) || Objects.equals(entity.getResult(), SmsStatusEnum.SENDING.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(successList)) {
            //检查是否有发送成功的短信，或者正在发送中的
            return;
        }
        //如果所有短信都是发送失败状态，则通过助通发短信
        //根据短信内容查询短信模版
        List<SmsTemplateEntity> smsTemplateEntities = smsTemplateService.selectListByContent(content);
        smsTemplateEntities = smsTemplateEntities.stream().filter(entity -> Objects.equals(entity.getType(), SmsChannelEnum.ZHU_TONG.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(smsTemplateEntities)) {
            log.info("助通短信未配置,无法发送短信,mobile:{},content:{}", mobile, content);
            return;
        }
        SmsTemplateEntity entity = smsTemplateEntities.get(0);
        LiuziSmsSendRecordEntity insertEntity = new LiuziSmsSendRecordEntity();
        insertEntity.setContent(content);
        insertEntity.setLiuziRecordId(liuziRecordId);
        insertEntity.setTpId(entity.getTpId());
        insertEntity.setType(entity.getType());
        insertEntity.setMsgId("");
        liuziSmsSendRecordService.insert(insertEntity);
        String zhutongMsgId = liuziSmsManager.sendSmsZhuTong(mobile, entity.getTpId(), content, insertEntity.getId());
        liuziSmsManager.updateLiuziSmsMsgId(insertEntity.getId(), zhutongMsgId);
    }
    /**
     * 检查短信是否发送成功，如果都失败，则通过助通发短信
     *
     * @param otherChannel 要发送的通道
     * @param mobile
     * @param oldChannel 原通道
     * @param msgId
     */
    private void checkAndSendOtherChannel(SmsChannelEnum otherChannel,String mobile, Integer oldChannel, String msgId) {
        //查询短信内容
        LiuziSmsSendRecordEntity recordEntity = liuziSmsSendRecordService.selectByTypeAndMsgId(oldChannel, msgId);
        if (Objects.isNull(recordEntity)) {
            return;
        }
        String content = recordEntity.getContent();
        Long liuziRecordId = recordEntity.getLiuziRecordId();
        //根据短信内容查询短信模版
        List<SmsTemplateEntity> smsTemplateEntities = smsTemplateService.selectListByContent(content);
        smsTemplateEntities = smsTemplateEntities.stream().filter(entity -> Objects.equals(entity.getType(), otherChannel.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(smsTemplateEntities)) {
            log.info("短信未配置,无法发送短信,mobile:{},content:{}", mobile, content);
            return;
        }
        SmsTemplateEntity entity = smsTemplateEntities.get(0);
        LiuziSmsSendRecordEntity insertEntity = new LiuziSmsSendRecordEntity();
        insertEntity.setContent(content);
        insertEntity.setLiuziRecordId(liuziRecordId);
        insertEntity.setTpId(entity.getTpId());
        insertEntity.setType(entity.getType());
        insertEntity.setMsgId("");
        liuziSmsSendRecordService.insert(insertEntity);
        String msgIdStr = "";
        if(Objects.equals(otherChannel,SmsChannelEnum.GAN_AN)){
            msgIdStr = liuziSmsManager.sendSmsGanAn(mobile, content);
        }else if(Objects.equals(otherChannel,SmsChannelEnum.FENG_XUE_YUN)) {
            msgIdStr = liuziSmsManager.sendSmsFengXueYun(mobile, content);
        }
        liuziSmsManager.updateLiuziSmsMsgId(insertEntity.getId(), msgIdStr);
    }
}
