package com.ruoyi.web.controller.open;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.ServletUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 芒果接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/08/16
 */
@Slf4j
@RestController
@RequestMapping("/open/mgtv")
public class MgtvController {

    /**
     * 芒果点击监测接口
     */
    @CrossOrigin
    @GetMapping("/click")
    public void click() {
        log.info("芒果TV回调，param={}", JSON.toJSONString(ServletUtils.getRequest().getParameterMap()));
    }
}
