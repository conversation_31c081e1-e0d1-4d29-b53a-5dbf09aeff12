package com.ruoyi.web.controller.engine;

import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.system.bo.kefu.IntendedFormBo;
import com.ruoyi.system.bo.kefu.IntendedFormImportErrorBo;
import com.ruoyi.system.domain.adengine.AppCacheDto;
import com.ruoyi.system.domain.manager.AreaVO;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.req.kefu.ImportFileReq;
import com.ruoyi.system.req.kefu.ImportManualReq;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.engine.cache.AppCacheService;
import com.ruoyi.system.service.kefu.IntendedFormService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.vo.kefu.IntendedFormImportResultVO;
import com.ruoyi.system.vo.kefu.KefuCompanyVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 客服意向客户回传
 *
 * <AUTHOR>
 * @date 2023/04/10
 */
@Slf4j
@RestController
@RequestMapping("/kefu/intendedForm")
public class KefuIntendedFormController {

    @Autowired
    private AppCacheService appCacheService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private IntendedFormService intendedFormService;

    @Autowired
    private AreaService areaService;

    /**
     * 查询客服公司信息
     */
    @CrossOrigin
    @GetMapping("info")
    public Result<KefuCompanyVO> info(String appKey) {
        AppCacheDto app = appCacheService.getAppCache(appKey);
        if (null == app) {
            return ResultBuilder.fail("未查询到公司信息");
        }
        Account account = accountService.selectAccountById(app.getAccountId());
        if (null == account) {
            return ResultBuilder.fail("未查询到公司信息");
        }

        KefuCompanyVO companyVO = new KefuCompanyVO();
        companyVO.setCompanyName(account.getCompanyName());
        return ResultBuilder.success(companyVO);
    }

    /**
     * 查询地域列表
     */
    @CrossOrigin
    @GetMapping("/areaList")
    public Result<List<AreaVO>> areaList() {
        return ResultBuilder.success(areaService.queryTotalAreaCache());
    }

    /**
     * 批量导入
     */
    @CrossOrigin
    @PostMapping("importFile")
    public Result<IntendedFormImportResultVO> importFile(ImportFileReq req) {
        AppCacheDto app = appCacheService.getAppCache(req.getAppKey());
        if (null == app) {
            return ResultBuilder.fail("无效的应用标识");
        }
        // 解析Excel
        List<IntendedFormBo> list = intendedFormService.analysisExcel(req.getFile());
        if (CollectionUtils.isEmpty(list)) {
            return ResultBuilder.fail("数据不为空");
        }
        int totalSize = list.size();
        // 数据校验
        List<IntendedFormImportErrorBo> errList = intendedFormService.dataVerify(list);
        // 表单处理
        intendedFormService.handleForm(app.getAccountId(), list);

        IntendedFormImportResultVO result = new IntendedFormImportResultVO();
        result.setTotalCount(totalSize);
        result.setErrorCount(errList.size());
        result.setErrors(errList);
        return ResultBuilder.success(result);
    }

    /**
     * 手动导入
     */
    @CrossOrigin
    @PostMapping("importManual")
    public Result<IntendedFormImportResultVO> importManual(@RequestBody ImportManualReq req) {
        AppCacheDto app = appCacheService.getAppCache(req.getAppKey());
        if (null == app) {
            return ResultBuilder.fail("无效的应用标识");
        }
        if (CollectionUtils.isEmpty(req.getList())) {
            return ResultBuilder.fail("数据不为空");
        }
        int totalSize = req.getList().size();
        // 数据校验
        List<IntendedFormImportErrorBo> errList = intendedFormService.dataVerify(req.getList());
        // 表单处理
        intendedFormService.handleForm(app.getAccountId(), req.getList());

        IntendedFormImportResultVO result = new IntendedFormImportResultVO();
        result.setTotalCount(totalSize);
        result.setErrorCount(errList.size());
        result.setErrors(errList);
        return ResultBuilder.success(result);
    }
}
