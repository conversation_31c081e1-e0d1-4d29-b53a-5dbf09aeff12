package com.ruoyi.web.controller.system;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.account.AccountRelationType;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.bo.account.AccountPermissionBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.manager.account.AccountPermissionManager;
import com.ruoyi.system.manager.account.OaStaffManger;
import com.ruoyi.system.req.account.AccountRegisterReq;
import com.ruoyi.system.req.account.EmailReq;
import com.ruoyi.system.req.account.PosternLoginReq;
import com.ruoyi.system.req.account.ResetPwdReq;
import com.ruoyi.system.service.advertiser.AdvertiserQualificationService;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.vo.account.AccountMangerListVO;
import com.ruoyi.system.vo.account.CrmAccountVO;
import com.ruoyi.system.vo.account.LoginUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.ADVERTISER;
import static com.ruoyi.common.enums.account.AccountMainType.AGENT;
import static com.ruoyi.common.enums.account.AccountMainType.PUBLISHER;
import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.account.AccountMainType.isPublisher;
import static com.ruoyi.common.enums.account.SendEmailType.REGISTER;
import static com.ruoyi.common.enums.account.SendEmailType.RESETPWD;
import static com.ruoyi.common.enums.common.WhitelistType.*;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class SysLoginController {

    @Autowired
    private SysLoginService loginService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    @Autowired
    private AccountPermissionManager accountPermissionManager;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private OaStaffManger oaStaffManger;

    @Autowired
    private AccountRelationService accountRelationService;

    @Autowired
    private AdvertiserQualificationService advertiserQualificationService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        if (StringUtils.isBlank(loginBody.getUsername())) {
            return AjaxResult.error("参数错误");
        }
        // 默认流量主
        if (null == loginBody.getMainType()) {
            loginBody.setMainType(PUBLISHER.getType());
        }

        // 生成令牌
        String token = loginService.login(loginBody.getMainType(), loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(), loginBody.getUuid());

        // 构造结果
        AjaxResult ajax = AjaxResult.success();
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 新增用户
     */
    @PostMapping("/register")
    public AjaxResult add(@Validated @RequestBody AccountRegisterReq account) {
        // 默认注册流量主
        if (null == account.getMainType()) {
            account.setMainType(PUBLISHER.getType());
        }

        if (StringUtils.isEmpty(account.getCompanyName())) {
            return AjaxResult.error("公司名称不能为空");
        }
        if (StringUtils.isEmpty(account.getPhone())) {
            return AjaxResult.error("手机号码不能为空");
        }
        if (StringUtils.isEmpty(account.getEmail())) {
            return AjaxResult.error("邮箱不能为空");
        }
        if (StringUtils.isEmpty(account.getCode())) {
            return AjaxResult.error("验证码不能为空");
        }
        if (StringUtils.isEmpty(account.getPasswd())) {
            return AjaxResult.error("密码不能为空");
        }
        if (account.getPasswd().length() < 6 || account.getPasswd().length() > 20) {
            return AjaxResult.error("密码为6-20位英文和数字组合");
        }
        if (!Objects.equals(account.getPasswd(), account.getRepeatPasswd())) {
            return AjaxResult.error("两次密码输入不一致");
        }

        // 校验验证码(测试环境不校验)
        String emailMD5 = Md5Utils.hash(account.getEmail());
        String key = Constants.CAPTCHA_VERIFY_KEY + emailMD5;
        if (!SpringEnvironmentUtils.isTest() && !SpringEnvironmentUtils.isDev()) {
            String value = redisCache.getCacheObject(key);
            if (!Objects.equals(value, account.getCode())) {
                return AjaxResult.error(ErrorCode.E101005);
            }
        }

        int emailExist;
        int companyNameExist;
        int phoneExist = 0;
        if (isAdvertiser(account.getMainType()) || isAgent(account.getMainType())) {
            // 广告主/广告主代理商不能有相同邮箱和公司名称
            emailExist = accountService.checkEmailUnique(account.getEmail(), ADVERTISER.getType()) | accountService.checkEmailUnique(account.getEmail(), AGENT.getType());
            companyNameExist = accountService.checkCompanyNameUnique(account.getCompanyName(), ADVERTISER.getType()) | accountService.checkCompanyNameUnique(account.getCompanyName(), AGENT.getType());
            // 广告主/广告主代理商不校验联系人手机号是否重复
        } else {
            emailExist = accountService.checkEmailUnique(account.getEmail(), account.getMainType());
            companyNameExist = accountService.checkCompanyNameUnique(account.getCompanyName(), account.getMainType());
            phoneExist = accountService.checkPhoneUnique(account.getPhone(), account.getMainType());
        }

        if (UserConstants.EXIST.equals(companyNameExist)) {
            return AjaxResult.error(ErrorCode.E101001);
        } else if (UserConstants.EXIST.equals(phoneExist) && UserConstants.EXIST.equals(emailExist)) {
            return AjaxResult.error(ErrorCode.E101002);
        } else if (UserConstants.EXIST.equals(phoneExist)) {
            return AjaxResult.error(ErrorCode.E101003);
        } else if (UserConstants.EXIST.equals(emailExist)) {
            return AjaxResult.error(ErrorCode.E101004);
        }

        // 广告主/代理商注册校验营业执照注册号
        if (isAdvertiser(account.getMainType()) || isAgent(account.getMainType())) {
            if (StringUtils.isEmpty(account.getBusinessLicense())) {
                return AjaxResult.error("营业执照注册号不能为空");
            }
            if (StringUtils.isEmpty(account.getBusinessLicenseImg())) {
                return AjaxResult.error("请上传营业执照");
            }
            int licenseUnique = accountService.checkBusinessLicenseUnique(account.getBusinessLicense());
            if (UserConstants.EXIST.equals(licenseUnique)) {
                return AjaxResult.error(ErrorCode.E101006);
            }
        }

//        user.setCreateBy(SecurityUtils.getUsername());
        account.setPasswd(SecurityUtils.encryptPassword(account.getPasswd()));

        RedisLock lock = redisAtomicClient.getLock(Constants.REGISTER_REPEAT_KEY + emailMD5, 3);
        if (lock == null) {
            return AjaxResult.error("请勿重复提交");
        }
        Long accountId;
        try {
            accountId = accountService.insertAccount(account);
        } catch (Exception e) {
            log.error("注册异常, company={}, email={}, phone={}", account.getCompanyName(), account.getEmail(), account.getPhone(), e);
            return AjaxResult.error("注册失败");
        } finally {
            lock.unlock();
        }

        //查询商务信息
        if (StringUtils.isNotBlank(account.getBusinessPhone())) {
            String email = oaStaffManger.selectEmailByPhone(account.getBusinessPhone());
            Account crmAccount = accountService.selectCrmUserByEmail(email);
            if(Objects.nonNull(crmAccount)){
                AccountMangerListVO crmAccountList = accountService.getCrmAccountList();
                List<Long> bdIds = crmAccountList.getBdManagers().stream().map(CrmAccountVO::getId).collect(Collectors.toList());
                //账号关联负责人
                Integer relationType = bdIds.contains(crmAccount.getId()) ? AccountRelationType.BD_MANAGER.getType() :  AccountRelationType.OPERATION_MANAGER.getType();
                accountRelationService.updateRelation(crmAccount.getId(),accountId,relationType);
            }

        }

        if (null != accountId) {
            try {
                // 注册成功钉钉通知
                accountService.registerNoticeAsync(accountId, null);
                // 删除验证码缓存
                redisCache.deleteObject(key);
            } catch (Exception e) {
                log.error("注册异常, accountId={}, company={}", accountId, account.getCompanyName(), e);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 发送邮件验证码(注册)
     */
    @PostMapping("/sendVerificationEmail")
    public AjaxResult sendVerificationEmail(@RequestBody EmailReq req) {
        // 校验邮箱
        if (StringUtils.isBlank(req.getEmail()) || !Validator.isEmail(req.getEmail())) {
            return AjaxResult.error("请输入有效的邮箱");
        }
        // 测试环境不发邮件
        if (SpringEnvironmentUtils.isTest() || SpringEnvironmentUtils.isDev()) {
            return AjaxResult.success();
        }
        try {
            loginService.sendVerificationEmail(req.getEmail(), REGISTER);
        } catch (CustomException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            return AjaxResult.error("获取验证码失败");
        }
        return AjaxResult.success();
    }

    /**
     * 发送邮件验证码(忘记密码)
     */
    @PostMapping("/sendResetPwdEmail")
    public AjaxResult sendResetPwdEmail(@RequestBody EmailReq req) {
        // 参数校验
        if (StringUtils.isBlank(req.getEmail()) || !Validator.isEmail(req.getEmail())) {
            return AjaxResult.error("请输入有效的邮箱");
        }
        // 默认流量主
        if (null == req.getMainType()) {
            req.setMainType(PUBLISHER.getType());
        }

        // 检查邮箱是否已注册
        int emailExist = accountService.checkEmailUnique(req.getEmail(), req.getMainType());
        if (!UserConstants.EXIST.equals(emailExist)) {
            return AjaxResult.error("请输入已注册邮箱哦～");
        }

        try {
            loginService.sendVerificationEmail(req.getEmail(), RESETPWD);
        } catch (CustomException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            return AjaxResult.error("获取验证码失败");
        }
        return AjaxResult.success();
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        LoginUser user = SecurityUtils.getLoginUser();
        LoginUserVo userVo = BeanUtil.copyProperties(user,LoginUserVo.class);
        AccountPermissionBo bo = accountPermissionManager.selectByCurAccount();
        AjaxResult ajax = AjaxResult.success();
        userVo.setPermissions(bo.getPermissionList());
        userVo.setUserId(user.getCrmAccountId());
        ajax.put("user", userVo);
        if (isAdvertiser(user.getMainType())) {
            Set<String> tags = advertiserTagService.get(user.getCrmAccountId());
            // 移除置顶标签
            tags.remove("CPC结算");
            ajax.put("tags", tags);
            ajax.put("isOffline", whitelistService.contains(OFFLINE_DATA_ADVERTISER, user.getCrmAccountId()));
            ajax.put("isCpc", advertiserService.isCpcAdvertiser(user.getCrmAccountId()));
            ajax.put("showAdvertSwitch", whitelistService.contains(ADVERT_SWITCH_ADVERTISER, user.getCrmAccountId()));
            ajax.put("articleSwitch", whitelistService.contains(ARTICLE_REFRESH_ADVERTISER, user.getCrmAccountId()));
        } else if (isPublisher(user.getMainType())) {
            userVo.setFcArticleCheckSwitch(whitelistService.contains(FC_ARTICLE_CHECK_TRAFFICMASTER, user.getCrmAccountId()));
            userVo.setShowSlotExposure(whitelistService.contains(WhitelistType.APP_SLOT_EXPOSURE, user.getCrmAccountId()));
        } else {
            ajax.put("tags", Lists.newArrayList());
        }
        // 行业
        if (isAdvertiser(user.getMainType()) || isAgent(user.getMainType())) {
            // 数据隐藏
            ajax.put("dataHide", whitelistService.contains(DATA_HIDE_ADVERTISER, user.getCrmAccountId()));
            // 行业资质
            List<String> industryList = advertiserQualificationService.selectIndustryListByAccountId(user.getCrmAccountId());
            ajax.put("industryList", industryList);
            ajax.put("tabLock", 0);
            if (CollectionUtils.isEmpty(industryList)) {
                Account account = accountService.selectAccountById(user.getCrmAccountId());
                if (null != account && account.getGmtCreate().after(DateUtil.parseDate("2023-04-18"))) {
                    ajax.put("tabLock", 1);
                }
            }
        }
        return ajax;
    }

    /**
     * 校验营业执照注册号
     *
     * @param businessLicense 营业执照注册号
     * @return 校验结果
     */
    @GetMapping("/checkBusinessLicense")
    public AjaxResult checkBusinessLicense(String businessLicense) {
        if (StringUtils.isBlank(businessLicense)) {
            return AjaxResult.error(ErrorCode.ARGS);
        }

        int licenseUnique = accountService.checkBusinessLicenseUnique(businessLicense);
        if (UserConstants.EXIST.equals(licenseUnique)) {
            return AjaxResult.error(ErrorCode.E101006);
        }
        return AjaxResult.success();
    }

    /**
     * 重置密码
     */
    @PostMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody ResetPwdReq req) {
        // 默认流量主
        if (null == req.getMainType()) {
            req.setMainType(PUBLISHER.getType());
        }

        // 参数校验
        if (StringUtils.isEmpty(req.getEmail())) {
            return AjaxResult.error("邮箱不能为空");
        }
        if (StringUtils.isEmpty(req.getCode())) {
            return AjaxResult.error("验证码不能为空");
        }
        if (StringUtils.isEmpty(req.getPasswd())) {
            return AjaxResult.error("密码不能为空");
        }
        if (req.getPasswd().length() < 6 || req.getPasswd().length() > 20) {
            return AjaxResult.error("密码为6-20位英文和数字组合");
        }
        if (!Objects.equals(req.getPasswd(), req.getRepeatPasswd())) {
            return AjaxResult.error("两次密码输入不一致");
        }

        // 校验验证码
        String emailMD5 = Md5Utils.hash(req.getEmail());
        String key = Constants.CAPTCHA_VERIFY_KEY + emailMD5;
        String value = redisCache.getCacheObject(key);
        if (!Objects.equals(value, req.getCode())) {
            return AjaxResult.error("验证码错误");
        }

        // 查询账号信息
        Account param = new Account();
        param.setEmail(req.getEmail());
        param.setMainType(req.getMainType());
        Account account = accountService.selectAccountForLogin(param);
        if (null == account) {
            return AjaxResult.error("请输入已注册邮箱哦～");
        }

        // 更新账号密码
        String password = SecurityUtils.encryptPassword(req.getPasswd());
        int result = accountService.updatePassword(account.getId(), password);
        if (result > 0) {
            redisCache.deleteObject(key);
            return AjaxResult.success("密码重置成功");
        }
        return AjaxResult.error("密码重置失败");
    }

    public static void main(String[] args) {
        System.out.println(SecurityUtils.encryptPassword("s123456"));
    }

    /**
     * 任意门
     */
    @PostMapping("posternLogin")
    public Result<String> posternLogin(@RequestBody @Validated PosternLoginReq req) {
        Account account = accountService.selectAccountById(req.getAccountId());
        LoginUser user = new LoginUser();
        user.setCrmAccountId(account.getId());
        user.setEmail(account.getEmail());
        user.setMainType(account.getMainType());
        user.setAdminType(account.getAdminType());
        user.setUserName(account.getCompanyName());
        user.setPermissions(permissionService.getMenuPermission(user));
        String token = tokenService.createToken(user);
        return ResultBuilder.success(token);
    }
}
