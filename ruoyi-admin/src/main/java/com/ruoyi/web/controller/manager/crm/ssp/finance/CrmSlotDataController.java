package com.ruoyi.web.controller.manager.crm.ssp.finance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.data.AppDataUpdateBO;
import com.ruoyi.system.entity.datashow.SlotActivityHourData;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.req.datashow.AppSlotRequestDataReq;
import com.ruoyi.system.req.datashow.ExportAppSlotRequestDataReq;
import com.ruoyi.system.req.datashow.SlotDataReq;
import com.ruoyi.system.req.datashow.UpdateSlotDataReq;
import com.ruoyi.system.req.slot.SlotDataUpdateReq;
import com.ruoyi.system.service.datasource.SlotActivityHourDataService;
import com.ruoyi.system.service.datasource.SlotAppDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.ActivityService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.slotdata.SlotMonthDataService;
import com.ruoyi.system.vo.datashow.CrmSlotActivityDataExportVO;
import com.ruoyi.system.vo.datashow.CrmSlotDataExportVO;
import com.ruoyi.system.vo.datashow.CrmSlotDataStatisticsVO;
import com.ruoyi.system.vo.datashow.CrmSlotDataVO;
import com.ruoyi.system.vo.datashow.CrmSlotDayDataExportVO;
import com.ruoyi.system.vo.datashow.SlotDataAnalysisExcelResultVO;
import com.ruoyi.system.vo.datashow.SlotDataAnalysisExcelVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * crm广告位数据
 *
 * <AUTHOR>
 * @date 2021-09-02
 */
@Slf4j
@RestController
@RequestMapping("/datashow/crmSlotData")
public class CrmSlotDataController extends BaseController {

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private SlotAppDataService slotAppDataService;

    @Autowired
    private SlotMonthDataService slotMonthDataService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AppService appService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private AccountRelationService accountRelationService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private SlotActivityHourDataService slotActivityHourDataService;

    /**
     * 更新广告位数据
     */
    @Log(title = "更新广告位数据", businessType = BusinessType.UPDATE)
    @PostMapping("updateSlotData")
    public Result updateSlotData(@RequestBody @Validated UpdateSlotDataReq req) {
        SlotDataUpdateReq param = BeanUtil.copyProperties(req, SlotDataUpdateReq.class);
        param.setSlotDataId(req.getId());
        param.setSource(2);
        slotAppDataService.updateSlotAppData(param);
        return ResultBuilder.success();
    }

    /**
     * 查询广告位数据列表 crm专用
     *
     * @param req 请求参数
     */
    @GetMapping("/list")
    public TableDataInfo<CrmSlotDataVO> crmList(SlotDataReq req) {
        PageInfo<CrmSlotDataVO> pageInfo = slotDataService.selectCrmSlotDataList(req, false);
        return getDataTable(pageInfo);
    }

    /**
     * 查询广告位数据汇总
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @GetMapping("/statistic")
    public Result<CrmSlotDataVO> statistic(SlotDataReq req) {
        return ResultBuilder.success(slotDataService.selectStatisticCrmSlotData(req));
    }

    /**
     * 更新媒体反馈广告位请求数据
     *
     * @param req
     * @return
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("updateAppSlotRequestData")
    public Result<Boolean> updateAppSlotRequestData(@RequestBody @Validated AppSlotRequestDataReq req) {
        SlotData slotData = slotDataService.selectSlotDataById(req.getId());
        if (Objects.isNull(slotData)) {
            throw new CustomException(ErrorCode.E108001);
        }
        // 判断月账单数据是否生成
        int slotMonthDayCount = slotMonthDataService.countBySlotIdAndMonth(slotData.getSlotId(), DateUtils.dateTimeMonth(slotData.getCurDate()));
        if (slotMonthDayCount > 0) {
            throw new CustomException(ErrorCode.E108003);
        }

        SlotData updateSlotData = BeanUtil.copyProperties(req, SlotData.class);
        updateSlotData.setId(req.getId());
        slotDataService.updateSlotData(updateSlotData);

        //更新媒体日数据
        slotAppDataService.updateAppData(slotData.getAppId(), slotData.getCurDate());
        return ResultBuilder.success(true);

    }

    /**
     * 导出广告位数据列表 crm专用
     *
     * @param req 请求参数
     */
    @Log(title = "crm广告位数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult crmExport(SlotDataReq req) {
        PageInfo<CrmSlotDataVO> pageInfo = slotDataService.selectCrmSlotDataList(req, true);
        List<CrmSlotDataExportVO> exportList = pageInfo.getList().stream().map(this::convertExportVO).collect(Collectors.toList());
        ExcelUtil<CrmSlotDataExportVO> util = new ExcelUtil<>(CrmSlotDataExportVO.class);
        return util.exportExcel(exportList, "广告位数据");
    }

    /**
     * 导出广告位活动数据
     */
    @Log(title = "crm广告位活动数据", businessType = BusinessType.EXPORT)
    @GetMapping("/exportActivityData")
    public AjaxResult exportActivityData(SlotDataReq req) {
        ExcelUtil<CrmSlotActivityDataExportVO> util = new ExcelUtil<>(CrmSlotActivityDataExportVO.class);

        SlotActivityHourData param = new SlotActivityHourData();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 媒体模糊查询
        if (StringUtils.isNotBlank(req.getAppSearch())) {
            List<Long> appIds = appService.selectAppIdsBySearchValue(req.getAppSearch());
            if (CollectionUtils.isEmpty(appIds)) {
                return util.exportExcel(Collections.emptyList(), "广告位活动数据");
            }
            param.setAppIds(appIds);
        }
        // 广告位模糊查询
        if (StringUtils.isNotBlank(req.getSlotSearch())) {
            List<Long> slotIds = slotService.selectSlotIdsBySearchValue(req.getSlotSearch());
            if (CollectionUtils.isEmpty(slotIds)) {
                return util.exportExcel(Collections.emptyList(), "广告位活动数据");
            }
            param.setSlotIds(slotIds);
        }
        // 负责人查询
        if (CollectionUtils.isNotEmpty(req.getManagerIds())) {
            List<Long> accountIds = accountRelationService.selectBySrcAccountIds(req.getManagerIds());
            if (CollectionUtils.isEmpty(accountIds)) {
                return util.exportExcel(Collections.emptyList(), "广告位活动数据");
            }
            param.setAccountIds(accountIds);
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectSlot();
        if (hasPartialPermission(permission.getType())) {
            param.setSlotIds(mergeParamIds(param.getSlotIds(), permission.getValues()));
            if (CollectionUtils.isEmpty(param.getSlotIds())) {
                return util.exportExcel(Collections.emptyList(), "广告位活动数据");
            }
        }

        List<SlotActivityHourData> list = slotActivityHourDataService.selectSlotActivityHourDataList(param);

        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(ListUtils.mapToList(list, SlotActivityHourData::getSlotId));
        Map<Long, String> appNameMap = appService.selectAppNameMap(ListUtils.mapToList(list, SlotActivityHourData::getAppId));
        Map<Long, String> activityNameMap = activityService.selectActivityNameMap(ListUtils.mapToList(list, SlotActivityHourData::getActivityId));

        List<CrmSlotActivityDataExportVO> exportList = list.stream().map(data -> {
            CrmSlotActivityDataExportVO vo = BeanUtil.copyProperties(data, CrmSlotActivityDataExportVO.class);
            vo.setAppName(String.format("%s（ID:%s）", appNameMap.get(data.getAppId()), data.getAppId()));
            vo.setSlotName(String.format("%s（ID:%s）", slotNameMap.get(data.getSlotId()), data.getSlotId()));
            if (data.getActivityId() > 0) {
                vo.setActivityName(String.format("%s（ID:%s）", activityNameMap.get(data.getActivityId()), data.getActivityId()));
            }
            vo.setLaunchRate(NumberUtils.calculatePercent(data.getAdLaunch(), data.getAdRequest()));
            vo.setJoinRate(NumberUtils.calculatePercent(data.getJoinUv(), data.getActivityRequestUv()));
            vo.setRejoin(NumberUtils.calculateRate(data.getJoinPv(), data.getJoinUv()));
            vo.setCtr(NumberUtils.calculatePercent(data.getAdClickPv(), data.getAdExposurePv()));
            vo.setAdLaunchPerUv(NumberUtils.calculateRate(data.getAdLaunch(), data.getActivityRequestUv()));
            vo.setAdClickPerUv(NumberUtils.calculateRate(data.getAdClickUv(), data.getActivityRequestUv()));
            vo.setConsume(NumberUtils.fenToYuan(data.getConsume()));
            vo.setArpu(NumberUtils.calculateRate(data.getConsume(), data.getAdLaunch() * 100));
            vo.setCvrPv(NumberUtils.calculatePercent(data.getLpClickPv(), data.getLpExposurePv()));
            vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getLpExposureUv()));
            vo.setTheoryCost(NumberUtils.fenToYuan(data.getTheoryCost()));
            return vo;
        }).collect(Collectors.toList());
        return util.exportExcel(exportList, "广告位活动数据");
    }

    /**
     * 导出广告位日账单列表 crm专用
     *
     * @param req 请求参数
     */
    @Log(title = "crm广告位日账单数据", businessType = BusinessType.EXPORT)
    @GetMapping("/exportDayData")
    public Result exportDayData(SlotDataReq req) {
        PageInfo<CrmSlotDataVO> pageInfo = slotDataService.selectCrmSlotDataList(req, true);
        List<CrmSlotDayDataExportVO> exportList = pageInfo.getList().stream().map(data -> convertDayDataExportVO(data)).collect(Collectors.toList());

        ExcelUtil<CrmSlotDayDataExportVO> util = new ExcelUtil<>(CrmSlotDayDataExportVO.class);
        return util.exportExcelResult(exportList, "广告位日账单");
    }

    /**
     * 账号统计广告位数据 总计
     *
     * @param req 请求参数
     * @return 结果
     */
    @GetMapping("statisticsSlotData")
    public Result<CrmSlotDataStatisticsVO> statisticsSlotData(SlotDataReq req) {
        return ResultBuilder.success(slotDataService.statisticsCrmSlotData(req));
    }

    /**
     * 批量导入媒体反馈数据
     *
     * @param reqs
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("exportSlotRequestData")
    public Result<Boolean> exportSlotRequestData(@RequestBody List<ExportAppSlotRequestDataReq> reqs) {
        List<Long> slotIds = reqs.stream().map(ExportAppSlotRequestDataReq::getSlotId).collect(Collectors.toList());
        List<Slot> slots = slotService.selectSimpleSlotByIds(slotIds);
        Map<Long, Slot> slotMap = slots.stream().collect(Collectors.toMap(Slot::getId, Function.identity(), (v1, v2) -> v1));
        List<SlotData> updateList = reqs.stream().map(data -> {
            SlotData slotData = BeanUtil.copyProperties(data, SlotData.class);
            Slot slot = slotMap.get(data.getSlotId());
            if (Objects.isNull(slot)) {
                return null;
            }
            slotData.setAppId(slot.getAppId());
            slotData.setAccountId(slot.getAccountId());
            return slotData;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Lists.partition(updateList, 100).stream().forEach(list -> {
            slotDataService.batchInsertOrUpdateAppSlotRequestData(updateList);
            List<AppDataUpdateBO> updateBOS = list.stream().map(data -> BeanUtil.copyProperties(data, AppDataUpdateBO.class)).collect(Collectors.toList());
            slotAppDataService.batchUpdate(updateBOS);
        });

        return ResultBuilder.success(true);
    }

    /**
     * 解析excel
     *
     * @param file
     * @return
     */
    @PostMapping("analysisExcel")
    public Result<SlotDataAnalysisExcelResultVO> analysisExcel(MultipartFile file) {
        if (Objects.isNull(file)) {
            throw new CustomException(ErrorCode.ARGS);
        }
        List<SlotDataAnalysisExcelVO> list = new ArrayList<>();
        SlotDataAnalysisExcelResultVO result = new SlotDataAnalysisExcelResultVO();
        try {
            Date today = new Date();
            List<String> repetitionList = new ArrayList<>();
            cn.hutool.poi.excel.ExcelUtil.readBySax(file.getInputStream(), 0, (sheetIndex, rowIndex, rows) -> {
                if (Objects.isNull(rows.get(0)) || Objects.equals(rowIndex, 0L)) {
                    return;
                }
                log.info("解析参数,rowIndex:{},rows:{}", rowIndex, rows);
                SlotDataAnalysisExcelVO vo = checkOrAnalysis(today, rows, repetitionList);
                vo.setRow(rowIndex);
                list.add(vo);
            });
            Set<Long> slotIds = list.stream().filter(vo -> StringUtils.isBlank(vo.getErrorMessage())).map(vo -> NumberUtils.parseLong(vo.getSlotId())).collect(Collectors.toSet());
            Map<Long, String> slotNameMap = slotService.selectSlotNameMap(new ArrayList<>(slotIds));
            list.stream().filter(vo -> StringUtils.isBlank(vo.getErrorMessage())).forEach(vo -> {
                if (Objects.isNull(slotNameMap.get(NumberUtils.parseLong(vo.getSlotId())))) {
                    vo.setErrorMessage(vo.getErrorMessage() + "广告位不存在");
                }
            });

            List<SlotDataAnalysisExcelVO> errList = list.stream().filter(vo -> StringUtils.isNotBlank(vo.getErrorMessage())).sorted(Comparator.comparing(SlotDataAnalysisExcelVO::getRow)).collect(Collectors.toList());
            List<SlotDataAnalysisExcelVO> successList = list.stream().filter(vo -> StringUtils.isBlank(vo.getErrorMessage())).sorted(Comparator.comparing(SlotDataAnalysisExcelVO::getRow)).collect(Collectors.toList());
            successList.addAll(0, errList);
            //错误排前面
            result.setErrCount(errList.size());
            result.setExcelVOS(successList);
            ;
            result.setSlotCount(slotIds.size());
            //总数
            result.setDataCount(list.size());

        } catch (Exception e) {
            log.error("解析excel异常,e:", e);
            throw new CustomException(ErrorCode.E108006);
        }

        return ResultBuilder.success(result);
    }


    /**
     * 校验解析数据格式
     *
     * @param today
     * @param rows
     * @return
     */
    private SlotDataAnalysisExcelVO checkOrAnalysis(Date today, List<Object> rows, List<String> repetitionList) {
        String curDateObj = Objects.isNull(rows.get(0)) ? "" : String.valueOf(rows.get(0));
        String slotIdObj = Objects.isNull(rows.get(1)) ? "" : String.valueOf(rows.get(1));
        String exposurePvObj = Objects.isNull(rows.get(2)) ? "" : String.valueOf(rows.get(2));
        String exposureUvObj = Objects.isNull(rows.get(3)) ? "" : String.valueOf(rows.get(3));
        String clickPvObj = Objects.isNull(rows.get(4)) ? "" : String.valueOf(rows.get(4));
        String clickUvObj = Objects.isNull(rows.get(5)) ? "" : String.valueOf(rows.get(5));

        SlotDataAnalysisExcelVO vo = new SlotDataAnalysisExcelVO();
        vo.setCurDate(curDateObj);
        vo.setSlotId(slotIdObj);
        vo.setAppSlotExposurePv(exposurePvObj);
        vo.setAppSlotExposureUv(exposureUvObj);
        vo.setAppSlotClickPv(clickPvObj);
        vo.setAppSlotClickUv(clickUvObj);
        List<String> errMessage = new ArrayList<>();
        try {
            Date curDate = DateUtil.parse(curDateObj, "yyyy-MM-dd");
            if (Objects.isNull(curDate)) {
                errMessage.add("日期格式错误");
            } else if (DateUtil.compare(DateUtil.beginOfDay(today), DateUtil.beginOfDay(curDate)) <= 0) {
                errMessage.add("日期不能超过当日");
            } else {
                //在月账单生成前，可以上传上个月的数据，在月账单生成后，只能上传当月的，每月2号凌晨生成月账单
                DateTime curMonthTime = DateUtil.beginOfMonth(today);
                DateTime todayTime = DateUtil.beginOfDay(today);
                DateTime dataDateTime = DateUtil.beginOfDay(curDate);
                //当天是1号。则只能上传今天和上个月的数据 && 当天非1号，则只能上传当月的数据
                if(!(DateUtil.compare(todayTime,curMonthTime) == 0 && DateUtil.compare(DateUtils.addMonths(curMonthTime,-1),dataDateTime) <= 0) && DateUtil.compare(curMonthTime,dataDateTime) > 0){
                    errMessage.add("月账单已生成无法上传");
                }
            }
        } catch (Exception e) {
            errMessage.add("日期格式错误");
        }
        if (Objects.isNull(NumberUtils.parseLong(slotIdObj))) {
            errMessage.add("广告位id数据异常");
        }
        if (Objects.isNull(NumberUtils.parseInteger(exposurePvObj))) {
            errMessage.add("广告位曝光pv数据异常");
        }
        if (Objects.isNull(NumberUtils.parseInteger(exposureUvObj))) {
            errMessage.add("广告位曝光uv数据异常");
        }
        if (Objects.isNull(NumberUtils.parseInteger(clickPvObj))) {
            errMessage.add("广告位点击pv数据异常");
        }
        if (Objects.isNull(NumberUtils.parseInteger(clickUvObj))) {
            errMessage.add("广告位点击uv数据异常");
        }
        if (repetitionList.contains(vo.getCurDate() + "-" + vo.getSlotId())) {
            errMessage.add("数据重复");
        } else {
            repetitionList.add(vo.getCurDate() + "-" + vo.getSlotId());
        }
        vo.setErrorMessage(Joiner.on(",").join(errMessage));
        return vo;
    }

    /**
     * 数据转换导出vo
     *
     * @param slot 广告位数据
     * @return exportVo
     */
    private CrmSlotDataExportVO convertExportVO(CrmSlotDataVO slot) {
        CrmSlotDataExportVO exportVO = new CrmSlotDataExportVO();
        BeanUtils.copyBeanProp(exportVO, slot);
        exportVO.setTotalConsume(NumberUtils.fenToYuan(slot.getTotalConsume(), "-"));
        exportVO.setNhConsume(NumberUtils.fenToYuan(slot.getNhConsume(), "-"));
        exportVO.setOuterConsume(NumberUtils.fenToYuan(slot.getOuterConsume(), "-"));
        exportVO.setNhCost(NumberUtils.fenToYuan(slot.getNhCost(), "0"));
        exportVO.setOuterCost(NumberUtils.fenToYuan(slot.getOuterCost(), "0"));
        exportVO.setTotalCost(NumberUtils.fenToYuan(slot.getTotalCost(), "0"));
        exportVO.setAppRevenue(NumberUtils.fenToYuan(slot.getAppRevenue()));
        exportVO.setAppName(String.format("%s（ID:%s）", slot.getAppName(), slot.getAppId()));
        exportVO.setSlotName(String.format("%s（ID:%s）", slot.getSlotName(), slot.getSlotId()));
        exportVO.setSlotRequestUvCalculate("");
        exportVO.setBiddingConsume(NumberUtils.fenToYuan(slot.getBiddingConsume()));
        return exportVO;
    }

    /**
     * 数据转换导出vo 日账单
     *
     * @param slot 广告位数据
     * @return exportVo
     */
    private CrmSlotDayDataExportVO convertDayDataExportVO(CrmSlotDataVO slot) {
        CrmSlotDayDataExportVO exportVO = new CrmSlotDayDataExportVO();

        BeanUtils.copyBeanProp(exportVO, slot);
        exportVO.setTotalConsume(NumberUtils.fenToYuan(slot.getTotalConsume(), "-"));
        exportVO.setNhConsume(NumberUtils.fenToYuan(slot.getNhConsume(), "-"));
        exportVO.setOuterConsume(NumberUtils.fenToYuan(slot.getOuterConsume(), "-"));
        exportVO.setNhCost(NumberUtils.fenToYuan(slot.getNhCost(), "-"));
        exportVO.setOuterCost(NumberUtils.fenToYuan(slot.getOuterCost(), "-"));
        exportVO.setTotalCost(NumberUtils.fenToYuan(slot.getTotalCost(), "-"));
        exportVO.setAppRevenue(NumberUtils.fenToYuan(slot.getAppRevenue(), "-"));
        exportVO.setAppName(String.format("%s（ID:%s）", slot.getAppName(), slot.getAppId()));
        exportVO.setSlotName(String.format("%s（ID:%s）", slot.getSlotName(), slot.getSlotId()));
        if (NumberUtils.isNullOrLteZero(exportVO.getSlotRequestPv())) {
            exportVO.setJoinRate("0");
        } else {
            exportVO.setJoinRate(NumberUtils.calculateRate(NumberUtils.defaultInt(slot.getJoinPv()), exportVO.getSlotRequestPv()));
        }
        return exportVO;
    }
}
