package com.ruoyi.web.controller.manager.crm.dsp.advert;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.advert.InnovateLayer;
import com.ruoyi.system.req.advert.InnovateLayerReq;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.service.manager.InnovateLayerService;
import com.ruoyi.system.service.manager.LayerSkinService;
import com.ruoyi.system.service.manager.MaterialService;
import com.ruoyi.system.vo.advert.InnovateLayerVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * [CRM后台]创新弹层
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/layer")
public class InnovateLayerController extends BaseController {

    @Autowired
    private InnovateLayerService innovateLayerService;

    @Autowired
    private LayerSkinService layerSkinService;

    @Autowired
    private MaterialService materialService;

    /**
     * 查询创新弹层列表
     */
    @GetMapping("/list")
    public TableDataInfo<InnovateLayerVO> list(InnovateLayerReq req) {
        startPage();
        List<InnovateLayer> list = innovateLayerService.selectInnovateLayerList(req);
        Map<String, String> skinNameMap = layerSkinService.selectSkinNameMap();
        List<Long> usedLayerIds = materialService.selectUsedLayerId(ListUtils.mapToList(list, InnovateLayer::getId));
        return getDataTable(PageInfoUtils.dto2Vo(list, layer -> {
            InnovateLayerVO layerVO = BeanUtil.copyProperties(layer, InnovateLayerVO.class, "layerInfo");
            layerVO.setSkinName(skinNameMap.get(layer.getSkinCode()));
            layerVO.setCanDelete(!usedLayerIds.contains(layer.getId()));
            return layerVO;
        }));
    }

    /**
     * 查询所有弹层列表
     */
    @GetMapping("/listTotal")
    public AjaxResult listTotal() {
        List<InnovateLayer> list = innovateLayerService.selectInnovateLayerList(new InnovateLayerReq());
        return AjaxResult.success(list.stream().map(layer -> {
            InnovateLayerVO layerVO = new InnovateLayerVO();
            layerVO.setId(layer.getId());
            layerVO.setBgImg(layer.getBgImg());
            return layerVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 获取创新弹层详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(innovateLayerService.selectInnovateVOLayerById(id));
    }

    /**
     * 新增创新弹层
     */
    @Log(title = "创新弹层", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InnovateLayerReq req) {
        // 参数校验
        if (StringUtils.isBlank(req.getBgImg())) {
            return AjaxResult.error("无效的背景图");
        }
        if (StringUtils.isBlank(req.getSkinCode())) {
            return AjaxResult.error("无效的弹层皮肤");
        }

        return toAjax(innovateLayerService.insertInnovateLayer(req));
    }

    /**
     * 修改创新弹层
     */
    @Log(title = "创新弹层", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InnovateLayerReq req) {
        // 参数校验
        if (null == req.getId()) {
            return AjaxResult.error("无效的弹层ID");
        }
        if (StringUtils.isBlank(req.getBgImg())) {
            return AjaxResult.error("无效的背景图");
        }
        if (StringUtils.isBlank(req.getSkinCode())) {
            return AjaxResult.error("无效的弹层皮肤");
        }

        int result = innovateLayerService.updateInnovateLayer(req);
        // 同步修改素材
        if (result > 0) {
            materialService.updateMaterialByLayer(req.getId(), req.getBgImg());
        }
        return toAjax(result);
    }

    /**
     * 删除创新弹层
     */
    @Log(title = "创新弹层", businessType = BusinessType.UPDATE)
    @PostMapping("/delete")
    public AjaxResult delete(@RequestBody IdReq req) {
        // 参数校验
        if (null == req.getId()) {
            return AjaxResult.error("无效的弹层ID");
        }
        InnovateLayer layer = innovateLayerService.selectInnovateLayerById(req.getId());
        if (null == layer || !Objects.equals(layer.getIsDeleted(), 0)) {
            return AjaxResult.error("创新弹层不存在");
        }
        // 检查是否可以删除
        if (CollectionUtils.isNotEmpty(materialService.selectUsedLayerId(Collections.singletonList(req.getId())))) {
            return AjaxResult.error("弹层已被使用，无法删除");
        }
        return toAjax(innovateLayerService.deleteInnovateLayer(req.getId()));
    }
}
