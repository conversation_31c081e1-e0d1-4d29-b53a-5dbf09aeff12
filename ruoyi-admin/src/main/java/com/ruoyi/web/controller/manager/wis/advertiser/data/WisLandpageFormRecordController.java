package com.ruoyi.web.controller.manager.wis.advertiser.data;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord;
import com.ruoyi.system.entity.landpage.LandpageFormFullRecord;
import com.ruoyi.system.req.wis.WisLandpageFormRecordReq;
import com.ruoyi.system.service.landpage.BaijiuLandpageFormRecordService;
import com.ruoyi.system.service.landpage.LandpageFormRecordService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.vo.wis.WisBaijiuLandpageFormRecordExcel;
import com.ruoyi.system.vo.wis.WisBaijiuLandpageFormRecordVO;
import com.ruoyi.system.vo.wis.WisLandpageFormRecordVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 广告主后台落地页表单记录Controller
 *
 * <AUTHOR>
 * @date 2022-02-21
 */
@RestController
@RequestMapping("/wis/landpageFormRecord")
public class WisLandpageFormRecordController extends BaseController {

    @Autowired
    private LandpageFormRecordService landpageFormRecordService;
    @Autowired
    private BaijiuLandpageFormRecordService baijiuLandpageFormRecordService;
    @Autowired
    private AdvertService advertService;

    /**
     * 查询落地页表单记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<WisLandpageFormRecordVO> list(WisLandpageFormRecordReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非广告主不展示
        if (!Objects.equals(user.getMainType(), AccountMainType.ADVERTISER.getType())) {
            return getDataTable(Collections.emptyList());
        }

        startPage();
        LandpageFormFullRecord param = convertParam(req, user.getCrmAccountId());
        List<LandpageFormFullRecord> list = landpageFormRecordService.selectList(param);
        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            WisLandpageFormRecordVO recordVO = BeanUtil.copyProperties(record, WisLandpageFormRecordVO.class);
            recordVO.setIdCard(StrUtil.hide(DesensitizedUtil.idCardNum(recordVO.getIdCard(), 6, 4),
                    recordVO.getIdCard().length() - 2, recordVO.getIdCard().length()));
            recordVO.setPhone(DesensitizedUtil.mobilePhone(recordVO.getPhone()));
            recordVO.setCommitTime(record.getCallbackTime());
            return recordVO;
        }));
    }
    /**
     * 查询白酒落地页表单记录列表
     */
    @GetMapping("/baijiu/list")
    public TableDataInfo<WisBaijiuLandpageFormRecordVO> baijiuList(WisLandpageFormRecordReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非广告主不展示
        if (!Objects.equals(user.getMainType(), AccountMainType.ADVERTISER.getType())) {
            return getDataTable(Collections.emptyList());
        }

        List<Long> advertIds = advertService.selectAdvertIdsByAdvertiserId(user.getCrmAccountId());
        if(CollectionUtils.isEmpty(advertIds)){
            return getDataTable(Collections.emptyList());
        }
        startPage();
        Date startDate = null,endDate = null;
        if(Objects.nonNull(req.getStartDate())){
            startDate = DateUtil.beginOfDay(req.getStartDate());
            endDate = DateUtil.endOfDay(req.getEndDate());
        }
        List<BaijiuLandpageFormRecord> list = baijiuLandpageFormRecordService.selectListByAdvertiserIdAndDate(startDate,endDate,advertIds);
        return getDataTable(PageInfoUtils.dto2Vo(list, record -> {
            WisBaijiuLandpageFormRecordVO recordVO = BeanUtil.copyProperties(record, WisBaijiuLandpageFormRecordVO.class);
            recordVO.setPhone(DesensitizedUtil.mobilePhone(recordVO.getPhone()));
            recordVO.setCommitTime(record.getGmtCreate());
            return recordVO;
        }));
    }

    /**
     * 导出落地页单记录列表
     */
    @Log(title = "广告主后台落地页表单记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(WisLandpageFormRecordReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非广告主不展示
        if (!Objects.equals(user.getMainType(), AccountMainType.ADVERTISER.getType())) {
            return AjaxResult.success();
        }

        LandpageFormFullRecord param = convertParam(req, user.getCrmAccountId());
        List<LandpageFormFullRecord> list = landpageFormRecordService.selectList(param);
        ExcelUtil<WisLandpageFormRecordVO> util = new ExcelUtil<>(WisLandpageFormRecordVO.class);
        return util.exportExcel(list.stream().map(record -> {
            WisLandpageFormRecordVO recordVO = BeanUtil.copyProperties(record, WisLandpageFormRecordVO.class);
            recordVO.setCommitTime(record.getCallbackTime());
            return recordVO;
        }).collect(Collectors.toList()), "表单数据");
//        ExcelUtil.encrypt(result.get("msg").toString() , user.getEmail());
    }
    /**
     * 导出白酒落地页单记录列表
     */
    @Log(title = "白酒广告主后台落地页表单记录", businessType = BusinessType.EXPORT)
    @GetMapping("/baijiu/export")
    public AjaxResult baijiuExport(WisLandpageFormRecordReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非广告主不展示
        if (!Objects.equals(user.getMainType(), AccountMainType.ADVERTISER.getType())) {
            return AjaxResult.success();
        }

        Date startDate = null,endDate = null;
        if(Objects.nonNull(req.getStartDate())){
            startDate = DateUtil.beginOfDay(req.getStartDate());
            endDate = DateUtil.endOfDay(req.getEndDate());
        }
        List<Long> advertIds = advertService.selectAdvertIdsByAdvertiserId(user.getCrmAccountId());
        List<BaijiuLandpageFormRecord> list = baijiuLandpageFormRecordService.selectListByAdvertiserIdAndDate(startDate,endDate,advertIds);
        ExcelUtil<WisBaijiuLandpageFormRecordExcel> util = new ExcelUtil<>(WisBaijiuLandpageFormRecordExcel.class);
        return util.exportExcel(list.stream().map(record -> {
            WisBaijiuLandpageFormRecordExcel recordVO = BeanUtil.copyProperties(record, WisBaijiuLandpageFormRecordExcel.class);
            recordVO.setCommitTime(record.getGmtCreate());
            recordVO.setAmount(NumberUtils.fenToYuanForDouble(record.getAmount()));
            return recordVO;
        }).collect(Collectors.toList()), "表单数据");
    }

    private LandpageFormFullRecord convertParam(WisLandpageFormRecordReq req, Long advertiserId) {
        LandpageFormFullRecord param = new LandpageFormFullRecord();
        param.setTargetAdvertiserId(SecurityUtils.getLoginUser().getCrmAccountId());
        param.setCbStartDate(req.getStartDate());
        if (null != req.getEndDate()) {
            param.setCbEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        param.setTargetAdvertiserId(NumberUtils.defaultLong(advertiserId, 0L));
        return param;
    }
}
