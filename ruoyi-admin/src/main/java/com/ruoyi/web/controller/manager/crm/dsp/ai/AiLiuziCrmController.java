package com.ruoyi.web.controller.manager.crm.dsp.ai;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.ai.AiLiuziEntity;
import com.ruoyi.system.req.ai.AiLiuziListReq;
import com.ruoyi.system.req.ai.AiLiuziUpdateStatusReq;
import com.ruoyi.system.service.ai.AiLiuziService;
import com.ruoyi.system.vo.ai.AiLiuziListVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * ai官网留资 crm
 * <AUTHOR>
 * @date 2024/1/2 14:26
 */
@RestController
@RequestMapping("/ai")
public class AiLiuziCrmController extends BaseController {

    @Autowired
    private AiLiuziService aiLiuziService;

    /**
     * 留资列表
     * @param req
     * @return
     */
    @GetMapping("list")
    public TableDataInfo<AiLiuziListVo> list(AiLiuziListReq req){
        TableSupport.startPage();
        List<AiLiuziEntity> aiLiuziEntities = aiLiuziService.selectList(req);
        return getDataTable(PageInfoUtils.dto2Vo(aiLiuziEntities,entity->{
            return BeanUtil.copyProperties(entity,AiLiuziListVo.class);
        }));
    }

    /**
     * 更新留资状态
     */
    @PostMapping("updateStatus")
    public Result<Boolean> updateStatus(@RequestBody @Valid AiLiuziUpdateStatusReq req){
        AiLiuziEntity aiLiuziEntity = BeanUtil.copyProperties(req, AiLiuziEntity.class);
        aiLiuziEntity.setOperatorName(SecurityUtils.getUsername());
        return ResultBuilder.success(aiLiuziService.updateById(aiLiuziEntity));
    }
}
