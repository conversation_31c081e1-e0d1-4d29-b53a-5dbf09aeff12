package com.ruoyi.web.controller.manager.crm.ssp.publisher;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.publisher.PublisherManager;
import com.ruoyi.system.req.publisher.UpdatePayTypeReq;
import com.ruoyi.system.vo.publisher.PublisherVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * 媒体Controller
 *
 * <AUTHOR>
 * @date 2022-07-29
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/ssp/publisher")
public class PublisherController {

    @Autowired
    private PublisherManager publisherManager;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    /**
     * 获取媒体账号信息
     */
    @GetMapping(value = "getInfo")
    public Result<PublisherVO> getInfo(Long accountId) {
        // 数据权限控制
        if (!hasPermission(accountId)) {
            return ResultBuilder.fail("无访问权限");
        }
        // 查询账号信息
        PublisherVO publisher = publisherManager.getInfo(accountId);
        return ResultBuilder.success(publisher);
    }

    /**
     * 修改媒体付款类型
     */
    @Log(title = "媒体付款类型修改", businessType = BusinessType.UPDATE)
    @PostMapping("updatePayType")
    public Result<Void> updatePayType(@RequestBody @Validated UpdatePayTypeReq req) {
        int result = publisherManager.updatePayType(req.getAccountId(), req.getPayType());
        return ResultBuilder.result(result);
    }

    /**
     * 是否有媒体账号的权限
     *
     * @param accountId 媒体账号ID
     * @return 是否有权限
     */
    private boolean hasPermission(Long accountId) {
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            return CollectionUtils.isNotEmpty(permission.getValues()) && permission.getValues().contains(accountId);
        }
        return true;
    }
}
