package com.ruoyi.web.controller.engine;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.enums.landpage.IspVipProductTypeEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.IspVipLandpageFormRecordEntity;
import com.ruoyi.system.req.engine.IspVipCwCallbackReq;
import com.ruoyi.system.req.engine.IspVipGhCallbackReq;
import com.ruoyi.system.req.engine.IspVipLandpageCodeReq;
import com.ruoyi.system.req.engine.IspVipLandpageOrderReq;
import com.ruoyi.system.req.engine.IspVipMdCallbackReq;
import com.ruoyi.system.req.engine.IspVipPayStatusReq;
import com.ruoyi.system.req.engine.IspVipSjsCallbackReq;
import com.ruoyi.system.req.engine.IspVipTthCallbackReq;
import com.ruoyi.system.req.engine.IspVipUnSmsLandpageOrderReq;
import com.ruoyi.system.req.engine.IspVipWqCallbackReq;
import com.ruoyi.system.req.engine.SjsCreateOrderCallBackVo;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.SlotUpService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.service.landpage.IspVipLandpageFormRecordService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.vo.ispvip.IspVipMdPayVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.InnerLogType.BLIND_BOX_POPUP_CLICK;
import static com.ruoyi.common.enums.InnerLogType.LANDPAGE_CLICK;

/**
 * 运营商会员落地页(不鉴权)
 *
 * <AUTHOR>
 * @date 2023/11/07
 */
@Slf4j
@RestController
@RequestMapping("/lp/ispVip")
public class IspVipLandpageController {

    /**
     * [欣鼎/巧思]接口参数
     */
    private static final String CHANNEL_ID = "1140510003";
    private static final String PRODUCT = "HJYHD1226";

    /**
     * [望秋]接口参数
     */
    private static final String APP_SECRET = "1A4DA457D20BA7DB7F0EFA41CB3FDDBC";
    private static final String APP_KEY = "1698308805427";
    private static final String API_URL = "http://yunliapi.shujul.cn/router";
    private static final String WQ_PNO = "LT-WYD-20Y";
    private static final Integer WQ_CHANNEL_ID = 441;

    /**
     * [桂郃]接口参数
     */
    private static final String GH_API_URL = "https://z.huanledd.com/pts";
    private static final String GH_SECRET = "1HEV4yJ9kWvl6zs0";
    private static final String GH_PNO = "GZYD_XHDLJYB20YD";
    private static final String GH_MAC_ID = "1064003406572661674";

    /**
     * [淘淘汇]接口参数
     */
    private static final String TTH_API_URL = "https://gateway.chengseml2022.com";
    private static final String TTH_APP_KEY = "8ymlju1mwzbl";
    private static final String TTH_APP_SECRET = "1213123";
    private static final String TTH_CHANNEL_CODE = "rh9pdy";

    /**
     * [盛嘉胜] 接口参数
     */
    private static final String SJS_25PICK1_CHANNEL_ID="20240321900822867511";

    private static final String SJS_25PICK2_CHANNEL_ID="20240407500248297963";

    /**
     * 默认参数
     */
    private static final Pair<Boolean, String> DEFAULT_RESULT = Pair.of(false, "未匹配到产品");

    @Autowired
    private OrderService orderService;

    @Autowired
    private StatService statService;

    @Autowired
    private IspVipLandpageFormRecordService landpageFormRecordService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private ConvCallbackService convCallbackService;

    @Autowired
    private SlotUpService slotUpService;

    /**
     * 异步操作任务调度线程池
     */
    private final ScheduledExecutorService scheduledExecutor = SpringUtils.getBean("scheduledExecutorService");

    /**
     * 获取验证码
     */
    @CrossOrigin
    @PostMapping("/getSmsCode")
    public AjaxResult getSmsCode(@RequestBody IspVipLandpageCodeReq req, HttpServletRequest request) {
        log.info("运营商会员落地页获取验证码, req={}", JSON.toJSONString(req));
        if (StrUtil.length(req.getPhone()) != 11) {
            return AjaxResult.error("无效的手机号");
        }

        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K106.join(req.getOrderId(), req.getPhone()), 10);
        if (lock == null) {
            return AjaxResult.error("验证码已发送，请稍后再试");
        }

        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            return AjaxResult.error("无效的订单号");
        }

        // 对接的产品类型
        Integer productType = NumberUtils.defaultInt(req.getProductType(), IspVipProductTypeEnum.XINDING.getType());

        IspVipLandpageFormRecordEntity record = landpageFormRecordService.selectBy(req.getOrderId(), req.getPhone());
        if (null == record) {
            record = new IspVipLandpageFormRecordEntity();
            record.setPhone(req.getPhone());
            record.setOrderId(req.getOrderId());
            record.setAdvertId(order.getAdvertId());
            record.setConsumerId(order.getConsumerId());
            record.setAppId(order.getAppId());
            record.setSlotId(order.getSlotId());

            AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
            if (null != adSnapshot) {
                record.setLandpageUrl(adSnapshot.getLandpageUrl());
            }
        }

        record.setProductType(productType);
        record.setReferer(StrUtil.subPre(request.getHeader("Referer"), 255));
        record.setIp(StrUtil.subPre(IpUtils.getIpAddr(request), 32));
        record.setUserAgent(StrUtil.subPre(request.getHeader("User-Agent"), 255));

        // 获取验证码
        // 后面抽取这部分代码，改用策略模式，根据不同类型区分不同广告主的调用
        Pair<Boolean, String> result = DEFAULT_RESULT;
        if (Objects.equals(productType, IspVipProductTypeEnum.XINDING.getType()) ||
                Objects.equals(productType, IspVipProductTypeEnum.QIAOSI.getType())) {
            String channelId = getChannelId(order.getAdvertId());

            JSONObject ext = new JSONObject();
            ext.put("product", PRODUCT);
            ext.put("channel_id", channelId);
            record.setExt(ext.toString());

            result = callCodeApi(req.getOrderId(), req.getPhone(), channelId, request);
        } else if (Objects.equals(productType, IspVipProductTypeEnum.WANGQIU.getType())) {
            JSONObject ext = new JSONObject();
            ext.put("channelId", WQ_CHANNEL_ID);
            ext.put("pno", WQ_PNO);
            record.setExt(ext.toString());
            result = identifyCodeGet(req.getPhone());
        } else if (Objects.equals(productType, IspVipProductTypeEnum.GUIHE.getType())) {
            JSONObject ext = new JSONObject();
            ext.put("pno", GH_PNO);
            ext.put("macId", GH_MAC_ID);
            record.setExt(ext.toString());
            // 下单
            if (StringUtils.isBlank(record.getBizOrderNo())) {
                Pair<Boolean, Pair<String, String>> ghResult = ghCreate(req.getOrderId(), req.getPhone());
                record.setBizOrderNo(ghResult.getValue().getValue());
                result = Pair.of(ghResult.getKey(), ghResult.getValue().getKey());
            }
            // 重发验证码
            else {
                result = ghSmscode(record.getBizOrderNo());
            }
        } else if (Objects.equals(productType, IspVipProductTypeEnum.TAOTAOHUI.getType())) {
            JSONObject ext = new JSONObject();
            ext.put("channelCode", TTH_CHANNEL_CODE);
            record.setExt(ext.toString());
            result = tthSmsCode(req.getOrderId(), req.getPhone());
        } else if (Objects.equals(productType, IspVipProductTypeEnum.CHUANGWO_YD.getType())
                || Objects.equals(productType, IspVipProductTypeEnum.CHUANGWO_LT.getType())) {
            String channelId = Objects.equals(productType, IspVipProductTypeEnum.CHUANGWO_YD.getType()) ? "5213151" : "5213152";
            Pair<Boolean, Pair<String, String>> cwResult = cwCreate(req.getOrderId(), req.getPhone(), channelId, record.getIp(), record.getUserAgent());
            record.setBizOrderNo(cwResult.getValue().getValue());
            result = Pair.of(cwResult.getKey(), cwResult.getValue().getKey());
        }

        if (!result.getKey()) {
            record.setMsg(result.getValue());
            lock.unlock();
        }
        if (null == record.getId()) {
            landpageFormRecordService.insert(record);
        } else {
            landpageFormRecordService.updateById(record);
        }
        return AjaxResult.success(result.getValue(), result.getKey());
    }

    /**
     * 提交订单
     */
    @CrossOrigin
    @PostMapping("/order")
    public AjaxResult order(@RequestBody IspVipLandpageOrderReq req) {
        log.info("运营商会员落地页下单, req={}", JSON.toJSONString(req));
        if (StringUtils.isBlank(req.getSmsCode())) {
            return AjaxResult.error("无效的验证码");
        }

        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K106.join(req.getOrderId(), req.getPhone(), req.getSmsCode()), 3);
        if (lock == null) {
            return AjaxResult.error("已提交，请稍后再试");
        }

        IspVipLandpageFormRecordEntity record = landpageFormRecordService.selectBy(req.getOrderId(), req.getPhone());
        if (null == record) {
            return AjaxResult.error("未查询到记录");
        }
        if (Objects.equals(record.getStatus(), 1)) {
            return AjaxResult.success("提交成功", true);
        }

        // 提交验证码
        // 后面抽取这部分代码，根据不同类型区分不同广告主的调用
        Integer productType = record.getProductType();
        if (Objects.equals(productType, IspVipProductTypeEnum.XINDING.getType()) ||
                Objects.equals(productType, IspVipProductTypeEnum.QIAOSI.getType())) {
            Pair<Boolean, String> result = callOrderApi(req.getOrderId(), req.getPhone(), req.getSmsCode());
            updateRecord(record.getId(), result.getKey(), result.getValue());
            if (result.getKey()) {
                // 领取埋点
                statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());
            }
            return AjaxResult.success(result.getValue(), result.getKey());
        }
        if (Objects.equals(productType, IspVipProductTypeEnum.WANGQIU.getType())) {
            // 校验验证码
            Pair<Boolean, String> result = identifyCodeCheck(req.getPhone(), req.getSmsCode());
            if (!result.getKey()) {
                updateRecord(record.getId(), result.getKey(), result.getValue());
                return AjaxResult.success(result.getValue(), result.getKey());
            }
            statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());

            // 下单
            Pair<Integer, Pair<String, String>> orderResult = saveChargeOrder(req.getOrderId(), req.getPhone(), req.getSmsCode());
            if (Objects.equals(orderResult.getKey(), 301)) {
                return AjaxResult.success("订购中", true);
            }
            if (Objects.equals(orderResult.getKey(), 302)) {
                updateRecord(record.getId(), true, orderResult.getValue().getKey(), orderResult.getValue().getValue());
                landpageClick(record.getOrderId());
                return AjaxResult.success( orderResult.getValue().getKey(), true);
            }
            updateRecord(record.getId(), false, orderResult.getValue().getKey(), orderResult.getValue().getValue());
            return AjaxResult.success( orderResult.getValue().getKey(), false);
        }
        if (Objects.equals(productType, IspVipProductTypeEnum.GUIHE.getType())) {
            Pair<Integer, String> result = ghPay(record.getBizOrderNo(), req.getSmsCode(), req.getPhone());
            if (!Objects.equals(result.getKey(), 3) && !StrUtil.contains(result.getValue(), "验证码")) {
                statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());
            }

            if (Objects.equals(result.getKey(), 0) || Objects.equals(result.getKey(), 1)) {
                // 延迟查询订单
                queryOrderDelay(record.getId());
                return AjaxResult.success("订购中", true);
            }
            if (Objects.equals(result.getKey(), 2)) {
                updateRecord(record.getId(), true, result.getValue());
                landpageClick(record.getOrderId());
                return AjaxResult.success(result.getValue(), true);
            }
            updateRecord(record.getId(), false, result.getValue());
            return AjaxResult.success(result.getValue(), false);
        }
        if (Objects.equals(productType, IspVipProductTypeEnum.TAOTAOHUI.getType())) {
            Pair<Boolean, String> result = tthOrder(req.getPhone(), req.getSmsCode());
            if (result.getKey()) {
                // 领取埋点
                statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());
                // 延迟查询订单
                queryOrderDelay(record.getId());
            }
            return AjaxResult.success(result.getValue(), result.getKey());
        }
        if (Objects.equals(productType, IspVipProductTypeEnum.CHUANGWO_YD.getType()) ||
                Objects.equals(productType, IspVipProductTypeEnum.CHUANGWO_LT.getType())) {
            Pair<Boolean, String> result = cwPay(req.getOrderId(), req.getPhone(), req.getSmsCode());
            if (result.getKey()) {
                // 领取埋点
                statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());
            } else {
                updateRecord(record.getId(), result.getKey(), result.getValue());
            }
            return AjaxResult.success(result.getValue(), result.getKey());
        }

        return AjaxResult.success(DEFAULT_RESULT.getValue(), DEFAULT_RESULT.getKey());
    }

    /**
     * 提交订单 不需要短信
     */
    @CrossOrigin
    @PostMapping("/saveOrder")
    public Result<IspVipMdPayVO> saveOrder(@RequestBody IspVipUnSmsLandpageOrderReq req,HttpServletRequest request) {
        log.info("落地页下单, req={}", JSON.toJSONString(req));
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            return ResultBuilder.fail("无效的订单号");
        }

        if(StringUtils.isEmpty(req.getPhone()) || req.getPhone().length() !=11){
            return ResultBuilder.fail("无效手机号");
        }
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K106.join(req.getOrderId(), req.getPhone()), 3);
        if (lock == null) {
            return ResultBuilder.fail("已提交，请稍后再试");
        }

        Integer productType = NumberUtils.defaultInt(req.getProductType(), IspVipProductTypeEnum.MINDING.getType());
        IspVipLandpageFormRecordEntity record = landpageFormRecordService.selectBy(req.getOrderId(), req.getPhone());
        if (Objects.isNull(record)) {
            //新增记录
            record  =new IspVipLandpageFormRecordEntity();
            record.setPhone(req.getPhone());
            record.setOrderId(req.getOrderId());
            record.setAdvertId(order.getAdvertId());
            record.setConsumerId(order.getConsumerId());
            record.setAppId(order.getAppId());
            record.setSlotId(order.getSlotId());

            AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
            if (null != adSnapshot) {
                record.setLandpageUrl(adSnapshot.getLandpageUrl());
            }
            record.setProductType(productType);
            record.setReferer(StrUtil.subPre(request.getHeader("Referer"), 255));
            record.setIp(StrUtil.subPre(IpUtils.getIpAddr(request), 32));
            record.setUserAgent(StrUtil.subPre(request.getHeader("User-Agent"), 255));
            landpageFormRecordService.insert(record);
        }
        if (Objects.equals(record.getStatus(), 1)) {
            return ResultBuilder.fail("已提交");
        }

        if (Objects.equals(productType, IspVipProductTypeEnum.MINDING.getType())) {
            String alipayUrl = callMinDingPay(record.getId(), req.getPhone());
            if (StringUtils.isNotEmpty(alipayUrl)) {
                // 领取埋点
                statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());
                IspVipMdPayVO vo = new IspVipMdPayVO();
                vo.setAlipayUrl(alipayUrl);
                vo.setRecordId(record.getId());
                return ResultBuilder.success(vo);
            }
            return ResultBuilder.fail("提交失败，请稍后再试");
        }
        if(Objects.equals(productType, IspVipProductTypeEnum.SHENGJIASHENG.getType()) || Objects.equals(productType,IspVipProductTypeEnum.SHENGJIASHENG_25PICK2.getType())){
          SjsCreateOrderCallBackVo callBackVo = callShengJiaShengPay(productType,record.getId(), req.getPhone(),IpUtils.getIpAddr(request));
            if (StringUtils.isNotEmpty(callBackVo.getTgurl())) {
                IspVipMdPayVO vo=new IspVipMdPayVO();
                vo.setAlipayUrl(callBackVo.getTgurl());
                vo.setRecordId(record.getId());
                // 领取埋点
                statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());
                JSONObject ext = new JSONObject();
                ext.put("orderNo", callBackVo.getOrderNo());
                IspVipLandpageFormRecordEntity recordEntity=new IspVipLandpageFormRecordEntity();
                recordEntity.setId(record.getId());
                recordEntity.setExt(ext.toString());
                landpageFormRecordService.updateById(recordEntity);
                return ResultBuilder.success(vo);
            }
            return ResultBuilder.fail("提交失败，请稍后再试");
        }
        if (Objects.equals(productType, IspVipProductTypeEnum.MIJIE.getType())) {
            SjsCreateOrderCallBackVo callBackVo = callMiJiePay(record.getId(), req.getPhone(), IpUtils.getIpAddr(request));
            if (StringUtils.isNotEmpty(callBackVo.getTgurl())) {
                IspVipMdPayVO vo = new IspVipMdPayVO();
                vo.setAlipayUrl(callBackVo.getTgurl());
                vo.setRecordId(record.getId());
                // 领取埋点
                statService.innerLogStatByOrderId(BLIND_BOX_POPUP_CLICK, req.getOrderId());
                JSONObject ext = new JSONObject();
                ext.put("orderNo", callBackVo.getOrderNo());
                IspVipLandpageFormRecordEntity recordEntity = new IspVipLandpageFormRecordEntity();
                recordEntity.setId(record.getId());
                recordEntity.setExt(ext.toString());
                landpageFormRecordService.updateById(recordEntity);
                return ResultBuilder.success(vo);
            }
            return ResultBuilder.fail("提交失败，请稍后再试");
        }

        return ResultBuilder.fail("无效请求");
    }

    /**
     * 闵鼎回调
     */
    @CrossOrigin
    @GetMapping("mdCallback")
    public String mdCallback(IspVipMdCallbackReq req){
        log.info("闵鼎接口回调参数,req:{}",req);
        if(NumberUtils.isNullOrLteZero(req.getClickid())){
            log.error("闵鼎回调无效参数");
            return "fail";
        }
        IspVipLandpageFormRecordEntity recordEntity = landpageFormRecordService.selectById(req.getClickid());
        if(Objects.isNull(recordEntity)){
            log.error("闵鼎回调无效参数,recordId:{}",req.getClickid());
            return "fail";
        }
        if(Objects.equals(recordEntity.getStatus(),1)){
            return "success";
        }
        updateRecord(req.getClickid(),true,"");
        //增加支付数据
        landpageClick(recordEntity.getOrderId());
        return "success";
    }

    /**
     * 盛嘉胜回调
     */
    @CrossOrigin
    @GetMapping("sjsCallback/{reqId}")
    public String sjsCallback(IspVipSjsCallbackReq req, @PathVariable("reqId") Long reqId){
        log.info("盛嘉胜接口回调参数,req:{}",req);
        if(NumberUtils.isNullOrLteZero(reqId)){
            log.error("盛嘉胜回调无效参数,recordId:{}",reqId);
            return "fail";
        }
        IspVipLandpageFormRecordEntity recordEntity = landpageFormRecordService.selectById(reqId);
        if(Objects.isNull(recordEntity)){
            log.error("盛嘉胜回调无效参数,recordId:{}",reqId);
            return "fail";
        }
        if (Objects.equals(recordEntity.getStatus(),1)){
            return "success";
        }
        updateRecord(reqId,Objects.equals(req.getStatus(),1),req.getNotes());
        if(Objects.equals(req.getStatus(),1)){
            landpageClick(recordEntity.getOrderId());
            statService.convertEvent(recordEntity.getOrderId(), ConvType.PAY.getType());
        }
        //增加支付数据
        return "success";
    }

    /**
     * 米节回调
     */
    @CrossOrigin
    @GetMapping("mjCallback/{reqId}")
    public String mjCallback(IspVipSjsCallbackReq req, @PathVariable("reqId") Long reqId) {
        log.info("米节接口回调参数,req:{}",req);
        if (NumberUtils.isNullOrLteZero(reqId)) {
            log.error("米节回调无效参数,recordId:{}", reqId);
            return "fail";
        }
        IspVipLandpageFormRecordEntity recordEntity = landpageFormRecordService.selectById(reqId);
        if (Objects.isNull(recordEntity)) {
            log.error("米节回调无效参数,recordId:{}", reqId);
            return "fail";
        }
        if (Objects.equals(recordEntity.getStatus(), 1)) {
            return "success";
        }
        updateRecord(reqId, Objects.equals(req.getStatus(), 1), req.getNotes());
        if (Objects.equals(req.getStatus(), 1)) {
            landpageClick(recordEntity.getOrderId());
            statService.convertEvent(recordEntity.getOrderId(), ConvType.PAY.getType());
        }
        return "success";
    }

    /**
     * 签约支付结果查询 1.成功,2.失败
     * @param req
     * @return
     */
    @CrossOrigin
    @PostMapping("payStatus")
    public Result<Integer> mdPayStatus(@RequestBody IspVipPayStatusReq req){
        if(NumberUtils.isNullOrLteZero(req.getRecordId())){
            return ResultBuilder.fail("无效的参数");
        }
        IspVipLandpageFormRecordEntity recordEntity = landpageFormRecordService.selectById(req.getRecordId());
        if(Objects.isNull(recordEntity)) {
            return ResultBuilder.fail("无效参数");
        }
        return ResultBuilder.success(recordEntity.getStatus());
    }

    /**
     * 望秋回调
     */
    @CrossOrigin
    @PostMapping("/wqCallback")
    public JSONObject wqCallback(IspVipWqCallbackReq req) {
        log.info("[望秋]运营商会员接口回调，req={}", JSON.toJSONString(req));
        IspVipLandpageFormRecordEntity record = landpageFormRecordService.selectByBizOrderNoAndOrderId(req.getOrderNumber(), req.getPartnerOrderId());
        if (null != record && !Objects.equals(record.getStatus(), 1)) {
            if (Objects.equals(req.getStatus(), 302)) {
                updateRecord(record.getId(), true, req.getMsg());
                landpageClick(record.getOrderId());
            } else if (Objects.equals(req.getStatus(), 303)) {
                updateRecord(record.getId(), false, req.getMsg());
            }
        }

        JSONObject result = new JSONObject();
        result.put("code", "200");
        result.put("message", "success");
        return result;
    }

    /**
     * [欣鼎/巧思]获取验证码
     */
    private Pair<Boolean, String> callCodeApi(String orderId, String phone, String channelId, HttpServletRequest request) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(true, "");
        }

        JSONObject body = new JSONObject();
        body.put("phone", phone);
        body.put("nadkey", orderId);
        body.put("product", PRODUCT);
        body.put("channel_id", channelId);
        body.put("userAgent", request.getHeader("User-Agent"));
        body.put("userIp", IpUtils.getIpAddr(request));

        String resp = HttpUtil.post("https://api.0168.net.cn/qiaosi/product/nuohe/payment", body.toString());
        log.info("[欣鼎/巧思]运营商会员获取验证码接口调用, body={}, resp={}", body, resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp) && StrUtil.containsAny(resp, "HTTP状态 404 - 未找到", "504 Gateway Time-out")) {
            resp = HttpUtil.post("https://api.0168.net.cn/qiaosi/product/nuohe/payment", body.toString());
            log.info("[欣鼎/巧思]运营商会员获取验证码接口调用-重试, body={}, resp={}", body, resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(false, "接口调用失败");
        }
        return Pair.of(result.getBooleanValue("success"), result.getString("desc"));
    }

    /**
     * [欣鼎/巧思]提交验证码
     */
    private Pair<Boolean, String> callOrderApi(String orderId, String phone, String smsCode) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(true, "");
        }

        JSONObject body = new JSONObject();
        body.put("phone", phone);
        body.put("nadkey", orderId);
        body.put("smsCode", smsCode);
        String resp = HttpUtil.post("https://api.0168.net.cn/qiaosi/product/nuohe/code", body.toString());
        log.info("[欣鼎/巧思]运营商会员下单接口调用, body={}, resp={}", body, resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp) && StrUtil.contains(resp, "Whitelabel Error Page")) {
            resp = HttpUtil.post("https://api.0168.net.cn/qiaosi/product/nuohe/code", body.toString());
            log.info("[欣鼎/巧思]运营商会员下单接口调用-重试, body={}, resp={}", body, resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(false, "接口调用失败");
        }
        return Pair.of(result.getBooleanValue("success"), result.getString("desc"));
    }

    /**
     * 闵鼎创建订单
     */
    private String callMinDingPay(Long recordId,String phone){
        // 只有生产环境才调用
//        if (!SpringEnvironmentUtils.isProd()) {
//            return Pair.of(true, "");
//        }
        String url = "https://nclr.chenggnet.com/prod-api/open/sign/alipay";
        String planCode="";
        if(!SpringEnvironmentUtils.isProd()){
            url = "https://coding.chenggnet.com:88/qyco/api/open/sign/alipay";
            planCode = "cfcfaf0bec2148a5bd1a569cb7ad3937";
        }
        JSONObject body = new JSONObject();
        body.put("planCode", planCode);
        body.put("phone", phone);
        body.put("clickid", recordId);
        String resp = HttpUtil.get(url, body);
        log.info("闵鼎支付宝签约接口调用结果,body:{},resp:{}",body,resp);
        JSONObject respObj = JSONObject.parseObject(resp);
        if(respObj.getInteger("code") != 200){
            log.error("闵鼎支付宝签约接口调用结果异常,resp:{}",resp);
            return "";
        }
        return respObj.getString("data");
    }

    /**
     * 盛嘉胜创建订单
     */
    private SjsCreateOrderCallBackVo callShengJiaShengPay(Integer productType,Long recordId, String phone, String ip){
        String channelId="";
        if (Objects.equals(productType,IspVipProductTypeEnum.SHENGJIASHENG.getType())){
            channelId=SJS_25PICK1_CHANNEL_ID;
        }else if (Objects.equals(productType,IspVipProductTypeEnum.SHENGJIASHENG_25PICK2.getType())){
            channelId=SJS_25PICK2_CHANNEL_ID;
        }
        String url = "http://san.ssp.3fahudong.com/api/common/processing/business";
        JSONObject body = new JSONObject();
        body.put("channelId", channelId);
        body.put("mobile", phone);
        body.put("clickId", recordId);
        body.put("ip",ip);
        if (SpringEnvironmentUtils.isTest()){
            body.put("callBack","http://msg-test.miaojie.net/lp/ispVip/sjsCallback/"+recordId);
        }
        if (SpringEnvironmentUtils.isProd()){
            body.put("callBack","http://msg.miaojie.net/lp/ispVip/sjsCallback/"+recordId);
        }
        String resp = HttpUtil.post(url, body.toJSONString());
        log.info("盛嘉胜支付宝签约接口调用结果,body:{},resp:{}",body,resp);
        JSONObject respObj = JSONObject.parseObject(resp);
        if(respObj.getInteger("code") != 0){
            log.error("盛嘉胜支付宝签约接口调用结果异常,resp:{}",resp);
            return new SjsCreateOrderCallBackVo();
        }
        SjsCreateOrderCallBackVo callBackVo=new SjsCreateOrderCallBackVo();
        callBackVo.setTgurl(respObj.getString("tgurl"));
        callBackVo.setOrderNo(respObj.getString("orderNo"));
        return callBackVo;
    }

    /**
     * 米节创建订单
     */
    private SjsCreateOrderCallBackVo callMiJiePay(Long recordId, String phone, String ip) {
        JSONObject body = new JSONObject();
        body.put("channelId", "20240403300879364247");
        body.put("mobile", phone);
        body.put("clickId", recordId);
        body.put("ip", ip);
        if (SpringEnvironmentUtils.isTest()) {
            body.put("callBack", "http://msg-test.miaojie.net/lp/ispVip/mjCallback/" + recordId);
        }
        if (SpringEnvironmentUtils.isProd()) {
            body.put("callBack", "http://msg.miaojie.net/lp/ispVip/mjCallback/" + recordId);
        }
        String resp = HttpUtil.post("http://san.ssp.3fahudong.com/api/common/processing/business", body.toJSONString());
        log.info("米节支付宝签约接口调用结果,body:{},resp:{}", body, resp);
        JSONObject respObj = JSONObject.parseObject(resp);
        if (respObj.getInteger("code") != 0) {
            log.error("米节支付宝签约接口调用结果异常,resp:{}", resp);
            return new SjsCreateOrderCallBackVo();
        }
        SjsCreateOrderCallBackVo callBackVo = new SjsCreateOrderCallBackVo();
        callBackVo.setTgurl(respObj.getString("tgurl"));
        callBackVo.setOrderNo(respObj.getString("orderNo"));
        return callBackVo;
    }

    /**
     * [欣鼎/巧思]获取channelId
     */
    private String getChannelId(Long advertId) {
        try {
            Long advertiserId = advertCacheService.queryAdvertiserId(advertId);
            if (Objects.equals(advertiserId, 856L)) {
                return "1140523003";
            } else if (Objects.equals(advertiserId, 880L)) {
                return "1140510007";
            }
            return CHANNEL_ID;
        } catch (Exception e) {
            log.error("getChannelId error, advertId={}", advertId, e);
        }
        return CHANNEL_ID;
    }

    /**
     * [望秋]获取验证码
     */
    private Pair<Boolean, String> identifyCodeGet(String phone) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(true, "");
        }

        Map<String, Object> reqMap = getReqMap("cht.identifyCodeGet");
        // 加入接口入参
        reqMap.put("channelId", WQ_CHANNEL_ID); // 渠道 id，平台提供
        reqMap.put("pno", WQ_PNO);    // 商品编码
        reqMap.put("mobile", phone); // 办理手机号
        String sign = sign(reqMap, APP_SECRET);
        reqMap.put("sign", sign);
        String resp = HttpUtil.post(API_URL, reqMap);
        log.info("[望秋]运营商会员获取验证码接口调用, req={}, resp={}", JSON.toJSONString(reqMap), resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp)) {
            resp = HttpUtil.post(API_URL, reqMap);
            log.info("[望秋]运营商会员获取验证码接口调用-重试, req={}, resp={}", JSON.toJSONString(reqMap), resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(false, "接口调用失败");
        }
        boolean isSuccess = Objects.equals(result.getString("code"), "200");
        return Pair.of(isSuccess, result.getString("message"));
    }

    /**
     * [望秋]验证码校验
     */
    private Pair<Boolean, String> identifyCodeCheck(String phone, String smsCode) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(true, "");
        }

        Map<String, Object> reqMap = getReqMap("cht.identifyCodeCheck");
        // 加入接口入参
        reqMap.put("mobile", phone); // 订购手机号
        reqMap.put("smsCode", smsCode); // 验证码
        reqMap.put("pno", WQ_PNO);    // 商品编码
        reqMap.put("channelId", WQ_CHANNEL_ID); // 渠道 id，平台提供
        String sign = sign(reqMap, APP_SECRET);
        reqMap.put("sign", sign);
        String resp = HttpUtil.post(API_URL, reqMap);
        log.info("[望秋]运营商会员验证码校验接口调用, req={}, resp={}", JSON.toJSONString(reqMap), resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp)) {
            resp = HttpUtil.post(API_URL, reqMap);
            log.info("[望秋]运营商会员验证码校验接口调用-重试, req={}, resp={}", JSON.toJSONString(reqMap), resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(false, "接口调用失败");
        }
        boolean isSuccess = Objects.equals(result.getString("code"), "200");
        return Pair.of(isSuccess, result.getString("message"));
    }

    /**
     * [望秋]办理下单
     */
    private Pair<Integer, Pair<String, String>> saveChargeOrder(String orderId, String phone, String smsCode) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(302, Pair.of("", ""));
        }

        Map<String, Object> reqMap = getReqMap("cht.saveChargeOrder");
        // 加入接口入参
        reqMap.put("mobile", phone); // 订购手机号
        reqMap.put("partnerOrderId", orderId);    // 合作商订单号。
        reqMap.put("smsCode", smsCode); // 验证码
        reqMap.put("pno", WQ_PNO);    // 商品编码
        reqMap.put("channelId", WQ_CHANNEL_ID); // 渠道 id，平台提供
        reqMap.put("notifyUrl", "https://actengine.ydns.cn/lp/ispVip/wqCallback");
        String sign = sign(reqMap, APP_SECRET);
        reqMap.put("sign", sign);
        String resp = HttpUtil.post(API_URL, reqMap);
        log.info("[望秋]运营商会员下单接口调用, req={}, resp={}", JSON.toJSONString(reqMap), resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp)) {
            resp = HttpUtil.post(API_URL, reqMap);
            log.info("[望秋]运营商会员下单接口调用-重试, req={}, resp={}", JSON.toJSONString(reqMap), resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(303, Pair.of("接口调用失败", ""));
        }
        if (!Objects.equals(result.getString("code"), "200") || !result.containsKey("result")) {
            return Pair.of(303, Pair.of(result.getString("message"), ""));
        }
        result = result.getJSONObject("result");
        return Pair.of(result.getInteger("status"), Pair.of(result.getString("msg"), result.getString("orderNumber")));
    }

    /**
     * [望秋]获取请求参数
     */
    private Map<String, Object> getReqMap(String method) {
        Map<String, Object> reqMap = new HashMap<>();
        // 加入系统参数
        reqMap.put("appKey", APP_KEY);
        reqMap.put("v", "1.0");
        reqMap.put("messageFormat", "json");
        reqMap.put("method", method);
        return reqMap;
    }

    /**
     * [望秋]签名
     */
    private String sign(Map<String, Object> requestMap, String secret) {
        StringBuilder str = new StringBuilder();
        List<String> arrayList = new ArrayList<>();
        for (String key : requestMap.keySet()) {
            arrayList.add(key + requestMap.get(key));
        }
        Collections.sort(arrayList);
        for (String value : arrayList) {
            str.append(value);
        }
        String content = secret + str + secret;
        content = SecureUtil.sha1(content);
        return content.toUpperCase();
    }

    /**
     * [桂郃]获取验证码接口
     */
    private Pair<Boolean, Pair<String, String>> ghCreate(String orderId, String phone) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(true, Pair.of("", ""));
        }

        JSONObject reqMap = new JSONObject();
        reqMap.put("timestamp", System.currentTimeMillis());
        reqMap.put("macId", GH_MAC_ID);
        reqMap.put("mobile", phone);
        reqMap.put("macOrderId", orderId);
        reqMap.put("pno", GH_PNO);
        reqMap.put("notifyUrl", "http://actengine.ydns.cn/lp/ispVip/ghCallback");
        reqMap.put("sign", ghSign(reqMap));
        String resp = HttpUtil.post(GH_API_URL + "/api/flow/create", reqMap.toString());
        log.info("[桂郃]运营商会员获取验证码接口调用, req={}, resp={}", reqMap, resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp)) {
            resp = HttpUtil.post(GH_API_URL + "/api/flow/create", reqMap.toString());
            log.info("[桂郃]运营商会员获取验证码接口调用-重试, req={}, resp={}", reqMap, resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(false, Pair.of("接口调用失败", ""));
        }
        if (!Objects.equals(result.getInteger("code"), 0)) {
            return Pair.of(false, Pair.of(result.getString("message"), ""));
        }
        JSONObject data = result.getJSONObject("data");
        return Pair.of(!Objects.equals(data.getInteger("status"), 3), Pair.of(result.getString("message"), data.getString("orderId")));
    }

    /**
     * [桂郃]重新获取业务办理验证码
     */
    private Pair<Boolean, String> ghSmscode(String bizOrderNo) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(true, "");
        }

        JSONObject reqMap = new JSONObject();
        reqMap.put("timestamp", System.currentTimeMillis());
        reqMap.put("macId", GH_MAC_ID);
        reqMap.put("orderId", bizOrderNo);
        reqMap.put("sign", ghSign(reqMap));
        String resp = HttpUtil.post(GH_API_URL + "/api/flow/smscode", reqMap.toString());
        log.info("[桂郃]运营商会员重新获取验证码接口调用, req={}, resp={}", reqMap, resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp)) {
            resp = HttpUtil.post(GH_API_URL + "/api/flow/smscode", reqMap.toString());
            log.info("[桂郃]运营商会员重新获取验证码接口调用-重试, req={}, resp={}", reqMap, resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(false, "接口调用失败");
        }
        boolean isSuccess = Objects.equals(result.getInteger("code"), 0) && result.getBooleanValue("data");
        return Pair.of(isSuccess, result.getString("message"));
    }

    /**
     * [桂郃]下单接口
     */
    private Pair<Integer, String> ghPay(String bizOrderNo, String smsCode, String phone) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(1, "");
        }

        JSONObject reqMap = new JSONObject();
        reqMap.put("timestamp", System.currentTimeMillis());
        reqMap.put("macId", GH_MAC_ID);
        reqMap.put("mobile", phone);
        reqMap.put("orderId", bizOrderNo);
        reqMap.put("smsCode", smsCode);
        reqMap.put("sign", ghSign(reqMap));
        String resp = HttpUtil.post(GH_API_URL + "/api/flow/pay", reqMap.toString());
        log.info("[桂郃]运营商会员下单接口调用, req={}, resp={}", reqMap, resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp)) {
            resp = HttpUtil.post(GH_API_URL + "/api/flow/pay", reqMap.toString());
            log.info("[桂郃]运营商会员下单接口调用-重试, req={}, resp={}", reqMap, resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(3, "接口调用失败");
        }
        if (!Objects.equals(result.getInteger("code"), 0)) {
            return Pair.of(3, result.getString("message"));
        }
        return Pair.of(result.getJSONObject("data").getInteger("status"), result.getString("message"));
    }

    /**
     * [桂郃]查询订单接口
     */
    private Integer ghQuery(String orderId) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return 1;
        }

        JSONObject reqMap = new JSONObject();
        reqMap.put("timestamp", System.currentTimeMillis());
        reqMap.put("macId", GH_MAC_ID);
        reqMap.put("macOrderId", orderId);
        reqMap.put("sign", ghSign(reqMap));
        String resp = HttpUtil.post(GH_API_URL + "/api/flow/query", reqMap.toString());
        log.info("[桂郃]运营商会员查询订单接口调用, req={}, resp={}", reqMap, resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp)) {
            resp = HttpUtil.post(GH_API_URL + "/api/flow/query", reqMap.toString());
            log.info("[桂郃]运营商会员查询订单接口调用-重试, req={}, resp={}", reqMap, resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result || !Objects.equals(result.getInteger("code"), 0)) {
            return 1;
        }
        return result.getJSONObject("data").getInteger("status");
    }

    /**
     * 桂郃回调
     */
    @CrossOrigin
    @PostMapping("/ghCallback")
    public JSONObject ghCallback(@RequestBody IspVipGhCallbackReq req) {
        log.info("[桂郃]运营商会员接口回调，req={}", JSON.toJSONString(req));
        IspVipLandpageFormRecordEntity record = landpageFormRecordService.selectByBizOrderNoAndOrderId(req.getOrderId(), req.getMacOrderId());
        if (null != record && !Objects.equals(record.getStatus(), 1)) {
            if (Objects.equals(req.getStatus(), 2)) {
                updateRecord(record.getId(), true, StrUtil.blankToDefault(req.getMessage(), "订购成功"));
                landpageClick(record.getOrderId());
            } else if (Objects.equals(req.getStatus(), 3)) {
                updateRecord(record.getId(), false, StrUtil.blankToDefault(req.getMessage(), "订购失败"));
            }
        }

        JSONObject result = new JSONObject();
        result.put("code", 0);
        result.put("message", "请求成功");
        return result;
    }

    /**
     * [桂郃]签名
     */
    private String ghSign(Map<String, Object> requestMap) {
        return SecureUtil.md5(getEncode(requestMap.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .map(s -> s.getKey() + s.getValue()).collect(Collectors.joining()) + GH_SECRET)).toUpperCase();
    }

    /**
     * [淘淘汇]获取验证码
     */
    private Pair<Boolean, String> tthSmsCode(String orderId, String phone) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(true, "");
        }

        JSONObject body = new JSONObject();
        body.put("appKey", TTH_APP_KEY);
        body.put("phone", phone);
        body.put("channelCode", TTH_CHANNEL_CODE);
        body.put("timestamp", System.currentTimeMillis());
        body.put("busOrderId", orderId);
        body.put("busCallbackUrl", "http://actengine.ydns.cn/lp/ispVip/tthCallback");
        body.put("sign", tthSign(body));
        String resp = HttpUtil.post(TTH_API_URL + "/mall/tg/huaFei/create/order", body.toString());
        log.info("[淘淘汇]运营商会员获取验证码接口调用, req={}, resp={}", body, resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp)) {
            resp = HttpUtil.post(TTH_API_URL + "/mall/tg/huaFei/create/order", body.toString());
            log.info("[淘淘汇]运营商会员获取验证码接口调用-重试, req={}, resp={}", body, resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(false, "接口调用失败");
        }
        boolean isSuccess = Objects.equals(result.getString("code"), "200");
        return Pair.of(isSuccess, result.getString("msg"));
    }

    /**
     * [淘淘汇]下单接口
     */
    private Pair<Boolean, String> tthOrder(String phone, String smsCode) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(true, "");
        }

        JSONObject body = new JSONObject();
        body.put("appKey", TTH_APP_KEY);
        body.put("phone", phone);
        body.put("smsCode", smsCode);
        body.put("timestamp", System.currentTimeMillis());
        body.put("sign", tthSign(body));
        String resp = HttpUtil.post(TTH_API_URL + "/mall/tg/huaFei/checkCode", body.toString());
        log.info("[淘淘汇]运营商会员下单接口调用, req={}, resp={}", body, resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp)) {
            resp = HttpUtil.post(TTH_API_URL + "/mall/tg/huaFei/checkCode", body.toString());
            log.info("[淘淘汇]运营商会员下单接口调用-重试, req={}, resp={}", body, resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(false, "接口调用失败");
        }
        boolean isSuccess = Objects.equals(result.getString("code"), "200");
        return Pair.of(isSuccess, result.getString("msg"));
    }

    /**
     * [淘淘汇]下单接口
     */
    private Integer tthInfo(String orderId) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return 1;
        }

        JSONObject body = new JSONObject();
        body.put("appKey", TTH_APP_KEY);
        body.put("busOrderId", orderId);
        body.put("timestamp", System.currentTimeMillis());
        body.put("sign", tthSign(body));
        String resp = HttpUtil.post(TTH_API_URL + "/mall/tg/huaFei/order/info", body.toString());
        log.info("[淘淘汇]运营商会员查询订单接口调用, req={}, resp={}", body, resp);
        // 失败重试
        if (!JSONUtil.isTypeJSON(resp)) {
            resp = HttpUtil.post(TTH_API_URL + "/mall/tg/huaFei/order/info", body.toString());
            log.info("[淘淘汇]运营商会员查询订单接口调用-重试, req={}, resp={}", body, resp);
        }
        JSONObject result = JSON.parseObject(resp);
        if (null == result || !Objects.equals(result.getInteger("code"), 200)) {
            return 1;
        }
        return result.getJSONObject("data").getInteger("payStatus");
    }

    /**
     * 淘淘汇回调
     */
    @CrossOrigin
    @PostMapping("/tthCallback")
    public AjaxResult tthCallback(@RequestBody IspVipTthCallbackReq req) {
        log.info("[淘淘汇]运营商会员接口回调，req={}", JSON.toJSONString(req));
        IspVipLandpageFormRecordEntity record = landpageFormRecordService.selectByOrderId(req.getBusOrderId());
        if (null != record && !Objects.equals(record.getStatus(), 1)) {
            if (Objects.equals(req.getPayStatus(), 2)) {
                updateRecord(record.getId(), true, "支付成功");
                landpageClick(record.getOrderId());
            } else if (Objects.equals(req.getPayStatus(), 3)) {
                updateRecord(record.getId(), false, "支付失败");
            }
        }
        return AjaxResult.success();
    }

    /**
     * [淘淘汇]签名
     */
    private String tthSign(JSONObject body) {
        // 下单签名
        if (StringUtils.isNotBlank(body.getString("smsCode"))) {
            return SecureUtil.md5(body.getString("appKey") + TTH_APP_SECRET + body.getString("phone") + body.getString("smsCode") + body.getString("timestamp"));
        }
        // 订单查询签名
        if (StringUtils.isBlank(body.getString("phone"))) {
            return SecureUtil.md5(body.getString("appKey") + TTH_APP_SECRET + body.getString("timestamp") + body.getString("busOrderId"));
        }
        // 发送验证码签名
        return SecureUtil.md5(
                body.getString("appKey") + TTH_APP_SECRET + body.getString("phone") + body.getString("channelCode")
                        + body.getString("timestamp") + body.getString("busOrderId") + body.getString("busCallbackUrl")
        );
    }

    /**
     * [创蜗]获取验证码接口
     */
    private Pair<Boolean, Pair<String, String>> cwCreate(String orderId, String phone, String channelId, String ip, String userAgent) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(true, Pair.of("", ""));
        }

        JSONObject body = new JSONObject();
        body.put("aOId", orderId + "_" + phone);
        body.put("ip", ip);
        body.put("channelId", channelId);
        body.put("phone", phone);
        body.put("userAgent", UrlUtils.urlEncode(userAgent));
        String resp = HttpUtil.post("http://*************:13569/nuoHeWeb/applyCode", body.toString());
        log.info("[创蜗]运营商会员获取验证码接口调用, body={}, resp={}", body, resp);
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(false, Pair.of("接口调用失败", ""));
        }
        if (Objects.equals(result.getInteger("result"), 0)) {
            return Pair.of(true, Pair.of(result.getString("msg"), result.getString("orderId")));
        }
        if (null != result.getBoolean("success")) {
            return Pair.of(result.getBoolean("success"), Pair.of(result.getString("desc"), ""));
        }
        return Pair.of(false, Pair.of(result.getString("msg"), ""));
    }

    /**
     * [创蜗]下单接口
     */
    private Pair<Boolean, String> cwPay(String orderId, String phone, String smsCode) {
        // 只有生产环境才调用
        if (!SpringEnvironmentUtils.isProd()) {
            return Pair.of(true, "");
        }

        JSONObject body = new JSONObject();
        body.put("aOId", orderId + "_" + phone);
        body.put("smsCode", smsCode);
        String resp = HttpUtil.post("http://*************:13569/nuoHeWeb/validateCode", body.toString());
        log.info("[创蜗]运营商会员下单接口调用, req={}, resp={}", body, resp);
        JSONObject result = JSON.parseObject(resp);
        if (null == result) {
            return Pair.of(false, "接口调用失败");
        }
        if (Objects.equals(result.getInteger("result"), 0)) {
            return Pair.of(true, result.getString("msg"));
        }
        if (null != result.getBoolean("success")) {
            return Pair.of(result.getBoolean("success"), result.getString("desc"));
        }
        return Pair.of(false, result.getString("msg"));
    }

    /**
     * [创蜗]回调
     */
    @CrossOrigin
    @GetMapping("/cwCallback")
    public JSONObject cwCallback(IspVipCwCallbackReq req) {
        log.info("[创蜗]运营商会员接口回调，req={}", JSON.toJSONString(req));
        if (StringUtils.isNotBlank(req.getaOId()) && req.getaOId().contains("_")) {
            List<String> params = StrUtil.split(req.getaOId(), "_", true, true);
            if (params.size() >= 2) {
                IspVipLandpageFormRecordEntity record = landpageFormRecordService.selectBy(params.get(0), params.get(1));
                if (null != record && !Objects.equals(record.getStatus(), 1)) {
                    updateRecord(record.getId(), true, "订购成功");
                    landpageClick(record.getOrderId());
                    statService.convertEvent(record.getOrderId(), ConvType.PAY.getType());
                }
            }
        }

        JSONObject result = new JSONObject();
        result.put("result", 0);
        result.put("msg", "success");
        return result;
    }


    /**
     * 延迟查询订单
     */
    private void queryOrderDelay(final Long recordId) {
        try {
            scheduledExecutor.schedule(() -> {
                try {
                    IspVipLandpageFormRecordEntity record = landpageFormRecordService.selectById(recordId);
                    if (null == record || !Objects.equals(record.getStatus(), 0)) {
                        return;
                    }

                    if (Objects.equals(record.getProductType(), IspVipProductTypeEnum.GUIHE.getType())) {
                        Integer payStatus = ghQuery(record.getOrderId());
                        if (Objects.equals(payStatus, 2)) {
                            updateRecord(record.getId(), true, "支付成功");
                            landpageClick(record.getOrderId());
                        } else if (Objects.equals(payStatus, 3)) {
                            updateRecord(record.getId(), false, "支付失败");
                        }
                    } else if (Objects.equals(record.getProductType(), IspVipProductTypeEnum.TAOTAOHUI.getType())) {
                        Integer payStatus = tthInfo(record.getOrderId());
                        if (Objects.equals(payStatus, 2)) {
                            updateRecord(record.getId(), true, "支付成功");
                            landpageClick(record.getOrderId());
                        } else if (Objects.equals(payStatus, 3)) {
                            updateRecord(record.getId(), false, "支付失败");
                        }
                    }
                } catch (Exception e) {
                    log.error("延迟查询订单异常, recordId={}", recordId, e);
                }
            }, 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("queryOrderDelay, recordId={}", recordId, e);
        }
    }

    /**
     * 更新表单
     */
    private void updateRecord(Long recordId, boolean isSuccess, String msg) {
        updateRecord(recordId, isSuccess, msg, null);
    }

    /**
     * 更新表单
     */
    private void updateRecord(Long recordId, boolean isSuccess, String msg, String bizOrderNo) {
        IspVipLandpageFormRecordEntity updateRecord = new IspVipLandpageFormRecordEntity();
        updateRecord.setId(recordId);
        updateRecord.setStatus(isSuccess ? 1 : 2);
        updateRecord.setMsg(msg);
        updateRecord.setBizOrderNo(bizOrderNo);
        landpageFormRecordService.updateById(updateRecord);
    }

    /**
     * 小写的UrlEncode
     */
    private String getEncode(String needEncodeStr) {
        try {
            String encodeStr = URLEncoder.encode(needEncodeStr, "utf-8");
            String[] uppercase = new String[0XFF + 1];
            String[] lowercase = new String[0XFF + 1];
            for (int i = 0; i <= 0XFF; i++) {
                uppercase[i] = "%" + String.format("%02x", i);
                lowercase[i] = uppercase[i];
                uppercase[i] = uppercase[i].toUpperCase();
            }
            return StringUtils.replaceEach(encodeStr, uppercase, lowercase);
        } catch (Exception e) {
            log.error("getEncode", e);
        }
        return needEncodeStr;
    }

    /**
     * 落地页转化
     */
    private void landpageClick(String orderId) {
        // 转化埋点
        statService.innerLogStatByOrderId(LANDPAGE_CLICK, orderId);

        // 固定收益上报
        Order order = orderService.selectByOrderId(orderId);
        if (null != order) {
            JSONObject param = callbackService.getParameterFromCache(orderId);
            slotUpService.slotUpAddCostMulti(order, param.getString("hu"));
            convCallbackService.handleAsync(LANDPAGE_CLICK, null, order);
        }
    }
}
