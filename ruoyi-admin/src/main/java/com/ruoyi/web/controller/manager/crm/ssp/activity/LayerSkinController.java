package com.ruoyi.web.controller.manager.crm.ssp.activity;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.system.entity.advert.LayerSkin;
import com.ruoyi.system.service.manager.LayerSkinService;
import com.ruoyi.system.vo.advert.LayerSkinVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 弹层皮肤Controller
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
@RestController
@RequestMapping("/manager/layer/skin")
public class LayerSkinController extends BaseController {

    @Autowired
    private LayerSkinService layerSkinService;

    /**
     * 查询弹层皮肤列表
     */
    @GetMapping("/listTotal")
    public Result<List<LayerSkinVO>> list() {
        List<LayerSkin> list = layerSkinService.selectLayerSkinList(new LayerSkin());
        return ResultBuilder.success(BeanUtil.copyToList(list, LayerSkinVO.class));
    }
}
