package com.ruoyi.web.controller.manager.wis.advertiser.info;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.advertiser.QualificationAuditStatus;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity;
import com.ruoyi.system.req.advertiser.qualification.WisAdvertiserCompanyInfoReq;
import com.ruoyi.system.service.advertiser.AdvertiserQualificationService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.vo.advertiser.qualification.WisAdvertiserCompanyInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * [广告主平台]客户信息
 *
 * <AUTHOR>
 * @date 2023/03/29
 */
@RestController
@RequestMapping("/wis/advertiser/info/company")
public class WisAdvertiserCompanyInfoController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertiserQualificationService advertiserQualificationService;

    @Autowired
    public TransactionTemplate transactionTemplate;

    /**
     * 查询客户信息
     */
    @GetMapping("/info")
    public Result<WisAdvertiserCompanyInfoVO> info() {
        Long accountId = SecurityUtils.getLoginUser().getCrmAccountId();
        Account account = accountService.selectAccountById(accountId);
        if (null == account) {
            return ResultBuilder.success();
        }
        AccountExtInfo extInfo = Optional.ofNullable(JSON.parseObject(account.getExtInfo(), AccountExtInfo.class)).orElse(new AccountExtInfo());

        WisAdvertiserCompanyInfoVO info = new WisAdvertiserCompanyInfoVO();
        // 联系人信息
        info.setContract(account.getContact());
        info.setPhone(account.getPhone());
        info.setEmail(account.getEmail());
        info.setCompanyName(account.getCompanyName());

        // 营业执照
        info.setBusinessLicense(extInfo.getBusinessLicense());
        AdvertiserQualificationEntity qualification = advertiserQualificationService.selectBy(accountId, 0L, "营业执照");
        if (null != qualification) {
            info.setExpireTime(qualification.getExpireTime());
            info.setBusinessLicenseImg(CollUtil.getFirst(JSON.parseArray(qualification.getQualificationImg(), String.class)));
            info.setAuditStatus(qualification.getAuditStatus());
            info.setAuditReason(qualification.getAuditReason());
        }
        return ResultBuilder.success(info);
    }

    /**
     * 更新客户信息
     */
    @PostMapping("update")
    public Result<Boolean> update(@RequestBody WisAdvertiserCompanyInfoReq req) {
        Long accountId = SecurityUtils.getLoginUser().getCrmAccountId();
        Account account = accountService.selectAccountById(accountId);
        if (null == account) {
            return ResultBuilder.fail("未查询到客户信息");
        }
        AccountExtInfo extInfo = Optional.ofNullable(JSON.parseObject(account.getExtInfo(), AccountExtInfo.class)).orElse(new AccountExtInfo());
        AdvertiserQualificationEntity qualification = advertiserQualificationService.selectBy(accountId, 0L, "营业执照");
        if (null == qualification) {
            return ResultBuilder.fail("未查询到营业执照，请联系平台处理");
        }
        // 更新营业执照
        AdvertiserQualificationEntity updateQualification = new AdvertiserQualificationEntity();
        updateQualification.setId(qualification.getId());
        updateQualification.setQualificationImg(JSON.toJSONString(Collections.singletonList(req.getBusinessLicenseImg())));
        updateQualification.setExpireTime(req.getExpireTime());
        if (!Objects.equals(extInfo.getBusinessLicense(), req.getBusinessLicense())
                || !Objects.equals(CollUtil.getFirst(JSON.parseArray(qualification.getQualificationImg(), String.class)), req.getBusinessLicenseImg())) {
            updateQualification.setApplicationTime(new Date());
            updateQualification.setAuditStatus(QualificationAuditStatus.READY.getStatus());
        }
        advertiserQualificationService.updateById(updateQualification);

        // 更新联系人信息
        Account updateAccount = new Account();
        updateAccount.setId(accountId);
        updateAccount.setContact(req.getContract());
        updateAccount.setPhone(req.getPhone());
        extInfo.setBusinessLicense(req.getBusinessLicense());
        updateAccount.setExtInfo(JSON.toJSONString(extInfo));
        accountService.updateAccount(updateAccount);
        return ResultBuilder.success();
    }
}
