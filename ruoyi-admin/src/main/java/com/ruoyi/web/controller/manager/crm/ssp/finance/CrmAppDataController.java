package com.ruoyi.web.controller.manager.crm.ssp.finance;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.ConfirmStatusEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slotcharge.SlotChargeEntity;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.req.datashow.AppMonthDataReq;
import com.ruoyi.system.req.datashow.SlotMonthDataListReq;
import com.ruoyi.system.req.datashow.UpdateMonthDataListReq;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.slotcharge.SlotChargeService;
import com.ruoyi.system.vo.datashow.AppSlotMonthDataVO;
import com.ruoyi.system.vo.datashow.CrmAppMonthDataExportVO;
import com.ruoyi.system.vo.datashow.CrmAppMonthDataStatisticsVO;
import com.ruoyi.system.vo.datashow.CrmAppMonthDataVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * crm媒体数据
 *
 * <AUTHOR>
 * @date 2021-09-02
 */
@RestController
@RequestMapping("/datashow/crmAppData")
public class CrmAppDataController extends BaseController {

    @Autowired
    private AppMonthDataService appMonthDataService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private SlotChargeService slotChargeService;

    /**
     * 查询媒体月数据列表 crm专用
     *
     * @param req 请求参数
     */
    @GetMapping("/monthList")
    public TableDataInfo<CrmAppMonthDataVO> monthList(@Validated AppMonthDataReq req) {
        PageInfo<CrmAppMonthDataVO> pageInfo = appMonthDataService.selectCrmAppMonthList(req, false);
        return getDataTable(pageInfo);
    }

    /**
     * 账号统计媒体月数据 总计
     *
     * @param req 请求参数
     * @return 结果
     */
    @GetMapping("statisticsAppData")
    public Result<CrmAppMonthDataStatisticsVO> statisticsAppData(AppMonthDataReq req) {
        return ResultBuilder.success(appMonthDataService.statisticsCrmAppMonthData(req));
    }

    /**
     * 导出媒体月账单列表 crm专用
     *
     * @param req 请求参数
     */
    @Log(title = "crm媒体月账单数据", businessType = BusinessType.EXPORT)
    @GetMapping("/exportMonthData")
    public Result exportMonthData(AppMonthDataReq req) {
        PageInfo<CrmAppMonthDataVO> pageInfo = appMonthDataService.selectCrmAppMonthList(req, true);
        List<CrmAppMonthDataExportVO> exportList = pageInfo.getList().stream().map(data -> convertMonthDataExportVO(data)).collect(Collectors.toList());

        ExcelUtil<CrmAppMonthDataExportVO> util = new ExcelUtil<>(CrmAppMonthDataExportVO.class);
        return util.exportExcelResult(exportList, "媒体月账单");
    }

    /**
     * 确认结算单
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @PostMapping("/confirmStatement")
    @Log(title = "确认结算单", businessType = BusinessType.UPDATE)
    public Result<Boolean> confirmStatement(@RequestBody @Validated IdReq req) {
        return ResultBuilder.success(appMonthDataService.confirmStatement(req.getId()));
    }

    /**
     * 更新媒体月账单
     *
     * @param req 请求参数
     * @return 结果
     */
    @Log(title = "更新媒体月账单", businessType = BusinessType.UPDATE)
    @PostMapping("updateMonthData")
    public Result<Boolean> updateMonthData(@RequestBody UpdateMonthDataListReq req) {
        if (Objects.isNull(req) || NumberUtils.isNullOrLteZero(req.getAppId()) || CollectionUtils.isEmpty(req.getUpdateMonthDataReqs())) {
            throw new CustomException(ErrorCode.ARGS);
        }

        List<String> checkList = new ArrayList<>();
        //判断是否重复修改广告位和日期同一天的数据
        req.getUpdateMonthDataReqs().forEach(data -> {
            String tempKey = StringUtils.join(data.getSlotId(), "_", DateUtils.dateTime(data.getCurDate()));
            if (checkList.contains(tempKey)) {
                throw new CustomException(ErrorCode.E108004);
            }
            checkList.add(tempKey);
        });

        return ResultBuilder.success(appMonthDataService.updateMonthData(req));
    }

    /**
     * 媒体月账单查询广告位数据
     *
     * @param req
     * @return
     */
    @GetMapping("slotDataList")
    public Result<List<AppSlotMonthDataVO>> slotDataList(@Validated SlotMonthDataListReq req) {
        List<Long> slotIds = req.getSlotIds();
        if(CollectionUtils.isEmpty(slotIds)){
            //查询当前媒体下的所有广告位id
            slotIds = slotService.selectSlotIdsByAppIds(Lists.newArrayList(req.getAppId()));
        }
        SlotData data = new SlotData();
        data.setAppId(req.getAppId());
        data.setSlotIds(req.getSlotIds());
        data.setStartDate(DateUtil.beginOfDay(req.getStartDate()));
        data.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        //查询出所有广告位数据
        List<SlotData> slotDatas = slotDataService.selectAllSlotDataList(data);
        Map<String, SlotData> slotDataMap = slotDatas.stream().collect(Collectors.toMap(slotData -> StringUtils.join(DateUtil.formatDate(slotData.getCurDate()), "-",slotData.getSlotId()), Function.identity(), (v1, v2) -> v1));
        //查询广告位名称
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(slotIds);
        //查询广告位每日计费方式
        List<SlotChargeEntity> slotChargeEntities = slotChargeService.selectListBySlotIdsAndDateRange(slotIds, data.getStartDate(), data.getEndDate());
        Map<String, SlotChargeEntity> chargeEntityMap = slotChargeEntities.stream().collect(Collectors.toMap(charge -> StringUtils.join(DateUtil.formatDate(charge.getCurDate()), "-",charge.getSlotId()), Function.identity(), (v1, v2) -> v1));

        int daySpan = (int) DateUtil.betweenDay(req.getStartDate(), req.getEndDate(), true);
        List<Long> finalSlotIds = slotIds;

        List<AppSlotMonthDataVO> resultList = new ArrayList<>();
        IntStream.range(0,daySpan+1).forEach(addDay ->{
            Date date = DateUtils.addDays(req.getEndDate(), -addDay);

            List<AppSlotMonthDataVO> list = finalSlotIds.stream().map(slotId -> {
                String groupKey = StringUtils.join(DateUtil.formatDate(date), "-",slotId);
                SlotData slotData = slotDataMap.get(groupKey);

                AppSlotMonthDataVO vo = new AppSlotMonthDataVO();
                vo.setCurDate(date);
                vo.setSlotId(slotId);
                vo.setSlotName(slotNameMap.get(slotId));
                if (Objects.nonNull(slotData)) {
                    vo.setAppRevenue(slotData.getAppRevenue());
                    vo.setSlotRequestPv(slotData.getSlotRequestPv());
                    vo.setSlotRequestUv(slotData.getSlotRequestUv());
                    vo.setOuterCost(slotData.getOuterCost());
                    vo.setNhCost(slotData.getNhCost());
                }else{
                    vo.setSlotRequestUv(0);
                }
                SlotChargeEntity chargeEntity = chargeEntityMap.get(groupKey);
                if (Objects.nonNull(chargeEntity)) {
                    vo.setChargePrice(chargeEntity.getChargePrice());
                    vo.setChargeType(chargeEntity.getChargeType());
                }
                return vo;
            }).sorted(Comparator.comparing(AppSlotMonthDataVO::getSlotRequestUv).reversed()).collect(Collectors.toList());
            resultList.addAll(list);
        });
        return ResultBuilder.success(resultList);
    }

    /**
     * 数据转换导出vo 月账单
     *
     * @param data 广告位数据
     * @return exportVo
     */
    private CrmAppMonthDataExportVO convertMonthDataExportVO(CrmAppMonthDataVO data) {
        CrmAppMonthDataExportVO exportVO = new CrmAppMonthDataExportVO();

        BeanUtils.copyBeanProp(exportVO, data);
        exportVO.setTotalConsume(NumberUtils.fenToYuan(data.getTotalConsume(), "-"));
        exportVO.setNhConsume(NumberUtils.fenToYuan(data.getNhConsume(), "-"));
        exportVO.setOuterConsume(NumberUtils.fenToYuan(data.getOuterConsume(), "-"));
        exportVO.setAppRevenue(NumberUtils.fenToYuan(data.getAppRevenue(), "-"));
        exportVO.setAppName(String.format("%s（ID:%s）", data.getAppName(), data.getAppId()));
        if (NumberUtils.isNullOrLteZero(exportVO.getSlotRequestPv())) {
            exportVO.setJoinRate("0");
        } else {
            exportVO.setJoinRate(NumberUtils.calculateRate(NumberUtils.defaultInt(exportVO.getJoinPv()), exportVO.getSlotRequestPv()));
        }
        exportVO.setNhCost(NumberUtils.fenToYuan(data.getNhCost(), "0"));
        exportVO.setOuterCost(NumberUtils.fenToYuan(data.getOuterCost(), "0"));
        exportVO.setTotalCost(NumberUtils.fenToYuan(data.getTotalCost(), "0"));
        exportVO.setAccountEmail(String.format("%s（ID:%s）", data.getEmail(), data.getAccountId()));
        exportVO.setIdStr(data.getId().toString());
        exportVO.setStatus(ConfirmStatusEnum.getDescByStatus(data.getConfirmStatus()));
        return exportVO;
    }
}
