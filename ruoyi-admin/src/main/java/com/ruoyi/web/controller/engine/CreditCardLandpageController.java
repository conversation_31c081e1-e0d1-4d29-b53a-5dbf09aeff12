package com.ruoyi.web.controller.engine;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.req.engine.CreditCardLandpageFormReq;
import com.ruoyi.system.service.landpage.CreditCardLandpageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 信用卡落地页(不鉴权)
 *
 * <AUTHOR>
 * @date 2023/06/02
 */
@Slf4j
@RestController
@RequestMapping("/lp/creditCard")
public class CreditCardLandpageController {

    @Autowired
    private CreditCardLandpageService creditCardLandpageService;

    /**
     * 表单提交
     */
    @CrossOrigin
    @PostMapping("/submit")
    public Result<Void> submit(@RequestBody CreditCardLandpageFormReq req) {
        if (StringUtils.isBlank(req.getOrderId())) {
            return ResultBuilder.fail("链接已失效");
        }
        if (null == req.getCreditLimit() || req.getCreditLimit() < 0) {
            return ResultBuilder.fail("申办额度不能为负");
        }
        if (StrUtil.length(req.getPhone()) != 11) {
            return ResultBuilder.fail("请输入有效的手机号");
        }
        if (StrUtil.isBlank(req.getIdCard())) {
            return ResultBuilder.fail("身份证号不能为空");
        }
        req.setIdCard(req.getIdCard().toUpperCase());
        if (!IdcardUtil.isValidCard(req.getIdCard())) {
            return ResultBuilder.fail("请输入有效的身份证号");
        }

        creditCardLandpageService.formSubmit(req);
        return ResultBuilder.success();
    }
}
