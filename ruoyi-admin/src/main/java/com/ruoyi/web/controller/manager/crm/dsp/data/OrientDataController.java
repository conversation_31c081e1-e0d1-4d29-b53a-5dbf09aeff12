package com.ruoyi.web.controller.manager.crm.dsp.data;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.advert.AdvertStatusEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.bo.advert.OrientDayDataBo;
import com.ruoyi.system.bo.advert.OrientHourDataParam;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.req.advert.OrientAppDayDataReq;
import com.ruoyi.system.req.advert.OrientDayDataReq;
import com.ruoyi.system.req.advert.OrientHourDataReq;
import com.ruoyi.system.req.advert.OrientSlotDayDataReq;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.datasource.OrientHourDataService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SearchConditionService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.vo.advert.OrientAppDayDataVO;
import com.ruoyi.system.vo.advert.OrientBaseDataVO;
import com.ruoyi.system.vo.advert.OrientDayDataVO;
import com.ruoyi.system.vo.advert.OrientHourDataVO;
import com.ruoyi.system.vo.advert.OrientSlotDayDataVO;
import com.ruoyi.system.vo.advert.OrientSlotDayDataVO2;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.ruoyi.common.constant.BizConstants.INVALID_ID_LIST;
import static com.ruoyi.common.enums.common.SwitchStatusEnum.isSwitchOff;

/**
 * [CRM后台]广告配置数据
 *
 * <AUTHOR>
 * @date 2023-7-12
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/data/orientData")
public class OrientDataController extends BaseController {

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private OrientHourDataService orientHourDataService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private SearchConditionService searchConditionService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    /**
     * 查询配置日数据
     */
    @GetMapping("/orientDayDataList")
    public TableDataInfo<OrientDayDataVO> orientDayDataList(OrientDayDataReq req) {
        PageInfo<OrientDayDataVO> orientDayDataPage = selectOrientDayData(req);
        return getDataTable(orientDayDataPage);
    }

    /**
     * 查询配置日汇总数据
     */
    @GetMapping("/statisticOrientDayData")
    public Result<OrientBaseDataVO> statisticOrientDayData(OrientDayDataReq req) {
        return ResultBuilder.success(selectStatisticOrientDayData(req));
    }

    /**
     * 导出配置日数据
     */
    @Log(title = "配置日数据", businessType = BusinessType.EXPORT)
    @GetMapping("/orientDayDataExport")
    public AjaxResult advertDayDataExport(OrientDayDataReq req) {
        req.setIsExport(true);
        PageInfo<OrientDayDataVO> orientDayDataPage = selectOrientDayData(req);
        ExcelUtil<OrientDayDataVO> util = new ExcelUtil<>(OrientDayDataVO.class);
        return util.exportExcel(orientDayDataPage.getList(), "配置日数据");
    }

    /**
     * 导出配置日数据(含广告位)
     */
    @Log(title = "配置日数据含广告位",businessType = BusinessType.EXPORT)
    @GetMapping("/orientDayDataIncludeSlotExport")
    public AjaxResult orientDayDataIncludeSlotExport(OrientDayDataReq req) {
        req.setIsExport(true);
        PageInfo<OrientSlotDayDataVO2> orientDayDataPage = selectOrientSlotDayDataForExport(req);
        ExcelUtil<OrientSlotDayDataVO2> util = new ExcelUtil<>(OrientSlotDayDataVO2.class);
        return util.exportExcel(orientDayDataPage.getList(), "配置日数据");
    }

    /**
     * 查询配置时段数据
     */
    @GetMapping("/orientHourDataList")
    public TableDataInfo<OrientHourDataVO> orientHourDataList(OrientHourDataReq req) {
        PageInfo<OrientHourDataVO> orientHourDataPage = selectOrientHourData(req);
        return getDataTable(orientHourDataPage);
    }

    /**
     * 查询配置时段汇总数据
     */
    @GetMapping("/statisticAdvertHourData")
    public Result<OrientBaseDataVO> statisticAdvertHourData(OrientHourDataReq req) {
        return ResultBuilder.success(selectStatisticOrientHourData(req));
    }

    /**
     * 导出配置时段数据
     */
    @Log(title = "配置时段数据", businessType = BusinessType.EXPORT)
    @GetMapping("/orientHourDataExport")
    public AjaxResult orientHourDataExport(OrientHourDataReq req) {
        req.setIsExport(true);
        PageInfo<OrientHourDataVO> orientHourDataPage = selectOrientHourData(req);
        ExcelUtil<OrientHourDataVO> util = new ExcelUtil<>(OrientHourDataVO.class);
        return util.exportExcel(orientHourDataPage.getList(), "配置时段数据");
    }

    /**
     * 查询配置分媒体日数据
     */
    @GetMapping("/orientAppDayDataList")
    public TableDataInfo<OrientAppDayDataVO> orientAppDayDataList(OrientAppDayDataReq req) {
        PageInfo<OrientAppDayDataVO> orientHourDataPage = selectOrientAppDayData(req);
        return getDataTable(orientHourDataPage);
    }

    /**
     * 查询配置分媒体日汇总数据
     */
    @GetMapping("/statisticOrientAppDayData")
    public Result<OrientBaseDataVO> statisticOrientAppDayData(OrientAppDayDataReq req) {
        return ResultBuilder.success(selectStatisticOrientAppDayData(req));
    }

    /**
     * 导出配置分媒体日数据
     */
    @Log(title = "广告媒体维度日数据", businessType = BusinessType.EXPORT)
    @GetMapping("/orientAppDayDataExport")
    public AjaxResult orientAppDayDataExport(OrientAppDayDataReq req) {
        req.setIsExport(true);
        PageInfo<OrientAppDayDataVO> orientAppDataPage = selectOrientAppDayData(req);
        ExcelUtil<OrientAppDayDataVO> util = new ExcelUtil<>(OrientAppDayDataVO.class);
        return util.exportExcel(orientAppDataPage.getList(), "配置分媒体日数据");
    }

    /**
     * 查询配置分广告位日数据
     */
    @GetMapping("/orientSlotDayDataList")
    public TableDataInfo<OrientAppDayDataVO> orientSlotDayDataList(OrientSlotDayDataReq req) {
        PageInfo<OrientSlotDayDataVO> orientSlotDataPage = selectOrientSlotDayData(req);
        return getDataTable(orientSlotDataPage);
    }

    /**
     * 查询配置分广告位日数据汇总
     */
    @GetMapping("/statisticAdvertSlotDayData")
    public Result<OrientBaseDataVO> statisticOrientSlotDayData(OrientSlotDayDataReq req) {
        return ResultBuilder.success(selectStatisticOrientSlotDayData(req));
    }

    /**
     * 导出配置分广告位日数据
     */
    @Log(title = "配置分广告位日数据", businessType = BusinessType.EXPORT)
    @GetMapping("/advertSlotDayDataExport")
    public AjaxResult advertSlotDayDataExport(OrientSlotDayDataReq req) {
        req.setIsExport(true);
        PageInfo<OrientSlotDayDataVO> orientSlotDataPage = selectOrientSlotDayData(req);
        ExcelUtil<OrientSlotDayDataVO> util = new ExcelUtil<>(OrientSlotDayDataVO.class);
        return util.exportExcel(orientSlotDataPage.getList(), "配置分广告位日数据");
    }

    /**
     * 查询配置日数据
     */
    public PageInfo<OrientDayDataVO> selectOrientDayData(OrientDayDataReq req) {
        // 构造搜索条件
        OrientHourDataParam param = new OrientHourDataParam();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setOrderColumn(req.getOrderColumn());
        if (req.getIsAsc() != null) {
            param.setOrderType(req.getIsAsc() ? "ASC" : "DESC");
        }

        // 配置关键词查询
        param.setOrientIds(searchConditionService.filterOrientIdsByOrientSearch(param.getOrientIds(), req.getOrientSearch()));
        // 广告关键词查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertSearch(param.getAdvertIds(), req.getAdvertSearch()));
        // 广告主/代理商查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertiserOrAgent(param.getAdvertIds(), req.getAdvertiserIds(), req.getAgentIds()));
        // 负责人查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByManagerIds(param.getAdvertIds(), req.getManagerIds()));
        // 数据权限控制
        param.setAdvertIds(searchConditionService.filterAdvertIdsByDatePermission(param.getAdvertIds()));
        if (CollUtil.isEqualList(param.getAdvertIds(), INVALID_ID_LIST) || CollUtil.isEqualList(param.getOrientIds(), INVALID_ID_LIST)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        // 查询配置数据
        if (BooleanUtils.isNotTrue(req.getIsExport())) {
            startPage();
        }
        List<OrientDayDataBo> list = orientHourDataService.selectOrientDayDataList(param);

        // 补充信息
        List<Long> orientIds = ListUtils.mapToList(list, OrientDayDataBo::getOrientId);
        List<Long> advertIds = ListUtils.mapToList(list, OrientDayDataBo::getAdvertId);
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(advertIds);
        Map<Long, AdvertOrientation> orientMap = advertOrientationService.selectMapByIds(orientIds);
        Map<Long, String> managerNameMap = accountService.selectManagerMap(advertService.selectByIds(advertIds, Advert::getAdvertiserId));

        return PageInfoUtils.dto2Vo(list, data -> {
            OrientDayDataVO vo = BeanUtil.copyProperties(data, OrientDayDataVO.class);
            Optional.ofNullable(advertMap.get(data.getAdvertId())).ifPresent(advert -> {
                vo.setAdvertName(advert.getAdvertName());
                vo.setManagerName(managerNameMap.getOrDefault(advert.getAdvertiserId(), ""));
                vo.setStatusStr(AdvertStatusEnum.getStatusStr(advert.getAdvertStatus(), advert.getServingSwitch(), advert.getStartServingDate(), advert.getStopServingDate()));
            });
            Optional.ofNullable(orientMap.get(data.getOrientId())).ifPresent(orient -> {
                vo.setOrientName(orient.getOrientName());
                vo.setBudget(null == orient.getDailyBudget() ? "不限" : NumberUtils.fenToYuan(orient.getDailyBudget()));
                if (Objects.equals(vo.getStatusStr(), "正常")) {
                    if (isSwitchOff(orient.getServingSwitch())) {
                        vo.setStatusStr("已暂停");
                    } else if (null != orient.getDailyBudget()) {
                        if (orient.getDailyBudget() < data.getConsume() + orient.getUnitPrice()) {
                            vo.setStatusStr("配置预算不足");
                        } else if (orient.getDailyBudget() - data.getConsume() < orient.getDailyBudget() / 100) {
                            Long launchConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K080.join(DateUtil.formatDate(data.getCurDate()), orient.getAdvertId(), orient.getId())));
                            if (launchConsume + orient.getUnitPrice() > orient.getDailyBudget()) {
                                vo.setStatusStr("配置超发控制");
                            }
                        }
                    }
                }
            });
            calculateIndicator(vo, data);
            return vo;
        });
    }

    /**
     * 查询配置日数据(包含广告位)
     */
    public PageInfo<OrientSlotDayDataVO2> selectOrientSlotDayDataForExport(OrientDayDataReq req) {
        // 构造搜索条件
        OrientHourDataParam param = new OrientHourDataParam();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setAdvertIds(new ArrayList<>());

        // 广告关键词查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertSearch(param.getAdvertIds(), req.getAdvertSearch()));
        // 广告主/代理商查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertiserOrAgent(param.getAdvertIds(), req.getAdvertiserIds(), req.getAgentIds()));
        // 负责人查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByManagerIds(param.getAdvertIds(), req.getManagerIds()));
        // 数据权限控制
        param.setAdvertIds(searchConditionService.filterAdvertIdsByDatePermission(param.getAdvertIds()));
        // 配置关键词查询
        param.setOrientIds(searchConditionService.filterOrientIdsByOrientSearch(param.getOrientIds(), req.getOrientSearch()));
        if (CollUtil.isEqualList(param.getAdvertIds(), INVALID_ID_LIST) || CollUtil.isEqualList(param.getOrientIds(), INVALID_ID_LIST)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        // 查询配置数据
        List<OrientDayDataBo> list = orientHourDataService.selectOrientSlotDayDataList(param);

        // 补充信息
        List<Long> orientIds = ListUtils.mapToList(list, OrientDayDataBo::getOrientId);
        List<Long> advertIds = ListUtils.mapToList(list, OrientDayDataBo::getAdvertId);
        List<Long> appIds = ListUtils.mapToList(list, OrientDayDataBo::getAppId);
        List<Long> slotIds = ListUtils.mapToList(list, OrientDayDataBo::getSlotId);
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(advertIds);
        Map<Long, AdvertOrientation> orientMap = advertOrientationService.selectMapByIds(orientIds);
        Map<Long, String> managerNameMap = accountService.selectManagerMap(advertService.selectByIds(advertIds, Advert::getAdvertiserId));
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(slotIds);
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);

        return PageInfoUtils.dto2Vo(list, data -> {
            OrientSlotDayDataVO2 vo = BeanUtil.copyProperties(data, OrientSlotDayDataVO2.class);
            vo.setAppName(appNameMap.get(data.getAppId()));
            vo.setSlotName(slotNameMap.get(data.getSlotId()));
            Optional.ofNullable(advertMap.get(data.getAdvertId())).ifPresent(advert -> {
                vo.setAdvertName(advert.getAdvertName());
                vo.setManagerName(managerNameMap.getOrDefault(advert.getAdvertiserId(), ""));
                vo.setStatusStr(AdvertStatusEnum.getStatusStr(advert.getAdvertStatus(), advert.getServingSwitch(), advert.getStartServingDate(), advert.getStopServingDate()));
            });
            Optional.ofNullable(orientMap.get(data.getOrientId())).ifPresent(orient -> {
                vo.setOrientName(orient.getOrientName());
                vo.setBudget(null == orient.getDailyBudget() ? "不限" : NumberUtils.fenToYuan(orient.getDailyBudget()));
                if (Objects.equals(vo.getStatusStr(), "正常")) {
                    if (isSwitchOff(orient.getServingSwitch())) {
                        vo.setStatusStr("配置投放开关关闭");
                    } else if (null != orient.getDailyBudget() && orient.getDailyBudget() < data.getConsume() + orient.getUnitPrice()) {
                        vo.setStatusStr("配置预算不足");
                    }
                }
            });
            calculateIndicator(vo, data);
            return vo;
        });
    }

    /**
     * 查询配置日数据汇总
     */
    public OrientBaseDataVO selectStatisticOrientDayData(OrientDayDataReq req) {
        // 构造搜索条件
        OrientHourDataParam param = new OrientHourDataParam();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setOrderColumn(req.getOrderColumn());
        if (req.getIsAsc() != null) {
            param.setOrderType(req.getIsAsc() ? "ASC" : "DESC");
        }

        // 配置关键词查询
        param.setOrientIds(searchConditionService.filterOrientIdsByOrientSearch(param.getOrientIds(), req.getOrientSearch()));
        // 广告关键词查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertSearch(param.getAdvertIds(), req.getAdvertSearch()));
        // 广告主/代理商查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByAdvertiserOrAgent(param.getAdvertIds(), req.getAdvertiserIds(), req.getAgentIds()));
        // 负责人查询
        param.setAdvertIds(searchConditionService.filterAdvertIdsByManagerIds(param.getAdvertIds(), req.getManagerIds()));
        // 数据权限控制
        param.setAdvertIds(searchConditionService.filterAdvertIdsByDatePermission(param.getAdvertIds()));
        if (CollUtil.isEqualList(param.getAdvertIds(), INVALID_ID_LIST) || CollUtil.isEqualList(param.getOrientIds(), INVALID_ID_LIST)) {
            return null;
        }

        OrientDayDataBo data = orientHourDataService.selectStatisticOrientData(param);
        return convertTo(data);
    }

    /**
     * 查询配置时段数据
     */
    public PageInfo<OrientHourDataVO> selectOrientHourData(OrientHourDataReq req) {
        // 构造搜索条件
        OrientHourDataParam param = new OrientHourDataParam();
        param.setCurDate(req.getDate());
        param.setAdvertId(req.getAdvertId());
        param.setOrientId(req.getOrientId());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 数据权限控制
        if (CollUtil.isEqualList(searchConditionService.filterAdvertIdsByDatePermission(Collections.singletonList(req.getAdvertId())), INVALID_ID_LIST)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        // 查询配置数据
        if (BooleanUtils.isNotTrue(req.getIsExport())) {
            startPage();
        }
        List<OrientDayDataBo> list;
        if (null == param.getAdvertId() && null == param.getOrientId()) {
            list = orientHourDataService.selectOrientHourDataListGroupByDateHour(param);
        } else {
            list = orientHourDataService.selectOrientHourDataList(param);
        }

        // 补充信息
        List<Long> orientIds = ListUtils.mapToList(list, OrientDayDataBo::getOrientId);
        List<Long> advertIds = ListUtils.mapToList(list, OrientDayDataBo::getAdvertId);
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        Map<Long, AdvertOrientation> orientMap = advertOrientationService.selectMapByIds(orientIds);

        return PageInfoUtils.dto2Vo(list, data -> {
            OrientHourDataVO vo = BeanUtil.copyProperties(data, OrientHourDataVO.class);
            vo.setAdvertName(advertNameMap.get(data.getAdvertId()));
            Optional.ofNullable(orientMap.get(data.getOrientId())).ifPresent(orient -> {
                vo.setOrientName(orient.getOrientName());
            });
            vo.setPeriod(String.format("%d:00~%d:00", data.getCurHour(), data.getCurHour() + 1));
            calculateIndicator(vo, data);
            return vo;
        });
    }

    /**
     * 查询配置时段数据汇总
     */
    public OrientBaseDataVO selectStatisticOrientHourData(OrientHourDataReq req) {
        // 构造搜索条件
        OrientHourDataParam param = new OrientHourDataParam();
        param.setCurDate(req.getDate());
        param.setAdvertId(req.getAdvertId());
        param.setOrientId(req.getOrientId());
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 数据权限控制
        if (CollUtil.isEqualList(searchConditionService.filterAdvertIdsByDatePermission(Collections.singletonList(req.getAdvertId())), INVALID_ID_LIST)) {
            return null;
        }

        OrientDayDataBo data = orientHourDataService.selectStatisticOrientData(param);
        return convertTo(data);
    }

    /**
     * 查询配置分媒体日数据
     */
    public PageInfo<OrientAppDayDataVO> selectOrientAppDayData(OrientAppDayDataReq req) {
        // 构造搜索条件
        OrientHourDataParam param = new OrientHourDataParam();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setAppIds(req.getAppIds());
        param.setAdvertId(req.getAdvertId());
        param.setOrientId(req.getOrientId());

        // 数据权限控制
        if (CollUtil.isEqualList(searchConditionService.filterAdvertIdsByDatePermission(Collections.singletonList(req.getAdvertId())), INVALID_ID_LIST)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        // 查询配置数据
        if (BooleanUtils.isNotTrue(req.getIsExport())) {
            startPage();
        }
        List<OrientDayDataBo> list = orientHourDataService.selectOrientAppDayDataList(param);

        // 补充信息
        List<Long> orientIds = ListUtils.mapToList(list, OrientDayDataBo::getOrientId);
        List<Long> advertIds = ListUtils.mapToList(list, OrientDayDataBo::getAdvertId);
        List<Long> appIds = ListUtils.mapToList(list, OrientDayDataBo::getAppId);
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        Map<Long, String> orientNameMap = advertOrientationService.selectOrientNameMap(orientIds);
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);

        return PageInfoUtils.dto2Vo(list, data -> {
            OrientAppDayDataVO vo = BeanUtil.copyProperties(data, OrientAppDayDataVO.class);
            vo.setAdvertName(advertNameMap.get(data.getAdvertId()));
            vo.setAppName(appNameMap.get(data.getAppId()));
            vo.setOrientName(orientNameMap.get(data.getOrientId()));
            calculateIndicator(vo, data);
            return vo;
        });
    }

    /**
     * 查询配置分媒体日数据汇总
     */
    public OrientBaseDataVO selectStatisticOrientAppDayData(OrientAppDayDataReq req) {
        // 构造搜索条件
        OrientHourDataParam param = new OrientHourDataParam();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setAppIds(req.getAppIds());
        param.setAdvertId(req.getAdvertId());
        param.setOrientId(req.getOrientId());

        // 数据权限控制
        if (CollUtil.isEqualList(searchConditionService.filterAdvertIdsByDatePermission(Collections.singletonList(req.getAdvertId())), INVALID_ID_LIST)) {
            return null;
        }

        OrientDayDataBo data = orientHourDataService.selectStatisticOrientData(param);
        return convertTo(data);
    }

    /**
     * 查询配置分广告位日数据
     */
    public PageInfo<OrientSlotDayDataVO> selectOrientSlotDayData(OrientSlotDayDataReq req) {
        // 构造搜索条件
        OrientHourDataParam param = new OrientHourDataParam();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setAppIds(req.getAppIds());
        param.setSlotIds(req.getSlotIds());
        param.setAdvertId(req.getAdvertId());
        param.setOrientId(req.getOrientId());

        // 数据权限控制
        if (CollUtil.isEqualList(searchConditionService.filterAdvertIdsByDatePermission(Collections.singletonList(req.getAdvertId())), INVALID_ID_LIST)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        // 查询配置数据
        if (BooleanUtils.isNotTrue(req.getIsExport())) {
            startPage();
        }
        List<OrientDayDataBo> list = orientHourDataService.selectOrientSlotDayDataList(param);

        // 补充信息
        List<Long> orientIds = ListUtils.mapToList(list, OrientDayDataBo::getOrientId);
        List<Long> advertIds = ListUtils.mapToList(list, OrientDayDataBo::getAdvertId);
        List<Long> appIds = ListUtils.mapToList(list, OrientDayDataBo::getAppId);
        List<Long> slotIds = ListUtils.mapToList(list, OrientDayDataBo::getSlotId);
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        Map<Long, String> orientNameMap = advertOrientationService.selectOrientNameMap(orientIds);
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(slotIds);

        return PageInfoUtils.dto2Vo(list, data -> {
            OrientSlotDayDataVO vo = BeanUtil.copyProperties(data, OrientSlotDayDataVO.class);
            vo.setAdvertName(advertNameMap.get(data.getAdvertId()));
            vo.setAppName(appNameMap.get(data.getAppId()));
            vo.setSlotName(slotNameMap.get(data.getSlotId()));
            vo.setOrientName(orientNameMap.get(data.getOrientId()));
            calculateIndicator(vo, data);
            return vo;
        });
    }

    /**
     * 查询配置分广告位日数据汇总
     */
    public OrientBaseDataVO selectStatisticOrientSlotDayData(OrientSlotDayDataReq req) {
        // 构造搜索条件
        OrientHourDataParam param = new OrientHourDataParam();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setAppIds(req.getAppIds());
        param.setSlotIds(req.getSlotIds());
        param.setAdvertId(req.getAdvertId());
        param.setOrientId(req.getOrientId());

        // 数据权限控制
        if (CollUtil.isEqualList(searchConditionService.filterAdvertIdsByDatePermission(Collections.singletonList(req.getAdvertId())), INVALID_ID_LIST)) {
            return null;
        }

        OrientDayDataBo data = orientHourDataService.selectStatisticOrientData(param);
        return convertTo(data);
    }

    /**
     * Convert OrientDayDataBo To OrientBaseDataVO
     */
    private OrientBaseDataVO convertTo(OrientDayDataBo data) {
        if (null == data) {
            return null;
        }
        OrientBaseDataVO vo = BeanUtil.copyProperties(data, OrientBaseDataVO.class);
        calculateIndicator(vo, data);
        return vo;
    }

    /**
     * 计算指标
     */
    private void calculateIndicator(OrientBaseDataVO vo, OrientDayDataBo data) {
        vo.setConsume(NumberUtils.fenToYuan(data.getConsume()));
        vo.setCtr(NumberUtils.calculatePercent(data.getClickPv(), data.getExposurePv()));
        vo.setCpc(NumberUtils.calculateRate(data.getConsume(), data.getClickPv() * 100));
        vo.setLpExposurePVClickPv(NumberUtils.calculatePercent(data.getLpExposurePv(), data.getClickPv()));
        vo.setTakeCvr(NumberUtils.calculatePercent(data.getTakeUv(), data.getLpExposureUv()));
        vo.setTakeCost(NumberUtils.calculateRate(data.getConsume(), data.getTakeUv() * 100));
        vo.setCvr(NumberUtils.calculatePercent(data.getLpClickPv(), data.getClickPv()));
        vo.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getClickUv()));
        vo.setArpu(NumberUtils.calculateRate(data.getConsume(), data.getAdLaunchPv() * 100));
        vo.setLpClickCost(NumberUtils.calculateRate(data.getConsume(), data.getLpClickPv() * 100));
        vo.setPayCost(NumberUtils.calculateRate(data.getConsume(), data.getPay() * 100));
    }
}
