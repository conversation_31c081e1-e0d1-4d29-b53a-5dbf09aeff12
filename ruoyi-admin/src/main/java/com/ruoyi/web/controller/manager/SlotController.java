package com.ruoyi.web.controller.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.ConfirmStatusEnum;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.enums.domain.DomainType;
import com.ruoyi.common.enums.plugin.PluginSwitchEnum;
import com.ruoyi.common.enums.plugin.PluginTypeEnum;
import com.ruoyi.common.enums.slot.SlotChargeTypeEnum;
import com.ruoyi.common.enums.slot.SlotStatusEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.bo.slot.SlotDomainConfigBo;
import com.ruoyi.system.domain.slot.AreaTargetRedirectItem;
import com.ruoyi.system.domain.slot.ShuntRedirectItem;
import com.ruoyi.system.entity.activity.Activity;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.entity.slotcharge.SlotChargeEntity;
import com.ruoyi.system.manager.slot.SlotAdvertManager;
import com.ruoyi.system.manager.slot.SlotDataManager;
import com.ruoyi.system.req.slot.RetConfigReq;
import com.ruoyi.system.req.slot.SlotAdjustSwitchReq;
import com.ruoyi.system.req.slot.SlotChargeDataBatchUpdateReq;
import com.ruoyi.system.req.slot.SlotChargeDataUpdateReq;
import com.ruoyi.system.req.slot.SlotDataAnalysisExcelReq;
import com.ruoyi.system.req.slot.SlotDegradeOrientReq;
import com.ruoyi.system.req.slot.SlotModifyReq;
import com.ruoyi.system.req.slot.SlotRedirectValueOrientReq;
import com.ruoyi.system.req.slot.SlotReq;
import com.ruoyi.system.req.slot.SlotSwitchConfigReq;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.app.AppTagRelationService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.domain.DomainReplaceService;
import com.ruoyi.system.service.engine.cache.DomainCacheService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.ActivityService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotConfigService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.slot.SlotTagRelationService;
import com.ruoyi.system.service.slotcharge.SlotChargeOperLogService;
import com.ruoyi.system.service.slotcharge.SlotChargeService;
import com.ruoyi.system.service.validate.SlotRedirectValidateService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.activity.ActivitySelectVO;
import com.ruoyi.system.vo.advert.AdvertOrientSelectPageVO;
import com.ruoyi.system.vo.advert.AdvertProportionVO;
import com.ruoyi.system.vo.app.AppSimpleVO;
import com.ruoyi.system.vo.datashow.SlotChargeDataAnalysisExcelResultVO;
import com.ruoyi.system.vo.datashow.SlotChargeDataAnalysisExcelVO;
import com.ruoyi.system.vo.slot.AppSlotSimpleVO;
import com.ruoyi.system.vo.slot.SlotAdvertVO;
import com.ruoyi.system.vo.slot.SlotBaseInfoVO;
import com.ruoyi.system.vo.slot.SlotVO;
import com.ruoyi.system.vo.slotcharge.SlotChargeDataVO;
import com.ruoyi.system.vo.slotcharge.SlotChargeOpenLogVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.isCrmUser;
import static com.ruoyi.common.enums.slot.SlotEnableEnum.isSlotEnable;
import static com.ruoyi.common.enums.slot.SlotRedirectType.AREA_TARGET;
import static com.ruoyi.common.enums.slot.SlotRedirectType.SHUNT;
import static com.ruoyi.common.enums.slot.SlotRedirectType.isAreaTargetRedirect;
import static com.ruoyi.common.enums.slot.SlotRedirectType.isShuntRedirect;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToActivity;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToAdvert;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToUrl;

/**
 * 广告位Controller
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/manager/slot")
public class SlotController extends BaseController {

    @Autowired
    private SlotService slotService;

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private SlotConfigService slotConfigService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private SlotChargeService slotChargeService;

    @Autowired
    private SlotChargeOperLogService slotChargeOperLogService;

    @Autowired
    private SlotDataManager slotDataManager;

    @Autowired
    private DomainReplaceService domainReplaceService;

    @Autowired
    private SlotRedirectValidateService slotRedirectValidateService;

    @Autowired
    private SlotAdvertManager slotAdvertManager;

    @Autowired
    private AppService appService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private SlotTagRelationService slotTagRelationService;

    @Autowired
    private DomainCacheService domainCacheService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private AppMonthDataService appMonthDataService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private AppTagRelationService appTagRelationService;

    /**
     * 活动下拉列表
     */
    @GetMapping("/activityList")
    public Result<List<ActivitySelectVO>> activityList() {
        List<Activity> list = activityService.selectTotalOpenActivity();
        return ResultBuilder.success(BeanUtil.copyToList(list, ActivitySelectVO.class));
    }

    /**
     * 查询媒体广告位列表
     * 注:用于广告定向配置定向媒体时查询
     */
    @GetMapping("/listBy")
    public TableDataInfo<AppSimpleVO> listBy(String appSearch, String slotSearch) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非CRM用户不展示
        if (!isCrmUser(user.getMainType())) {
            return getDataTable(Collections.emptyList());
        }
        List<Long> appIds = null;
        if (StringUtils.isNotBlank(appSearch)) {
            App param = new App();
            param.setSearchValue(appSearch);
            List<App> apps = appService.selectAppList(param);
            if (CollectionUtils.isEmpty(apps)) {
                return getDataTable(Collections.emptyList());
            }
            appIds = ListUtils.mapToList(apps, App::getId);
        }

        Slot param = new Slot();
        param.setAppIds(appIds);
        param.setSearchValue(slotSearch);
        startPage();
        List<Slot> slots = slotService.selectList(param);
        Map<Long, String> appNameMap = appService.selectAppNameMap(ListUtils.mapToList(slots, Slot::getAppId));
        return getDataTable(PageInfoUtils.dto2Vo(slots, slot -> {
            AppSlotSimpleVO vo = new AppSlotSimpleVO();
            vo.setSlotId(slot.getId());
            vo.setSlotName(slot.getSlotName());
            vo.setAppId(slot.getAppId());
            vo.setAppName(appNameMap.get(slot.getAppId()));
            return vo;
        }));
    }

    /**
     * 查询广告位列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SlotReq req) {
        Slot param = BeanUtil.copyProperties(req, Slot.class);
        param.setSearchValue(req.getSearchKey());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        // 投放类型查询
        if (null != req.getIsRedirectShunting()) {
            List<Long> slotIds = queryRedirectShuntingSlotIds();
            if (Objects.equals(req.getIsRedirectShunting(), 1)) {
                if (CollectionUtils.isEmpty(slotIds)) {
                    return getDataTable(Collections.emptyList());
                } else {
                    param.setSlotIds(slotIds);
                }
            } else {
                param.setNotSlotIds(slotIds);
            }
        }

        List<Slot> slots = slotService.selectSlotList(param);
        if (CollectionUtils.isEmpty(slots)) {
            return getDataTable(Collections.emptyList());
        }

        List<Long> slotIds = slots.stream().map(Slot::getId).collect(Collectors.toList());
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
        boolean isCrmUser = isCrmUser(SecurityUtils.getLoginUser().getMainType());

        // 媒体账号-广告位数据汇总
        Map<Long, SlotData> slotDataMap = slotDataService.groupBySlotId(slotIds, yesterday);
        // 广告位配置映射
        Map<Long, SlotConfig> slotConfigMap = slotConfigService.selectSlotConfigMap(slotIds);
        //查询广告位昨日分成计费方式
        List<SlotChargeEntity> slotChargeEntities = slotChargeService.selectListBySlotIdsAndYesterday(slotIds);
        Map<Long, SlotChargeEntity> chargeEntityMap = slotChargeEntities.stream().collect(Collectors.toMap(SlotChargeEntity::getSlotId, Function.identity(), (v1, v2) -> v1));
        // 账号ID-负责人映射
        Map<Long, String> managerNameMap = accountService.selectManagerMap(ListUtils.mapToList(slots, Slot::getAccountId));
        // 标签数量
        Map<Long, Integer> tagCountMap = slotTagRelationService.selectTagCountMapBySlotIds(slotIds);
        Set<Long> wxSlotIds = new HashSet<>(slotTagRelationService.selectSlotIdsByTagName("微信自动替换"));
        Set<Long> biddingAppIds = new HashSet<>(appTagRelationService.selectAppIdsByParentTagName("信息流"));
        // 微信可用的广告位域名池
        Set<String> domainPool = domainCacheService.selectWechatValidDomainListCache(DomainType.SLOT_DOMAIN.getType());

        return getDataTable(PageInfoUtils.dto2Vo(slots, slot -> {
            SlotVO slotVO = BeanUtil.copyProperties(slot, SlotVO.class);

            // 获取广告位配置
            SlotConfig slotConfig = slotConfigMap.getOrDefault(slot.getId(), new SlotConfig());
            slotVO.setEnable(isSlotEnable(StringUtils.isNotEmpty(slotConfig.getRedirectValue())));

            // 是否分流投放
            slotVO.setIsRedirectShunting(isRedirectShunting(slotConfig));

            // 广告位投放链接
            slotVO.setServingUrl(slot.getSlotUrl().replace("/open", "/serving"));

            // 广告位域名替换
            Optional.ofNullable(JSON.parseObject(slotConfig.getDomainConfig(), SlotDomainConfigBo.class)).ifPresent(domainConfig -> {
                slotVO.setSlotUrl(domainReplaceService.doReplaceDomain(slot.getSlotUrl(), domainConfig.getSlotDomain()));
            });

            // 如果存在「微信自动替换」标签，则域名替换为微信可用域名
            if (wxSlotIds.contains(slot.getId()) && CollectionUtils.isNotEmpty(domainPool)) {
                String originDomain = UrlUtils.extractDomain(slotVO.getSlotUrl());
                if (!domainPool.contains(originDomain)) {
                    slotVO.setSlotUrl(domainReplaceService.doReplaceDomain(slotVO.getSlotUrl(), domainPool.iterator().next()));
                }
            }

            // CRM用户展示更多数据
            if (isCrmUser) {
                slotVO.setManagerName(managerNameMap.getOrDefault(slot.getAccountId(), ""));
                SlotData slotData = slotDataMap.get(slot.getId());
                if (null != slotData) {
                    slotVO.setYdaySlotReqUv(slotData.getSlotRequestUv());
                    slotVO.setYdaySlotReqPv(slotData.getSlotRequestPv());
                    slotVO.setYdayAppRevenue(slotData.getAppRevenue());
                    slotVO.setYdayOuterConsume(slotData.getOuterConsume());
                    slotVO.setYdayNhCost(slotData.getNhCost());
                    slotVO.setYdayOuterCost(slotData.getOuterCost());
                } else {
                    slotVO.setYdaySlotReqUv(0);
                    slotVO.setYdaySlotReqPv(0);
                    slotVO.setYdayAppRevenue(0L);
                    slotVO.setYdayOuterConsume(0L);
                    slotVO.setYdayNhCost(0L);
                    slotVO.setYdayOuterCost(0L);
                }
                SlotChargeEntity chargeEntity = chargeEntityMap.get(slot.getId());
                if(Objects.nonNull(chargeEntity)){
                    slotVO.setChargeType(chargeEntity.getChargeType());
                    slotVO.setChargePrice(chargeEntity.getChargePrice());
                }else{
                    slotVO.setChargeType(null);
                    slotVO.setChargePrice(0);
                }
                slotVO.setAdjustSwitch(SwitchStatusEnum.OFF.getStatus());
                Optional.ofNullable(JSON.parseObject(slotConfig.getSwitchConfig(), SlotSwitchConfig.class)).ifPresent(switchConfig -> {
                    if (SwitchStatusEnum.isSwitchOn(switchConfig.getAdjust())) {
                        slotVO.setAdjustSwitch(SwitchStatusEnum.ON.getStatus());
                    }
                    if (SwitchStatusEnum.isSwitchOn(switchConfig.getAutoCharge())) {
                        slotVO.setAutoChargeSwitch(SwitchStatusEnum.ON.getStatus());
                    }
                });
                if (whitelistService.contains(WhitelistType.ADJUST_DATA_SLOT, slot.getId())) {
                    slotVO.setAdjustSwitch(SwitchStatusEnum.ON.getStatus());
                }

                slotVO.setIqiyiAudit(0);
                slotVO.setTagCount(tagCountMap.getOrDefault(slot.getId(), 0));
                slotVO.setShowBiddingConfig(biddingAppIds.contains(slot.getAppId()) ? 1 : 0);
            }
            return slotVO;
        }));
    }

    /**
     * 查询广告位列表
     */
    @GetMapping("/listTotal")
    public AjaxResult listTotal() {
        return AjaxResult.success(slotService.selectTotalSlotList());
    }

    /**
     * 获取广告位详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        SlotVO slotVO = slotService.selectSlotById(id);
        compliantRedirectValue(slotVO);
        return AjaxResult.success(slotVO);
    }

    /**
     * 新增广告位
     */
    @Log(title = "广告位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Slot slot) {
        if (null == slot.getSlotSpecId() && (null == slot.getSlotSpec()
                || null == slot.getSlotSpec().getLength() || null == slot.getSlotSpec().getWidth())) {
            return AjaxResult.error("无效的广告位规格");
        }

        return toAjax(slotService.insertSlot(slot));
    }

    /**
     * 修改广告位
     */
    @Log(title = "广告位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SlotModifyReq req) {
        // 校验返回挽留设置
        if (null != req.getRetConfig()) {
            RetConfigReq retConfig = req.getRetConfig();
            if (PluginSwitchEnum.isOn(retConfig.getIsOpen())) {
                if (null == retConfig.getPluginType()) {
                    return AjaxResult.error(ErrorCode.E102003);
                }
                if (!Objects.equals(retConfig.getPluginType(), PluginTypeEnum.RED_PACKETS.getType())
                        && null == retConfig.getActivityId() && StringUtils.isBlank(retConfig.getUrl())) {
                    return AjaxResult.error(ErrorCode.E102003);
                }
                // 校验活动ID
                if (Objects.equals(retConfig.getPluginType(), PluginTypeEnum.ACTIVITY.getType())) {
                    if (!slotRedirectValidateService.checkActivityId(String.valueOf(retConfig.getActivityId()))) {
                        return AjaxResult.error(ErrorCode.E102001.getCode(), "返回挽留配置-活动ID不存在或活动计划未开启");
                    }
                }
            }
        }
        return toAjax(slotService.updateSlot(req));
    }

    /**
     * 更新广告位状态
     */
    @Log(title = "广告位", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody SlotReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非CRM用户限制操作
        if (!Objects.equals(user.getMainType(), AccountMainType.CRM.getType())) {
            return AjaxResult.error("无操作权限");
        }

        // 校验广告位是否允许开启（是否配置了活动）
        if (SlotStatusEnum.isSlotOpen(req.getStatus())) {
            SlotVO slot = slotService.selectSlotById(req.getId());
            if (null == slot) {
                return AjaxResult.error("无效的广告位ID");
            }
            if (null == slot.getRedirectType() || StringUtils.isBlank(slot.getRedirectValue())) {
                return AjaxResult.error("开启前请先进行投放设置");
            }
        }

        return toAjax(slotService.updateStatus(req));
    }

    /**
     * 查询广告位域名配置
     */
    @GetMapping(value = "/domainConfig")
    public AjaxResult querySlotDomainConfig(Long slotId) {
        if (null == slotId) {
            return AjaxResult.error("无效的广告位ID");
        }
        return AjaxResult.success(slotService.querySlotDomainConfig(slotId));
    }

    /**
     * 查询广告位能投放的广告列表
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @GetMapping(value = "/queryAdvertBySlot")
    public Result<List<SlotAdvertVO>> queryAdvertBySlot(Long slotId, Integer canServe) {
        if (null == slotId) {
            return ResultBuilder.fail("无效的广告位ID");
        }
        return ResultBuilder.success(slotAdvertManager.queryAdvertBySlot(slotId, canServe));
    }

    /**
     * 根据媒体ID查询广告位基础信息
     */
    @GetMapping("slotListByAppId")
    public Result<List<SlotBaseInfoVO>> slotListByAppId(@Validated @NotNull(message = "媒体id不能为空") Long appId){
        List<Slot> slots = slotService.selectByAppId(appId);
        return ResultBuilder.success(BeanUtil.copyToList(slots,SlotBaseInfoVO.class));
    }

    /**
     * 根据媒体id列表查询广告位列表信息
     */
    @GetMapping("slotListByAppIds")
    public Result<List<SlotBaseInfoVO>> slotListByAppIds(@RequestParam("appIds") List<Long> appIds){
        if(CollectionUtils.isEmpty(appIds)){
            return ResultBuilder.success(Collections.emptyList());
        }
        List<Slot> slots = slotService.selectByAppIds(appIds);
        return ResultBuilder.success(BeanUtil.copyToList(slots,SlotBaseInfoVO.class));
    }

    /**
     * 根据广告位ID列表查询广告位基础信息
     */
    @GetMapping("slotListBySlotIds")
    public Result<List<SlotBaseInfoVO>> slotListBySlotId(@RequestParam("slotIds") List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return ResultBuilder.fail("广告位ID列表不能为空");
        }
        List<Slot> slots = slotService.selectSimpleSlotByIds(slotIds);
        Map<Long,String> appNameMap = appService.selectAppNameMap(ListUtils.mapToList(slots, Slot::getAppId));
        return ResultBuilder.success(slots.stream().map(slot -> {
            SlotBaseInfoVO info = BeanUtil.copyProperties(slot, SlotBaseInfoVO.class);
            info.setAppName(appNameMap.get(slot.getAppId()));
            return info;
        }).collect(Collectors.toList()));
    }

    /**
     * 广告位结算设置
     */
    @Log(title = "广告位结算设置", businessType = BusinessType.UPDATE)
    @PostMapping("updateSlotCharge")
    public Result<Boolean> updateSlotCharge(@RequestBody @Validated SlotChargeDataUpdateReq req){
        if (!req.getCurDate().before(DateUtil.beginOfDay(new Date()))) {
            return ResultBuilder.fail("请选择有效的日期");
        }
        return ResultBuilder.success(slotChargeService.updateSlotChargeData(req));
    }
    /**
     * 广告位结算设置批量修改
     */
    @Log(title = "广告位结算设置批量修改", businessType = BusinessType.UPDATE)
    @PostMapping("batchUpdateSlotCharge")
    public Result<Boolean> batchUpdateSlotCharge(@RequestBody @Validated SlotChargeDataBatchUpdateReq req){
        Long slotId = req.getSlotId();
        req.getList().forEach(data ->{
            data.setSlotId(slotId);
            slotChargeService.updateSlotChargeData(data);
        });
        return ResultBuilder.success(true);
    }

    /**
     * 查询广告位结算操作记录
     */
    @GetMapping("selectSlotChargeOperLog")
    public TableDataInfo<SlotChargeOpenLogVO> selectSlotChargeOperLog(@Validated @NotNull(message = "广告位id不能为空") Long slotId){
        startPage();
        return getDataTable(PageInfoUtils.dto2Vo(slotChargeOperLogService.selectListBySlotId(slotId),data -> BeanUtil.copyProperties(data,SlotChargeOpenLogVO.class)));
    }

    /**
     * 根据日期查询广告位每日计费方式数据
     */
    @GetMapping("selectSlotChargeDataByDate")
    public Result<SlotChargeDataVO> selectSlotChargeDataByDate(@Validated @NotNull(message = "广告位id不能为空") Long slotId,@Validated @NotNull(message = "日期不能为空") Date date){
        return ResultBuilder.success(slotDataManager.selectSlotChargeDataByDate(slotId,date));
    }
    /**
     * 根据月份查询广告位每日计费方式数据
     */
    @GetMapping("selectSlotChargeDataByMonth")
    public Result<List<SlotChargeDataVO>> selectSlotChargeDataByMonth(@Validated @NotNull(message = "广告位id不能为空") Long slotId,@Validated @NotNull(message = "日期不能为空") Date date){
        return ResultBuilder.success(slotDataManager.selectSlotChargeDataByMonth(slotId,date));
    }

    /**
     * 设置数据校准开关
     * 下次部署可以删除
     */
    @Deprecated
    @PreAuthorize("@ss.hasCrmPermi()")
    @Log(title = "广告位数据校准设置", businessType = BusinessType.UPDATE)
    @PostMapping("/adjustSwitch")
    public Result<Void> setAdjustSwitch(@RequestBody SlotAdjustSwitchReq req) {
        if (null == req.getSlotId() || !SwitchStatusEnum.isValidStatus(req.getStatus())) {
            return ResultBuilder.fail("参数异常");
        }
        slotService.setAdjustSwitch(req.getSlotId(), req.getStatus());
        refreshCacheService.sendRefreshSlotCacheMsg(req.getSlotId());
        return ResultBuilder.success();
    }

    /**
     * 广告开关配置
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @Log(title = "广告开关配置", businessType = BusinessType.UPDATE)
    @PostMapping("/updateSlotSwitchConfig")
    public Result<Void> updateSlotSwitchConfig(@RequestBody SlotSwitchConfigReq req) {
        if (null == req.getSlotId()) {
            return ResultBuilder.fail("参数异常");
        }
        // 数据校准开关
        if (SwitchStatusEnum.isValidStatus(req.getAdjustSwitch())) {
            slotService.setAdjustSwitch(req.getSlotId(), req.getAdjustSwitch());
        }
        // 自动结算开关
        if (SwitchStatusEnum.isValidStatus(req.getAutoChargeSwitch())) {
            slotService.setAutoChargeSwitch(req.getSlotId(), req.getAutoChargeSwitch());
        }
        // 刷新缓存
        refreshCacheService.sendRefreshSlotCacheMsg(req.getSlotId());
        // 钉钉通知
        GlobalThreadPool.executorService.submit(() -> {
            Slot slot = slotService.selectSimpleSlotById(req.getSlotId());
            SlotConfig config = slotConfigService.selectBySlotId(req.getSlotId());
            SlotSwitchConfig switchConfig = JSON.parseObject(config.getSwitchConfig(), SlotSwitchConfig.class);
            String sb = "广告位数据校准设置\n" +
                    "\n广告位ID: " + req.getSlotId() +
                    "\n广告位名称: " + slot.getSlotName() +
                    "\n数据校准开关: " + SwitchStatusEnum.getDescByStatus(switchConfig.getAdjust()) +
                    "\n自动结算开关: " + SwitchStatusEnum.getDescByStatus(switchConfig.getAutoCharge());
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb);
        });
        return ResultBuilder.success();
    }

    /**
     * 获取直投广告排序(广告)
     */
    @GetMapping(value = "/directAdvertOrder")
    public Result<List<AdvertProportionVO>> directAdvertOrder(Long slotId, String advertStr, String orientStr) {
        if (null == slotId) {
            return ResultBuilder.fail("广告位ID不能为空");
        }
        if (StringUtils.isBlank(advertStr) && StringUtils.isBlank(orientStr)) {
            return ResultBuilder.success();
        }
        List<Long> orientIds = StrSplitter.split(orientStr, ',', -1, true, Long::valueOf);
        if (StringUtils.isNotBlank(advertStr)) {
            List<Long> advertIds = StrSplitter.split(advertStr, ',', -1, true, Long::valueOf);
            orientIds = ListUtils.mapToList(advertOrientationService.selectDefaultByAdvertIds(advertIds), AdvertOrientation::getId);
        }
        if (CollectionUtils.isEmpty(orientIds)) {
            return ResultBuilder.success();
        }
        return ResultBuilder.success(advertService.getDirectAdvertOrder(slotId, orientIds));
    }

    /**
     * 获取直投广告排序(配置)
     */
    @GetMapping(value = "/directAdvertOrientOrder")
    public Result<List<AdvertProportionVO>> directAdvertOrientOrder(Long slotId, String orientStr) {
        if (null == slotId) {
            return ResultBuilder.fail("广告位ID不能为空");
        }
        List<Long> orientIds = StrSplitter.split(orientStr, ',', -1, true, Long::valueOf);
        if (CollectionUtils.isEmpty(orientIds)) {
            return ResultBuilder.fail("广告配置不能为空");
        }
        return ResultBuilder.success(advertService.getDirectAdvertOrder(slotId, orientIds));
    }

    /**
     * 直投广告投放设置获取广告信息
     */
    @GetMapping(value = "/getOrientBySlotRedirectValue")
    public Result<List<AdvertOrientSelectPageVO>> getOrientBySlotRedirectValue(SlotRedirectValueOrientReq req) {
        List<Long> advertIds = req.getAdvertIds();
        List<Long> orientIds = req.getOrientIds();
        if (CollectionUtils.isEmpty(advertIds) && CollectionUtils.isEmpty(orientIds)) {
            return ResultBuilder.success();
        }
        if (CollectionUtils.isEmpty(orientIds)) {
            orientIds = ListUtils.mapToList(advertOrientationService.selectDefaultByAdvertIds(advertIds), AdvertOrientation::getId);
        }
        if (CollectionUtils.isEmpty(orientIds)) {
            return ResultBuilder.success();
        }
        List<AdvertOrientation> orients = advertOrientationService.selectListByIds(orientIds);
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(ListUtils.mapToList(orients, AdvertOrientation::getAdvertId));
        return ResultBuilder.success(orients.stream().map(orient -> {
            AdvertOrientSelectPageVO vo = new AdvertOrientSelectPageVO();
            vo.setId(orient.getAdvertId());
            vo.setAdvertName(advertNameMap.get(orient.getAdvertId()));
            vo.setOrientId(orient.getId());
            vo.setOrientName(orient.getOrientName());
            return vo;
        }).collect(Collectors.toList()));
    }

    /**
     * 设置广告位兜底直投广告
     */
    @PreAuthorize("@ss.hasCrmPermi()")
    @Log(title = "广告位兜底直投广告", businessType = BusinessType.UPDATE)
    @PostMapping("/updateDegradedAdverts")
    public Result<Void> updateDegradedAdverts(@RequestBody SlotDegradeOrientReq req) {
        SlotConfig slotConfig = slotConfigService.selectBySlotId(req.getSlotId());
        if (null == slotConfig) {
            return ResultBuilder.fail("请先配置投放信息再设置兜底广告");
        }
        SlotConfig updateConfig = new SlotConfig();
        updateConfig.setId(slotConfig.getId());
        updateConfig.setDegradedOrientIds(JSON.toJSONString(req.getOrientIds()));
        slotConfigService.updateById(updateConfig);
        // 刷新缓存
        refreshCacheService.sendRefreshSlotCacheMsg(req.getSlotId());
        return ResultBuilder.success();
    }


    /**
     * 投放信息兼容改造
     */
    private void compliantRedirectValue(SlotVO slot) {
        if (null == slot.getRedirectType()) {
            return;
        }
        if (isAreaTargetRedirect(slot.getRedirectType())) {
            List<AreaTargetRedirectItem> list = JSON.parseArray(slot.getRedirectValue(), AreaTargetRedirectItem.class);
            boolean flag = false;
            for (AreaTargetRedirectItem item : list) {
                if (StringUtils.isBlank(item.getDesc()) && CollectionUtils.isNotEmpty(item.getTargetArea())) {
                    item.setDesc("未命名");
                    flag = true;
                }
            }
            if (flag) {
                slot.setRedirectValue(JSON.toJSONString(list));
            }
        }
        if (redirectToActivity(slot.getRedirectType()) || redirectToUrl(slot.getRedirectType()) || redirectToAdvert(slot.getRedirectType())) {
            ShuntRedirectItem item = new ShuntRedirectItem();
            item.setRatio(100);
            item.setRedirectType(slot.getRedirectType());
            item.setRedirectValue(slot.getRedirectValue());
            slot.setRedirectType(SHUNT.getType());
            slot.setRedirectValue(JSON.toJSONString(Collections.singletonList(item)));
        }
        if (isShuntRedirect(slot.getRedirectType())) {
            AreaTargetRedirectItem item = new AreaTargetRedirectItem();
            item.setDesc("默认");
            item.setTargetArea(Collections.emptySet());
            item.setRedirectType(SHUNT.getType());
            item.setRedirectValue(JSON.parseArray(slot.getRedirectValue(), ShuntRedirectItem.class));
            slot.setRedirectType(AREA_TARGET.getType());
            slot.setRedirectValue(JSON.toJSONString(Collections.singletonList(item)));
        }
    }

    /**
     * 是否分流投放
     *
     * @param slotConfig 广告位配置
     * @return 1.是,0.否
     */
    private int isRedirectShunting(SlotConfig slotConfig) {
        if (isShuntRedirect(slotConfig.getRedirectType())) {
            List<ShuntRedirectItem> shuntRedirectItems = JSON.parseArray(slotConfig.getRedirectValue(), ShuntRedirectItem.class);
            if (null != shuntRedirectItems && shuntRedirectItems.size() > 1) {
                return 1;
            }
        }
        if (isAreaTargetRedirect(slotConfig.getRedirectType())) {
            List<AreaTargetRedirectItem> list = JSON.parseArray(slotConfig.getRedirectValue(), AreaTargetRedirectItem.class);
            if (list.size() > 1 || list.get(0).getRedirectValue().size() > 1) {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 查询投放分流的广告位ID列表
     *
     * @return 广告位ID列表
     */
    private List<Long> queryRedirectShuntingSlotIds() {
        List<SlotConfig> configs = slotConfigService.selectSlotConfigList(new SlotConfig());
        return configs.stream().filter(config -> {
            if (isShuntRedirect(config.getRedirectType())) {
                List<ShuntRedirectItem> shuntRedirectItems = JSON.parseArray(config.getRedirectValue(), ShuntRedirectItem.class);
                return shuntRedirectItems.size() > 1;
            }
            if (isAreaTargetRedirect(config.getRedirectType())) {
                List<AreaTargetRedirectItem> list = JSON.parseArray(config.getRedirectValue(), AreaTargetRedirectItem.class);
                return list.size() > 1 || list.get(0).getRedirectValue().size() > 1;
            }
            return false;
        }).map(SlotConfig::getSlotId).collect(Collectors.toList());
    }

    /**
     * 解析excel
     *
     * @param req
     * @return
     */
    @PostMapping("analysisExcel")
    public Result<SlotChargeDataAnalysisExcelResultVO> analysisExcel(@Validated SlotDataAnalysisExcelReq req) {
        Long slotId = req.getSlotId();
        MultipartFile file = req.getFile();
        if (Objects.isNull(file)) {
            throw new CustomException(ErrorCode.ARGS);
        }
        List<SlotChargeDataAnalysisExcelVO> list = new ArrayList<>();
        SlotChargeDataAnalysisExcelResultVO result = new SlotChargeDataAnalysisExcelResultVO();
        //查询媒体id
        Slot slot = slotService.selectSimpleSlotById(slotId);
        if(Objects.isNull(slot)){
            throw new CustomException(ErrorCode.E107002);
        }
        try {
            Date today = new Date();
            List<String> repetitionList = new ArrayList<>();
            cn.hutool.poi.excel.ExcelUtil.readBySax(file.getInputStream(), 0, (sheetIndex, rowIndex, rows) -> {
                if (Objects.isNull(rows.get(0)) || Objects.equals(rowIndex, 0L)) {
                    return;
                }
                log.info("解析参数,rowIndex:{},rows:{}", rowIndex, rows);
                SlotChargeDataAnalysisExcelVO vo = checkOrAnalysis(slot.getAppId(),today, rows, repetitionList);
                vo.setRow(rowIndex+1);
                list.add(vo);
            });


            List<SlotChargeDataAnalysisExcelVO> errList = list.stream().filter(vo -> StringUtils.isNotBlank(vo.getErrorMessage())).sorted(Comparator.comparing(SlotChargeDataAnalysisExcelVO::getRow)).collect(Collectors.toList());
            //错误排前面
            result.setErrCount(errList.size());
            result.setExcelVOS(list);
            //总数
            result.setDataCount(list.size());

        } catch (Exception e) {
            log.error("解析excel异常,e:", e);
            throw new CustomException(ErrorCode.E108006);
        }

        return ResultBuilder.success(result);
    }


    /**
     * 校验解析数据格式
     *
     * @param today
     * @param rows
     * @return
     */
    private SlotChargeDataAnalysisExcelVO checkOrAnalysis(Long appId,Date today, List<Object> rows, List<String> repetitionList) {
        String curDateObj = Objects.isNull(rows.get(0)) ? "" : String.valueOf(rows.get(0));
        String slotRequestPvObj = Objects.isNull(rows.get(1)) ? "" : String.valueOf(rows.get(1));
        String slotRequestUvObj = Objects.isNull(rows.get(2)) ? "" : String.valueOf(rows.get(2));
        String chargeTypeObj = Objects.isNull(rows.get(3)) ? "" : String.valueOf(rows.get(3));
        String chargePriceObj = Objects.isNull(rows.get(4)) ? "" : String.valueOf(rows.get(4));
        String nhCostObj = Objects.isNull(rows.get(5)) ? "" : String.valueOf(rows.get(5));
        String outCostObj = Objects.isNull(rows.get(6)) ? "" : String.valueOf(rows.get(6));
        String appRevenueObj = Objects.isNull(rows.get(7)) ? "" : String.valueOf(rows.get(7));

        SlotChargeDataAnalysisExcelVO vo = new SlotChargeDataAnalysisExcelVO();
        vo.setCurDate(curDateObj);
        vo.setAppRevenue(appRevenueObj);
        vo.setNhCost(nhCostObj);
        vo.setOuterCost(outCostObj);
        vo.setSlotRequestUv(slotRequestUvObj);
        vo.setSlotRequestPv(slotRequestPvObj);
        vo.setChargePrice(chargePriceObj);
        vo.setChargeType(chargeTypeObj);
        chargePriceObj = chargePriceObj.replaceAll("%","");
        List<String> errMessage = new ArrayList<>();
        try {
            Date curDate = DateUtil.parse(curDateObj, "yyyy-MM-dd");
            if (Objects.isNull(curDate)) {
                errMessage.add("日期格式错误");
            } else if (DateUtil.compare(DateUtil.beginOfDay(today), DateUtil.beginOfDay(curDate)) <= 0) {
                errMessage.add("日期不能超过当日");
            }else{
                //判断月账单是否已确认
                Integer month = DateUtils.dateTimeMonth(curDate);
                AppMonthDataEntity monthDataEntity = appMonthDataService.selectByAppIdAndMonth(appId, month);
                if(Objects.nonNull(monthDataEntity) && !Objects.equals(monthDataEntity.getConfirmStatus(), ConfirmStatusEnum.NO_CONFIRM.getStatus())){
                    errMessage.add("该日期不支持录入");
                }
            }
        } catch (Exception e) {
            errMessage.add("日期格式错误");
        }
        if (Objects.isNull(NumberUtils.parseLong(slotRequestPvObj))) {
            errMessage.add("广告位访问pv数据异常");
        }
        if (Objects.isNull(NumberUtils.parseInteger(slotRequestUvObj))) {
            errMessage.add("广告位访问uv数据异常");
        }
        boolean isValidChargeType = Arrays.stream(SlotChargeTypeEnum.values()).anyMatch(s-> StrUtil.equalsIgnoreCase(chargeTypeObj, s.getDesc()));
        if (!isValidChargeType) {
            errMessage.add("计费方式数据异常");
        }
        if (Objects.isNull(NumberUtils.parseDouble(chargePriceObj))) {
            errMessage.add("计费价格数据异常");
        }
        if (Objects.isNull(NumberUtils.parseDouble(nhCostObj))) {
            errMessage.add("诺禾结算金额数据异常");
        }
        if (Objects.isNull(NumberUtils.parseDouble(outCostObj))) {
            errMessage.add("外部结算金额数据异常");
        }
        if (Objects.isNull(NumberUtils.parseDouble(appRevenueObj))) {
            errMessage.add("媒体应得收入数据异常");
        }
        if (repetitionList.contains(vo.getCurDate())) {
            errMessage.add("数据重复");
        } else {
            repetitionList.add(vo.getCurDate());
        }
        vo.setErrorMessage(Joiner.on(",").join(errMessage));
        return vo;
    }
}
