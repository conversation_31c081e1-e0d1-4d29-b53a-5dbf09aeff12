package com.ruoyi.web.controller.manager.wis.advertiser.data;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkListParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleCountBo;
import com.ruoyi.system.bo.landpage.article.ArticleListBo;
import com.ruoyi.system.bo.landpage.article.ArticleListParamBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.entity.landpage.article.ArticleRefreshRecordEntity;
import com.ruoyi.system.req.landpage.article.ArticleAddReq;
import com.ruoyi.system.req.landpage.article.ArticleBatchAddReq;
import com.ruoyi.system.req.wis.article.WisArticleAggrLinkAddReq;
import com.ruoyi.system.req.wis.article.WisArticleAggrLinkListReq;
import com.ruoyi.system.req.wis.article.WisArticleBatchEditReq;
import com.ruoyi.system.req.wis.article.WisArticleEditReq;
import com.ruoyi.system.req.wis.article.WisArticleListReq;
import com.ruoyi.system.req.wis.article.WisArticleRefreshReq;
import com.ruoyi.system.req.wis.article.WisArticleStopReq;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkService;
import com.ruoyi.system.service.landpage.article.ArticleApiService;
import com.ruoyi.system.service.landpage.article.ArticleEditHistoryService;
import com.ruoyi.system.service.landpage.article.ArticleRefreshRecordService;
import com.ruoyi.system.service.landpage.article.ArticleService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.landpage.article.ArticleBatchAddResultItemVO;
import com.ruoyi.system.vo.landpage.article.ArticleBatchAddResultVO;
import com.ruoyi.system.vo.wis.WisArticleAggrLinkVO;
import com.ruoyi.system.vo.wis.WisArticleEditVO;
import com.ruoyi.system.vo.wis.WisArticleStatisticDataVO;
import com.ruoyi.system.vo.wis.WisArticleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.common.WhitelistType.ARTICLE_REFRESH_ADVERTISER;

/**
 * [DSP后台]文章聚合链接管理
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/wis/advertiser/landpage/articleAggrLink")
public class WisArticleAggrLinkController extends BaseController {

    @Autowired
    public TransactionTemplate transactionTemplate;

    @Autowired
    private ArticleService articleService;

    @Autowired
    private ArticleAggrLinkService articleAggrLinkService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private ArticleEditHistoryService articleEditHistoryService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AccountService accountService;

    @Autowired
    private ArticleRefreshRecordService articleRefreshRecordService;

    @Autowired
    private ArticleApiService articleApiService;

    @Autowired
    private WhitelistService whitelistService;

    /**
     * 查询文章聚合链接列表
     */
    @GetMapping("/list")
    public TableDataInfo<WisArticleAggrLinkVO> list(WisArticleAggrLinkListReq req) {
        ArticleAggrLinkListParamBo param = buildQueryParam(req);
        // 隐藏当天无文章的链接
        if (Objects.equals(req.getTodayArticleCount(), 1)) {
            List<Long> bgZeroLinkIds = articleService.selectBgZeroTodayArticleLinkId();
            if (CollectionUtils.isEmpty(bgZeroLinkIds)) {
                return getDataTable(Collections.emptyList());
            }
            param.setLinkIds(bgZeroLinkIds);
        }
        startPage();
        List<ArticleAggrLinkEntity> list = articleAggrLinkService.selectList(param);

        List<Long> linkIds = ListUtils.mapToList(list, ArticleAggrLinkEntity::getId);
        Map<Long, ArticleCountBo> articleCountMap = articleService.countByLinkId(linkIds);

        return getDataTable(PageInfoUtils.dto2Vo(list, entity -> {
            WisArticleAggrLinkVO vo = BeanUtil.copyProperties(entity, WisArticleAggrLinkVO.class);
            ArticleCountBo articleCount = articleCountMap.get(entity.getId());
            if (null != articleCount) {
                vo.setArticleCount(articleCount.getArticleCount());
                vo.setTodayArticleCount(articleCount.getTodayArticleCount());
                vo.setOnlineArticleCount(articleCount.getOnlineArticleCount());
            }
            return vo;
        }));
    }

    /**
     * 新增文章聚合链接
     */
    @Log(title = "文章聚合链接", businessType = BusinessType.INSERT)
    @PostMapping("/addLink")
    public Result<Boolean> addLink(@RequestBody WisArticleAggrLinkAddReq req) {
        if (StringUtils.isBlank(req.getName())) {
            return ResultBuilder.fail("投放链接名称不能为空");
        }
        Long advertiserId = SecurityUtils.getLoginUser().getCrmAccountId();
        if (articleAggrLinkService.isNameExist(req.getName(), advertiserId, null)) {
            return ResultBuilder.fail("该名称已存在");
        }

        // 分布式锁限制频次
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K120.join(advertiserId), 60);
        if (lock == null) {
            return ResultBuilder.fail("链接正在创建中，请稍后再试");
        }
        try {
            LoginUser operator = SecurityUtils.getLoginUser();
            String key = articleAggrLinkService.generateKey();
            String url = sysConfigService.selectConfigCacheByKey(BizConfigEnum.DEFAULT_DOMAIN_ARTICLE.getKey()) + "/api/wz/" + key + "?userId=__N_DEVICE__";

            ArticleAggrLinkEntity link = new ArticleAggrLinkEntity();
            link.setKey(key);
            link.setName(req.getName());
            link.setUrl(url);
            link.setOperatorId(operator.getCrmAccountId());
            link.setOperatorName(operator.getUserName());
            link.setAdvertiserId(advertiserId);
            link.setCreatorId(operator.getCrmAccountId());
            link.setCreatorName(operator.getUserName());
            //钉钉通知
            advertiserLinkWarning(operator.getCrmAccountId(), "广告主新增了一条文章聚合链接");
            return ResultBuilder.success(articleAggrLinkService.insert(link));
        } catch (Exception e) {
            return ResultBuilder.fail("链接创建失败，请稍后再试");
        } finally {
            lock.unlock();
        }
    }

    /**
     * 更新文章聚合链接
     */
    @Log(title = "文章聚合链接", businessType = BusinessType.INSERT)
    @PostMapping("/updateLink")
    public Result<Boolean> updateLink(@RequestBody @Valid WisArticleAggrLinkAddReq req) {
        if (Objects.isNull(req.getId())) {
            return ResultBuilder.fail("链接id不能为空");
        }
        Long advertiserId = SecurityUtils.getLoginUser().getCrmAccountId();
        if (articleAggrLinkService.isNameExist(req.getName(), advertiserId, req.getId())) {
            return ResultBuilder.fail("该名称已存在");
        }
        ArticleAggrLinkEntity articleAggrLinkEntity = articleAggrLinkService.selectById(req.getId());
        if (Objects.isNull(articleAggrLinkEntity) || !Objects.equals(articleAggrLinkEntity.getAdvertiserId(), advertiserId)) {
            return ResultBuilder.fail("链接不存在");
        }
        LoginUser operator = SecurityUtils.getLoginUser();
        ArticleAggrLinkEntity entity = BeanUtil.copyProperties(req, ArticleAggrLinkEntity.class);
        entity.setOperatorId(operator.getCrmAccountId());
        entity.setOperatorName(operator.getUserName());
        return ResultBuilder.success(articleAggrLinkService.updateById(entity));
    }


    /**
     * 查询文章列表
     */
    @GetMapping("/articleList")
    public TableDataInfo<WisArticleVO> articleList(@Valid WisArticleListReq req) {
        ArticleAggrLinkEntity aggrLinkEntity = articleAggrLinkService.selectById(req.getLinkId());
        if (Objects.isNull(aggrLinkEntity) || !Objects.equals(aggrLinkEntity.getAdvertiserId(), SecurityUtils.getLoginUser().getCrmAccountId())) {
            return getDataTable(Collections.emptyList());
        }
        startPage();
        List<ArticleListBo> list = articleService.selectListWithData(buildArticleListQueryParam(req));
        boolean isPvDegrade = degradeJudge();

        return getDataTable(PageInfoUtils.dto2Vo(list, entity -> {
            WisArticleVO vo = BeanUtil.copyProperties(entity, WisArticleVO.class);
            vo.setIsStop(entity.getWeight() > 0 ? 0 : 1);
            // 如果高并发导致更新延迟，暂时用redis的pv数据展示
            if (isPvDegrade && Objects.equals(entity.getOnline(), 1)) {
                Integer requestPv = redisCache.getCacheMapValue(EngineRedisKeyFactory.K111.join(DateUtil.today(), entity.getLinkId()), String.valueOf(entity.getId()));
                if (null != requestPv && requestPv > 0) {
                    vo.setRequestPv(requestPv);
                    vo.setRequestUv(requestPv);
                }
            }
            return vo;
        }));
    }

    /**
     * 查询文章汇总数据
     */
    @GetMapping("/articleStatistics")
    public Result<WisArticleStatisticDataVO> articleStatistics(@Valid WisArticleListReq req) {
        ArticleAggrLinkEntity aggrLinkEntity = articleAggrLinkService.selectById(req.getLinkId());
        if (Objects.isNull(aggrLinkEntity) || !Objects.equals(aggrLinkEntity.getAdvertiserId(), SecurityUtils.getLoginUser().getCrmAccountId())) {
            return ResultBuilder.success();
        }
        ArticleListParamBo param = buildArticleListQueryParam(req);
        ArticleListBo data = articleService.selectStatisticData(param);
        WisArticleStatisticDataVO result = BeanUtil.copyProperties(data, WisArticleStatisticDataVO.class);
        if (null != result) {
            result.setArticleRefreshTimes(articleRefreshRecordService.countBy(param));
        }
        return ResultBuilder.success(result);
    }

    /**
     * 新增文章
     */
    @Log(title = "文章", businessType = BusinessType.INSERT)
    @PostMapping("/addArticle")
    public Result<Void> addArticle(@RequestBody ArticleAddReq req) {
        if (StringUtils.isBlank(req.getName())) {
            return ResultBuilder.fail("名称不能为空");
        }
        if (StringUtils.isBlank(req.getUrl())) {
            return ResultBuilder.fail("链接不能为空");
        }
        if (!StrUtil.startWith(req.getUrl(), "http")) {
            return ResultBuilder.fail("无效的链接格式");
        }
        if (null != req.getInitRequestPv() && req.getInitRequestPv() < 0) {
            return ResultBuilder.fail("初始阅读量不能为负数");
        }

        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(req.getLinkId());
        if (null == link || !Objects.equals(link.getAdvertiserId(), SecurityUtils.getLoginUser().getCrmAccountId())) {
            return ResultBuilder.fail("无效的链接ID");
        }
        // 2024.2.6上线需求，放开广告主的链接的限制
        if (link.getGmtCreate().before(DateUtil.parseDate("2024-02-06"))) {
            return ResultBuilder.fail("历史创建的链接不可新增");
        }
        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(req.getLinkId());
        for (ArticleEntity article : existArticleList) {
            if (Objects.equals(article.getName(), req.getName())) {
                return ResultBuilder.fail("该名称已存在");
            }
            if (Objects.equals(article.getUrl(), req.getUrl())) {
                return ResultBuilder.fail("该文章链接已存在");
            }
        }
        LoginUser operator = SecurityUtils.getLoginUser();

        ArticleEntity article = new ArticleEntity();
        article.setLinkId(req.getLinkId());
        article.setName(req.getName());
        article.setUrl(req.getUrl());
        article.setTargetRequestPv(NumberUtils.defaultInt(req.getTargetRequestPv()));
        article.setWeight(1); //默认1
        article.setInitRequestPv(req.getInitRequestPv());
        article.setOperatorId(operator.getCrmAccountId());
        article.setOperatorName(operator.getUserName());
        articleService.insert(article);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), req.getLinkId()));
        //钉钉通知
        advertiserLinkWarning(operator.getCrmAccountId(), "广告主新增了文章到聚合链接");
        articleService.updateArticleProfileAsync(article.getId(), article.getUrl());
        return ResultBuilder.success();
    }

    /**
     * 编辑文章
     */
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PostMapping("/editArticle")
    public Result<Void> editArticle(@RequestBody @Valid WisArticleEditReq req) {
        if (Objects.isNull(req.getId())) {
            return ResultBuilder.fail("文章ID不能为空");
        }
        if (StringUtils.isBlank(req.getName())) {
            return ResultBuilder.fail("名称不能为空");
        }
        if (null != req.getInitRequestPv() && req.getInitRequestPv() < 0) {
            return ResultBuilder.fail("初始阅读量不能为负数");
        }
        ArticleEntity articleEntity = articleService.selectById(req.getId());
        if (Objects.isNull(articleEntity)) {
            return ResultBuilder.fail("文章不存在");
        }
        ArticleAggrLinkEntity articleAggrLinkEntity = articleAggrLinkService.selectById(articleEntity.getLinkId());
        if (Objects.isNull(articleAggrLinkEntity) || !Objects.equals(articleAggrLinkEntity.getAdvertiserId(), SecurityUtils.getLoginUser().getCrmAccountId())) {
            return ResultBuilder.fail("文章聚合链接不存在");
        }
        // 2024.2.6上线需求，放开广告主的链接的限制
        if (articleAggrLinkEntity.getGmtCreate().before(DateUtil.parseDate("2024-02-06"))) {
            return ResultBuilder.fail("历史创建的链接不可修改");
        }
        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(articleEntity.getLinkId());
        for (ArticleEntity article : existArticleList) {
            if (Objects.equals(article.getName(), req.getName()) && !Objects.equals(article.getId(), req.getId())) {
                return ResultBuilder.fail("该名称已存在");
            }
        }
        ArticleListParamBo bo = new ArticleListParamBo();
        bo.setIds(Lists.newArrayList(articleEntity.getId()));
        bo.setLinkId(articleEntity.getLinkId());
        List<ArticleListBo> articleListBos = articleService.selectListWithData(bo);
        if (CollectionUtils.isEmpty(articleListBos)) {
            return ResultBuilder.fail("文章不存在");
        }
        for (ArticleListBo articleListBo : articleListBos) {
            if (Objects.equals(articleListBo.getId(), req.getId()) && articleListBo.getRequestPv() >= req.getTargetRequestPv()) {
                return ResultBuilder.fail("该文章阅读数超出目标数，无法保存");
            }
        }
        LoginUser operator = SecurityUtils.getLoginUser();

        ArticleEntity article = new ArticleEntity();
        article.setId(req.getId());
        article.setName(req.getName());
        article.setTargetRequestPv(NumberUtils.defaultInt(req.getTargetRequestPv()));
        article.setInitRequestPv(req.getInitRequestPv());
        article.setOperatorId(operator.getCrmAccountId());
        article.setOperatorName(operator.getUserName());
        articleService.updateById(article);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), articleEntity.getLinkId()));
        //钉钉通知
        advertiserLinkWarning(operator.getCrmAccountId(), "广告主修改了文章聚合链接的目标阅读量");
        return ResultBuilder.success();
    }

    /**
     * 批量导入文章
     */
    @Log(title = "文章", businessType = BusinessType.INSERT)
    @PostMapping("/batchAddArticle")
    public Result<ArticleBatchAddResultVO> batchAddArticle(@RequestBody ArticleBatchAddReq req) {
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(req.getLinkId());
        if (null == link || !Objects.equals(link.getAdvertiserId(), SecurityUtils.getLoginUser().getCrmAccountId())) {
            return ResultBuilder.fail("无效的链接ID");
        }
        // 2024.2.6上线需求，放开广告主的链接的限制
        if (link.getGmtCreate().before(DateUtil.parseDate("2024-02-06"))) {
            return ResultBuilder.fail("历史创建的链接不可批量导入文章");
        }
        if (CollectionUtils.isEmpty(req.getArticles())) {
            return ResultBuilder.fail("文章列表不能为空");
        }

        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(req.getLinkId());
        Set<String> existArticleNameSet = existArticleList.stream().map(ArticleEntity::getName).collect(Collectors.toSet());
        Set<String> existArticleUrlSet = existArticleList.stream().map(ArticleEntity::getUrl).collect(Collectors.toSet());
        Set<String> addArticleNameSet = new HashSet<>();
        Set<String> addArticleUrlSet = new HashSet<>();

        LoginUser operator = SecurityUtils.getLoginUser();
        List<ArticleBatchAddResultItemVO> errList = new ArrayList<>();
        List<ArticleEntity> insertList = new ArrayList<>();
        for (ArticleAddReq article : req.getArticles()) {
            String errMsg = null;
            if (StringUtils.isBlank(article.getName())) {
                errMsg = "名称不能为空";
            } else if (StringUtils.isBlank(article.getUrl())) {
                errMsg = "链接不能为空";
            } else if (!StrUtil.startWith(article.getUrl(), "http")) {
                errMsg = "无效的链接格式";
            } else if (existArticleNameSet.contains(article.getName())) {
                errMsg = "名称已存在";
            } else if (existArticleUrlSet.contains(article.getUrl())) {
                errMsg = "文章链接已存在";
            } else if (addArticleNameSet.contains(article.getName())) {
                errMsg = "名称已存在";
            } else if (addArticleUrlSet.contains(article.getUrl())) {
                errMsg = "文章链接已存在";
            }
            if (StringUtils.isBlank(errMsg)) {
                ArticleEntity entity = BeanUtil.copyProperties(article, ArticleEntity.class);
                entity.setWeight(1);
                entity.setTargetRequestPv(NumberUtils.defaultInt(entity.getTargetRequestPv()));
                entity.setOperatorId(operator.getCrmAccountId());
                entity.setOperatorName(operator.getUserName());
                insertList.add(entity);
                addArticleNameSet.add(entity.getName());
                addArticleUrlSet.add(entity.getUrl());
            } else {
                ArticleBatchAddResultItemVO errItem = BeanUtil.copyProperties(article, ArticleBatchAddResultItemVO.class);
                errItem.setErrMsg(errMsg);
                errList.add(errItem);
            }
        }
        articleService.batchInsert(insertList);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), req.getLinkId()));
        //钉钉通知
        advertiserLinkWarning(operator.getCrmAccountId(), "广告主导入了一批文章到聚合链接");
        return ResultBuilder.success(new ArticleBatchAddResultVO(insertList.size(), errList.size(), errList));
    }

    /**
     * 批量修改
     */
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PostMapping("/batchEditArticle")
    public Result<List<WisArticleEditVO>> batchEditArticle(@RequestBody WisArticleBatchEditReq req) {
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(req.getLinkId());
        if (null == link || !Objects.equals(link.getAdvertiserId(), SecurityUtils.getLoginUser().getCrmAccountId())) {
            return ResultBuilder.fail("无效的链接ID");
        }
        if (CollectionUtils.isEmpty(req.getArticles())) {
            return ResultBuilder.fail("文章列表不能为空");
        }
        List<String> articleNameList = req.getArticles().stream().map(WisArticleEditReq::getName).filter(Objects::nonNull).collect(Collectors.toList());
        if (articleNameList.size() != new HashSet<>(articleNameList).size()) {
            return ResultBuilder.fail("文章名称不能重复");
        }

        Date now = new Date();
        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(req.getLinkId());
        Map<String, Long> articleNameMap = existArticleList.stream().collect(Collectors.toMap(ArticleEntity::getName, ArticleEntity::getId, (v1, v2) -> v2));
        LoginUser operator = SecurityUtils.getLoginUser();
        List<Long> articleIds = req.getArticles().stream().filter(article -> Objects.nonNull(article.getId())).map(WisArticleEditReq::getId).collect(Collectors.toList());
        if (articleIds.size() != req.getArticles().size()) {
            return ResultBuilder.fail("文章ID不能为空");
        }
        //校验文章是否到达目标阅读量
        ArticleListParamBo bo = new ArticleListParamBo();
        bo.setIds(articleIds);
        bo.setLinkId(req.getLinkId());
        List<ArticleListBo> articleListBos = articleService.selectListWithData(bo);
        Map<Long, Integer> articleIdAndRequestPvMap = articleListBos.stream().collect(Collectors.toMap(ArticleListBo::getId, ArticleListBo::getRequestPv, (v1, v2) -> v2));

        if (CollectionUtils.isEmpty(articleListBos)) {
            return ResultBuilder.fail("文章不存在");
        }

        List<WisArticleEditVO> vos = new ArrayList<>();
        for (WisArticleEditReq article : req.getArticles()) {
            Integer requestPv = NumberUtils.defaultInt(articleIdAndRequestPvMap.get(article.getId()), 0);
            if (null != article.getTargetRequestPv() && article.getTargetRequestPv() <= requestPv) {
                WisArticleEditVO vo = new WisArticleEditVO();
                vo.setId(article.getId());
                vo.setErrMessage("该文章阅读数超出目标数，无法保存");
                vos.add(vo);
            } else if (null != article.getInitRequestPv() && article.getInitRequestPv() < 0) {
                WisArticleEditVO vo = new WisArticleEditVO();
                vo.setId(article.getId());
                vo.setErrMessage("该文章初始阅读量为负数，无法保存");
                vos.add(vo);
            }
        }
        if(CollectionUtils.isNotEmpty(vos)){
            return ResultBuilder.success(vos);
        }

        for (WisArticleEditReq article : req.getArticles()) {
            if (StringUtils.isNotBlank(article.getName()) && articleNameMap.containsKey(article.getName())
                    && !Objects.equals(articleNameMap.get(article.getName()), article.getId())) {
                return ResultBuilder.fail("文章「" + article.getName() + "」已存在");
            }
            ArticleEntity updateRecord = new ArticleEntity();
            updateRecord.setId(article.getId());
            updateRecord.setName(article.getName());
            updateRecord.setInitRequestPv(article.getInitRequestPv());
            updateRecord.setTargetRequestPv(article.getTargetRequestPv());
            updateRecord.setOperatorId(operator.getCrmAccountId());
            updateRecord.setOperatorName(operator.getUserName());
            updateRecord.setOperatorTime(now);
            articleEditHistoryService.add(updateRecord);
            articleService.updateById(updateRecord);
        }
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), req.getLinkId()));
        //钉钉通知
        advertiserLinkWarning(operator.getCrmAccountId(), "广告主修改了一批文章聚合链接的目标阅读量");
        return ResultBuilder.success();
    }

    /**
     * 手动更新文章阅读量
     */
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PostMapping("/manualArticleRefresh")
    public Result<Boolean> manualArticleRefresh(@RequestBody WisArticleRefreshReq req) {
        ArticleEntity article = articleService.selectById(req.getArticleId());
        if (null == req.getArticleId()) {
            return ResultBuilder.fail("未查询到该文章");
        }
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(article.getLinkId());
        if (!Objects.equals(link.getAdvertiserId(), SecurityUtils.getLoginUser().getCrmAccountId())) {
            return ResultBuilder.fail("只能更新自己的文章");
        }
        if (!whitelistService.contains(ARTICLE_REFRESH_ADVERTISER, link.getAdvertiserId())) {
            return ResultBuilder.fail("广告主未加白，无法操作");
        }
        GlobalThreadPool.longTimeExecutorService.execute(() -> {
            Integer requestPv = articleApiService.getArticleRealRequestPv(article.getUrl());
            if (requestPv > 0) {
                ArticleEntity updateArticle = new ArticleEntity();
                updateArticle.setId(article.getId());
                updateArticle.setDisplayActualRequestPv(requestPv);
                articleService.updateById(updateArticle);

                ArticleRefreshRecordEntity record = new ArticleRefreshRecordEntity();
                record.setCurDate(DateUtil.beginOfDay(new Date()));
                record.setAdvertiserId(link.getAdvertiserId());
                record.setLinkId(article.getLinkId());
                record.setArticleId(article.getId());
                record.setUrl(article.getUrl());
                record.setActualRequestPv(requestPv);
                record.setOperatorId(link.getAdvertiserId());
                articleRefreshRecordService.insert(record);
            }
        });
        return ResultBuilder.success(true);
    }

    /**
     * 文章撤单
     */
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PostMapping("/articleStop")
    public Result<Boolean> articleStop(@RequestBody WisArticleStopReq req) {
        ArticleEntity article = articleService.selectById(req.getArticleId());
        if (null == req.getArticleId()) {
            return ResultBuilder.fail("未查询到该文章");
        }
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(article.getLinkId());
        if (!Objects.equals(link.getAdvertiserId(), SecurityUtils.getLoginUser().getCrmAccountId())) {
            return ResultBuilder.fail("只能撤单自己的文章");
        }
        ArticleEntity updateArticle = new ArticleEntity();
        updateArticle.setId(article.getId());
        updateArticle.setWeight(0);
        articleService.updateById(updateArticle);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), article.getLinkId()));
        advertiserLinkWarning(SecurityUtils.getLoginUser().getCrmAccountId(), "广告主撤单了文章");
        // 查询当前阅读量
        GlobalThreadPool.longTimeExecutorService.execute(() -> {
            Integer requestPv = articleApiService.getArticleRealRequestPv(article.getUrl());
            articleService.updateActualRequestPv(article.getId(), requestPv);
        });
        return ResultBuilder.success(true);
    }

    /**
     * 广告主聚合链接告警
     */
    private void advertiserLinkWarning(Long advertiserId,String content) {
        Account account = accountService.selectAccountById(advertiserId);
        if(Objects.isNull(account)){
            return;
        }

        DingRobotUtil.sendText(DingWebhookConfig.getArticleOperateAlert(),
                content+"\n" +
                        "\n广告主名称: " + account.getCompanyName());
    }


    /**
     * 构造查询参数
     *
     * @param req 请求参数
     * @return 查询参数
     */
    private ArticleListParamBo buildArticleListQueryParam(WisArticleListReq req) {
        ArticleListParamBo param = new ArticleListParamBo(req.getLinkId());
//        param.setName(req.getName());
//        param.setUrl(req.getUrl());
        param.setSearchKey(req.getName());
        param.setStartDate(req.getStartDate());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        return param;
    }

    /**
     * 构造查询参数 dsp用
     *
     * @param req 请求参数
     * @return 查询参数
     */
    private ArticleAggrLinkListParamBo buildQueryParam(WisArticleAggrLinkListReq req) {
        ArticleAggrLinkListParamBo param = new ArticleAggrLinkListParamBo();
        param.setName(req.getName());
        param.setAdvertiserId(SecurityUtils.getLoginUser().getCrmAccountId());
        if (req.getStartDate() != null) {
            param.setStartDate(DateUtil.beginOfDay(req.getStartDate()));
        }
        if (req.getEndDate() != null) {
            param.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        return param;
    }

    /**
     * 根据埋点线程池判断是否降级
     */
    private boolean degradeJudge() {
        try {
            ThreadPoolExecutor te = (ThreadPoolExecutor) GlobalThreadPool.statExecutorService;
            return te.getQueue().size() >= 1000;
        } catch (Exception e) {
            logger.error("degradeJudge error", e);
        }
        return false;
    }
}
