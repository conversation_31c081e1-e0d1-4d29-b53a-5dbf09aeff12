package com.ruoyi.web.controller.manager.crm.ssp.activity;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.req.activity.ActivitySkinListReq;
import com.ruoyi.system.req.activity.ActivitySkinReq;
import com.ruoyi.system.vo.activity.ActivitySkinVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.entity.activity.ActivitySkin;
import com.ruoyi.system.service.manager.ActivitySkinService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * [CRM后台]活动皮肤
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/skin")
public class ActivitySkinController extends BaseController {

    @Autowired
    private ActivitySkinService activitySkinService;

    /**
     * 查询活动皮肤列表
     */
    @GetMapping("/list")
    public TableDataInfo<ActivitySkinVO> list(ActivitySkinListReq req) {
        startPage();
        ActivitySkin param = BeanUtil.copyProperties(req, ActivitySkin.class);
        List<ActivitySkin> list = activitySkinService.selectActivitySkinList(param);
        return getDataTable(PageInfoUtils.dto2Vo(list, skin -> BeanUtil.copyProperties(skin, ActivitySkinVO.class)));
    }

    /**
     * 活动皮肤下拉列表
     */
    @GetMapping("/listTotal")
    public AjaxResult listTotal() {
        List<ActivitySkin> list = activitySkinService.selectTotalActivitySkinList().stream()
                .filter(skin -> !StrUtil.contains(skin.getSkinName(), "已废弃"))
                .collect(Collectors.toList());
        return AjaxResult.success(list);
    }

    /**
     * 皮肤信息
     */
    @GetMapping(value = "/{id}")
    public Result<ActivitySkinVO> info(@PathVariable("id") Long id) {
        ActivitySkin skin = activitySkinService.selectActivitySkinById(id);
        return ResultBuilder.success(BeanUtil.copyProperties(skin, ActivitySkinVO.class));
    }

    /**
     * 新增皮肤
     */
    @Log(title = "活动皮肤", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody ActivitySkinReq req) {
        if (StringUtils.isBlank(req.getSkinName())) {
            return ResultBuilder.fail("皮肤名称不能为空");
        }
        if (StringUtils.isBlank(req.getSkinCode())) {
            return ResultBuilder.fail("皮肤标识不能为空");
        }
        if (StringUtils.isBlank(req.getRedirectPath())) {
            return ResultBuilder.fail("跳转路径不能为空");
        }
        if (!JSONUtil.isTypeJSON(req.getSkinConfig())) {
            return ResultBuilder.fail("皮肤配置不是标准的JSON");
        }
        if (!JSONUtil.isTypeJSON(req.getGlobalConfig())) {
            return ResultBuilder.fail("全局配置不是标准的JSON");
        }
        req.setJsTemplate(StrUtil.blankToDefault(req.getJsTemplate(), "{}"));
        int result = activitySkinService.insertActivitySkin(BeanUtil.copyProperties(req, ActivitySkin.class));
        return ResultBuilder.success(result > 0);
    }

    /**
     * 修改皮肤
     */
    @Log(title = "活动皮肤", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public Result<Boolean>  update(@RequestBody ActivitySkinReq req) {
        if (null == req.getId()) {
            return ResultBuilder.fail("皮肤ID不能为空");
        }
        if (StringUtils.isBlank(req.getSkinName())) {
            return ResultBuilder.fail("皮肤名称不能为空");
        }
        if (StringUtils.isBlank(req.getRedirectPath())) {
            return ResultBuilder.fail("跳转路径不能为空");
        }
        if (!JSONUtil.isTypeJSON(req.getSkinConfig())) {
            return ResultBuilder.fail("皮肤配置不是标准的JSON");
        }
        if (!JSONUtil.isTypeJSON(req.getGlobalConfig())) {
            return ResultBuilder.fail("全局配置不是标准的JSON");
        }

        ActivitySkin updateSkin = new ActivitySkin();
        updateSkin.setId(req.getId());
        updateSkin.setSkinName(req.getSkinName());
        updateSkin.setThumbnailImage(req.getThumbnailImage());
        updateSkin.setJsTemplate(req.getJsTemplate());
        updateSkin.setRedirectPath(req.getRedirectPath());
        updateSkin.setSkinConfig(req.getSkinConfig());
        updateSkin.setGlobalConfig(req.getGlobalConfig());
        int result = activitySkinService.updateActivitySkin(updateSkin);
        return ResultBuilder.success(result > 0);
    }
}
