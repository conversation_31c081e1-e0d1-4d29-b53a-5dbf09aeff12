package com.ruoyi.web.controller.open;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.entity.open.ZxsCallbackRecord;
import com.ruoyi.system.req.open.ZxsCallbackReq;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.open.ZxsCallbackRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 摘星社接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/04/12
 */
@Slf4j
@RestController
@RequestMapping("/open/zxs")
public class ZxsController {

    /**
     * 应用密钥
     */
    private static final String SECRET = "fX5G2rU54Pw01aFC";

    @Autowired
    private StatService statService;

    @Autowired
    private ZxsCallbackRecordService zxsCallbackRecordService;

    /**
     * 摘星社事件回调接口
     */
    @CrossOrigin
    @GetMapping("/callback")
    public JSONObject callback(ZxsCallbackReq req) {
        log.info("摘星社回调: req={}", JSON.toJSONString(req));

        JSONObject result = new JSONObject();
        result.put("flag", false);

        // 签名校验
        if (!validateSignature(req)) {
            result.put("msg", "签名校验失败");
            log.info("摘星社回调返回: orderNo={}, result={}", req.getOrderNo(), result.toString());
            return result;
        }

        // 落地页转化埋点
        try {
            String orderId = req.getOthers();
            if (StringUtils.isNotBlank(orderId) && Objects.equals(req.getStatus(), "1")) {
                statService.landpageClick(orderId);
            }
        } catch (Exception e) {
            log.error("摘星社回调, 埋点统计异常, req={}", JSON.toJSONString(req), e);
        }

        // 回调记录
        try {
            zxsCallbackRecordService.insert(BeanUtil.copyProperties(req, ZxsCallbackRecord.class));
        } catch (Exception e) {
            log.error("摘星社回调, 新增回调记录异常, req={}", JSON.toJSONString(req), e);
        }

        result.put("flag", true);
        result.put("msg", "操作成功");
        log.info("摘星社回调返回: orderNo={}, result={}", req.getOrderNo(), result.toString());
        return result;
    }

    /**
     * 签名校验
     *
     * @param req 参数
     * @return 是否校验通过
     */
    private static boolean validateSignature(ZxsCallbackReq req) {
        try {
            String param = Joiner.on("").join(
                    BeanUtil.beanToMap(req, false, true).entrySet()
                            .stream()
                            .filter(entry -> !Objects.equals(entry.getKey(), "sign"))
                            .sorted(Map.Entry.comparingByKey())
                            .map(entry -> entry.getKey() + entry.getValue())
                            .collect(Collectors.toList()));
            String signature = Md5Utils.hash(URLEncoder.encode(param,"UTF-8") + SECRET).toUpperCase();
            return Objects.equals(req.getSign(), signature);
        } catch (Exception e) {
            log.error("摘星社回调，签名校验失败, req={}", JSON.toJSONString(req), e);
        }
        return false;
    }
}
