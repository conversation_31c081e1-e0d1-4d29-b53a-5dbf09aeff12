package com.ruoyi.web.controller.manager.ssp.finance;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.account.finance.AccountRevenueEntity;
import com.ruoyi.system.entity.qualification.AccountQualificationEntity;
import com.ruoyi.system.service.finance.AccountRevenueService;
import com.ruoyi.system.service.publisher.prepay.AccountPrepayStatementRecordService;
import com.ruoyi.system.service.qualification.AccountQualificationService;
import com.ruoyi.system.vo.revenue.AccountRevenueVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

import static com.ruoyi.common.enums.publisher.PayType.POSTPAID;
import static com.ruoyi.common.enums.publisher.PayType.isPrepay;

/**
 * 账号收益管理
 *
 * <AUTHOR>
 * @date 2021/9/15 4:04 下午
 */
@RestController
@RequestMapping("/manager/ssp/revenue")
public class AccountRevenueController extends BaseController {

    @Autowired
    private AccountRevenueService accountRevenueService;

    @Autowired
    private AccountQualificationService accountQualificationService;

    @Autowired
    private AccountPrepayStatementRecordService accountPrepayStatementRecordService;

    /**
     * 查询账号收益信息
     */
    @GetMapping("info")
    public Result<AccountRevenueVO> info(){
        LoginUser user = SecurityUtils.getLoginUser();
        AccountRevenueEntity accountRevenueEntity = accountRevenueService.selectByAccountId(user.getCrmAccountId());
        AccountRevenueVO vo = new AccountRevenueVO();
        if(Objects.nonNull(accountRevenueEntity)){
            vo = BeanUtil.copyProperties(accountRevenueEntity, AccountRevenueVO.class);
            if (isPrepay(vo.getPayType())) {
                Integer prepayStatementAmount = accountPrepayStatementRecordService.sumAmountByAccountId(user.getCrmAccountId());
                vo.setWithdrawableAmount(vo.getWithdrawableAmount() - (accountRevenueEntity.getPrepayAmount() - prepayStatementAmount));
            }
        }else{
            vo.setTotalRevenue(0L);
            vo.setWithdrawableAmount(0L);
            vo.setPayType(POSTPAID.getType());
        }
        AccountQualificationEntity qualificationEntity = accountQualificationService.selectByAccountId(user.getCrmAccountId());
        vo.setHasQualification(null != qualificationEntity);
        return ResultBuilder.success(vo);
    }
}
