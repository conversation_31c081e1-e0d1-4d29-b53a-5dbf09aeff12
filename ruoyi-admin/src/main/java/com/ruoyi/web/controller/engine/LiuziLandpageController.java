package com.ruoyi.web.controller.engine;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.youku.Md5Util;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.manager.sms.CodeSmsManager;
import com.ruoyi.system.req.engine.LiuziLandPageFormReq;
import com.ruoyi.system.req.engine.LiuziLandpagePhoneFormReq;
import com.ruoyi.system.req.engine.LiuziLandpageSendCodeReq;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.landpage.LiuziLandpageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 留资落地页(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/01/24
 */
@Slf4j
@RestController
@RequestMapping("/lp/liuzi")
public class LiuziLandpageController {
    /**
     * 私域订单页
     */
    private static final List<String> PRIVATE_ORDER_PAGE =  Lists.newArrayList("I3JHC1DB","V7PYRDGY","BVDRQSXT","HRPR1ZLG","JOESRASE","S3X91GLD","T08GTJCM","YG60WJ1G","YHH3ROTL","PISKM032");

    @Autowired
    private LiuziLandpageService liuziLandpageService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private CodeSmsManager codeSmsManager;

    /**
     * 手机号姓名表单提交
     */
    @CrossOrigin
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody LiuziLandPageFormReq req) {
        if (StringUtils.isBlank(req.getName()) || StringUtils.isBlank(req.getPhone())) {
            return AjaxResult.error("参数异常");
        }
        if (StringUtils.isBlank(req.getOrderId())) {
            return AjaxResult.error("链接已失效");
        }
        // "-"特殊兼容
        if (req.getPhone().length() != 11 && !Objects.equals("-",req.getPhone())) {
            return AjaxResult.error("参数异常");
        }

        liuziLandpageService.formSubmit(req);
        return AjaxResult.success();
    }

    /**
     * 手机号验证码表单提交
     */
    @CrossOrigin
    @PostMapping("/submitPhone")
    public AjaxResult submitPhone(@RequestBody LiuziLandpagePhoneFormReq req) {
        if (StringUtils.isBlank(req.getOrderId())) {
            return AjaxResult.success("链接已失效", false);
       }
        if (StrUtil.length(req.getPhone()) != 11) {
            return AjaxResult.success("请输入有效的手机号", false);
        }
        // 获取页面配置
        Landpage landpage = landpageCacheService.selectLandpageCache(req.getKey());
        if (Objects.isNull(landpage)) {
            return AjaxResult.success("落地页不存在", false);
        }
        Integer codeSwitch = null;
        JSONObject jo = JSON.parseObject(landpage.getPageConfig());
        if (null != jo) {
            codeSwitch = jo.getInteger("codeSwitch");
        }
        // 校验验证码
        if (Objects.equals(codeSwitch, 1)) {
            if (StringUtils.isBlank(req.getCode())) {
                return AjaxResult.success("验证码不能为空", false);
            }
            String code = redisCache.getCacheObject(EngineRedisKeyFactory.K061.join(req.getPhone()));
            if (StringUtils.isBlank(code)) {
                return AjaxResult.success("验证码已失效，请重新获取", false);
            }
            if (!StrUtil.equals(code, req.getCode())) {
                return AjaxResult.success("验证码错误", false);
            }
        }
        if(interceptPrivateOrder(req)){
            return AjaxResult.success(true);
        }
//        if(!SpringEnvironmentUtils.isProd()){
            liuziLandpageService.phoneFormSubmit(req.getOrderId(), req.getPhone());
//        }
        // 前端根据data判断是否操作成功，若data为false则toast展示msg信息
        return AjaxResult.success(true);
    }

    /**
     * 拦截私域订单
     */
    private boolean interceptPrivateOrder(LiuziLandpagePhoneFormReq req) {
        if (Objects.isNull(req) || !PRIVATE_ORDER_PAGE.contains(req.getKey())) {
            return false;
        }
        try {
            if(!SpringEnvironmentUtils.isProd()){
                return true;
            }
            String mobile = req.getPhone();
            //获取前端页面地址
            String referer = ServletUtils.getRequest().getHeader("referer");
            if(!referer.contains("land/render")){
                if(referer.endsWith("/")){
                    referer = referer+"land/render/"+req.getKey();
                }else{
                    referer = referer+"/land/render/"+req.getKey();
                }
            }
            JSONObject object = new JSONObject();
            object.put("mobile", mobile);
            object.put("landUrl", referer);
            HttpPost httpPost = new HttpPost("https://marsapi.elinks.cn/api/order/create");
            httpPost.setEntity(new StringEntity(JSON.toJSONString(object), ContentType.APPLICATION_JSON));

            CloseableHttpClient httpClient = HttpClients.custom().setMaxConnTotal(100).setMaxConnPerRoute(100).setRetryHandler(
                    new DefaultHttpRequestRetryHandler(0, false)).disableAutomaticRetries().build();


            httpClient.execute(httpPost);
            log.info("私域订单保存成功,req:{}" , JSON.toJSONString(req));
        } catch (Exception e) {
            return true;
        }
        return true;
    }

    /**
     * 发送短信验证码
     */
    @CrossOrigin
    @PostMapping("/sendCode")
    public AjaxResult sendCode(@RequestBody LiuziLandpageSendCodeReq req) {
        if (StringUtils.isBlank(req.getOrderId()) || StringUtils.isBlank(req.getPhone())
                || StringUtils.isBlank(req.getSign()) || null == req.getTimestamp()) {
            return AjaxResult.success("参数异常", false);
        }
        String secret = Md5Util.MD5(req.getOrderId() + IpUtils.getIpAddr(ServletUtils.getRequest()));
        String sign = Md5Util.MD5(req.getOrderId() + req.getTimestamp() + req.getPhone() + secret);
        if (!StrUtil.equalsAnyIgnoreCase(req.getSign(), sign)) {
            return AjaxResult.success("签名校验失败，请刷新页面重试", false);
        }
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K062.join(req.getPhone()), 60);
        if (lock == null) {
            Long expire = NumberUtils.defaultLong(redisCache.getExpire(EngineRedisKeyFactory.K062.join(req.getPhone())));
            return AjaxResult.success("验证码已发送，" + expire + "秒后可重新发送", false);
        }
        String timesKey = EngineRedisKeyFactory.K063.join(req.getPhone(), DateUtil.today());
        Long times = NumberUtils.defaultLong(redisAtomicClient.getLong(timesKey));
        if (times >= 10) {
            return AjaxResult.success("该手机号发送验证码次数过多，请换个手机号", false);
        }
        String code = RandomUtil.randomNumbers(4);
        redisCache.setCacheObject(EngineRedisKeyFactory.K061.join(req.getPhone()), code, 20, TimeUnit.MINUTES);
        redisAtomicClient.incrBy(timesKey, 1, 1, TimeUnit.DAYS);
        boolean sendResult = codeSmsManager.sendByChuanglan(req.getPhone(), code);
        if (sendResult) {
            // 前端根据data判断是否操作成功，若data为false则toast展示msg信息
            return AjaxResult.success(true);
        }
        return AjaxResult.success("验证码发送失败", false);
    }
}
