package com.ruoyi.web.controller.manager.crm.dsp.setting;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.landpage.LandpageFormFullRecord;
import com.ruoyi.system.entity.redpacket.PhoneRedPacketRecordEntity;
import com.ruoyi.system.req.cashback.CashBackListReq;
import com.ruoyi.system.req.cashback.TransferReq;
import com.ruoyi.system.service.landpage.LandpageFormRecordService;
import com.ruoyi.system.service.redpacket.PhoneRedPacketRecordService;
import com.ruoyi.system.vo.cashback.CashBackListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/30 5:36 下午
 */
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/redpacket")
@RestController
public class DspRedPacketController extends BaseController {

    @Autowired
    private PhoneRedPacketRecordService phoneRedPacketRecordService;
    @Autowired
    private LandpageFormRecordService landpageFormRecordService;

    /**
     * 分页查询号卡返现列表
     * @param req
     * @return
     */
    @GetMapping("list")
    public TableDataInfo list(CashBackListReq req){

        startPage();

        List<PhoneRedPacketRecordEntity> entityList = phoneRedPacketRecordService.selectByReq(req);
        List<String> orderIds = entityList.stream().map(PhoneRedPacketRecordEntity::getOrderId).collect(Collectors.toList());

        List<LandpageFormFullRecord> records = landpageFormRecordService.selectByOrderIds(orderIds);
        Map<String, LandpageFormFullRecord> recordMap = records.stream().collect(Collectors.toMap(LandpageFormFullRecord::getOrderId, Function.identity(), (v1, v2) -> v1));

        return getDataTable(PageInfoUtils.dto2Vo(entityList,entity ->{
            CashBackListVO vo = BeanUtil.copyProperties(entity, CashBackListVO.class);
            LandpageFormFullRecord record = recordMap.get(entity.getOrderId());
            if(Objects.nonNull(record)){
                vo.setFormSubmitTime(record.getGmtCreate());
            }
            return vo;

        }));

    }

    /**
     *
     * @param req
     * @return
     */
    @PostMapping("updateTransfer")
    public Result<Boolean> updateTransfer(@RequestBody @Validated TransferReq req){
        LoginUser user = SecurityUtils.getLoginUser();
        PhoneRedPacketRecordEntity entity = BeanUtil.copyProperties(req,PhoneRedPacketRecordEntity.class);
        entity.setOperatorName(user.getUserName());
        entity.setOperatorId(user.getCrmAccountId());
        entity.setOperatorTime(new Date());
        int success = phoneRedPacketRecordService.updateById(entity);
        return ResultBuilder.success(success>0);
    }
}
