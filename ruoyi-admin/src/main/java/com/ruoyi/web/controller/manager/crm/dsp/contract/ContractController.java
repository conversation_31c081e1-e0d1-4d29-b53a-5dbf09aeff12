package com.ruoyi.web.controller.manager.crm.dsp.contract;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.contract.ContractStatusEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.contract.ContractEntity;
import com.ruoyi.system.req.contract.AddContractReq;
import com.ruoyi.system.req.contract.UpdateContractReq;
import com.ruoyi.system.service.contract.ContractService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.vo.contract.ContractListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 合同管理
 * <AUTHOR>
 * @date 2022/11/3 6:35 下午
 */
@RestController
@RequestMapping("manager/contract")
public class ContractController extends BaseController {

    @Autowired
    private ContractService contractService;
    @Autowired
    private AccountService accountService;

    /**
     * 新增合同
     * @param req
     * @return
     */
    @Log(title = "合同新增", businessType = BusinessType.INSERT)
    @PostMapping("addContract")
    public Result<Boolean> addContract(@RequestBody @Validated AddContractReq req){
        if(Objects.nonNull(contractService.selectByContractCode(req.getContractCode()))){
            throw new CustomException(ErrorCode.E151001);
        }
        ContractEntity contractEntity = BeanUtil.copyProperties(req,ContractEntity.class);
        contractEntity.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
        return ResultBuilder.success(contractService.insert(contractEntity));
    }

    /**
     * 更新合同
     * @param req
     * @return
     */
    @Log(title = "合同更新", businessType = BusinessType.UPDATE)
    @PostMapping("updateContract")
    public Result<Boolean> updateContract(@RequestBody @Validated UpdateContractReq req){
        ContractEntity contractEntity = BeanUtil.copyProperties(req, ContractEntity.class);
        contractEntity.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
        return ResultBuilder.success(contractService.updateById(contractEntity));
    }

    /**
     * 根据账号id获取合同详情
     * @param accountId
     * @return
     */
    @GetMapping("listByAccountId")
    public TableDataInfo<ContractListVO> listByAccountId(@Validated @NotNull(message = "账号id不能为空") Long accountId){
        startPage();
        List<ContractEntity> contractEntities = contractService.selectListByAccountId(accountId);
        //查询操作人名
        List<Long> operatorIds = contractEntities.stream().map(ContractEntity::getOperatorId).collect(Collectors.toList());
        Map<Long, String> contactMap = accountService.selectAccountContactMap(operatorIds);
        //查询乙方主体名称
        List<Long> accountIds = contractEntities.stream().map(ContractEntity::getAccountId).collect(Collectors.toList());
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(accountIds);

        return getDataTable(PageInfoUtils.dto2Vo(contractEntities,entity ->{
            ContractListVO contractListVO = BeanUtil.copyProperties(entity, ContractListVO.class);
            contractListVO.setCompanyName(companyNameMap.get(entity.getAccountId()));
            contractListVO.setOperatorName(contactMap.get(entity.getOperatorId()));
            contractListVO.setContractStatus(ContractStatusEnum.getContractStatusByDate(entity.getEndDate()));
            contractListVO.setDateDiff((int)DateUtil.between(new Date(), entity.getEndDate(), DateUnit.DAY));
            return contractListVO;
        }));
    }
}
