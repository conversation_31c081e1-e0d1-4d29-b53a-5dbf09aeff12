package com.ruoyi.web.controller.engine;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord;
import com.ruoyi.system.req.engine.BaijiuLandPageFormReq;
import com.ruoyi.system.req.engine.BaijiuLandpageStatReq;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.engine.BaijiuLandpageStatService;
import com.ruoyi.system.service.landpage.BaijiuLandpageFormRecordService;
import com.ruoyi.system.service.landpage.BaijiuLandpageService;
import com.ruoyi.system.vo.engine.BaijiuLandpageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * 白酒落地页(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/01/24
 */
@Slf4j
@RestController
@RequestMapping("/lp/baijiu")
public class BaijiuLandpageController {

    @Autowired
    private BaijiuLandpageService baijiuLandpageService;

    @Autowired
    private AlipayClient alipayClient;

    @Autowired
    private BaijiuLandpageFormRecordService baijiuLandpageFormRecordService;

    @Autowired
    private BaijiuLandpageStatService baijiuLandpageStatService;

    @Autowired
    private AreaService areaService;

    /**
     * 页面初始化
     */
    @GetMapping("/init")
    public Result<BaijiuLandpageVO> init(String orderId, HttpServletRequest request) {
        BaijiuLandpageVO page = new BaijiuLandpageVO();
        page.setRetSwitch(baijiuLandpageService.getRetSwitch(orderId));
        page.setArea(areaService.ipAnalysis(IpUtils.getIpAddr(request)));
        return ResultBuilder.success(page);
    }

    /**
     * 落地页转化表单提交
     */
    @CrossOrigin
    @PostMapping("/submit")
    public Result<Long> submit(@RequestBody BaijiuLandPageFormReq req) {
        if (StringUtils.isBlank(req.getName()) || StringUtils.isBlank(req.getPhone())
                || StringUtils.isBlank(req.getAreaNum()) || StringUtils.isBlank(req.getAddress())) {
            return ResultBuilder.fail("参数异常");
        }
        if (StringUtils.isBlank(req.getOrderId())) {
            return ResultBuilder.fail("链接已失效");
        }
        if (req.getPhone().length() != 11) {
            return ResultBuilder.fail("参数异常");
        }

        return ResultBuilder.success(baijiuLandpageService.formSubmit(req));
    }

    /**
     * 埋点
     */
    @CrossOrigin
    @ResponseBody
    @GetMapping("/stat")
    public AjaxResult stat(BaijiuLandpageStatReq req) {
        InnerLogType type = InnerLogType.getByType(req.getType());
        if (null == type) {
            return AjaxResult.success("参数错误");
        }

        switch (type) {
            case LANDPAGE_LAYER_EXPOSURE:
                baijiuLandpageStatService.layerExposure(req);
                break;
            case LANDPAGE_RET_EXPOSURE:
                baijiuLandpageStatService.retExposure(req);
                break;
            default:
                return AjaxResult.success("无效的类型");
        }
        return AjaxResult.success();
    }

    /**
     * 设置落地页返回挽留开关
     */
    @PostMapping("/retSwitch")
    public AjaxResult setRetSwitch(Long slotId, Integer status) {
        if (null == slotId || null == status) {
            return AjaxResult.error("参数异常");
        }
        baijiuLandpageService.setRetSwitch(slotId, status);
        return AjaxResult.success();
    }

    /**
     * 查询白酒支付结果 0 代表没有结果 1代表成功 2代表失败
     *
     * @return
     */
    @GetMapping("payResult")
    public Result<Integer> payResult(@Validated @NotNull(message = "id不能为空") Long id){
        BaijiuLandpageFormRecord formRecord = baijiuLandpageFormRecordService.selectById(id);
        if(Objects.isNull(formRecord)){
            throw new CustomException("无效订单");
        }
        if(StringUtils.isBlank(formRecord.getTradeStatus())){
            //没有结果，则主动去调一次
            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            JSONObject bizContent = new JSONObject();
            bizContent.put("trade_no", formRecord.getTradeNo());
            request.setBizContent(bizContent.toString());
            try {
                AlipayTradeQueryResponse response = alipayClient.execute(request);
                if(response.isSuccess() && Objects.equals(response.getTradeStatus(),"TRADE_SUCCESS")){
                    return ResultBuilder.success(1);
                }
            } catch (AlipayApiException e) {
                log.error("支付宝支付结果查询异常,e:",e);
            }

            return ResultBuilder.success(0);
        }
        if(Objects.equals("TRADE_SUCCESS",formRecord.getTradeStatus())){
            return ResultBuilder.success(1);
        }
        return ResultBuilder.success(2);
    }
}
