package com.ruoyi.web.controller.manager.crm.ssp.slot;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.slot.SlotRedirectType;
import com.ruoyi.common.enums.slot.SlotShuntType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.SlotShuntData;
import com.ruoyi.system.entity.slot.SlotShuntTask;
import com.ruoyi.system.req.slot.shunt.SlotShuntTaskListReq;
import com.ruoyi.system.req.slot.shunt.SlotShuntTaskParam;
import com.ruoyi.system.req.slot.shunt.SlotShuntTaskReq;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.slot.SlotShuntDataService;
import com.ruoyi.system.service.slot.SlotShuntTaskService;
import com.ruoyi.system.service.validate.SlotRedirectValidateService;
import com.ruoyi.system.vo.slot.shunt.SlotShuntTaskExcelVO;
import com.ruoyi.system.vo.slot.shunt.SlotShuntTaskInitInfoVO;
import com.ruoyi.system.vo.slot.shunt.SlotShuntTaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToActivity;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToAdvert;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToAdvertOrient;
import static com.ruoyi.common.enums.slot.SlotShuntStatusEnum.*;

/**
 * 广告位切量计划Controller
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/ssp/custom/slot/slotShuntTask")
public class SlotShuntTaskController extends BaseController {

    @Autowired
    private SlotShuntTaskService slotShuntTaskService;

    @Autowired
    private SlotShuntDataService slotShuntDataService;

    @Autowired
    private SlotRedirectValidateService slotRedirectValidateService;

    @Autowired
    private SlotDataService slotDataService;

    /**
     * 查询广告位切量计划列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SlotShuntTaskListReq req) {
        if (null == req.getSlotId()) {
            return getDataTable(Collections.emptyList());
        }

        SlotShuntTaskParam param = convertParam(req);
        startPage();
        List<SlotShuntTask> tasks = slotShuntTaskService.selectList(param);

        // 查询广告位切量数据
        Map<Long, SlotShuntData> dataMap = slotShuntDataService.selectMap(tasks.stream().map(SlotShuntTask::getId).collect(Collectors.toList()));

        // 构造返回对象，补充数据
        return getDataTable(PageInfoUtils.dto2Vo(tasks, task -> {
            SlotShuntTaskVO taskVO = BeanUtil.copyProperties(task, SlotShuntTaskVO.class);
            // 如果计划已结束，结束时间展示实际停止或者取消的时间
            if (Objects.equals(task.getTaskStatus(), TERMINATED.getStatus())) {
                taskVO.setEndTime(null != task.getCancelTime() ? task.getCancelTime() : task.getFinishTime());
            }
            // 补充广告位访问数据
            SlotShuntData data = dataMap.get(task.getId());
            if (null != data) {
                taskVO.setSlotRequestPv(data.getSlotRequestPv());
                taskVO.setSlotRequestUv(data.getSlotRequestUv());
            }
            return taskVO;
        }));
    }

    /**
     * 导出广告位切量计划列表
     */
    @Log(title = "广告位切量计划", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(SlotShuntTaskListReq req) {
        SlotShuntTaskParam param = convertParam(req);
        List<SlotShuntTask> tasks = slotShuntTaskService.selectList(param);

        // 查询广告位切量数据
        Map<Long, SlotShuntData> dataMap = slotShuntDataService.selectMap(tasks.stream().map(SlotShuntTask::getId).collect(Collectors.toList()));

        // 构造Excel导出对象
        List<SlotShuntTaskExcelVO> excels = tasks.stream().map(task -> {
            SlotShuntTaskExcelVO taskVO = BeanUtil.copyProperties(task, SlotShuntTaskExcelVO.class);
            taskVO.setShuntType(SlotShuntType.getDescByType(task.getShuntType()));
            taskVO.setRedirectType(SlotRedirectType.getDescByType(task.getRedirectType()));
            taskVO.setShuntRatio(task.getShuntRatio() + "%");
            taskVO.setTaskStatus(getDescByStatus(task.getTaskStatus()));
            // 如果计划已结束，结束时间展示实际停止或者取消的时间
            if (Objects.equals(task.getTaskStatus(), TERMINATED.getStatus())) {
                taskVO.setEndTime(null != task.getCancelTime() ? task.getCancelTime() : task.getFinishTime());
            }
            // 补充广告位访问数据
            SlotShuntData data = dataMap.get(task.getId());
            if (null != data) {
                taskVO.setSlotRequestPv(data.getSlotRequestPv());
                taskVO.setSlotRequestUv(data.getSlotRequestUv());
            }
            return taskVO;
        }).collect(Collectors.toList());

        String fileName = String.format("%s_广告位%s切量计划.xlsx", UUID.randomUUID().toString(), req.getSlotId());
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        EasyExcel.write(filePath, SlotShuntTaskExcelVO.class).sheet("广告位切量计划").doWrite(excels);
        return AjaxResult.success(fileName);
    }

    /**
     * 新增广告位切量计划
     */
    @Log(title = "广告位切量计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody @Validated SlotShuntTaskReq req) {
        // 时间校验
        Date dayBegin = DateUtil.beginOfDay(req.getStartTime());
        Date dayEnd = DateUtil.endOfDay(req.getStartTime());
        if (null == req.getEndTime()) {
            req.setEndTime(DateUtil.truncate(dayEnd, DateField.SECOND));
        }
        if (req.getEndTime().after(dayEnd) || req.getStartTime().after(dayEnd)) {
            return AjaxResult.error("结束时间必须为当日");
        }
        if (req.getStartTime().before(DateUtil.beginOfDay(new Date()))) {
            return AjaxResult.error("请选择今天或之后的时间");
        }
        if (req.getEndTime().before(req.getStartTime())) {
            return AjaxResult.error("结束时间不能早于结束时间");
        }
        // 分流比例校验
        Integer shuntRatio = slotShuntTaskService.selectUsedShuntRatio(req.getSlotId(), dayBegin);
        if (shuntRatio + req.getShuntRatio() > 100) {
            return AjaxResult.error("该广告位在日期" + DateUtil.formatDate(req.getStartTime())
                    + "已有切量任务，剩余可用的流量比例为" + (100 - shuntRatio) + "%");
        }
        // 切量类型校验
        Integer usedShuntType = slotShuntTaskService.selectUsedShuntType(req.getSlotId(), dayBegin);
        if (null != usedShuntType && !Objects.equals(usedShuntType, req.getShuntType())) {
            return AjaxResult.error("该广告位在日期" + DateUtil.formatDate(req.getStartTime())
                    + "已有切量任务，切量类型只能选择" + SlotShuntType.getDescByType(usedShuntType));
        }
        // 活动ID校验
        if (redirectToActivity(req.getRedirectType()) && !slotRedirectValidateService.checkActivityId(req.getRedirectValue())) {
            return AjaxResult.error("您输入的活动ID不存在或活动计划未开启");
        }
        // 广告ID校验
        if (redirectToAdvert(req.getRedirectType()) && !slotRedirectValidateService.checkAdvertId(req.getRedirectValue())) {
            return AjaxResult.error("您输入的广告ID不存在");
        }
        // 广告配置ID校验
        if (redirectToAdvertOrient(req.getRedirectType())) {
            Pair<Boolean, Object> result = slotRedirectValidateService.checkOrientIds(req.getRedirectValue());
            if (!result.getKey()) {
                return AjaxResult.error("广告配置ID「" + result.getValue() + "」无效，请检查");
            }
        }
        return toAjax(slotShuntTaskService.addTask(BeanUtil.copyProperties(req, SlotShuntTask.class)));
    }

    /**
     * 修改广告位切量计划
     */
    @Log(title = "广告位切量计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody @Validated SlotShuntTaskReq req) {
        SlotShuntTask task = slotShuntTaskService.selectById(req.getId());
        if (null == task) {
            return AjaxResult.error("未查询到切量计划");
        }
        if (!canModify(task.getTaskStatus())) {
            return AjaxResult.error("该计划不允许修改");
        }
        // 时间校验
        Date dayBegin = DateUtil.beginOfDay(req.getStartTime());
        Date dayEnd = DateUtil.endOfDay(req.getStartTime());
        if (null == req.getEndTime()) {
            req.setEndTime(DateUtil.truncate(dayEnd, DateField.SECOND));
        }
        if (req.getEndTime().after(dayEnd) || req.getStartTime().after(dayEnd)) {
            return AjaxResult.error("结束时间必须为当日");
        }
        if (req.getStartTime().before(DateUtil.beginOfDay(new Date()))) {
            return AjaxResult.error("请选择今天或之后的时间");
        }
        if (req.getEndTime().before(req.getStartTime())) {
            return AjaxResult.error("结束时间不能早于结束时间");
        }
        // 活动ID校验
        if (redirectToActivity(req.getRedirectType()) && !slotRedirectValidateService.checkActivityId(req.getRedirectValue())) {
            return AjaxResult.error("您输入的活动ID不存在或活动计划未开启");
        }
        // 广告ID校验
        if (redirectToAdvert(req.getRedirectType()) && !slotRedirectValidateService.checkAdvertId(req.getRedirectValue())) {
            return AjaxResult.error("您输入的广告ID不存在");
        }

        SlotShuntTaskParam param = new SlotShuntTaskParam();
        param.setSlotId(req.getSlotId());
        int taskCount = slotShuntTaskService.countByParam(param);
        if (taskCount > 1) {
            // 分流比例校验
            Integer shuntRatio = slotShuntTaskService.selectUsedShuntRatio(req.getSlotId(), dayBegin);
            if (shuntRatio - task.getShuntRatio() + req.getShuntRatio() > 100) {
                return AjaxResult.error("该广告位在日期" + DateUtil.formatDate(req.getStartTime())
                        + "已有切量任务，剩余可用的流量比例为" + (100 - shuntRatio + task.getShuntRatio()) + "%");
            }
            // 切量类型校验
            Integer usedShuntType = slotShuntTaskService.selectUsedShuntType(req.getSlotId(), dayBegin);
            if (null != usedShuntType && !Objects.equals(usedShuntType, req.getShuntType())) {
                return AjaxResult.error("该广告位在日期" + DateUtil.formatDate(req.getStartTime())
                        + "已有切量任务，切量类型只能选择" + SlotShuntType.getDescByType(usedShuntType));
            }
        }

        return toAjax(slotShuntTaskService.updateTask(BeanUtil.copyProperties(req, SlotShuntTask.class)));
    }

    /**
     * 查询计划初始信息
     */
    @GetMapping("/getInitInfo")
    public AjaxResult getInitInfo(Long slotId, Date date) {
        if (null == slotId) {
            return AjaxResult.error("广告位ID不能为空");
        }

        // 查询昨日广告位数据
        SlotData slotData = slotDataService.selectBySlotIdAndDate(slotId, DateUtil.beginOfDay(DateUtil.yesterday()));
        // 查询当日已经使用的切量类型
        Integer usedShuntType = slotShuntTaskService.selectUsedShuntType(slotId, date);
        // 查询当日已经使用的切量比例
        Integer shuntRatio = slotShuntTaskService.selectUsedShuntRatio(slotId, date);

        // 构造返回结果
        SlotShuntTaskInitInfoVO infoVO = new SlotShuntTaskInitInfoVO();
        infoVO.setShuntTypeList(null != usedShuntType ? Collections.singletonList(usedShuntType) : SlotShuntType.toList());
        infoVO.setShuntRatioLimit(100 - shuntRatio);
        if (null != slotData) {
            infoVO.setSlotRequestPv(slotData.getSlotRequestPv());
            infoVO.setSlotRequestUv(slotData.getSlotRequestUv());
        }
        return AjaxResult.success(infoVO);
    }

    /**
     * 停止广告位切量计划
     */
    @Log(title = "广告位切量计划", businessType = BusinessType.UPDATE)
    @PostMapping("/terminate")
    public AjaxResult terminate(Long id) {
        if (null == id) {
            return AjaxResult.error("计划ID不能为空");
        }
        return toAjax(slotShuntTaskService.cancelTask(id));
    }

    /**
     * 删除广告位切量计划
     */
    @Log(title = "广告位切量计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        if (null == id) {
            return AjaxResult.error("无效的计划ID");
        }
        return toAjax(slotShuntTaskService.deleteTask(id));
    }

    private SlotShuntTaskParam convertParam(SlotShuntTaskListReq req) {
        SlotShuntTaskParam param = new SlotShuntTaskParam();
        param.setSlotId(req.getSlotId());
        param.setTaskName(req.getTaskName());
        param.setStartDate(Optional.ofNullable(req.getStartDate()).map(DateUtil::beginOfDay).orElse(null));
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        return param;
    }
}
