package com.ruoyi.web.controller.manager.wis.agent.info;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity;
import com.ruoyi.system.entity.common.IndustryEntity;
import com.ruoyi.system.entity.common.IndustryQualificationRequireEntity;
import com.ruoyi.system.req.advertiser.qualification.WisQualificationAddEditReq;
import com.ruoyi.system.req.advertiser.qualification.WisQualificationInfoReq;
import com.ruoyi.system.service.advertiser.AdvertiserQualificationService;
import com.ruoyi.system.service.common.IndustryQualificationRequireService;
import com.ruoyi.system.service.common.IndustryService;
import com.ruoyi.system.vo.advertiser.qualification.WisAdvertiserQualificationRequireVO;
import com.ruoyi.system.vo.advertiser.qualification.WisAgentQualificationVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.isAgent;

/**
 * [代理商平台]资质信息
 *
 * <AUTHOR>
 * @date 2022/10/26
 */
@RestController
@RequestMapping("/wis/agent/info/qualification")
public class WisAgentQualificationInfoController extends BaseController {

    @Autowired
    private AdvertiserQualificationService advertiserQualificationService;

    @Autowired
    private IndustryService industryService;

    @Autowired
    private IndustryQualificationRequireService industryQualificationRequireService;

    /**
     * 行业及资质要求
     */
    @GetMapping("/qualificationRequire")
    public Result<List<WisAdvertiserQualificationRequireVO>> qualificationRequire() {
        List<IndustryEntity> industries = industryService.selectEnableList();
        Map<Long, List<IndustryQualificationRequireEntity>> qualificationRequireMap = industryQualificationRequireService.selectMapByIndustryIds(ListUtils.mapToList(industries, IndustryEntity::getId));
        return ResultBuilder.success(industries.stream().map(industry -> {
            WisAdvertiserQualificationRequireVO require = new WisAdvertiserQualificationRequireVO();
            require.setIndustryId(industry.getId());
            require.setIndustryName(industry.getIndustryName());
            require.setQualificationList(BeanUtil.copyToList(qualificationRequireMap.get(industry.getId()), WisAdvertiserQualificationRequireVO.QualificationRequire.class));
            return require;
        }).collect(Collectors.toList()));
    }

    /**
     * 资质列表
     */
    @GetMapping("/list")
    public Result<List<WisAgentQualificationVO>> list() {
        List<AdvertiserQualificationEntity> qualificationList = advertiserQualificationService.selectListByAccountId(SecurityUtils.getLoginUser().getCrmAccountId());
        Map<Long, List<AdvertiserQualificationEntity>> qualificationMap = qualificationList.stream().collect(Collectors.groupingBy(AdvertiserQualificationEntity::getIndustryId));
        Map<Long, String> industryNameMap = industryService.selectIndustryNameMap(new ArrayList<>(qualificationMap.keySet()));

        return ResultBuilder.success(qualificationMap.entrySet().stream()
                .map(entry -> {
                    WisAgentQualificationVO vo = new WisAgentQualificationVO();
                    vo.setIndustryId(entry.getKey());
                    vo.setIndustryName(industryNameMap.get(entry.getKey()));
                    vo.setQualificationList(entry.getValue().stream().map(qualification -> {
                        WisAgentQualificationVO.Qualification info = BeanUtil.copyProperties(qualification, WisAgentQualificationVO.Qualification.class);
                        info.setQualificationId(qualification.getId());
                        info.setQualificationImgs(JSON.parseArray(qualification.getQualificationImg(), String.class));
                        return info;
                    }).collect(Collectors.toList()));
                    Set<Integer> auditStatusSet = new HashSet<>(ListUtils.mapToList(entry.getValue(), AdvertiserQualificationEntity::getAuditStatus));
                    vo.setIndustryStatus(auditStatusSet.contains(2) ? 2 : (auditStatusSet.contains(0) ? 0 : 1));
                    return vo;
                }).collect(Collectors.toList()));
    }

    /**
     * 新增资质
     */
    @PostMapping("add")
    public Result<Boolean> add(@Validated @RequestBody WisQualificationAddEditReq req) {
        // 登录用户校验
        LoginUser user = SecurityUtils.getLoginUser();
        if (!isAgent(user.getMainType())) {
            return ResultBuilder.fail("操作失败，请退出重新登录");
        }
        // 资质名称/资质图校验
        for (WisQualificationInfoReq e : req.getQualificationList()) {
            if (StringUtils.isBlank(e.getQualificationName())) {
                return ResultBuilder.fail("资质名称不能为空");
            }
            if (CollectionUtils.isEmpty(e.getQualificationImgs())) {
                return ResultBuilder.fail("[" + e.getQualificationName() +"]需要上传资质");
            }
        }
        // 必填资质校验
        List<Long> mustRequireIds = industryQualificationRequireService.selectMustRequireIdByIndustryId(req.getIndustryId());
        mustRequireIds.removeAll(ListUtils.mapToList(req.getQualificationList(), WisQualificationInfoReq::getQualificationRequireId));
        if (CollectionUtils.isNotEmpty(mustRequireIds)) {
            return ResultBuilder.fail("缺少必填资质");
        }
        // 查询行业是否存在
        Long accountId = user.getCrmAccountId();
        List<Long> industryIds = advertiserQualificationService.selectIndustryIdsByAccountId(accountId);
        if (CollUtil.contains(industryIds, req.getIndustryId())) {
            return ResultBuilder.fail("该行业资质已存在");
        }

        Date now = new Date();
        boolean result = advertiserQualificationService.batchInsert(req.getQualificationList().stream()
                .map(qualification -> {
                    AdvertiserQualificationEntity entity = new AdvertiserQualificationEntity();
                    entity.setAccountId(accountId);
                    entity.setQualificationName(qualification.getQualificationName());
                    entity.setQualificationImg(JSON.toJSONString(qualification.getQualificationImgs()));
                    entity.setExpireTime(qualification.getExpireTime());
                    entity.setQualificationType(NumberUtils.isNonNullAndGtZero(qualification.getQualificationRequireId()) ? 1 : 2);
                    entity.setIndustryId(req.getIndustryId());
                    entity.setQualificationRequireId(NumberUtils.defaultLong(qualification.getQualificationRequireId()));
                    entity.setApplicationTime(now);
                    return entity;
                }).collect(Collectors.toList()));
        return ResultBuilder.result(result);
    }

    /**
     * 更新资质
     */
    @PostMapping("edit")
    public Result<Boolean> edit(@Validated @RequestBody WisQualificationAddEditReq req) {
        // 登录用户校验
        LoginUser user = SecurityUtils.getLoginUser();
        if (!isAgent(user.getMainType())) {
            return ResultBuilder.fail("操作失败，请退出重新登录");
        }
        // 资质名称/资质图校验
        for (WisQualificationInfoReq e : req.getQualificationList()) {
            if (StringUtils.isBlank(e.getQualificationName())) {
                return ResultBuilder.fail("资质名称不能为空");
            }
            if (CollectionUtils.isEmpty(e.getQualificationImgs())) {
                return ResultBuilder.fail("[" + e.getQualificationName() +"]需要上传资质");
            }
        }
        // 必填资质校验
        List<Long> mustRequireIds = industryQualificationRequireService.selectMustRequireIdByIndustryId(req.getIndustryId());
        mustRequireIds.removeAll(ListUtils.mapToList(req.getQualificationList(), WisQualificationInfoReq::getQualificationRequireId));
        if (CollectionUtils.isNotEmpty(mustRequireIds)) {
            return ResultBuilder.fail("缺少必填资质");
        }

        boolean result = advertiserQualificationService.edit(user.getCrmAccountId(), req.getIndustryId(), req.getQualificationList());
        return ResultBuilder.result(result);
    }
}
