package com.ruoyi.web.controller.manager.crm.dsp.advert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.IsDefaultEnum;
import com.ruoyi.common.enums.advert.DeviceTargetType;
import com.ruoyi.common.enums.advert.FlowTargetType;
import com.ruoyi.common.enums.advert.IspTargetType;
import com.ruoyi.common.enums.advert.LandpageTypeEnum;
import com.ruoyi.common.enums.advert.OsTargetType;
import com.ruoyi.common.enums.advert.ServingHourEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.advert.AdvertBannedAppBo;
import com.ruoyi.system.bo.advert.AdvertOrientAppBo;
import com.ruoyi.system.bo.advert.OrientHourDataParam;
import com.ruoyi.system.bo.data.OrderDataBo;
import com.ruoyi.system.bo.traffic.TrafficPackageListBo;
import com.ruoyi.system.domain.slot.ShuntRedirectItem;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.advert.OcpcManager;
import com.ruoyi.system.req.advert.AdvertOrientationAreaTargetModifyReq;
import com.ruoyi.system.req.advert.AdvertOrientationCpcModifyReq;
import com.ruoyi.system.req.advert.AdvertOrientationDailyBudgetModifyReq;
import com.ruoyi.system.req.advert.AdvertOrientationAddReq;
import com.ruoyi.system.req.advert.AdvertOrientationCopyReq;
import com.ruoyi.system.req.advert.AdvertOrientationListReq;
import com.ruoyi.system.req.advert.AdvertOrientationModifyReq;
import com.ruoyi.system.req.advert.AdvertOrientationServingSwitchReq;
import com.ruoyi.system.req.advert.AdvertOrientationWeightModifyReq;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.req.slot.AppSlotReq;
import com.ruoyi.system.service.advert.AdvertBannedAppService;
import com.ruoyi.system.service.advert.AdvertOrientAppService;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.datasource.OrientHourDataService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.engine.cache.SlotDataCacheService;
import com.ruoyi.system.service.engine.cache.WxIfrUrlCacheService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.service.manager.SlotConfigService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.traffic.TrafficPackageService;
import com.ruoyi.system.service.validate.SlotRedirectValidateService;
import com.ruoyi.system.util.LandpageUtil;
import com.ruoyi.system.vo.advert.AdvertOrientationListVO;
import com.ruoyi.system.vo.advert.AdvertOrientationVO;
import com.ruoyi.system.vo.slot.AppSlotGroupVO;
import com.ruoyi.system.vo.slot.SlotSimpleVO;
import com.ruoyi.system.vo.traffic.TrafficPackageListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.BizConstants.OUTER_HD_ADVERT;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;
import static com.ruoyi.common.enums.advert.AdvertCategory.MINI_PROGRAM;
import static com.ruoyi.common.enums.advert.ChargeTypeEnum.isCPC;
import static com.ruoyi.common.enums.advert.ChargeTypeEnum.isOCPC;
import static com.ruoyi.common.enums.slot.SlotRedirectType.isAreaTargetRedirect;
import static com.ruoyi.common.enums.slot.SlotRedirectType.isShuntRedirect;

/**
 * 广告定向配置Controller
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/orientation")
public class AdvertOrientationController extends BaseController {

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private TrafficPackageService trafficPackageService;

    @Autowired
    private OcpcManager ocpcManager;

    @Autowired
    private AdvertBannedAppService advertBannedAppService;

    @Autowired
    private AdvertOrientAppService advertOrientAppService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private SlotConfigService slotConfigService;

    @Autowired
    private SlotRedirectValidateService slotRedirectValidateService;

    @Autowired
    private OrientHourDataService orientHourDataService;

    @Autowired
    private WxIfrUrlCacheService wxIfrUrlCacheService;

    @Autowired
    private SlotDataCacheService slotDataCacheService;

    /**
     * 获取广告定向配置
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getOrientationById(@PathVariable("id") Long id) {
        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(id);
        if (null == orient) {
            return AjaxResult.success();
        }

        AdvertOrientationVO orientVO = BeanUtil.copyProperties(orient, AdvertOrientationVO.class);
        // 屏蔽媒体
        orientVO.setBannedApps(BeanUtil.copyToList(advertBannedAppService.selectListByOrientId(orient.getId()), AppSlotGroupVO.class));
        // 定向媒体
        List<AdvertOrientAppBo> orientApps = advertOrientAppService.selectListByOrientId(orient.getId());
        if (CollectionUtils.isNotEmpty(orientApps)) {
            orientVO.setOrientApps(BeanUtil.copyToList(orientApps, AppSlotGroupVO.class));
            Map<Long, String> slotNameMap = slotService.selectSlotNameMapByAppIds(ListUtils.mapToList(orientApps, AdvertOrientAppBo::getAppId));
            Map<Long, List<Long>> appSlotMap = orientApps.stream().filter(orientApp -> StringUtils.isNotBlank(orientApp.getOrienteSlotIds())).collect(Collectors.toMap(AdvertOrientAppBo::getAppId, orientApp -> JSONArray.parseArray(orientApp.getOrienteSlotIds(), Long.class), (v1, v2) -> v2));
            if (MapUtils.isNotEmpty(appSlotMap)) {
                orientVO.getOrientApps().forEach(orientApp -> {
                    orientApp.setSlots(appSlotMap.get(orientApp.getAppId()).stream()
                            .map(slotId -> {
                                SlotSimpleVO slot = new SlotSimpleVO();
                                slot.setSlotId(slotId);
                                slot.setSlotName(slotNameMap.get(slotId));
                                return slot;
                            }).collect(Collectors.toList()));
                });
            }
        }
        // 定向流量包
        orientVO.setOrientTraffic(BeanUtil.copyToList(trafficPackageService.selectByOrientId(orient.getId()), TrafficPackageListVO.class));
        // 地域定向
        orientVO.setAreaTarget(JSON.parseObject(orient.getAreaTarget(), new TypeReference<Set<String>>() {}));
        // 设备定向
        orientVO.setDeviceTargets(DeviceTargetType.convertToList(orient.getDeviceTarget()));
        // 流量定向
        orientVO.setFlowTargets(FlowTargetType.convertToList(orient.getFlowTarget()));
        // 系统定向
        orientVO.setOsTargets(OsTargetType.convertToList(orient.getOsTarget()));
        // 运营商定向
        orientVO.setIspTargets(IspTargetType.convertToList(orient.getIspTarget()));
        // 投放时段
        orientVO.setServingHours(ServingHourEnum.convertToList(orient.getServingHour()));
        // 预估OCPC出价
        if (isOCPC(orient.getChargeType())) {
            OrderDataBo cvr = slotDataCacheService.getAdvertCvrCache(orient.getAdvertId());
            Integer ocpcPrice = ocpcManager.calculateOcpcPrice(orient.getId(), cvr.getCtr(), cvr.getCvr());
            if (null != ocpcPrice && ocpcPrice > 0) {
                orientVO.setPredictOcpcPrice(ocpcPrice);
            }
        }
        // 是否通投
        orientVO.setLaunchTotalApp(CollectionUtils.isEmpty(orientVO.getBannedApps())
                && CollectionUtils.isEmpty(orientVO.getOrientApps())
                && CollectionUtils.isEmpty(orientVO.getOrientTraffic()) ? 1 : 0);
        return AjaxResult.success(orientVO);
    }

    /**
     * 获取广告定向配置列表
     */
    @GetMapping(value = "/list/{advertId}")
    public Result<List<AdvertOrientationVO>> getOrientationList(@PathVariable("advertId") Long advertId) {
        // 校验参数
        if (null == advertId) {
            return ResultBuilder.fail("广告ID不能为空");
        }
        if (Objects.equals(advertId, OUTER_HD_ADVERT)) {
            return ResultBuilder.fail("外部互动广告无配置信息");
        }

        List<AdvertOrientation> orients = advertOrientationService.selectListByAdvertId(advertId);

        // 补充信息
        Map<Long, List<AdvertBannedAppBo>> bannedAppMap = advertBannedAppService.selectMapByAdvertId(advertId);
        Map<Long, List<AdvertOrientAppBo>> orientAppMap = advertOrientAppService.selectMapByAdvertId(advertId);
        Map<Long, List<TrafficPackageListBo>> trafficPackageMap = trafficPackageService.selectByAdvertId(advertId);
        Map<Long, String> slotNameMap = slotService.selectSlotNameMapByAppIds(orientAppMap.values().stream().flatMap(s->s.stream().map(AdvertOrientAppBo::getAppId)).collect(Collectors.toList()));

        return ResultBuilder.success(orients.stream().map(orient -> {
            AdvertOrientationVO orientVO = BeanUtil.copyProperties(orient, AdvertOrientationVO.class);

            // 屏蔽媒体
            orientVO.setBannedApps(BeanUtil.copyToList(bannedAppMap.get(orient.getId()), AppSlotGroupVO.class));
            // 定向媒体
            Optional.ofNullable(orientAppMap.get(orient.getId())).ifPresent(orientApps -> {
                orientVO.setOrientApps(BeanUtil.copyToList(orientApps, AppSlotGroupVO.class));
                // 定向媒体的定向广告位
                Map<Long, List<Long>> appSlotMap = orientApps.stream().filter(orientApp -> StringUtils.isNotBlank(orientApp.getOrienteSlotIds())).collect(Collectors.toMap(AdvertOrientAppBo::getAppId, orientApp -> JSONArray.parseArray(orientApp.getOrienteSlotIds(), Long.class), (v1, v2) -> v2));
                if (MapUtils.isNotEmpty(appSlotMap)) {
                    orientVO.getOrientApps().forEach(orientApp -> {
                        orientApp.setSlots(appSlotMap.get(orientApp.getAppId()).stream()
                        .map(slotId -> {
                            SlotSimpleVO slot = new SlotSimpleVO();
                            slot.setSlotId(slotId);
                            slot.setSlotName(slotNameMap.get(slotId));
                            return slot;
                        }).collect(Collectors.toList()));
                    });
                }
            });
            // 定向流量包
            orientVO.setOrientTraffic(BeanUtil.copyToList(trafficPackageMap.get(orient.getId()), TrafficPackageListVO.class));
            // 地域定向
            orientVO.setAreaTarget(JSON.parseObject(orient.getAreaTarget(), new TypeReference<Set<String>>() {}));
            // 设备定向
            orientVO.setDeviceTargets(DeviceTargetType.convertToList(orient.getDeviceTarget()));
            // 流量定向
            orientVO.setFlowTargets(FlowTargetType.convertToList(orient.getFlowTarget()));
            // 系统定向
            orientVO.setOsTargets(OsTargetType.convertToList(orient.getOsTarget()));
            // 运营商定向
            orientVO.setIspTargets(IspTargetType.convertToList(orient.getIspTarget()));
            // 投放时段
            orientVO.setServingHours(ServingHourEnum.convertToList(orient.getServingHour()));
            // 预估OCPC出价
            if (isOCPC(orient.getChargeType())) {
                OrderDataBo cvr = slotDataCacheService.getAdvertCvrCache(advertId);
                Integer ocpcPrice = ocpcManager.calculateOcpcPrice(orient.getId(), cvr.getCtr(), cvr.getCvr());
                if (null != ocpcPrice && ocpcPrice > 0) {
                    orientVO.setPredictOcpcPrice(ocpcPrice);
                }
            }
            // 是否通投
            orientVO.setLaunchTotalApp(CollectionUtils.isEmpty(orientVO.getBannedApps())
                    && CollectionUtils.isEmpty(orientVO.getOrientApps())
                    && CollectionUtils.isEmpty(orientVO.getOrientTraffic()) ? 1 : 0);
            // 落地页预览链接(仅针对微信防封场景)
            if (StrUtil.startWithIgnoreCase(orientVO.getLandpageUrl(), "ifr://http")) {
                String ifrUrl = wxIfrUrlCacheService.getWxIfrUrl(LandpageUtil.extractLpk(orientVO.getLandpageUrl()));
                if (StringUtils.isNotBlank(ifrUrl)) {
                    String previewUrl = StrUtil.removePrefixIgnoreCase(orientVO.getLandpageUrl(), "ifr://");
                    orientVO.setPreviewLandpageUrl((previewUrl.startsWith("http://") ? "http://" : "https://") + ifrUrl + UrlUtils.urlEncode(previewUrl));
                }
            }
            return orientVO;
        }).sorted(Comparator.comparing(AdvertOrientationVO::getId)).collect(Collectors.toList()));
    }

    /**
     * 广告配置分页列表
     */
    @GetMapping(value = "/list")
    public TableDataInfo<AdvertOrientationListVO> list(AdvertOrientationListReq req) {
        AdvertOrientation param = new AdvertOrientation();
        param.setOrientSearch(req.getOrientSearch());
        param.setOperatorName(req.getOperatorName());
        param.setGmtCreateStart(Optional.ofNullable(req.getGmtCreateStart()).map(DateUtil::beginOfDay).orElse(null));
        param.setGmtCreateEnd(Optional.ofNullable(req.getGmtCreateEnd()).map(DateUtil::endOfDay).orElse(null));
        List<Long> advertiserIds = req.getAdvertiserIds();

        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            advertiserIds = mergeParamIds(advertiserIds, permission.getValues());
            if (CollectionUtils.isEmpty(advertiserIds)) {
                return getDataTable(Collections.emptyList());
            }
        }
        // 代理商查询
        if (CollectionUtils.isNotEmpty(req.getAgentIds())) {
            List<Long> tmpAdvertiserIds = agentService.selectAdvertiserIdsByAgentIds(req.getAgentIds());
            advertiserIds = mergeParamIds(advertiserIds, tmpAdvertiserIds);
            if (CollectionUtils.isEmpty(advertiserIds)) {
                return getDataTable(Collections.emptyList());
            }
        }
        // 广告查询
        if (null != req.getAdvertId() || StringUtils.isNotBlank(req.getAdvertSearch()) || CollectionUtils.isNotEmpty(advertiserIds)) {
            Advert advertParam = new Advert();
            advertParam.setId(req.getAdvertId());
            advertParam.setSearchValue(req.getAdvertSearch());
            advertParam.setAdvertiserIds(req.getAdvertiserIds());
            List<Advert> adverts = advertService.selectAdvertList(advertParam);
            if (CollectionUtils.isEmpty(adverts)) {
                return getDataTable(Collections.emptyList());
            }
            param.setAdvertIds(ListUtils.mapToList(adverts, Advert::getId));
        }

        startPage();
        List<AdvertOrientation> list = advertOrientationService.selectAdvertOrientationList(param);

        // 补充信息
        List<Long> advertIds = ListUtils.mapToList(list, AdvertOrientation::getAdvertId);
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        Map<Long, Long> advertiserIdMap = advertService.selectAdvertiserIdMapByAdvertIds(advertIds);
        Set<Long> orientBannedOrientIds = advertOrientationService.selectOrientBannedOrientIds(advertIds);
        advertiserIds = new ArrayList<>(advertiserIdMap.values());
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(advertiserIds);
        Map<Long, Account> agentMap = agentService.selectAgentMap(advertiserIds);

        return getDataTable(PageInfoUtils.dto2Vo(list, orient -> {
            AdvertOrientationListVO orientVO = BeanUtil.copyProperties(orient, AdvertOrientationListVO.class);
            orientVO.setAdvertName(advertNameMap.get(orient.getAdvertId()));
            orientVO.setLaunchTotalApp(!orientBannedOrientIds.contains(orient.getId()));
            Optional.ofNullable(advertiserIdMap.get(orient.getAdvertId())).ifPresent(advertiserId -> {
                orientVO.setAdvertiserId(advertiserId);
                orientVO.setAdvertiserName(advertiserNameMap.get(advertiserId));
                Optional.ofNullable(agentMap.get(advertiserId)).ifPresent(agent -> {
                    orientVO.setAgentId(agent.getId());
                    orientVO.setAgentName(agent.getCompanyName());
                });
            });
            return orientVO;
        }));
    }

    /**
     * 新增广告定向配置
     */
    @Log(title = "广告定向配置", businessType = BusinessType.INSERT)
    @PutMapping("/add")
    public AjaxResult add(@RequestBody AdvertOrientationAddReq req) {
        // 配置名称校验
        if (StringUtils.isBlank(req.getOrientName())) {
            return AjaxResult.error("配置名称不能为空");
        }
        // 投放时段校验
        if (!ServingHourEnum.validate(req.getServingHours())) {
            return AjaxResult.error("请输入选择有效的投放时段");
        }
        // 计费类型校验
        if (isCPC(req.getChargeType())) {
            if (null == req.getMilliUnitPrice() && null == req.getUnitPrice()) {
                return AjaxResult.error("计费单价不能为空");
            }
            if (null != req.getMilliUnitPrice() && req.getMilliUnitPrice() < 0
                    || null != req.getUnitPrice() && req.getUnitPrice() < 0) {
                return AjaxResult.error("请输入合规价格");
            }
        } else if (isOCPC(req.getChargeType())) {
            if (null == req.getOcpcConvType()) {
                return AjaxResult.error("请选择OCPC转化类型");
            }
            if (null == req.getOcpcConvCost() || req.getOcpcConvCost() < 0) {
                return AjaxResult.error("请输入合理的OCPC转化成本");
            }
            if (null == req.getMilliUnitPrice() && null == req.getUnitPrice()) {
                return AjaxResult.error("兜底CPC价格不能为空");
            }
            if (null != req.getMilliUnitPrice() && req.getMilliUnitPrice() < 0
                    || null != req.getUnitPrice() && req.getUnitPrice() < 0) {
                return AjaxResult.error("请输入合规的兜底CPC价格");
            }
        }
        // 屏蔽媒体校验
        if (CollectionUtils.isNotEmpty(req.getBannedApps())) {
            for (AppSlotReq appSlot : req.getBannedApps()) {
                if (null != appSlot && null == appSlot.getAppId()) {
                    return AjaxResult.error("屏蔽媒体参数存在异常");
                }
            }
        }
        // 定向媒体校验
        if (CollectionUtils.isNotEmpty(req.getOrientApps())) {
            for (AppSlotReq appSlot : req.getOrientApps()) {
                if (null != appSlot && (null == appSlot.getAppId() || null == appSlot.getSlotId())) {
                    return AjaxResult.error("定向媒体参数存在异常");
                }
            }
        }

        // 查询广告
        Advert advert = advertService.selectAdvertById(req.getAdvertId());
        if (null == advert) {
            return AjaxResult.error("未查询到对应的广告");
        }
        // 落地页校验
        if (LandpageTypeEnum.isCustom(req.getLandpageType())) {
            if (!Objects.equals(advert.getAdvertCategory(), MINI_PROGRAM.getType())) {
                if (StringUtils.isBlank(req.getLandpageUrl())) {
                    return AjaxResult.error("落地页链接不能为空");
                }
                if (!req.getLandpageUrl().contains("//")) {
                    return AjaxResult.error("请输入合法的落地页链接");
                }
            }
        }

        // 新增配置
        advertOrientationService.insertAdvertOrientation(req);
        return AjaxResult.success();
    }

    /**
     * 修改广告定向配置
     */
    @Log(title = "广告定向配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdvertOrientationModifyReq req) {
        // 投放时段校验
        if (!ServingHourEnum.validate(req.getServingHours())) {
            return AjaxResult.error("请输入选择有效的投放时段");
        }
        // 计费类型校验
        if (isCPC(req.getChargeType())) {
            if (null == req.getMilliUnitPrice() && null == req.getUnitPrice()) {
                return AjaxResult.error("计费单价不能为空");
            }
            if (null != req.getMilliUnitPrice() && req.getMilliUnitPrice() < 0
                    || null != req.getUnitPrice() && req.getUnitPrice() < 0) {
                return AjaxResult.error("请输入合规价格");
            }
        } else if (isOCPC(req.getChargeType())) {
            if (null == req.getOcpcConvType()) {
                return AjaxResult.error("请选择OCPC转化类型");
            }
            if (null == req.getOcpcConvCost() || req.getOcpcConvCost() < 0) {
                return AjaxResult.error("请输入合理的OCPC转化成本");
            }
            if (null == req.getMilliUnitPrice() && null == req.getUnitPrice()) {
                return AjaxResult.error("兜底CPC价格不能为空");
            }
            if (null != req.getMilliUnitPrice() && req.getMilliUnitPrice() < 0
                    || null != req.getUnitPrice() && req.getUnitPrice() < 0) {
                return AjaxResult.error("请输入合规的兜底CPC价格");
            }
        }
        // 屏蔽媒体校验
        if (CollectionUtils.isNotEmpty(req.getBannedApps())) {
            for (AppSlotReq appSlot : req.getBannedApps()) {
                if (null != appSlot && null == appSlot.getAppId()) {
                    return AjaxResult.error("屏蔽媒体参数存在异常");
                }
            }
        }
        // 定向媒体校验
        if (CollectionUtils.isNotEmpty(req.getOrientApps())) {
            for (AppSlotReq appSlot : req.getOrientApps()) {
                if (null != appSlot && (null == appSlot.getAppId() || null == appSlot.getSlotId())) {
                    return AjaxResult.error("定向媒体参数存在异常");
                }
            }
        }

        // 查询配置
        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(req.getId());
        if (null == orient) {
            return AjaxResult.error("无效的配置ID");
        }
        Advert advert = advertService.selectAdvertById(orient.getAdvertId());
        if (null == advert) {
            return AjaxResult.error("未查询到对应的广告");
        }
        // 落地页校验
        if (LandpageTypeEnum.isCustom(req.getLandpageType())) {
            if (!Objects.equals(advert.getAdvertCategory(), MINI_PROGRAM.getType())) {
                if (StringUtils.isBlank(req.getLandpageUrl())) {
                    return AjaxResult.error("落地页链接不能为空");
                }
                if (!req.getLandpageUrl().contains("//")) {
                    return AjaxResult.error("请输入合法的落地页链接");
                }
            }
        }

        // 更新配置
        Long advertId = advertOrientationService.updateAdvertOrientation(req);
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertOrientationCacheMsg(orient.getId());
        // 检查广告预算
        advertService.sendCheckBudgetMsg(advertId);
        return AjaxResult.success();
    }

    /**
     * 复制广告定向配置
     */
    @Log(title = "广告定向配置", businessType = BusinessType.INSERT)
    @PutMapping("/copy")
    public Result<Void> copy(@RequestBody AdvertOrientationCopyReq req) {
        advertOrientationService.copyAdvertOrientation(req.getId(), null);
        return ResultBuilder.success();
    }

    /**
     * 删除配置
     */
    @Log(title = "广告定向配置", businessType = BusinessType.UPDATE)
    @PostMapping("/deleteOrient")
    public Result<Void> deleteOrient(@RequestBody IdReq req) {
        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(req.getId());
        if (null == orient) {
            return ResultBuilder.fail("无效的配置ID");
        }
        if (IsDefaultEnum.isDefault(orient.getIsDefault())) {
            return ResultBuilder.fail("默认配置不允许删除");
        }
        // 检查是否被作为直投广告
        List<SlotConfig> slotConfigs = slotConfigService.selectSlotConfigList(new SlotConfig());
        if (CollectionUtils.isNotEmpty(slotConfigs)) {
            List<Long> slotIds = new ArrayList<>();
            String orientIdStr = String.valueOf(req.getId());
            for (SlotConfig slotConfig : slotConfigs) {
                if (isShuntRedirect(slotConfig.getRedirectType()) && slotRedirectValidateService.isContainsAdvertOrient(JSON.parseArray(slotConfig.getRedirectValue(), ShuntRedirectItem.class), orientIdStr)
                        || isAreaTargetRedirect(slotConfig.getRedirectType()) && slotRedirectValidateService.isContainsAdvertOrient(slotConfig.getRedirectValue(), orientIdStr)) {
                    slotIds.add(slotConfig.getSlotId());
                }
            }
            if (CollectionUtils.isNotEmpty(slotIds)) {
                return ResultBuilder.fail(Joiner.on(",").join(slotIds) + "等广告位配置了该直投广告，暂时不能删除");
            }
        }

        // 检查近30天是否有数据
        OrientHourDataParam param = new OrientHourDataParam();
        param.setOrientId(req.getId());
        param.setStartDate(DateUtil.offsetDay(new Date(), -30));
        if (orientHourDataService.isExistBy(param)) {
            return ResultBuilder.fail("配置近30天有投放记录，暂时不能删除");
        }
        // 删除配置
        advertOrientationService.deleteAdvertOrientation(req.getId());
        refreshCacheService.sendRefreshAdvertOrientationCacheMsg(orient.getId());
        return ResultBuilder.success();
    }

    /**
     * 修改配置投放开关
     */
    @Log(title = "广告定向配置", businessType = BusinessType.UPDATE)
    @PostMapping("/toggleServingSwitch")
    public Result<Void> servingSwitch(@RequestBody @Validated AdvertOrientationServingSwitchReq req) {
        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(req.getId());
        if (null == orient) {
            return ResultBuilder.fail("无效的配置ID");
        }
        AdvertOrientation updateOrient = new AdvertOrientation();
        updateOrient.setId(req.getId());
        updateOrient.setServingSwitch(req.getServingSwitch());
        advertOrientationService.updateAdvertOrientation(updateOrient);
        refreshCacheService.sendRefreshAdvertOrientationCacheMsg(orient.getId());
        return ResultBuilder.success();
    }

    /**
     * 修改配置地域定向
     */
    @Log(title = "广告定向配置", businessType = BusinessType.UPDATE)
    @PostMapping("modifyAreaTarget")
    public Result<Void> modifyAreaTarget(@RequestBody AdvertOrientationAreaTargetModifyReq req) {
        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(req.getId());
        if (null == orient) {
            return ResultBuilder.fail("无效的配置ID");
        }
        AdvertOrientation updateOrient = new AdvertOrientation();
        updateOrient.setId(req.getId());
        updateOrient.setAreaTarget(JSON.toJSONString(CollUtil.defaultIfEmpty(req.getAreaTarget(), Collections.emptySet())));
        advertOrientationService.updateAdvertOrientation(updateOrient);
        refreshCacheService.sendRefreshAdvertOrientationCacheMsg(orient.getId());
        return ResultBuilder.success();
    }

    /**
     * 修改配置权重
     */
    @Log(title = "广告定向配置", businessType = BusinessType.UPDATE)
    @PostMapping("modifyWeight")
    public Result<Void> modifyWeight(@RequestBody @Validated AdvertOrientationWeightModifyReq req) {
        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(req.getId());
        if (null == orient) {
            return ResultBuilder.fail("无效的配置ID");
        }
        AdvertOrientation updateOrient = new AdvertOrientation();
        updateOrient.setId(req.getId());
        updateOrient.setWeight(req.getWeight());
        advertOrientationService.updateAdvertOrientation(updateOrient);
        refreshCacheService.sendRefreshAdvertOrientationCacheMsg(orient.getId());
        return ResultBuilder.success();
    }

    /**
     * 修改配置每日预算
     */
    @Log(title = "广告定向配置", businessType = BusinessType.UPDATE)
    @PostMapping("modifyDailyBudget")
    public Result<Void> modifyDailyBudget(@RequestBody AdvertOrientationDailyBudgetModifyReq req) {
        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(req.getId());
        if (null == orient) {
            return ResultBuilder.fail("无效的配置ID");
        }
        AdvertOrientation updateOrient = new AdvertOrientation();
        updateOrient.setId(req.getId());
        updateOrient.setDailyBudget(req.getDailyBudget());
        advertOrientationService.updateDailyBudget(updateOrient);
        refreshCacheService.sendRefreshAdvertOrientationCacheMsg(orient.getId());
        return ResultBuilder.success();
    }

    /**
     * 修改配置CPC价格
     */
    @Log(title = "广告定向配置", businessType = BusinessType.UPDATE)
    @PostMapping("modifyCpc")
    public Result<Void> modifyCpc(@RequestBody @Validated AdvertOrientationCpcModifyReq req) {
        if (null == req.getMilliUnitPrice() && null == req.getUnitPrice()) {
            return ResultBuilder.fail("计费单价不能为空");
        }
        int milliUnitPrice = NumberUtils.defaultInt(req.getMilliUnitPrice(), NumberUtils.defaultInt(req.getUnitPrice()) * 100);
        if (milliUnitPrice < 0) {
            return ResultBuilder.fail("计费单价不能为负数");
        }

        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(req.getId());
        if (null == orient) {
            return ResultBuilder.fail("无效的配置ID");
        }
        AdvertOrientation updateOrient = new AdvertOrientation();
        updateOrient.setId(req.getId());
        updateOrient.setMilliUnitPrice(milliUnitPrice);
        updateOrient.setUnitPrice(milliUnitPrice / 100);
        advertOrientationService.updateAdvertOrientation(updateOrient);
        refreshCacheService.sendRefreshAdvertOrientationCacheMsg(orient.getId());
        return ResultBuilder.success();
    }
}
