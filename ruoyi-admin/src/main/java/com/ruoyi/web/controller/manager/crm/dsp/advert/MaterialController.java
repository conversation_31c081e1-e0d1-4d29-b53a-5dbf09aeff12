package com.ruoyi.web.controller.manager.crm.dsp.advert;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.entity.advert.Material;
import com.ruoyi.system.req.advert.MaterialReq;
import com.ruoyi.system.service.manager.MaterialService;
import com.ruoyi.system.vo.advert.MaterialVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * 广告素材Controller
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/manager/material")
public class MaterialController extends BaseController {

    @Autowired
    private MaterialService materialService;

    /**
     * 查询广告素材列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Material req) {
        // 参数校验
        if (null == req.getAdvertId()) {
            return getDataTable(Collections.emptyList());
        }
        startPage();
        List<Material> list = materialService.selectMaterialList(req);
        return getDataTable(PageInfoUtils.dto2Vo(list, material -> BeanUtil.copyProperties(material, MaterialVO.class)));
    }

    /**
     * 获取广告素材详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        Material material = materialService.selectMaterialById(id);
        return AjaxResult.success(BeanUtil.copyProperties(material, MaterialVO.class));
    }

    /**
     * 新增广告素材
     */
    @Log(title = "广告素材", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaterialReq req) {
        return toAjax(materialService.insertMaterial(req));
    }

    /**
     * 修改广告素材
     */
    @Log(title = "广告素材", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaterialReq req) {
        return toAjax(materialService.updateMaterial(req));
    }

    /**
     * 素材状态
     */
    @Log(title = "广告素材", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestBody MaterialReq req) {
        // 校验参数
        if (null == req.getId() || null == req.getStatus()) {
            return AjaxResult.error("参数异常");
        }
        return toAjax(materialService.updateMaterialStatus(req));
    }

    /**
     * 素材权重
     */
    @Log(title = "广告素材", businessType = BusinessType.UPDATE)
    @PostMapping("/updateWeight")
    public AjaxResult updateWeight(@RequestBody MaterialReq req) {
        // 校验参数
        if (null == req.getId() || null == req.getWeight() || req.getWeight() < 1 || req.getWeight() > 10) {
            return AjaxResult.error("参数异常");
        }
        return toAjax(materialService.updateMaterialWeight(req));
    }
}
