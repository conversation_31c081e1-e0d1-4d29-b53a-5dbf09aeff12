package com.ruoyi.web.controller.manager.ssp.data;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.entity.datashow.AppData;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.vo.datashow.AppDataExportVO;
import com.ruoyi.system.vo.datashow.AppDataMoreExportVO;
import com.ruoyi.system.vo.datashow.AppDataVO;
import com.ruoyi.system.service.datasource.AppDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 媒体数据Controller
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@RestController
@RequestMapping("/datashow/appData")
public class AppDataController extends BaseController {

    @Autowired
    private AppDataService appDataService;

    @Autowired
    private WhitelistService whitelistService;

    /**
     * 查询媒体数据列表
     */
    @GetMapping("/list")
    public TableDataInfo list(AppData appData) {
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
        int hour = DateUtil.thisHour(true);

        List<AppData> list = appDataService.selectAppDataList(appData, false);
//        list = list.stream().filter(data -> !DateUtil.isSameDay(yesterday, data.getCurDate()) || hour >= 1).collect(Collectors.toList());

        // 是否展示媒体反馈的曝光数据
        boolean showSlotExposure = whitelistService.contains(WhitelistType.APP_SLOT_EXPOSURE, SecurityUtils.getLoginUser().getCrmAccountId());
        return getDataTable(PageInfoUtils.dto2Vo(list, s -> {
            AppDataVO appDataVO = BeanUtil.copyProperties(s, AppDataVO.class);
            appDataVO.setAppRevenue(s.getAppRevenue());

            // 是否展示媒体反馈的曝光数据
            if (showSlotExposure) {
                appDataVO.setSlotClickRate(NumberUtils.calculatePercent(s.getAppSlotClickPv(), s.getAppSlotExposurePv()));
            } else {
                appDataVO.setAppSlotExposurePv(null);
                appDataVO.setAppSlotExposureUv(null);
                appDataVO.setAppSlotClickPv(null);
                appDataVO.setAppSlotClickUv(null);
            }

            // 媒体收益数据13:00之后可见
            if (hour < 13 && DateUtil.isSameDay(yesterday, s.getCurDate())) {
                appDataVO.setAppRevenue(null);
            }
            return appDataVO;
        }));
    }

    /**
     * 导出媒体数据列表
     */
    @Log(title = "媒体数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(AppData appData) {
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
        int hour = DateUtil.thisHour(true);
        List<AppData> list = appDataService.selectAppDataList(appData, true);

        // 是否展示媒体反馈的曝光数据
        boolean showSlotExposure = whitelistService.contains(WhitelistType.APP_SLOT_EXPOSURE, SecurityUtils.getLoginUser().getCrmAccountId());
        if (showSlotExposure) {
            List<AppDataMoreExportVO> exportList = new ArrayList<>(list.size());
            for (AppData app : list) {
                AppDataMoreExportVO exportVO = new AppDataMoreExportVO();
                BeanUtils.copyBeanProp(exportVO, app);
                exportVO.setAppRevenue(NumberUtils.fenToYuan(app.getAppRevenue(), "-"));
                // 媒体收益数据13:00之后可见
                if (hour < 13 && DateUtil.isSameDay(yesterday, app.getCurDate())) {
                    exportVO.setAppRevenue("-");
                }
                exportVO.setAppName(String.format("%s（ID:%s）", app.getAppName(), app.getAppId()));
                exportVO.setSlotClickRate(NumberUtils.calculatePercent(app.getAppSlotClickPv(), app.getAppSlotExposurePv()));
                exportList.add(exportVO);
            }

            ExcelUtil<AppDataMoreExportVO> util = new ExcelUtil<>(AppDataMoreExportVO.class);
            return util.exportExcel(exportList, "媒体数据数据");
        }

        List<AppDataExportVO> exportList = new ArrayList<>(list.size());
        for (AppData app : list) {
            AppDataExportVO exportVO = new AppDataExportVO();
            BeanUtils.copyBeanProp(exportVO, app);
            exportVO.setAppRevenue(NumberUtils.fenToYuan(app.getAppRevenue(), "-"));
//            if (null != app.getAppRevenue()) {
//                exportVO.setAppRevenue(new BigDecimal(app.getAppRevenue()).divide(new BigDecimal(100)).toString());
//            } else {
//                exportVO.setAppRevenue("-");
//            }
            // 媒体收益数据13:00之后可见
            if (hour < 13 && DateUtil.isSameDay(yesterday, app.getCurDate())) {
                exportVO.setAppRevenue("-");
            }
            exportVO.setAppName(String.format("%s（ID:%s）", app.getAppName(), app.getAppId()));
            exportList.add(exportVO);
        }

        ExcelUtil<AppDataExportVO> util = new ExcelUtil<>(AppDataExportVO.class);
        return util.exportExcel(exportList, "媒体数据数据");
    }
}
