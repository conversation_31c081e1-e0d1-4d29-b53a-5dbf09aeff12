package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.QwFriendStatusEnum;
import com.ruoyi.common.enums.SmsChannelEnum;
import com.ruoyi.common.enums.SmsStatusEnum;
import com.ruoyi.common.enums.common.SysConfigKeyEnum;
import com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord;
import com.ruoyi.system.entity.datashow.LiuziLandpageFormRecordExcel;
import com.ruoyi.system.entity.landpage.LandpageLiuziAlipayEntity;
import com.ruoyi.system.entity.sms.LiuziSmsSendRecordCountBO;
import com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity;
import com.ruoyi.system.entity.sms.SmsTemplateEntity;
import com.ruoyi.system.entity.system.SysConfig;
import com.ruoyi.system.manager.liuzi.AsyncAlipayLandpageFormDataManager;
import com.ruoyi.system.manager.sms.LiuziSmsManager;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.req.datashow.LiuziLandpageFromRecordReq;
import com.ruoyi.system.req.sms.EditSendQiWeiConfigReq;
import com.ruoyi.system.req.sms.SmsConfigReq;
import com.ruoyi.system.req.sms.SmsContentAddReq;
import com.ruoyi.system.service.landpage.LandpageLiuziAlipayService;
import com.ruoyi.system.service.landpage.LiuziLandpageFormRecordService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.sms.LiuziSmsSendRecordService;
import com.ruoyi.system.service.sms.SmsTemplateService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.vo.advert.AdvertSelectVO;
import com.ruoyi.system.vo.app.AppSelectVO;
import com.ruoyi.system.vo.datashow.LiuziLandpageFormRecordVO;
import com.ruoyi.system.vo.datashow.LiuziLandpageSmsRecordVO;
import com.ruoyi.system.vo.slot.SlotSelectVO;
import com.ruoyi.system.vo.sms.LiuziSmsConfigVO;
import com.ruoyi.system.vo.sms.SmsContentListVO;
import com.ruoyi.system.vo.sms.SmsTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.EasyExcelUtils.exportExcel;
import static com.ruoyi.common.utils.ListUtils.mapToList;

/**
 * [CRM后台]留资转化记录
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
@Slf4j
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/datashow/liuzi/lpForm")
public class LiuziLandpageFormRecordController extends BaseController {

    private static final String EXPORT_EXCEL_NAME = "留资落地页表单记录数据";

    @Autowired
    private LiuziLandpageFormRecordService liuziLandpageFormRecordService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private LandpageLiuziAlipayService landpageLiuziAlipayService;

    @Autowired
    private LiuziSmsSendRecordService liuziSmsSendRecordService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private LiuziSmsManager liuziSmsManager;

    @Autowired
    private AsyncAlipayLandpageFormDataManager asyncAlipayLandpageFormDataManager;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private SmsTemplateService smsTemplateService;

    @Autowired
    private AdvertService advertService;

    /**
     * 广告下拉列表
     */
    @GetMapping("/advertList")
    public Result<List<AdvertSelectVO>> advertList() {
        List<Long> advertIds = liuziLandpageFormRecordService.selectTotalAdvertIds();
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(advertIds);
        Map<Long, String> landpageNameMap = landpageLiuziAlipayService.selectLandpageNameMap(advertIds);
        return ResultBuilder.success(advertIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(advertId -> new AdvertSelectVO(advertId, landpageNameMap.getOrDefault(advertId, advertNameMap.get(advertId)))).collect(Collectors.toList()));
    }

    /**
     * 媒体下拉列表
     */
    @GetMapping("/appList")
    public Result<List<AppSelectVO>> appList() {
        List<Long> appIds = liuziLandpageFormRecordService.selectTotalAppIds();
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        return ResultBuilder.success(appIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(appId -> new AppSelectVO(appId, appNameMap.get(appId))).collect(Collectors.toList()));
    }

    /**
     * 广告位下拉列表
     */
    @GetMapping("/slotList")
    public Result<List<SlotSelectVO>> slotList() {
        List<Long> slotIds = liuziLandpageFormRecordService.selectTotalSlotIds();
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(slotIds);
        return ResultBuilder.success(slotIds.stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).map(slotId -> new SlotSelectVO(slotId, slotNameMap.get(slotId))).collect(Collectors.toList()));
    }

    /**
     * 查询落地页单记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<LiuziLandpageFormRecordVO> list(LiuziLandpageFromRecordReq req) {
        // 构造查询条件
        LiuziLandpageFormRecord param = buildQueryParam(req);

        startPage();
        List<LiuziLandpageFormRecord> list = liuziLandpageFormRecordService.selectList(param);

        // 补充信息
        Map<Long, String> appNameMap = appService.selectAppNameMap(mapToList(list, LiuziLandpageFormRecord::getAppId));
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(mapToList(list, LiuziLandpageFormRecord::getSlotId));
        //查询外部自建站落地页信息
        List<Long> extendIds = list.stream().map(LiuziLandpageFormRecord::getAdvertId).collect(Collectors.toList());
        List<LandpageLiuziAlipayEntity> landpageLiuziAlipayEntities = landpageLiuziAlipayService.selectListByLandpageIds(extendIds);
        Map<Long, String> extendMap = landpageLiuziAlipayEntities.stream().collect(Collectors.toMap(LandpageLiuziAlipayEntity::getLandpageId, LandpageLiuziAlipayEntity::getLandpageName));
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(extendIds);
        //查询短信条数
        List<Long> recordIds = list.stream().map(LiuziLandpageFormRecord::getId).collect(Collectors.toList());
        List<LiuziSmsSendRecordCountBO> recordCountBOS = liuziSmsSendRecordService.countByLiuziRecordIds(recordIds);
        Map<Long, LiuziSmsSendRecordCountBO> countBOMap = recordCountBOS.stream().collect(Collectors.toMap(LiuziSmsSendRecordCountBO::getLiuziRecordId, Function.identity()));

        return getDataTable(PageInfoUtils.dto2Vo(list, data -> {
            LiuziLandpageFormRecordVO record = BeanUtil.copyProperties(data, LiuziLandpageFormRecordVO.class);
            record.setAppName(appNameMap.get(record.getAppId()));
            record.setSlotName(slotNameMap.get(record.getSlotId()));
            if (Objects.equals(data.getLandpageType(), LiuziLandPageTypeEnum.ALIPAY.getType())) {
                record.setExtendName(extendMap.getOrDefault(data.getAdvertId(), ""));
            } else {
                record.setExtendName(advertNameMap.getOrDefault(data.getAdvertId(), ""));
            }
            if (countBOMap.containsKey(data.getId())) {
                record.setSmsCount(countBOMap.get(data.getId()).getSmsCount());
            } else {
                record.setSmsCount(0);
            }

            return record;
        }));
    }

    /**
     * 导出落地页单记录列表
     */
    @Log(title = "落地页单记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LiuziLandpageFromRecordReq req) {
        // 构造查询条件
        LiuziLandpageFormRecord param = buildQueryParam(req);

        List<LiuziLandpageFormRecord> list = liuziLandpageFormRecordService.selectList(param);
        Map<Long, String> appNameMap = appService.selectAppNameMap(mapToList(list, LiuziLandpageFormRecord::getAppId));
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(mapToList(list, LiuziLandpageFormRecord::getSlotId));
        //查询外部自建站落地页信息
        List<Long> extendIds = list.stream().map(LiuziLandpageFormRecord::getAdvertId).collect(Collectors.toList());
        List<LandpageLiuziAlipayEntity> landpageLiuziAlipayEntities = landpageLiuziAlipayService.selectListByLandpageIds(extendIds);
        Map<Long, String> extendMap = landpageLiuziAlipayEntities.stream().collect(Collectors.toMap(LandpageLiuziAlipayEntity::getLandpageId, LandpageLiuziAlipayEntity::getLandpageName));
        Map<Long, String> advertNameMap = advertService.selectAdvertNameMap(extendIds);
        //查询短信发送记录
        List<Long> recordIds = list.stream().map(LiuziLandpageFormRecord::getId).collect(Collectors.toList());
        Map<Long, List<LiuziSmsSendRecordEntity>> smsRecordMap = liuziSmsSendRecordService.selectMapByLiuziRecordIds(recordIds);

        // 导出Excel
        List<LiuziLandpageFormRecordExcel> excelData = list.stream().map(record -> {
            LiuziLandpageFormRecordExcel data = BeanUtil.copyProperties(record, LiuziLandpageFormRecordExcel.class);
            data.setAppName(appNameMap.get(record.getAppId()));
            data.setSlotName(slotNameMap.get(record.getSlotId()));
            data.setLandpageTypeName(LiuziLandPageTypeEnum.getDescByType(record.getLandpageType()));
            data.setFriendStatusStr(QwFriendStatusEnum.getDescByStatus(record.getFriendStatus()));
            if (Objects.equals(record.getLandpageType(), LiuziLandPageTypeEnum.ALIPAY.getType())) {
                data.setExtendName(extendMap.getOrDefault(data.getAdvertId(), ""));
            } else {
                data.setExtendName(advertNameMap.getOrDefault(data.getAdvertId(), ""));
            }
            Optional.ofNullable(record.getPayAmount()).ifPresent(payAmount -> data.setPayAmount(NumberUtils.fenToYuan(payAmount)));
            // 短信发送记录
            List<LiuziSmsSendRecordEntity> smsRecords = smsRecordMap.getOrDefault(data.getId(), Collections.emptyList());
            data.setSmsCount(smsRecords.size());
            data.setSmsResult(getSmsResult(smsRecords));
            return data;
        }).collect(Collectors.toList());
        return AjaxResult.success(exportExcel(EXPORT_EXCEL_NAME, excelData, LiuziLandpageFormRecordExcel.class));
    }

    /**
     * 根据表单记录id查询发送信息列表
     */
    @GetMapping("smsInfoList")
    public TableDataInfo<LiuziLandpageSmsRecordVO> smsInfoList(Long id){
        startPage();
        List<LiuziSmsSendRecordEntity> liuziSmsSendRecordEntities = liuziSmsSendRecordService.selectListByLiuziRecordId(id);
        return getDataTable(PageInfoUtils.dto2Vo(liuziSmsSendRecordEntities,entity ->{
            LiuziLandpageSmsRecordVO result = BeanUtil.copyProperties(entity, LiuziLandpageSmsRecordVO.class);
            result.setChannelName(SmsChannelEnum.getChannelNameByType(result.getType()));
            return result;
        }));
    }

    /**
     * 短信内容配置详情
     */
    @GetMapping("smsConfig")
    public Result<LiuziSmsConfigVO> smsConfig(){
        String config = sysConfigService.selectConfigByKey(SysConfigKeyEnum.LIUZI_SMS_CONFIG.getKey());
        return ResultBuilder.success(JSON.parseObject(config,LiuziSmsConfigVO.class));
    }
    /**
     * 留资触达用户方式配置 发短信还是加企微 true发企微，false发短信
     * @return
     */
    @GetMapping("sendQiWeiConfig")
    public Result<Boolean> sendQiWeiConfig(){
        String config = sysConfigService.selectConfigByKey(SysConfigKeyEnum.LIUZI_SEND_QIWEI_FRIEND.getKey());
        return ResultBuilder.success(Boolean.valueOf(config));
    }

    /**
     * 更新留资触达用户方式配置
     * @param req 发送企微true，发送短信false
     * @return
     */
    @PostMapping("updateSendQiWeiConfig")
    public Result<Boolean> updateSendQiWeiConfig(@RequestBody @Validated EditSendQiWeiConfigReq req){
        SysConfig config = new SysConfig();
        config.setConfigKey(SysConfigKeyEnum.LIUZI_SEND_QIWEI_FRIEND.getKey());
        config.setConfigName(SysConfigKeyEnum.LIUZI_SEND_QIWEI_FRIEND.getDesc());
        config.setConfigType("N");
        config.setConfigValue(BooleanUtils.toStringTrueFalse(req.getOpen()));
        return ResultBuilder.success( sysConfigService.insertOrUpdateConfig(config) > 0);
    }

    /**
     * 留资短信配置
     */
    @PostMapping("edit")
    public Result<Boolean> edit(@RequestBody @Validated SmsConfigReq req){
        SysConfig config = new SysConfig();
        config.setConfigKey(SysConfigKeyEnum.LIUZI_SMS_CONFIG.getKey());
        config.setConfigName(SysConfigKeyEnum.LIUZI_SMS_CONFIG.getDesc());
        config.setConfigType("N");
        config.setConfigValue(JSON.toJSONString(req));
        return ResultBuilder.success( sysConfigService.insertOrUpdateConfig(config) > 0);
    }

    /**
     * 模版列表
     */
    @Deprecated
    @GetMapping("smsTpList")
    public Result<List<SmsTemplateVO>> smsTpList(){
        //调第三接口获取最新，同时更新到数据库
        return ResultBuilder.success(liuziSmsManager.selectAllTemplate());
    }

    /**
     * 删除短信内容
     */
    @PostMapping("deleteSmsContent")
    public Result<Boolean> deleteSmsContent(@RequestBody @Validated IdReq req){
        return ResultBuilder.success(smsTemplateService.deleteById(req.getId()));
    }

    /**
     * 给未发送短信的号码发送短信，支付宝留资号码
     */
    @GetMapping("sendSms")
    public Result<Boolean> sendSms(){
        //查询所有支付宝留资 未发送过短信的手机号码
        try (RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K028.toString(), 60)) {
            if (lock == null) {
                return ResultBuilder.fail("正在发送中，稍后再试");
            }
            List<LiuziLandpageFormRecord> liuziLandpageFormRecords = liuziLandpageFormRecordService.selectUnSendSmsList();
            liuziLandpageFormRecords.stream().forEach(record ->{
                asyncAlipayLandpageFormDataManager.sendSmsContent(record.getLandpageType(),record.getPhone(),record.getAdvertId(),record.getId(),record.getLandpageUrl());
            });

        } catch (Exception e) {
            log.error("给未发送短信的号码发送短信异常,e:", e);
        }

        return ResultBuilder.success(true);
    }

    /**
     * 新增短信内容
     */
    @PostMapping("addSmsContent")
    public Result<Boolean> addSmsContent(@RequestBody @Validated SmsContentAddReq req){
        SmsTemplateEntity insert = BeanUtil.copyProperties(req,SmsTemplateEntity.class);
        return ResultBuilder.success(smsTemplateService.insert(insert));
    }

    /**
     * 获取短信内容列表
     */
    @GetMapping("contentList")
    public TableDataInfo<SmsContentListVO> contentList(){
        TableSupport.startPage();
        List<SmsTemplateEntity> smsTemplateEntities = smsTemplateService.selectAllList();
        return getDataTable(PageInfoUtils.dto2Vo(smsTemplateEntities,entity ->{
            SmsContentListVO result = BeanUtil.copyProperties(entity, SmsContentListVO.class);
            result.setChannelName(SmsChannelEnum.getChannelNameByType(result.getType()));
            return result;
        }));
    }

    /**
     * 查询去重后的短信内容列表
     */
    @GetMapping("distinctContentList")
    public Result<List<String>> distinctContentList(){
        List<SmsTemplateEntity> smsTemplateEntities = smsTemplateService.selectAllList();
        List<String> contentList = smsTemplateEntities.stream().map(SmsTemplateEntity::getContent).distinct().collect(Collectors.toList());
        return ResultBuilder.success(contentList);
    }

    /**
     * 构造查询条件
     *
     * @param req 参数
     * @return 查询条件
     */
    private LiuziLandpageFormRecord buildQueryParam(LiuziLandpageFromRecordReq req) {
        LiuziLandpageFormRecord param = new LiuziLandpageFormRecord();
        param.setAdvertIds(req.getAdvertIds());
        param.setStartDate(req.getStartDate());
        param.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        param.setInfoSearch(req.getInfoSearch());
        param.setLandpageUrl(req.getLandpageUrl());
        param.setLandpageType(req.getLandpageType());
        param.setFriendStatus(req.getFriendStatus());
        param.setSlotIds(req.getSlotIds());
        param.setAppIds(req.getAppIds());
        return param;
    }

    /**
     * 获取短信发送状态
     */
    private String getSmsResult(List<LiuziSmsSendRecordEntity> smsRecords) {
        String smsResult = "";
        for (LiuziSmsSendRecordEntity smsRecord : smsRecords) {
            if (SmsStatusEnum.isSuccess(smsRecord.getResult())) {
                return "成功";
            }
            if (SmsStatusEnum.isSending(smsRecord.getResult())) {
                smsResult = "-";
            }
            if (SmsStatusEnum.isFail(smsRecord.getResult()) && StringUtils.isBlank(smsResult)) {
                smsResult = "失败";
            }
        }
        return StringUtils.defaultIfBlank(smsResult, "-");
    }
}
