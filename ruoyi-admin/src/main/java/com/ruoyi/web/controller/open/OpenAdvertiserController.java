package com.ruoyi.web.controller.open;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.ConvertUploadStatus;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.enums.landpage.LandPageSkinTypeEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.account.AdvertiserAccess;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.entity.open.AdvertiserCallbackRecord;
import com.ruoyi.system.manager.common.CheckManager;
import com.ruoyi.system.req.open.AdvertiserCallbackReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.SlotUpService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.AdvertiserCacheService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.open.AdvertiserCallbackRecordService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.ruoyi.common.enums.InnerLogType.CONVERT_EVENT;
import static com.ruoyi.common.enums.InnerLogType.LANDPAGE_CLICK;
import static com.ruoyi.common.enums.advert.ConvType.PAY;
import static com.ruoyi.common.enums.advert.ConvType.isPay;
import static com.ruoyi.common.enums.common.MapConfigEnum.ADVERTISER_CONV_MAP;

/**
 * 广告主接口对接(不鉴权)
 *
 * <AUTHOR>
 * @date 2022/04/15
 */
@Slf4j
@RestController
@RequestMapping("/open/advertiser")
public class OpenAdvertiserController {

    @Autowired
    private StatService statService;

    @Autowired
    private AdvertiserCallbackRecordService advertiserCallbackRecordService;

    @Autowired
    private AdvertiserCacheService advertiserCacheService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private CheckManager checkManager;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private SlotUpService slotUpService;

    @Autowired
    private ConvCallbackService convCallbackService;

    @Autowired
    private LandpageCacheService landpageCacheService;

    /**
     * 广告主回调接口
     */
    @CrossOrigin
    @PostMapping("/callback")
    public AjaxResult callback(@RequestBody @Validated AdvertiserCallbackReq req) {
        if (StringUtils.isBlank(req.getOrderNo()) && StringUtils.isBlank(req.getOrderId())) {
            return AjaxResult.error("无效的订单号");
        }

        AjaxResult result;
        try {
            result = handleCallback(req);
        } catch (Exception e) {
            log.error("广告主回调异常, req={}", JSON.toJSONString(req), e);
            result = AjaxResult.error("系统异常");
        }
        log.info("广告主回调记录: req={}, result={}", JSON.toJSONString(req), JSON.toJSONString(result));
        return result;
    }

    /**
     * 上报媒体(留资落地页转化)
     */
    @CrossOrigin
    @GetMapping("/mediaCallback")
    public AjaxResult mediaCallback(String orderId) {
        if (StringUtils.isNumeric(orderId)) {
            // 分布式锁防重复提交
            RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K074.join(orderId), 300);
            if (lock != null) {
                Order order = orderService.selectByOrderId(orderId);
                convCallbackService.handleAsync(LANDPAGE_CLICK, null, order);
            }
            log.info("mediaCallback, orderId={}, callback={}", orderId, lock != null);
        }
        return AjaxResult.success();
    }

    /**
     * 模拟上报媒体
     */
    @CrossOrigin
    @GetMapping("/mockMediaCallback")
    public AjaxResult mockMediaCallback(String nadkey) {
        if (StringUtils.isBlank(nadkey)) {
            return AjaxResult.success();
        }
        Order order = orderService.selectByOrderId(nadkey);
        mediaCallback(PAY.getType(), order, LandPageSkinTypeEnum.NO_SKIN.getType());
        JSONObject json = callbackService.getParameterFromCache(nadkey);
        json.put("orderId", nadkey);
        if (null != order) {
            json.put("advertId", order.getAdvertId());
            json.put("orientId", order.getOrientId());
        }
        return AjaxResult.success("操作成功", json);
    }

    /**
     * 签名校验
     *
     * @param req       参数
     * @param orderId   订单号
     * @param secretKey 广告主秘钥
     * @return 是否校验通过
     */
    private boolean checkSignature(AdvertiserCallbackReq req, String orderId, String secretKey) {
        List<String> arr = Arrays.asList(orderId, req.getAccessKey(), secretKey,
                String.valueOf(req.getStatus()), String.valueOf(req.getSubmitTime()), String.valueOf(req.getTimestamp()));
        arr.sort(String::compareTo);
        return StringUtils.isNotBlank(req.getSign()) && StringUtils.equalsIgnoreCase(req.getSign(), Md5Utils.hash(Joiner.on("").join(arr)));
    }

    private AjaxResult handleCallback(AdvertiserCallbackReq req) {
        // 订单号
        String orderId = StringUtils.defaultIfBlank(req.getOrderNo(), req.getOrderId());
        // 转化类型
        Integer status = req.getStatus();

        // 订单号校验
        if (!StringUtils.isNumeric(orderId)) {
            return AjaxResult.error("无效的订单号");
        }
        // 查询广告主秘钥
        AdvertiserAccess access = advertiserCacheService.queryAdvertiserAccess(req.getAccessKey());
        if (null == access || null == access.getAdvertiserId()) {
            return AjaxResult.error("无效的广告主标识");
        }
        // 签名校验
        if (!checkSignature(req, orderId, access.getSecretKey())) {
            return AjaxResult.error("无效的签名");
        }
        // 测试订单
        if (StrUtil.equalsAny(orderId, "123456", "888888")) {
            log.info("广告主转化上报测试, param={}", JSON.toJSONString(req));
            return AjaxResult.success("测试成功");
        }

        // 分布式锁防重复提交
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K013.join(orderId, status), 300);
        if (lock == null) {
            return AjaxResult.error("订单重复提交");
        }

        // 查询订单
        Order order = orderService.selectByOrderId(orderId);

        // 落地页皮肤类型
        Integer landpageSkinType = getLandpageSkinType(order);

        // 是否是落地页转化
        boolean isLandpageClick = isLandpageClick(access.getAdvertiserId(), landpageSkinType, status);

        // 支付成功的状态，记录落地页转化埋点
        try {
            if (isLandpageClick) {
                statService.landpageClick(orderId);
            }
            statService.convertEvent(orderId, status, req.getExt());
        } catch (Exception e) {
            log.error("广告主回调, 埋点统计异常, req={}", JSON.toJSONString(req), e);
        }

        // 回调记录
        try {
            AdvertiserCallbackRecord record = new AdvertiserCallbackRecord();
            if (null != order) {
                record.setSlotId(order.getSlotId());
                record.setAdvertId(order.getAdvertId());
                record.setConsumerId(order.getConsumerId());
                record.setActivityId(order.getActivityId());
                record.setOrientId(order.getOrientId());
            }
            record.setOrderNo(orderId);
            record.setAdvertiserId(access.getAdvertiserId());
            record.setStatus(status);
            record.setSubmitTime(new Date(req.getSubmitTime()));
            record.setCurDate(DateUtil.beginOfDay(record.getSubmitTime()));
            if (null != req.getExt()) {
                record.setExt(req.getExt().toString());
            }
            advertiserCallbackRecordService.insert(record);

            // 联调广告位发送钉钉提醒
            testOrderNotice(record, order);
        } catch (Exception e) {
            log.error("广告主回调, 新增回调记录异常, req={}", JSON.toJSONString(req), e);
        }

        // 获取广告位参数
        JSONObject param = callbackService.getParameterFromCache(orderId);

        // [固定收益上报]缓存转化个数和理论消耗
        if (isLandpageClick) {
            slotUpService.slotUpAddCostMulti(order, param.getString("hu"));
        }

        // 上报媒体
        if (null != order) {
            log.info("上报媒体广告位{}, orderId={}, param={}", order.getSlotId(), orderId, JSON.toJSONString(param));
        }
        mediaCallback(status, order, landpageSkinType);

        // 广告主转化上限检查
        checkAdvertiserConvLimit();

        return AjaxResult.success("成功");
    }

    /**
     * 上报媒体
     */
    private void mediaCallback(Integer status, Order order, Integer landpageSkinType) {
        if (null == order) {
            return;
        }

        if (isPay(status)) {
            // 非号卡上报付费
            if (!Objects.equals(LandPageSkinTypeEnum.PHONE_CARD.getType(), landpageSkinType)) {
                // 上报巨量【付费事件后面上传的时候要注意同步修改：active_pay】
//                callbackService.callbackOceanEngine(order.getOrderId(), order.getSlotId(), "active_pay");
//                callbackService.callbackOceanEngine(order, "form");

                // 异步上报且自增广告位转化数据
                callbackService.callbackAndIncrData(order, req -> {
                    // 上报返利
                    if (StringUtils.isNotBlank(req.getValue().getString("click_id"))) {
                        return callbackService.callbackFanli(req.getKey(), req.getValue());
                    }
                    // 上报极准
                    if (StringUtils.isNotBlank(req.getValue().getString("jcid"))) {
                        return callbackService.callbackJizhun(req.getKey(), req.getValue());
                    }
                    // 其他情况默认上报成功
                    return Pair.of(ConvertUploadStatus.SUCCESS.getStatus(), "");
                });

                convCallbackService.handleAsync(CONVERT_EVENT, PAY, order);
            }
//            // 号卡上报付费
//            else {
//                // 上报巨量
//                callbackService.callbackOceanEngine(order, "customer_effective");
//            }

            // 移除监控
            GlobalThreadPool.executorService.submit(() -> {
                Optional.ofNullable(advertService.selectAdvertById(order.getAdvertId())).ifPresent(advert -> {
                    String key = EngineRedisKeyFactory.K049.join(advert.getAdvertiserId());
                    redisCache.deleteObject(key);
                });
            });
        }
    }

    /**
     * 检查广告主转化上限
     */
    private void checkAdvertiserConvLimit() {
        GlobalThreadPool.executorService.submit(() -> {
            try {
                checkManager.checkAdvertiserConvLimit();
            } catch (Exception e) {
                log.error("checkAdvertiserConvLimit error", e);
            }
        });
    }

    /**
     * 联调回调上报
     */
    private void testOrderNotice(AdvertiserCallbackRecord record, Order order) {
        GlobalThreadPool.executorService.submit(() -> {
            if (null == record.getId()) {
                return;
            }
            // 判断是否联调广告位
            if (!Objects.equals(record.getSlotId(), 8544L)) {
                // 如果订单是产生转化的24小时前创建的，则认为在联调
                if (null != order && order.getGmtCreate().after(DateUtil.offsetDay(record.getSubmitTime(), -1))) {
                    return;
                }
            }
            StringBuilder sb = new StringBuilder("转化上报联调\n\n");
            sb.append("订单号：").append(record.getOrderNo()).append("\n")
                    .append("广告位ID：").append(record.getSlotId()).append("\n")
                    .append("广告ID：").append(record.getAdvertId()).append("\n")
                    .append("广告主ID：").append(record.getAdvertiserId()).append("\n")
                    .append("广告主名称：").append(advertiserService.selectAdvertiserName(record.getAdvertiserId())).append("\n")
                    .append("转化时间：").append(DateUtil.formatDateTime(record.getSubmitTime())).append("\n")
                    .append("转化类型：").append(ConvType.getDescByType(record.getStatus()));
            DingRobotUtil.sendText(DingWebhookConfig.getBizAlert(), sb.toString());
        });
    }

    /**
     * 根据订单号查询落地页皮肤类型
     *
     * @param order 订单
     * @return 落地页皮肤类型
     */
    private Integer getLandpageSkinType(Order order) {
        if (null == order || null == order.getAdSnapshot()) {
            return null;
        }
        try {
            AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
            if (null != adSnapshot) {
                String originLandpageUrl = adSnapshot.getOriginLandpageUrl();
                Landpage landpage = landpageCacheService.selectLandpageCache(LandpageUtil.extractLpk(originLandpageUrl));
                return null != landpage ? landpage.getSkinType() : null;
            }
        } catch (Exception e) {
            log.error("getLandpageSkinType error, orderId={}", order.getOrderId(), e);
        }
        return null;
    }

    /**
     * 判断本次上报是否落地页转化
     *
     * @param advertiserId 广告主ID
     * @param landpageSkinType 落地页皮肤类型
     * @param status 转化类型
     * @return 是否落地页转化
     */
    private boolean isLandpageClick(Long advertiserId, Integer landpageSkinType, Integer status) {
        // 广告主落地页转化对应的后端转化类型
        Integer lpClickType = mapConfigService.getValue(ADVERTISER_CONV_MAP, advertiserId, Integer.class);

        // 判断是否号卡
        boolean isHaoKa = Objects.equals(LandPageSkinTypeEnum.PHONE_CARD.getType(), landpageSkinType);

        // 支付&&非号卡 || 指定转化类型
        return (null == lpClickType && isPay(status) && !isHaoKa) || (null != lpClickType && Objects.equals(lpClickType, status));
    }
}
