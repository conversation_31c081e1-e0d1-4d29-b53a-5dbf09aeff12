package com.ruoyi.web.controller.manager.crm.ssp.setting;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.enums.domain.DomainStatus;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.req.manager.DomainListReq;
import com.ruoyi.system.req.manager.DomainUpdateStatusReq;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.service.domain.WxDomainService;
import com.ruoyi.system.util.DingRobotUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 域名Controller
 */
@RestController
@RequestMapping("/manual/manager/domain")
public class DomainCheckController extends BaseController {

    @Autowired
    private DomainService domainService;

    @Autowired
    private WxDomainService wxDomainService;

    public static String ALIPAY_BLACK_URL = "http://st.ydns.cn";

    public static Map<String, Integer> DAY_MAP = new ConcurrentHashMap<>();


    /**
     * 查询域名列表：后台和巡查：支付宝巡查使用
     */
    @CrossOrigin
    @GetMapping("/list")
    public List<String> list(DomainListReq req) {
        // 1.参数处理
        Domain param = BeanUtil.copyProperties(req, Domain.class);
        param.setDomainStatus(DomainStatus.NORMAL.getStatus());
        param.setAlipayStatus(DomainStatus.NORMAL.getStatus());

        param.setRemark("支付宝专用");
        // 2.查询域名：支付宝可用+备注【支付宝专用】
        List<Domain> aliPayList = domainService.selectDomainList(param);
        // 2.查询域名：微信可用+支付宝可用
        param.setRemark(null);
        param.setWxStatus(DomainStatus.NORMAL.getStatus());
        List<Domain> list = domainService.selectDomainList(param);
        List<Domain> listReturn = getDomains(aliPayList, list);

        // 3.过滤：域名类型:1.广告位域名,2.活动域名,3.落地页域名
        List<String> domainList = listReturn.stream().filter(domain -> domain.getDomainType() != 0)
                .map(Domain::getDomain).collect(Collectors.toList());
        // fix：每天早上10点，巡查告警是否生效
        String dateTime = DateUtils.dateTime();
        int hour = DateUtils.dateHour();
        Integer count = DAY_MAP.get(dateTime);
        if (hour >= 9 && hour <= 12 && null == count) {
            // 报警健康状态使用，事务性不用考虑
            DAY_MAP.put(dateTime, 1);
            domainList.add(ALIPAY_BLACK_URL);
        }
        // 4.返回查询列表
        return domainList;
    }

    /**
     * 支付宝：混合返回List
     * 查询域名：支付宝可用+备注【支付宝专用】：巡查时间10分钟
     * 查询域名：微信可用+支付宝可用；巡查时间2小时
     */
    private List<Domain> getDomains(List<Domain> aliPayList, List<Domain> allList) {
        if (CollectionUtils.isNotEmpty(allList) && CollectionUtils.isEmpty(aliPayList)) {
            return allList;
        }
        if (CollectionUtils.isEmpty(allList) && CollectionUtils.isNotEmpty(aliPayList)) {
            return aliPayList;
        }
        List<Domain> listReturn = new ArrayList<>();
        if (CollectionUtils.isEmpty(allList) && CollectionUtils.isEmpty(aliPayList)) {
            return listReturn;
        }
        int aliPaySize = aliPayList.size();
        int count = 0;
        // allList量比较大，巡查要1个小时
        // aliPayList量少，重要，返回的list里面2倍步长掺杂进去
        for (Domain domain : allList) {
            listReturn.add(domain);
            count++;
            if (count < aliPaySize) {
                continue;
            }
            listReturn.addAll(aliPayList);
            count = 0;
        }
        return listReturn;
    }

    /**
     * 更新支付宝和微信可投状态
     *
     * @return 是否更新成功
     */
    @CrossOrigin
    @GetMapping("/updateAlipayStatus")
    public int updateAlipayStatus(String domainstr) {
        if (StringUtils.isEmpty(domainstr)) {
            return 0;
        }
        // fix
        if (StringUtils.equals(domainstr, ALIPAY_BLACK_URL)) {
            // 钉钉告警：健康检查
            String text = "早上好，支付宝巡查任务正常，巡查域名：" + ALIPAY_BLACK_URL;
            DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), text);
            return 0;
        }
        //1.支付宝状态异常
        String text = "支付宝状态异常：" + domainstr;
        text = wxDomainService.getStringFromDB(text, domainstr);
        //2.钉钉告警
        DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), text);
        //3.修改数据库
        DomainUpdateStatusReq req = new DomainUpdateStatusReq();
        req.setAlipayStatus(DomainStatus.DISABLE.getStatus());
        return updateRefreshStatus(domainstr, req);
    }

    @CrossOrigin
    @GetMapping("/updateWxStatus")
    public int updateWxStatus(String domainstr) {
        DomainUpdateStatusReq req = new DomainUpdateStatusReq();
        req.setWxStatus(DomainStatus.DISABLE.getStatus());
        return updateRefreshStatus(domainstr, req);
    }

    @CrossOrigin
    @PostMapping("/wxCheck")
    public Result<Void> wxCheck(String url, String callbackUrl) {
        if (!StrUtil.startWithIgnoreCase(url, "http")) {
            return ResultBuilder.fail("无效的链接");
        }
        wxDomainService.checkWxBlockAsync(url, false, isBlock -> {
            if (isBlock) {
                if (StrUtil.startWithIgnoreCase(callbackUrl, "http")) {
                    HttpUtil.get(callbackUrl);
                }
            }
        });
        return ResultBuilder.success();
    }

    /**
     * 更新状态，并清理缓存
     *
     * @param domainstr
     * @param req
     * @return
     */
    private int updateRefreshStatus(String domainstr, DomainUpdateStatusReq req) {
        if (StringUtils.isEmpty(domainstr)) {
            return 0;
        }
        Domain domain = domainService.selectDomain(domainstr);
        if (null == domain) {
            return 0;
        }
        // 更新状态【需要更新缓存】
        req.setId(domain.getId());
        boolean b = domainService.updateWxAlipayStatus(req);
        return b ? 1 : 0;
    }

}


