package com.ruoyi.web.core.config;

import cn.hutool.core.io.IoUtil;
import com.ruoyi.system.service.area.Ip2regionSearcher;
import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;

@Slf4j
@Configuration
public class Ip2RegionAutoConfig {

    @Bean
    public Ip2regionSearcher ip2regionSearcher() {
        InputStream ris = ClassPathResource.class.getClassLoader().getResourceAsStream("ip2region/ip2region.xdb");
        // 空引用
        Searcher searcher;
        try {
            // 创建
            searcher = Searcher.newWithBuffer(IoUtil.readBytes(ris));
            //注意：不能使用文件类型，打成jar包后，会找不到文件
            //searcher = Searcher.newWithFileOnly(Objects.requireNonNull(this.getClass().getResource("/ipdb/ip2region.xdb")).getPath());
            return new Ip2regionSearcher(searcher);
        } catch (IOException e) {
            log.error("解析ip地址失败,无法创建搜索器", e);
            throw new RuntimeException(e);
        }
    }
}
