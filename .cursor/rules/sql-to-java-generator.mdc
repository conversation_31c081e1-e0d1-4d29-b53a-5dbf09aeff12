---
description: 
globs: 
alwaysApply: false
---
# SQL to Java Generator Rule

This rule provides guidance on generating Java files (entity, mapper, service) from SQL table structure.

## Process Overview

1. When presented with an SQL `CREATE TABLE` statement, identify:
   - Table name (for entity class naming)
   - Column names and data types
   - Primary key information
   - Comments for JavaDoc

2. **IMPORTANT**: Always ask in which subfolder to place the generated files:
   - "In which subfolder would you like me to place these files? (e.g., fc, datashow, system)"

3. Generate these files:
   - Entity class: `/ruoyi-system/src/main/java/com/ruoyi/system/entity/{subfolder}/{EntityName}Entity.java`
   - Mapper interface: `/ruoyi-system/src/main/java/com/ruoyi/system/mapper/{subfolder}/{EntityName}Mapper.java`
   - Mapper XML: `/ruoyi-system/src/main/resources/mapper/{subfolder}/{EntityName}Mapper.xml`
   - Service interface: `/ruoyi-system/src/main/java/com/ruoyi/system/service/{subfolder}/{EntityName}Service.java`
   - Service implementation: `/ruoyi-system/src/main/java/com/ruoyi/system/service/{subfolder}/impl/{EntityName}ServiceImpl.java`

4. Entity class conventions:
   - Entity class name must end with "Entity"
   - Use `@Data` annotation from Lombok
   - Add `@JsonFormat` for Date fields
   - Include JavaDoc from SQL comments

5. Mapper and Service conventions:
   - Include standard CRUD operations
   - Match method names to entity name
   - Include appropriate parameter annotations

## Example

Input:
```sql
CREATE TABLE `example_table` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
  `name` varchar(255) NOT NULL COMMENT 'Name',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;
```

Ask:
"In which subfolder would you like me to place these files?"

Then create:
- ExampleTableEntity.java
- ExampleTableMapper.java
- ExampleTableMapper.xml
- ExampleTableService.java
- ExampleTableServiceImpl.java
