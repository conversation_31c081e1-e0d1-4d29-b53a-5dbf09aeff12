-- 该lua脚本用于uv统计，当统计的数据量达到阈值时删除set避免占用过多内存，用HyperLogLog统计uv
-- KEYS[1]: setKey
-- KEYS[2]: hllKey
-- KEYS[3]: disableKey
-- ARGV[1]: 被计数的内容。如：userid
-- ARGV[2]: 删除set时的阈值
-- ARGV[3]: set过期时间
-- ARGV[4]: HyperLogLog的过期时间
-- ARGV[5]: 标记set已被删除的键的过期时间

-- 如果已经标记 Set 被淘汰，跳过 Set 操作
if redis.call('EXISTS', KEYS[3]) == 0 then
    local isNewSet = redis.call('EXISTS', KEYS[1]) == 0
    redis.call('SADD', KEYS[1], ARGV[1])

    if isNewSet then
        redis.call('EXPIRE', KEYS[1], tonumber(ARGV[3]))
    end

    local size = redis.call('SCARD', KEYS[1])
    if size >= tonumber(ARGV[2]) then
        redis.call('DEL', KEYS[1])
        redis.call('SET', KEYS[3], '1')
        redis.call('EXPIRE', KEYS[3], tonumber(ARGV[5]))
    end
end

-- 写入 HyperLogLog
local isNewHLL = redis.call('EXISTS', KEYS[2]) == 0
redis.call('PFADD', KEYS[2], ARGV[1])
if isNewHLL then
    redis.call('EXPIRE', KEYS[2], tonumber(ARGV[4]))
end

return 1