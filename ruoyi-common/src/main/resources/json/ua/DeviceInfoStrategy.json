[{"regularList": ["(ip[honead]+)(?:.*os\\s([\\w.,/\\-]+)\\slike|;\\sopera)", "(ip[honead]+).*os\\s([\\w.,/\\-]+)[);]", "(ios)\\s([\\w.,/\\-]+)[);]", "(ios|ip[honead]+)\\s*([;)])"], "funList": [{"name": "model", "valueFlag": true}]}, {"regularList": [";\\s*([\\w.,/\\- ]+)\\sbuild/", "linux;.*android.*harmony.*;\\s*([\\w.,/\\- ]+);\\s*([\\w.,/\\- ]+);.*HuaWeiBrowser", "linux;.*android.*harmony.*;\\s*([\\w.,/\\- ]+);.*HuaWeiBrowser", "linux;.*android.*harmony.*;\\s*([\\w.,/\\- ]+)[)].*HuaWeiBrowser", "linux;.*android.*;\\s*HUAWEI\\s([\\w.,/\\- ]+);.*HuaWeiBrowser", "linux;.*android.*;\\s([\\w.,/\\- ]+);\\sHMSCore.*HuaWeiBrowser", "linux;.*android.*;\\s*([\\w.,/\\- ]+);.*HuaWeiBrowser", "linux;.*android.*;\\s*([\\w.,/\\- ]+)[)].*HuaWeiBrowser", ".*android.*;\\s*([\\w.,/\\- ]+); wv[)].*VivoBrowser", ".*android.*;\\s*([\\w.,/\\- ]+); wv[)].*", "linux;.*android.*;\\s*([\\w.,/\\- ]+)[)]"], "funList": [{"name": "model", "valueFlag": true}]}]