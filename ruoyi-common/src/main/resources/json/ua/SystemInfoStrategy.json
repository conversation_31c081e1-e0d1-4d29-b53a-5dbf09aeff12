[{"regularList": ["(ip[honead]+)(?:.*os\\s([\\w.,/\\-]+)\\slike|;\\sopera)", "(ip[honead]+).*os\\s([\\w.,/\\-]+)[);]", "(ios)\\s([\\w.,/\\-]+)\\s*[);]"], "funList": [{"name": "osType", "value": "iOS"}, {"name": "osVersion", "r1": "[^a-zA-Z0-9 ]", "r2": "."}]}, {"regularList": ["(ios|ip[honead]+)\\s*([;)])"], "funList": [{"name": "osType", "value": "iOS"}, {"name": "osVersion", "value": "unknown"}]}, {"regularList": ["linux;.*(android)\\s([\\w.,/\\-]+)\\s*[;)]"], "funList": [{"name": "osType", "value": "Android"}, {"name": "osVersion", "r1": "[^a-zA-Z0-9 ]", "r2": "."}]}, {"regularList": ["linux;.*(android)\\s*([;)])"], "funList": [{"name": "osType", "value": "Android"}, {"name": "osVersion", "value": "unknown"}]}, {"regularList": ["Windows\\s*(NT)\\s([\\w.,/\\-]+)\\s*[;)]"], "funList": [{"name": "osType", "value": "Windows"}, {"name": "osVersion", "r1": "[^a-zA-Z0-9 ]", "r2": "."}]}]