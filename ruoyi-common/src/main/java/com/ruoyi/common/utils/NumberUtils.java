package com.ruoyi.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 数值工具类
 *
 * <AUTHOR>
 * @date 2021/7/22
 */
@Slf4j
public class NumberUtils {

    /**
     * 百
     */
    public static final BigDecimal HUNDRED = new BigDecimal(100);

    /**
     * 数字转字符串
     */
    public static String toStr(Number number, String defaultStr) {
        return null != number ? number.toString() : defaultStr;
    }

    /**
     * 字符串转长整数
     */
    public static Long parseLong(String str) {
        return StringUtils.isNumeric(str) ? Long.valueOf(str) : null;
    }

    /**
     * 字符串转int
     */
    public static Integer parseInteger(String str) {
        return StringUtils.isNumeric(str) ? Integer.valueOf(str) : null;
    }

    /**
     * 字符串转double
     */
    public static Double parseDouble(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        try {
            return Double.parseDouble(str);
        }catch (Exception e){
            log.error("数字转换异常,str:{}",str);
        }
        return null;
    }

    /**
     * 获取非空整数
     */
    public static int defaultInt(Integer i) {
        return null == i ? 0 : i;
    }

    /**
     * 获取非空整数
     */
    public static int defaultInt(Integer i, int defaultNum) {
        return null != i ? i : defaultNum;
    }

    /**
     * 获取非空整数
     */
    public static Long defaultLong(Long num, Long defaultNum) {
        return null != num ? num : defaultNum;
    }

    /**
     * 获取非空小数
     */
    public static double defaultDouble(Double num, double defaultNum) {
        return null != num ? num : defaultNum;
    }

    /**
     * 获取非空整数
     */
    public static Long defaultLong(Long num) {
        return null != num ? num : 0L;
    }

    /**
     * 获取非空大数
     */
    public static BigDecimal defaultBigDecimal(BigDecimal num) {
        return null != num ? num : BigDecimal.ZERO;
    }

    /**
     * 判断数字是否为空, 或者小于等于0
     */
    public static boolean isNullOrLteZero(Long value) {
        return Objects.isNull(value) || value <= 0;
    }

    /**
     * 判断数字是否非空, 且大于0
     */
    public static boolean isNonNullAndGtZero(Long value) {
        return !isNullOrLteZero(value);
    }

    /**
     * 判断数字是否为空, 或者小于等于0
     */
    public static boolean isNullOrLteZero(Integer value) {
        return Objects.isNull(value) || value <= 0;
    }

    /**
     * 判断数字是否非空, 且大于0
     */
    public static boolean isNonNullAndGtZero(Integer value) {
        return !isNullOrLteZero(value);
    }

    /**
     * 分转为元并保留两位小数
     */
    public static String fenToYuan(Integer value) {
        if (null == value) {
            return null;
        }
        return new BigDecimal(value).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP).toString();
    }
    /**
     * 分转为元并保留两位小数
     */
    public static double fenToYuanForDouble(Integer value) {
        if (null == value) {
            return 0;
        }
        return new BigDecimal(value).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 分转为元并保留两位小数
     */
    public static String fenToYuan(Long value) {
        if (null == value) {
            return null;
        }
        return new BigDecimal(value).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 分转为元并保留两位小数，如果为空则返回默认字符
     */
    public static String fenToYuan(Integer value, String defaultStr) {
        if (null == value) {
            return defaultStr;
        }
        return fenToYuan(value);
    }

    /**
     * 分转为元并保留两位小数，如果为空则返回默认字符
     */
    public static String fenToYuan(Long value, String defaultStr) {
        if (null == value) {
            return defaultStr;
        }
        return new BigDecimal(value).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 计算百分比
     */
    public static String calculatePercent(Integer dividend, Integer divisor) {
        if (null == dividend || null == divisor || Objects.equals(0, divisor)) {
            return null;
        }
        return new BigDecimal(dividend).multiply(HUNDRED)
                .divide(new BigDecimal(divisor), 2, BigDecimal.ROUND_HALF_UP).toString() + "%";
    }
    /**
     * 计算百分比
     */
    public static String calculatePercent(Long dividend, Long divisor) {
        if (null == dividend || NumberUtils.isNullOrLteZero(divisor)) {
            return null;
        }
        return new BigDecimal(dividend).multiply(HUNDRED)
                .divide(new BigDecimal(divisor), 2, BigDecimal.ROUND_HALF_UP).toString() + "%";
    }

    /**
     * 计算比率
     */
    public static String calculateRate(Integer dividend, Integer divisor) {
        if (null == dividend || null == divisor || Objects.equals(0, divisor)) {
            return null;
        }
        return new BigDecimal(dividend).divide(new BigDecimal(divisor), 2, BigDecimal.ROUND_HALF_UP).toString();
    }
    /**
     * 计算比率
     */
    public static String calculateRate(Long dividend, Long divisor) {
        if (null == dividend || null == divisor || Objects.equals(0, divisor)) {
            return null;
        }
        return new BigDecimal(dividend).divide(new BigDecimal(divisor), 2, BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 计算比率
     */
    public static String calculateRate(Integer dividend, Integer divisor,String defaultValue) {
        if (null == dividend || null == divisor || Objects.equals(0, divisor)) {
            return defaultValue;
        }
        return new BigDecimal(dividend).divide(new BigDecimal(divisor), 2, BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 计算比率
     */
    public static String calculateRate(Long dividend, Integer divisor) {
        if (null == dividend || null == divisor || Objects.equals(0, divisor)) {
            return null;
        }
        return new BigDecimal(dividend).divide(new BigDecimal(divisor), 2, BigDecimal.ROUND_HALF_UP).toString();
    }
}
