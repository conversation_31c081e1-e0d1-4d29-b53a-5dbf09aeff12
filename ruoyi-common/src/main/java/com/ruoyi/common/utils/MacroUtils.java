package com.ruoyi.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.ClickCallbackMacroEnum;
import com.ruoyi.common.enums.LandpageMacroEnum;
import org.apache.commons.collections4.MapUtils;

import java.util.Objects;

import static com.ruoyi.common.enums.ClickCallbackMacroEnum.USER_AGENT;

/**
 * 宏替换工具类
 *
 * <AUTHOR>
 * @date 2021/8/18
 */
public class MacroUtils {

    /**
     * 落地页宏替换
     *
     * @param url 链接
     * @param data 替换数据
     * @return 替换后的链接
     */
    public static String lpMacroReplace(String url, JSONObject data) {
        if (!StringUtils.contains(url, "__") || MapUtils.isEmpty(data)) {
            return url;
        }

        for (LandpageMacroEnum macro : LandpageMacroEnum.values()) {
            url = url.replace(macro.getCode(), String.valueOf(data.getOrDefault(macro.getKey(), StringUtils.EMPTY)));
        }
        return url;
    }

    /**
     * 点击监测链接宏替换
     *
     * @param url 链接
     * @param data 替换数据
     * @return 替换后的链接
     */
    public static String ccMacroReplace(String url, JSONObject data) {
        if (StringUtils.isBlank(url) || MapUtils.isEmpty(data)) {
            return url;
        }

        for (ClickCallbackMacroEnum macro : ClickCallbackMacroEnum.values()) {
            // TODO 需要对个别参数 urlEncode，以后优化
            if (Objects.equals(macro, USER_AGENT)) {
                url = url.replace(macro.getCode(), UrlUtils.urlEncode(String.valueOf(data.getOrDefault(macro.getKey(), StringUtils.EMPTY))));
            } else {
                url = url.replace(macro.getCode(), String.valueOf(data.getOrDefault(macro.getKey(), StringUtils.EMPTY)));
            }
        }
        return url;
    }
}
