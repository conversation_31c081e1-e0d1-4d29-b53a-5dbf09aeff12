package com.ruoyi.common.utils.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.math.BigDecimal;

import static com.ruoyi.common.utils.NumberUtils.HUNDRED;

/**
 * EXCEL导出格式转换:分转元
 */
public class FenToYuanConverter implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.NUMBER;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (null == value) {
            return new WriteCellData<>();
        }
        return new WriteCellData<>(new BigDecimal(value).divide(HUNDRED, 2, BigDecimal.ROUND_HALF_UP));
    }
}
