package com.ruoyi.common.utils;

import java.util.concurrent.*;

/**
 * 线程池（后期优化）
 */
public class GlobalThreadPool {

    // 通用的线程池
    public static ExecutorService executorService =
            new ThreadPoolExecutor(6, 6, 60L, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>());

    // 用于数据更新的线程池
    public static ExecutorService statExecutorService =
            new ThreadPoolExecutor(10, 10, 60L, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>());

    // 用于执行插入操作的线程池
    public static ExecutorService insertExecutorService =
            new ThreadPoolExecutor(4, 4, 60L, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>());

    // 用于耗时操作的线程池
    public static ExecutorService longTimeExecutorService =
            new ThreadPoolExecutor(30, 30, 60L, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>());

    // 用于业务日志的线程池
    public static ExecutorService logExecutorService =
            new ThreadPoolExecutor(1, 1, 60L, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>());
}
