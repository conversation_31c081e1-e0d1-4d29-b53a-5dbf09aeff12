package com.ruoyi.common.utils;

import com.ruoyi.common.constant.CookieConstant;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.uuid.IdUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * 设备号工具类
 *
 * <AUTHOR>
 * @date 2021/9/22
 */
public class DeviceIdUtils {

    /**
     * 获取或者生成设备号
     * 规则优先级:
     * 1.链接中带参数 deviceId=
     * 2.生成deviceId
     *
     * @return 设备号
     */
    public static String getOrGenerate(HttpServletRequest request) {
        // 从链接中获取deviceId参数值
        String deviceId = request.getParameter(CookieConstant.DEVICE_ID);
        if (StringUtils.isNotBlank(deviceId)) {
            return deviceId;
        }

        // 生成deviceId
        return generate(request);
    }

    /**
     * 生成设备号
     * 规则优先级:
     * 1.md5(ip+ua)
     * 2.uuid
     *
     * @return 设备号
     */
    public static String generate(HttpServletRequest request) {
        // md5(ip+ua)
        String ip = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        if (StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(userAgent)) {
            return Md5Utils.hash(ip + userAgent);
        }

        // uuid
        return IdUtils.fastUUID();
    }
}
