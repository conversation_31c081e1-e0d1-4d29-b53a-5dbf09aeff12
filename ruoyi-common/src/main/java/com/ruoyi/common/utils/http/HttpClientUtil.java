package com.ruoyi.common.utils.http;

import org.apache.commons.collections4.MapUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * HttpClient工具类
 *
 * <AUTHOR>
 * @date 2022/02/11
 */
public class HttpClientUtil {

    private HttpClientUtil() {
    }

    private static final Logger log = LoggerFactory.getLogger(HttpClientUtil.class);
    private static HttpClient defaultClient = createHttpClient(500, 50, 1000, 1000, 1000);

    /**
     * 实例化HttpClient
     *
     * @param maxTotal                 连接池最大并发连接数
     * @param maxPerRoute              单路由最大并发数
     * @param socketTimeout            从服务器读取数据的timeout
     * @param connectTimeout           和服务器建立连接的timeout
     * @param connectionRequestTimeout 从连接池获取连接的timeout
     * @return HttpClient
     */
    public static HttpClient createHttpClient(int maxTotal, int maxPerRoute, int socketTimeout, int connectTimeout,
                                              int connectionRequestTimeout) {
        RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).setConnectionRequestTimeout(connectionRequestTimeout).build();
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setMaxTotal(maxTotal);
        cm.setDefaultMaxPerRoute(maxPerRoute);
        return HttpClients.custom().setConnectionManager(cm).setDefaultRequestConfig(defaultRequestConfig).build();
    }

    /**
     * 发送post请求
     *
     * @param httpClient httpClient
     * @param url        请求地址
     * @param params     请求
     * @param encoding   编码
     * @return 请求结果
     */
    public static String sendPost(HttpClient httpClient, String url, Map<String, String> params, Charset encoding) {
        String resp = "";
        HttpPost httpPost = new HttpPost(url);
        if (MapUtils.isNotEmpty(params)) {

            // 参数集
            List<NameValuePair> formParams = new ArrayList<>(params.size());

            params.forEach((k, v) -> formParams.add(new BasicNameValuePair(k, v)));

            UrlEncodedFormEntity postEntity = new UrlEncodedFormEntity(formParams, encoding);
            httpPost.setEntity(postEntity);
        }

        CloseableHttpResponse response = null;
        try {
            response = (CloseableHttpResponse) httpClient.execute(httpPost);
            resp = EntityUtils.toString(response.getEntity(), encoding);
        } catch (IOException e) {
            log.warn("发送post请求失败,url={}", url, e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.warn("关闭response失败", e);
                }
            }
        }
        return resp;
    }

    /**
     * 使用默认httpClient发送post请求
     *
     * @param url    请求地址
     * @param params 请求参数
     * @return 请求返回结果
     */
    public static String sendPost(String url, Map<String, String> params) {
        return sendPost(defaultClient, url, params, StandardCharsets.UTF_8);
    }

    /**
     * 发送get请求
     *
     * @param httpClient httpClient
     * @param url        请求地址
     * @param encoding   编码方式
     * @return 响应结果
     */
    public static String sendGet(HttpClient httpClient, String url, Charset encoding) {
        HttpGet httpget = new HttpGet(url);
        String resp = "";
        // 执行get请求.
        CloseableHttpResponse response = null;
        try {
            response = (CloseableHttpResponse) httpClient.execute(httpget);
            resp = EntityUtils.toString(response.getEntity(), encoding);
        } catch (IOException e) {
            log.warn("发送get请求失败", e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.warn("关闭response失败", e);
                }
            }
        }
        return resp;
    }

    /**
     * 使用默认httpClient发送get请求
     *
     * @param url 请求地址
     * @return 请求返回结果
     */
    public static String sendGet(String url) {
        return sendGet(defaultClient, url, StandardCharsets.UTF_8);
    }

    /**
     * 使用默认httpClient发送get请求
     *
     * @param url 请求地址
     * @return 请求status
     */
    public static int sendGetStatus(String url) {
        HttpGet httpget = new HttpGet(url);
        // 执行get请求.
        CloseableHttpResponse response = null;
        try {
            response = (CloseableHttpResponse) defaultClient.execute(httpget);
            return response.getStatusLine().getStatusCode();
        } catch (IOException e) {
            log.warn("发送get请求获取status失败", e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.warn("关闭response失败", e);
                }
            }
        }
        return 0;
    }

    public static String sendPostJson(String url, String jsonData) {
        return sendPostJson(url, jsonData, "utf-8");
    }

    public static String sendPostJson(String url, String jsonData, String encoding) {
        return sendPostJson(url, jsonData, encoding, true);
    }

    public static String sendPostJson(String url, String jsonData, String encoding, boolean isSetHeader) {
        HttpPost httpPost = new HttpPost(url);
        if (isSetHeader) {
            httpPost.addHeader("Content-Type", "application/json");
        }
        String result = null;
        try {
            StringEntity strEntity = new StringEntity(jsonData, encoding);
            strEntity.setContentEncoding(encoding);
            httpPost.setEntity(strEntity);
            // 执行post请求
            HttpResponse response = defaultClient.execute(httpPost);
            HttpEntity resEntity = response.getEntity();
            if (resEntity != null) {
                result = EntityUtils.toString(resEntity, encoding).trim();
            }
        } catch (UnsupportedEncodingException e) {
            log.error("HttpClientUtil post JSON UnsupportedEncodingException ", e);
        } catch (ClientProtocolException e) {
            log.error("HttpClientUtil post JSON ClientProtocolException ", e);
        } catch (IOException e) {
            log.error("HttpClientUtil post JSON IOException ", e);
        } finally {
            httpPost.abort();
        }
        return result;
    }
}
