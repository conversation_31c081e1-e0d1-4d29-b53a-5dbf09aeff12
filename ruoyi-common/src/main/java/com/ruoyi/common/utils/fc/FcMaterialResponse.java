package com.ruoyi.common.utils.fc;

import lombok.Data;
import java.util.List;

/**
 * 丰巢物料接口响应类
 * <AUTHOR>
 */
@Data
public class FcMaterialResponse {
    /**
     * 中文描述
     */
    private String chnDesc;

    /**
     * 响应码
     */
    private String code;

    private String msg;

    /**
     * 响应数据
     */
    private OuterData data;

    @Data
    public static class OuterData {
        /**
         * 响应消息
         */
        private String msg;

        /**
         * 响应码
         */
        private String code;

        /**
         * 内层数据
         */
        private InnerData data;
    }

    @Data
    public static class InnerData {
        /**
         * 失败数量
         */
        private Integer failCount;

        /**
         * 成功数量
         */
        private Integer successCount;

        /**
         * 失败详情
         */
        private List<FailDetailInfo> failDetailInfo;

        /**
         * 总数量
         */
        private Integer totalCount;
    }

    @Data
    public static class FailDetailInfo {
        /**
         * 物料编码列表
         */
        private List<String> materialCodeList;

        /**
         * 失败原因
         */
        private String failReason;
    }
}