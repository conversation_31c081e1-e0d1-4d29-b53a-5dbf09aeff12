package com.ruoyi.common.utils;

import org.apache.commons.collections4.SetUtils;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 文本工具类
 *
 * <AUTHOR>
 * @date 2023/03/07
 */
public class TextUtils {

    /**
     * 文本相似度
     *
     * @param source 源文本
     * @param target 目标文本
     * @return 相似度
     */
    public static double similar(String source, String target) {
        if (source == null && target == null) {
            return 1f;
        }
        if (source == null || target == null) {
            return 0f;
        }
        Set<Integer> aChar = source.chars().boxed().collect(Collectors.toSet());
        Set<Integer> bChar = target.chars().boxed().collect(Collectors.toSet());
        // 交集数量
        int intersection = SetUtils.intersection(aChar, bChar).size();
        if (intersection == 0) {
            return 0f;
        }
        int union = SetUtils.union(aChar, bChar).size();
        return (double) intersection / union;
    }
}
