package com.ruoyi.common.utils;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class UserAgentUtils {

    /**
     * 识别操作系统的正则，只配了iOS/Android
     * 配置正则存在顺序（短路）
     */
    private static final List<StrategyRule> systemInfoStrategy = JSON.parseArray(ResourceUtil.readUtf8Str("json/ua/SystemInfoStrategy.json"), UserAgentUtils.StrategyRule.class);

    /**
     * 识别设备型号的正则
     * 配置正则存在顺序（短路）
     */
    private static final List<StrategyRule> deviceInfoStrategy = JSON.parseArray(ResourceUtil.readUtf8Str("json/ua/DeviceInfoStrategy.json"), UserAgentUtils.StrategyRule.class);

    /**
     * 设备品牌前缀映射
     */
    private static final Map<String, String> deviceBrandPrefixMap = JSON.parseObject(ResourceUtil.readUtf8Str("json/ua/DeviceBrandPrefixMap.json"), new TypeReference<Map<String, String>>() {});

    private static final String OS_TYPE = "osType";
    private static final String OS_VERSION = "osVersion";
    private static final String OS_DEVICE = "model";

    /**
     * 先获取操作系统的类别（Android x，iOS xx.x.x）
     * 再去截取手机型号
     *
     * @param ua userAgent
     * @return 解析后的设备信息
     */
    public static UserAgentDevice analysisUserAgent(String ua) {
        if (StringUtils.isBlank(ua)) {
            return null;
        }

        UserAgentDevice agentDevice = new UserAgentDevice();
        Map<String, String> systemInfo = handleByStrategy(ua, systemInfoStrategy);
        if (systemInfo == null) {
            log.warn("不可识别的系统，可能是pc端之类没有做正则的类别，ua:{}", ua);
            return null;
        } else {
            agentDevice.setOsType(systemInfo.get(OS_TYPE));
            agentDevice.setOsVersion(systemInfo.get(OS_VERSION));
        }

        Map<String, String> osInfo = handleByStrategy(ua, deviceInfoStrategy);
        if (osInfo == null) {
            log.warn("未获取到手机型号，可能是未配置的渠道样式，ua:{}", ua);
        } else {
            agentDevice.setModel(osInfo.get(OS_DEVICE));
        }
        return agentDevice;
    }

    /**
     * UserAgent解析手机型号
     *
     * @param ua userAgent
     * @return 手机型号
     */
    public static String getModel(String ua) {
        if (StringUtils.isBlank(ua)) {
            return null;
        }
        Map<String, String> osInfo = handleByStrategy(ua, deviceInfoStrategy);
        return null != osInfo ? osInfo.get(OS_DEVICE) : null;
    }

    /**
     * 针对不同功能，ua处理方法不同
     * 这是一个统一处理方法
     *
     * @param ua userAgent
     * @param strategyList 正则策略列表
     * @return 解析后的映射
     */
    private static Map<String, String> handleByStrategy(String ua, List<StrategyRule> strategyList) {
        Map<String, String> result = new HashMap<>();
        for (StrategyRule rule : strategyList) {
            for (String regular : rule.getRegularList()) {
                // 匹配正则
                Matcher matcher = PatternPool.get(regular, Pattern.CASE_INSENSITIVE).matcher(ua);
                if (matcher.find()) {
                    int k = 1;
                    // 对捕获组做处理以适应不同返回
                    for (StrategyFun f : rule.getFunList()) {
                        result.putAll(f.doAction(matcher.group(k++)));
                    }
                    return result;
                }
            }
        }
        return result;
    }

    @Data
    public static class UserAgentDevice {

        /**
         * 系统类型:Android、iOS
         */
        private String osType;

        /**
         * 系统版本
         */
        private String osVersion;

        /**
         * 手机型号，eg：Mi，HUAWEI, iPhone
         */
        private String model;
    }

    @Data
    public static class StrategyRule {

        /**
         * 正则list
         */
        private List<String> regularList;
        private List<StrategyFun> funList;
    }

    @Data
    public static class StrategyFun {
        /**
         * 对应直接返回键值对情况
         */
        String name;
        String value;

        boolean valueFlag = false;

        /**
         * 对应replace的情况
         */
        String r1;
        String r2;

        /**
         * 对不同策略对处理函数
         */
        public Map<String, String> doAction(String str) {
            if (valueFlag) {
                return MapUtil.of(name, str);
            }
            if (value != null) {
                return MapUtil.of(name, value);
            }
            if (r1 != null) {
                return MapUtil.of(name, str.replaceAll(r1, r2));
            }
            return null;
        }
    }

    /**
     * 根据UserAgent和手机型号猜品牌
     *
     * @param userAgent userAgent
     * @param model 手机型号
     * @return 手机品牌
     */
    public static String guessBrand(String userAgent, String model) {
        String brand = null;
        if (StrUtil.containsAnyIgnoreCase(userAgent, "HUAWEI", "HONOR")) {
            brand = "华为";
        } else if (StrUtil.containsAnyIgnoreCase(userAgent, "vivo", "IQOO")) {
            brand = "VIVO";
        } else if (StrUtil.containsAnyIgnoreCase(userAgent, "OPPO", "ONEPLUS")) {
            brand = "OPPO";
        } else if (StrUtil.containsAnyIgnoreCase(userAgent, "XiaoMi", "Redmi")) {
            brand = "小米";
        } else if (StrUtil.containsAnyIgnoreCase(userAgent, "SAMSUNG")) {
            brand = "三星";
        } else {
            for (Map.Entry<String, String> entry : deviceBrandPrefixMap.entrySet()) {
                if (StrUtil.startWithIgnoreCase(model, entry.getKey())) {
                    brand = entry.getValue();
                    break;
                }
            }
        }
        return brand;
    }
}
