package com.ruoyi.common.utils;

import com.ruoyi.common.enums.account.AdminTypeEnum;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.CustomException;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.SecureRandom;
import java.util.Objects;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils {

    private static final String DES = "DES";
    private static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");
    private static final String DEFAULT_KEY = "SecurityUtils";

    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getUserName();
        } catch (Exception e) {
            throw new CustomException("获取用户账户异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new CustomException("获取用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }


    /**
     * 是否为管理员
     *
     * @return 结果
     */
    public static boolean isAdmin() {
        return Objects.equals(getLoginUser().getAdminType(), AdminTypeEnum.ADMIN.getType());
    }

    public static byte[] encode2BytesBySHA(String src) {
        return DigestUtils.sha1(src);
    }

    public static String encode2StringBySHA(String src) {
        return DigestUtils.sha1Hex(src.getBytes(DEFAULT_CHARSET));
    }

    public static String encode2StringByMd5(String src) {
        return DigestUtils.md5Hex(src.getBytes(DEFAULT_CHARSET));
    }

    public static String encode2StringByMd5(byte[] src) {
        return DigestUtils.md5Hex(src);
    }

    public static String encode2StringByBase64(byte[] bytes) {
        return Base64.encodeBase64String(bytes);
    }

    public static byte[] decodeBase64(byte[] bytes) {
        return Base64.decodeBase64(bytes);
    }

    public static byte[] decodeBase64(String src) {
        return Base64.decodeBase64(src);
    }

    private static byte[] encodeOrDecodeByAes(byte[] content, String key, boolean isEncode) {
        try {
            SecretKeySpec key1 = new SecretKeySpec(key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(isEncode ? 1 : 2, key1);
            return cipher.doFinal(content);
        } catch (Exception var5) {
            throw new IllegalStateException(var5);
        }
    }

    public static byte[] encodeByAes(String content, String key) {
        return encodeByAes(content.getBytes(DEFAULT_CHARSET), key);
    }

    public static byte[] encodeByAes(byte[] content, String key) {
        return encodeOrDecodeByAes(content, key, true);
    }

    public static byte[] decodeByAes(String content, String key) {
        return encodeOrDecodeByAes(content.getBytes(DEFAULT_CHARSET), key, false);
    }

    public static byte[] decodeByAes(byte[] content, String key) {
        return encodeOrDecodeByAes(content, key, false);
    }

    public static String encrypt(String data) throws Exception {
        byte[] bt = encrypt(data.getBytes(DEFAULT_CHARSET), DEFAULT_KEY.getBytes(DEFAULT_CHARSET));
        return Base64.encodeBase64String(bt);
    }

    public static String decrypt(String data) throws IOException, Exception {
        if (data == null) {
            return null;
        } else {
            byte[] buf = Base64.decodeBase64(data);
            byte[] bt = decrypt(buf, DEFAULT_KEY.getBytes(DEFAULT_CHARSET));
            return new String(bt, DEFAULT_CHARSET);
        }
    }

    public static String encrypt(String data, String key) throws Exception {
        byte[] bt = encrypt(data.getBytes(DEFAULT_CHARSET), key.getBytes(DEFAULT_CHARSET));
        String strs = Base64.encodeBase64String(bt);
        return strs;
    }

    public static String decrypt(String data, String key) throws IOException, Exception {
        if (data == null) {
            return null;
        } else {
            byte[] buf = Base64.decodeBase64(data);
            byte[] bt = decrypt(buf, key.getBytes(DEFAULT_CHARSET));
            return new String(bt, DEFAULT_CHARSET);
        }
    }

    private static byte[] encrypt(byte[] data, byte[] key) throws Exception {
        SecureRandom sr = new SecureRandom();
        DESKeySpec dks = new DESKeySpec(key);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey securekey = keyFactory.generateSecret(dks);
        Cipher cipher = Cipher.getInstance("DES");
        cipher.init(1, securekey, sr);
        return cipher.doFinal(data);
    }

    private static byte[] decrypt(byte[] data, byte[] key) throws Exception {
        SecureRandom sr = new SecureRandom();
        DESKeySpec dks = new DESKeySpec(key);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey securekey = keyFactory.generateSecret(dks);
        Cipher cipher = Cipher.getInstance("DES");
        cipher.init(2, securekey, sr);
        return cipher.doFinal(data);
    }
}
