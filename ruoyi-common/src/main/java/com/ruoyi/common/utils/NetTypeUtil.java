package com.ruoyi.common.utils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 网络类型工具类
 *
 * <AUTHOR>
 * @date 2023-09-26
 */
public class NetTypeUtil {

    /**
     * 蜂窝网络
     */
    public static final Set<String> CELLULAR_NETWORKS = new HashSet<>(Arrays.asList("5G", "4G", "3G", "2G"));

    /**
     * 根据UserAgent判断网络类型
     *
     * @param userAgent userAgent
     * @return 网络类型
     */
    public static String getNetTypeByUserAgent(String userAgent) {
        if (StringUtils.isBlank(userAgent)) {
            return "unknown";
        }
        userAgent = userAgent.toUpperCase();
        if (userAgent.contains("WIFI") || userAgent.contains("WI-FI")) {
            return "WiFi";
        }
        if (userAgent.contains("5G")) {
            return "5G";
        }
        if (userAgent.contains("4G")) {
            return "4G";
        }
        if (userAgent.contains("3G")) {
            return "3G";
        }
        if (userAgent.contains("2G")) {
            return "2G";
        }
        return "unknown";
    }

    /**
     * 判断是否是蜂窝网络
     *
     * @param netType 网络类型
     * @return true.蜂窝网络,false.非蜂窝网络
     */
    public static boolean isCellularNetwork(String netType) {
        return null != netType && CELLULAR_NETWORKS.contains(netType);
    }
}
