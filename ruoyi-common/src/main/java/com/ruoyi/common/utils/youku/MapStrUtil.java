package com.ruoyi.common.utils.youku;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class MapStrUtil {

    public static String toStr(Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return null;
        }
        List<String> paramNameList = Lists.newArrayList();
        paramNameList.addAll(params.keySet());
        Collections.sort(paramNameList);
        StringBuilder builder = new StringBuilder();
        boolean first = true;
        for (String key : paramNameList) {
            if (first) {
                first = false;
            } else {
                builder.append("&");
            }
            builder.append(key).append("=");
            Object value = params.get(key);
            String valueString = "";
            if (null != value) {
                valueString = String.valueOf(value);
            }
            builder.append(valueString);
        }
        return builder.toString();
    }
}
