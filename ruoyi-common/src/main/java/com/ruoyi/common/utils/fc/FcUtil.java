package com.ruoyi.common.utils.fc;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.FcConfig;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpClientUtil;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 丰巢工具类
 *
 * <AUTHOR>
 */
public class FcUtil {
    private static final Logger log = LoggerFactory.getLogger(FcUtil.class);

    private static final String SIGN_METHOD = "md5";
    private static final String TOKEN_PATH = "/oauth2/token";
    private static final String MATERIAL_PATH = "/dsp/ideaMaterial/addPdbIdeaMaterial";
    private static final String PARAM_SEPARATOR = "&";
    private static final String EQUAL_SIGN = "=";
    public static final String ACCESS_TOKEN = "access_token";
    public static final String EXPIRES_IN = "expires_in";

    /** 丰巢配置实例 */
    private static FcConfig fcConfig;

    /**
     * 获取丰巢配置
     */
    private static FcConfig getFcConfig() {
        if (fcConfig == null) {
            fcConfig = SpringUtils.getBean(FcConfig.class);
        }
        return fcConfig;
    }

    public static String signTopRequest(Map<String, String> params) throws Exception {
        FcConfig fcConfig = getFcConfig();
        String appSecret = fcConfig.getAppSecret();
        String signMethod = SIGN_METHOD;
        if ( Objects.isNull(params) || params.isEmpty() || StringUtils.isEmpty(appSecret) ) {
            throw new RuntimeException("params or secret is null");
        }
        // 第一步：把字典按Key的字母顺序排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        // 第二步：把所有参数名和参数值串在一起
        StringBuilder query = new StringBuilder();
        if (signMethod == null || "md5".equals(signMethod)) {
            query.append(appSecret);
        }
        for (String key : keys) {
            String value = params.get(key);
            if ( !StringUtils.isEmpty(key) && !StringUtils.isEmpty(value) ) {
                query.append(key).append(value);
            }
        }
        // 第三步：使用MD5/HMAC加密
        byte[] bytes = new byte[0];
        if (signMethod == null || "md5".equals(signMethod)) {
            query.append(appSecret);
            log.info("request md5 decryp is ====" + query.toString());
            bytes = encryptMD5(query.toString());
        } else if ("hmac".equals(signMethod)) {
            bytes = encryptHMAC(query.toString(), appSecret);
        }

        // 第四步：把二进制转化为大写的十六进制
        return byte2hex(bytes);
    }

    private static byte[] encryptMD5(String data) throws IOException {
        byte[] bytes = null;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            bytes = md.digest(data.getBytes("UTF-8"));
        } catch (GeneralSecurityException gse) {
            throw new IOException(gse);
        }
        return bytes;
    }

    private static byte[] encryptHMAC(String data, String secret) throws IOException {
        byte[] bytes = null;
        try {
            SecretKey secretKey = new SecretKeySpec(secret.getBytes("UTF-8"), "HmacMD5");
            Mac mac = Mac.getInstance(secretKey.getAlgorithm());
            mac.init(secretKey);
            bytes = mac.doFinal(data.getBytes("UTF-8"));
        } catch (GeneralSecurityException gse) {
            throw new IOException(gse);
        }
        return bytes;
    }

    private static String getTimeStamp() {
        return DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date());
    }

    private static int getRandomNumber( ) {
        // 生成6位数的随机数
        return (int) 100000 + new Random().nextInt(900000);
    }


    private static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }

    /**
     * 获取丰巢token
     * @return token信息
     * @throws Exception 请求异常
     */
    public static Map<String, String> getToken() {
        FcConfig fcConfig = getFcConfig();
        Map<String, String> params = new HashMap<>();
        params.put("app_key", fcConfig.getAppKey());
//        params.put("timestamp", getTimeStamp());
//        params.put("random", String.valueOf(getRandomNumber()));
//        params.put("sign_method", SIGN_METHOD);
//        params.put("sign", signTopRequest(params));
        params.put("app_secret", fcConfig.getAppSecret());

        // 构建完整URL
        String url = buildUrl(params, TOKEN_PATH);

        // 发送请求并返回结果
        log.info("获取丰巢token， url={}",  url);
        String s = HttpClientUtil.sendGet(url);
        log.info("获取丰巢token， url={}, result={}",  url, s);
        if ( StringUtils.isEmpty(s) ) {
            return null;
        }else {
            JSONObject jsonObject = JSONObject.parseObject(s);
            String accessToken = jsonObject.getString("access_token");
            String expiresIn = jsonObject.getString("expires_in");
            return new HashMap<String, String>() {{
                put(ACCESS_TOKEN, accessToken);
                put(EXPIRES_IN, expiresIn);
            }};
        }
    }

    /**
     * 同步丰巢素材
     * @param accessToken 访问令牌
     * @param list 素材列表
     * @return 响应结果
     * @throws Exception 请求异常
     */
    public static FcMaterialResponse addPdbIdeaMaterial(String accessToken, List<FcMaterialReq> list) throws Exception {
        FcConfig fcConfig = getFcConfig();
        Map<String, String> params = new HashMap<>();
        params.put("app_key", fcConfig.getAppKey());
        params.put("access_token", accessToken);
        params.put("timestamp", getTimeStamp());
        params.put("random", String.valueOf(getRandomNumber()));
        params.put("sign_method", SIGN_METHOD);
        params.put("sign", signTopRequest(params));

        // 构建完整URL
        String url = buildUrl(params, MATERIAL_PATH);
        HashMap<String, Object> map = new HashMap<>();
        map.put("dealId", UUID.randomUUID().toString().replaceAll("-", ""));
        map.put("ideaMaterialList", list);
        map.put("accountId", fcConfig.getAccountId());
        log.info("同步丰巢素材， url={}, data={}",  url, JSONObject.toJSONString( map));
        String response = HttpClientUtil.sendPostJson(url, JSONObject.toJSONString( map));
        log.info("同步丰巢素材， url={}, result={}",  url, response);

        // 解析响应
        if (StringUtils.isEmpty(response)) {
            return null;
        }

        return JSONObject.parseObject(response, FcMaterialResponse.class);
    }

    /**
     * 构建请求URL
     * @param params 请求参数
     * @param path 请求路径
     * @return 完整URL
     */
    private static String buildUrl(Map<String, String> params, String path) {
        FcConfig fcConfig = getFcConfig();
        StringBuilder urlBuilder = new StringBuilder(fcConfig.getBaseUrl())
                .append(path)
                .append("?");

        // 构建查询字符串
        String queryString = params.entrySet().stream()
                .map(entry -> entry.getKey() + EQUAL_SIGN + entry.getValue())
                .collect(Collectors.joining(PARAM_SEPARATOR));

        return urlBuilder.append(queryString).toString();
    }

}
