package com.ruoyi.common.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * Url工具类
 */
@Slf4j
public class UrlUtils {

    private static final List<Character> EXTRACT_DOMAIN_SPLITTER = Arrays.asList('?', '/', '#');

    /**
     * 加密算法
     */
    private static final String ALGORITHM = "AES";

    /**
     * 加密模式
     */
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";

    /**
     * Base64编码器（URL安全）
     */
    private static final Base64.Encoder URL_ENCODER = Base64.getUrlEncoder().withoutPadding();

    /**
     * Base64解码器（URL安全）
     */
    private static final Base64.Decoder URL_DECODER = Base64.getUrlDecoder();

    /**
     * 解析出url参数中的键值对,
     * 如 "http://www.baidu.com/index.jsp?Action=del&id=123"，解析出Action:del,id:123存入map中
     *
     * @param url url地址
     * @return url请求参数部分
     */
    public static Map<String, String> extractUrlParamsFromUrl(String url) {
        String strUrlParam = truncateUrlPage(url);
        if (strUrlParam == null) {
            return Collections.emptyMap();
        }
        return extractUrlParams(strUrlParam);
    }

    /**
     * 解析出url参数中的键值对 如 "action=del&id=123"，解析出action:del,id:123存入map中
     * (注意：此方法对类似a=1&a=2的字符串支持不友好)
     */
    public static Map<String, String> extractUrlParams(String paramsStr) {
        if (StringUtils.isBlank(paramsStr)) {
            return Collections.emptyMap();
        }
        Map<String, String> mapRequest = new HashMap<>();
        String[] arrSplit;
        // 每个键值为一组
        arrSplit = StringUtils.split(paramsStr, '&');
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = StringUtils.split(strSplit, '=');
            // 解析出键值
            if (arrSplitEqual.length > 1) {
                // 正确解析
                mapRequest.put(arrSplitEqual[0], arrSplitEqual[1]);
            } else {
                if (!"".equals(arrSplitEqual[0])) {
                    // 只有参数没有值，加入空字符
                    mapRequest.put(arrSplitEqual[0], "");
                }
            }
        }
        return mapRequest;
    }

    /**
     * 把map组装成类似action=del&id=123的字符串。
     *
     * @param params
     * @return
     */
    public static String buildUrlParams(Map<String, String> params) {
        if (null == params || params.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 去掉url中的路径，留下请求参数部分
     *
     * @param strURL url地址
     * @return url请求参数部分
     */
    private static String truncateUrlPage(String strURL) {
        String url = StringUtils.trimToEmpty(strURL);

        int idx = url.indexOf('?');
        if (idx == -1) {
            return null;
        }
        return url.substring(idx + 1);
    }

    /**
     * 解析出url请求排除掉GET参数的部分, 比如入参http://www.baidu.com/index?a=1返回http://www.baidu.com/index
     *
     * @param strUrl url地址
     * @return url路径
     */
    public static String extractUrl(String strUrl) {
        if (StringUtils.isBlank(strUrl)) {
            return strUrl;
        }
        String url = StringUtils.trimToEmpty(strUrl);

        int idx = url.indexOf('?');
        if (idx == -1) {
            return strUrl;
        }
        return url.substring(0, idx);
    }

    /**
     * 往url后面附加GET参数:(url拼参数). <br/>
     * 注意，不会对参数进行urlencode，你需要自行encode
     *
     * @param url 原链接
     * @param params 拼接参数
     * @return url 拼接参数后的链接
     */
    public static String appendParams(String url, Map<String, String> params) {
        if (null == url || null == params || params.isEmpty()) {
            return url;
        }

        StringBuilder sb = new StringBuilder(url);
        sb.append(sb.indexOf("?") != -1 ? "&" : "?");
        sb.append(buildUrlParams(params));
        return sb.toString();
    }

    /**
     * UrlEncode
     * 注:+不会被编码
     */
    public static String urlEncode(String str) {
        try {
            return URLEncoder.encode(str, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            return str;
        }
    }

    /**
     * UrlDecode
     */
    public static String urlDecode(String str) {
        if (null == str || str.isEmpty()) {
            return null;
        }
        try {
            return URLDecoder.decode(str, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            return str;
        }
    }

    /**
     * 从链接中解析出域名
     *
     * @param url 链接
     * @return 域名
     */
    public static String extractDomain(String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }

        String tempUrl;
        if (url.contains("http://")) {
            // 截取 http:// 之后的地址
            tempUrl = url.substring(7);
        } else if (url.contains("https://")) {
            // 截取 https:// 之后的地址
            tempUrl = url.substring(8);
        } else {
            tempUrl = url;
        }

        int index;
        for (Character ch : EXTRACT_DOMAIN_SPLITTER) {
            index = tempUrl.indexOf(ch);
            if (index >= 0) {
                tempUrl = tempUrl.substring(0, index);
            }
        }
        return tempUrl;
    }

    /**
     * 加密目标URL（指定密钥）
     *
     * @param targetUrl 原始目标URL
     * @param secretKey 加密密钥
     * @return 加密后的短字符串
     */
    public static String encryptTargetUrl(String targetUrl, String secretKey) {
        if (StringUtils.isBlank(targetUrl)) {
            return null;
        }

        try {
            // 创建密钥
            SecretKeySpec keySpec = createSecretKey(secretKey);

            // 创建加密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);

            // 加密
            byte[] encrypted = cipher.doFinal(targetUrl.getBytes(StandardCharsets.UTF_8));

            // Base64编码（URL安全，无填充）
            return URL_ENCODER.encodeToString(encrypted);

        } catch (Exception e) {
            log.error("URL加密失败: {}", targetUrl, e);
            return null;
        }
    }

    /**
     * 解密目标URL（指定密钥）
     *
     * @param encryptedUrl 加密后的URL字符串
     * @param secretKey 解密密钥
     * @return 原始目标URL
     */
    public static String decryptTargetUrl(String encryptedUrl, String secretKey) {
        if (StringUtils.isBlank(encryptedUrl)) {
            return null;
        }

        try {
            // 创建密钥
            SecretKeySpec keySpec = createSecretKey(secretKey);

            // Base64解码
            byte[] encrypted = URL_DECODER.decode(encryptedUrl);

            // 创建解密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);

            // 解密
            byte[] decrypted = cipher.doFinal(encrypted);

            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("URL解密失败: {}", encryptedUrl, e);
            return null;
        }
    }

    /**
     * 创建密钥规范
     *
     * @param secretKey 密钥字符串
     * @return SecretKeySpec对象
     */
    private static SecretKeySpec createSecretKey(String secretKey) {
        try {
            // 使用SHA-256哈希确保密钥长度为32字节（256位）
            byte[] keyBytes = secretKey.getBytes(StandardCharsets.UTF_8);

            // 如果密钥长度不足16字节，进行填充
            if (keyBytes.length < 16) {
                byte[] paddedKey = new byte[16];
                System.arraycopy(keyBytes, 0, paddedKey, 0, keyBytes.length);
                // 用密钥本身循环填充
                for (int i = keyBytes.length; i < 16; i++) {
                    paddedKey[i] = keyBytes[i % keyBytes.length];
                }
                keyBytes = paddedKey;
            } else if (keyBytes.length > 16) {
                // 如果密钥长度超过16字节，截取前16字节
                byte[] truncatedKey = new byte[16];
                System.arraycopy(keyBytes, 0, truncatedKey, 0, 16);
                keyBytes = truncatedKey;
            }

            return new SecretKeySpec(keyBytes, ALGORITHM);

        } catch (Exception e) {
            log.error("创建密钥失败", e);
            throw new RuntimeException("创建密钥失败", e);
        }
    }
}
