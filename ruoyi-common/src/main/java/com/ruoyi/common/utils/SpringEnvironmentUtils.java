package com.ruoyi.common.utils;

import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 * Spring环境工具类
 *
 * <AUTHOR>
 * @date 2022/5/31 5:27 下午
 */
public class SpringEnvironmentUtils {

    private static ApplicationContext applicationContext;

    // 环境枚举:0.开发,1.测试,2.生产
    private static byte environ = 0;

    public static String DEV = "dev";
    public static String TEST = "test";
    public static String PROD = "prod";

    public static void setApplicationContext(ApplicationContext context) {
        SpringEnvironmentUtils.applicationContext = context;
        //获取当前的系统环境
        Environment evn = applicationContext.getEnvironment();
        String[] activeProfiles = evn.getActiveProfiles();
        for (String profile : activeProfiles) {
            if (DEV.equals(profile)){
                break;
            } else if (TEST.equals(profile)){
                environ = 1;
                break;
            } else if (PROD.equals(profile)){
                environ = 2;
                break;
            }
        }
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    //获取配置文件配置值
    public static String getEvnProperty(String key){
        return applicationContext.getEnvironment().getProperty(key);
    }

    //通过bean名称获取bean
    public static Object getBeanByName(String name){
        try {
            return getApplicationContext().getBean(name);
        } catch (Exception e){
            return null;
        }
    }

    public static Object getBeanByClassName(String className){
        try{
            Class aClass = Class.forName(className);
            Object obj = getApplicationContext().getBean(aClass);
            return obj;
        }catch(Exception ex){
            return null;
        }
    }

    public static <T> T getBean(Class<T> clazz) {
        if (SpringEnvironmentUtils.applicationContext == null) {
            return null;
        }
        try {
            return SpringEnvironmentUtils.applicationContext.getBean(clazz);
        } catch (Exception e){
            return null;
        }
    }

    //当前是否为开发环境
    public static boolean isDev() {
        return environ == 0;
    }

    //是否为测试环境
    public static boolean isTest(){
        return environ == 1;
    }

    //是否为生产环境
    public static boolean isProd(){
        return environ == 2;
    }

    ///获取当前环境
    public static byte getEnviron(){
        return environ;
    }

    // 获取当前环境
    public static String getCurrentEnv(){

        if(environ == 1){
            return TEST;
        }else if(environ == 2){
            return PROD;
        }
        return DEV;
    }

    //获取当前请求
    public static HttpServletRequest currentRequest(){
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return Optional.ofNullable(servletRequestAttributes).map(ServletRequestAttributes::getRequest).orElse(null);
    }
}
