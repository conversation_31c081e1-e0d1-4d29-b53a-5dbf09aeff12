package com.ruoyi.common.utils;

import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * UV统计工具类。
 * 数据量低于阈值时采用Set与HyperLogLog双写，超出阈值后删除Set保留HyperLogLog减少Redis内存使用。
 */
public class UvCountUtils {

    // 预加载Lua脚本
    private static final DefaultRedisScript<Long> UV_COUNT_SCRIPT = LuaScriptUtils.getScript("UvCount", Long.class);

    private static final RedisCache redisCache = SpringUtils.getBean(RedisCache.class);


    // Set元素数量达到该阈值后，转为仅使用HyperLogLog
    private static final long THRESHOLD = 1000;

    // Key前缀
    private static final String SET_PREFIX = "set:";
    private static final String HLL_PREFIX = "hll:";
    private static final String DISABLE_PREFIX = "disable:";

    /**
     * 记录UV
     *
     * @param key     主键
     * @param value   用户唯一标识
     * @param timeout 超时时间
     * @param unit    时间单位
     */
    public static void recordUv(String key, String value, long timeout, TimeUnit unit) {
        String setKey = SET_PREFIX + key;
        String hllKey = HLL_PREFIX + key;
        String disableKey = DISABLE_PREFIX + key;

        String expireSeconds = String.valueOf(unit.toSeconds(timeout));

        redisCache.executeLuaScript(
                UV_COUNT_SCRIPT,
                Arrays.asList(setKey, hllKey, disableKey),
                value,
                String.valueOf(THRESHOLD),
                expireSeconds,
                expireSeconds,
                expireSeconds
        );
    }

    /**
     * 获取UV计数
     *
     * @param key 主键
     * @return 去重后的UV数量
     */
    public static Long getUv(String key) {
        String setKey = SET_PREFIX + key;
        String hllKey = HLL_PREFIX + key;
        return redisCache.hasKey(setKey) ? redisCache.countCacheSet(setKey) : redisCache.countHyperLogLog(hllKey);
    }
    
    /**
     * 删除UV统计数据
     *
     * @param key 主键
     * @return 是否删除成功
     */
    public static boolean deleteUvData(String key) {
        String setKey = SET_PREFIX + key;
        String hllKey = HLL_PREFIX + key;
        String disableKey = DISABLE_PREFIX + key;
        
        // 直接删除所有相关键，不检查是否存在
        Set<String> keysToDelete = new HashSet<>();
        keysToDelete.add(setKey);
        keysToDelete.add(hllKey);
        keysToDelete.add(disableKey);
        
        // 批量删除键
        redisCache.deleteObject(keysToDelete);
        return true;
    }
}
