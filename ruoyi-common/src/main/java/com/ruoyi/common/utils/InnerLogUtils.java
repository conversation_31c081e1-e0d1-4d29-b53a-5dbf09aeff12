package com.ruoyi.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.local.RequestThreadLocal;
import com.ruoyi.common.enums.InnerLogType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 内部日志工具类
 *
 * <AUTHOR>
 * @date 2021/7/19
 */
public class InnerLogUtils {

    private static final Logger logger = LoggerFactory.getLogger(InnerLogUtils.class);

    /**
     * 构造日志内容
     */
    public static JSONObject buildJSON() {
        RequestThreadLocal local = RequestThreadLocal.get();

        JSONObject logJson = new JSONObject();
        logJson.put("slotId", local.getSlotId());
        logJson.put("appId", local.getAppId());
        logJson.put("srid", local.getSrid());
        logJson.put("deviceId", local.getDeviceId());
        logJson.put("consumerId", local.getConsumerId());
        logJson.put("ip", local.getIp());
        logJson.put("userAgent", local.getUserAgent());
        logJson.put("activityId", local.getActivityId());
        logJson.put("pluginId", local.getPluginId());
        logJson.put("advertFlag", local.getAdvertFlag());
        logJson.put("orderId", local.getOrderId());
        return logJson;
    }

    /**
     * 构造空日志内容
     */
    public static JSONObject buildEmptyJSON() {
        JSONObject logJson = new JSONObject();
        logJson.put("slotId", 0L);
        logJson.put("appId", 0L);
        logJson.put("srid", "");
        logJson.put("deviceId", "");
        logJson.put("activityId", 0L);
        return logJson;
    }

    /**
     * 打印日志
     */
    public static void log(InnerLogType logType, JSONObject json) {
        JSONObject jo = new JSONObject();
        jo.put("type", logType.getType());
        jo.put("time", DateUtils.getTime());
        jo.put("json", json);
        logger.info(jo.toJSONString());
    }
}
