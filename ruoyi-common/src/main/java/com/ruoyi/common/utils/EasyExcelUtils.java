package com.ruoyi.common.utils;

import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.config.RuoYiConfig;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * EasyExcel工具类
 *
 * <AUTHOR>
 * @date 2022/05/25
 */
public class EasyExcelUtils {

    /**
     * 导出空Excel
     *
     * @param sheetName 表格名称
     * @param clazz 数据类型
     * @return Excel文件名
     */
    public static <E> String exportEmptyExcel(String sheetName, Class<E> clazz) {
        return exportExcel(sheetName, Collections.emptyList(), clazz);
    }

    /**
     * 导出Excel
     *
     * @param sheetName 表格名称
     * @param data 导出数据
     * @param clazz 数据类型
     * @return Excel文件名
     */
    public static <E> String exportExcel(String sheetName, List<E> data, Class<E> clazz) {
        String fileName = UUID.randomUUID() + "_" + sheetName + ".xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        // 这里需要指定写用哪个class去写，然后写到第一个sheet，名字为模板，然后文件流会自动关闭
        EasyExcel.write(filePath, clazz).sheet(sheetName).doWrite(data);
        return fileName;
    }
}
