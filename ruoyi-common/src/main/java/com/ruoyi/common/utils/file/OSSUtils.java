package com.ruoyi.common.utils.file;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.ObjectMetadata;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.exception.file.FileNameLengthLimitExceededException;
import com.ruoyi.common.exception.file.FileSizeLimitExceededException;
import com.ruoyi.common.exception.file.InvalidExtensionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

/**
 * OSS上传工具类
 *
 * <AUTHOR>
 * @date 2021/7/19
 */
public class OSSUtils {
    private static final Logger logger = LoggerFactory.getLogger(OSSUtils.class);

    private static String endpoint = "http://oss-cn-hangzhou-internal.aliyuncs.com";
    private static String accessKeyId = "LTAI5tLGKudCmLbqTnSgQCKz";
    private static String accessKeySecret = "******************************";
    private static String bucketName = "nh-static";

    private static OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

    /**
     * 根据文件路径上传
     *
     * @param file 上传的文件
     * @return 文件名称
     * @throws IOException
     */
    public static String upload(MultipartFile file) throws IOException {
        try {
            String baseDir = RuoYiConfig.getUploadPath();
            return upload(baseDir, file, MimeTypeUtils.OSS_ALLOWED_EXTENSION, false);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 根据文件路径上传文档
     *
     * @param file 上传的文件
     * @return 文件名称
     * @throws IOException
     */
    public static String uploadDocument(MultipartFile file) throws IOException {
        try {
            String baseDir = RuoYiConfig.getUploadPath();
            return upload(baseDir, file, MimeTypeUtils.OSS_DOCUMENT_ALLOWED_EXTENSION, true);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 文件上传
     *
     * @param baseDir 相对应用的基目录
     * @param file 上传的文件
     * @param allowedExtension 上传文件类型
     * @param originName 是否保持原名
     * @return 返回上传成功的文件名
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws FileNameLengthLimitExceededException 文件名太长
     * @throws IOException 比如读写文件出错时
     * @throws InvalidExtensionException 文件校验异常
     */
    private static String upload(String baseDir, MultipartFile file, String[] allowedExtension, boolean originName)
            throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException,
            InvalidExtensionException {

        int fileNameLength = Objects.requireNonNull(file.getOriginalFilename()).length();
        if (fileNameLength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }

        // 检查是否为允许上传的类型
        FileUploadUtils.assertAllowed(file, allowedExtension);

        // 上传到本地
        String fileName = FileUploadUtils.extractFilename(file, originName);
        File desc = FileUploadUtils.getAbsoluteFile(baseDir, fileName);
        file.transferTo(desc);

        // 上传到OSS并删除本地文件
        try (InputStream content = new FileInputStream(desc)) {
            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentLength(desc.length());
            meta.setContentType(file.getContentType());
            // 上传Object.
            ossClient.putObject(bucketName, fileName, content, meta);
            // 删除本地文件
            if (desc.isFile() && desc.exists()) {
                boolean result = desc.delete();
                if (!result) {
                    logger.warn("上传OSS后本地文件删除失败, fileName={}", fileName);
                }
            }
            return fileName;
        } catch (OSSException | ClientException | IOException e) {
            logger.error(e.getMessage(), e);
        }

        return "";
    }

    /**
     * 上传本地到OSS
     */
    public static void upload(String dir, File file) {
        if (null == file || !file.exists()) {
            return;
        }

        try (InputStream content = new FileInputStream(file)) {
            String path = dir + "/" + file.getName();
            ossClient.putObject(bucketName, path, content);
        } catch (OSSException | ClientException | IOException e) {
            logger.error(e.getMessage(), e);
        }
    }
}
