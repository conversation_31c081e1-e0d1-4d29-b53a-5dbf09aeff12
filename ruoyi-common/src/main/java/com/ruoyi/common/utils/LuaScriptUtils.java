package com.ruoyi.common.utils;

import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scripting.support.ResourceScriptSource;

/**
 * Lua 脚本工具类
 * 用于按需加载指定 Lua 脚本，调用方需指定脚本名和返回类型。
 * 示例：LuaScriptUtil.getScript("UvCount", Long.class)
 */
public class LuaScriptUtils {

    private static final String LUA_SCRIPT_BASE_PATH = "lua/";

    /**
     * 按需获取 Lua 脚本对象
     *
     * @param scriptName 脚本名称（不包含路径、不带 .lua 后缀）
     * @param resultType 返回类型（如 Long.class, Boolean.class, String.class）
     * @return 带返回类型的 RedisScript 对象
     */
    public static <T> DefaultRedisScript<T> getScript(String scriptName, Class<T> resultType) {
        String path = LUA_SCRIPT_BASE_PATH + scriptName + ".lua";
        ClassPathResource resource = new ClassPathResource(path);

        if (!resource.exists()) {
            throw new IllegalArgumentException("Lua script not found: " + path);
        }

        DefaultRedisScript<T> script = new DefaultRedisScript<>();
        script.setScriptSource(new ResourceScriptSource(resource));
        script.setResultType(resultType);
        return script;
    }

    // 禁止实例化
    private LuaScriptUtils() {}
}

