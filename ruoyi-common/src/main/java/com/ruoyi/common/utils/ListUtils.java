package com.ruoyi.common.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 列表工具类
 *
 * <AUTHOR>
 * @date 2022/05/25
 */
public class ListUtils {

    /**
     * 列表转换
     */
    public static <T, R> List<R> mapToList(List<T> list, Function<T, R> mapper) {
        if (null == list) {
            return new ArrayList<>(0);
        }
        return list.stream().map(mapper).distinct().collect(Collectors.toList());
    }

    /**
     * 列表元素调整
     */
    public static List<Integer> offsetList(List<Integer> list, Integer offset) {
        if (null == list || null == offset) {
            return list;
        }
        return list.stream().map(ele -> ele + offset).collect(Collectors.toList());
    }

   /**
     * 转字符串列表
     */
    public static <T> List<String> toStringList(List<T> list) {
        if (null == list) {
            return new ArrayList<>(0);
        }
        return list.stream().map(Object::toString).collect(Collectors.toList());
    }
}
