package com.ruoyi.common.utils;

import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 整数列表互转工具
 * 基于位运算实现，整数转成二进制，获取1的位数集合
 * 例: 67(=01000011) <=> [1, 2, 7]
 *
 * <AUTHOR>
 * @date 2021/8/18
 */
public class IntListUtils {

    /**
     * 列表转数字
     *
     * @param maxSize 最大位数
     * @param types 设备定向类型列表
     * @return 设备定向类型整数
     */
    public static Integer convertToInteger(int maxSize, List<Integer> types) {
        if (CollectionUtils.isEmpty(types) || types.contains(0)) {
            return 0;
        }
        int result = 0;
        for (Integer type : types) {
            if (type > maxSize) {
                continue;
            }
            result |= 1 << (type - 1);
        }
        return result;
    }

    /**
     * 数字转列表
     *
     * @param maxSize 最大位数
     * @param type 设备定向类型整数
     * @return 设备定向类型列表
     */
    public static List<Integer> convertToList(int maxSize, Integer type) {
        if (null == type || 0 == type) {
            return Collections.emptyList();
        }
        List<Integer> types = new ArrayList<>();
        for (int i = 0; i < maxSize && type > 0; i++) {
            if ((type & 1) == 1) {
                types.add(i + 1);
            }
            type >>= 1;
        }
        return types;
    }

    /**
     * 判断选项是否被勾选
     *
     * @param maxSize 最大位数
     * @param selectedNumber 勾选整数
     * @param option 选项
     * @return 是否被勾选
     */
    public static boolean isSelected(int maxSize, Integer selectedNumber, Integer option) {
        if (null == selectedNumber || 0 == selectedNumber || null == option || 0 == option || option > maxSize) {
            return false;
        }
        return (selectedNumber & (1 << (option - 1))) > 0;
    }
}
