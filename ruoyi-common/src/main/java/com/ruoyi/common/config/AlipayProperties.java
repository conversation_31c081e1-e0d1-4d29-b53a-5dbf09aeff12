package com.ruoyi.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 支付宝落地页配置
 * <AUTHOR>
 * @date 2023/2/9 15:47
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "alipay")
public class AlipayProperties {

    private List<Config> configs;

    @Data
    public static class Config {
        private String appId;
        private String privateKey;
        private String alipayPublicKey;
        private String bizToken;
        private String principalTag;
    }


    
}
