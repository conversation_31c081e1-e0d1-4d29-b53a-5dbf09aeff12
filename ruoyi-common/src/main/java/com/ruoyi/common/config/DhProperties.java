package com.ruoyi.common.config;

import com.ruoyi.common.utils.StringUtils;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Objects;

/**
 * 灯火回传配置
 *
 * <AUTHOR>
 * @date 2024/1/9
 */
@Configuration
@ConfigurationProperties(prefix = "dh")
public class DhProperties {

    private static List<Config> configs;

    @Data
    public static class Config {
        private String principalId;
        private String appId;
        private String privateKey;
        private String alipayPublicKey;
        private String bizToken;
        private String principalTag;
        private String conversionType;
    }

    public static List<Config> getConfigs() {
        return configs;
    }

    public void setConfigs(List<Config> configs) {
        DhProperties.configs = configs;
    }

    public static Config getConfigByPrincipalId(String principalId) {
        if (StringUtils.isBlank(principalId)) {
            return configs.get(0);
        }
        for (Config config : configs) {
            if (Objects.equals(config.getPrincipalId(), principalId)) {
                return config;
            }
        }
        return configs.get(0);
    }
}
