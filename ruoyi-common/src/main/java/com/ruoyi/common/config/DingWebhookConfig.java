package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 钉钉机器人Webhook配置
 *
 * <AUTHOR>
 * @date 2022/05/30
 */
@Component
@ConfigurationProperties(prefix = "ding.webhook")
public class DingWebhookConfig {

    /**
     * 落地页通知
     */
    private static String lpNotice;

    /**
     * CRM业务通知
     */
    private static String crmBiz;

    /**
     * 系统告警
     */
    private static String systemAlert;

    /**
     * 数据告警
     */
    private static String dataAlert;

    /**
     * 运营指标提醒
     */
    private static String bizAlert;

    /**
     * OCPC提醒
     */
    private static String ocpcAlert;

    /**
     * 广告主余额提醒
     */
    private static String balanceAlert;

    /**
     * 巨量提醒
     */
    private static String oceanAlert;

    /**
     * 域名告警
     */
    private static String domainAlert;

    /**
     * 文章告警
     */
    private static String articleAlert;

    /**
     * 文章操作告警
     */
    private static String articleOperateAlert;

    public static String getLpNotice() {
        return lpNotice;
    }

    public void setLpNotice(String lpNotice) {
        DingWebhookConfig.lpNotice = lpNotice;
    }

    public static String getCrmBiz() {
        return crmBiz;
    }

    public void setCrmBiz(String crmBiz) {
        DingWebhookConfig.crmBiz = crmBiz;
    }

    public static String getSystemAlert() {
        return systemAlert;
    }

    public void setSystemAlert(String systemAlert) {
        DingWebhookConfig.systemAlert = systemAlert;
    }

    public static String getDataAlert() {
        return dataAlert;
    }

    public void setDataAlert(String dataAlert) {
        DingWebhookConfig.dataAlert = dataAlert;
    }

    public static String getBizAlert() {
        return bizAlert;
    }

    public void setBizAlert(String bizAlert) {
        DingWebhookConfig.bizAlert = bizAlert;
    }

    public static String getOcpcAlert() {
        return ocpcAlert;
    }

    public void setOcpcAlert(String ocpcAlert) {
        DingWebhookConfig.ocpcAlert = ocpcAlert;
    }

    public static String getBalanceAlert() {
        return balanceAlert;
    }

    public void setBalanceAlert(String balanceAlert) {
        DingWebhookConfig.balanceAlert = balanceAlert;
    }

    public static String getOceanAlert() {
        return oceanAlert;
    }

    public void setOceanAlert(String oceanAlert) {
        DingWebhookConfig.oceanAlert = oceanAlert;
    }

    public static String getDomainAlert() {
        return domainAlert;
    }

    public void setDomainAlert(String domainAlert) {
        DingWebhookConfig.domainAlert = domainAlert;
    }

    public static String getArticleAlert() {
        return articleAlert;
    }

    public void setArticleAlert(String articleAlert) {
        DingWebhookConfig.articleAlert = articleAlert;
    }

    public static String getArticleOperateAlert() {
        return articleOperateAlert;
    }

    public void setArticleOperateAlert(String articleOperateAlert) {
        DingWebhookConfig.articleOperateAlert = articleOperateAlert;
    }
}
