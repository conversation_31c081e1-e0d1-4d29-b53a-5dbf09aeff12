package com.ruoyi.common.config;

import com.ruoyi.common.core.redis.RedisAtomicClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

@Configuration
public class RedisAtomicClientConfig {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Bean
    public RedisAtomicClient redisAtomicClient() {
        return new RedisAtomicClient(stringRedisTemplate);
    }
}
