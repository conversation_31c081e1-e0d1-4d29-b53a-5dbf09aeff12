package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信支付配置
 *
 * <AUTHOR>
 * @date 2023/01/16
 */
@Component
@ConfigurationProperties(prefix = "wxpay")
public class WxPayConfig {

    /**
     * 公众账号ID
     */
    private static String appId;

    /**
     * 商户号
     */
    private static String mchId;

    /**
     * 支付密钥
     */
    private static String partnerKey;

    /**
     * 通知地址
     */
    private static String notifyUrl;

    /**
     * 公众号appId(JSAPI支付用)
     */
    private static String gzhAppId;

    /**
     * 公众号secret(JSAPI支付用)
     */
    private static String gzhSecret;

    public static String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        WxPayConfig.appId = appId;
    }

    public static String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        WxPayConfig.mchId = mchId;
    }

    public static String getPartnerKey() {
        return partnerKey;
    }

    public void setPartnerKey(String partnerKey) {
        WxPayConfig.partnerKey = partnerKey;
    }

    public static String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        WxPayConfig.notifyUrl = notifyUrl;
    }

    public static String getGzhAppId() {
        return gzhAppId;
    }

    public void setGzhAppId(String gzhAppId) {
        WxPayConfig.gzhAppId = gzhAppId;
    }

    public static String getGzhSecret() {
        return gzhSecret;
    }

    public void setGzhSecret(String gzhSecret) {
        WxPayConfig.gzhSecret = gzhSecret;
    }
}
