package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 丰巢配置属性
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "fc")
public class FcConfig {
    
    /** 应用Key */
    private String appKey;
    
    /** 应用密钥 */
    private String appSecret;
    
    /** 账户ID */
    private String accountId;
    
    /** 基础URL */
    private String baseUrl;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }
}
