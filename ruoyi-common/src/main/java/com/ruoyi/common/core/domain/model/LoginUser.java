package com.ruoyi.common.core.domain.model;

import com.ruoyi.common.enums.account.AdminTypeEnum;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;

/**
 * 登录用户身份权限
 *
 * <AUTHOR>
 */
@Data
public class LoginUser implements UserDetails {

    /**
     * 环境
     */
    private String env;

    /**
     * 过期时间
     */
    private Long time;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * oa账号用户ID，crm中使用请注意 ,不能删除改字段，该字段参与token生成，其他系统要使用到
     */
    @Deprecated
    private Long userId;

    /**
     * 广告crm账号id
     */
    private Long crmAccountId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String userStatus;

    /**
     * 主体类型
     */
    private Integer mainType;

    /**
     * 管理员类型
     *
     * @see com.ruoyi.common.enums.account.AdminTypeEnum
     */
    private Integer adminType;

    public LoginUser() {
    }

    public LoginUser(Set<String> permissions) {
        this.permissions = permissions;
    }

    public boolean isAdmin(){
        return Objects.equals(adminType, AdminTypeEnum.ADMIN.getType());
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @Override
    public String getPassword() {
        return this.password;
    }

    @Override
    public String getUsername() {
        return this.userName;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    public String getUserName() {
        return this.userName;
    }
}
