package com.ruoyi.common.core.domain;

import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.constant.HttpStatus;

import static com.ruoyi.common.constant.ErrorCode.ARGS;

/**
 * 构建请求返回数据对象工具类
 *
 * <AUTHOR>
 * @date 2021/9/16 4:04 下午
 */
public class ResultBuilder {

    /**
     * 默认错误码
     */
    private static final Integer DEFAULT_FAIL_CODE = HttpStatus.ERROR;

    /**
     * 默认成功码
     */
    private static final Integer DEFAULT_SUCCESS_CODE = HttpStatus.SUCCESS;

    private ResultBuilder() {
    }

    /**
     * 带自定义响应码的描述信息数据对象Result
     *
     * @param code 错误码
     * @param desc 失败描述
     * @return Result
     */
    public static <T> Result<T> fail(Integer code, String desc) {
        Result<T> result = new Result<>();
        result.setTimestamp(System.currentTimeMillis());
        result.setSuccess(false);
        result.setCode(code);
        result.setMsg(desc);
        return result;
    }

    /**
     * 使用默认错误码,描述错误信息(统一使用默认错误码)
     *
     * @param desc 失败描述
     * @return Result
     */
    public static <T> Result<T> fail(String desc) {
        return fail(DEFAULT_FAIL_CODE, desc);
    }

    /**
     * 使用默认错误码,描述错误信息(统一使用默认错误码)
     *
     * @param errorCode 错误信息
     * @return Result
     */
    public static <T> Result<T> fail(ErrorCode errorCode) {
        return fail(errorCode.getCode(), errorCode.getMsg());
    }

    /**
     * 参数错误
     * @return
     * @param <T>
     */
    public static <T> Result<T> paramFail() {
        return fail(ARGS);
    }

    /**
     * 带有数据的失败返回
     *
     * @param code 失败码
     * @param desc 失败信息
     * @param data 数据
     * @return Result
     */
    public static <T> Result<T> fail(Integer code, String desc, T data) {
        Result<T> result = new Result<>();
        result.setTimestamp(System.currentTimeMillis());
        result.setSuccess(false);
        result.setCode(code);
        result.setMsg(desc);
        result.setData(data);
        return result;
    }

    /**
     * 带有数据的失败返回,统一使用默认错误码
     *
     * @param desc 失败信息
     * @param data 数据
     * @return Result
     */
    public static <T> Result<T> fail(String desc, T data) {
        return fail(DEFAULT_FAIL_CODE, desc, data);
    }

    /**
     * 使用自定义成功码的成功数据对象构建
     *
     * @param code 成功码
     * @param data 数据
     * @return Result
     */
    public static <T> Result<T> success(Integer code, T data) {
        Result<T> result = new Result<>();
        result.setTimestamp(System.currentTimeMillis());
        result.setSuccess(true);
        result.setCode(code);
        result.setData(data);
        result.setMsg("OK");
        return result;
    }

    /**
     * 统一使用默认成功码的成功数据对象构建
     *
     * @param data 数据
     * @return Result
     */
    public static <T> Result<T> success(T data) {
        return success(DEFAULT_SUCCESS_CODE, data);
    }

    /**
     * 成功,不返回任何数据,统一使用默认成功码
     *
     * @return Result
     */
    public static <T> Result<T> success() {
        return success(DEFAULT_SUCCESS_CODE, null);
    }

    /**
     * 根据影响行数判断是否成功
     *
     * @param rows 影响行数
     * @return Result
     */
    public static <T> Result<T> result(int rows) {
        return rows > 0 ? success() : fail("");
    }

    /**
     * 根据结果判断是否成功
     *
     * @param result 结果
     * @return Result
     */
    public static <T> Result<T> result(boolean result) {
        return result ? success() : fail("");
    }
}
