package com.ruoyi.common.core.redis;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

/**
 * spring redis 工具类
 *
 * <AUTHOR>
 **/
@SuppressWarnings(value = { "unchecked", "rawtypes" })
@Component
public class RedisCache {

    @Autowired
    public RedisTemplate redisTemplate;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 判断 Key 是否存在
     *
     * @param key 缓存的键值
     * @return Key 是否存在
     */
    public Boolean hasKey(final String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 获取缓存过期时间
     *
     * @param key 缓存的键值
     * @return 过期时间(秒)
     */
    public Long getExpire(final String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value)
    {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     * @param timeout 时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit)
    {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 缓存基本的对象如果不存在
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     * @param timeout 时间
     * @param timeUnit 时间颗粒度
     * @return 是否设置成功
     */
    public <T> Boolean setCacheObjectIfAbsent(final String key, final T value, final Integer timeout, final TimeUnit timeUnit)
    {
        return redisTemplate.opsForValue().setIfAbsent(key, value, timeout, timeUnit);
    }

    /**
     * 设置有效时间
     *
     * @param key Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout)
    {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key Redis键
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit)
    {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key)
    {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key)
    {
        return redisTemplate.delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public long deleteObject(final Collection collection)
    {
        return redisTemplate.delete(collection);
    }

    public <T> long rightPush(final String key, final T data) {
        Long count = redisTemplate.opsForList().rightPush(key, data);
        return count == null ? 0 : count;
    }

    public void leftTrim(final String key, long start, long end) {
        redisTemplate.opsForList().trim(key, start, end);
    }

    /**
     * 缓存List数据
     *
     * @param key 缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList)
    {
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key)
    {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * 缓存Set
     *
     * @param key 缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet)
    {
        BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext())
        {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key)
    {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 判断是否在缓存的set中
     *
     * @param key
     * @param value
     * @return
     */
    public <T> Boolean cacheSetContains(final String key, final T value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }

    /**
     * 添加到缓存的set中
     *
     * @param key
     * @param value
     * @return
     */
    public <T> Long addCacheSet(final String key, final T value) {
        return redisTemplate.opsForSet().add(key, value);
    }

    /**
     * 缓存的Set中移除对象
     *
     * @param key
     * @param value
     * @return
     */
    public <T> Long deleteCacheSet(final String key, final T value) {
        return redisTemplate.opsForSet().remove(key, value);
    }

    /**
     * 计算Set数量
     */
    public Long countCacheSet(final String key) {
        return redisTemplate.opsForSet().size(key);
    }

    /**
     * 添加到缓存的ZSet中
     *
     * @param key
     * @param value
     * @return
     */
    public <T> Boolean addCacheZSet(final String key, final T value, double score) {
        return redisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * 计算ZSet数量
     */
    public Long countCacheZSet(final String key) {
        return redisTemplate.opsForZSet().zCard(key);
    }

    /**
     * 缓存的ZSet中移除对象
     *
     * @param key
     * @param value
     * @return
     */
    public <T> Long deleteCacheZSet(final String key, final T value) {
        return redisTemplate.opsForZSet().remove(key, value);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap)
    {
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key)
    {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value)
    {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * Hash中删除数据
     *
     * @param key Redis键
     * @param hKey Hash键列表
     * @return 被成功删除字段的数量，不包括被忽略的字段
     */
    public Long delCacheMapValue(final String key, final Object... hKey) {
        return redisTemplate.opsForHash().delete(key, hKey);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey)
    {
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * Hash中的数据自增
     *
     * @param key Redis键
     * @param hKey Hash键
     * @param delta 增加的步长
     * @return Hash中字段的值
     */
    public <T> Long incrCacheMapValue(final String key, final String hKey, final long delta) {
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.increment(key, hKey, delta);
    }

    /**
     * 原子递增
     * @param key
     * @return
     */
    public Long incrCacheValue(final String key) {
        return redisTemplate.opsForValue().increment(key, 1);

    }


    /**
     * 获取多个Hash中的数据
     *
     * @param key Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<String> hKeys)
    {
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 根据Score获取ZSet的数据
     *
     * @param key Redis键
     * @param min Score区间下限
     * @param max Score区间上限
     * @return ZSet数据集合
     */
    public <T> Set<T> getZSetRangeByScore(final String key, double min, double max) {
        return redisTemplate.opsForZSet().rangeByScore(key, min, max);
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern)
    {
        return redisTemplate.keys(pattern);
    }

    /**
     * 执行lua脚本
     * @param var1
     * @param var2
     * @param var3
     * @return
     * @param <T>
     */
    public <T> T executeLuaScript(RedisScript<T> var1, List var2, Object... var3) {
        return (T) stringRedisTemplate.execute(var1, var2, var3);
    }

    /**
     * 获取HyperLogLog数量
     * @param key
     * @return
     */
    public Long countHyperLogLog(String key) {
        return redisTemplate.opsForHyperLogLog().size(key);
    }
}
