package com.ruoyi.common.core.local;

import com.alibaba.fastjson.JSONObject;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public class RequestThreadLocal {

    private RequestThreadLocal() {
    }

    private static ThreadLocal<RequestThreadLocal> local = new ThreadLocal<>();

    public static RequestThreadLocal get() {
        RequestThreadLocal rl = local.get();
        if (rl == null) {
            rl = new RequestThreadLocal();
            local.set(rl);
        }
        return rl;
    }

    public static void clear() {
        local.set(null);
    }

    public static void remove() {
        local.remove();
    }

    private HttpServletRequest request;

    private HttpServletResponse response;

    /** 媒体账号ID */
    private Long accountId;

    /** 广告位ID */
    private Long slotId;

    /** 应用标识 */
    private String appKey;

    /** 媒体ID */
    private Long appId;

    /** 设备号 */
    private String deviceId;

    /** 用户ID */
    private Long consumerId;

    /** UV分流哈希值 */
    private Integer shuntHash;

    /** 广告位请求ID */
    private String srid;

    /** 活动ID */
    private Long activityId;

    /** 插件ID */
    private Long pluginId;

    /** IP */
    private String ip;

    /** User-Agent */
    private String userAgent;

    /** 域名配置 */
    private JSONObject domainConfig;

    /** 广告标识:1.诺禾广告,2.分流出外部广告,3.降级出外部广告 */
    private Integer advertFlag;

    /** 订单号 */
    private String orderId;

    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    public void setResponse(HttpServletResponse response) {
        this.response = response;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getSlotId() {
        return slotId;
    }

    public void setSlotId(Long slotId) {
        this.slotId = slotId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Long getConsumerId() {
        return consumerId;
    }

    public void setConsumerId(Long consumerId) {
        this.consumerId = consumerId;
    }

    public Integer getShuntHash() {
        return shuntHash;
    }

    public void setShuntHash(Integer shuntHash) {
        this.shuntHash = shuntHash;
    }

    public String getSrid() {
        return srid;
    }

    public void setSrid(String srid) {
        this.srid = srid;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public JSONObject getDomainConfig() {
        return domainConfig;
    }

    public void setDomainConfig(JSONObject domainConfig) {
        this.domainConfig = domainConfig;
    }

    public Integer getAdvertFlag() {
        return advertFlag;
    }

    public void setAdvertFlag(Integer advertFlag) {
        this.advertFlag = advertFlag;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Long getPluginId() {
        return pluginId;
    }

    public void setPluginId(Long pluginId) {
        this.pluginId = pluginId;
    }
}
