package com.ruoyi.common.core.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * 统一请求返回数据格式
 * 
 * <AUTHOR>
 */
@Data
public class Result<T> implements Serializable {
    private static final long serialVersionUID = -4927862224066177225L;

    /** 是否成功 */
    private Boolean success;

    /** 状态码 */
    public Integer code;

    /** 错误信息 */
    public String msg;

    /** 数据对象 */
    public T data;

    /** 服务器当前时间戳 */
    private Long timestamp;
}
