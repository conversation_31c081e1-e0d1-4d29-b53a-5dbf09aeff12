package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.ruoyi.common.enums.InnerLogType.*;

/**
 * 数据统计维度枚举
 *
 * <AUTHOR>
 * @date 2021/9/29
 */
@Getter
@AllArgsConstructor
public enum DataDimensionEnum {

    REDIS_DATA("redisData", "Redis缓存数据", Arrays.asList(ADVERT_BILLING, LANDPAGE_CLICK, CONVERT_EVENT)),
    ADVERT_DAY("advertDay", "广告维度日数据", Arrays.asList(ADVERT_LAUNCH, ADVERT_EXPOSURE, ADVERT_CLICK, LANDPAGE_EXPOSURE, LANDPAGE_CLICK, ADVERT_BILLING, BLIND_BOX_POPUP_CLICK)),
    ADVERT_HOUR("advertHour", "广告维度时段数据", Arrays.asList(ADVERT_LAUNCH, ADVERT_EXPOSURE, ADVERT_CLICK, LANDPAGE_EXPOSURE, LANDPAGE_CLICK, ADVERT_BILLING, BLIND_BOX_POPUP_CLICK)),
    ADVERT_APP_DAY("advertAppDay", "广告媒体维度日数据", Arrays.asList(ADVERT_LAUNCH, ADVERT_EXPOSURE, ADVERT_CLICK, LANDPAGE_EXPOSURE, LANDPAGE_CLICK, ADVERT_BILLING, BLIND_BOX_POPUP_CLICK)),
    ADVERT_SLOT_DAY("advertSlotDay", "广告广告位维度日数据", Arrays.asList(ADVERT_LAUNCH, ADVERT_EXPOSURE, ADVERT_CLICK, LANDPAGE_EXPOSURE, LANDPAGE_CLICK, ADVERT_BILLING, BLIND_BOX_POPUP_CLICK)),
    MATERIAL_SLOT_DAY("materialSlotDay", "素材广告位维度日数据", Arrays.asList(ADVERT_EXPOSURE, ADVERT_CLICK, ADVERT_BILLING)),
    ADVERT_DAY_BUDGET("advertDayBudget", "广告维度日预算", Collections.singletonList(ADVERT_LAUNCH)),
    LANDPAGE_DAY("landpageDay", "落地页维度日数据", Arrays.asList(ADVERT_LAUNCH, ADVERT_EXPOSURE, ADVERT_CLICK, LANDPAGE_EXPOSURE, LANDPAGE_CLICK, BLIND_BOX_JOIN, BLIND_BOX_POPUP_EXPOSURE, BLIND_BOX_POPUP_CLICK, CONVERT_EVENT)),
    ADVERT_CHARGE_HOUR("advertChargeHour", "广告计费类型维度时段数据", Arrays.asList(ADVERT_LAUNCH, ADVERT_EXPOSURE, ADVERT_CLICK, ADVERT_BILLING, LANDPAGE_EXPOSURE, LANDPAGE_CLICK, CONVERT_EVENT)),
    ADVERT_QUARTER("advertQuarter", "广告维度时刻数据", Arrays.asList(ADVERT_LAUNCH, ADVERT_EXPOSURE, ADVERT_CLICK, ADVERT_BILLING, LANDPAGE_EXPOSURE, LANDPAGE_CLICK, CONVERT_EVENT)),
    MOBILE_HAP_DATA("mobileHapData", "设备维度快应用数据", Arrays.asList(ADVERT_LAUNCH, ADVERT_EXPOSURE, ADVERT_CLICK, QUICKAPP_LAUNCH, LANDPAGE_EXPOSURE, LANDPAGE_CLICK)),
    ADVERT_SLOT_CONV_DAY("advertSlotConvDay", "广告广告位维度后端转化日数据", Collections.singletonList(CONVERT_EVENT)),
    ADVERT_SLOT_CONV_HOUR("advertSlotConvHour", "广告广告位维度后端转化时段数据", Collections.singletonList(CONVERT_EVENT)),
    ORIENT_CONSUME_DAY("orientConsumeDay", "配置消耗日数据", Collections.singletonList(ADVERT_BILLING)),
    ORIENT_HOUR("orientHour", "配置时段数据", Arrays.asList(ADVERT_LAUNCH, ADVERT_EXPOSURE, ADVERT_CLICK, LANDPAGE_EXPOSURE, LANDPAGE_CLICK, ADVERT_BILLING, BLIND_BOX_POPUP_CLICK, CONVERT_EVENT)),
    SLOT_ACTIVITY_HOUR("slotActivityHour", "广告位活动维度时段数据", Arrays.asList(ADVERT_BILLING, LANDPAGE_EXPOSURE, LANDPAGE_CLICK)),
    FC_LINK_DAY("fcLinkDay", "丰巢链接维度日数据", Collections.singletonList(FC_LINK_REQUEST)),
    ;

    private final String key;
    private final String desc;
    private final List<InnerLogType> innerLogTypes;

    public static List<DataDimensionEnum> getByInnerLogType(InnerLogType type) {
        if (null == type) {
            return Collections.emptyList();
        }

        List<DataDimensionEnum> list = new ArrayList<>();
        for (DataDimensionEnum e : values()) {
            if (e.innerLogTypes.contains(type)) {
                list.add(e);
            }
        }
        return list;
    }
}
