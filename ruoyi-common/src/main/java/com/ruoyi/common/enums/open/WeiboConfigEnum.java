package com.ruoyi.common.enums.open;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 微博配置枚举
 *
 * <AUTHOR>
 * @date 2022/07/20
 */
@Getter
@AllArgsConstructor
public enum WeiboConfigEnum {

    DEFAULT(853285L, "202207198700928100"),
    S2(853286L, "20220719230463100"),
    S3(853318L, "202208117850864100"),
    S4(853320L, "202208110746547100"),
    S5(853323L, "202208175936138100"),
    S6(853324L, "20220817540480100"),
    ;

    private final Long slotId;
    private final String clientId;

    public static String getClientId(Long slotId) {
        for (WeiboConfigEnum config : values()) {
            if (Objects.equals(config.getSlotId(), slotId)) {
                return config.getClientId();
            }
        }
        return DEFAULT.getClientId();
    }
}
