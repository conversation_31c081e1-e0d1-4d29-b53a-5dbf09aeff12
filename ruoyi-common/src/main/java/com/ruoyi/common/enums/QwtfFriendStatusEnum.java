package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 企微囤粉加好友状态
 *
 * <AUTHOR>
 * @date 2023/9/22
 */
@Getter
@AllArgsConstructor
public enum QwtfFriendStatusEnum {

    NOT(1, "未加好友"),
    ADDED(3, "已加好友"),
    ;

    private final int status;
    private final String desc;

    public static String getDescByStatus(Integer status) {
        for (QwtfFriendStatusEnum statusEnum : QwtfFriendStatusEnum.values()) {
            if (Objects.equals(statusEnum.getStatus(), status)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
