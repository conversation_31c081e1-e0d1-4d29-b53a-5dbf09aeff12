package com.ruoyi.common.enums.redpacket;

/**
 * <AUTHOR>
 * @date 2022/5/31 11:50 上午
 */
public enum TransferStatusEnum {
    NO_TRANSFER(0,"未打款"),
    TRANSFER(1,"已打款"),
    INVALID_ALIPAY(2,"无效信息")
    ;
    private Integer status;
    private String desc;

    TransferStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
