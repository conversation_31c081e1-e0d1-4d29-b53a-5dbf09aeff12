package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 是否默认枚举
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Getter
@AllArgsConstructor
public enum IsDefaultEnum {

    IS_DEFAULT(1, "默认"),
    NOT_DEFAULT(0, "非默认");

    private final int type;
    private final String desc;

    public static boolean isDefault(Integer type) {
        return Objects.equals(type, IS_DEFAULT.type);
    }
}
