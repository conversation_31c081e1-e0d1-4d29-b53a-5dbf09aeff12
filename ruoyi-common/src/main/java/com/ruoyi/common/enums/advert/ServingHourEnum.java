package com.ruoyi.common.enums.advert;

import com.ruoyi.common.utils.IntListUtils;
import com.ruoyi.common.utils.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 投放时段枚举
 *
 * <AUTHOR>
 * @date 2022/09/05
 */
@Getter
@AllArgsConstructor
public enum ServingHourEnum {

    UNLIMITED(0, "不限时段"),
    ALL(0xFFFFFF, "所有时段"),
    ;

    private final int type;
    private final String desc;

    private static final int SIZE = 24;

    /**
     * 校验是否是有效的投放时段列表
     *
     * @param hours 投放时段列表
     * @return 是否有效
     */
    public static boolean validate(List<Integer> hours) {
        return CollectionUtils.isEmpty(hours) || hours.stream().noneMatch(hour -> hour < 0 || hour > 23);
    }

    /**
     * 校验是否是有效的投放时段
     *
     * @param servingHour 投放时段
     * @return 是否有效
     */
    public static boolean validate(Integer servingHour) {
        return null == servingHour || (servingHour >= 0 && servingHour <= 0xFFFFFF);
    }

    /**
     * 是否不限投放时段
     *
     * @param servingHour 投放时段
     * @return 是否不限
     */
    public static boolean isUnlimited(Integer servingHour) {
        return null == servingHour || servingHour == 0 || servingHour == 0xFFFFFF;
    }

    /**
     * 列表转数字
     *
     * @param hours 投放时段列表
     * @return 投放时段整数
     */
    public static Integer convertToInteger(List<Integer> hours) {
        return IntListUtils.convertToInteger(SIZE, ListUtils.offsetList(hours, 1));
    }

    /**
     * 数字转列表
     *
     * @param hour 投放时段整数
     * @return 投放时段列表
     */
    public static List<Integer> convertToList(Integer hour) {
        return ListUtils.offsetList(IntListUtils.convertToList(SIZE, hour), -1);
    }

    /**
     * 判断当前时段是否在投放时段内
     *
     * @param servingHour 投放时段
     * @param hour 当前时段
     * @return 当前时段是否在投放时段内
     */
    public static boolean contains(Integer servingHour, Integer hour) {
        if (isUnlimited(servingHour)) {
            return true;
        }
        if (hour < 0 || hour > 23) {
            return false;
        }
        return (servingHour & (1 << hour)) >= 1;
    }
}
