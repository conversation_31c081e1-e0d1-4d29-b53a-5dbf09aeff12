package com.ruoyi.common.enums.open;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 七猫配置枚举
 *
 * <AUTHOR>
 * @date 2022/08/10
 */
@Getter
@AllArgsConstructor
public enum QimaoConfigEnum {

    DEFAULT(1L, "AGs8T..s8AVZgIz1hQ-Vjpti30KjP", "59393"),
    ;

    private final Long slotId;
    private final String a;
    private final String gl;

    public static QimaoConfigEnum getBySlotId(Long slotId) {
        for (QimaoConfigEnum config : values()) {
            if (Objects.equals(config.getSlotId(), slotId)) {
                return config;
            }
        }
        return DEFAULT;
    }
}
