package com.ruoyi.common.enums.callback;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 上报处理器类型枚举
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
@Getter
@AllArgsConstructor
public enum CallbackProcessorTypeEnum {

    QTT("趣头条"),
    SWT("三维推"),
    MGTV("芒果TV"),
    JJXQ("集集星球"),
    MI("小米"),
    QIMAO("七猫"),
    YOUKU("优酷"),
    SIGMOB("Sigmob"),
    WEIBO("微博"),
    SOHU("搜狐"),
    OPPO("OPPO"),
    VIVO("VIVO"),
    XIMALAYA("喜马拉雅"),
    IQIYI("爱奇艺"),
    BAIDU("百度"),
    KUAISHOU("快手"),
    MEITUAN("美团"),
    OCEANENGINE("巨量"),
    QQ("广点通"),
    DENGHUO("灯火"),
    ;

    private final String name;
}
