package com.ruoyi.common.enums.landpage;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 导入类型枚举
 *
 * <AUTHOR>
 * @date 2023/4/7
 */
@Getter
@AllArgsConstructor
public enum ImportTypeEnum {

    API(1,"API回传"),
    MANUAL(2,"手动导入"),
    ;

    private final int type;
    private final String desc;

    public static String getDescByType(Integer type) {
        for (ImportTypeEnum typeEnum : ImportTypeEnum.values()) {
            if (Objects.equals(typeEnum.getType(), type)) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }
}
