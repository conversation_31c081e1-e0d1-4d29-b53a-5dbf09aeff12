package com.ruoyi.common.enums.advert;

import com.ruoyi.common.utils.IntListUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 设备定向类型
 *
 * <AUTHOR>
 * @date 2022/03/28
 */
@Getter
@AllArgsConstructor
public enum DeviceTargetType {

    UNLIMITED(0, "不限"),
    HUAWEI(1, "华为"),
    VIVO(2, "VIVO"),
    OPPO(3, "OPPO"),
    MI(4, "小米"),
    ;

    private final int type;
    private final String brand;

    // 限制可以配置的最大数量
    private static final int size = Math.max(8, DeviceTargetType.values().length);

    /**
     * 列表转数字
     *
     * @param types 设备定向类型列表
     * @return 设备定向类型整数
     */
    public static Integer convertToInteger(List<Integer> types) {
        return IntListUtils.convertToInteger(size, types);
    }

    /**
     * 数字转列表
     *
     * @param type 设备定向类型整数
     * @return 设备定向类型列表
     */
    public static List<Integer> convertToList(Integer type) {
        return IntListUtils.convertToList(size, type);
    }

    /**
     * 是否包含选项
     *
     * @param type 设备定向类型整数
     * @param option 设备定向选项
     * @return 是否包含
     */
    public static boolean contains(Integer type, Integer option) {
        return IntListUtils.isSelected(size, type, option);
    }
}
