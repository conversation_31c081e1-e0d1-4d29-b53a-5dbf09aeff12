package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 预付款主体枚举
 */
@Getter
@AllArgsConstructor
public enum PrepaySubjectEnum {


    ELINKS_SMART(1, "易联智能"),
    ELINKS_SHORT_MOVIE(2, "易联短剧"),
    NUOHE(3, "诺禾"),
    WANSHI(4, "玩时"),
    YUETOU(5, "悦投"),
    ZHONGQING(6, "中情"),
    ELINKS_SIGHT(7, "易联视界"),
    OTHER(8, "其他"),
    ;
    Integer type;
    String name;

    public static String getNameByType(Integer type) {
        if (Objects.isNull(type) ) {
            return null;
        }
        for (PrepaySubjectEnum value : PrepaySubjectEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getName();
            }
        }
        return null;
    }

    public static String getNamesByTypeList(List<Integer> typeList) {
        if (CollectionUtils.isEmpty(typeList) ) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (Integer type : typeList) {
            String name = getNameByType(type);
            if (Objects.nonNull(name)) {
                sb.append(name).append(",");
            }
        }
        return sb.length() > 0 ? sb.substring(0, sb.length() - 1) : "";
    }


}
