package com.ruoyi.common.enums.landpage;

/**
 * 导出状态枚举
 * <AUTHOR>
 * @date 2023/4/6 16:06
 */
public enum ExportStatusEnum {
    NOT_EXPORT(1,"未导出"),
    EXPORT(2,"已导出"),
    ;
    private int status;
    private String desc;

    ExportStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
