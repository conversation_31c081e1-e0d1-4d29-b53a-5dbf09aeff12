package com.ruoyi.common.enums.advertiser;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 广告主资质审核状态
 *
 * <AUTHOR>
 * @date 2023/03/29
 */
@Getter
@AllArgsConstructor
public enum QualificationAuditStatus {

    READY(0, "待审核"),
    APPROVE(1, "审核通过"),
    REFUSE(2, "审核拒绝");

    private final Integer status;
    private final String desc;

    public static String getDescByStatus(Integer status) {
        for (QualificationAuditStatus typeEnum : QualificationAuditStatus.values()) {
            if (Objects.equals(status, typeEnum.getStatus())) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }

    /**
     * 是否是待审核状态
     *
     * @param status 审核状态
     * @return 是否是待审核状态
     */
    public static boolean isReadyToAudit(Integer status) {
        return Objects.equals(status, READY.status);
    }

    /**
     * 是否是审核通过状态
     *
     * @param status 审核状态
     * @return 是否是审核通过状态
     */
    public static boolean isAuditApprove(Integer status) {
        return Objects.equals(status, APPROVE.status);
    }

    /**
     * 是否是审核拒绝状态
     *
     * @param status 审核状态
     * @return 是否是审核拒绝状态
     */
    public static boolean isAuditRefuse(Integer status) {
        return Objects.equals(status, REFUSE.status);
    }
}
