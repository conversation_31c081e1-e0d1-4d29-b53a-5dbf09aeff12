package com.ruoyi.common.enums.slot;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 广告位切量计划状态枚举
 *
 * <AUTHOR>
 * @date 2022/04/22
 */
@Getter
@AllArgsConstructor
public enum SlotShuntStatusEnum {

    READY(0, "未开启"),
    EXECUTE(1, "执行中"),
    TERMINATED(2, "已结束"),
    DELETED(3, "已删除");

    private final int status;
    private final String desc;

    /**
     * 判断当前状态是否能修改参数
     *
     * @param status 状态
     * @return 是否能修改参数
     */
    public static boolean canModify(Integer status) {
        return Objects.equals(READY.status, status);
    }

    /**
     * 判断当前状态是否能执行
     *
     * @param status 状态
     * @return 是否能执行
     */
    public static boolean canExecute(Integer status) {
        return Objects.equals(READY.status, status);
    }

    /**
     * 判断当前状态是否能完成
     *
     * @param status 状态
     * @return 是否能完成
     */
    public static boolean canFinish(Integer status) {
        return Objects.equals(EXECUTE.status, status);
    }

    /**
     * 判断当前状态是否能停止
     *
     * @param status 状态
     * @return 是否能停止
     */
    public static boolean canCancel(Integer status) {
        return Objects.equals(EXECUTE.status, status);
    }

    /**
     * 判断当前状态是否能被删除
     *
     * @param status 状态
     * @return 是否能删除
     */
    public static boolean canDelete(Integer status) {
        return Objects.equals(READY.status, status);
    }

    /**
     * 根据状态获取描述
     *
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (SlotShuntStatusEnum statusEnum : SlotShuntStatusEnum.values()) {
            if (Objects.equals(status, statusEnum.getStatus())) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
