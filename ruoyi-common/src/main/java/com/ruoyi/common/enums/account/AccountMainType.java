package com.ruoyi.common.enums.account;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 账号主体类型
 *
 * <AUTHOR>
 * @date 2021/7/8
 */
@Getter
@AllArgsConstructor
public enum AccountMainType {

    PUBLISHER(1, "流量主"),
    ADVERTISER(2, "广告主"),
    CRM(3, "CRM用户"),
    AGENT(4, "广告主代理商");

    private final int type;
    private final String desc;

    /**
     * 判断是否是流量主
     */
    public static boolean isPublisher(Integer type) {
        return Objects.equals(PUBLISHER.type, type);
    }

    /**
     * 判断是否是广告主
     */
    public static boolean isAdvertiser(Integer type) {
        return Objects.equals(ADVERTISER.type, type);
    }

    /**
     * 判断是否是CRM用户
     */
    public static boolean isCrmUser(Integer type) {
        return Objects.equals(CRM.type, type);
    }

    /**
     * 判断是否是代理商
     */
    public static boolean isAgent(Integer type) {
        return Objects.equals(AGENT.type, type);
    }
}
