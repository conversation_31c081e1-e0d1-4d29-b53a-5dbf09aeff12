package com.ruoyi.common.enums.advert;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 计费类型
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Getter
@AllArgsConstructor
public enum ChargeTypeEnum {

    CPC(1, "CPC"),
    OCPC(2, "OCPC");

    private final int type;
    private final String desc;

    public static boolean isCPC(Integer type) {
        return Objects.equals(CPC.type, type);
    }

    public static boolean isOCPC(Integer type) {
        return Objects.equals(OCPC.type, type);
    }
}
