package com.ruoyi.common.enums.account;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 账号数据权限类型
 *
 * <AUTHOR>
 * @date 2022/06/22
 */
@Getter
@AllArgsConstructor
public enum DataPermissionType {

    FULL(1, "所有权限"),
    PARTIAL(2, "部分权限"),
    NONE(3, "无权限"),
    ;

    private final int type;
    private final String desc;

    /**
     * 是否有所有权限
     *
     * @param type 权限类型
     * @return true.是,false.否
     */
    public static boolean hasFullPermission(Integer type) {
        return Objects.equals(FULL.type, type);
    }

    /**
     * 是否只有部分权限
     *
     * @param type 权限类型
     * @return true.是,false.否
     */
    public static boolean hasPartialPermission(Integer type) {
        return Objects.equals(PARTIAL.type, type);
    }
}
