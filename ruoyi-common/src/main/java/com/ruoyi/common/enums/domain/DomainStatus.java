package com.ruoyi.common.enums.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 域名状态枚举
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Getter
@AllArgsConstructor
public enum DomainStatus {

    NORMAL(0, "正常"),
    DISABLE(1, "不可用");

    private final int status;
    private final String desc;

    /**
     * 判断是否正常
     *
     * @param status 域名状态
     * @return 是否正常
     */
    public static boolean isNormal(Integer status) {
        return Objects.equals(status, NORMAL.status);
    }
}
