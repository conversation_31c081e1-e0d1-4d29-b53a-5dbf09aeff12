package com.ruoyi.common.enums.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 可用状态枚举
 *
 * <AUTHOR>
 * @date 2022/05/24
 */
@Getter
@AllArgsConstructor
public enum EnableStatusEnum {

    DISABLE(0, "不可用"),
    ENABLE(1, "可用");

    private final int status;
    private final String desc;

    /**
     * 判断是否是可用的状态
     *
     * @param status 可用状态
     * @return 是否可用
     */
    public static boolean isEnable(Integer status) {
        return Objects.equals(status, ENABLE.status);
    }
}
