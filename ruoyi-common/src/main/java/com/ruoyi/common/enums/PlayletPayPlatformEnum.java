package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 支付通道枚举
 *
 * <AUTHOR>
 * @date 2023/9/4
 */
@Getter
@AllArgsConstructor
public enum PlayletPayPlatformEnum {

    WECHAT(1, "微信"),
    ALIPAY(2, "支付宝"),
    ;

    private final Integer type;
    private final String desc;

    public static String getDescByType(Integer type) {
        for (PlayletPayPlatformEnum e : PlayletPayPlatformEnum.values()) {
            if (Objects.equals(e.getType(), type)) {
                return e.getDesc();
            }
        }
        return "";
    }
}
