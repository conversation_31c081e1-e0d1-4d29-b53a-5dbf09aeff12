package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 落地页支持的宏替换枚举
 * 格式:__PARAM__
 *
 * <AUTHOR>
 * @date 2021/8/18
 */
@Getter
@AllArgsConstructor
public enum LandpageMacroEnum {

    ORDER_ID("__N_OID__", "orderId", "订单号"),
    CONSUMER_ID("__N_CID__","consumerId", "用户ID"),
    DEVICE_ID("__N_DEVICE__","deviceId", "设备ID"),
    ;

    private final String code;
    private final String key;
    private final String desc;
}
