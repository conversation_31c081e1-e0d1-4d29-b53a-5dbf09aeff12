package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 弹层类型枚举
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Getter
@AllArgsConstructor
public enum LayerType {

    NORMAL(1, "普通弹层"),
    INNOVATE(2, "创新弹层"),
    ;

    private final int type;
    private final String desc;

    /**
     * 判断是否是创新弹层类型
     *
     * @param type 类型
     * @return true.是,false.否
     */
    public static boolean isInnovateLayer(Integer type) {
        return Objects.equals(type, INNOVATE.type);
    }
}
