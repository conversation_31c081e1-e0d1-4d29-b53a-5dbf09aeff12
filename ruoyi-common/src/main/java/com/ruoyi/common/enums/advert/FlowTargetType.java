package com.ruoyi.common.enums.advert;

import com.ruoyi.common.utils.IntListUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 流量定向类型
 *
 * <AUTHOR>
 * @date 2022/08/31
 */
@Getter
@AllArgsConstructor
public enum FlowTargetType {

    UNLIMITED(0, "不限"),
    WECHAT(1, "微信"),
    ALIPAY(2, "支付宝"),
    OTHER(8, "其他"),
    ;

    private final int type;
    private final String desc;

    // 限制可以配置的最大数量
    private static final int size = Math.max(8, FlowTargetType.values().length);

    /**
     * 列表转数字
     *
     * @param types 流量定向类型列表
     * @return 流量定向类型整数
     */
    public static Integer convertToInteger(List<Integer> types) {
        return IntListUtils.convertToInteger(size, types);
    }

    /**
     * 数字转列表
     *
     * @param type 流量定向类型整数
     * @return 流量定向类型列表
     */
    public static List<Integer> convertToList(Integer type) {
        return IntListUtils.convertToList(size, type);
    }

    /**
     * 是否包含选项
     *
     * @param type 流量定向类型整数
     * @param option 流量定向选项
     * @return 是否包含
     */
    public static boolean contains(Integer type, Integer option) {
        return IntListUtils.isSelected(size, type, option);
    }
}
