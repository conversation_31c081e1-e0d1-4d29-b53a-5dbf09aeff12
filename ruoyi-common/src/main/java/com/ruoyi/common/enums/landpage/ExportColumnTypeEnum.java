package com.ruoyi.common.enums.landpage;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 导出字段枚举
 *
 * <AUTHOR>
 * @date 2023/4/6 16:06
 */
@Getter
@AllArgsConstructor
public enum ExportColumnTypeEnum {

    ALL(0,"全部字段"),
    AI(1,"AI外呼字段"),
    ;

    private final int type;
    private final String desc;

    public static boolean isAllColumn(Integer type) {
        return Objects.equals(ALL.type, type);
    }
}
