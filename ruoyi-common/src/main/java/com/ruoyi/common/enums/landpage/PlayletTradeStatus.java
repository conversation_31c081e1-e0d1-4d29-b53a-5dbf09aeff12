package com.ruoyi.common.enums.landpage;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 短剧交易状态枚举
 *
 * <AUTHOR>
 * @date 2023/8/10
 */
@Getter
@AllArgsConstructor
public enum PlayletTradeStatus {

    READY(0, "待支付"),
    PAID(1, "已支付"),
    REFUND(2, "已退款"),
    ;

    private final int status;
    private final String desc;

    /**
     * 判断是否待支付
     *
     * @param status 状态
     * @return 是否待支付
     */
    public static boolean isReady(Integer status) {
        return Objects.equals(status, READY.status);
    }

    /**
     * 判断是否支付成功
     *
     * @param status 状态
     * @return 是否支付成功
     */
    public static boolean isPaid(Integer status) {
        return Objects.equals(status, PAID.status);
    }
}
