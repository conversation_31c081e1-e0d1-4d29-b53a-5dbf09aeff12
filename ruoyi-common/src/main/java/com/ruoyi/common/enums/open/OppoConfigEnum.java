package com.ruoyi.common.enums.open;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * oppo配置枚举
 *
 * <AUTHOR>
 * @date 2022/09/27
 */
@Getter
@AllArgsConstructor
public enum OppoConfigEnum {

    DEFAULT(853899L, "1000260386", "7a9ca16da6e748df9fbaf72b897a8d14", "d4a65d9946be40159e925df5ad214865"),
    S2(2L, "1000260385", "dd807e153c4d45e1a7670dcb5be68cb0", "73709694ce524705b64848f2530b7c44"),
    S3(3L, "1000260383", "2f805a6f5e2b4213b059af4228f2e240", "30cf63b8c72849b799311d2d030d2f54"),
    S4(4L, "1000260382", "cae63452b0514f80827f426607e4dc18", "9da41e8aa1494ab1a3627146bd1fccd4"),
    S5(5L, "1000260381", "8b26ff189fb9487a9e37dea575d7da79", "d614739fe8ed4b9997ac3e44879637e0"),
    S6(6L, "1000260380", "04553aab5f934b3da40047ce39afbd8f", "9284ef6c7dd840129f5e88895945e722"),
    S7(7L, "1000260379", "8769b4b3fec34a578c5f3dae17992cbb", "0004ed7ee7144b72a07c360bb889c53d"),
    S8(8L, "1000260378", "463d1d92df574212bc6b53258132c81f", "d9ca1735a1a449bcbaaf505fdc129dcb"),
    S9(9L, "1000260377", "925dd174fb244c23b5856a9edaf6ec8d", "476b532a04d3433b8435d79bc937aa46"),
    S10(10L, "1000260375", "d056a71200574ee4a13841eebb8796f5", "0656b84f0d794a5c80de8069313a86b3"),
    S11(11L, "1000260374", "6d9e01adc05e49698a33e69a1d5f44a9", "95a94719750349fb8ece2196db88e4cb"),
    S12(12L, "1000260373", "a86a2387b350438f9c9f756390f99e8a", "1d34e6e5f68d4451847d2d5c0607969d"),
    S13(13L, "1000260369", "71f3789ea199460c8e95c58db4cde3bb", "266dd9c6b03b4bc9b470c30161f069f8"),
    S14(14L, "1000260364", "c1bc78eeba9c4103a2073af594a7214a", "2a07123dda5245e0b68fbc64104c93d5"),
    S15(15L, "1000260363", "2d058e7b39a14455b67f9a960c65f237", "830c72e23cb2430e93e943e1719e8960"),
    S16(16L, "1000260032", "18b7b3694cca4f35bb89ce11f433b7ae", "c493e96884524aaf8b0c1132f23183da"),
    S17(17L, "1000259280", "5441c4e07bdb40bfacb23344f81af312", "612513d78654463199968f9d41cad604"),
    S18(18L, "1000259278", "6ed97a9b78d24ac08728014484dfe89e", "7fb51c09b95342e98ca57ad1c062a9ec"),
    S19(19L, "1000259275", "5568396e878d497499003dca0dcaff76", "d46b6405006047009bc5dd3bf3d5fea6"),
    S20(20L, "1000259272", "7f5a4d3453de4c318ec53937c492b7a4", "3786afb0db5447b49299fc4c991f543f"),
    S21(21L, "1000262591", "bae956ad240240beaf321893169636ad", "d996dfaef7c94f54903eb8b219449486"),
    S22(22L, "1000262590", "727c948625344a339c59375f19e50298", "d75821c9101b45eb9436e33611b690f0"),
    S23(23L, "1000262588", "cb40e6cbaa304a659946b69c830f6154", "9d9b647a23984852974bd07479142367"),
    S24(24L, "1000262587", "a635ca4581974bc19b806e6a42023db5", "d5a03fa900f74a5cb1e23bdc60909391"),
    S25(25L, "1000262586", "50a066d6bfb04240a3f294b6702f6b3d", "d02b635cd4a24d038a6e1ab053e77473"),
    S26(26L, "1000262584", "c899cd875c434b3ba2738a8397d89542", "823fecc846094701b3a3ff555cf14817"),
    S27(27L, "1000262580", "ca42220dc20b43b4948909fac1fbe72b", "c127fae6983448b3bfaa0bb1b6abcaaa"),
    S28(28L, "1000262577", "fcbcb4f0a6864a51a40cd065f90d59c3", "1423e5ec64d14a90bf655456583fb142"),
    S29(29L, "1000262576", "197ed6cce4f4431d8ea213bc09a8494b", "ff8a2890935541c5b5ce107b45fd0570"),
    S30(30L, "1000262575", "495eed6626814e7ca5c6aed32cbf9efa", "004b46df271d4d368ab8abaf9873b3ad"),
    ;

    private final Long slotId;
    private final String ownerId;
    private final String apiId;
    private final String apiKey;

    public static OppoConfigEnum getBySlotId(Long slotId) {
        for (OppoConfigEnum config : values()) {
            if (Objects.equals(config.getSlotId(), slotId)) {
                return config;
            }
        }
        return DEFAULT;
    }

    public static OppoConfigEnum getByOwnId(String ownerId) {
        for (OppoConfigEnum config : values()) {
            if (Objects.equals(config.getOwnerId(), ownerId)) {
                return config;
            }
        }
        return DEFAULT;
    }

    public static String getOwnerIdBySlotId(Long slotId) {
        return getBySlotId(slotId).getOwnerId();
    }
}
