package com.ruoyi.common.enums.advert;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 考核指标枚举
 *
 * <AUTHOR>
 * @date 2021/8/11
 */
@Getter
@AllArgsConstructor
public enum AssessTypeEnum {

    OTHER(0, "其他"),
    CONVERSION(1, "落地页转化"),
    RECEIVE(2, "领取"),
    ACTIVE(3, "激活"),
    REGISTER(4, "注册"),
    PAY_APPLY(5, "提交支付"),
    PAY_SUCCESS(6, "支付成功");

    private final int type;
    private final String desc;

    public static JSONArray toJSONArray() {
        return Arrays.stream(values()).map(e -> {
            JSONObject jo = new JSONObject();
            jo.put("type", e.type);
            jo.put("desc", e.desc);
            return jo;
        }).collect(Collectors.toCollection(JSONArray::new));
    }
}
