package com.ruoyi.common.enums.advert;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 落地页类型
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
@Getter
@AllArgsConstructor
public enum LandpageTypeEnum {

    DEFAULT(1, "默认"),
    CUSTOM(2, "自定义");

    private final int type;
    private final String desc;

    public static boolean isDefault(Integer type) {
        return Objects.equals(DEFAULT.type, type);
    }

    public static boolean isCustom(Integer type) {
        return Objects.equals(CUSTOM.type, type);
    }
}
