package com.ruoyi.common.enums.contract;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;

import java.util.Date;
import java.util.Objects;

/**
 * 合同状态
 * <AUTHOR>
 * @date 2022/11/4 10:52 上午
 */
public enum ContractStatusEnum {
    COOPERATING(1,"合作中"),
    ENDING(2,"即将到期"),
    EXPIRE(3,"过期");
    private Integer status;
    private String desc;

    ContractStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据结束时间结算合同状态
     * @param endDate
     * @return
     */
    public static Integer getContractStatusByDate(Date endDate){
        if(Objects.isNull(endDate)){
            return null;
        }
        Date today = new Date();

        if(today.after(endDate)){
            return EXPIRE.status;
        }
        if(DateUtil.between(today, endDate, DateUnit.DAY) < 30){
            return ENDING.status;
        }
        return COOPERATING.status;
    }
}
