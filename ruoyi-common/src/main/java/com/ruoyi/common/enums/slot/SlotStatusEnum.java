package com.ruoyi.common.enums.slot;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 广告位状态枚举
 *
 * <AUTHOR>
 * @date 2021/7/15
 */
@Getter
@AllArgsConstructor
public enum SlotStatusEnum {

    OPEN(0, "开启"),
    CLOSE(1, "关闭");

    private final int status;
    private final String desc;

    /**
     * 广告位是否开启
     *
     * @param status 广告位状态
     * @return 是否开启
     */
    public static boolean isSlotOpen(Integer status) {
        return Objects.equals(status, OPEN.status);
    }
}
