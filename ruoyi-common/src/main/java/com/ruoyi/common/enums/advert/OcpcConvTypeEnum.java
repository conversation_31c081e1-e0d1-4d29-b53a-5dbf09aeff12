package com.ruoyi.common.enums.advert;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * OCPC转化类型
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Getter
@AllArgsConstructor
public enum OcpcConvTypeEnum {

    OTHER(0, "其他"),
    CONVERSION(1, "落地页转化"),
    PAY(2, "支付");

    private final int type;
    private final String desc;

    public static boolean isConversion(Integer type) {
        return Objects.equals(CONVERSION.type, type);
    }

    public static boolean isPay(Integer type) {
        return Objects.equals(PAY.type, type);
    }
}
