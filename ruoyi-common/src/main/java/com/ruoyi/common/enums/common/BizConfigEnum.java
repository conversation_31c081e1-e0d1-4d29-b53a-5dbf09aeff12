package com.ruoyi.common.enums.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 业务配置枚举
 *
 * <AUTHOR>
 * @date 2022/03/16
 */
@Getter
@AllArgsConstructor
public enum BizConfigEnum {

    DEFAULT_SLOT_URL("slot.st.url", "广告位默认链接(勿动)", ""),
    DEFAULT_ACT_URL("activity.act.url", "活动默认链接(勿动)", ""),
    DEFAULT_DOMAIN_LANDPAGE("default.domain.landpage", "落地页默认域名(勿动)", ""),
    DEFAULT_DOMAIN_EMPTYPAGE("default.domain.emptypage", "空白页默认链接(勿动)", ""),
    DEFAULT_DOMAIN_ARTICLE("default.domain.article", "文章聚合链接域名(勿动)", ""),
    DEFAULT_URL_ARTICLE_RET("default.url.article.ret", "文章返回拦截页链接(勿动)", ""),
    DEFAULT_SHORT_URL("default.short.url", "短链默认链接(勿动)", ""),
    DEFAULT_SHORT_URL_BATCH("default.short.url.batch", "短链批量默认链接(勿动)", "批量生成短链使用的域名"),
    DEFAULT_FC_URL("default.domain.fc", "丰巢阅读链接域名(勿动)", ""),
    FORM_RED_PACKET_AMOUNT("form.redpacket.amount", "表单用户领红包金额(元)", ""),
    FORM_AREA_LIMIT("form.area.limit", "地域表单分配上限", "设置地域表单每小时分配上限值，达到这个上限该地域产生的表单不再分配给这个广告主"),
    IDCARD_AUDIT_RATIO("idcard.audit.ratio", "身份证校验接口灰度", "使用新的身份证实名认证接口的比例[0,100]"),
    TAKE_THRESHOLD("take.threshold", "广告主领取监控阈值", ""),
    ARTICLE_CHECK_THRESHOLD("article.check.threshold", "文章到达率监控配置", "timeThreshold: 监测间隔(分钟), pvThreshold: 监测增量阈值, arriveThreshold: 监测到达率阈值"),
    ;

    private final String key;
    private final String desc;
    private final String placeholder;

    public static BizConfigEnum getByKey(String key) {
        for (BizConfigEnum e : BizConfigEnum.values()) {
            if (Objects.equals(key, e.getKey())) {
                return e;
            }
        }
        return null;
    }
}