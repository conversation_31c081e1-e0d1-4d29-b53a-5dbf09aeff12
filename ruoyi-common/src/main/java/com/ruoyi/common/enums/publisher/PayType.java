package com.ruoyi.common.enums.publisher;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 媒体付款类型
 *
 * <AUTHOR>
 * @date 2022/07/29
 */
@Getter
@AllArgsConstructor
public enum PayType {

    PREPAY(1, "预付款媒体"),
    POSTPAID(0, "后付款媒体");

    private final int type;
    private final String desc;

    /**
     * 是否是预付款媒体
     *
     * @param type 付款类型
     * @return 是否是预付款媒体
     */
    public static boolean isPrepay(Integer type) {
        return Objects.equals(PREPAY.type, type);
    }
}
