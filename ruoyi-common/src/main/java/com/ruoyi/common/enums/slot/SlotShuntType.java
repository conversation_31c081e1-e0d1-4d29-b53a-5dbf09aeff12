package com.ruoyi.common.enums.slot;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 广告位切量类型
 *
 * <AUTHOR>
 * @date 2022/04/25
 */
@Getter
@AllArgsConstructor
public enum SlotShuntType {

    PV(1, "PV"),
    UV(2, "UV");

    private final int type;
    private final String desc;

    /**
     * 是否PV分流
     *
     * @param type 切量类型
     * @return 是否PV分流
     */
    public static boolean isPvShunt(Integer type) {
        return Objects.equals(type, PV.type);
    }

    /**
     * 是否UV分流
     *
     * @param type 切量类型
     * @return 是否UV分流
     */
    public static boolean isUvShunt(Integer type) {
        return Objects.equals(type, UV.type);
    }

    /**
     * 根据类型获取描述
     *
     * @param type 切量类型
     * @return 描述
     */
    public static String getDescByType(Integer type) {
        for (SlotShuntType typeEnum : SlotShuntType.values()) {
            if (Objects.equals(type, typeEnum.getType())) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }

    /**
     * 转成切量类型列表
     *
     * @return 切量类型列表
     */
    public static List<Integer> toList() {
        return Arrays.asList(PV.type, UV.type);
    }
}
