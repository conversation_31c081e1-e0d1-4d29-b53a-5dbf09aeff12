package com.ruoyi.common.enums.landpage;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 运营商联合会员产品类型
 *
 * <AUTHOR>
 * @date 2023/11/22
 */
@Getter
@AllArgsConstructor
public enum IspVipProductTypeEnum {

    XINDING(1,"欣鼎"),
    QIAOSI(2,"巧思"),
    WANGQIU(3,"望秋"),
    GUIHE(4,"桂郃"),
    TAOTAOHUI(5,"淘淘汇"),
    MINDING(6,"闵鼎"),
    SHENGJIASHENG(7,"盛嘉胜(25选1)"),
    MIJIE(9,"米节"),
    CHUANGWO_YD(10,"创蜗(移动)"),
    CHUANGWO_LT(11,"创蜗(联通)"),
    SHENGJIASHENG_25PICK2(12,"盛嘉胜(25选2)")
    ;

    private final int type;
    private final String desc;

    public static String getDescByType(Integer type) {
        for (IspVipProductTypeEnum typeEnum : IspVipProductTypeEnum.values()) {
            if (Objects.equals(typeEnum.getType(), type)) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }
}