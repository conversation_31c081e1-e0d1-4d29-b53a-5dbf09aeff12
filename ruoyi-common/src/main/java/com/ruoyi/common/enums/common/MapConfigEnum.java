package com.ruoyi.common.enums.common;

import com.ruoyi.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.function.Predicate;

/**
 * 映射配置枚举
 *
 * <AUTHOR>
 * @date 2022/03/16
 */
@Getter
@AllArgsConstructor
public enum MapConfigEnum {

    ADVERTISER_ORDER_STATUS("advertiser.order.status", "映射配置-后端转化类型", "", StringUtils::isNumeric, StringUtils::isNotBlank),
    ADVERTISER_CONV_LIMIT_MAP("advertiser.conv.limit.map", "映射配置-广告主转化上限", "限制广告主每日转化数量，达到上限自动关闭广告。格式:广告主ID-转化数，逗号分隔", StringUtils::isNumeric, StringUtils::isNumeric),
    ADVERT_OCPC_CONV_MAP("advert.ocpc.conv.map", "映射配置-广告开启OCPC转化数", "设置广告开启OCPC的最低转化数。格式:广告ID-转化数，逗号分隔", StringUtils::isNumeric, StringUtils::isNumeric),
    FORM_AREA_DAY_LIMIT_MAP("form.area.day.limit.map", "映射配置-表单日维度地域分配上限", "针对某个广告主配置，限制每天每个地域分配的表单数。配置格式:广告主ID-表单数上限", StringUtils::isNumeric, StringUtils::isNumeric),
    ADVERTISER_CONV_MAP("advertiser.conv.type.map", "映射配置-广告主落地页转化类型","指定广告主某个后端转化类型同步到落地页转化数据。格式：广告主ID-后端转化类型", StringUtils::isNumeric, StringUtils::isNumeric),
    SLOT_KS_COST_MAP("slot.ks.cost.map", "映射配置-广告位固定收益上报的价格","设置广告位固定收益上报价格，达标才会回传转化数据。建议上报价格与外部设置的目标成本一致。配置规则：广告位ID-固定收益的价格", StringUtils::isNumeric, StringUtils::isNotBlank),
    SLOT_ARTICLE_PLUGIN_MAP("slot.article.plugin.map", "映射配置-文章插件配置","配置格式：广告位ID-返回拦截次数", StringUtils::isNumeric, StringUtils::isNumeric),
    ARTICLE_OPEN_LINK("link.article.open.map", "文章聚合链接白名单-闪动","说明：链接ID-加密Key", StringUtils::isNumeric, StringUtils::isNotBlank),
    ;

    private final String key;
    private final String desc;
    private final String placeholder;
    private final Predicate<? super String> keyPredicate;
    private final Predicate<? super String> valuePredicate;

    public static MapConfigEnum getByKey(String key) {
        for (MapConfigEnum e : MapConfigEnum.values()) {
            if (Objects.equals(key, e.getKey())) {
                return e;
            }
        }
        return null;
    }
}
