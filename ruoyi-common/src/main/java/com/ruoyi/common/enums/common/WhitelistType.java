package com.ruoyi.common.enums.common;

import com.ruoyi.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.function.Predicate;

/**
 * 白名单业务类型
 *
 * <AUTHOR>
 * @date 2021/9/9
 */
@Getter
@AllArgsConstructor
public enum WhitelistType {

    ADJUST_DATA_SLOT("whitelist.adjustdata.slot", "广告位白名单-调整广告位数据","", Long.class, StringUtils::isNumeric),
    MONITOR_HOUR_DATA_SLOT("whitelist.monitor.hour.data.slot", "广告位白名单-同时段数据监控","", Long.class, StringUtils::isNumeric),
    ADVERT_EXPOSURE_ONCE_SLOT("whitelist.advert.exposure.once.slot", "广告位白名单-广告配置仅曝光一次","", Long.class, StringUtils::isNumeric),
    OFFLINE_DATA_ADVERTISER("whitelist.offline.data.advertiser", "广告主白名单-离线数据同步", "", Long.class, StringUtils::isNumeric),
    ADVERT_SWITCH_ADVERTISER("whitelist.advert.switch.advertiser", "广告主白名单-账户开关", "", Long.class, StringUtils::isNumeric),
    DATA_HIDE_ADVERTISER("whitelist.data.hide.advertiser", "广告主白名单-数据隐藏", "", Long.class, StringUtils::isNumeric),
    BAIJIU_PHONE_ADVERTISER("whitelist.baijiu.phone.advertiser", "广告主白名单-白酒表单手机号去重", "", Long.class, StringUtils::isNumeric),
    ARTICLE_REFRESH_ADVERTISER("whitelist.article.refresh.advertiser", "广告主白名单-查看阅读量", "", Long.class, StringUtils::isNumeric),
    FANLI_CHANNEL("whitelist.fanli.channel", "返利落地页替换渠道","", String.class, StringUtils::isNotBlank),
    APP_SLOT_EXPOSURE("whitelist.app.slot.exposure", "媒体白名单-广告曝光数据回传","", Long.class, StringUtils::isNumeric),
    BAIJIU_JIZHUN("whitelist.baijiu.jz", "广告白名单-白酒上报极准","", Long.class, StringUtils::isNumeric),
    ADVERT_NOT_MQ("whitelist.not.mq.advert", "广告白名单-不使用消息队列消费","", Long.class, StringUtils::isNumeric),
    MAU_REPEAT_FILTER_ADVERT("whitelist.mau.repeat.filter.advert", "广告白名单-MAU去重","", Long.class, StringUtils::isNumeric),
    IFR_URL("whitelist.ifr.url", "微信防封链接","", String.class, StringUtils::isNotBlank),
    QWTF_USERID("whitelist.qwtf.userid", "企微囤粉-企业成员userid","", String.class, StringUtils::isNotBlank),
    NOT_REPLACE_DOMAIN("whitelist.domain.not.replace", "域名白名单-落地页域名不替换","", String.class, StringUtils::isNotBlank),
    ARTICLE_SCHEME_LINK("whitelist.link.article.scheme", "文章聚合链接白名单-文章UrlScheme","", Long.class, StringUtils::isNumeric),
    FC_ARTICLE_CHECK_TRAFFICMASTER("whitelist.fc.article.check.trafficMaster", "流量主白名单-丰巢文章审核", "", Long.class, StringUtils::isNumeric),
    ;

    private final String key;
    private final String desc;
    private final String placeholder;
    private final Class<?> clazz;
    private final Predicate<? super String> predicate;

    public static WhitelistType getByKey(String key) {
        for (WhitelistType type : WhitelistType.values()) {
            if (Objects.equals(key, type.getKey())) {
                return type;
            }
        }
        return null;
    }
}
