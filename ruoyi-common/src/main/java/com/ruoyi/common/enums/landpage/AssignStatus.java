package com.ruoyi.common.enums.landpage;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 落地页表单分配状态
 *
 * <AUTHOR>
 * @date 2022/02/09
 */
@Getter
@AllArgsConstructor
public enum AssignStatus {

    UNASSIGNED_VALID(1, "未分配（有效订单）"),
    UNASSIGNED_INVALID(2, "未分配（无效订单）"),
    ASSIGNED_SUCCESS(3, "已分配（成功）"),
    ASSIGNED_FAILED(4, "已分配（失败）");

    private final int status;
    private final String desc;
}
