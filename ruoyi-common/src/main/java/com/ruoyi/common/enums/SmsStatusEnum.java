package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 短信发送状态枚举
 *
 * <AUTHOR>
 * @date 2022/12/7 13:45
 */
@Getter
@AllArgsConstructor
public enum SmsStatusEnum {

    SENDING(0,"发送中"),
    SUCCESS(1,"成功"),
    FAIL(2,"失败"),
    @Deprecated
    UNKNOW(3,"未知")
    ;

    private final int status;
    private final String desc;

    /**
     * 短信是否发送成功
     *
     * @param status 短信发送状态
     * @return true.是,false.否
     */
    public static boolean isSuccess(Integer status) {
        return Objects.equals(status, SUCCESS.status);
    }

    /**
     * 短信是否发送失败
     *
     * @param status 短信发送状态
     * @return true.是,false.否
     */
    public static boolean isFail(Integer status) {
        return Objects.equals(status, FAIL.status);
    }

    /**
     * 短信是否发送中
     *
     * @param status 短信发送状态
     * @return true.是,false.否
     */
    public static boolean isSending(Integer status) {
        return Objects.equals(status, SENDING.status);
    }
}
