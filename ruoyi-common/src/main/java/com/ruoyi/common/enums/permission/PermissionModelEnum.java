package com.ruoyi.common.enums.permission;

/**
 * 权限模块枚举
 * <AUTHOR>
 * @date 2022/6/24 11:53 上午
 */
public enum PermissionModelEnum {
    SSP("ssp","ssp系统权限"),
    DSP("dsp","dsp系统权限"),
    ;
    private String model;
    private String desc;

    PermissionModelEnum(String model, String desc) {
        this.model = model;
        this.desc = desc;
    }

    public String getModel() {
        return model;
    }

    public String getDesc() {
        return desc;
    }
}
