package com.ruoyi.common.enums.publisher;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

import static com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum.CHECK_REFUSE;
import static com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum.CHECK_SUCCESS;
import static com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum.WAIT_CHECK;

/**
 * 提现复合审核状态
 *
 * <AUTHOR>
 * @date 2022/11/25
 */
@Getter
@AllArgsConstructor
public enum WithdrawComplexAuditStatus {

    LEADER_READY(0, "待部门负责人审核"),
    APPROVE(1, "审核通过"),
    REFUSE(2, "审核拒绝"),
    CEO_READY(3, "待业务负责人审核"),
    FINANCE_READY(4, "待财务审核"),
    LEADER_REFUSE(5, "部门负责人审核拒绝"),
    CEO_REFUSE(6, "业务负责人审核拒绝"),
    FINANCE_REFUSE(7, "财务审核拒绝");

    private final Integer status;
    private final String desc;

    public static String getDescByStatus(Integer status) {
        for (WithdrawComplexAuditStatus e : WithdrawComplexAuditStatus.values()) {
            if (e.getStatus().equals(status)) {
                return e.getDesc();
            }
        }
        return "";
    }

    public static Integer getByCheckStatus(Integer status) {
        if (Objects.equals(status, WAIT_CHECK.getStatus())) {
            return LEADER_READY.getStatus();
        }
        if (Objects.equals(status, CHECK_SUCCESS.getStatus())) {
            return APPROVE.getStatus();
        }
        if (Objects.equals(status, CHECK_REFUSE.getStatus())) {
            return REFUSE.getStatus();
        }
        return LEADER_READY.getStatus();
    }

    public static boolean isReadyToAudit(Integer status) {
        return Objects.equals(status, LEADER_READY.status) || Objects.equals(status, CEO_READY.status) || Objects.equals(status, FINANCE_READY.status);
    }

    public static WithdrawCheckStatusEnum getLeaderAuditStatus(Integer status) {
        if (Objects.equals(status, LEADER_REFUSE.status) || Objects.equals(status, REFUSE.status)) {
            return CHECK_REFUSE;
        }
        if (Objects.equals(status, APPROVE.status)
                || Objects.equals(status, CEO_READY.status) || Objects.equals(status, CEO_REFUSE.status)
                || Objects.equals(status, FINANCE_READY.status) || Objects.equals(status, FINANCE_REFUSE.status)) {
            return CHECK_SUCCESS;
        }
        return WAIT_CHECK;
    }

    public static WithdrawCheckStatusEnum getCeoAuditStatus(Integer status) {
        if (Objects.equals(status, CEO_REFUSE.status) || Objects.equals(status, REFUSE.status)) {
            return CHECK_REFUSE;
        }
        if (Objects.equals(status, APPROVE.status)
                || Objects.equals(status, FINANCE_READY.status) || Objects.equals(status, FINANCE_REFUSE.status)) {
            return CHECK_SUCCESS;
        }
        return WAIT_CHECK;
    }

    public static WithdrawCheckStatusEnum getFinanceAuditStatus(Integer status) {
        if (Objects.equals(status, FINANCE_REFUSE.status) || Objects.equals(status, REFUSE.status)) {
            return CHECK_REFUSE;
        }
        if (Objects.equals(status, APPROVE.status)) {
            return CHECK_SUCCESS;
        }
        return WAIT_CHECK;
    }
}
