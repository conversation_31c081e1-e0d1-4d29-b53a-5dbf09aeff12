package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 业务日志类型枚举
 *
 * <AUTHOR>
 * @date 2021/7/21
 */
@Getter
@AllArgsConstructor
public enum InnerLogType {

    MATERIAL_EXPOSURE(1, "素材曝光"),
    MATERIAL_CLICK(2, "素材点击"),
    SLOT_REQUEST(3, "广告位访问"),
    ACTIVITY_REQUEST(4, "活动访问"),
    ACTIVITY_MAIN_LOAD(5, "活动首屏加载完成"),
    ACTIVITY_TOTAL_LOAD(6, "活动加载完成"),
    ACTIVITY_JOIN(7, "活动参与"),
    ADVERT_REQUEST(8, "券请求"),
    ADVERT_LAUNCH(9, "发券"),
    LAYER_EXPOSURE(10, "弹层曝光"),
    LAYER_CLICK(11, "弹层点击"),
    ADVERT_EXPOSURE(12, "券曝光"),
    ADVERT_CLICK(13, "券点击"),
    ADVERT_BILLING(14, "券计费"),
    LANDPAGE_EXPOSURE(15, "落地页曝光"),
    LANDPAGE_CLICK(16, "落地页转化"),
//    EXTERNAL_LANDPAGE_EVENT(17, "外部落地页事件"),
    CONVERT_EVENT(18, "广告后端转化"),
    PLUGIN_EXPOSURE(19, "活动返回挽留触发"),
    LANDPAGE_LAYER_EXPOSURE(20, "落地页弹层触发"),
    LANDPAGE_RET_EXPOSURE(21, "落地页返回挽留触发"),
    PRIZE_ICON_CLICK(22, "奖品ICON点击"),
    KEFU_ICON_CLICK(23, "客服ICON点击"),
    RULE_ICON_CLICK(24, "规则ICON点击"),
    ADVERTISER_CONSUME(25, "广告主消耗"),
    MINIPROGRAM_PLUGIN_EXPOSURE(26, "小程序插件曝光"),
    MINIPROGRAM_PLUGIN_CLICK(27, "小程序插件点击"),
    MINIPROGRAM_PLUGIN_POPUP_EXPOSURE(28, "小程序插件授权弹窗曝光"),
    MINIPROGRAM_PLUGIN_POPUP_CONFIRM(29, "小程序插件授权弹窗允许"),
    MINIPROGRAM_PLUGIN_POPUP_CANCEL(30, "小程序插件授权弹窗取消"),
    QUICKAPP_LAUNCH(31, "快应用启动"),
    ADVERTISER_BILLING_CONSUME(32, "广告主结算消费"),
    BLIND_BOX_JOIN(33, "盲盒落地页参与"),
    BLIND_BOX_POPUP_EXPOSURE(34, "盲盒落地页弹层曝光"),
    BLIND_BOX_POPUP_CLICK(35, "盲盒落地页弹层点击/领取"),
    QUICKAPP_FAIL_TO_BROWSER(36, "快应用启动失败跳转浏览器"),
    SHORT_URL_REQUEST(37, "短链访问"),
    TRANSFER_PAGE_EXPOSURE(38, "中转页曝光"),
    TRANSFER_PAGE_CLICK(39, "中转页点击"),
    AUTHORIZATION_POPUP_EXPOSURE(40, "授权弹窗曝光"),
    AUTHORIZATION_POPUP_CLICK(41, "授权弹窗点击"),
    QRCODE_EXPOSURE(42, "二维码弹层曝光"),
    QRCODE_CLICK(43, "二维码弹层长按"),
    KEY_ACTION_1(51, "关键行为1"),
    KEY_ACTION_2(52, "关键行为2"),
    KEY_ACTION_3(53, "关键行为3"),
    KEY_ACTION_4(54, "关键行为4"),
    KEY_ACTION_5(55, "关键行为5"),
    KEY_ACTION_6(56, "关键行为6"),
    KEY_ACTION_7(57, "关键行为7"),
    KEY_ACTION_8(58, "关键行为8"),
    KEY_ACTION_9(59, "关键行为9"),
    KEY_ACTION_10(60, "关键行为10"),
    FC_LINK_REQUEST(61, "丰巢链接访问")
    ;

    private final int type;
    private final String desc;

    public static InnerLogType getByType(Integer type) {
        if (null == type) {
            return null;
        }
        for (InnerLogType typeEnum : values()) {
            if (Objects.equals(typeEnum.type, type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
