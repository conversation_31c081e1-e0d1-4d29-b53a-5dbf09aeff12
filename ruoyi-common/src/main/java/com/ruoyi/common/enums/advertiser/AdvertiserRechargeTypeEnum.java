package com.ruoyi.common.enums.advertiser;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 广告主充值类型
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@Getter
@AllArgsConstructor
public enum AdvertiserRechargeTypeEnum {

    ONLINE(1, "线上充值"),
    REBATE(2, "返货充值"),
    TRANSFER_ONLINE(3, "代理商划账-线上充值"),
    TRANSFER_REBATE(4, "代理商划账-返货充值"),
    ;

    private final int type;
    private final String desc;

    public static String getDescByType(Integer type) {
        for (AdvertiserRechargeTypeEnum typeEnum : AdvertiserRechargeTypeEnum.values()) {
            if (Objects.equals(type, typeEnum.getType())) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }

    public static boolean isRecharge(Integer type) {
        return Objects.equals(type, ONLINE.type) || Objects.equals(type, REBATE.type);
    }

    public static boolean isTransfer(Integer type) {
        return Objects.equals(type, TRANSFER_ONLINE.type) || Objects.equals(type, TRANSFER_REBATE.type);
    }
}
