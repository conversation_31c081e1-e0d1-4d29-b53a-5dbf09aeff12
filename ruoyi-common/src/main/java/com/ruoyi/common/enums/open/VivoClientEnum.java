package com.ruoyi.common.enums.open;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * Vivo应用枚举
 *
 * <AUTHOR>
 * @date 2024/01/12
 */
@Getter
@AllArgsConstructor
public enum VivoClientEnum {

    DEFAULT("20240112003", "FB3B95A39F63173B9464C8955DFE9194E4A552FE311B258382E7C664C111376B"), // hznhjj
    ;

    private final String clientId;
    private final String clientSecret;

    public static String getClientSecretByClientId(String clientId) {
        for (VivoClientEnum config : values()) {
            if (Objects.equals(config.getClientId(), clientId)) {
                return config.getClientSecret();
            }
        }
        return DEFAULT.getClientSecret();
    }
}
