package com.ruoyi.common.enums.landpage;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 落地页皮肤类型
 *
 * <AUTHOR>
 * @date 2022/9/19 2:22 下午
 */
@Getter
@AllArgsConstructor
public enum LandPageSkinTypeEnum {

    NO_SKIN(0,"老页面，无皮肤"),
    MAGIC_CARD(1,"神奇卡"),
    BLIND_BOX(2,"盲盒"),
    WEIXIN_CARD(3,"微信卡包"),
    ALIPAY_CARD(4,"支付宝卡包"),
    PHONE_CARD(5,"号卡"),
    MINIAPP_H5(6,"小程序H5"),
    LIU_ZI_H5(7,"留资H5"),
    BLIND_BOX2(8,"盲盒(带商品列表)"),
    EQUITY_CARD(9,"权益卡(大转盘样式)"),
    BLIND_BOX_TURNTABLE(10,"盲盒大转盘"),
    EQUITY_RED_PACKET(11,"权益卡(红包)"),
    EQUITY_WA_WA(12,"权益卡(抓娃娃)"),
    BLIND_BOX_PHONE(13,"盲盒手机弹层"),
    BAI_JIU(15,"白酒常规"),
    JING_XI_BLIND_BOX(16,"惊喜开盒"),
    QWTF(26,"企微囤粉"),
    QWTF_H5(27,"企微囤粉H5"),
    ;

    private final int type;
    private final String desc;
}
