package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 身份证信息校验状态
 *
 * <AUTHOR>
 * @date 2021/9/2
 */
@Getter
@AllArgsConstructor
public enum IdCardAuditStatus {

    UNKNOWN(0, "未校验"),
    ACCEPT(1, "通过"),
    REFUSE(2, "未通过");

    private final Integer status;
    private final String desc;

    public static String getDescByStatus(Integer status) {
        for (IdCardAuditStatus statusEnum : IdCardAuditStatus.values()) {
            if (Objects.equals(status, statusEnum.getStatus())) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
