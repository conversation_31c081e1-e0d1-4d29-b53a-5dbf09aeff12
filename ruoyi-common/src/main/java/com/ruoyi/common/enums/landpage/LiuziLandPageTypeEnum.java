package com.ruoyi.common.enums.landpage;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 留资落地页自建站类型
 *
 * <AUTHOR>
 * @date 2022/9/19 2:22 下午
 */
@Getter
@AllArgsConstructor
public enum LiuziLandPageTypeEnum {

    NUO_HE(1,"诺禾自建站"),
    ALIPAY(2,"支付宝自建站"),
    ;

    private final int type;
    private final String desc;

    public static String getDescByType(Integer type){
        for (LiuziLandPageTypeEnum typeEnum:LiuziLandPageTypeEnum.values()){
            if(Objects.equals(typeEnum.getType(),type)){
                return typeEnum.getDesc();
            }
        }
        return "";
    }
}
