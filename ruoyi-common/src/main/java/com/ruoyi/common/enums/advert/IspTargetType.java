package com.ruoyi.common.enums.advert;

import com.ruoyi.common.utils.IntListUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 运营商定向类型
 *
 * <AUTHOR>
 * @date 2023/09/21
 */
@Getter
@AllArgsConstructor
public enum IspTargetType {

    UNLIMITED(0, "不限"),
    CTCC(1, "电信"),
    CMCC(2, "移动"),
    CUCC(3, "联通"),
    OTHER(8, "其他"),
    ;

    private final int type;
    private final String isp;

    // 限制可以配置的最大数量
    private static final int size = Math.max(8, IspTargetType.values().length);

    /**
     * 列表转数字
     *
     * @param types 运营商定向类型列表
     * @return 运营商定向类型整数
     */
    public static Integer convertToInteger(List<Integer> types) {
        return IntListUtils.convertToInteger(size, types);
    }

    /**
     * 数字转列表
     *
     * @param type 运营商定向类型整数
     * @return 运营商定向类型列表
     */
    public static List<Integer> convertToList(Integer type) {
        return IntListUtils.convertToList(size, type);
    }

    /**
     * 是否包含选项
     *
     * @param type 运营商定向类型整数
     * @param option 运营商定向选项
     * @return 是否包含
     */
    public static boolean contains(Integer type, Integer option) {
        return IntListUtils.isSelected(size, type, option);
    }
}
