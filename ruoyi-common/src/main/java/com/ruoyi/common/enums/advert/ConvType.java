package com.ruoyi.common.enums.advert;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 后端转化类型
 *
 * <AUTHOR>
 * @date 2021/8/5
 */
@Getter
@AllArgsConstructor
public enum ConvType {

    OTHER(0, "其他"),
    SUBMIT(1, "表单提交"),
    PAY(2, "支付/充值"),
    REFUND(3, "退款"),
    REGISTER(4, "注册/激活"),
    DELIVERY(5, "发货"),
    COMPLAIN(6, "投诉"),
    APP_DOWNLOAD(11, "APP下载"),
    APP_ACTIVE(12, "APP激活"),
    APP_REGISTER(13, "APP注册"),
    KEY_ACTION1(21, "关键行为1"),
    KEY_ACTION2(22, "关键行为2"),
    KEY_ACTION3(23, "关键行为3"),
    KEY_ACTION4(24, "关键行为4"),
    KEY_ACTION5(25, "关键行为5"),
    KEY_ACTION6(26, "关键行为6"),
    KEY_ACTION7(27, "关键行为7"),
    KEY_ACTION8(28, "关键行为8"),
    KEY_ACTION9(29, "关键行为9"),
    KEY_ACTION10(30, "关键行为10"),
    ;

    private final int type;
    private final String desc;

    public static boolean isSubmit(Integer type) {
        return Objects.equals(SUBMIT.type, type);
    }

    public static boolean isRegister(Integer type) {
        return Objects.equals(REGISTER.type, type);
    }

    public static boolean isPay(Integer type) {
        return Objects.equals(PAY.type, type);
    }

    public static boolean isAppActive(Integer type) {
        return Objects.equals(APP_ACTIVE.type, type);
    }

    public static ConvType getByType(Integer type) {
        for (ConvType e : ConvType.values()) {
            if (Objects.equals(type, e.getType())) {
                return e;
            }
        }
        return OTHER;
    }

    public static String getDescByType(Integer type) {
        for (ConvType e : ConvType.values()) {
            if (Objects.equals(type, e.getType())) {
                return e.getDesc();
            }
        }
        return "";
    }

    /**
     * 获取类型描述映射(号卡使用)
     */
    public static Map<Integer, String> getHaoKaMap() {
        Map<Integer, String> map = Arrays.stream(values()).collect(Collectors.toMap(ConvType::getType, ConvType::getDesc, (oldVal, newVal) -> newVal));
        map.put(2, "支付");
        map.put(4, "激活");
        return map;
    }
}
