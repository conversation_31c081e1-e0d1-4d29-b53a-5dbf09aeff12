package com.ruoyi.common.enums.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 全局配置Key枚举
 *
 * <AUTHOR>
 * @date 2021/9/16
 */
@Getter
@AllArgsConstructor
public enum SysConfigKeyEnum {

    DEFAULT_SLOT_URL("slot.st.url", "广告位默认链接"),
    DEFAULT_ACT_URL("activity.act.url", "活动默认链接"),
    DEFAULT_DOMAIN_LANDPAGE("default.domain.landpage", "落地页默认域名"),
    ADVERT_TARGET_ADVERTISER("advert.target.advertiser", "广告映射广告主"),
    SLOT_CHANNEL("slot.channel", "广告位渠道映射"),
    ADVERTISER_TAG("advertiser.tag", "广告主标签"),
    LIUZI_SMS_CONFIG("liuzi.sms.config","留资短信配置"),
    LIUZI_SEND_QIWEI_FRIEND("liuzi.send.qiwei.friend","留资企微加好友配置")
    ;

    private final String key;
    private final String desc;
}
