package com.ruoyi.common.enums.advertiser;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 广告主结算类型
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
@Getter
@AllArgsConstructor
public enum AdvertiserConsumeType {

    CPC(1, "CPC结算"),

    // 因平台客户对接规则统一更改为了CPC，历史遗留CPA结算类型废弃
//    CPA_CUCC(2, "联通(CPA结算)"),
//    CPA_CMCC(3, "移动(CPA结算)"),
//    CPA_CTCC(4, "电信(CPA结算)"),
//    CPA_BAIJIU(5, "白酒(CPA结算)"),
    ;

    private final int type;
    private final String desc;

    public static boolean isCPC(Integer type) {
        return Objects.equals(type, CPC.type);
    }
}
