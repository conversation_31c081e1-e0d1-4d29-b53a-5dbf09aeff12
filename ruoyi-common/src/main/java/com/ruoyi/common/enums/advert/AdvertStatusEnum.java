package com.ruoyi.common.enums.advert;

import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Date;
import java.util.Objects;

import static com.ruoyi.common.enums.common.SwitchStatusEnum.isSwitchOff;

/**
 * 广告状态枚举
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Getter
@AllArgsConstructor
public enum AdvertStatusEnum {

    NORMAL(0, "正常"),
    INVALID(1, "无效"),
    INVALID_WITHOUT_BUDGET(2, "无效(预算不足)"),
    INVALID_WITHOUT_BALANCE(3, "无效(广告主余额不足)"),
    AGENT_DISABLE(4, "无效(代理商禁用)"),
    INVALID_WITHOUT_ADVERTISER_BUDGET(5, "无效(广告主日预算不足)"),
    ;

    private final int status;
    private final String desc;

    /**
     * 判断广告是否是有效的状态
     *
     * @param status 广告状态
     * @return 是否有效
     */
    public static boolean isAdvertValid(Integer status) {
        return Objects.equals(status, NORMAL.getStatus());
    }

    /**
     * 判断广告是否预算不足状态
     *
     * @param status 广告状态
     * @return 是否预算不足状态
     */
    public static boolean isBudgetRunOut(Integer status) {
        return Objects.equals(status, INVALID_WITHOUT_BUDGET.getStatus())
                || Objects.equals(status, INVALID_WITHOUT_BALANCE.getStatus())
                || Objects.equals(status, INVALID_WITHOUT_ADVERTISER_BUDGET.getStatus());
    }

    /**
     * 根据状态获取状态描述
     *
     * @param status 状态
     * @return 状态描述
     */
    public static String getDescByStatus(Integer status) {
        for (AdvertStatusEnum statusEnum : AdvertStatusEnum.values()) {
            if (Objects.equals(statusEnum.getStatus(), status)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }

    /**
     * 根据状态获取枚举
     *
     * @param status 状态
     * @return 枚举
     */
    public static AdvertStatusEnum getByStatus(Integer status) {
        for (AdvertStatusEnum e : AdvertStatusEnum.values()) {
            if (Objects.equals(status, e.status)) {
                return e;
            }
        }
        return INVALID;
    }

    /**
     * 获取广告状态描述
     * 注:用于CRM后台列表展示
     *
     * @param status 状态
     * @param servingSwitch 投放开关
     * @param startServingDate 开始投放日期
     * @param stopServingDate 结束投放日期
     * @return 状态描述
     */
    public static String getStatusStr(Integer status, Integer servingSwitch, Date startServingDate, Date stopServingDate) {
        AdvertStatusEnum e = getByStatus(status);
        switch (e) {
            case INVALID:
                return "无效广告";
            case AGENT_DISABLE:
                return "被代理商禁用";
            case INVALID_WITHOUT_BUDGET:
                return "计划预算不足";
            case INVALID_WITHOUT_ADVERTISER_BUDGET:
                return "广告主预算不足";
            case INVALID_WITHOUT_BALANCE:
                return "账户余额不足";
            case NORMAL:
            default:
                if (isSwitchOff(servingSwitch)) {
                    return "已暂停";
                }
                Date today = DateUtil.beginOfDay(new Date());
                if (today.before(startServingDate) || today.after(stopServingDate)) {
                    return "不在投放日期";
                }
                return "正常";
        }
    }
}
