package com.ruoyi.common.enums.open;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 百度配置枚举
 *
 * <AUTHOR>
 * @date 2022/10/17
 */
@Getter
@AllArgsConstructor
public enum BaiduConfigEnum {

    DEFAULT(853726L, "pWZxGmrDAtXAF5epKDs55VE7doNLXlXW@5BGZZ6oFLXDGfG9LTlnlGmMS7TG7awfn"),
    S853384(853384L, "vIyFOxH7lrtsVcQ1joSWW4iwHU8Bmni7@9lHpOOW1sI4dI8nPoNKgx5grdQoVgV3g"),
    S853726(853726L, "xLGCq0x13NT0WLon1SATus0ckffctlDs@fLkcIHwR3ubz5ZzRC02GuHTRpTcSV4PZ"),
    S853748(853748L, "MaRNtbybSDgQWIL0mzV1kG58yTD8smwW@U8xV0Zru0OxNqKa3fG9vpcQTXEl5g0OL"),
    S854347(854347L, "uxjhIgcShIcmiN9x2jnWMHe2LvCuzX7G@WIM0PZLzjtDCd7hM6Lui7UQiV4FErisT"),
    ;

    private final Long slotId;
    private final String token;

    public static String getTokenBySlotId(Long slotId) {
        return getBySlotId(slotId).getToken();
    }

    public static BaiduConfigEnum getBySlotId(Long slotId) {
        for (BaiduConfigEnum config : values()) {
            if (Objects.equals(config.getSlotId(), slotId)) {
                return config;
            }
        }
        return DEFAULT;
    }
}
