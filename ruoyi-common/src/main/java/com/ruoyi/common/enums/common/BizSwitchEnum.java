package com.ruoyi.common.enums.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 业务开关枚举
 * 注:Redis有值为关，无为开
 *
 * <AUTHOR>
 * @date 2024/04/02
 */
@Getter
@AllArgsConstructor
public enum BizSwitchEnum {

    ARTICLE_API("callCkApi", "业务开关-文章接口阅读量API", ""),
    ARTICLE_COMPENSATE("articleCompensate", "业务开关-文章自动补量", ""),
    WX_CHECK_API("weixinCheckApi", "业务开关-微信防封监测API", ""),
    ;

    private final String key;
    private final String desc;
    private final String placeholder;

    public static BizSwitchEnum getByKey(String key) {
        for (BizSwitchEnum e : BizSwitchEnum.values()) {
            if (Objects.equals(key, e.getKey())) {
                return e;
            }
        }
        return null;
    }
}
