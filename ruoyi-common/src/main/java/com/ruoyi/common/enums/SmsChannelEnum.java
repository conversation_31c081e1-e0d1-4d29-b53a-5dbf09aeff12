package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 短信渠道枚举
 *
 * <AUTHOR>
 * @date 2023/1/9 14:30
 */
@Getter
@AllArgsConstructor
public enum SmsChannelEnum {

    ZHU_TONG(1,"助通"),
    ZHANG_RONG(2,"掌榕"),
    RUI_ZHUO(3,"瑞濯"),
    BAI_WU(4,"百悟"),
    //以上四个渠道为旧渠道，不再使用
    CHUANG_LAN(5,"创蓝云智"),
    CHUAN_ZHEN(6,"传臻"),
    GAN_AN(7,"赣安"),
    FENG_XUE_YUN(8,"枫雪云"),
    ;

    private final Integer type;
    private final String desc;

    public static String getChannelNameByType(Integer type){
        for (SmsChannelEnum smsChannelEnum:SmsChannelEnum.values()){
            if(Objects.equals(smsChannelEnum.getType(),type)){
                return smsChannelEnum.getDesc();
            }
        }
        return "";
    }
}
