package com.ruoyi.common.enums.slot;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 广告位是否可投放枚举
 *
 * <AUTHOR>
 * @date 2021/7/26
 */
@Getter
@AllArgsConstructor
public enum SlotEnableEnum {

    DISABLE(0, "不可投放"),
    ENABLE(1, "可投放");

    private final int status;
    private final String desc;

    public static int isSlotEnable(boolean isEnable) {
        return isEnable ? ENABLE.status : DISABLE.status;
    }
}
