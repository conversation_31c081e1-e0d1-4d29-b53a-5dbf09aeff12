package com.ruoyi.common.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 月账单结算状态
 *
 * <AUTHOR>
 * @date 2021/9/13 11:23 上午
 */
@Getter
public enum ConfirmStatusEnum{

    ALL(0,"全部"),
    NO_CONFIRM(1,"待确认"),
    CONFIRM(2,"已确认"),
    NO_WITHDRAW(3,"待结算"),
    WITHDRAW(4,"已结算"),
    ;

    private Integer status;
    private String desc;

    ConfirmStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static String getDescByStatus(Integer status){
        for (ConfirmStatusEnum statusEnum : ConfirmStatusEnum.values()){
            if(Objects.equals(status,statusEnum.getStatus())){
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
