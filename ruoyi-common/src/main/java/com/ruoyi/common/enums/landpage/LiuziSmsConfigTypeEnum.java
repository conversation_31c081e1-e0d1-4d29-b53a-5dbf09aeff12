package com.ruoyi.common.enums.landpage;

import java.util.Objects;

/**
 * 留资落地页短信配置枚举
 *
 * <AUTHOR>
 * @date 2022/9/19 2:22 下午
 */
public enum LiuziSmsConfigTypeEnum {

    DEFAULT(1,"默认"),
    CUSTOM(2,"自定义")
    ;

    private final int type;
    private final String desc;

    LiuziSmsConfigTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
    public static String getDescByType(Integer type){
        for (LiuziSmsConfigTypeEnum typeEnum: LiuziSmsConfigTypeEnum.values()){
            if(Objects.equals(typeEnum.getType(),type)){
                return typeEnum.getDesc();
            }
        }
        return "";
    }
}
