package com.ruoyi.common.enums.activity;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 活动推广计划状态枚举
 *
 * <AUTHOR>
 * @date 2021/7/17
 */
@Getter
@AllArgsConstructor
public enum ActPlanStatusEnum {

    OPEN(0, "开启"),
    CLOSE(1, "关闭");

    private final int status;
    private final String desc;

    /**
     * 活动推广计划是否开启
     *
     * @param status 活动推广计划状态
     * @return 是否开启
     */
    public static boolean isActPlanOpen(Integer status) {
        return Objects.equals(status, OPEN.status);
    }
}
