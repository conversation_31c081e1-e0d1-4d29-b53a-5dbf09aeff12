package com.ruoyi.common.enums.advertiser;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 表单广告主计费类型
 *
 * <AUTHOR>
 * @date 2022/07/11
 */
@Getter
@AllArgsConstructor
public enum FormAdvertiserPriceType {

    VALID_FORM(0, "有效表单"),
    GROSS_FORM(1, "毛表单"),
    ;

    private final int type;
    private final String desc;

    public static String getDescByType(Integer type) {
        for (FormAdvertiserPriceType typeEnum : FormAdvertiserPriceType.values()) {
            if (Objects.equals(type, typeEnum.getType())) {
                return typeEnum.getDesc();
            }
        }
        return VALID_FORM.desc;
    }

    public static boolean isConsume(Integer type, int isSuccess) {
        return Objects.equals(type, GROSS_FORM.type) || isSuccess > 0;
    }
}
