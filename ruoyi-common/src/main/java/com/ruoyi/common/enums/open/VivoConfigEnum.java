package com.ruoyi.common.enums.open;

import com.alibaba.excel.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * Vivo配置枚举
 *
 * <AUTHOR>
 * @date 2022/07/20
 */
@Getter
@AllArgsConstructor
public enum VivoConfigEnum {

    nhbj01("nhbj01", "ds-202312214307", "c23843178bbddb97b6b3"),
    nhbj02("nhbj02", "ds-202401125869", "7083b9955c468c3bed8e"),
    nhbj05("nhbj05", "ds-202403079575", "27944377e4062f978eef"),
    nhbj06("nhbj06", "ds-202403078585", "57234733cf5d1419898a"),
    nhbj07("nhbj07", "ds-202402239788", "3e8e5d6b98848cbfca84"),
    nhbj08("nhbj08", "ds-202402234706", "ae0e3dea282ee1640613"),
    nhbj09("nhbj09", "ds-************", "8ce6a9d101a4587fb96f"),
    jnbb01("jnbb01", "ds-************", "185e3aa3174b47d6abd5"),
    jnbb02("jnbb02", "ds-************", "a8d017a3d40a39438b61"),
    nadou01("nadou01", "ds-************", "0bdddf3eba5af205f1b9"),
    nadou02("nadou02", "ds-************", "ba83d7d64ee0379d0bee"),
    nadou03("nadou03", "ds-************", "b88d43e10da27b4e021a"),
    ;

    private final String advertiserId;
    private final String srcId;
    private final String uuid;

    public static VivoConfigEnum getConfig(String advertiserId) {
        if (StringUtils.isNotBlank(advertiserId)) {
            for (VivoConfigEnum config : values()) {
                if (Objects.equals(config.getAdvertiserId(), advertiserId)) {
                    return config;
                }
            }
        }
        return nhbj01;
    }

    public static String getAdvertiserIdByUuid(String uuid) {
        for (VivoConfigEnum config : values()) {
            if (Objects.equals(config.getUuid(), uuid)) {
                return config.getAdvertiserId();
            }
        }
        return "";
    }
}
