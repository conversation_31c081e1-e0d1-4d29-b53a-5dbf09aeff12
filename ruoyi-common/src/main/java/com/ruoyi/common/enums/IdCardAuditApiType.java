package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 身份证实名接口提供方枚举
 *
 * <AUTHOR>
 * @date 2022/11/02
 */
@Getter
@AllArgsConstructor
public enum IdCardAuditApiType {

    UNKNOWN(0, "未知"),
    JUHE(1, "聚合"),
    HAOMIAO(2, "毫秒科技");

    private final Integer type;
    private final String desc;

    public static String getDescByType(Integer type) {
        for (IdCardAuditApiType statusEnum : IdCardAuditApiType.values()) {
            if (Objects.equals(type, statusEnum.getType())) {
                return statusEnum.getDesc();
            }
        }
        return UNKNOWN.desc;
    }
}
