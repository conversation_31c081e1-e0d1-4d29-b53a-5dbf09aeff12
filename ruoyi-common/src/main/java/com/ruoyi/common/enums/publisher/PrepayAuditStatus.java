package com.ruoyi.common.enums.publisher;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 预付款审核状态
 *
 * <AUTHOR>
 * @date 2022/08/02
 */
@Getter
@AllArgsConstructor
public enum PrepayAuditStatus {

    IN_AUDIT(0, "审核中"),
    PASSED(1, "审核通过"),
    REFUSED(2, "审核拒绝");

    private final int status;
    private final String desc;

    public static boolean isInAudit(Integer status) {
        return Objects.equals(status, IN_AUDIT.status);
    }

    public static boolean isAuditPassed(Integer status) {
        return Objects.equals(status, PASSED.status);
    }
}
