package com.ruoyi.common.enums.advert;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 广告形式枚举
 *
 * <AUTHOR>
 * @date 2021/8/13
 */
@Getter
@AllArgsConstructor
public enum AdvertModeEnum {

    NH("nh", "诺禾广告"),
    OTHER("other", "外部广告"),
    ;

    private final String key;
    private final String desc;

    /**
     * 是否诺禾广告
     */
    public static boolean isNhAdvert(String key) {
        return Objects.equals(key, NH.key);
    }
}
