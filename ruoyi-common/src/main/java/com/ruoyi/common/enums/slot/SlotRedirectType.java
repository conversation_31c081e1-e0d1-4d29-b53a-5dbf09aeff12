package com.ruoyi.common.enums.slot;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 广告位的活动投放类型枚举
 *
 * <AUTHOR>
 * @date 2021/7/17
 */
@Getter
@AllArgsConstructor
public enum SlotRedirectType {

    ACTIVITY(1, "活动"),
    URL(2, "外部链接"),
    SHUNT(3, "分流投放"),
    AREA_TARGET(4, "地域定向"),
    ADVERT(5, "直投广告"),
    ADVERT_ORIENT(6, "直投广告配置"),
    ACTIVITY_ARPU(7, "活动-ARPU排序"),
    TEXT(8, "文本"),
    ;

    private final int type;
    private final String desc;

    /**
     * 是否跳转到活动
     */
    public static boolean redirectToActivity(Integer type) {
        return Objects.equals(type, ACTIVITY.getType()) || Objects.equals(type, ACTIVITY_ARPU.getType());
    }

    /**
     * 是否跳转到外链
     */
    public static boolean redirectToUrl(Integer type) {
        return Objects.equals(type, URL.getType());
    }

    /**
     * 是否直投广告
     */
    public static boolean redirectToAdvert(Integer type) {
        return Objects.equals(type, ADVERT.getType());
    }

    /**
     * 是否直投广告配置
     */
    public static boolean redirectToAdvertOrient(Integer type) {
        return Objects.equals(type, ADVERT_ORIENT.getType());
    }

    /**
     * 是否文本
     */
    public static boolean redirectToText(Integer type) {
        return Objects.equals(type, TEXT.getType());
    }

    /**
     * 是否分流跳转
     */
    public static boolean isShuntRedirect(Integer type) {
        return Objects.equals(type, SHUNT.getType());
    }

    /**
     * 是否地域定向跳转
     */
    public static boolean isAreaTargetRedirect(Integer type) {
        return Objects.equals(type, AREA_TARGET.getType());
    }

    /**
     * 是否复合跳转
     */
    public static boolean isCompositeRedirect(Integer type) {
        return Objects.equals(type, AREA_TARGET.getType()) || Objects.equals(type, SHUNT.getType());
    }

    /**
     * 根据类型获取描述
     *
     * @param type 投放类型
     * @return 描述
     */
    public static String getDescByType(Integer type) {
        for (SlotRedirectType typeEnum : SlotRedirectType.values()) {
            if (Objects.equals(type, typeEnum.getType())) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }
}
