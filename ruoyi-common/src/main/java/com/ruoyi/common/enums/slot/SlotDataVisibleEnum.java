package com.ruoyi.common.enums.slot;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 广告位数据媒体可见状态枚举
 *
 * <AUTHOR>
 * @date 2022/03/09
 */
@Getter
@AllArgsConstructor
public enum SlotDataVisibleEnum {

    INVISIBLE(0, "媒体不可见"),
    VISIBLE(1, "媒体可见");

    private final int status;
    private final String desc;

    public static boolean isVisible(Integer status) {
        return Objects.equals(VISIBLE.getStatus(), status);
    }
}
