package com.ruoyi.common.enums.plugin;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 插件开关枚举
 *
 * <AUTHOR>
 * @date 2021/12/28
 */
@Getter
@AllArgsConstructor
public enum PluginSwitchEnum {

    OFF(0, "关闭"),
    ON(1, "开启");

    private final int status;
    private final String desc;

    /**
     * 判断开关是否开启
     *
     * @param status 开关状态
     * @return 是否开启
     */
    public static boolean isOn(Integer status) {
        return Objects.equals(status, ON.status);
    }
}
