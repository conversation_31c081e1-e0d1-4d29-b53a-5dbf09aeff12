package com.ruoyi.common.enums;

import java.util.Objects;

/**
 * 企微加好友状态
 * <AUTHOR>
 * @date 2023/5/10 16:31
 */
public enum QwFriendStatusEnum {
    NO_APPLY(1,"未加好友"),
    APPLY(2,"发起申请"),
    BECOME_FRIEND(3,"已加好友")
    ;
    private int status;
    private String desc;


    QwFriendStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByStatus(Integer status){
        for (QwFriendStatusEnum statusEnum:QwFriendStatusEnum.values()){
            if(Objects.equals(statusEnum.getStatus(),status)){
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
