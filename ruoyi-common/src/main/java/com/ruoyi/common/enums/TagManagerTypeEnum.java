package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 标签管理类型枚举
 *
 * <AUTHOR>
 * @date 2022/9/23 11:55 上午
 */
@Getter
@AllArgsConstructor
public enum TagManagerTypeEnum {

    APP_TAG(1,"媒体标签"),
    LANDPAGE_TAG(2,"落地页标签"),
    ADVERTISER_TAG(3,"广告主标签"),
    DOMAIN_TAG(4,"域名标签"),
    SLOT_TAG(5,"广告位标签"),
    ;

    private final int type;
    private final String desc;

    public static String getDescByType(int type){
        for(TagManagerTypeEnum typeEnum : TagManagerTypeEnum.values()){
            if(Objects.equals(typeEnum.getType(),type)){
                return typeEnum.getDesc();
            }
        }
        return "";
    }
}
