package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 微信支付订单状态枚举
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
@Getter
@AllArgsConstructor
public enum WxPayOrderStatus {

    READY(0, "待支付"),
    PAID(1, "支付成功"),
    CLOSED(2, "支付关闭"),
    ;

    private final int status;
    private final String desc;

    /**
     * 判断是否待支付
     *
     * @param status 状态
     * @return 是否待支付
     */
    public static boolean isReady(Integer status) {
        return Objects.equals(status, READY.status);
    }

    /**
     * 判断是否支付成功
     *
     * @param status 状态
     * @return 是否支付成功
     */
    public static boolean isPaid(Integer status) {
        return Objects.equals(status, PAID.status);
    }
}
