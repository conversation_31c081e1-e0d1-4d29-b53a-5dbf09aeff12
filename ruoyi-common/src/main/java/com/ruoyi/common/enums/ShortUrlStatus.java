package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 短链状态枚举
 *
 * <AUTHOR>
 * @date 2022/10/10
 */
@Getter
@AllArgsConstructor
public enum ShortUrlStatus {

    ENABLE(0, "启用"),
    DISABLE(1, "禁用"),
    DELETED(2, "删除");

    private final int status;
    private final String desc;

    /**
     * 判断是否正常
     *
     * @param status 短链状态
     * @return 是否正常
     */
    public static boolean isEnable(Integer status) {
        return Objects.equals(status, ENABLE.status);
    }
}
