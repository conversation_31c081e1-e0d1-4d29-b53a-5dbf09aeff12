package com.ruoyi.common.enums.portal;

/**
 * 官网发布信息角色类型
 * <AUTHOR>
 * @date 2022/9/27 1:47 下午
 */
public enum PortalContentTypeEnum {
    ADVERTISER(1,"广告主"),
    PUBLISHER(2,"流量主");
    private Integer type;
    private String desc;

    PortalContentTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
