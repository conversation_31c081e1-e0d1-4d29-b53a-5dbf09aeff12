package com.ruoyi.common.enums.open;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 优酷配置枚举
 *
 * <AUTHOR>
 * @date 2023/03/08
 */
@Getter
@AllArgsConstructor
public enum YoukuConfigEnum {

    DEFAULT(1L, "29045004", "6T1SJksvPuWVErw9jt0kyu5r3BHNGLnt"),
    ;

    private final Long slotId;
    private final String appKey;
    private final String token;

    public static YoukuConfigEnum getBySlotId(Long slotId) {
//        for (YoukuConfigEnum config : values()) {
//            if (Objects.equals(config.getSlotId(), slotId)) {
//                return config;
//            }
//        }
        return DEFAULT;
    }
}
