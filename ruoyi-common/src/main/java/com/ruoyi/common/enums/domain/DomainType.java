package com.ruoyi.common.enums.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 域名类型枚举
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Getter
@AllArgsConstructor
public enum DomainType {

    OTHER_DOMAIN(0, "otherDomain", "其他域名"),
    SLOT_DOMAIN(1, "slotDomain", "广告位域名"),
    ACTIVITY_DOMAIN(2, "activityDomain", "活动域名"),
    LANDPAGE_DOMAIN(3, "landpageDomain", "落地页域名");

    private final int type;
    private final String key;
    private final String desc;

    public static List<String> keys() {
        return Arrays.stream(values()).map(DomainType::getKey).collect(Collectors.toList());
    }

    public static List<Integer> types() {
        return Arrays.stream(values()).map(DomainType::getType).collect(Collectors.toList());
    }

    public static String getKeyByType(Integer type) {
        for (DomainType e : values()) {
            if (Objects.equals(e.getType(), type)) {
                return e.getKey();
            }
        }
        return null;
    }

    public static String getDescByType(Integer type) {
        for (DomainType e : values()) {
            if (Objects.equals(e.getType(), type)) {
                return e.getDesc();
            }
        }
        return null;
    }

    public static boolean isActivityDomain(Integer domainType) {
        return Objects.equals(ACTIVITY_DOMAIN.type, domainType);
    }

    public static boolean isLandpageDomain(Integer domainType) {
        return Objects.equals(LANDPAGE_DOMAIN.type, domainType);
    }

    public static boolean isOtherDomain(Integer domainType) {
        return Objects.equals(OTHER_DOMAIN.type, domainType);
    }
}
