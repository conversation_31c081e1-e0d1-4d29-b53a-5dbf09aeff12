package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 计数监控类型
 *
 * <AUTHOR>
 * @date 2022/05/19
 */
@Getter
@AllArgsConstructor
public enum CountMonitorTypeEnum {

    JUHE_SFZ(1, "聚合-身份证失败次数超过阈值", 20, 60),
    DIRECT_ADVERT(2, "直投广告投放失败", 100, 60),
    HAOMIAO_SFZ(3, "毫秒科技-身份证失败次数超过阈值", 20, 60),
    HAOMIAO_IP(4, "毫秒科技-IP解析失败次数超过阈值", 100, 60),
    ARTICLE_API(5, "文章阅读量-接口余额不足", 1, 1440),
    ;

    /**
     * 类型
     */
    private final int type;

    /**
     * 错误信息
     */
    private final String msg;

    /**
     * 告警阈值
     */
    private final int threshold;

    /**
     * 间隔(分钟)
     */
    private final int interval;

    /**
     * 根据类型获取枚举
     */
    public static CountMonitorTypeEnum getByType(Integer type) {
        for (CountMonitorTypeEnum typeEnum : CountMonitorTypeEnum.values()) {
            if (Objects.equals(type, typeEnum.type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
