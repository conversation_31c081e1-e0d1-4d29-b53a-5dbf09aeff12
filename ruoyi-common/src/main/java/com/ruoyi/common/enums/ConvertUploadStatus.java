package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 媒体转化上报状态
 *
 * <AUTHOR>
 * @date 2022/10/19
 */
@Getter
@AllArgsConstructor
public enum ConvertUploadStatus {

    NOT_YET(0, "未上报"),
    SUCCESS(1, "上报成功"),
    FAIL(2, "上报失败");

    private final int status;
    private final String desc;

    public static boolean isSuccess(Integer status) {
        return Objects.equals(status, SUCCESS.getStatus());
    }

    public static String getDescByStatus(Integer status) {
        for (ConvertUploadStatus statusEnum : ConvertUploadStatus.values()) {
            if (Objects.equals(status, statusEnum.getStatus())) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
