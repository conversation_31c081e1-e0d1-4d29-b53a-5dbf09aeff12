package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 点击监测支持的宏替换枚举
 *
 * <AUTHOR>
 * @date 2021/8/18
 * * uid_o5=__OAID_MD5__&uid_m5=__IMEI__&uid_f5=__IDFA__
 */
@Getter
@AllArgsConstructor
public enum ClickCallbackMacroEnum {

    APP_ID("__N_APPID__", "appId", "媒体ID"),
    SLOT_ID("__N_SLOTID__", "slotId", "广告位ID"),
    ORDER_ID("__N_OID__", "orderId", "订单号"),
    CONSUMER_ID("__N_CID__","consumerId", "用户ID"),
    DEVICE_ID("__N_DEVICEID__","deviceId", "设备号"),
    USER_AGENT("__N_UA__","userAgent", "UserAgent"),
    IP("__N_IP__","ip", "IP地址"),
    OAID_MD5("__OAID_MD5__","uid_o5", "设备号oaid"),
    IMEI_MD5("__IMEI_MD5__","uid_m5", "设备号imei安卓"),
    IDFA_MD5("__IDFA_MD5__","uid_f5", "设备号idfa苹果"),
    ;

    private final String code;
    private final String key;
    private final String desc;
}
