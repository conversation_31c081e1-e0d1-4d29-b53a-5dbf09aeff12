package com.ruoyi.common.enums.advert;

import com.ruoyi.common.utils.IntListUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 系统定向类型
 *
 * <AUTHOR>
 * @date 2022/08/31
 */
@Getter
@AllArgsConstructor
public enum OsTargetType {

    UNLIMITED(0, "不限"),
    IOS(1, "iOS"),
    ANDROID(2, "Android"),
    OTHER(8, "其他"),
    ;

    private final int type;
    private final String desc;

    // 限制可以配置的最大数量
    private static final int size = Math.max(8, OsTargetType.values().length);

    /**
     * 列表转数字
     *
     * @param types 系统定向类型列表
     * @return 系统定向类型整数
     */
    public static Integer convertToInteger(List<Integer> types) {
        return IntListUtils.convertToInteger(size, types);
    }

    /**
     * 数字转列表
     *
     * @param type 系统定向类型整数
     * @return 系统定向类型列表
     */
    public static List<Integer> convertToList(Integer type) {
        return IntListUtils.convertToList(size, type);
    }

    /**
     * 是否包含选项
     *
     * @param type 系统定向类型整数
     * @param option 系统定向选项
     * @return 是否包含
     */
    public static boolean contains(Integer type, Integer option) {
        return IntListUtils.isSelected(size, type, option);
    }
}
