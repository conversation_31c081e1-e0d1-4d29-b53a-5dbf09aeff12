package com.ruoyi.common.enums.advert;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 广告过滤类型枚举
 *
 * <AUTHOR>
 * @date 2021/8/17
 */
@Getter
@AllArgsConstructor
public enum AdvertFilterType {

    // 定向配置过滤
    BANNED_APP("1.1", "屏蔽媒体", 15),
    AREA_TARGET("1.2", "地域定向", 20),
    ORIENTED_APP("1.3","定向媒体广告位",16),
    DEVICE_TARGET("1.4", "设备定向", 25),
    OS_TARGET("1.5", "系统定向", 26),
    FLOW_TARGET("1.6", "流量定向", 27),
    SERVING_HOUR("1.7", "投放时段", 28),
    ORIENT_BUDGET("1.8", "配置每日预算", 29),
    ISP_TARGET("1.9", "运营商定向", 30),
    ADVERT_BUDGET("1.10", "广告每日预算", 31),

    // 广告过滤
    ADVERT_STATUS("2.1", "广告状态", 1),
    SERVING_SWITCH("2.2", "广告/配置开关", 2),
    SERVING_DATE("2.3", "广告投放周期", 3),
//    IQIYI_LANDPAGE("2.4", "爱奇艺落地页送审", 4),

    // 业务过滤
    REPEAT_EXPOSURE("3.1", "重复曝光", 10),
    HAP_PREFER("3.2", "快应用优选", 40),
    MAU_REPEAT("3.3", "MAU去重", 45),
    ;

    private final String code;
    private final String desc;
    private final int order;
}
