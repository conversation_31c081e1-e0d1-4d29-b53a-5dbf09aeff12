package com.ruoyi.common.enums.publisher;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 提现审核状态
 *
 * <AUTHOR>
 * @date 2021/9/15 10:19 上午
 */
@Getter
@AllArgsConstructor
public enum WithdrawCheckStatusEnum {

    WAIT_CHECK(1,"待审核"),
    CHECK_SUCCESS(2,"审核通过"),
    CHECK_REFUSE(3,"审核拒绝"),
    ;

    private final Integer status;
    private final String desc;

    public static String getDescByStatus(Integer status){
        for (WithdrawCheckStatusEnum statusEnum: WithdrawCheckStatusEnum.values()){
            if(statusEnum.getStatus().equals(status)){
                return statusEnum.getDesc();
            }
        }
        return "";
    }

    public static boolean isWaitCheck(Integer status) {
        return Objects.equals(status, WAIT_CHECK.status);
    }

    public static boolean isCheckSuccess(Integer status) {
        return Objects.equals(status, CHECK_SUCCESS.status);
    }
}
