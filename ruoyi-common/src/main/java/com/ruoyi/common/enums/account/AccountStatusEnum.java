package com.ruoyi.common.enums.account;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 账号状态枚举
 * status是二进制对应的十进制整数，每一位代表一个状态，|复合多种状态
 *
 * <AUTHOR>
 * @date 2022/06/17
 */
@Getter
@AllArgsConstructor
public enum AccountStatusEnum {

    NORMAL(0, "正常"),
    DISABLE(1, "停用"),
    NO_AUTHORITY(0b10, "无访问权限"),
    DISABLE_AND_NO_AUTHORITY(0b11, "停用&无访问权限"),
    AGENT_DISABLE(0b100, "代理商禁用"),
    OTHER(127, "其他"),
    ;

    private final int status;
    private final String desc;

    public static boolean isNormal(Integer status) {
        return Objects.equals(status, NORMAL.status);
    }

    public static boolean isAgentDisable(Integer status) {
        return Objects.equals(status, AGENT_DISABLE.status);
    }

    public static AccountStatusEnum getByStatus(Integer status) {
        for (AccountStatusEnum e : AccountStatusEnum.values()) {
            if (Objects.equals(status, e.status)) {
                return e;
            }
        }
        return OTHER;
    }
}
