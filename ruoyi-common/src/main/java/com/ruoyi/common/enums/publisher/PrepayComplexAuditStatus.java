package com.ruoyi.common.enums.publisher;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 预付款复合审核状态
 *
 * <AUTHOR>
 * @date 2022/11/25
 */
@Getter
@AllArgsConstructor
public enum PrepayComplexAuditStatus {

    LEADER_READY(0, "待部门负责人审核"),
    APPROVE(1, "审核通过"),
    REFUSE(2, "审核拒绝"),
    CEO_READY(3, "待业务负责人审核"),
    FINANCE_READY(4, "待财务审核"),
    LEADER_REFUSE(5, "部门负责人审核拒绝"),
    CEO_REFUSE(6, "业务负责人审核拒绝"),
    FINANCE_REFUSE(7, "财务审核拒绝");

    private final Integer status;
    private final String desc;

    public static PrepayAuditStatus getLeaderAuditStatus(Integer status) {
        if (Objects.equals(status, LEADER_REFUSE.status) || Objects.equals(status, REFUSE.status)) {
            return PrepayAuditStatus.REFUSED;
        }
        if (Objects.equals(status, APPROVE.status)
                || Objects.equals(status, CEO_READY.status) || Objects.equals(status, CEO_REFUSE.status)
                || Objects.equals(status, FINANCE_READY.status) || Objects.equals(status, FINANCE_REFUSE.status)) {
            return PrepayAuditStatus.PASSED;
        }
        return PrepayAuditStatus.IN_AUDIT;
    }

    public static PrepayAuditStatus getCeoAuditStatus(Integer status) {
        if (Objects.equals(status, CEO_REFUSE.status) || Objects.equals(status, REFUSE.status)) {
            return PrepayAuditStatus.REFUSED;
        }
        if (Objects.equals(status, APPROVE.status)
                || Objects.equals(status, FINANCE_READY.status) || Objects.equals(status, FINANCE_REFUSE.status)) {
            return PrepayAuditStatus.PASSED;
        }
        return PrepayAuditStatus.IN_AUDIT;
    }

    public static PrepayAuditStatus getFinanceAuditStatus(Integer status) {
        if (Objects.equals(status, FINANCE_REFUSE.status) || Objects.equals(status, REFUSE.status)) {
            return PrepayAuditStatus.REFUSED;
        }
        if (Objects.equals(status, APPROVE.status)) {
            return PrepayAuditStatus.PASSED;
        }
        return PrepayAuditStatus.IN_AUDIT;
    }
}
