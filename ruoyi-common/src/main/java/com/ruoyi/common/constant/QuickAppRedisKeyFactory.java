package com.ruoyi.common.constant;

/**
 * quickapp系统使用的Redis Key
 * <AUTHOR>
 * @date 2021/9/30
 */
public enum QuickAppRedisKeyFactory {

    K001("登陆token"),
    ;

    private static final String SPACE = "quick";
    private static final String SEPARATOR = "_";

    private final String desc;

    QuickAppRedisKeyFactory(String desc){
        this.desc = desc;
    }

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg : args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    @Override
    public String toString() {
        return SPACE + SEPARATOR + this.name();
    }
}
