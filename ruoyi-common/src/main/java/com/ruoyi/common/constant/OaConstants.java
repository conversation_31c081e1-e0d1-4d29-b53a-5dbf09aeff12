package com.ruoyi.common.constant;

import cn.hutool.core.collection.CollUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * OA常量
 *
 * <AUTHOR>
 * @date 2022/12/5
 */
public class OaConstants {

    /**
     * 自定义权限：广告-账号数据
     */
    public static final String PERMISSION_TOTAL_ACCOUNT = "custom:custom:allAccount";

    /**
     * 自定义权限：部门数据
     */
    public static final String PERMISSION_DEPARTMENT_DATA = "custom:custom:departmentData";

    /**
     * 自定义权限：预付款-部门审核
     */
    public static final String PERMISSION_PREPAY_DEPARTMENT_AUDIT = "custom:custom:prepayDepartmentAudit";

    /**
     * 自定义权限：预付款-业务审核
     */
    public static final String PERMISSION_PREPAY_BUSINESS_AUDIT = "custom:custom:prepayBusinessAudit";

    /**
     * 自定义权限：提现-部门审核
     */
    public static final String PERMISSION_WITHDRAW_DEPARTMENT_AUDIT = "custom:custom:withdrawDepartmentAudit";

    /**
     * 自定义权限：提现-业务审核
     */
    public static final String PERMISSION_WITHDRAW_BUSINESS_AUDIT = "custom:custom:withdrawBusinessAudit";

    /**
     * 自定义权限：指派负责人
     */
    public static final String PERMISSION_ASSIGN_MANAGER = "custom:custom:assignManager";

    /**
     * 自定义权限：私域-账号数据
     */
    public static final String PERMISSION_TOTAL_PRIVATE_ACCOUNT = "custom:custom:allPrivateAccount";

    /**
     * [媒体预付款/提现]财务审核的职位列表
     */
    public static final Set<String> FINANCE_AUDITOR_LIST = CollUtil.newHashSet("CAI_WU");

    /**
     * 指派负责人-广告运营
     */
    public static final String GUANG_GAO_YUN_YING = "YUN_YING";

    /**
     * 指派负责人-私域运营
     */
    public static final List<String> SI_YU_YUN_YING_LIST = Arrays.asList("SI_YU_YUN_YING", "SI_YU_NEI_RONG_YUN_YING_ZU");

    /**
     * 指派负责人-广告商务
     */
    public static final String GUANG_GAO_SHANG_WU =  "SHANG_WU";

    /**
     * 指派负责人-私域商务
     */
    public static final String SI_YU_SHANG_WU = "SI_YU_SHANG_WU";

    /**
     * 指派负责人-短剧商务
     */
    public static final String DUAN_JU_JIAO_YI_ZU = "DUAN_JU_JIAO_YI_ZU";
}
