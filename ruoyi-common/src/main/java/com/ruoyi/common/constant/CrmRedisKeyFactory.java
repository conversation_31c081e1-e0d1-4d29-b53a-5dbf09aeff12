package com.ruoyi.common.constant;

/**
 * CRM系统使用的Redis Key
 * <AUTHOR>
 * @date 2021/9/30
 */
public enum CrmRedisKeyFactory {

    K001("定时任务分布式锁(通用)"),
    K002("落地页数据回传分布式锁"),
    K003("广告位对比昨日时段流量下降提醒沉默"),
    K004("广告位告警缓存"),
    K005("每15分钟广告位访问pv数据缓存"),
    K006("每15分钟广告位访问uv数据缓存"),
    K007("每15分钟活动访问pv数据缓存"),
    K008("每15分钟计费点击pv数据缓存"),
    K009("每15分钟落地页曝光pv数据缓存"),
    K010("微信域名监测定时任务分布式锁"),
    K011("域名证书到期时间"),
//    K012("落地页行为埋点定时任务分布式锁"), 弃用
    K013("手动分配表单延时队列"),
    K014("广告结算成本每日同步分布式锁"),
    K015("结算款计算定时任务分布式锁"),
    K016("月结算款计算定时任务分布式锁"),
    K017("特定媒体广告位访问数据重新计算分布式锁"),
    K018("落地页提醒钉钉机器人限流缓存"),
    K019("每15分钟活动访问uv数据缓存"),
    K020("vivoToken缓存"),
    K021("微博Token缓存"),
    K022("表单广告主余额不足提醒"),
    K023("广告主维度手动待分配表单"),
//    K024("广点通Token缓存"), // 废弃
    K025("广告主转化上限提醒分布式锁"),
    K026("同步当日支付宝落地页表单分布式锁"),
    K027("同步昨日支付宝落地页表单分布式锁"),
    K028("给未发送支付宝留资短信的手机号发送短信分布式锁"),
    K029("微信环境落地页拦截告警沉默"),
    K030("vivo的AdvertiserId缓存"),
    K031("捷停车Token缓存"),
    K032("蚂蚁保回调幂等处理"),
    K033("导出任务分布式锁"),
    ;

    private static final String SPACE = "Crm";
    private static final String SEPARATOR = "_";

    private final String desc;

    CrmRedisKeyFactory(String desc){
        this.desc = desc;
    }

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg : args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    @Override
    public String toString() {
        return SPACE + SEPARATOR + this.name();
    }
}
