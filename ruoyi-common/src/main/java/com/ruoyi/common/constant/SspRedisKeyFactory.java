package com.ruoyi.common.constant;

/**
 * SSP Redis Key
 *
 * <AUTHOR>
 * @date 2021/9/17 10:18 上午
 */
public enum SspRedisKeyFactory {

    K001("媒体月账单确认分布式锁"),
    K002("审核提现分布式锁"),
    K003("提现申请分布式锁"),
    K004("媒体月账单生成定时任务分布式锁"),
    K005("广告位月账单生成定时任务分布式锁"),
    K006("圈量AccessToken缓存"),
    ;

    private static final String SPACE = "Ssp";
    private static final String SEPARATOR = "_";

    String desc;

    SspRedisKeyFactory(String desc){
        this.desc = desc;
    }

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg :
                args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    @Override
    public String toString() {
        return SPACE + SEPARATOR + this.name();
    }
}
