package com.ruoyi.common.constant;

import org.apache.commons.compress.utils.Sets;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 业务常量
 *
 * <AUTHOR>
 * @date 2021/9/8
 */
public class BizConstants {

    /**
     * 假定为中国的areaNum，用于广告配置-地域定向的全选处理
     */
    public static final String CHINA_AREA_NUM = "000000";

    /**
     * 外部互动广告的ID
     */
    public static final Long OUTER_HD_ADVERT = 1000L;

    /**
     * 地域库标识-联通
     */
    public static final String AREA_TAG_LT = "联通";

    /**
     * 地域库标识-移动
     */
    public static final String AREA_TAG_YD = "移动";

    /**
     * 地域库标识-电信
     */
    public static final String AREA_TAG_DX = "电信";

    /**
     * 默认落地页表单回传链接
     */
    public static final String DEFAULT_LP_CALLBACK_URL = "http://localhost";

    /**
     * IP/UA黑名单，临时放这里
     */
    public static final Set<String> IP_BLACKLIST = new HashSet<>();
    public static final Set<String> UA_BLACKLIST = Sets.newHashSet("Go-http-client/2.0");

    /**
     * 无效ID列表
     */
    public static final List<Long> INVALID_ID_LIST = Collections.singletonList(-1L);

    /**
     * 文章空白/返回拦截页域名
     */
    public static final List<String> ARTICLE_RET_PAGE_DOMAIN_LIST = Arrays.asList(
            "gslmeng.cn", "guangslm.cn", "yuyuekm.com.cn", "yykaimeng.com.cn", "whyykm.com.cn",
            "stwanshi.com.cn", "ggtland.com.cn", "wangshiwan.cn", "wef45.com.cn",
            "yhjk7.com.cn", "knj24.cn", "r8i22.cn",
            "wuhanqwei.com.cn", "whqwgs.com.cn", "whqwjs.com.cn", "whchanghuanjishu.cn", "whanchyouxiangon.cn", "wuhanxin.cn", "wuqjxxyxgs.cn", "wuhanxinx.cn",
            "nanynany.com.cn", "nanyhl.com.cn", "nyhailiang.com.cn", "nanyanghl.com.cn", "nyhliang.com.cn", "nyang.com.cn", "nanfliang.com.cn", "nyfliang.com.cn", "nanyfl.com.cn", "nfliang.com.cn",
            "wuhtxkj.cn", "whsztxgs.cn",
            "whchyb.cn", "whchyb.com.cn", "chyb1.cn","1chyb.cn", "whyuebo.cn"
    );

    /**
     * 文章空白/返回拦截页域名
     */
    public static String ARTICLE_RET_PAGE_DOMAIN = "wuhanqwei.com.cn";


    /**
     * OSS文件访问地址
     */
    public static final String OSS_URL = "https://nh-static.elinks.cn/";

    /**
     * 丰巢素材id前缀
     */
    public static final String FC_MATERIAL_ID_PREFIX = "FCID_";
}
