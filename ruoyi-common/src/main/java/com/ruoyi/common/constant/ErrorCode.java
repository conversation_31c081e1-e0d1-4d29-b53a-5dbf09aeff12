package com.ruoyi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务错误枚举 (模块+错误)
 * 模块:
 *  100.预留
 *  101.注册
 *  102.广告位
 *
 * <AUTHOR>
 * @date 2021/7/9
 */
@Getter
@AllArgsConstructor
public enum ErrorCode {

    // 通用提示
    ARGS(999990, "参数错误"),
    RESUBMIT(999991,"重复提交"),
    SYSTEM(999999, "系统错误"),

    // 101.注册模块
    E101001(101001, "公司名称已存在"),
    E101002(101002, "手机号和邮箱已被使用，请更换"),
    E101003(101003, "手机号已被使用，请更换"),
    E101004(101004, "邮箱已被使用，请更换"),
    E101005(101005, "验证码错误"),
    E101006(101006, "营业执照号已存在，请重新输入"),

    // 102.广告位
    E102001(102001, "投放设置保存失败"),
    E102002(102002, "无效的广告投放占比"),
    E102003(102003, "请检查返回挽留配置"),

    // 103.活动引擎
    E103001(103001, "活动参与次数已用完"),

    // 104 账户相关
    E104001(104001,"账户资质不存在"),
    E104002(104002,"账户不存在"),


    // 105 账号相关
    E105001(105001,"权限不足"),

    // 106 提现相关
    E106001(106001,"提现记录不存在"),
    E106002(106002,"媒体月账单不存在"),
    E106003(106003,"账号可提现金额不足"),
    E106004(106004,"可提现金额异常"),
    E106005(106005,"提现记录已审核"),

    // 107 媒体
    E107001(107001,"媒体不存在"),
    E107002(107002,"广告位不存在"),

    //108 数据
    E108001(108001,"广告位数据不存在"),
    E108002(108002,"媒体数据不存在"),
    E108003(108003,"月账单已生成，无法修改"),
    E108004(108004,"重复的广告位和日期数据修改，请核对后再试"),
    E108005(108005,"广告位数据更新失败"),
    E108006(108006,"导入广告位数据异常"),
    E108007(108007,"月账单已确认，无法修改"),

    //109 广告
    E109001(109001,"广告不存在"),

    // 110 落地页
    E110001(110001,"仅限16～65岁的用户申请，请更换信息重新提交"),
    E110002(110002,"落地页不存在"),
    E110003(110003,"没有资格参与活动"),

    // 111 开放接口
    E111001(111001, "无效的签名"),
    E111002(111002, "请求过于频繁"),
    E111003(111003, "请求已过期"),
    E111004(111004, "无效的日期"),
    E111005(111005, "无效的应用标识"),
    E111006(111006, "参数不能为空"),
    E111007(111007, "请求失败，日期只能为当月日期"),
    E111008(111008, "无更新权限，请联系运营处理"),
    E111009(111009, "无效的广告位ID"),
    E111010(111010, "请求失败，请重试"),
    E111011(111011, "请求失败，请联系运营处理"),

    // 112 外部落地页表单
    E112001(112001,"无效的广告主标识"),
    E112002(112002,"时间戳已过期，请重试"),
    E112003(112003,"表单列表不能为空"),
    E112004(112004,"表单编号不能为空"),
    E112005(112005,"表单信息不能为空"),
    E112006(112006,"无效的签名"),
    E112007(112007,"提交失败，请重试"),
    E112008(112008,"无效的手机号"),
    E112009(112009,"无效的身份证号"),

    // 130 快应用相关
    E130001(130001,"账号或密码错误"),
    E130002(130002,"请登录"),

    // 140 私域业务
    E140001(140001,"产品已存在"),
    E140002(140002,"渠道已存在"),
    E140003(140003,"当日数据已存在"),

    // 150 权限相关
    E150001(150001, "职位不存在"),

    // 151 合同相关
    E151001(151001,"合同已存在"),

    //152 支付宝相关
    E152001(152001,"下单失败,请重新再试"),
    E152002(152002,"小程序授权失败,请重新授权"),
    ;

    private final int code;
    private final String msg;
}
