package com.ruoyi.common.constant;

/**
 * 广告引擎的Redis Key
 *
 * <AUTHOR>
 * @date 2021/9/17 10:18 上午
 */
public enum EngineRedisKeyFactory {

    K001("广告维度日数据UV去重"),
    K002("广告维度时段数据UV去重"),
    K003("广告媒体维度日数据UV去重"),
    K004("广告广告位维度日数据UV去重"),
    K005("素材广告位维度日数据UV去重"),
    K006("圈量AccessToken"),
    K007("圈量企微AccessToken"),
    K008("广告订单缓存"),
    K009("落地页表单提交记录分布式锁"),
    K010("摘星社回调防重复提交"),
    K011("活动返回挽留触发用户缓存"),
    K012("维度数据UV去重"),
    K013("广告主回调防重复提交"),
    K014("钉钉提醒沉默"),
    K015("业务开关"),
    K016("落地页表单提交分布式锁"),
    K017("支付宝域名访问缓存"),
    K018("落地页表单提交身份证限制"),
    K019("落地页表单提交手机号限制"),
    K020("落地页返回挽留触发用户缓存"),
    K021("白酒落地页表单提交记录分布式锁"),
    K022("数据ID缓存"),
    K023("埋点统计分布式锁"),
    K024("计数监控缓存"),
    K025("计数监控分布式锁"),
    K026("爱奇艺上报缓存"),
    K027("广告主每日表单自动回传数量缓存"),
    K028("落地页表单回传规则缓存"),
    K029("媒体收益API防重复提交"),
    K030("广告位切量计划缓存"),
    K031("广告位切量计划统计缓存"),
    K032("广告位切量计划用户去重缓存"),
    K033("外部回调接口参数缓存"),
    K034("广告位投放小时数据用户去重缓存"),
    K035("表单用户领红包分布式锁"),
    K036("表单用户领红包验证码缓存"),
    K037("表单用户领红包风控限制"),
    K038("直投广告券点击幂等限制"),
    K039("IP黑名单"),
    K040("地域表单分配数量(小时/日维度)"),
    K041("广告无预算停止投放分布式锁"),
    K042("广告主无余额停止投放分布式锁"),
    K043("短链日数据UV去重"),
    K044("用户广告券曝光点击缓存"),
    K045("广告主余额不足提醒分布式锁"),
    K046("身份证实名认证缓存"),
    K047("留资落地页表单提交记录分布式锁"),
    K048("用户ConsumeId缓存"),
    K049("广告主领取监控阈值"),
    K050("微信环境活动达到率统计"),
    K051("广告位维度落地页表单消耗统计"),
    K052("微信环境广告位域名访问统计"),
    K053("微信环境广告落地页巡查"),
    K054("微信支付防重复点击"),
    K055("微信支付留资转化防重限制"),
    K056("微信支付公众号AccessToken缓存"),
    K057("微信支付公众号jsapi_ticket缓存"),
    K058("微信支付公众号用户openid缓存"),
    K059("媒体上报数据API防重复提交"),
    K060("广告主无预算停止投放分布式锁"),
    K061("手机号验证码缓存"),
    K062("手机号发送验证码分布式锁"),
    K063("手机号发送验证码当日次数统计"),
    K064("活动皮肤预览"),
    K065("广告位维度落地页转化缓存"),
    K066("广告位上报价格累计增加"),
    K067("分布式锁"),
    K068("信用卡落地页表单提交身份证限制"),
    K069("信用卡落地页表单提交手机号限制"),
    K070("信用卡落地页表单防重复提交"),
    K071("留资短信企微任务缓存"),
    K072("设备唯一标识缓存"),
    K073("点击广告的设备缓存"),
    K074("上报媒体防重复提交"),
    K075("媒体广告点击监测标识和consumerId映射缓存"),
    K076("媒体广告点击监测标识和orderId映射缓存"),
    K077("广告位维度理论消耗：落地页转化*目标出价(不包含冷启动期)"),
    K078("广告位广告发券次数缓存(非精确，超过1000不更新)"),
    K079("广告位广告维度理论消耗：落地页转化*目标出价(包含冷启动期)"),
    K080("广告发券消耗累计"),
    K081("orientHourDataId缓存"),
    K082("配置消耗日维度数据"),
    K083("配置维度时段数据UV去重"),
    K084("用户广告券配置曝光点击缓存"),
    K085("广告位广告维度真实消耗缓存"),
    K086("广告消耗日维度数据"),
    K087("广告位维度真实消耗缓存"),
    K088("信息流投放账户和广告位关联"),
    K089("广告位多账户列表"),
    K090("微信域名待检测队列"),
    K091("域名微信状态缓存"),
    K092("微信防封链接缓存"),
    K093("IP缓存"),
    K094("ALAPI调用限制"),
    K095("用户当日已发券次数"),
    K096("企微囤粉小程序AccessToken缓存"),
    K097("企微囤粉unionId和订单号关联缓存"),
    K098("企微囤粉落地页领取/授权分布式锁"),
    K099("企微囤粉已加好友的外部联系人userid缓存"),
    K100("企微囤粉外部联系人unionId缓存"),
    K101("企微囤粉手机号缓存"),
    K102("企微囤粉外部联系人详情缓存"),
    K103("advertOrderLogId缓存"),
    K104("短链归因缓存"),
//    K105("ARPU预估发券量"),  // 弃用，遗留了一些永久key以后处理
    K106("运营商-联合会员防重点击"),
    K107("广告位活动维度时段数据UV去重"),
    K108("广告配置维度日数据缓存"),
    K109("文章UrlScheme缓存"),
    K110("文章缓存"),
    K111("文章请求PV缓存"),
    K112("预估CVR最小ID缓存"),
    K113("用户文章曝光列表缓存"),
    K114("默认文章缓存"),
    K115("文章聚合链接缓存"),
    K116("文章返回拦截次数缓存"),
    K117("号卡落地页手机号验证码缓存"),
    K118("号卡落地页手机号发送验证码分布式锁"),
    K119("号卡落地页手机号发送验证码当日次数统计"),
    K120("新增聚合链接分布式锁"),
    K121("灯火点击回调参数缓存"),
    K122("Sigmob参数缓存"),
    K123("公众号文章告警分布式锁"),
    K124("公众号文章信息"),
    K125("公众号文章全渠道监测开关"),
    K126("公众号文章媒体和文章映射"),
    K127("公众号文章媒体绑定文章分布式锁"),
    K128("公众号文章全渠道监测告警分布式锁"),
    K129("公众号文章实际阅读量查询分布式锁"),
    K130("公众号文章初始阅读量查询分布式锁"),
    K131("微信域名检测-哒哒限流分布式锁"),
    K132("微信域名检测去重分布式锁"),
    K133("微信域名检测-ALAPI限流分布式锁"),
    K134("广告位投放链接缓存"),
    K135("文章空白页域名切换"),
    K136("文章阅读量接口限流分布式锁"),
    K137("文章阅读量接口调用统计"),
    K138("文章阅读量接口调用缓存"),
    K139("文章阅读量接口剩余调用量"),
    K140("优惠券发放幂等处理"),
    K141("亿通行openId缓存"),
    K142("落地页手机号缓存"),
    K143("文章阅读自动补量"),
    K144("文章竞品链接去重"),
    K145("广告MAU去重-用户openId缓存"),
    K146("广告MAU去重-缓存有效标识"),
    K147("广告MAU去重-用户consumerId和openId映射"),
    K148("广告MAU去重-用户发券记录"),
    k149("丰巢文章缓存"),
    k150("丰巢聚合链接缓存"),
    k151("丰巢token缓存"),
    k152("丰巢文章请求日数据缓存"),
    k153("丰巢默认文章缓存"),
    k154("丰巢无文章可出")
    ;

    private static final String SPACE = "Eng";
    private static final String SEPARATOR = "_";

    private final String desc;

    EngineRedisKeyFactory(String desc){
        this.desc = desc;
    }

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg : args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    @Override
    public String toString() {
        return SPACE + SEPARATOR + this.name();
    }
}
