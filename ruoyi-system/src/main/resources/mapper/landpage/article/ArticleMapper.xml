<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.article.ArticleMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.article.ArticleEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="linkId" column="link_id"/>
            <result property="name" column="name"/>
            <result property="url" column="url"/>
            <result property="jumpUrl" column="jump_url"/>
            <result property="targetRequestPv" column="target_request_pv"/>
            <result property="initRequestPv" column="init_request_pv"/>
            <result property="actualRequestPv" column="actual_request_pv"/>
            <result property="syRequestPv" column="sy_request_pv"/>
            <result property="displayActualRequestPv" column="display_actual_request_pv"/>
            <result property="compensateRequestPv" column="compensate_request_pv"/>
            <result property="weight" column="weight"/>
            <result property="status" column="status"/>
            <result property="profile" column="profile"/>
            <result property="operatorId" column="operator_id"/>
            <result property="operatorName" column="operator_name"/>
            <result property="operatorTime" column="operator_time"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
            <result property="fcCheckStatus" column="fc_check_status"/>
            <result property="fcSyncStatus" column="fc_sync_status"/>
            <result property="fcRejectReason" column="fc_reject_reason"/>
            <result property="fcCheckTime" column="fc_check_time"/>
            <result property="fcSyncFailReason" column="fc_sync_fail_reason"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            link_id,
            `name`,
            url,
            jump_url,
            target_request_pv,
            init_request_pv,
            actual_request_pv,
            sy_request_pv,
            display_actual_request_pv,
            compensate_request_pv,
            weight,
            `status`,
            operator_id,
            operator_name,
            operator_time,
            gmt_create,
            gmt_modified,
            fc_check_status,
            fc_sync_status,
            fc_reject_reason,
            fc_check_time,
            fc_sync_fail_reason
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.bo.landpage.article.ArticleListParamBo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article
        <where>
            `status` != 1
            <if test="searchKey != null and searchKey != ''"> and (`name` like concat('%', #{searchKey}, '%') or `url` like concat('%', #{searchKey}, '%'))</if>
            <if test="linkId != null "> and link_id = #{linkId}</if>
        </where>
        order by id desc
    </select>

    <select id="selectListWithData" parameterType="com.ruoyi.system.bo.landpage.article.ArticleListParamBo" resultType="com.ruoyi.system.bo.landpage.article.ArticleListBo">
        SELECT
            a.id, a.link_id as linkId, a.`name`, a.url, a.target_request_pv as targetRequestPv, a.weight, a.`status`,
            a.init_request_pv as initRequestPv, a.actual_request_pv as actualRequestPv, a.sy_request_pv as syRequestPv, a.display_actual_request_pv as displayActualRequestPv, a.compensate_request_pv as compensateRequestPv,
            a.operator_id as operatorId, a.operator_name as operatorName, a.operator_time as operatorTime, a.gmt_create as gmtCreate, a.fc_sync_status as fcSyncStatus, a.fc_check_status as fcCheckStatus,
            ifnull(d.requestPv, 0) as requestPv, ifnull(d.requestUv, 0) as requestUv, if (a.target_request_pv + a.compensate_request_pv > ifnull(d.requestPv, 0) and a.status = 0 and a.weight > 0 and a.gmt_create >= curdate(), 1, 0 ) as `online`
        FROM tb_article a
        LEFT JOIN (
            SELECT article_id as articleId, ifnull(sum(request_pv), 0) as requestPv, ifnull(sum(request_uv), 0) as requestUv
            FROM tb_article_hour_data
            <where>
                cur_date = curdate()
                <if test="linkId != null "> and link_id = #{linkId}</if>
            </where>
            GROUP BY article_id
        ) d on a.id = d.articleId
        <where>
            a.`status` != 1
            <if test="searchKey != null and searchKey != ''"> and (a.`name` like concat('%', #{searchKey}, '%') or a.`url` like concat('%', #{searchKey}, '%'))</if>
            <if test="linkId != null "> and a.link_id = #{linkId}</if>
            <if test="ids != null and ids.size > 0">
                and a.id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="name != null and name != ''">
                and a.`name` like concat('%', #{name}, '%')
            </if>
            <if test="url != null and url != ''">
                and a.`url` like concat('%', #{url}, '%')
            </if>
            <if test="startDate != null "> and a.gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and a.gmt_create &lt;= #{endDate}</if>
        </where>
        order by `online` desc, a.id desc
    </select>

    <select id="selectStatisticData" parameterType="com.ruoyi.system.bo.landpage.article.ArticleListParamBo" resultType="com.ruoyi.system.bo.landpage.article.ArticleListBo">
        SELECT
            sum(a.target_request_pv) as targetRequestPv, sum(a.init_request_pv) as initRequestPv, sum(ifnull(d.requestPv, 0)) as requestPv, sum(ifnull(d.requestUv, 0)) as requestUv
        FROM tb_article a
        LEFT JOIN (
            SELECT article_id as articleId, ifnull(sum(request_pv), 0) as requestPv, ifnull(sum(request_uv), 0) as requestUv
            FROM tb_article_hour_data
            <where>
                cur_date = curdate()
                <if test="linkId != null "> and link_id = #{linkId}</if>
            </where>
            GROUP BY article_id
        ) d on a.id = d.articleId
        <where>
            a.`status` != 1
            <if test="searchKey != null and searchKey != ''"> and (a.`name` like concat('%', #{searchKey}, '%') or a.`url` like concat('%', #{searchKey}, '%'))</if>
            <if test="linkId != null "> and a.link_id = #{linkId}</if>
            <if test="ids != null and ids.size > 0">
                and a.id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="name != null and name != ''">
                and a.`name` like concat('%', #{name}, '%')
            </if>
            <if test="url != null and url != ''">
                and a.`url` like concat('%', #{url}, '%')
            </if>
            <if test="startDate != null "> and a.gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and a.gmt_create &lt;= #{endDate}</if>
        </where>
    </select>

    <select id="selectTodaySyIncrRequestPvSum" parameterType="Long" resultType="Integer">
        SELECT sum(sy_request_pv - init_request_pv)
        FROM tb_article
        <where>
            link_id = #{linkId}
            and gmt_create &gt;= curdate()
            and init_request_pv is not null
            and sy_request_pv is not null
        </where>
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.article.ArticleEntity">
        INSERT INTO tb_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="linkId != null">
                    link_id,
                </if>
                <if test="name != null">
                    `name`,
                </if>
                <if test="url != null">
                    url,
                </if>
                <if test="jumpUrl != null">
                    jump_url,
                </if>
                <if test="targetRequestPv != null">
                    target_request_pv,
                </if>
                <if test="initRequestPv != null">
                    init_request_pv,
                </if>
                <if test="weight != null">
                    weight,
                </if>
                <if test="status != null">
                    `status`,
                </if>
                <if test="operatorId != null">
                    operator_id,
                </if>
                <if test="operatorName != null">
                    operator_name,
                </if>
                <if test="operatorTime != null">
                    operator_time,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="linkId != null">
                    #{linkId},
                </if>
                <if test="name != null">
                    #{name},
                </if>
                <if test="url != null">
                    #{url},
                </if>
                <if test="jumpUrl != null">
                    #{jumpUrl},
                </if>
                <if test="targetRequestPv != null">
                    #{targetRequestPv},
                </if>
                <if test="initRequestPv != null">
                    #{initRequestPv},
                </if>
                <if test="weight != null">
                    #{weight},
                </if>
                <if test="status != null">
                    #{status},
                </if>
                <if test="operatorId != null">
                    #{operatorId},
                </if>
                <if test="operatorName != null">
                    #{operatorName},
                </if>
                <if test="operatorTime != null">
                    #{operatorTime},
                </if>
        </trim>
    </insert>

    <insert id="batchInsert">
        insert into tb_article(`link_id`,`name`,`url`,`jump_url`,`target_request_pv`,`weight`,`init_request_pv`,`operator_id`,`operator_name`)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.linkId}, #{entity.name}, #{entity.url}, #{entity.jumpUrl}, #{entity.targetRequestPv}, #{entity.weight}, #{entity.initRequestPv}, #{entity.operatorId}, #{entity.operatorName})
        </foreach>
    </insert>

    <update id="deleteById">
        UPDATE tb_article set status = 1 WHERE id=#{id}
    </update>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.article.ArticleEntity">
        UPDATE tb_article
        <set>
                    <if test="linkId != null">
                        link_id = #{linkId},
                    </if>
                    <if test="name != null">
                        `name` = #{name},
                    </if>
                    <if test="url != null">
                        url = #{url},
                    </if>
                    <if test="jumpUrl != null">
                        jump_url = #{jumpUrl},
                    </if>
                    <if test="targetRequestPv != null">
                        target_request_pv = #{targetRequestPv},
                    </if>
                    <if test="initRequestPv != null">
                        init_request_pv = #{initRequestPv},
                    </if>
                    <if test="actualRequestPv != null">
                        actual_request_pv = #{actualRequestPv},
                    </if>
                    <if test="syRequestPv != null">
                        sy_request_pv = #{syRequestPv},
                    </if>
                    <if test="displayActualRequestPv != null">
                        display_actual_request_pv = #{displayActualRequestPv},
                    </if>
                    <if test="compensateRequestPv != null">
                        compensate_request_pv = #{compensateRequestPv},
                    </if>
                    <if test="weight != null">
                        weight = #{weight},
                    </if>
                    <if test="status != null">
                        `status` = #{status},
                    </if>
                    <if test="profile != null">
                        `profile` = #{profile},
                    </if>
                    <if test="operatorId != null">
                        operator_id = #{operatorId},
                    </if>
                    <if test="operatorName != null">
                        operator_name = #{operatorName},
                    </if>
                    <if test="operatorTime != null">
                        operator_time = #{operatorTime},
                    </if>
        </set>
        WHERE id=#{id}
    </update>
    <update id="updateFcCheckStatus">
        UPDATE
            tb_article
        SET
            fc_check_status = #{checkStatus},
            fc_reject_reason = #{rejectReason},
            fc_check_time = now()
        WHERE
            id = #{articleId}
    </update>
    <update id="updateFcSyncStatus">
        UPDATE tb_article
        SET 
            fc_sync_status = #{syncStatus},
            fc_sync_fail_reason = #{syncFailReason}
        WHERE id = #{articleId}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article
        WHERE id = #{id}
    </select>

    <select id="selectWeightSum" resultType="Integer">
        SELECT ifnull(sum(weight), 0)
        FROM tb_article
        WHERE link_id = #{linkId} and `status` != 1
    </select>

    <select id="countByLinkId" resultType="com.ruoyi.system.bo.landpage.article.ArticleCountBo">
        SELECT link_id as linkId,
        count(1) as articleCount,
        count(gmt_create >= curdate() or null) as todayArticleCount,
        count(if(`status`= 0 and weight > 0 and `target_request_pv` > 0 and target_request_pv + compensate_request_pv > ifnull(d.request_pv, 0) and gmt_create >= curdate(), 1, null)) as onlineArticleCount,
            sum(target_request_pv) as articleTargetRequestPv
        FROM tb_article a
        left join (
            select article_id, sum(`request_pv`) as `request_pv`
            from `tb_article_hour_data`
            where `cur_date` = curdate() and `link_id` in
                <foreach collection="linkIds" item="linkId" open="(" separator="," close=")">
                    #{linkId}
                </foreach>
            GROUP BY article_id
        ) d on a.id = d.article_id
        <where>
            status = 0
            and `link_id` in
            <foreach collection="linkIds" item="linkId" open="(" separator="," close=")">
                #{linkId}
            </foreach>
        </where>
        GROUP BY link_id
    </select>

    <select id="selectArticleWithoutProfile" resultMap="BaseResultMap">
        SELECT id, url
        FROM tb_article
        WHERE `profile` = '' and `url` like 'https://mp.weixin.qq.com/%'
        order by id desc
        limit 1
    </select>

    <select id="selectBgZeroTodayArticleLinkId" resultType="java.lang.Long">
        SELECT distinct link_id
        from tb_article
        where status = 0 and gmt_create >= curdate()
    </select>
    <select id="selectListByLinkIds" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            tb_article
        WHERE
            status = 0
            AND link_id in
                <foreach collection="linkIds" item="linkId" open="(" separator="," close=")">
                    #{linkId}
                </foreach>
    </select>

    <select id="countFcStatusByLinkIds" resultType="com.ruoyi.system.bo.fc.FcArticleStatusCountBo">
        SELECT
            link_id as linkId,
            sum(CASE WHEN `fc_check_status` = 1 THEN 1 ELSE 0 END) AS approvedCount,
            sum(CASE WHEN `fc_check_status` = 2 THEN 1 ELSE 0 END) AS rejectedCount,
            sum(CASE WHEN `fc_sync_status` = 1 THEN 1 ELSE 0 END) AS syncedCount
        FROM
            tb_article
        WHERE
            link_id IN
            <foreach collection="linkIds" item="linkId" open="(" separator="," close=")">
                #{linkId}
            </foreach>
            AND status = 0
        GROUP BY
            link_id
    </select>

    <select id="hasFcSyncSuccessArticleByLinkId" resultType="java.lang.Boolean">
        SELECT
            CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END
        FROM
            tb_article
        WHERE
            link_id = #{linkId}
            AND fc_sync_status = 1
            AND status = 0
    </select>

    <select id="hasFcSyncSuccessArticleByLinkIds" resultType="java.lang.Boolean">
        SELECT
            CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END
        FROM
            tb_article
        WHERE
            link_id IN
            <foreach collection="linkIds" item="linkId" open="(" separator="," close=")">
                #{linkId}
            </foreach>
            AND fc_sync_status = 1
            AND status = 0
    </select>
</mapper>
