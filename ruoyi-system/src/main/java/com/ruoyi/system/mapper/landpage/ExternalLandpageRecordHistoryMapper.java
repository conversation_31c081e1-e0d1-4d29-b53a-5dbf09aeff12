package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.landpage.ExternalLandpageRecordHistoryEntity;
import com.ruoyi.system.vo.datashow.CrmExternalLandpageRecordHistoryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 外部落地页表单修改历史 Mapper
 *
 * <AUTHOR>
 * @date 2023-4-11 15:38:42
 */
public interface ExternalLandpageRecordHistoryMapper {

    /**
     * 新增记录
     */
    int insert(ExternalLandpageRecordHistoryEntity externalLandpageRecordHistoryEntity);

    /**
     * 根据id更新
     */
    int updateById(ExternalLandpageRecordHistoryEntity externalLandpageRecordHistoryEntity);

    /**
     * 根据id获取
     */
    ExternalLandpageRecordHistoryEntity selectById(Long id);

    /**
     * 根据表单记录ID列表查询修改历史
     *
     * @param originRecordIds 记录ID列表
     * @return 修改历史列表
     */
    List<CrmExternalLandpageRecordHistoryVO> selectListByOriginRecordId(@Param("list") List<Long> originRecordIds);
}
