package com.ruoyi.system.mapper.traffic;

import com.ruoyi.system.bo.traffic.TrafficPackageAdvertCountBo;
import com.ruoyi.system.entity.traffic.AdvertOrientTrafficEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告定向流量包表 Mapper
 *
 * <AUTHOR>
 * @date 2022-8-24 10:54:06
 */
public interface AdvertOrientTrafficMapper {

    /**
     * 新增记录
     */
    int batchInsert(List<AdvertOrientTrafficEntity> entities);

    /**
     * 根据广告定向配置ID删除
     */
    int deleteByOrientId(Long orientId);

    /**
     * 根据id获取
     */
    AdvertOrientTrafficEntity selectById(Long id);

    /**
     * 统计使用流量包的广告数量
     *
     * @param trafficPackageIds 流量包ID列表
     * @return 流量包广告数量列表
     */
    List<TrafficPackageAdvertCountBo> countByTrafficPackageId(@Param("trafficPackageIds") List<Long> trafficPackageIds);

    /**
     * 根据流量包ID查询定向该流量包的广告ID列表
     *
     * @param trafficPackageId 流量包ID
     * @return 广告ID列表
     */
    List<Long> selectAdvertIdByTrafficPackageId(@Param("trafficPackageId") Long trafficPackageId);

    /**
     * 根据广告定向配置ID查询定向的流量包ID
     *
     * @param orientId 广告定向配置ID
     * @return 流量包ID列表
     */
    List<Long> selectTrafficPackageIdByOrientId(@Param("orientId") Long orientId);

    /**
     * 根据广告ID查询定向的流量包明细
     *
     * @param advertIds 广告ID列表
     * @return 流量包定向列表
     */
    List<AdvertOrientTrafficEntity> selectByAdvertIds(@Param("advertIds") List<Long> advertIds);

    /**
     * 根据配置ID查询定向的流量包明细
     *
     * @param orientIds 配置ID列表
     * @return 流量包定向列表
     */
    List<AdvertOrientTrafficEntity> selectByOrientIds(@Param("orientIds") List<Long> orientIds);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();
}
