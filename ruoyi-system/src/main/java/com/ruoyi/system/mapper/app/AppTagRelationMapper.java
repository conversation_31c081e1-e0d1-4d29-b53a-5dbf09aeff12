package com.ruoyi.system.mapper.app;

import com.ruoyi.system.entity.apptag.AppTagRelationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 媒体标签关联表 Mapper
 *
 * <AUTHOR>
 * @date 2022-9-23 16:00:26
 */
public interface AppTagRelationMapper {
    /**
     * 新增记录
     */
    int insert(AppTagRelationEntity appTagRelationEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(AppTagRelationEntity appTagRelationEntity);

    /**
     * 根据id获取
     */
    AppTagRelationEntity selectById(Long id);


    /**
     * 根据标签id列表查询被使用的标签id列表 ，删除标签时判断用
     * @param tagIds
     * @return
     */
    List<Long> selectTagIdsByTagIds(@Param("tagIds") List<Long> tagIds);

    /**
     * 根据标签id查询使用的总数
     * @param tagId
     * @return
     */
    int countByTagId(@Param("tagId") Long tagId);

    /**
     * 根据媒体id查询标签列表
     *
     * @param appId 媒体ID
     * @return 标签ID列表
     */
    List<Long> selectTagIdsByAppId(Long appId);

    /**
     * 根据媒体id删除标签
     * @param appId
     * @return
     */
    int deleteByAppId(Long appId);

    /**
     * 批量新增
     * @param entities
     * @return
     */
    int batchInsert(@Param("entities") List<AppTagRelationEntity> entities);

    /**
     * 根据标签ID查询媒体ID列表
     *
     * @param tagIds 标签ID列表
     * @return 媒体ID列表
     */
    List<Long> selectAppIdsByTagIds(@Param("tagIds") List<Long> tagIds);
}
