package com.ruoyi.system.mapper.system;

import org.apache.ibatis.annotations.Param;

/**
 * IdMaker
 */
public interface IdMakerMapper {

    /**
     * insert
     *
     * @param bizType 业务类型
     * @param init 初始值
     */
    int insert(@Param("bizType") String bizType, @Param("init") long init);

    /**
     * incrBy
     *
     * @param bizType 业务类型
     * @param num 增长步长
     */
    int incrBy(@Param("bizType") String bizType, @Param("num") long num);

    /**
     * selectForUpdate
     *
     * @param bizType 业务类型
     * @returne 当前步数
     */
    Long selectForUpdate(@Param("bizType") String bizType);
}
