package com.ruoyi.system.mapper.sms;

import com.ruoyi.system.entity.sms.LiuziSmsSendRecordCountBO;
import com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 留资短信发送记录表 Mapper
 *
 * <AUTHOR>
 * @date 2022-12-5 10:09:17
 */
public interface LiuziSmsSendRecordMapper {
    /**
     * 新增记录
     */
    int insert(LiuziSmsSendRecordEntity liuziSmsSendRecordEntity);

    /**
     * 批量新增
     * @param entities
     * @return
     */
    int batchInsert(@Param("entities") List<LiuziSmsSendRecordEntity> entities);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(LiuziSmsSendRecordEntity liuziSmsSendRecordEntity);

    /**
     * 根据id获取
     */
    LiuziSmsSendRecordEntity selectById(Long id);


    /**
     * 根据留资记录id查询
     * @param liuziRecordId
     * @return
     */
    List<LiuziSmsSendRecordEntity> selectListByLiuziRecordId(@Param("liuziRecordId") Long liuziRecordId);

    /**
     * 根据留资记录id列表查询短信发送记录
     *
     * @param liuziRecordIds 留资记录ID列表
     * @return 短信发送记录
     */
    List<LiuziSmsSendRecordEntity> selectListByLiuziRecordIds(@Param("liuziRecordIds") List<Long> liuziRecordIds);

    /**
     * 根据留资记录id列表查询短信条数
     * @param liuziRecordIds
     * @return
     */
    List<LiuziSmsSendRecordCountBO> countByLiuziRecordIds(@Param("liuziRecordIds") List<Long> liuziRecordIds);


    /**
     * 根据id更新状态
     * @param id
     * @param status
     * @return
     */
    Boolean updateSmsStatusById(@Param("id") Long id,@Param("status") Integer status);
    /**
     * 根据渠道和消息id更新短信状态
     * @param type 渠道
     * @see com.ruoyi.common.enums.SmsChannelEnum
     * @param msgId 消息id
     * @param status 状态
     * @return
     */
    Boolean updateSmsStatusByMsgIdAndType(@Param("type") Integer type,@Param("msgId") String msgId,@Param("status") Integer status);

    /**
     * 根据渠道和消息id查询短信发送记录
     * @param type
     * @param msgId
     * @return
     */
    LiuziSmsSendRecordEntity selectByTypeAndMsgId(@Param("type") Integer type,@Param("msgId") String msgId);

}
