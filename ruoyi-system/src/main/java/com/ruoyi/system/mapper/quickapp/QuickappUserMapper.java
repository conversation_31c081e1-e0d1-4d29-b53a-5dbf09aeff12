package com.ruoyi.system.mapper.quickapp;

import com.ruoyi.system.entity.quickapp.QuickappUserEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 快应用用户表 Mapper
 *
 * <AUTHOR>
 * @date 2022-8-8 10:57:00
 */
public interface QuickappUserMapper {
    /**
     * 新增记录
     */
    int insert(QuickappUserEntity quickappUserEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(QuickappUserEntity quickappUserEntity);

    /**
     * 根据id获取
     */
    QuickappUserEntity selectById(Long id);

    /**
     * 根据邮箱获取用户信息
     *
     * @param email
     * @return
     */
    QuickappUserEntity selectByEmail(@Param("email") String email);

    /**
     * 根据用户id列表查询用户信息
     * @param userIds
     * @return
     */
    List<QuickappUserEntity> selectByUserIds(@Param("userIds") List<Long> userIds);
}
