package com.ruoyi.system.mapper.fc;

import com.ruoyi.system.entity.fc.FcLinkEntity;
import com.ruoyi.system.req.fc.GetArticleListReq;
import com.ruoyi.system.vo.fc.FcArticleVo;
import com.ruoyi.system.vo.fc.FcLinkVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

/**
 * 丰巢聚合链接表(FcLink)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-05 15:37:55
 */
public interface FcLinkMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    FcLinkEntity queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param fcLinkEntity   查询条件
     * @param pageable 分页对象
     * @return 对象列表
     */
    List<FcLinkEntity> queryAllByLimit(FcLinkEntity fcLinkEntity, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param fcLinkEntity 查询条件
     * @return 总行数
     */
    long count(FcLinkEntity fcLinkEntity);

    /**
     * 新增数据
     *
     * @param fcLinkEntity 实例对象
     * @return 影响行数
     */
    int insert(FcLinkEntity fcLinkEntity);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<FcLink> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<FcLinkEntity> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<FcLink> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<FcLinkEntity> entities);

    /**
     * 修改数据
     *
     * @param fcLinkEntity 实例对象
     * @return 影响行数
     */
    int update(FcLinkEntity fcLinkEntity);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 获取所有的key
     * @return
     */
    Set<String> selectTotalKey();

    /**
     * 更新status
     * @param id
     * @param status
     */
    void updateStatusById(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 根据status获取 key
     * @param status
     * @return
     */
    List<String> getFcLinkKeysByStatus(@Param("status") Integer status);

    /**
     * 获取文章列表
     * @param req
     * @return
     */
    List<FcArticleVo> getArticleByStatusAndTime(GetArticleListReq req);

    /**
     * 查询丰巢链接列表（id、key、url、name），按id降序排列
     * @param searchKey 搜索关键字
     * @return 丰巢链接列表
     */
    List<FcLinkVo> getFcLinkListOrderByIdDesc(@Param("searchKey") String searchKey);

    /**
     * 根据文章聚合链接id列表查询对应的丰巢链接详细信息
     * @param articleAggrLinkIdList 文章聚合链接id列表
     * @return 丰巢链接列表
     */
    List<FcLinkVo> getFcLinkInfoByArticleAggrLinkIdList(@Param("articleAggrLinkIdList") List<Long> articleAggrLinkIdList, @Param("fcLinkIdList") List<Long> fcLinkIdList);

    /**
     * 根据key查询丰巢链接
     * @param key 丰巢链接key
     * @return 丰巢链接实体
     */
    FcLinkEntity queryByKey(@Param("key") String key);

}

