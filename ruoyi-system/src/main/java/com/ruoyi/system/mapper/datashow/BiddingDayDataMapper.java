package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.slot.BiddingDayDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 投流日数据 Mapper
 *
 * <AUTHOR>
 * @date 2023-7-31 11:55:54
 */
public interface BiddingDayDataMapper {

    /**
     * 新增记录
     */
    int insert(BiddingDayDataEntity biddingDayDataEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(BiddingDayDataEntity biddingDayDataEntity);

    /**
     * 投流上报数累加
     *
     * @param id 数据ID
     */
    int incrConv(Long id);

    /**
     * 根据id获取
     */
    BiddingDayDataEntity selectById(Long id);

    /**
     * 根据参数获取
     */
    BiddingDayDataEntity selectBy(@Param("curDate") Date curDate, @Param("slotId") Long slotId, @Param("advertiserId") String advertiserId);

    /**
     * 统计广告位_日期维度的投流数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param slotIds 广告位ID列表
     * @return 投流数据列表
     */
    List<BiddingDayDataEntity> countByDateAndSlotIds(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("slotIds") List<Long> slotIds);

    /**
     * 投流数据汇总
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param slotIds 广告位ID列表
     * @return 投流数据汇总
     */
    BiddingDayDataEntity sumByDateAndSlotIds(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("slotIds") List<Long> slotIds);
}
