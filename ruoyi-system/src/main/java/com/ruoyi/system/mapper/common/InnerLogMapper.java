package com.ruoyi.system.mapper.common;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.entity.common.InnerLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务日志Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@DataSource(DataSourceType.LOG)
public interface InnerLogMapper {

    /**
     * 新增业务日志
     *
     * @param record 业务日志
     * @return 影响行数
     */
    int insert(InnerLogEntity record);

    /**
     * 批量新增业务日志
     *
     * @param tbSuffix 表后缀
     * @param records 业务日志列表
     * @return 影响行数
     */
    int batchInsert(@Param("tbSuffix") String tbSuffix, @Param("records") List<InnerLogEntity> records);

    /**
     * 创建表
     *
     * @param date 日期
     */
    void createTable(@Param("date") String date);

    /**
     * 删除表
     *
     * @param date 日期
     */
    void dropTable(@Param("date") String date);
}
