package com.ruoyi.system.mapper.manager;

import java.util.List;
import com.ruoyi.system.entity.activity.ActivitySkin;
import org.apache.ibatis.annotations.Param;

/**
 * 活动皮肤Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
public interface ActivitySkinMapper
{
    /**
     * 查询活动皮肤
     *
     * @param id 活动皮肤ID
     * @return 活动皮肤
     */
    ActivitySkin selectActivitySkinById(Long id);

    /**
     * 查询活动皮肤
     *
     * @param skinCode 皮肤标识
     * @return 活动皮肤
     */
    ActivitySkin selectBySkinCode(@Param("skinCode") String skinCode);

    /**
     * 查询活动皮肤名称
     *
     * @param skinCode 皮肤标识
     * @return 活动皮肤名称
     */
    String selectSkinNameBySkinCode(@Param("skinCode") String skinCode);

    /**
     * 查询活动皮肤列表
     *
     * @param activitySkin 活动皮肤
     * @return 活动皮肤集合
     */
    List<ActivitySkin> selectActivitySkinList(ActivitySkin activitySkin);

    /**
     * 查询活动皮肤列表
     *
     * @param activitySkin 活动皮肤
     * @return 活动皮肤集合
     */
    List<ActivitySkin> selectSimpleActivitySkinList(ActivitySkin activitySkin);

    /**
     * 新增活动皮肤
     *
     * @param activitySkin 活动皮肤
     * @return 结果
     */
    int insertActivitySkin(ActivitySkin activitySkin);

    /**
     * 修改活动皮肤
     *
     * @param activitySkin 活动皮肤
     * @return 结果
     */
    int updateActivitySkin(ActivitySkin activitySkin);
}
