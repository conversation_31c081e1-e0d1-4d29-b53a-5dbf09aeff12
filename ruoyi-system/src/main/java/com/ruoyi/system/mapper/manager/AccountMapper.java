package com.ruoyi.system.mapper.manager;

import java.util.List;
import com.ruoyi.system.entity.account.Account;
import org.apache.ibatis.annotations.Param;

/**
 * 账号Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface AccountMapper {

    /**
     * 查询账号
     *
     * @param id 账号ID
     * @return 账号
     */
    Account selectAccountById(Long id);

    /**
     * 查询账号(登录用)
     *
     * @param account 账号
     * @return 账号
     */
    Account selectAccountForLogin(Account account);

    /**
     * 查询账号列表
     *
     * @param account 账号
     * @return 账号集合
     */
    List<Account> selectAccountList(Account account);

    /**
     * 查询代理商及广告主账号列表
     *
     * @param account 账号
     * @return 账号列表
     */
    List<Account> selectAgentAdvertiserList(Account account);

    /**
     * 查询账号补充信息
     *
     * @param account 账号
     * @return 账号集合
     */
    List<Account> selectAccountExtInfo(Account account);

    /**
     * 根据ID批量查询账号列表
     *
     * @param ids 账号ID列表
     * @return 账号集合
     */
    List<Account> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 新增账号
     *
     * @param account 账号
     * @return 结果
     */
    int insertAccount(Account account);

    /**
     * 修改账号
     *
     * @param account 账号
     * @return 结果
     */
    int updateAccount(Account account);

    /**
     * 检查公司名称是否已存在
     *
     * @param companyName 公司名称
     * @param mainType 主体类型
     * @return 是否已存在(1.是,0.否)
     */
    int checkCompanyNameUnique(@Param("companyName") String companyName, @Param("mainType") Integer mainType);

    /**
     * 检查邮箱是否已存在
     *
     * @param email 邮箱
     * @param mainType 主体类型
     * @return 是否已存在(1.是,0.否)
     */
    int checkEmailUnique(@Param("email") String email, @Param("mainType") Integer mainType);

    /**
     * 检查手机号是否已存在
     *
     * @param phone 手机号
     * @param mainType 主体类型
     * @return 是否已存在(1.是,0.否)
     */
    int checkPhoneUnique(@Param("phone") String phone, @Param("mainType") Integer mainType);

    /**
     * 查询账号列表
     *
     * @param account 账号
     * @return 账号列表
     */
    List<Account> selectSimpleAccountList(Account account);

    /**
     * 根据公司名称和主体类型查询账号ID
     *
     * @param companyName 公司名称
     * @param mainType 主体类型
     * @return 账号ID列表
     */
    List<Long> selectIdByCompanyName(@Param("companyName") String companyName, @Param("mainType") Integer mainType);

    /**
     * 根据邮箱和主体类型查询账号ID
     *
     * @param email 邮箱
     * @param mainType 主体类型
     * @return 账号ID列表
     */
    List<Long> selectIdByEmail(@Param("email") String email, @Param("mainType") Integer mainType);

    /**
     * 根据公司名称/邮箱查询账号ID列表
     *
     * @param accountSearch 搜索条件
     * @param mainType 账号类型
     * @return 账号ID列表
     */
    List<Long> selectIdsByEmailOrCompanyName(@Param("accountSearch") String accountSearch, @Param("mainType") Integer mainType);

    /**
     * 根据邮箱或者id搜索 账号id
     * @param accountSearch 搜索条件
     * @return 账号id列表
     */
    List<Long> selectIdsByIdOrEmail(@Param("accountSearch") String accountSearch);

    /**
     * 根据邮箱或者id或公司搜索 账号id
     *
     * @param accountSearch 搜索条件
     * @return 账号id列表
     */
    List<Long> selectIdsByIdOrEmailOrCompanyName(@Param("accountSearch") String accountSearch);

    /**
     * 根据邮箱或者id搜索 账号id
     * @param accountSearch 搜索条件
     * @return 账号id列表
     */
    List<Long> selectIdsByIdOrEmailAndCompany(@Param("accountSearch") String accountSearch,@Param("companyName")String companyName);

    /**
     * 过滤出状态正常的账号
     *
     * @param ids 账号ID列表
     * @return 状态正常的账号ID列表
     */
    List<Long> filterNormalAccountByIds(@Param("ids") List<Long> ids);

    /**
     * 根据email获取CRM用户信息
     *
     * @param email 邮箱
     * @return 用户信息
     */
    Account selectCrmUserByEmail(String email);

    /**
     * 根据邮箱获取CRM用户
     *
     * @param emails 邮箱列表
     * @return 用户账号ID列表
     */
    List<Long> selectCrmUserIdsByEmail(@Param("emails") List<String> emails);
}
