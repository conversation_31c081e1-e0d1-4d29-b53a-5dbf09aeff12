package com.ruoyi.system.mapper.shorturl;

import com.ruoyi.system.entity.shorturl.ShortUrlEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 短链表 Mapper
 *
 * <AUTHOR>
 * @date 2022-10-8 14:16:35
 */
public interface ShortUrlMapper {

    /**
     * 新增记录
     */
    int insert(ShortUrlEntity shortUrlEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(ShortUrlEntity shortUrlEntity);

    /**
     * 根据id获取
     */
    ShortUrlEntity selectById(Long id);

    /**
     * 查询短链
     */
    ShortUrlEntity selectBy(ShortUrlEntity param);

    /**
     * 查询列表
     */
    List<ShortUrlEntity> selectList(ShortUrlEntity param);

    /**
     * 根据id列表查询短链信息
     * @param ids
     * @return
     */
    List<ShortUrlEntity> selectListByIds(@Param("ids") List<Long> ids);

    /**
     * 根据原链接md5列表查询
     * @param originUrlMd5List
     * @return
     */
    List<ShortUrlEntity> selectByOriginUrlMd5List(@Param("list") List<String> originUrlMd5List);

    /**
     * 批量新增
     * @param shortUrlEntities
     * @return
     */
    Boolean insertBatch(@Param("list") List<ShortUrlEntity> shortUrlEntities);
    /**
     * 根据ID批量更新对应数据
     * @param shortUrlEntities
     * @return
     */
    Boolean updateBatchById(@Param("list") List<ShortUrlEntity> shortUrlEntities);

}