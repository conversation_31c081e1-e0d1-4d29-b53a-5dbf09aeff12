package com.ruoyi.system.mapper.landpage.article;

import com.ruoyi.system.bo.landpage.article.ArticleRetDataUpdateParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleRetDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 文章返回拦截数据表 Mapper
 *
 * <AUTHOR>
 * @date 2024-2-29 17:39:13
 */
public interface ArticleRetDataMapper {

    /**
     * 新增记录
     */
    int insert(ArticleRetDataEntity articleRetDataEntity);

    /**
     * 根据id更新
     */
    int updateById(ArticleRetDataEntity articleRetDataEntity);

    /**
     * 根据id获取
     */
    ArticleRetDataEntity selectById(Long id);

    /**
     * 查询返回拦截数据
     */
    ArticleRetDataEntity selectBy(@Param("curDate") Date curDate, @Param("linkId") Long linkId, @Param("retUrlMd5") String retUrlMd5);

    /**
     * 更新数据
     */
    int update(ArticleRetDataUpdateParamBo param);
}
