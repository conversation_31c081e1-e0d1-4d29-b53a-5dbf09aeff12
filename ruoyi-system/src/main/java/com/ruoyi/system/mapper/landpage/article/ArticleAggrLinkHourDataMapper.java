package com.ruoyi.system.mapper.landpage.article;

import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataUpdateParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkHourDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 文章聚合链接时段数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:58
 */
public interface ArticleAggrLinkHourDataMapper {

    /**
     * 新增记录
     */
    int insert(ArticleAggrLinkHourDataEntity articleAggrLinkHourDataEntity);

    /**
     * 根据id更新
     */
    int updateById(ArticleAggrLinkHourDataEntity articleAggrLinkHourDataEntity);

    /**
     * 更新数据
     */
    int update(ArticleAggrLinkDataUpdateParamBo param);

    /**
     * 根据id获取
     */
    ArticleAggrLinkHourDataEntity selectById(Long id);

    /**
     * 查询聚合链接数据
     */
    ArticleAggrLinkHourDataEntity selectBy(@Param("curDate") Date curDate, @Param("curHour") Integer curHour, @Param("linkId") Long linkId);

    /**
     * 根据条件查询汇总数据
     */
    ArticleAggrLinkDataBo selectSumBy(ArticleAggrLinkDataParamBo param);

    /**
     * 根据条件查询日维度数据
     */
    List<ArticleAggrLinkDataBo> selectDayDataBy(ArticleAggrLinkDataParamBo param);
}
