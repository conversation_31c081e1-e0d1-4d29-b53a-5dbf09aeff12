package com.ruoyi.system.mapper.oa.staff;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.bo.account.PostStaffNumBo;
import com.ruoyi.system.entity.oa.staff.StaffInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * oa员工信息表 Mapper
 *
 * <AUTHOR>
 * @date 2021-10-9 14:14:04
 */
@DataSource(DataSourceType.OA)
public interface StaffInfoMapper {

    /**
     * 根据账号id获取
     *
     * @param userId 用户id
     * @return 结果
     */
    StaffInfoEntity selectByUserId(Long userId);

    /**
     * 根据部门id获取员工列表
     *
     * @param departmentId 部门id
     * @param companyId    公司id
     * @return 员工列表
     */
    List<StaffInfoEntity> selectByDepartmentId(@Param("companyId") Long companyId, @Param("departmentId") Long departmentId);

    /**
     * 根据用户id列表查询员工信息
     *
     * @param userIds 员工Id列表
     * @return 员工列表
     */
    List<StaffInfoEntity> selectByUserIds(@Param("userIds") List<Long> userIds);

    /**
     * 根据职位id查询每个职位对应的员工数量
     *
     * @param postIds 职位Id列表
     * @return 员工数量列表
     */
    List<PostStaffNumBo> selectPostStaffNumByPostIds(@Param("postIds") List<Long> postIds);

    /**
     * 根据手机号查询商务信息
     * @param phone
     * @return
     */
    StaffInfoEntity selectByPhone(String phone);

    /**
     * 查询部门下的员工邮箱列表
     *
     * @param departmentIds 部门ID列表
     * @return 员工邮箱列表
     */
    List<String> selectEmailByDepartmentIds(@Param("departmentIds") List<Long> departmentIds);
}
