package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.bo.slotactivitydata.SlotActivityDataSumBo;
import com.ruoyi.system.entity.datashow.SlotActivityData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告位维度活动数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-21
 */
public interface SlotActivityDataMapper {

    /**
     * 查询广告位维度活动数据
     *
     * @param slotId 广告位ID
     * @param activityId 活动ID
     * @param curDate 日期
     * @return 广告位数据
     */
    SlotActivityData selectBy(@Param("slotId") Long slotId, @Param("activityId") Long activityId, @Param("curDate") Date curDate);

    /**
     * 查询广告位维度活动数据
     *
     * @param id 广告位维度活动数据ID
     * @return 广告位维度活动数据
     */
    SlotActivityData selectSlotActivityDataById(String id);

    /**
     * 查询广告位维度活动数据列表
     *
     * @param slotActivityData 广告位维度活动数据
     * @return 广告位维度活动数据集合
     */
    List<SlotActivityData> selectSlotActivityDataList(SlotActivityData slotActivityData);

    /**
     * 新增广告位维度活动数据
     *
     * @param slotActivityData 广告位维度活动数据
     * @return 结果
     */
    int insertSlotActivityData(SlotActivityData slotActivityData);

    /**
     * 修改广告位维度活动数据
     *
     * @param slotActivityData 广告位维度活动数据
     * @return 结果
     */
    int updateSlotActivityData(SlotActivityData slotActivityData);

    /**
     * 根据广告位id列表查询指定时间段内的活动数据统计 ,根据广告位id和日期分组
     * @param slotIds 广告位id列表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 结果
     */
    List<SlotActivityData> selectSumSlotActivityDataBySlotIdsAndDate(@Param("slotIds") List<Long> slotIds, @Param("startDate") Date startDate , @Param("endDate") Date endDate);

    /**
     * 根据广告位ID列表和时间段查询活动数据总和
     *
     * @param slotIds 广告位id列表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 活动数据总和
     */
    SlotActivityData sumSlotActivityDataBySlotIdsAndDate(@Param("slotIds") List<Long> slotIds, @Param("startDate") Date startDate , @Param("endDate") Date endDate);

    /**
     * 统计广告位活动数据 根据广告位分组总计
     * @param slotIds 广告位id列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 总计
     */
    List<SlotActivityDataSumBo> selectSumSlotActivityDataGroupBySlot(@Param("slotIds") List<Long> slotIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
    /**
     * 统计广告位活动数据 总计
     * @param slotIds 广告位id列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 总计
     */
    SlotActivityDataSumBo selectSlotActivityDataSum(@Param("slotIds") List<Long> slotIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询广告位活动数据
     * @param slotIds 广告位id列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果
     */
    List<SlotActivityData> selectSlotActivityData(@Param("slotIds") List<Long> slotIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    /**
     * 根据媒体id和日期查询广告位活动数据
     * @param appId
     * @param curDate
     * @return
     */
    List<SlotActivityData> selectDataByAppIdAndDate(@Param("appId") Long appId, @Param("curDate") Date curDate);
}
