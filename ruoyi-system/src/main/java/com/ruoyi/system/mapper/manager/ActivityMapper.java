package com.ruoyi.system.mapper.manager;

import java.util.List;
import com.ruoyi.system.entity.activity.Activity;

/**
 * 活动工具Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
public interface ActivityMapper {

    /**
     * 查询活动工具
     *
     * @param id 活动工具ID
     * @return 活动工具
     */
    Activity selectActivityById(Long id);

    /**
     * 查询活动工具的皮肤标识
     *
     * @param id 活动工具ID
     * @return 皮肤标识
     */
    String selectSkinCodeById(Long id);

    /**
     * 查询活动工具列表
     *
     * @param activity 活动工具
     * @return 活动工具集合
     */
    List<Activity> selectActivityList(Activity activity);

    /**
     * 查询活动工具列表(简单信息)
     *
     * @param param 参数
     * @return 活动工具列表
     */
    List<Activity> selectSimpleActivityList(Activity param);

    /**
     * 新增活动工具
     *
     * @param activity 活动工具
     * @return 结果
     */
    int insertActivity(Activity activity);

    /**
     * 修改活动工具
     *
     * @param activity 活动工具
     * @return 结果
     */
    int updateActivity(Activity activity);

    /**
     * 查询所有开启推广计划的活动
     *
     * @return 活动列表
     */
    List<Activity> selectTotalOpenActivity();
}
