package com.ruoyi.system.mapper.convert;

import com.ruoyi.system.entity.convert.ConvertUploadRuleEntity;
import com.ruoyi.system.req.manager.ConvertUploadRuleListReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 媒体转化上报规则 Mapper
 *
 * <AUTHOR>
 * @date 2022-10-19 10:30:13
 */
public interface ConvertUploadRuleMapper {

    /**
     * 查询列表
     */
    List<ConvertUploadRuleEntity> selectList(ConvertUploadRuleListReq req);

    /**
     * 查询历史修改记录
     */
    List<ConvertUploadRuleEntity> selectHistory(@Param("slotId") Long slotId);

    /**
     * 根据广告位ID查询
     */
    ConvertUploadRuleEntity selectBySlotId(@Param("slotId") Long slotId);

    /**
     * 新增记录
     */
    int insert(ConvertUploadRuleEntity entity);

    /**
     * 更新记录
     */
    int update(ConvertUploadRuleEntity entity);
}
