package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.bo.landpage.ExternalLandpageRecordBo;
import com.ruoyi.system.bo.landpage.ExternalLandpageRecordSelectBo;
import com.ruoyi.system.entity.landpage.ExternalLandpageRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 外部落地页表单记录Mapper接口
 *
 * <AUTHOR>
 * @date 2021-10-09
 */
public interface ExternalLandpageRecordMapper {

    /**
     * 查询外部落地页表单记录
     *
     * @param id 外部落地页表单记录ID
     * @return 外部落地页表单记录
     */
    ExternalLandpageRecord selectExternalLandpageRecordById(Long id);

    /**
     * 查询列表
     *
     * @param param 查询条件
     * @return 表单列表
     */
    List<ExternalLandpageRecordBo> selectList(ExternalLandpageRecordSelectBo param);

    /**
     * 查询广告主ID列表
     *
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIds();

    /**
     * 查询代理商ID列表
     *
     * @return 代理商ID列表
     */
    List<Long> selectAgentIds();

    /**
     * 新增外部落地页表单记录
     *
     * @param externalLandpageRecord 外部落地页表单记录
     * @return 结果
     */
    int insertExternalLandpageRecord(ExternalLandpageRecord externalLandpageRecord);

    /**
     * 修改记录
     *
     * @param externalLandpageRecord 外部落地页表单记录
     * @return 结果
     */
    int updateExternalLandpageRecord(ExternalLandpageRecord externalLandpageRecord);

    /**
     * 批量新增外部落地页表单
     *
     * @param entities 表单列表
     * @return 结果
     */
    int batchInsert(@Param("entities") List<ExternalLandpageRecord> entities);

    /**
     * 批量更新导出状态
     *
     * @param ids 表单ID列表
     * @return 是否更新成功
     */
    int batchUpdateExportStatus(@Param("ids") List<Long> ids);

    /**
     * 查询外部表单编号
     *
     * @param externalNoList 外部表单编号列表
     * @return 存在的外部表单编号列表
     */
    List<String> selectExternalNo(@Param("list") List<String> externalNoList);

    /**
     * 查询外部表单列表
     *
     * @param externalNoList 外部表单编号列表
     * @return 外部表单列表
     */
    List<ExternalLandpageRecord> selectListByExternalNo(@Param("list") List<String> externalNoList);
}
