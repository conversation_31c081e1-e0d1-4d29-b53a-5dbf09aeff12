package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.bo.landpage.QwtfLandpageFormRecordSelectBo;
import com.ruoyi.system.entity.landpage.QwtfLandpageFormRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企微囤粉表单记录 Mapper
 *
 * <AUTHOR>
 * @date 2023-9-22 15:38:47
 */
public interface QwtfLandpageFormRecordMapper {

    /**
     * 新增记录
     */
    int insert(QwtfLandpageFormRecordEntity qwtfLandpageFormRecordEntity);

    /**
     * 查询表单列表
     *
     * @param param 查询条件
     * @return 表单列表
     */
    List<QwtfLandpageFormRecordEntity> selectList(QwtfLandpageFormRecordSelectBo param);

    /**
     * 根据id更新
     */
    int updateById(QwtfLandpageFormRecordEntity qwtfLandpageFormRecordEntity);

    /**
     * 批量更新好友状态
     *
     * @param unionIds unionId列表
     * @return 影响行数
     */
    int batchUpdateFriendStatus(@Param("unionIds") List<String> unionIds);

    /**
     * 批量更新好友状态
     *
     * @param orderIds orderId列表
     * @return 影响行数
     */
    int batchUpdateFriendStatusByOrderIds(@Param("orderIds") List<String> orderIds);

    /**
     * 根据id获取
     */
    QwtfLandpageFormRecordEntity selectById(Long id);

    /**
     * 根据unionId获取表单记录
     *
     * @param unionId 微信unionId
     * @return 表单记录
     */
    QwtfLandpageFormRecordEntity selectByUnionId(@Param("unionId") String unionId);

    /**
     * 根据unionId获取未加好友的表单记录
     *
     * @param unionIds 微信unionId列表
     * @return 表单记录
     */
    List<QwtfLandpageFormRecordEntity> selectNotFriendByUnionIds(@Param("unionIds") List<String> unionIds);

    /**
     * 根据orderId获取未加好友的表单记录
     *
     * @param orderIds 微信orderId列表
     * @return 表单记录
     */
    List<QwtfLandpageFormRecordEntity> selectNotFriendByOrderIds(@Param("orderIds") List<String> orderIds);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();

    /**
     * 查询所有的媒体ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAppIds();

    /**
     * 查询所有的广告位ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalSlotIds();
}
