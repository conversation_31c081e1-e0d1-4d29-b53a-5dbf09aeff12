package com.ruoyi.system.mapper.qualification;

import com.ruoyi.system.entity.qualification.AccountQualificationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 提现账号资质信息 Mapper
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:50
 */
public interface AccountQualificationMapper {

    /**
     * 新增记录
     */
    int insert(AccountQualificationEntity accountQualificationEntity);

    /**
     * 根据id更新
     */
    int updateById(AccountQualificationEntity accountQualificationEntity);

    /**
     * 根据id获取
     */
    AccountQualificationEntity selectById(Long id);

    /**
     * 根据账户id获取资质详情
     *
     * @param accountId 账户id
     * @return 资质详情
     */
    AccountQualificationEntity selectByAccountId(@Param("accountId") Long accountId);

    /**
     * 查询资质列表
     *
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @param accountIds 账号列表
     * @return 列表
     */
    List<AccountQualificationEntity> selectQualificationList(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("accountIds") List<Long> accountIds);

    /**
     * 根据公司名称模糊查询账号id列表
     *
     * @param company 公司名称
     * @return 账号列表
     */
    List<Long> selectAccountIdsByCompanyName(@Param("company") String company);
}
