package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.AdvertSlotDayData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告日数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
public interface AdvertSlotDayDataMapper {

    /**
     * 查询广告广告位日数据
     *
     * @param param 查询条件
     * @return 广告广告位日数据
     */
    AdvertSlotDayData selectBy(AdvertSlotDayData param);

    /**
     * 查询广告广告位日数据列表
     *
     * @param advertSlotDayData 广告广告位日数据
     * @return 广告广告位日数据集合
     */
    List<AdvertSlotDayData> selectAdvertSlotDayDataList(AdvertSlotDayData advertSlotDayData);

    /**
     * 查询广告广告位日数据汇总
     *
     * @param param 参数
     * @return 广告广告位日数据汇总
     */
    AdvertSlotDayData selectStatisticAdvertSlotDayData(AdvertSlotDayData param);

    /**
     * 根据广告位id列表查询指定时间段内的落地页数据统计,根据广告位id和日期分组
     *
     * @param slotIds 广告位id列表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 结果
     */
    List<AdvertSlotDayData> selectSumLpDataBySlotIdsAndDate(@Param("slotIds") List<Long> slotIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 根据广告位ID列表和时间段查询落地页数据总和
     *
     * @param slotIds 广告位id列表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 落地页数据总和
     */
    AdvertSlotDayData sumLpDataBySlotIdsAndDate(@Param("slotIds") List<Long> slotIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 新增广告广告位日数据
     *
     * @param advertSlotDayData 广告广告位日数据
     * @return 结果
     */
    int insertAdvertSlotDayData(AdvertSlotDayData advertSlotDayData);

    /**
     * 修改广告广告位日数据
     *
     * @param param 广告广告位日数据
     * @return 结果
     */
    int updateAdvertSlotDayData(AdvertSlotDayData param);

    /**
     * 查询广告位今日的CPC广告消耗
     *
     * @param slotIds 广告位ID列表
     * @return 广告位今日的CPC广告消耗列表
     */
    List<AdvertSlotDayData> selectTodaySlotAdvertCpcConsumeMap(@Param("slotIds") List<Long> slotIds);

    /**
     * 查询广告位今日的CPC广告消耗总和
     *
     * @param slotIds 广告位ID列表
     * @return 广告位今日的CPC广告消耗总和
     */
    Long sumTodaySlotAdvertCpcConsume(@Param("slotIds") List<Long> slotIds);
}
