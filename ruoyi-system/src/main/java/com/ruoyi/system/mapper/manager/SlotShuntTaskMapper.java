package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.slot.SlotShuntTask;
import com.ruoyi.system.req.slot.shunt.SlotShuntTaskParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告位切量计划Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
public interface SlotShuntTaskMapper {

    /**
     * 查询广告位切量计划列表
     *
     * @param param 参数
     * @return 广告位切量计划列表
     */
    List<SlotShuntTask> selectList(SlotShuntTaskParam param);

    /**
     * 统计广告位切量计划数量
     *
     * @param param 请求参数
     * @return 广告位切量计划数量
     */
    int countByParam(SlotShuntTaskParam param);

    /**
     * 查询待执行/执行中的广告位切量计划列表
     *
     * @return 广告位切量计划列表
     */
    List<SlotShuntTask> selectTotalValidList();

    /**
     * 查询执行中的广告位切量计划列表
     *
     * @param slotId 请求参数
     * @return 广告位切量计划列表
     */
    List<SlotShuntTask> selectExecuteList(@Param("slotId") Long slotId);

    /**
     * 查询广告位切量计划
     *
     * @param id 媒体应用ID
     * @return 广告位切量计划
     */
    SlotShuntTask selectById(Long id);

    /**
     * 新增广告位切量计划
     *
     * @param param 参数
     * @return 结果
     */
    int insert(SlotShuntTask param);

    /**
     * 修改广告位切量计划
     *
     * @param param 参数
     * @return 结果
     */
    int update(SlotShuntTask param);
}
