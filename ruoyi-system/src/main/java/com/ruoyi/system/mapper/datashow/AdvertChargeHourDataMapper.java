package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.AdvertChargeHourDataEntity;

/**
 * 广告计费类型维度时段数据表 Mapper
 *
 * <AUTHOR>
 * @date 2022-10-21 14:03:32
 */
public interface AdvertChargeHourDataMapper {

    /**
     * 查询广告计费类型维度时段数据
     *
     * @param param 查询条件
     * @return 广告计费类型维度时段数据
     */
    AdvertChargeHourDataEntity selectBy(AdvertChargeHourDataEntity param);

    /**
     * 新增记录
     */
    int insert(AdvertChargeHourDataEntity advertChargeHourDataEntity);

    /**
     * 根据id更新
     */
    int updateById(AdvertChargeHourDataEntity advertChargeHourDataEntity);

    /**
     * 根据id获取
     */
    AdvertChargeHourDataEntity selectById(Long id);
}
