package com.ruoyi.system.mapper.advertiser;

import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeOffsetBo;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * DSP广告主消费记录 Mapper
 *
 * <AUTHOR>
 * @date 2022-7-13 17:30:15
 */
public interface DspAdvertiserConsumeRecordMapper {

    /**
     * 新增记录
     */
    int insert(DspAdvertiserConsumeRecordEntity entity);

    /**
     * 根据id更新
     */
    int updateById(DspAdvertiserConsumeRecordEntity entity);

    /**
     * 根据广告主ID和日期获取
     *
     * @param advertiserId 广告主ID
     * @param curDate 日期
     * @return 广告主消费记录
     */
    DspAdvertiserConsumeRecordEntity selectByAdvertiserIdAndDate(@Param("advertiserId") Long advertiserId, @Param("curDate") Date curDate);

    /**
     * 查询广告主消费记录不可见的日期
     *
     * @param advertiserId 广告主ID
     * @return 日期列表
     */
    List<Date> selectInvisibleDateList(@Param("advertiserId") Long advertiserId);

    /**
     * 批量查询广告主消费记录不可见的日期
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告主不可见日期列表
     */
    List<DspAdvertiserConsumeRecordEntity> batchSelectInvisibleDateList(@Param("advertiserIds") List<Long> advertiserIds);

    /**
     * 查询广告主消费记录列表
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @param isVisible 是否可见
     * @return 广告主消费记录列表
     */
    List<DspAdvertiserConsumeRecordEntity> selectList(@Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                      @Param("advertiserIds") List<Long> advertiserIds, @Param("isVisible") Integer isVisible);

    /**
     * 离线广告主消费记录修改后，消费总额的差额计算(DSP使用，不展示的记为0)
     *
     * @param advertiserId 广告主ID
     * @return 新消费-原消费的差额
     */
    Integer consumeOffset(@Param("advertiserId") Long advertiserId);

    /**
     * 离线广告主消费记录修改后，消费总额的差额计算(CRM使用，包含不展示的)
     *
     * @param advertiserId 广告主ID
     * @return 新消费-原消费的差额
     */
    Integer consumeOffsetForCrm(@Param("advertiserId") Long advertiserId);

    /**
     * 离线广告主消费记录修改后，消费总额的差额计算(CRM使用，包含不展示的)
     *
     * @param advertiserIds 广告主ID列表
     * @return 新消费-原消费的差额
     */
    List<AdvertiserConsumeOffsetBo> batchConsumeOffsetForCrm(@Param("advertiserIds") List<Long> advertiserIds);

    /**
     * 离线广告主消费记录修改后，消费总额的差额计算(DSP使用)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @return 新消费-原消费的差额
     */
    List<AdvertiserConsumeOffsetBo> batchConsumeOffset(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("advertiserIds") List<Long> advertiserIds);

    /**
     * 离线广告主消费记录修改后，广告主的消费总额的差额计算(CRM使用)
     *
     * @param advertiserIds 广告主ID列表
     * @return 新消费-原消费的差额
     */
    List<AdvertiserConsumeOffsetBo> advertiserConsumeOffsetForCrm(@Param("advertiserIds") List<Long> advertiserIds);

    /**
     * 离线广告主消费记录修改后，广告主的消费总额的差额总和计算(CRM使用)
     *
     * @param advertiserIds 广告主ID列表
     * @param excludeAccountIds 排除的广告主ID列表
     * @return 新消费-原消费的差额总和
     */
    Long sumAdvertiserConsumeOffsetForCrm(@Param("advertiserIds") List<Long> advertiserIds, @Param("excludeAccountIds") List<Long> excludeAccountIds);

    /**
     * 离线广告主消费记录修改后，广告主的消费总额的差额计算(DSP使用)
     *
     * @param advertiserIds 广告主ID列表
     * @return 新消费-原消费的差额
     */
    List<AdvertiserConsumeOffsetBo> advertiserConsumeOffset(@Param("advertiserIds") List<Long> advertiserIds);

    /**
     * 离线广告主消费记录修改后，广告主的消费总额的差额总和计算(DSP使用)
     *
     * @param advertiserIds 广告主ID列表
     * @return 新消费-原消费的差额总和
     */
    Integer advertiserConsumeOffsetSum(@Param("advertiserIds") List<Long> advertiserIds);
}
