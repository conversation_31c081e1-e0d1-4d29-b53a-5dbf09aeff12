package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.MobileHapDataEntity;

import java.util.List;

/**
 * 设备维度快应用数据表 Mapper
 *
 * <AUTHOR>
 * @date 2022-11-14 11:45:23
 */
public interface MobileHapDataMapper {

    /**
     * 根据条件获取
     *
     * @param param 参数
     * @return 结果
     */
    MobileHapDataEntity selectBy(MobileHapDataEntity param);

    /**
     * 查询列表
     *
     * @param param 参数
     * @return 结果
     */
    List<MobileHapDataEntity> selectList(MobileHapDataEntity param);

    /**
     * 新增记录
     */
    int insert(MobileHapDataEntity mobileHapDataEntity);

    /**
     * 根据id更新
     */
    int updateById(MobileHapDataEntity mobileHapDataEntity);

    /**
     * 根据id获取
     */
    MobileHapDataEntity selectById(Long id);
}
