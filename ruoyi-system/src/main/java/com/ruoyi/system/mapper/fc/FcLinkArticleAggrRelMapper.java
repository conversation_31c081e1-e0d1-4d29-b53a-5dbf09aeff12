package com.ruoyi.system.mapper.fc;

import com.ruoyi.system.entity.fc.FcLinkArticleAggrRelEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

/**
 * 丰巢链接-文章聚合链接关联表(FcLinkArticleAggrRel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-05 16:37:13
 */
public interface FcLinkArticleAggrRelMapper {

    /**
     * 新增数据
     *
     * @param fcLinkArticleAggrRelEntity 实例对象
     * @return 影响行数
     */
    int insert(FcLinkArticleAggrRelEntity fcLinkArticleAggrRelEntity);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<FcLinkArticleAggrRel> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<FcLinkArticleAggrRelEntity> entities);

    /**
     * 根据文章聚合链接id判断是否存在
     *
     * @param articleAggrLinkId 文章聚合链接id
     * @return 结果
     * <AUTHOR>
     */
    Integer existsByArticleAggrLinkId(@Param("articleAggrLinkId") Long articleAggrLinkId);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新status
     * @param fcLinkKey
     * @param aggrId
     * @param status
     */
    void updateStatusByFcLinkKeyAndAggrId(@Param("fcLinkKey") String fcLinkKey, @Param("aggrId") Long aggrId, @Param("status") Integer status);

    /**
     * 获取丰巢链接下所有未删除的聚合链接的id
     * @param fcLinkKey
     * @return
     */
    Set<Long> selectAggrIdsByFcLinkKey(@Param("fcLinkKey") String fcLinkKey);

    /**
     * 获取多个丰巢链接下所有未删除的聚合链接的id
     * @param fcLinkKeys
     * @return
     */
    List<Long> selectAggrIdsByFcLinkKeys(@Param("fcLinkKeys") List<String> fcLinkKeys);

    /**
     * 根据文章id获取关联的丰巢链接key
     * @param articleId 文章ID
     * @return 丰巢链接key集合
     * <AUTHOR>
     */
    Set<String> selectFcLinkKeysByArticleId(@Param("articleId") Long articleId);

    /**
     * 根据文章聚合链接ID获取关联的丰巢链接名称列表
     * @param articleAggrLinkId 文章聚合链接ID
     * @return 丰巢链接名称列表
     * <AUTHOR>
     */
    List<String> selectFcLinkNamesByArticleAggrLinkId(@Param("articleAggrLinkId") Long articleAggrLinkId);

}

