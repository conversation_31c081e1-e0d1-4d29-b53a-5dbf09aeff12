package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.plugin.Plugin;

import java.util.List;

/**
 * 插件Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-29
 */
public interface PluginMapper {

    /**
     * 查询插件
     *
     * @param id 插件ID
     * @return 插件
     */
    Plugin selectById(Long id);

    /**
     * 查询插件列表
     *
     * @param param 参数
     * @return 插件列表
     */
    List<Plugin> selectList(Plugin param);

    /**
     * 新增插件
     *
     * @param plugin 插件
     * @return 结果
     */
    int insertPlugin(Plugin plugin);

    /**
     * 修改插件
     *
     * @param plugin 插件
     * @return 结果
     */
    int updatePlugin(Plugin plugin);
}
