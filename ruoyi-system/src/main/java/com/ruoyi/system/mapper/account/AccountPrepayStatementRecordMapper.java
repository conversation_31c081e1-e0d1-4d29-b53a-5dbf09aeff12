package com.ruoyi.system.mapper.account;

import com.ruoyi.system.bo.publisher.PrepayRecordStatementAmountBo;
import com.ruoyi.system.bo.publisher.PrepayStatementAmountBo;
import com.ruoyi.system.entity.account.finance.AccountPrepayStatementRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 媒体预付款结算记录 Mapper
 *
 * <AUTHOR>
 * @date 2022-7-29 14:45:13
 */
public interface AccountPrepayStatementRecordMapper {

    /**
     * 新增记录
     */
    int insert(AccountPrepayStatementRecordEntity record);

    /**
     * 根据id更新
     */
    int updateById(AccountPrepayStatementRecordEntity record);

    /**
     * 根据id获取
     */
    AccountPrepayStatementRecordEntity selectById(Long id);

    /**
     * 获取媒体的历史结算总金额
     *
     * @param accountId 媒体账号ID
     * @return 历史结算总金额
     */
    Integer sumAmountByAccountId(@Param("accountId") Long accountId);

    /**
     * 批量查询媒体的历史结算总金额
     *
     * @param accountIds 媒体账号ID列表
     * @return 媒体历史结算总金额列表
     */
    List<PrepayStatementAmountBo> sumAmountByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * 查询媒体的预付款记录结算金额
     *
     * @param accountId 媒体账号ID
     * @return 媒体预付款记录结算金额列表
     */
    List<PrepayRecordStatementAmountBo> sumAmountGroupByPrepayRecordId(@Param("accountId") Long accountId);
}
