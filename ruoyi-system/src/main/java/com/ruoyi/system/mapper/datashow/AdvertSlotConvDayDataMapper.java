package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.bo.landpage.AdvertConvDataParamBo;
import com.ruoyi.system.bo.landpage.ConvDataBo;
import com.ruoyi.system.entity.datashow.AdvertSlotConvDayDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告广告位维度后端转化日数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-2-9 10:54:16
 */
public interface AdvertSlotConvDayDataMapper {

    /**
     * 新增记录
     */
    int insert(AdvertSlotConvDayDataEntity advertSlotConvDayDataEntity);

    /**
     * 根据id更新
     */
    int updateById(AdvertSlotConvDayDataEntity advertSlotConvDayDataEntity);

    /**
     * 查询数据
     *
     * @param param 查询条件
     * @return 数据
     */
    AdvertSlotConvDayDataEntity selectBy(AdvertSlotConvDayDataEntity param);

    /**
     * 统计日期-广告位维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param slotIds 广告位ID列表
     * @return 后端转化数据
     */
    List<ConvDataBo> countByDateAndSlotIds(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("slotIds") List<Long> slotIds);

    /**
     * 统计日期-广告维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param advertIds 广告ID列表
     * @return 后端转化数据
     */
    List<ConvDataBo> countByDateAndAdvertIds(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("advertIds") List<Long> advertIds);

    /**
     * 统计日期维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param advertIds 广告ID列表
     * @return 后端转化数据
     */
    List<ConvDataBo> countByDateAndAdvertIds2(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("advertIds") List<Long> advertIds);

    /**
     * 统计广告-媒体-日期维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param advertId 广告ID
     * @param appIds 媒体ID列表
     * @return 后端转化数据
     */
    List<ConvDataBo> countByDateAndAdvertIdAndAppIds(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("advertId") Long advertId, @Param("appIds") List<Long> appIds);

    /**
     * 统计广告-广告位-日期维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param advertId 广告ID
     * @param slotIds 广告位ID列表
     * @return 后端转化数据
     */
    List<ConvDataBo> countByDateAndAdvertIdAndSlotIds(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("advertId") Long advertId, @Param("slotIds") List<Long> slotIds,@Param("advertIds") List<Long> advertIds);

    /**
     * 查询后端转化汇总数据
     *
     * @param param 参数
     * @return 后端转化汇总数据
     */
    ConvDataBo selectStatisticAdvertConvData(AdvertConvDataParamBo param);
}
