package com.ruoyi.system.mapper.slot;

import com.ruoyi.system.entity.slot.SlotShuntData;
import com.ruoyi.system.req.slot.shunt.SlotShuntDataParam;
import com.ruoyi.system.req.slot.shunt.SlotShuntDataUpdateParam;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告位切量数据Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
public interface SlotShuntDataMapper {

    /**
     * 查询广告位切量数据列表
     *
     * @param param 查询参数
     * @return 广告位切量数据列表
     */
    List<SlotShuntData> selectList(SlotShuntDataParam param);

    /**
     * 查询广告位切量数据
     *
     * @param taskId 广告位切量计划ID
     * @param curDate 日期
     * @return 广告位切量数据
     */
    SlotShuntData selectByTaskId(@Param("taskId") Long taskId, @Param("curDate") Date curDate);

    /**
     * 查询广告位切量数据
     *
     * @param id 广告位切量数据ID
     * @return 广告位切量数据
     */
    SlotShuntData selectById(@Param("id") Long id);

    /**
     * 新增广告位切量数据
     *
     * @param record 广告位切量数据
     * @return 影响行数
     */
    int insert(SlotShuntData record);

    /**
     * 更新广告位切量数据
     *
     * @param param 更新参数
     * @return 影响行数
     */
    int update(SlotShuntDataUpdateParam param);
}
