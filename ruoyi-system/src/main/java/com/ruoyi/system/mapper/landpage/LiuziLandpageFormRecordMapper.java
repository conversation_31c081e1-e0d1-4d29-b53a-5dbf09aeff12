package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord;
import com.ruoyi.system.entity.landpage.LandpageFormCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 留资落地页单记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
public interface LiuziLandpageFormRecordMapper {

    /**
     * 查询落地页单记录
     *
     * @param id 落地页单记录ID
     * @return 落地页单记录
     */
    LiuziLandpageFormRecord selectLandpageFormRecordById(Long id);

    /**
     * 根据订单号ID查询记录是否存在
     *
     * @param orderId 订单号
     * @return 是否存在(1.存在,2.不存在)
     */
    Integer existByOrderId(@Param("orderId") String orderId);

    /**
     * 查询落地页单记录列表
     *
     * @param param 查询条件
     * @return 落地页单记录集合
     */
    List<LiuziLandpageFormRecord> selectList(LiuziLandpageFormRecord param);

    /**
     * 新增落地页单记录
     *
     * @param landpageFormRecord 落地页单记录
     * @return 结果
     */
    int insertLandpageFormRecord(LiuziLandpageFormRecord landpageFormRecord);

    /**
     * 修改落地页单记录
     *
     * @param landpageFormRecord 落地页单记录
     * @return 结果
     */
    int updateLandpageFormRecord(LiuziLandpageFormRecord landpageFormRecord);

    /**
     * 统计日期-广告位维度的留资落地页表单数量
     *
     * @param param 查询条件
     * @return 留资落地页表单数量
     */
    List<LandpageFormCount> countByDateAndSlotId(LiuziLandpageFormRecord param);

    /**
     * 统计广告位-广告维度留资落地页表单数量
     *
     * @param param 查询条件
     * @return 留资落地页表单数量
     */
    List<LandpageFormCount> countByDate(LiuziLandpageFormRecord param);

    /**
     * 根据订单id列表查询订单id列表，主要用于查询当前订单是否已经记录
     * @param orderIds
     * @return
     */
    List<String> selectOrderIdsByOrderIds(@Param("orderIds") List<String> orderIds);

    /**
     * 查询未发送过短信的支付宝留资留资
     * @return
     */
    List<LiuziLandpageFormRecord> selectUnSendSmsList();

    /**
     * 根据支付订单号查询留资表单
     *
     * @param outTradeNo 支付订单号
     * @return 留资表单
     */
    LiuziLandpageFormRecord selectByOutTradeNo(@Param("outTradeNo") String outTradeNo);

    /**
     * 更新加好友状态
     * @param phone
     * @param status
     * @see com.ruoyi.common.enums.QwFriendStatusEnum
     * @return
     */
    int updateFriendStatus(@Param("phone") String phone,@Param("status") Integer status);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();

    /**
     * 查询所有的媒体ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAppIds();

    /**
     * 查询所有的广告位ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalSlotIds();

    /**
     * 根据号码查询留资记录
     * @param phones
     * @return
     */
    List<LiuziLandpageFormRecord> selectListByPhone(@Param("phones") List<String> phones);

    /**
     * 批量新增
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<LiuziLandpageFormRecord> list);
}
