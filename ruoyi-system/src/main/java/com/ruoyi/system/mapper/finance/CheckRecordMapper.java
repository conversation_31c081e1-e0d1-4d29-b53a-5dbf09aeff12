package com.ruoyi.system.mapper.finance;

import com.ruoyi.system.entity.checkrecord.CheckRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 提现记录 Mapper
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:02
 */
public interface CheckRecordMapper {

    /**
     * 新增记录
     */
    int insert(CheckRecordEntity checkRecordEntity);

    /**
     * 更新记录
     */
    int update(CheckRecordEntity record);

    /**
     * 根据提现记录id查询最新的审核记录
     *
     * @param withdrawId 提现记录ID
     * @return 审核记录
     */
    CheckRecordEntity selectByWithdrawId(@Param("withdrawId") Long withdrawId);

    /**
     * 根据提现记录id列表查询审核记录列表
     *
     * @param withdrawIds 提现记录id列表
     * @return 审核记录
     */
    List<CheckRecordEntity> selectListByWithdrawIds(@Param("withdrawIds") List<Long> withdrawIds);
}
