package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.bo.advert.AdvertHourWarningDataBo;
import com.ruoyi.system.entity.datashow.AdvertHourData;

import java.util.List;

/**
 * 广告时段数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-10-14
 */
public interface AdvertHourDataMapper {

    /**
     * 查询广告时段数据
     *
     * @param param 查询条件
     * @return 广告时段数据
     */
    AdvertHourData selectBy(AdvertHourData param);

    /**
     * 查询广告时段数据列表
     *
     * @param advertHourData 广告时段数据
     * @return 广告时段数据集合
     */
    List<AdvertHourData> selectAdvertHourDataList(AdvertHourData advertHourData);

    /**
     * 查询广告时段数据列表
     *
     * @param advertHourData 广告时段数据
     * @return 广告时段数据集合
     */
    List<AdvertHourData> selectAdvertHourDataListGroupByDateHour(AdvertHourData advertHourData);

    /**
     * 查询广告时段数据汇总
     *
     * @param param 参数
     * @return 广告时段数据汇总
     */
    AdvertHourData selectStatisticAdvertHourData(AdvertHourData param);

    /**
     * 新增广告时段数据
     *
     * @param advertHourData 广告时段数据
     * @return 结果
     */
    int insertAdvertHourData(AdvertHourData advertHourData);

    /**
     * 修改广告时段数据
     *
     * @param param 广告时段数据
     * @return 结果
     */
    int updateAdvertHourData(AdvertHourData param);

    /**
     * 查询落地页到达率过低的广告时段数据
     *
     * @return 广告时段告警数据集合
     */
    List<AdvertHourWarningDataBo> selectLpExposureWarningList();

    /**
     * 查询CVR对比昨日降低50%的广告时段数据
     *
     * @return 广告时段告警数据集合
     */
    List<AdvertHourWarningDataBo> selectCvrWarningList();
}
