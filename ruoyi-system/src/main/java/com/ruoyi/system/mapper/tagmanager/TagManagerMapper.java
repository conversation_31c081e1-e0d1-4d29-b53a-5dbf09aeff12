package com.ruoyi.system.mapper.tagmanager;

import com.ruoyi.system.entity.tagmanager.TagManagerEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 媒体标签表 Mapper
 *
 * <AUTHOR>
 * @date 2022-9-23 10:52:10
 */
public interface TagManagerMapper {
    /**
     * 新增记录
     */
    int insert(TagManagerEntity tagManagerEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(TagManagerEntity tagManagerEntity);

    /**
     * 根据id获取
     */
    TagManagerEntity selectById(Long id);

    /**
     * 查询所有标签
     * @return
     */
    List<TagManagerEntity> selectAllTag();

    /**
     * 根据标签类型查询所有标签
     * @param type
     * @return
     */
    List<TagManagerEntity> selectAllTagByType(Integer type);

    /**
     * 根据id列表删除
     * @param ids
     * @return
     */
    Boolean deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 批量更新标签列表
     * @param updateList
     * @return
     */
    int batchUpdate(@Param("updateList") List<TagManagerEntity> updateList);

    /**
     * 批量新增标签列表
     * @param insertList
     * @return
     */
    int batchInsert(@Param("insertList") List<TagManagerEntity> insertList);

    /**
     * 根据id列表查询
     * @param ids
     * @return
     */
    List<TagManagerEntity> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 根据一级标签名获取二级标签id列表
     * @param tag
     * @return
     */
    List<Long> selectTagIdsByFirstTagName(@Param("type")Integer type, @Param("tagName") String tag);

    /**
     * 根据标签名称和类型获取二级标签ID列表
     *
     * @param type 标签
     * @param tagNames 标签名称列表
     * @return 标签ID列表
     */
    List<Long> selectTagIdsBySecondTagNames(@Param("type")Integer type, @Param("tagNames") List<String> tagNames);

    /**
     * 根据父标签id列表查询子标签id列表
     * @param parentIds
     * @return
     */
    List<Long> selectIdsByParentIds(@Param("parentIds") List<Long> parentIds);

}
