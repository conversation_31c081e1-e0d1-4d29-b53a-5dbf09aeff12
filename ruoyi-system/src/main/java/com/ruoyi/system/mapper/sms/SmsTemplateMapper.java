package com.ruoyi.system.mapper.sms;

import com.ruoyi.system.entity.sms.SmsTemplateEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 短信模版表 Mapper
 *
 * <AUTHOR>
 * @date 2022-12-5 10:09:01
 */
public interface SmsTemplateMapper {
    /**
     * 新增记录
     */
    int insert(SmsTemplateEntity smsTemplateEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(SmsTemplateEntity smsTemplateEntity);

    /**
     * 根据id获取
     */
    SmsTemplateEntity selectById(Long id);


    /**
     * 查询所有短信模版
     * @return
     */
    List<SmsTemplateEntity> selectAllList();


    /**
     * 批量新增更新
     * @param entities
     * @return
     */
    int batchInsertOrUpdate(@Param("entities") List<SmsTemplateEntity> entities);

    /**
     * 根据模版id查询
     * @param tpId
     * @return
     */
    SmsTemplateEntity selectByTpId(@Param("tpId") Long tpId);

    /**
     * 根据渠道和模版id查询短信内容
     * @param type 渠道
     * @see com.ruoyi.common.enums.SmsChannelEnum
     * @param tpId 模版id
     * @return
     */
    SmsTemplateEntity selectByTypeAndTpId(@Param("type") Integer type,@Param("tpId") Long tpId);

    /**
     * 根据短信内容
     * @param content
     * @return
     */
    List<SmsTemplateEntity> selectListByContent(@Param("content") String content);

}
