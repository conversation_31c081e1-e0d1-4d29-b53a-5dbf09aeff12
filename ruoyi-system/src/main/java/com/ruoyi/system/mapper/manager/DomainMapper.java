package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.req.manager.DomainUpdateStatusReq;

import java.util.List;

/**
 * 域名Mapper接口
 *
 * <AUTHOR>
 * @date 2021-09-16
 */
public interface DomainMapper {

    /**
     * 查询域名
     *
     * @param id 域名ID
     * @return 域名
     */
    Domain selectDomainById(Long id);

    /**
     * 查询域名
     *
     * @param domain 域名
     * @return 域名
     */
    Domain selectByDomain(String domain);

    /**
     * 查询域名列表
     *
     * @param domain 域名
     * @return 域名集合
     */
    List<Domain> selectDomainList(Domain domain);

    /**
     * 新增域名
     *
     * @param domain 域名
     * @return 结果
     */
    int insertDomain(Domain domain);

    /**
     * 修改域名
     *
     * @param domain 域名
     * @return 结果
     */
    int updateDomain(Domain domain);

    /**
     * 更新支付宝和微信状态
     *
     * @param req 参数
     * @return 是否更新成功
     */
    int updateWxAlipayStatus(DomainUpdateStatusReq req);

    /**
     * 删除域名
     *
     * @param id 域名ID
     * @return 结果
     */
    int deleteDomainById(Long id);
}
