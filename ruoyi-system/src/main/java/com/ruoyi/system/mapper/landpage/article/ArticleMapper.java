package com.ruoyi.system.mapper.landpage.article;

import com.ruoyi.system.bo.landpage.article.ArticleCountBo;
import com.ruoyi.system.bo.landpage.article.ArticleListBo;
import com.ruoyi.system.bo.landpage.article.ArticleListParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.bo.fc.FcArticleStatusCountBo;

import java.util.Date;
import java.util.List;

/**
 * 文章表 Mapper
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:01
 */
public interface ArticleMapper {

    /**
     * 查询列表
     */
    List<ArticleEntity> selectList(ArticleListParamBo param);

    /**
     * 查询列表
     */
    List<ArticleListBo> selectListWithData(ArticleListParamBo param);

    /**
     * 查询文章汇总数据
     */
    ArticleListBo selectStatisticData(ArticleListParamBo param);

    /**
     * 查询今日私域增加阅读量
     */
    Integer selectTodaySyIncrRequestPvSum(Long linkId);

    /**
     * 新增记录
     */
    int insert(ArticleEntity articleEntity);

    /**
     * 批量新增文章
     */
    int batchInsert(@Param("entities") List<ArticleEntity> entities);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(ArticleEntity articleEntity);

    /**
     * 根据id获取
     */
    ArticleEntity selectById(Long id);

    /**
     * 查询权重总和
     *
     * @param linkId 链接ID
     * @return 权重总和
     */
    Integer selectWeightSum(@Param("linkId") Long linkId);

    /**
     * 查询链接对应的文章数量
     *
     * @param linkIds 链接ID列表
     * @return 文章数量列表
     */
    List<ArticleCountBo> countByLinkId(@Param("linkIds") List<Long> linkIds);

    /**
     * 查询无信息的文章
     */
    ArticleEntity selectArticleWithoutProfile();

    /**
     * 查询当天文章数不为0的链接ID
     */
    List<Long> selectBgZeroTodayArticleLinkId();

    /**
     * 根据聚合链接列表的文章列表
     * @param linkIds
     * @return
     */
    List<ArticleEntity> selectListByLinkIds(@Param("linkIds") List<Long> linkIds);

    /**
     * 更新丰巢审核状态
     * @param articleId
     * @param checkStatus
     */
    void updateFcCheckStatus(@Param("articleId") Long articleId, @Param("checkStatus") Integer checkStatus, @Param("rejectReason") String rejectReason);

    /**
     * 更新丰巢同步状态
     * @param articleId
     * @param syncStatus
     */
    void updateFcSyncStatus(@Param("articleId") Long articleId, @Param("syncStatus") Integer syncStatus, @Param("syncFailReason") String syncFailReason);

    /**
     * 统计丰巢文章审核数和同步数
     * @param linkIds
     * @return
     */
    List<FcArticleStatusCountBo> countFcStatusByLinkIds(@Param("linkIds") List<Long> linkIds);

    /**
     * 通过聚合链接id查询是否有丰巢同步成功的文章
     * @param linkId 聚合链接ID
     * @return 是否存在丰巢同步成功的文章
     * <AUTHOR>
     */
    Boolean hasFcSyncSuccessArticleByLinkId(@Param("linkId") Long linkId);

    /**
     * 通过多个聚合链接id查询是否有丰巢同步成功的文章
     * @param linkIds 聚合链接ID列表
     * @return 是否存在丰巢同步成功的文章
     * <AUTHOR>
     */
    Boolean hasFcSyncSuccessArticleByLinkIds(@Param("linkIds") List<Long> linkIds);
}
