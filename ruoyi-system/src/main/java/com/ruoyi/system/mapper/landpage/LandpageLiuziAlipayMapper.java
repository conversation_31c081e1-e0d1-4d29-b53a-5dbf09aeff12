package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.landpage.LandpageLiuziAlipayEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 支付宝留资落地页表 Mapper
 *
 * <AUTHOR>
 * @date 2022-12-5 10:08:00
 */
public interface LandpageLiuziAlipayMapper {
    /**
     * 新增记录
     */
    int insert(LandpageLiuziAlipayEntity landpageLiuziAlipayEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(LandpageLiuziAlipayEntity landpageLiuziAlipayEntity);

    /**
     * 根据id获取
     */
    LandpageLiuziAlipayEntity selectById(Long id);


    /**
     * 根据落地页id列表查询
     * @param landpageIds
     * @return
     */
    List<LandpageLiuziAlipayEntity> selectListByLandpageIds(@Param("landpageIds") List<Long> landpageIds);

    /**
     * 新增更新支付宝推广页
     * @param entities
     * @return
     */
    Boolean batchInsertOrUpdate(@Param("entities") List<LandpageLiuziAlipayEntity> entities);
}
