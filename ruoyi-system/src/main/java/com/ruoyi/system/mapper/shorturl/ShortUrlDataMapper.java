package com.ruoyi.system.mapper.shorturl;

import com.ruoyi.system.entity.shorturl.ShortUrlDataEntity;
import com.ruoyi.system.param.shorturl.ShortUrlDataParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 短链数据表 Mapper
 *
 * <AUTHOR>
 * @date 2022-10-8 14:16:45
 */
public interface ShortUrlDataMapper {

    /**
     * 新增记录
     */
    int insert(ShortUrlDataEntity param);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(ShortUrlDataEntity param);

    /**
     * 更新PV/UV数据
     */
    int update(@Param("id") Long id, @Param("pvAdd") Integer pvAdd, @Param("uvAdd") Integer uvAdd);

    /**
     * 根据id获取
     */
    ShortUrlDataEntity selectById(Long id);

    /**
     * 查询短链日数据
     *
     * @param param 参数
     * @return 短链日数据
     */
    ShortUrlDataEntity selectBy(ShortUrlDataEntity param);

    /**
     * 根据条件查询短链数据
     * @return
     */
    List<ShortUrlDataEntity> selectListByParam(ShortUrlDataParam param);
}
