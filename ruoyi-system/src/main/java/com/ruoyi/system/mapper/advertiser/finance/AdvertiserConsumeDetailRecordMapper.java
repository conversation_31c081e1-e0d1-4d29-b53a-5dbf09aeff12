package com.ruoyi.system.mapper.advertiser.finance;

import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeDetailRecordEntity;

import java.util.List;

/**
 * 广告主消费记录明细表 Mapper
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
public interface AdvertiserConsumeDetailRecordMapper {

    /**
     * 新增广告主消费记录明细
     */
    int insert(AdvertiserConsumeDetailRecordEntity record);

    /**
     * 更新广告主消费记录明细
     */
    int update(AdvertiserConsumeDetailRecordEntity record);

    /**
     * 查询广告主消费记录明细
     *
     * @param req 参数
     * @return 广告主消费记录明细
     */
    List<AdvertiserConsumeDetailRecordEntity> selectList(AdvertiserConsumeDetailRecordEntity req);
}
