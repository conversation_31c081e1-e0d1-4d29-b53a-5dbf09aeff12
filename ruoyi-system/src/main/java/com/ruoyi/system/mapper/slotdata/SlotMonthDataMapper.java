package com.ruoyi.system.mapper.slotdata;

import com.ruoyi.system.entity.slotdata.SlotMonthDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告位月账单数据表 Mapper
 *
 * <AUTHOR>
 * @date 2021-9-9 16:56:55
 */
public interface SlotMonthDataMapper {

    /**
     * 批量新增
     *
     * @param dataEntities 月数据
     * @return 结果
     */
	int batchInsertOrUpdate(@Param("dataEntities") List<SlotMonthDataEntity> dataEntities);


    /**
     * 根据媒体id和月份查询
     *
     * @param appId 媒体id
     * @param monthDate 月份
     * @return 结果列表
     */
    List<SlotMonthDataEntity> selectByAppIdAndDate(@Param("appId") Long appId, @Param("monthDate") Integer monthDate);

    /**
     * 根据广告位ID和月份查询广告位月账单是否生成
     *
     * @param slotId
     * @param month
     * @return
     */
    int countBySlotIdAndMonth(@Param("slotId") Long slotId,@Param("month") Integer month);

    /**
     * 根据广告位列表和月份列表查询
     *
     * @param slotIds 广告位Id列表
     * @param months 月份列表
     * @return 广告位月账单列表(无账单数据)
     */
    List<SlotMonthDataEntity> selectBySlotIdsAndMonths(@Param("slotIds") List<Long> slotIds,@Param("months") List<Integer> months);

    /**
     * 根据广告位列表和日期查询
     *
     * @return
     */
    List<SlotMonthDataEntity> selectBySlotIdsAndMonth(@Param("slotIds") List<Long> slotIds,@Param("month") Integer month);

    /**
     * 根据广告位和日期查询广告位月账单
     *
     * @param slotId 广告位Id
     * @param month 年月
     * @return 广告位月账单
     */
    SlotMonthDataEntity selectBySlotIdAndMonth(@Param("slotId") Long slotId, @Param("month") Integer month);

    /**
     * 新增广告位月账单
     *
     * @param entity 广告位月账单
     * @return 结果
     */
    int insert(SlotMonthDataEntity entity);

    /**
     * 更新广告位月账单
     *
     * @param entity 广告位月账单
     * @return 结果
     */
    int update(SlotMonthDataEntity entity);
}
