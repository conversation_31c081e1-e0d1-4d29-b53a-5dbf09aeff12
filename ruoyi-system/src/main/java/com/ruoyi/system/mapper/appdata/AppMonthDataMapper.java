package com.ruoyi.system.mapper.appdata;

import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.req.datashow.AppMonthDataReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 媒体月账单数据表 Mapper
 *
 * <AUTHOR>
 * @date 2021-9-9 16:55:56
 */
public interface AppMonthDataMapper {

    /**
     * 批量新增
     * @param dataEntities 月数据
     * @return 结果
     */
    int batchInsertOrUpdate(@Param("dataEntities") List<AppMonthDataEntity> dataEntities);

    /**
     * 批量新增
     * @param dataEntities 月数据
     * @return 结果
     */
    int batchUpdateCost(@Param("dataEntities") List<AppMonthDataEntity> dataEntities);

    /**
     * 根据id获取
     */
    AppMonthDataEntity selectById(Long id);

    /**
     * 根据媒体ID和月份查询媒体月账单
     *
     * @param appId 媒体ID
     * @param month 年月
     * @return 媒体月账单
     */
    AppMonthDataEntity selectByAppIdAndMonth(@Param("appId") Long appId, @Param("month") Integer month);

    /**
     * 查询媒体月数据
     *
     * @param req 参数
     * @return 媒体月数据
     */
    List<AppMonthDataEntity> selectAppMonthList(AppMonthDataReq req);

    /**
     * 确认月账单
     *
     * @param id 账单id
     * @param status 确认状态
     * @see com.ruoyi.common.enums.ConfirmStatusEnum
     * @return 结果
     */
    int confirmStatement(@Param("id") Long id,@Param("status") Integer status);

    /**
     * 根据提现id查询媒体月账单
     *
     * @param withdrawId 提现id
     * @return 月账单列表
     */
    List<AppMonthDataEntity> selectByWithdrawId(@Param("withdrawId") Long withdrawId);

    /**
     * 根据账号id查询未绑定提现的媒体月账单
     *
     * @param accountId 账号id
     * @param ids 月账单id列表
     * @param withdrawIds 关联的提现记录id
     * @param confirmStatus 审核状态
     * @return 月账单列表
     */
    List<AppMonthDataEntity> selectNoWithdrawByAccountId(@Param("accountId") Long accountId,@Param("ids") List<Long> ids,@Param("withdrawIds") List<Long> withdrawIds,@Param("status") List<Integer> confirmStatus);

    /**
     * 根据账号id查询未确认的媒体月账单的数量
     *
     * @param accountId  账号id
     * @return 未确认的媒体月账单的数量
     */
    int countNoConfirmByAccountId(@Param("accountId") Long accountId);

    /**
     * 媒体月账单关联提现记录
     *
     * @param ids 账单id列表
     * @param withdrawId 提现记录id 当id为0时，则表示取消关联
     * @param confirmStatus 确认提现状态
     * @return 结果
     */
    int relevanceWithdrawRecord(@Param("ids") List<Long> ids,@Param("withdrawId") Long withdrawId,@Param("confirmStatus") Integer confirmStatus);

    /**
     * 批量修改媒体月账单状态
     *
     * @param withdrawId 提现记录id
     * @param confirmStatus 状态
     * @return 结果
     */
    int updateConfirmStatus(@Param("withdrawId") Long withdrawId,@Param("confirmStatus") Integer confirmStatus);

    /**
     * 分页查询媒体月账单列表
     *
     * @param id 上一次最大id
     * @param pageSize 每页大小
     * @param monthDay 月份
     * @return 媒体月账单列表
     */
    List<AppMonthDataEntity> selectListByPage(@Param("id") Long id,@Param("pageSize") Integer pageSize,@Param("monthDay") Integer monthDay);

    /**
     * 新增媒体月账单
     *
     * @param entity 广告位月账单
     * @return 结果
     */
    int insert(AppMonthDataEntity entity);

    /**
     * 更新媒体月账单
     *
     * @param entity 广告位月账单
     * @return 结果
     */
    int update(AppMonthDataEntity entity);
}
