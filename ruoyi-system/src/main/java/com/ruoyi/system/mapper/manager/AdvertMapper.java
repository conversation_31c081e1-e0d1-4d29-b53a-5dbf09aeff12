package com.ruoyi.system.mapper.manager;

import java.util.List;
import com.ruoyi.system.entity.advert.Advert;
import org.apache.ibatis.annotations.Param;

/**
 * 广告Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
public interface AdvertMapper {

    /**
     * 查询广告
     *
     * @param id 广告ID
     * @return 广告
     */
    Advert selectAdvertById(Long id);

    /**
     * 查询广告列表
     *
     * @param advert 广告
     * @return 广告集合
     */
    List<Advert> selectAdvertList(Advert advert);

    /**
     * 查询广告ID列表
     *
     * @param searchValue 广告ID/名称搜索值
     * @return 广告ID列表
     */
    List<Long> selectAdvertIdsBySearchValue(@Param("searchValue") String searchValue);

    /**
     * 查询广告ID名称列表
     *
     * @param ids 广告ID列表
     * @return 广告ID名称列表
     */
    List<Advert> selectAdvertIdNameList(@Param("ids") List<Long> ids);

    /**
     * 查询有效广告ID列表
     *
     * @return 有效广告ID列表
     */
    List<Long> selectValidAdvertIdList();

    /**
     * 新增广告
     *
     * @param advert 广告
     * @return 结果
     */
    int insertAdvert(Advert advert);

    /**
     * 修改广告
     *
     * @param advert 广告
     * @return 结果
     */
    int updateAdvert(Advert advert);

    /**
     * 更新落地页链接
     *
     * @param id 广告id
     * @param landpageUrl 落地页链接
     * @return 影响行数
     */
    int updateLandPageUrl(@Param("id") Long id, @Param("landpageUrl") String landpageUrl);

    /**
     * 修改广告状态
     *
     * @param advert 广告
     * @return 结果
     */
    int updateAdvertStatus(Advert advert);

    /**
     * 重置预算不足的广告的状态
     *
     * @param advertIds 广告ID列表
     * @return 结果
     */
    int resetAdvertStatus(@Param("ids") List<Long> advertIds);

    /**
     * 根据广告主ID查询广告ID列表
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告ID列表
     */
    List<Long> selectAdvertIdByAdvertiserIds(@Param("advertiserIds") List<Long> advertiserIds);

    /**
     * 标记广告无效
     *
     * @param advertId 广告ID
     * @return 结果
     */
    int invalidateAdvert(@Param("id") Long advertId);

    /**
     * 禁用广告主下的所有广告
     *
     * @param advertiserId 广告主ID
     * @return 影响行数
     */
    int disableAdvertByAdvertiserId(@Param("advertiserId") Long advertiserId);

    /**
     * 恢复广告主下的所有广告
     *
     * @param advertiserId 广告主ID
     * @return 影响行数
     */
    int enableAdvertByAdvertiserId(@Param("advertiserId") Long advertiserId);

    /**
     * 查询广告主下特定开关状态的广告
     *
     * @param advertiserId 广告主ID
     * @param status 开关状态
     * @return 广告列表
     */
    List<Advert> selectAllOpenOrCloseAdvertNameByAdvertiserId(@Param("advertiserId") Long advertiserId,@Param("status")Integer status);

    /**
     * 批量更新广告开关
     *
     * @param ids 广告ID列表
     * @param servingSwitch 开关操作
     * @return 影响行数
     */
    int updateSwitchByIds(@Param("ids") List<Long> ids,@Param("servingSwitch") Integer servingSwitch);

    /**
     * 根据广告主ID和广告状态查询广告ID
     *
     * @param advertiserId 广告主ID
     * @param statusList 广告状态列表
     * @return 广告ID列表
     */
    List<Long> selectAdvertIdByAdvertiserIdAndStatus(@Param("advertiserId") Long advertiserId, @Param("statusList") List<Integer> statusList);
}
