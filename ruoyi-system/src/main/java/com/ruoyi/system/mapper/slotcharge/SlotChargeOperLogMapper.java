package com.ruoyi.system.mapper.slotcharge;

import com.ruoyi.system.entity.slotcharge.SlotChargeOperLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告位每日计费方式操作日志 Mapper
 *
 * <AUTHOR>
 * @date 2022-3-8 16:50:40
 */
public interface SlotChargeOperLogMapper {

    /**
     * 新增记录
     */
    int insert(SlotChargeOperLogEntity slotChargeOperLogEntity);

    /**
     * 根据广告位id获取操作日志
     * @param slotId
     * @return
     */
    List<SlotChargeOperLogEntity> selectListBySlotId(@Param("slotId") Long slotId);
}
