package com.ruoyi.system.mapper.landpage.article;

import com.ruoyi.system.entity.landpage.article.ArticleEditHistoryEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 文章修改记录表 Mapper
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:41
 */
public interface ArticleEditHistoryMapper {

    /**
     * 新增记录
     */
    int insert(ArticleEditHistoryEntity articleEditHistoryEntity);

    /**
     * 根据id更新
     */
    int updateById(ArticleEditHistoryEntity articleEditHistoryEntity);

    /**
     * 根据id获取
     */
    ArticleEditHistoryEntity selectById(Long id);

    /**
     * 查询文章是否存在修改记录
     *
     * @param articleId 文章ID
     * @return 是否存在(1.存在,null.不存在)
     */
    Integer existByArticleId(@Param("articleId") Long articleId);
}
