package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 外部优惠券发放记录表 Mapper
 *
 * <AUTHOR>
 * @date 2024-4-18 14:07:03
 */
public interface ExternalCouponRecordMapper {

    /**
     * 新增记录
     */
    int insert(ExternalCouponRecordEntity externalCouponRecordEntity);

    /**
     * 根据id更新
     */
    int updateById(ExternalCouponRecordEntity externalCouponRecordEntity);

    ExternalCouponRecordEntity selectByOrderId(@Param("orderId") String orderId);
}
