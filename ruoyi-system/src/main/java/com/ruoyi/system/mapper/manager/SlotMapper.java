package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.slot.Slot;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 广告位Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface SlotMapper {

    /**
     * 查询广告位
     *
     * @param id 广告位ID
     * @return 广告位
     */
    Slot selectSlotById(Long id);

    /**
     * 查询广告位列表
     *
     * @param slot 广告位
     * @return 广告位集合
     */
    List<Slot> selectSlotList(Slot slot);

    /**
     * 查询广告位列表
     *
     * @param slot 广告位
     * @return 广告位集合
     */
    List<Slot> selectSlotListBySlotDataSort(Slot slot);

    /**
     * 查询广告位列表
     *
     * @param slot 广告位
     * @return 广告位集合
     */
    List<Long> selectSlotIdList(Slot slot);

    /**
     * 查询广告位列表基础信息
     *
     * @param slot 广告位
     * @return 广告位集合
     */
    List<Slot> selectSimpleSlotList(Slot slot);

    /**
     * 新增广告位
     *
     * @param slot 广告位
     * @return 结果
     */
    int insertSlot(Slot slot);

    /**
     * 修改广告位
     *
     * @param slot 广告位
     * @return 结果
     */
    int updateSlot(Slot slot);

    /**
     * 根据媒体账号ID过滤且分组
     *
     * @param accountIds 媒体账号ID列表
     * @return 分组的Map列表
     */
    List<Map<String, Long>> groupByAccountId(@Param("accountIds") List<Long> accountIds);

    /**
     * 根据媒体ID过滤且分组
     *
     * @param appIds 媒体ID列表
     * @return 分组的Map列表
     */
    List<Map<String, Long>> groupByAppId(@Param("appIds") List<Long> appIds);

    /**
     * 根据广告位id列表查询
     *
     * @param ids 广告位id列表
     * @return 结果
     */
    List<Slot> selectSimpleSlotByIds(@Param("ids") List<Long> ids);

    /**
     * 根据媒体id查询广告位列表，不分页
     *
     * @param appId 媒体ID
     * @return 广告位列表
     */
    List<Slot> selectSlotByAppId(@Param("appId") Long appId);

    /**
     * 根据媒体账号ID查询广告位ID列表
     *
     * @param accountIds 媒体账号ID列表
     * @return 广告位ID列表
     */
    List<Long> selectSlotIdByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * 根据广告位ID和名称
     *
     * @return 广告位列表
     */
    List<Slot> selectSlotIdAndName();

    /**
     * 根据广告位ID和链接
     *
     * @return 广告位列表
     */
    List<Slot> selectSlotIdAndSlotUrl();

    /**
     * 根据媒体id列表查询广告位列表，不分页，不做数据隔离
     * @param appIds
     * @return
     */
    List<Slot> selectByAppIds(@Param("appIds") List<Long> appIds);
}
