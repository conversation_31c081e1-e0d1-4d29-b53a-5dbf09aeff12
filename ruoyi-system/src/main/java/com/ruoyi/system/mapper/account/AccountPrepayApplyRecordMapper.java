package com.ruoyi.system.mapper.account;

import com.ruoyi.system.entity.account.finance.AccountPrepayApplyRecordEntity;
import com.ruoyi.system.req.publisher.prepay.PrepayRecordListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 媒体预付款申请记录 Mapper
 *
 * <AUTHOR>
 * @date 2022-7-29 14:44:56
 */
public interface AccountPrepayApplyRecordMapper {

    /**
     * 新增记录
     */
    int insert(AccountPrepayApplyRecordEntity record);

    /**
     * 根据id更新
     */
    int updateById(AccountPrepayApplyRecordEntity record);

    /**
     * 根据id获取
     */
    AccountPrepayApplyRecordEntity selectById(Long id);

    /**
     * 查询媒体预付款申请列表
     *
     * @param param 参数
     * @return 媒体预付款申请列表
     */
    List<AccountPrepayApplyRecordEntity> selectList(PrepayRecordListParam param);

    /**
     * 查询媒体预付款申请记录
     *
     * @param accountId 媒体账号ID
     * @param auditStatus 审核状态
     * @return 媒体预付款申请记录
     */
    List<AccountPrepayApplyRecordEntity> selectListByAccountIdAndAuditStatus(@Param("accountId") Long accountId, @Param("auditStatus") Integer auditStatus);

    /**
     * 查询媒体预付款申请记录数量
     *
     * @param accountId 媒体账号ID
     * @param auditStatus 审核状态
     * @return 媒体预付款申请记录数量
     */
    int countByAccountIdAndAuditStatus(@Param("accountId") Long accountId, @Param("auditStatus") Integer auditStatus);
}
