package com.ruoyi.system.mapper.area;

import com.ruoyi.system.entity.area.AreaDataset;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 行政区划(移动)数据Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-11
 */
public interface AreaDatasetYdMapper {

    /**
     * 查询行政区划数据
     *
     * @param areaNum 行政区划代码
     * @param areaType 地域类型
     * @return 行政区划数据
     */
    AreaDataset selectByAreaNumAndType(@Param("areaNum") String areaNum, @Param("areaType") Integer areaType);

    /**
     * 查询行政区划数据
     *
     * @param areaName 行政区划名称
     * @return 行政区划数据
     */
    AreaDataset selectByAreaName(@Param("areaName") String areaName);

    /**
     * 查询行政区划代码列表
     *
     * @param areaDataset 行政区划代码
     * @return 行政区划数据
     */
    List<AreaDataset> selectAreaDatasetList(AreaDataset areaDataset);
}
