package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.LandpageDayData;
import com.ruoyi.system.req.landpage.LandpageDataReq;

import java.util.List;

/**
 * 落地页维度日数据表 Mapper
 *
 * <AUTHOR>
 * @date 2022-9-21 16:17:57
 */
public interface LandpageDayDataMapper {

    /**
     * 查询列表
     *
     * @param param 参数
     * @return 结果
     */
    List<LandpageDayData> selectList(LandpageDataReq param);

    /**
     * 新增记录
     */
    int insert(LandpageDayData landpageDayData);

    /**
     * 根据id更新
     */
    int updateById(LandpageDayData landpageDayData);

    /**
     * 根据id获取
     */
    LandpageDayData selectById(Long id);

    /**
     * 根据条件获取
     *
     * @param param 参数
     * @return 结果
     */
    LandpageDayData selectBy(LandpageDayData param);
}
