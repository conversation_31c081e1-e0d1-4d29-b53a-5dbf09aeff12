package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.SlotConvOpenDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 媒体可见的转化数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-3-10 11:10:04
 */
public interface SlotConvOpenDataMapper {

    /**
     * 新增记录
     *
     * @param param 参数
     * @return 影响行数
     */
    int insert(SlotConvOpenDataEntity param);

    /**
     * 根据id自增
     *
     * @param id 记录ID
     * @return 影响行数
     */
    int incr(@Param("id") Long id);

    /**
     * 根据广告位ID和日期查询数据
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     * @return 数据
     */
    SlotConvOpenDataEntity selectBy(@Param("slotId") Long slotId, @Param("curDate") Date curDate);

    /**
     * 根据广告位ID列表和日期查询数据
     *
     * @param slotIds 广告位ID列表
     * @return 广告位转化数据列表
     */
    List<SlotConvOpenDataEntity> selectListBySlotIdsAndDate(@Param("slotIds") List<Long> slotIds, @Param("curDate") Date curDate);
}
