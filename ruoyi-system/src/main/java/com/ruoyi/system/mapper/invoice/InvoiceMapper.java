package com.ruoyi.system.mapper.invoice;

import com.ruoyi.system.bo.invoice.InvoiceSumBO;
import com.ruoyi.system.bo.invoice.InvoiceSumListBO;
import com.ruoyi.system.entity.invoice.InvoiceEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 发票表 Mapper
 *
 * <AUTHOR>
 * @date 2022-10-20 14:33:51
 */
public interface InvoiceMapper {
    /**
     * 新增记录
     */
    int insert(InvoiceEntity invoiceEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(InvoiceEntity invoiceEntity);

    /**
     * 根据id获取
     */
    InvoiceEntity selectById(Long id);

    /**
     * 根据账号id列表查询发票总金额
     * @param accountIds 账号id列表
     * @return
     */
    List<InvoiceSumBO> sumInvoice(@Param("accountIds") List<Long> accountIds);

    /**
     * 根据账号id查询发票信息
     * @param accountId 账号id
     * @return
     */
    List<InvoiceEntity> getInvoiceInfoListByAccountId(Long accountId);

    /**
     * 根据更新时间查询发票列表
     * @param startDate
     * @param endDate
     * @return
     */
    List<InvoiceEntity> selectListByGmtModified(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 根据账号id列表和状态查询
     * @param accountIds
     * @param status
     * @return
     */
    List<InvoiceSumListBO> selectListByAccountIds(@Param("accountIds") List<Long> accountIds, @Param("status") Integer status);

}
