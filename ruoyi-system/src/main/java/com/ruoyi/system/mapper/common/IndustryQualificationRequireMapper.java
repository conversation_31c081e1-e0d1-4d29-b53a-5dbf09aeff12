package com.ruoyi.system.mapper.common;

import com.ruoyi.system.entity.common.IndustryQualificationRequireEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 行业资质要求表 Mapper
 *
 * <AUTHOR>
 * @date 2023-3-6 17:34:59
 */
public interface IndustryQualificationRequireMapper {

    /**
     * 查询列表
     */
    List<IndustryQualificationRequireEntity> selectList(IndustryQualificationRequireEntity param);

    /**
     * 新增记录
     */
    int insert(IndustryQualificationRequireEntity industryQualificationRequireEntity);

    /**
     * 根据id更新
     */
    int updateById(IndustryQualificationRequireEntity industryQualificationRequireEntity);

    /**
     * 根据id获取
     */
    IndustryQualificationRequireEntity selectById(Long id);

    /**
     * 查询行业的资质要求
     *
     * @param industryIds 行业ID列表
     * @return 资质要求列表
     */
    List<IndustryQualificationRequireEntity> selectByIndustryIds(@Param("industryIds") List<Long> industryIds);

    /**
     * 获取行业必填的资质要求
     *
     * @param industryId 行业ID
     * @return 资质要求ID列表
     */
    List<Long> selectMustRequireIdByIndustryId(@Param("industryId") Long industryId);
}
