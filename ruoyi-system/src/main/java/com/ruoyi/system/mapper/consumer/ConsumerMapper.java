package com.ruoyi.system.mapper.consumer;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.domain.consumer.Consumer;

/**
 * 用户Mapper接口
 *
 * <AUTHOR>
 * @date 2021/8/17
 */
@DataSource(DataSourceType.CONSUMER)
public interface ConsumerMapper {

    /**
     * 根据consumerId查询用户
     *
     * @param param 参数
     * @return 用户
     */
    Consumer selectByConsumerId(Consumer param);

    /**
     * 根据appId和设备号查询用户
     *
     * @param param 参数
     * @return 用户
     */
    Consumer selectByAppAndDevice(Consumer param);

    /**
     * 新增用户
     *
     * @param param 参数
     * @return 影响行数
     */
    int insertConsumer(Consumer param);
}
