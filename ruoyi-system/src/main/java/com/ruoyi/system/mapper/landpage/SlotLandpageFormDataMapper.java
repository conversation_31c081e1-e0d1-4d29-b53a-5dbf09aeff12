package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.landpage.SlotLandpageFormDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告位日维度表单数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-2-20 10:45:27
 */
public interface SlotLandpageFormDataMapper {

    /**
     * 新增记录
     */
    int insert(SlotLandpageFormDataEntity slotLandpageFormDataEntity);

    /**
     * 根据id更新
     */
    int updateById(SlotLandpageFormDataEntity slotLandpageFormDataEntity);

    /**
     * 根据id获取
     */
    SlotLandpageFormDataEntity selectById(Long id);

    /**
     * 根据日期和广告位ID获取
     *
     * @param curDate 日期
     * @param slotId 广告位ID
     * @return 广告位落地页表单数据
     */
    SlotLandpageFormDataEntity selectBy(@Param("curDate") Date curDate, @Param("slotId") Long slotId);

    /**
     * 查询广告位落地页表单数据列表
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param slotIds 广告位ID列表
     * @return 广告位落地页表单数据列表
     */
    List<SlotLandpageFormDataEntity> selectListByDateAndSlotIds(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("slotIds") List<Long> slotIds);

    /**
     * 查询广告位落地页表单数据汇总
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param slotIds 广告位ID列表
     * @return 落地页表单数量
     */
    Integer sumFormDataByDateAndSlotIds(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("slotIds") List<Long> slotIds);
}
