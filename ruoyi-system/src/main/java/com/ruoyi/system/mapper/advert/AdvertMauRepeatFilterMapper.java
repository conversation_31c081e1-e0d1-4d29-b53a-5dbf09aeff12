package com.ruoyi.system.mapper.advert;

import com.ruoyi.system.entity.advert.AdvertMauRepeatFilterEntity;

import java.util.List;

/**
 * 广告MAU去重表 Mapper
 *
 * <AUTHOR>
 * @date 2024-11-5 11:37:22
 */
public interface AdvertMauRepeatFilterMapper {

    /**
     * 新增记录
     */
    int insert(AdvertMauRepeatFilterEntity advertMauRepeatFilterEntity);

    /**
     * 根据id更新
     */
    int updateById(AdvertMauRepeatFilterEntity advertMauRepeatFilterEntity);

    /**
     * 根据id获取
     */
    AdvertMauRepeatFilterEntity selectById(Long id);

    /**
     * 根据openId获取
     */
    AdvertMauRepeatFilterEntity selectByOpenId(String openId);

    /**
     * 查询列表
     */
    List<AdvertMauRepeatFilterEntity> selectList(AdvertMauRepeatFilterEntity entity);
}
