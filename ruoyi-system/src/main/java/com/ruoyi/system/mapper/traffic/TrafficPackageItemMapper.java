package com.ruoyi.system.mapper.traffic;

import com.ruoyi.system.entity.traffic.TrafficPackageItemEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流量包组成 Mapper
 *
 * <AUTHOR>
 * @date 2022-8-23 17:57:44
 */
public interface TrafficPackageItemMapper {

    /**
     * 新增记录
     */
    int batchInsert(List<TrafficPackageItemEntity> entities);

    /**
     * 根据流量包ID删除
     */
    int deleteBy(@Param("trafficPackageId") Long trafficPackageId);

    /**
     * 根据流量包ID列表查询
     *
     * @param trafficPackageIds 流量包ID列表
     * @return 流量包组成列表
     */
    List<TrafficPackageItemEntity> selectByTrafficPackageIds(@Param("trafficPackageIds") List<Long> trafficPackageIds);

    /**
     * 根据媒体ID查询流量包ID列表
     *
     * @param appIds 媒体ID列表
     * @return 流量包ID列表
     */
    List<Long> selectTrafficPackageIdsByAppIds(@Param("appIds") List<Long> appIds);

    /**
     * 查询所有的媒体ID列表
     *
     * @return 媒体ID列表
     */
    List<Long> selectTotalAppIds();
}
