package com.ruoyi.system.mapper.common;

import com.ruoyi.system.entity.common.IndustryEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 行业管理表 Mapper
 *
 * <AUTHOR>
 * @date 2023-3-6 17:34:37
 */
public interface IndustryMapper {

    /**
     * 查询列表
     */
    List<IndustryEntity> selectList(IndustryEntity param);

    /**
     * 查询列表
     */
    List<IndustryEntity> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 新增记录
     */
    int insert(IndustryEntity industryEntity);

    /**
     * 根据id更新
     */
    int updateById(IndustryEntity industryEntity);

    /**
     * 根据id获取
     */
    IndustryEntity selectById(Long id);

    /**
     * 根据行业名称查询记录是否存在
     *
     * @param industryName 行业名称
     * @return 是否存在(1.存在,2.不存在)
     */
    Integer existByIndustryName(@Param("industryName") String industryName);
}
