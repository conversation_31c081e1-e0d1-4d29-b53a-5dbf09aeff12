package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.bo.slot.CrmSlotDataBo;
import com.ruoyi.system.entity.datashow.SlotData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 广告位数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface SlotDataMapper {

    /**
     * 查询广告位数据
     *
     * @param id 广告位数据ID
     * @return 广告位数据
     */
    SlotData selectSlotDataById(Long id);

    /**
     * 查询广告位数据
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     * @return 广告位数据
     */
    SlotData selectBySlotIdAndDate(@Param("slotId") Long slotId, @Param("curDate") Date curDate);

    /**
     * 查询广告位数据列表
     *
     * @param slotData 广告位数据
     * @return 广告位数据集合
     */
    List<SlotData> selectSlotDataList(SlotData slotData);

    /**
     * 查询广告位数据列表(CRM使用)
     *
     * @param slotData 广告位数据
     * @return 广告位数据集合
     */
    List<CrmSlotDataBo> selectCrmSlotDataList(SlotData slotData);

    /**
     * 查询广告位数据汇总
     *
     * @param param 请求参数
     * @return 广告位数据汇总
     */
    SlotData selectStatisticSlotData(SlotData param);

    /**
     * 查询广告位Id列表
     *
     * @param param 请求参数
     * @return 广告位Id列表
     */
    List<Long> selectSlotIds(SlotData param);

    /**
     * 新增广告位数据
     *
     * @param slotData 广告位数据
     * @return 结果
     */
    int insertSlotData(SlotData slotData);

    /**
     * 修改广告位数据
     *
     * @param slotData 广告位数据
     * @return 结果
     */
    int updateSlotData(SlotData slotData);


    /**
     * 更新诺禾消耗
     * @param slotData
     * @return
     */
    int updateNhCost(SlotData slotData);

    /**
     * 根据日期筛选指定月份的所有广告位id数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 广告位列表
     */
    List<Long> selectDistinctSlotIdByDate(@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    /**
     * 查询媒体账号对应的媒体收益总和(昨日及之前)
     *
     * @param accountIds 账号ID列表
     * @return 媒体收益总和列表
     */
    List<SlotData> sumAppRevenueByAccountId(@Param("accountIds") List<Long> accountIds);

    /**
     * 根据媒体id和日期查询广告位数据
     * @param appId
     * @param curDate
     * @return
     */
    List<SlotData> selectDataByAppIdAndDate(@Param("appId") Long appId,@Param("curDate") Date curDate);

    /**
     * 批量更新广告位访问pv和uv
     * @param slotDataList
     * @return
     */
    int batchUpdateSlotRequestPvAndUv(@Param("slotDataList") List<SlotData> slotDataList);

    /**
     * 批量更新广告位媒体收益
     * @param slotDataList
     * @return
     */
    int batchUpdateSlotAppRevenue(@Param("slotDataList") List<SlotData> slotDataList);

    /**
     * 初始化指定日期的媒体收益数据 设置为0
     * @param curDate 日期
     * @return
     */
    int initAppRevenueByDate(@Param("curDate") Date curDate);

    /**
     * 根据广告位id列表和日期列表查询广告位数据
     * @param slotIds 广告位列表
     * @param dates 日期列表
     * @return
     */
    List<SlotData> selectBySlotIdsAndDates(@Param("slotIds") List<Long> slotIds,@Param("dates") List<Date> dates);

    /**
     * 批量新增更新媒体广告位反馈数据
     * @param datas
     * @return
     */
    int batchInsertOrUpdateAppSlotRequestData(@Param("datas") List<SlotData> datas);

    /**
     * 根据媒体id列表和日期查询所有广告位日数据
     *
     * @param appIds 媒体ID列表
     * @param curDates 日期列表
     * @return 广告位日数据列表
     */
    List<SlotData> selectListByAppIdsAndDates(@Param("appIds") Set<Long> appIds, @Param("curDates") Set<String> curDates);
}
