package com.ruoyi.system.mapper.withdraw;

import com.ruoyi.system.entity.withdraw.WithdrawRecordEntity;
import com.ruoyi.system.req.withdraw.WithdrawFileReq;
import com.ruoyi.system.req.withdraw.WithdrawListReq;
import com.ruoyi.system.req.withdraw.WithdrawStatusReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 提现记录 Mapper
 *
 * <AUTHOR>
 * @date 2021-9-9 16:57:24
 */
public interface WithdrawRecordMapper {

    /**
    * 新增记录
    */
	int insert(WithdrawRecordEntity withdrawRecordEntity);

    /**
    * 根据id更新
    */
    int updateById(WithdrawRecordEntity withdrawRecordEntity);

    /**
    * 根据id获取
    */
    WithdrawRecordEntity selectById(Long id);

    /**
     * 根据条件搜索提现列表
     *
     * @param req 参数
     * @return 提现列表
     */
    List<WithdrawRecordEntity> selectListByCondition(WithdrawListReq req);

    /**
     * 更新提现状态
     * @param req 请求参数
     * @return 结果
     */
    int updateWithdrawStatus(WithdrawStatusReq req);

    /**
     * 更新提现单文件
     * @param req 请求参数
     * @return 结果
     */
    int updateWithdrawFile(WithdrawFileReq req);

    /**
     * 查询账户的已提现和待审核的提现金额总和
     *
     * @param accountIds 账户ID列表
     * @return 账户的提现金额总和列表
     */
    List<WithdrawRecordEntity> sumWithdrawAmountByAccountId(@Param("accountIds") List<Long> accountIds);
}
