package com.ruoyi.system.mapper.order;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.domain.order.Order;

import java.util.List;

/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @date 2021/8/18
 */
@DataSource(DataSourceType.ORDER)
public interface OrderMapper {

    /**
     * 查询用户的订单
     *
     * @param param 参数
     * @return 订单列表
     */
    List<Order> selectByConsumerIdAndDate(Order param);

    /**
     * 根据订单号查询订单
     *
     * @param param 参数
     * @return 订单
     */
    Order selectByOrderId(Order param);

    /**
     * 新增订单
     *
     * @param param 参数
     * @return 影响行数
     */
    int insertOrder(Order param);

    /**
     * 更新订单
     *
     * @param param 参数
     * @return 影响行数
     */
    int updateOrder(Order param);

    /**
     * 查询用户当日首个订单
     *
     * @param param 参数
     * @return 订单号
     */
    String selectFirstOrderIdByConsumerIdAndDate(Order param);
}
