package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.app.App;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 媒体应用Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface AppMapper {

    /**
     * 查询媒体应用
     *
     * @param id 媒体应用ID
     * @return 媒体应用
     */
    App selectAppById(Long id);

    /**
     * 查询媒体应用
     *
     * @param appKey 应用标识
     * @return 媒体应用
     */
    App selectAppByAppKey(@Param("appKey") String appKey);

    /**
     * 查询媒体应用列表
     *
     * @param app 媒体应用
     * @return 媒体应用集合
     */
    List<App> selectAppList(App app);

    /**
     * 查询媒体应用列表
     *
     * @param app 媒体应用
     * @return 媒体应用集合
     */
    List<Long> selectAppIdList(App app);

    /**
     * 查询媒体应用对应的账号ID列表
     *
     * @param app 媒体应用
     * @return 账号Id列表
     */
    List<Long> selectAccountIdList(App app);

    /**
     * 查询媒体应用列表
     *
     * @param app 媒体应用
     * @return 媒体应用集合
     */
    List<App> selectSimpleAppList(App app);

    /**
     * 新增媒体应用
     *
     * @param app 媒体应用
     * @return 结果
     */
    int insertApp(App app);

    /**
     * 修改媒体应用
     *
     * @param app 媒体应用
     * @return 结果
     */
    int updateApp(App app);

    /**
     * 根据媒体账号ID过滤且分组
     *
     * @param accountIds 媒体账号ID
     * @return 分组的Map列表
     */
    List<Map<String, Long>> groupByAccountId(@Param("accountIds") List<Long> accountIds);

    /**
     * 根据媒体账号ID查询媒体ID列表
     *
     * @param accountIds 媒体账号ID列表
     * @return 媒体ID列表
     */
    List<Long> selectAppIdByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * 批量查询媒体简易信息
     *
     * @param ids 媒体ID列表
     * @return 媒体列表
     */
    List<App> selectSimpleInfoByIds(@Param("ids") List<Long> ids);

    /**
     * 查询媒体ID和名称
     *
     * @return 媒体列表
     */
    List<App> selectAppIdAndName();
}
