package com.ruoyi.system.mapper.oa.user;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.entity.oa.user.UserEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * sso账号表 Mapper
 *
 * <AUTHOR>
 * @date 2022-6-2 15:43:29
 */
@DataSource(DataSourceType.OA)
public interface UserMapper {

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 账号信息
     */
    UserEntity selectByEmail(String email);

    /**
     * 根据邮箱查询账号ID列表
     *
     * @param emails 邮箱列表
     * @return 账号列表
     */
    List<UserEntity> selectByEmails(@Param("emails") List<String> emails);

    /**
     * 根据邮箱模糊查询用户
     *
     * @param email 邮箱
     * @return 账号列表
     */
    List<UserEntity> selectByLikeEmail(String email);

    /**
     * 根据id查询用户信息
     * @param id
     * @return
     */
    UserEntity selectById(Long id);
}
