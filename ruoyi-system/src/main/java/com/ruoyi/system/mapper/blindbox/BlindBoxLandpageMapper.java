package com.ruoyi.system.mapper.blindbox;

import com.ruoyi.system.entity.blindbox.BlindBoxLandpageEntity;

import java.util.List;

/**
 * 盲盒落地页表 Mapper
 *
 * <AUTHOR>
 * @date 2022-6-13 11:10:01
 */
public interface BlindBoxLandpageMapper {

    /**
     * 查询列表
     */
    List<BlindBoxLandpageEntity> selectList(BlindBoxLandpageEntity param);

    /**
     * 新增记录
     */
    int insert(BlindBoxLandpageEntity blindBoxLandpageEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(BlindBoxLandpageEntity blindBoxLandpageEntity);

    /**
     * 根据id获取
     */
    BlindBoxLandpageEntity selectById(Long id);

    /**
     * 根据landpageKey获取
     */
    BlindBoxLandpageEntity selectByLandpageKey(String landpageKey);
}
