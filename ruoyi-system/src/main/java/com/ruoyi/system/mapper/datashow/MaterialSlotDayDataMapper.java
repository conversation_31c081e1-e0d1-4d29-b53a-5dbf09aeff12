package com.ruoyi.system.mapper.datashow;

import java.util.List;
import com.ruoyi.system.entity.datashow.MaterialSlotDayData;

/**
 * 素材广告位日数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-10-15
 */
public interface MaterialSlotDayDataMapper {

    /**
     * 查询素材广告位日数据
     *
     * @param param 查询条件
     * @return 素材广告位日数据
     */
    MaterialSlotDayData selectBy(MaterialSlotDayData param);

    /**
     * 查询素材广告位日数据列表
     *
     * @param materialSlotDayData 素材广告位日数据
     * @return 素材日数据集合
     */
    List<MaterialSlotDayData> selectMaterialSlotDayDataList(MaterialSlotDayData materialSlotDayData);

    /**
     * 新增素材日数据
     *
     * @param materialSlotDayData 素材广告位日数据
     * @return 结果
     */
    int insertMaterialSlotDayData(MaterialSlotDayData materialSlotDayData);

    /**
     * 修改素材广告位日数据
     *
     * @param param 素材日数据
     * @return 结果
     */
    int updateMaterialSlotDayData(MaterialSlotDayData param);
}
