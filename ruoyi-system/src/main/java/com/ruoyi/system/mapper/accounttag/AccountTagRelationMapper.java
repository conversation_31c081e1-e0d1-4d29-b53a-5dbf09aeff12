package com.ruoyi.system.mapper.accounttag;

import com.ruoyi.system.entity.accounttag.AccountTagRelationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账号标签关联表 Mapper
 *
 * <AUTHOR>
 * @date 2023-3-6 10:53:35
 */
public interface AccountTagRelationMapper {
    /**
     * 新增记录
     */
    int insert(AccountTagRelationEntity accountTagRelationEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(AccountTagRelationEntity accountTagRelationEntity);

    /**
     * 根据id获取
     */
    AccountTagRelationEntity selectById(Long id);

    /**
     * 根据账号id和标签类型删除标签
     * @param accountId
     * @param type
     * @return
     */
    int deleteByAccountIdAndType(@Param("accountId") Long accountId, @Param("type") Integer type);
    /**
     * 批量新增
     * @param entities
     * @return
     */
    int batchInsert(@Param("entities") List<AccountTagRelationEntity> entities);

    /**
     * 根据账号id和类型获取账号标签
     * @param accountId
     * @param tagType
     * @return
     */
    List<AccountTagRelationEntity> selectListByAccountType(@Param("accountId") Long accountId,@Param("tagType") Integer tagType);

    /**
     * 根据标签id列表查询账号
     * @param tagIds
     * @return
     */
    List<AccountTagRelationEntity> selectListByTagIds(@Param("tagIds") List<Long> tagIds);


}
