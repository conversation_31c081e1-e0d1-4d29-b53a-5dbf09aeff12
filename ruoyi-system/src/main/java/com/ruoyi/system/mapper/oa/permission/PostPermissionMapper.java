package com.ruoyi.system.mapper.oa.permission;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.entity.oa.permission.PostPermissionEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 职位权限关联表 Mapper
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:18
 */
@DataSource(DataSourceType.OA)
public interface PostPermissionMapper {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    int insert(PostPermissionEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    int updateById(PostPermissionEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    PostPermissionEntity selectById(Long id);

    /**
     * 根据职位ID和系统ID查询职位权限
     *
     * @param postId 职位ID
     * @param systemId 系统ID
     * @return 职位权限
     */
    PostPermissionEntity selectByPostIdAndSystemId(@Param("postId") Long postId, @Param("systemId") Long systemId);
}
