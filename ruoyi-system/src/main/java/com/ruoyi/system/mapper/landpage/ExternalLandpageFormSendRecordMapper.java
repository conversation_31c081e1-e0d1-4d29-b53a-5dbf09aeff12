package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.landpage.ExternalLandpageFormSendRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 外部落地页表单上报记录 Mapper
 *
 * <AUTHOR>
 * @date 2023-4-11 15:38:53
 */
public interface ExternalLandpageFormSendRecordMapper {

    /**
     * 新增记录
     */
    int insert(ExternalLandpageFormSendRecordEntity externalLandpageFormSendRecordEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(ExternalLandpageFormSendRecordEntity externalLandpageFormSendRecordEntity);

    /**
     * 根据id获取
     */
    ExternalLandpageFormSendRecordEntity selectById(Long id);

    /**
     * 查询已回传的外部表单编号
     *
     * @param externalNoList 外部表单编号列表
     * @return 已回传的外部表单编号列表
     */
    List<String> selectExternalNo(@Param("list") List<String> externalNoList);

    /**
     * 查询广告主ID列表
     *
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIds();
}
