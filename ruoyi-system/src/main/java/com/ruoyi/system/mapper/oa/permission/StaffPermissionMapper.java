package com.ruoyi.system.mapper.oa.permission;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.entity.oa.permission.StaffPermissionEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 用户权限关联表 Mapper
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:19
 */
@DataSource(DataSourceType.OA)
public interface StaffPermissionMapper {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    int insert(StaffPermissionEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    int updateById(StaffPermissionEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    StaffPermissionEntity selectById(Long id);

    /**
     * 获取职员对应系统的权限
     *
     * @param staffId 职员ID
     * @param systemId 系统ID
     * @return 权限
     */
    StaffPermissionEntity selectByStaffIdAndSystemId(@Param("staffId") Long staffId, @Param("systemId") Long systemId);
}
