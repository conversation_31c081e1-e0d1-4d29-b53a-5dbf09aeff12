package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.AppData;
import com.ruoyi.system.entity.datashow.SlotData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 媒体数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface AppDataMapper {

    /**
     * 查询媒体数据
     *
     * @param id 媒体数据ID
     * @return 媒体数据
     */
    AppData selectAppDataById(Long id);

    /**
     * 查询媒体数据
     *
     * @param appId 媒体Id
     * @param curDate 日期
     * @return 广告位数据
     */
    AppData selectByAppIdAndDate(@Param("appId") Long appId, @Param("curDate") Date curDate);

    /**
     * 查询媒体数据列表
     *
     * @param appData 媒体数据
     * @return 媒体数据集合
     */
    List<AppData> selectAppDataList(AppData appData);

    /**
     * 新增媒体数据
     *
     * @param appData 媒体数据
     * @return 结果
     */
    int insertAppData(AppData appData);

    /**
     * 批量新增更新媒体数据 只更新广告位请求pv，uv，媒体收益
     * @param appDataList 媒体数据
     * @return 结果
     */
    int batchInsertOrUpdateAppData(@Param("slotDataList") List<AppData> appDataList);

    /**
     * 修改媒体数据
     *
     * @param appData 媒体数据
     * @return 结果
     */
    int updateAppData(AppData appData);

    /**
     * 修改媒体诺禾消耗数据
     *
     * @param appData 媒体数据
     * @return 结果
     */
    int updateAppNhCostData(AppData appData);

    /**
     * 根据日期筛选指定月份的所有媒体id数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 媒体列表
     */
    List<Long> selectDistinctAppIdByDate(@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    /**
     * 初始化指定日期的媒体收益数据 设置为0
     * @param curDate 日期
     * @return
     */
    int initAppRevenueByDate(@Param("curDate") Date curDate);
}
