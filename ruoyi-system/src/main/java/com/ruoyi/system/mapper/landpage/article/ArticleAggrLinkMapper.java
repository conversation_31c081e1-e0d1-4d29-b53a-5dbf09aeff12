package com.ruoyi.system.mapper.landpage.article;

import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkListParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 文章聚合链接表 Mapper
 *
 * <AUTHOR>
 * @date 2023-12-1 15:12:44
 */
public interface ArticleAggrLinkMapper {

    /**
     * 查询列表
     */
    List<ArticleAggrLinkEntity> selectList(ArticleAggrLinkListParamBo param);

    /**
     * 新增记录
     */
    int insert(ArticleAggrLinkEntity articleAggrLinkEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(ArticleAggrLinkEntity articleAggrLinkEntity);

    /**
     * 根据id获取
     */
    ArticleAggrLinkEntity selectById(Long id);

    /**
     * 根据Key获取
     */
    ArticleAggrLinkEntity selectByKey(@Param("key") String key);

    /**
     * 查询所有的Key
     */
    List<String> selectTotalKey();

    /**
     * 根据名称查询文章聚合链接是否存在
     *
     * @param name 链接名称
     * @param id
     * @return 是否存在(1.存在, null.不存在)
     */
    Integer existByName(@Param("name") String name,@Param("advertiserId") Long advertiserId,@Param("id")Long id);

    /**
     * 根据广告主id查询今日链接
     * @param advertiserId
     * @return
     */
    ArticleAggrLinkEntity selectTodayLinkByAdvertiserId(@Param("advertiserId") Long advertiserId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);
}
