package com.ruoyi.system.mapper.system;

import com.ruoyi.system.entity.system.SysConfigDomainEntity;

/**
 * 参数配置表域名为主查询配置信息 Mapper
 *
 * <AUTHOR>
 * @date 2023-6-7 16:25:42
 */
public interface SysConfigDomainMapper {
    /**
     * 新增记录
     */
    int insert(SysConfigDomainEntity sysConfigDomainEntity);

    /**
     * 根据id删除
     */
    int deleteById(Integer id);

    /**
     * 根据id更新
     */
    int updateById(SysConfigDomainEntity sysConfigDomainEntity);

    /**
     * 根据id获取
     */
    SysConfigDomainEntity selectById(Integer id);
    SysConfigDomainEntity selectByKey(String configKey);


}
