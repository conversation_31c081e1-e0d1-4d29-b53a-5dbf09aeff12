package com.ruoyi.system.mapper.advertiser;

import com.ruoyi.system.entity.advertiser.AdvertiserBudgetEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告主预算表 Mapper
 *
 * <AUTHOR>
 * @date 2023-4-17 11:42:58
 */
public interface AdvertiserBudgetMapper {

    /**
     * 新增记录
     */
    int insert(AdvertiserBudgetEntity advertiserBudgetEntity);

    /**
     * 根据id更新
     */
    int updateById(AdvertiserBudgetEntity advertiserBudgetEntity);

    /**
     * 根据id获取
     */
    AdvertiserBudgetEntity selectById(Long id);

    /**
     * 根据广告主ID查询
     *
     * @param accountId 广告主ID
     * @return 广告主预算
     */
    AdvertiserBudgetEntity selectByAccountId(@Param("accountId") Long accountId);

    /**
     * 根据广告主ID列表查询
     *
     * @param accountIds 广告主ID列表
     * @return 广告主预算列表
     */
    List<AdvertiserBudgetEntity> selectListByAccountId(@Param("accountIds") List<Long> accountIds);
}
