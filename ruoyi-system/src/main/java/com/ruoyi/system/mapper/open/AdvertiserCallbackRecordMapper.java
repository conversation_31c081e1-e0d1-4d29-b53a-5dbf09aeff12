package com.ruoyi.system.mapper.open;

import com.ruoyi.system.entity.open.AdvertiserCallbackRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告主回调记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-15
 */
public interface AdvertiserCallbackRecordMapper {

    /**
     * 查询记录
     *
     * @param param 参数
     * @return 记录
     */
    AdvertiserCallbackRecord selectBy(AdvertiserCallbackRecord param);

    /**
     * 新增记录
     *
     * @param record 参数
     * @return 结果
     */
    int insert(AdvertiserCallbackRecord record);

    /**
     * 根据订单查询广告主回调记录
     *
     * @param orderNoList 订单号列表
     * @return 广告主回调记录列表
     */
    List<AdvertiserCallbackRecord> selectStatusByOrderNo(@Param("orderNoList") List<String> orderNoList);
}
