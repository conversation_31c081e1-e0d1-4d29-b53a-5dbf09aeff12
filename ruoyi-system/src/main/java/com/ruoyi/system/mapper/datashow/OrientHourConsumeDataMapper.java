package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.OrientHourConsumeData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告配置分时段消耗数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-08-30
 */
public interface OrientHourConsumeDataMapper {

    /**
     * 查询广告配置分时段消耗数据
     * 
     * @param id 广告配置分时段消耗数据ID
     * @return 广告配置分时段消耗数据
     */
    OrientHourConsumeData selectOrientHourConsumeDataById(Long id);

    /**
     * 查询广告配置消耗数据
     *
     * @param curDate 日期
     * @param curHour 小时
     * @param orientId 广告配置ID
     * @return 广告配置消耗数据
     */
    OrientHourConsumeData selectByDateHourAndOrientId(@Param("curDate") Date curDate, @Param("curHour") Integer curHour,
                                                      @Param("orientId") Long orientId);

    /**
     * 修改广告配置分时段消耗数据
     *
     * @param orientHourConsumeData 广告配置分时段消耗数据
     * @return 结果
     */
    int addConsumeData(OrientHourConsumeData orientHourConsumeData);

    /**
     * 查询广告配置分时段消耗数据列表
     * 
     * @param orientHourConsumeData 广告配置分时段消耗数据
     * @return 广告配置分时段消耗数据集合
     */
    List<OrientHourConsumeData> selectOrientHourConsumeDataList(OrientHourConsumeData orientHourConsumeData);

    /**
     * 新增广告配置分时段消耗数据
     * 
     * @param orientHourConsumeData 广告配置分时段消耗数据
     * @return 结果
     */
    int insertOrientHourConsumeData(OrientHourConsumeData orientHourConsumeData);

    /**
     * 修改广告配置分时段消耗数据
     * 
     * @param orientHourConsumeData 广告配置分时段消耗数据
     * @return 结果
     */
    int updateOrientHourConsumeData(OrientHourConsumeData orientHourConsumeData);
}
