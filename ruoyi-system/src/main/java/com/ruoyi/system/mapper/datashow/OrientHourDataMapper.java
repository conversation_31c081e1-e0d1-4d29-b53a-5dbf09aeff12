package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.bo.advert.OrientDayDataBo;
import com.ruoyi.system.bo.advert.OrientHourDataParam;
import com.ruoyi.system.bo.advert.OrientHourDataUpdateParam;
import com.ruoyi.system.entity.datashow.OrientHourDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告配置维度小时数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-7-11 19:48:52
 */
public interface OrientHourDataMapper {

    /**
     * 查询配置时段数据
     *
     * @param param 查询条件
     * @return 配置时段数据
     */
    OrientHourDataEntity selectBy(OrientHourDataEntity param);

    /**
     * 查询配置数据是否存在
     *
     * @param param 查询条件
     * @return 1.存在,null.不存在
     */
    Integer isExistBy(OrientHourDataParam param);

    /**
     * 新增配置数据
     */
    int insert(OrientHourDataEntity entity);

    /**
     * 更新配置数据
     */
    int updateById(OrientHourDataUpdateParam param);

    /**
     * 查询配置日数据列表
     *
     * @param param 查询条件
     * @return 配置日数据集合
     */
    List<OrientDayDataBo> selectOrientDayDataList(OrientHourDataParam param);

    /**
     * 查询配置时段数据列表
     *
     * @param param 查询条件
     * @return 配置时段数据集合
     */
    List<OrientDayDataBo> selectOrientHourDataList(OrientHourDataParam param);

    /**
     * 查询配置时段数据列表
     *
     * @param param 查询条件
     * @return 配置时段数据集合
     */
    List<OrientDayDataBo> selectOrientHourDataListGroupByDateHour(OrientHourDataParam param);

    /**
     * 查询配置分媒体日数据列表
     *
     * @param param 查询条件
     * @return 配置分媒体日数据集合
     */
    List<OrientDayDataBo> selectOrientAppDayDataList(OrientHourDataParam param);

    /**
     * 查询配置分广告位日数据列表
     *
     * @param param 查询条件
     * @return 配置分广告位日数据集合
     */
    List<OrientDayDataBo> selectOrientSlotDayDataList(OrientHourDataParam param);

    /**
     * 查询配置数据汇总
     *
     * @param param 查询条件
     * @return 配置数据汇总
     */
    OrientDayDataBo selectStatisticOrientData(OrientHourDataParam param);

    /**
     * 查询配置数据(仅用于落地页数据)
     *
     * @param advertIds 广告位ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param invisibleDateList 排除的日期列表
     * @return 配置数据列表
     */
    List<OrientDayDataBo> selectOrientDataForLandpage(@Param("advertIds") List<Long> advertIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("invisibleDateList") List<Date> invisibleDateList);
}
