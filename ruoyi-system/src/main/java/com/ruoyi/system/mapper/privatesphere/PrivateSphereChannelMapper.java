package com.ruoyi.system.mapper.privatesphere;

import com.ruoyi.system.bo.privatesphere.PrivateSphereChannelListBO;
import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 私域渠道表 Mapper
 *
 * <AUTHOR>
 * @date 2023-2-8 15:51:57
 */
public interface PrivateSphereChannelMapper {
    /**
     * 新增记录
     */
    int insert(PrivateSphereChannelEntity privateSphereChannelEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(PrivateSphereChannelEntity privateSphereChannelEntity);

    /**
     * 根据id获取
     */
    PrivateSphereChannelEntity selectById(Long id);

    /**
     * 根据产品id和渠道名获取
     * @param productId
     * @param channelName
     * @return
     */
    PrivateSphereChannelEntity selectByProductAndChannelName(@Param("productId") Long productId, @Param("channel") String channelName);

    /**
     * 根据条件查询渠道列表
     * @param bo
     * @return
     */
    List<PrivateSphereChannelEntity> selectListByParam(PrivateSphereChannelListBO bo);


}
