package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.OrientDayConsumeData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告配置日消耗数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-24
 */
public interface OrientDayConsumeDataMapper {

    /**
     * 查询广告配置消耗数据
     *
     * @param curDate 日期
     * @param orientId 广告配置ID
     * @return 广告配置消耗数据
     */
    OrientDayConsumeData selectByDateAndOrientId(@Param("curDate") Date curDate, @Param("orientId") Long orientId);

    /**
     * 修改广告配置日消耗数据
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 结果
     */
    int addConsumeData(OrientDayConsumeData orientDayConsumeData);

    /**
     * 查询广告配置日消耗数据列表
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 广告配置日消耗数据集合
     */
    List<OrientDayConsumeData> selectOrientDayConsumeDataList(OrientDayConsumeData orientDayConsumeData);

    /**
     * 新增广告配置日消耗数据
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 结果
     */
    int insertOrientDayConsumeData(OrientDayConsumeData orientDayConsumeData);

    /**
     * 修改广告配置日消耗数据
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 结果
     */
    int updateOrientDayConsumeData(OrientDayConsumeData orientDayConsumeData);

    /**
     * 查询广告配置消耗数据
     *
     * @param curDate 日期
     * @param orientIds 广告配置ID列表
     * @return 广告配置消耗数据
     */
    List<OrientDayConsumeData> selectByDateAndOrientIds(@Param("curDate") Date curDate, @Param("orientIds") List<Long> orientIds);
}
