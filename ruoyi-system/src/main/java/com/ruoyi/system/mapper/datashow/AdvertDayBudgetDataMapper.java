package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.AdvertDayBudgetData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告日预算数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-10-19
 */
public interface AdvertDayBudgetDataMapper {

    /**
     * 查询广告预算数据
     *
     * @param curDate 日期
     * @param advertId 广告ID
     * @return 广告预算数据
     */
    AdvertDayBudgetData selectByDateAndAdvertId(@Param("curDate") Date curDate, @Param("advertId") Long advertId);

    /**
     * 查询广告日预算数据列表
     *
     * @param advertDayBudgetData 广告日预算数据
     * @return 广告日预算数据集合
     */
    List<AdvertDayBudgetData> selectAdvertDayBudgetDataList(AdvertDayBudgetData advertDayBudgetData);

    /**
     * 新增广告日预算数据
     *
     * @param advertDayBudgetData 广告日预算数据
     * @return 结果
     */
    int insertAdvertDayBudgetData(AdvertDayBudgetData advertDayBudgetData);

    /**
     * 修改广告日预算数据
     *
     * @param advertDayBudgetData 广告日预算数据
     * @return 结果
     */
    int updateBudget(AdvertDayBudgetData advertDayBudgetData);
}
