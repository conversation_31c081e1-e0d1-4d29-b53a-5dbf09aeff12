package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.SlotHourData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告位分时段数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-17
 */
public interface SlotHourDataMapper {

    /**
     * 查询广告位分时段数据列表
     *
     * @param slotHourData 广告位数据
     * @return 广告位数据集合
     */
    List<SlotHourData> selectSlotHourDataList(SlotHourData slotHourData);

    /**
     * 查询广告位数据
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     * @return 广告位数据
     */
    SlotHourData selectBySlotIdAndDateHour(@Param("slotId") Long slotId, @Param("curDate") Date curDate, @Param("curHour") Integer curHour);

    /**
     * 新增广告位数据
     *
     * @param slotHourData 广告位数据
     * @return 结果
     */
    int insertSlotHourData(SlotHourData slotHourData);

    /**
     * 修改广告位数据
     *
     * @param slotHourData 广告位数据
     * @return 结果
     */
    int updateSlotHourData(SlotHourData slotHourData);
}
