package com.ruoyi.system.mapper.common;

import com.ruoyi.system.entity.common.DocumentEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档表 Mapper
 *
 * <AUTHOR>
 * @date 2022-12-1 17:08:32
 */
public interface DocumentMapper {

    /**
     * 查询列表
     */
    List<DocumentEntity> selectList(DocumentEntity param);

    /**
     * 新增记录
     */
    int insert(DocumentEntity documentEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(DocumentEntity documentEntity);

    /**
     * 根据id获取
     */
    DocumentEntity selectById(Long id);

    /**
     * 同公司下是否存在同名文档或者相同链接
     *
     * @param id 文档ID
     * @param companyName 公司名称
     * @param documentName 文档链接
     * @param documentUrl 文档链接
     * @return true.存在,false.不存在
     */
    Integer existDuplication(@Param("id") Long id, @Param("companyName") String companyName, @Param("documentName") String documentName, @Param("documentUrl") String documentUrl);
}
