package com.ruoyi.system.mapper.redpacket;

import com.ruoyi.system.entity.redpacket.PhoneRedPacketRecordEntity;
import com.ruoyi.system.req.cashback.CashBackListReq;

import java.util.List;

/**
 * 表单用户领红包记录表 Mapper
 *
 * <AUTHOR>
 * @date 2022-5-26 17:50:17
 */
public interface PhoneRedPacketRecordMapper {

    /**
     * 新增记录
     */
    int insert(PhoneRedPacketRecordEntity record);

    /**
     * 根据id更新
     */
    int updateById(PhoneRedPacketRecordEntity record);

    /**
     * 查询手机号对应的记录
     *
     * @param phone 下单手机号
     * @return 记录
     */
    PhoneRedPacketRecordEntity selectByPhone(String phone);

    /**
     * 根据参数分页查询返现列表 后台用
     * @param req
     * @return
     */
    List<PhoneRedPacketRecordEntity> selectByReq(CashBackListReq req);
}
