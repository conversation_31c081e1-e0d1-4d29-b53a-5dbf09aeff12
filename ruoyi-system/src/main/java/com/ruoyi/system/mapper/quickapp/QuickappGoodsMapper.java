package com.ruoyi.system.mapper.quickapp;

import com.ruoyi.system.entity.quickapp.QuickappGoodsEntity;

import java.util.List;

/**
 * 快应用商品表 Mapper
 *
 * <AUTHOR>
 * @date 2022-8-15 14:15:35
 */
public interface QuickappGoodsMapper {
    /**
     * 新增记录
     */
    int insert(QuickappGoodsEntity quickappGoodsEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(QuickappGoodsEntity quickappGoodsEntity);

    /**
     * 根据id获取
     */
    QuickappGoodsEntity selectById(Long id);


    /**
     * 分页查询列表
     * @return
     */
    List<QuickappGoodsEntity> selectList();

}
