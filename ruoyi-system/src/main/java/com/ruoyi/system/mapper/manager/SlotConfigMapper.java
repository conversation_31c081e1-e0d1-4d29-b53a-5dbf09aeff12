package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.slot.SlotConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告位配置Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-15
 */
public interface SlotConfigMapper {

    /**
     * 查询广告位配置
     *
     * @param id 广告位配置ID
     * @return 广告位配置
     */
    SlotConfig selectSlotConfigById(Long id);

    /**
     * 查询广告位配置
     *
     * @param slotId 广告位ID
     * @return 广告位配置
     */
    SlotConfig selectBySlotId(@Param("slotId") Long slotId);

    /**
     * 查询广告位配置列表
     *
     * @param slotConfig 广告位配置
     * @return 广告位配置集合
     */
    List<SlotConfig> selectSlotConfigList(SlotConfig slotConfig);

    /**
     * 查询广告位配置列表
     *
     * @param slotIds 广告位ID列表
     * @return 广告位配置集合
     */
    List<SlotConfig> selectListBySlotIds(@Param("slotIds") List<Long> slotIds);

    /**
     * 新增广告位配置
     *
     * @param slotConfig 广告位配置
     * @return 结果
     */
    int insertSlotConfig(SlotConfig slotConfig);

    /**
     * 修改广告位配置
     *
     * @param slotConfig 广告位配置
     * @return 结果
     */
    int updateSlotConfig(SlotConfig slotConfig);

    /**
     * 修改广告位配置
     *
     * @param config 广告位配置
     * @return 结果
     */
    int updateById(SlotConfig config);
}
