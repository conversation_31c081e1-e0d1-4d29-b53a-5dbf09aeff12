package com.ruoyi.system.mapper.tagmanager;

import com.ruoyi.system.entity.tagmanager.DomainTagRelationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 域名标签关联表 Mapper
 *
 * <AUTHOR>
 * @date 2022-12-26 14:32:58
 */
public interface DomainTagRelationMapper {
    /**
     * 新增记录
     */
    int insert(DomainTagRelationEntity domainTagRelationEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(DomainTagRelationEntity domainTagRelationEntity);

    /**
     * 根据id获取
     */
    DomainTagRelationEntity selectById(Long id);


    /**
     * 根据标签id查询使用的总数
     *
     * @param tagId
     * @return
     */
    int countByTagId(@Param("tagId") Long tagId);

    /**
     * 根据标签id列表查询被使用的标签id列表 ，删除标签时判断用
     * @param tagIds
     * @return
     */
    List<Long> selectTagIdsByTagIds(@Param("tagIds") List<Long> tagIds);

    /**
     * 根据域名id查询标签列表
     * @param domainId
     * @return
     */
    List<Long> selectTagIdsByDomainId(Long domainId);

    /**
     * 根据域名id删除标签
     * @param domainId
     * @return
     */
    int deleteByDomainId(Long domainId);

    /**
     * 批量新增
     * @param entities
     * @return
     */
    int batchInsert(@Param("entities") List<DomainTagRelationEntity> entities);


}
