package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.AdvertiserConsumeData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告主日消耗数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-19
 */
public interface AdvertiserConsumeDataMapper {

    /**
     * 查询广告主日消耗数据列表
     *
     * @param advertiserConsumeData 广告主日消耗数据
     * @return 广告主日消耗数据集合
     */
    List<AdvertiserConsumeData> selectAdvertiserConsumeDataList(AdvertiserConsumeData advertiserConsumeData);

    /**
     * 查询广告主日消耗数据
     *
     * @param curDate 日期
     * @param advertiserId 广告主ID
     * @return 广告配置消耗数据
     */
    AdvertiserConsumeData selectByDateAndAdvertiserId(@Param("curDate") Date curDate, @Param("advertiserId") Long advertiserId);

    /**
     * 新增广告主日消耗数据
     *
     * @param advertiserConsumeData 广告主日消耗数据
     * @return 结果
     */
    int insertAdvertiserConsumeData(AdvertiserConsumeData advertiserConsumeData);

    /**
     * 修改广告主日消耗数据
     *
     * @param advertiserConsumeData 广告主日消耗数据
     * @return 结果
     */
    int updateAdvertiserConsumeData(AdvertiserConsumeData advertiserConsumeData);

    /**
     * 修改广告主日消耗数据
     *
     * @param id 数据记录ID
     * @param budget 广告主日预算
     * @return 结果
     */
    int updateBudget(@Param("id") Long id, @Param("budget") Long budget);

    /**
     * 修改广告主日消耗数据
     *
     * @param advertiserConsumeData 广告主日消耗数据
     * @return 结果
     */
    int addConsumeData(AdvertiserConsumeData advertiserConsumeData);

    /**
     * 根据广告主ID列表和日期查询消耗
     *
     * @param advertiserIds 广告主ID列表
     * @param curDate 日期
     * @return 广告主消耗列表
     */
    List<AdvertiserConsumeData> selectListByAdvertiserIdAndDate(@Param("advertiserIds") List<Long> advertiserIds, @Param("curDate") Date curDate);
}
