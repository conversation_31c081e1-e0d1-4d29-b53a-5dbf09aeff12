package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.AdvertDayConsumeData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告日消耗数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-24
 */
public interface AdvertDayConsumeDataMapper {

    /**
     * 查询广告日消耗数据
     *
     * @param id 广告日消耗数据ID
     * @return 广告日消耗数据
     */
    AdvertDayConsumeData selectAdvertDayConsumeDataById(Long id);

    /**
     * 查询广告消耗数据
     *
     * @param curDate 日期
     * @param advertId 广告ID
     * @return 广告消耗数据
     */
    AdvertDayConsumeData selectByDateAndAdvertId(@Param("curDate") Date curDate, @Param("advertId") Long advertId);

    /**
     * 修改广告日消耗数据
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 结果
     */
    int addConsumeData(AdvertDayConsumeData advertDayConsumeData);

    /**
     * 查询广告日消耗数据列表
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 广告日消耗数据集合
     */
    List<AdvertDayConsumeData> selectAdvertDayConsumeDataList(AdvertDayConsumeData advertDayConsumeData);

    /**
     * 新增广告日消耗数据
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 结果
     */
    int insertAdvertDayConsumeData(AdvertDayConsumeData advertDayConsumeData);

    /**
     * 修改广告日消耗数据
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 结果
     */
    int updateAdvertDayConsumeData(AdvertDayConsumeData advertDayConsumeData);
}
