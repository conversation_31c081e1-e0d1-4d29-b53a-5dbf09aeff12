package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.AdvertQuarterDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 广告维度时刻数据表 Mapper
 *
 * <AUTHOR>
 * @date 2022-10-21 14:39:16
 */
public interface AdvertQuarterDataMapper {

    /**
     * 查询广告维度时刻数据
     *
     * @param param 查询条件
     * @return 广告维度时刻数据
     */
    AdvertQuarterDataEntity selectBy(AdvertQuarterDataEntity param);

    /**
     * 查询广告维度时刻数据
     *
     * @param advertId 广告ID
     * @param curDate 日期
     * @return 广告维度时刻数据
     */
    AdvertQuarterDataEntity selectByAdvertAndDate(@Param("advertId") Long advertId, @Param("curDate") Date curDate);

    /**
     * 新增记录
     */
    int insert(AdvertQuarterDataEntity advertQuarterDataEntity);

    /**
     * 根据id更新
     */
    int updateById(AdvertQuarterDataEntity advertQuarterDataEntity);

    /**
     * 根据id获取
     */
    AdvertQuarterDataEntity selectById(Long id);
}
