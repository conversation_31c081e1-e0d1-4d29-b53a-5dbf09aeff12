package com.ruoyi.system.mapper.quickapp;

import com.ruoyi.system.entity.quickapp.QuickappDynamicEntity;

import java.util.List;

/**
 * 快应用发布动态表 Mapper
 *
 * <AUTHOR>
 * @date 2022-8-8 10:58:02
 */
public interface QuickappDynamicMapper {
    /**
     * 新增记录
     */
    int insert(QuickappDynamicEntity quickappDynamicEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(QuickappDynamicEntity quickappDynamicEntity);

    /**
     * 根据id获取
     */
    QuickappDynamicEntity selectById(Long id);

    /**
     * 查询列表
     * @return
     */
    List<QuickappDynamicEntity> selectList();

}
