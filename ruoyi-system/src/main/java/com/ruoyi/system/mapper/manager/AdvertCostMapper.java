package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.advert.AdvertCost;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告每日成本Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-16
 */
public interface AdvertCostMapper {

    /**
     * 新增/更新广告结算指标和成本
     *
     * @param advertCost 更新对象
     * @return 结果
     */
    int insertOrUpdateAdvertCost(AdvertCost advertCost);

    /**
     * 根据广告id查询当日结算成本
     * @param advertId
     * @return
     */
    AdvertCost selectById(@Param("advertId") Long advertId, @Param("curDate")String date);

    /**
     * 查询广告结算指标成本列表
     *
     * @param param 参数
     * @return 广告结算指标成本列表
     */
    List<AdvertCost> selectList(AdvertCost param);

    /**
     * 分页获取昨天的所有广告成本列表
     * @param id 上一个广告最大id
     * @param pageSize pagesize
     * @return
     */
    List<AdvertCost> selectListByYesterday(@Param("date") String date,@Param("id") Long id, @Param("pageSize") Integer pageSize);

    /**
     * 批量新增更新
     * @param advertCosts
     * @return
     */
    int batchInsertOrUpdate(@Param("advertCosts") List<AdvertCost> advertCosts);
}
