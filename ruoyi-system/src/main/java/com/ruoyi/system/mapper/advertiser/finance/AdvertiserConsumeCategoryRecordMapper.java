package com.ruoyi.system.mapper.advertiser.finance;

import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeCategorySumBo;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeCategoryRecordEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeRecordUpdateReq;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告主结算分类消费记录表 Mapper
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
public interface AdvertiserConsumeCategoryRecordMapper {

    /**
     * 新增记录
     */
    int insert(AdvertiserConsumeCategoryRecordEntity record);

    /**
     * 根据ID更新广告主消费记录
     */
    int updateByReq(AdvertiserConsumeRecordUpdateReq req);

    /**
     * 根据广告主ID和日期查询消费记录
     *
     * @param accountId 广告主ID
     * @param curDate 日期
     * @param billingType 结算指标类型
     * @return 广告主消费记录
     */
    AdvertiserConsumeCategoryRecordEntity selectBy(@Param("accountId") Long accountId, @Param("curDate") Date curDate, @Param("billingType") Integer billingType);

    /**
     * 根据账号和日期统计
     *
     * @param accountId 账号id
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param billingType 计费类型
     * @return 广告主消费汇总
     */
    List<AdvertiserConsumeCategorySumBo> selectSumByDateAndAccountId(@Param("accountId") Long accountId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("billingType") Integer billingType);
}
