package com.ruoyi.system.mapper.blindbox;

import com.ruoyi.system.entity.blindbox.BlindBoxLandpageRecordEntity;

/**
 * 盲盒落地页提交记录表 Mapper
 *
 * <AUTHOR>
 * @date 2022-6-13 11:10:16
 */
public interface BlindBoxLandpageRecordMapper {

    /**
     * 新增记录
     */
    int insert(BlindBoxLandpageRecordEntity blindBoxLandpageRecordEntity);

    /**
     * 根据id更新
     */
    int updateById(BlindBoxLandpageRecordEntity blindBoxLandpageRecordEntity);

    /**
     * 根据id获取
     */
    BlindBoxLandpageRecordEntity selectById(Long id);
}
