package com.ruoyi.system.mapper.privatesphere;

import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelNumberEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 私域渠道号表 Mapper
 *
 * <AUTHOR>
 * @date 2023-2-8 17:03:55
 */
public interface PrivateSphereChannelNumberMapper {
    /**
     * 新增记录
     */
    int insert(PrivateSphereChannelNumberEntity privateSphereChannelNumberEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(PrivateSphereChannelNumberEntity privateSphereChannelNumberEntity);

    /**
     * 根据id获取
     */
    PrivateSphereChannelNumberEntity selectById(Long id);

    /**
     * 批量新增更新
     * @param entities
     * @return
     */
    int batchInsertOrUpdate(@Param("entities") List<PrivateSphereChannelNumberEntity> entities);

    /**
     * 根据渠道id列表查询渠道编号列表
     * @param channelIds
     * @return
     */
    List<PrivateSphereChannelNumberEntity> selectByChannelIds(@Param("channelIds") List<Long> channelIds);


}
