package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.landpage.AdvertiserFormDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告主日维度表单数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-2-15 11:35:07
 */
public interface AdvertiserFormDataMapper {

    /**
     * 新增记录
     */
    int insert(AdvertiserFormDataEntity advertiserFormDataEntity);

    /**
     * 根据id更新
     */
    int updateById(AdvertiserFormDataEntity advertiserFormDataEntity);

    /**
     * 根据id获取
     */
    AdvertiserFormDataEntity selectById(Long id);

    /**
     * 根据日期和广告主ID获取
     *
     * @param curDate 日期
     * @param advertiserId 广告主ID
     * @return 广告主落地页表单数据
     */
    AdvertiserFormDataEntity selectBy(@Param("curDate") Date curDate, @Param("advertiserId") Long advertiserId);

    /**
     * 查询广告主落地页表单数据列表
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @return 广告主落地页表单数据列表
     */
    List<AdvertiserFormDataEntity> selectListByDateAndAdvertiserIds(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("advertiserIds") List<Long> advertiserIds);

    /**
     * 查询广告主落地页表单数据汇总
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @return 落地页表单数据汇总
     */
    AdvertiserFormDataEntity sumFormDataByDateAndAdvertiserIds(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("advertiserIds") List<Long> advertiserIds);
}
