package com.ruoyi.system.mapper.traffic;

import com.ruoyi.system.bo.traffic.TrafficPackageListBo;
import com.ruoyi.system.entity.traffic.TrafficPackageEntity;
import com.ruoyi.system.req.traffic.TrafficPackageListReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流量包 Mapper
 *
 * <AUTHOR>
 * @date 2022-8-23 10:31:21
 */
public interface TrafficPackageMapper {

    /**
     * 查询列表
     */
    List<TrafficPackageListBo> selectList(TrafficPackageListReq req);

    /**
     * 新增记录
     */
    int insert(TrafficPackageEntity entity);

    /**
     * 根据id更新
     */
    int updateById(TrafficPackageEntity entity);

    /**
     * 根据id获取
     */
    TrafficPackageEntity selectById(Long id);

    /**
     * 根据流量包名称判断是否存在
     *
     * @param name 流量包名称
     * @return 是否存在
     */
    Integer existByName(@Param("name") String name);
}
