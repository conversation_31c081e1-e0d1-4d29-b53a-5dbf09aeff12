package com.ruoyi.system.mapper.permission;

import com.ruoyi.system.entity.permission.PermissionEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限表 Mapper
 *
 * <AUTHOR>
 * @date 2022-6-23 16:24:50
 */
public interface SspPermissionMapper {
    /**
     * 新增记录
     */
    int insert(PermissionEntity permissionEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(PermissionEntity permissionEntity);

    /**
     * 根据id获取
     */
    PermissionEntity selectById(Long id);

    /**
     * 根据id列表查询权限
     *
     * @param ids id列表
     * @return 权限列表
     */
    List<PermissionEntity> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 查询所有权限
     *
     * @return 权限列表
     */
    List<PermissionEntity> selectAllPermission();
}
