package com.ruoyi.system.mapper.advertiser;

import com.ruoyi.system.bo.advertiser.AdvertiserIndustryBo;
import com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告主资质信息 Mapper
 *
 * <AUTHOR>
 * @date 2022-4-28 17:11:16
 */
public interface AdvertiserQualificationMapper {

    /**
     * 根据ID查询资质
     *
     * @param id 资质ID
     * @return 资质信息
     */
    AdvertiserQualificationEntity selectById(Long id);

    /**
     * 查询资质
     *
     * @param accountId 账号ID
     * @param industryId 行业ID
     * @param qualificationName 资质名称
     * @return 资质信息
     */
    AdvertiserQualificationEntity selectBy(@Param("accountId") Long accountId, @Param("industryId") Long industryId, @Param("qualificationName") String qualificationName);

    /**
     * 更新资质信息
     */
    int updateById(AdvertiserQualificationEntity entity);

    /**
     * 批量新增
     *
     * @param list 资质列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AdvertiserQualificationEntity> list);

    /**
     * 批量更新
     *
     * @param list 资质列表
     * @return 影响行数
     */
    int batchUpdate(@Param("list")List<AdvertiserQualificationEntity> list);

    /**
     * 批量删除
     *
     * @param accountId 广告主ID
     * @param ids 资质ID列表
     * @return 影响行数
     */
    int batchDelete(@Param("accountId") Long accountId, @Param("ids") List<Long> ids);

    /**
     * 根据账号ID查询资质ID列表
     *
     * @param accountId 账号id
     * @return 资质ID列表
     */
    List<Long> selectIdsByAccountId(@Param("accountId") Long accountId);

    /**
     * 根据账号id查询资质列表
     *
     * @param accountId 账号id
     * @return 资质列表
     */
    List<AdvertiserQualificationEntity> selectListByAccountId(@Param("accountId") Long accountId);

    /**
     * 根据账号ID和行业ID查询资质列表
     *
     * @param accountId 账号id
     * @return 资质列表
     */
    List<AdvertiserQualificationEntity> selectListByAccountIdAndIndustryId(@Param("accountId") Long accountId, @Param("industryId") Long industryId);

    /**
     * 根据账号id列表查询资质列表
     *
     * @param accountIds 账号id列表
     * @return 资质列表
     */
    List<AdvertiserQualificationEntity> selectListByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * 根据账号id列表查询广告主行业列表
     *
     * @param accountIds 账号id列表
     * @return 广告主行业列表
     */
    List<AdvertiserIndustryBo> selectIndustryListByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * 查询广告主的行业
     *
     * @param accountId 账号ID
     * @return 行业ID列表
     */
    List<Long> selectIndustryIdsByAccountId(@Param("accountId") Long accountId);
}
