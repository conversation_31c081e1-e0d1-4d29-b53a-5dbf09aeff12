package com.ruoyi.system.mapper.permission;

import com.ruoyi.system.entity.permission.PostPermissionRelationEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 职位权限关联表 Mapper
 *
 * <AUTHOR>
 * @date 2022-6-23 16:23:19
 */
public interface PostPermissionRelationMapper {

    /**
     * 新增记录
     */
    int insert(PostPermissionRelationEntity postPermissionRelationEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(PostPermissionRelationEntity postPermissionRelationEntity);

    /**
     * 根据id获取
     */
    PostPermissionRelationEntity selectById(Long id);

    /**
     * 根据职位id查询权限id列表
     *
     * @param postId 职位id
     * @return 职位权限关联
     */
    PostPermissionRelationEntity selectByPostId(@Param("postId") Long postId);

    /**
     * 更新职位权限
     *
     * @param entity 参数
     * @return 是否更新成功
     */
    Boolean updatePermissionByPostId(PostPermissionRelationEntity entity);
}
