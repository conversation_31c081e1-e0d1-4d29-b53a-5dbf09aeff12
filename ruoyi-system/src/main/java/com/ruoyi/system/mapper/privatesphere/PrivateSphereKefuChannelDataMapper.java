package com.ruoyi.system.mapper.privatesphere;

import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 私域客服渠道数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-3-7 10:30:06
 */
public interface PrivateSphereKefuChannelDataMapper {
    /**
     * 新增记录
     */
    int insert(PrivateSphereKefuChannelDataEntity privateSphereKefuChannelDataEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(PrivateSphereKefuChannelDataEntity privateSphereKefuChannelDataEntity);

    /**
     * 根据id获取
     */
    PrivateSphereKefuChannelDataEntity selectById(Long id);

    /**
     * 批量新增更新
     *
     * @param entities
     * @return
     */
    int batchInsertOrUpdate(@Param("entities") List<PrivateSphereKefuChannelDataEntity> entities);


    /**
     * 根据渠道id列表查询渠道数据
     * @param ids
     * @return
     */
    List<PrivateSphereKefuChannelDataEntity> selectListByDataIds(@Param("dataIds") List<Long> ids);

    /**
     * 根据数据id列表查询统计
     * @param dataIds
     * @return
     */
    List<PrivateSphereKefuChannelDataEntity> statisticsByDataIds(@Param("dataIds") List<Long> dataIds);


}
