package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.activity.ActivityPlan;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动推广计划Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
public interface ActivityPlanMapper {

    /**
     * 查询活动推广计划
     *
     * @param id 活动推广计划ID
     * @return 活动推广计划
     */
    ActivityPlan selectActivityPlanById(Long id);

    /**
     * 查询活动推广计划
     *
     * @param activityId 活动推广计划ID
     * @return 活动推广计划
     */
    ActivityPlan selectByActivityId(Long activityId);

    /**
     * 查询活动推广计划列表
     *
     * @param activityPlan 活动推广计划
     * @return 活动推广计划集合
     */
    List<ActivityPlan> selectActivityPlanList(ActivityPlan activityPlan);

    /**
     * 新增活动推广计划
     *
     * @param activityPlan 活动推广计划
     * @return 结果
     */
    int insertActivityPlan(ActivityPlan activityPlan);

    /**
     * 修改活动推广计划
     *
     * @param activityPlan 活动推广计划
     * @return 结果
     */
    int updateActivityPlan(ActivityPlan activityPlan);

    /**
     * 根据活动ID列表批量查询活动推广计划
     *
     * @param activityIds 活动ID列表
     * @return 活动推广计划列表
     */
    List<ActivityPlan> selectByActivityIds(@Param("activityIds") List<Long> activityIds);
}
