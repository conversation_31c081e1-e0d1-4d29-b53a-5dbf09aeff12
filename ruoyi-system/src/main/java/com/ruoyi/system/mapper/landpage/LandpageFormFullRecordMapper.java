package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.bo.landpage.LandPageSumDataBo;
import com.ruoyi.system.entity.landpage.LandpageFormAreaCount;
import com.ruoyi.system.entity.landpage.LandpageFormCount;
import com.ruoyi.system.entity.landpage.LandpageFormFullRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 落地页表单完整记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-08
 */
public interface LandpageFormFullRecordMapper {

    /**
     * 查询落地页表单完整记录列表
     *
     * @param param 查询条件
     * @return 落地页表单完整记录集合
     */
    List<LandpageFormFullRecord> selectList(LandpageFormFullRecord param);

    /**
     * 根据广告位分组统计表单价格总额
     *
     * @param param 查询条件
     * @return 广告位分组的表单价格总额列表
     */
    List<LandPageSumDataBo> selectSumPriceGroupBySlot(LandpageFormFullRecord param);

    /**
     * 统计日期-广告位维度的成功落地页表单数量
     *
     * @param param 查询条件
     * @return 成功落地页表单数量
     */
    List<LandpageFormCount> countByDateAndSlotId(LandpageFormFullRecord param);

    /**
     * 统计=成功落地页表单数量总和
     *
     * @param param 查询条件
     * @return 成功落地页表单数量总和
     */
    Integer sumByDateAndSlotId(LandpageFormFullRecord param);

    /**
     * 统计广告位-广告维度成功落地页表单数量
     *
     * @param param 查询条件
     * @return 成功落地页表单数量
     */
    List<LandpageFormCount> countByDate(LandpageFormFullRecord param);

    /**
     * 统计日期-广告主维度的成功落地页表单数量
     *
     * @param advertiserIds 广告主ID立碑
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param isSuccess 是否调用成功
     * @return 成功落地页表单数量
     */
    List<LandpageFormCount> countByDateAndAdvertiserId(@Param("advertiserIds") List<Long> advertiserIds,
                                                       @Param("startDate") Date startDate, @Param("endDate")Date endDate,
                                                       @Param("isSuccess") Integer isSuccess);
    /**
     * 根据订单id列表查询落地页表单
     *
     * @param orderIds 订单号列表
     * @return 表单列表
     */
    List<LandpageFormFullRecord> selectByOrderIds(@Param("orderIds") List<String> orderIds);

    /**
     * 表单数据地域分布
     *
     * @param advertiserIds 广告主ID立碑
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param areaList 地域列表
     * @return 表单数据地域分布
     */
    List<LandpageFormAreaCount> selectFormCountGroupByArea(@Param("advertiserIds") List<Long> advertiserIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate,@Param("areaList") List<String> areaList);

    /**
     * 表单汇总数据
     *
     * @param advertiserIds 广告主ID立碑
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param areaList 地域列表
     * @return 表单汇总数据
     */
    LandpageFormAreaCount selectFormCountSummary(@Param("advertiserIds") List<Long> advertiserIds, @Param("startDate") Date startDate, @Param("endDate")Date endDate,@Param("areaList") List<String> areaList );
}
