package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.bo.advert.AdvertDayDataBo;
import com.ruoyi.system.bo.advert.AdvertDaySumDataBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserDaySumDataBo;
import com.ruoyi.system.entity.datashow.AdvertDayData;
import com.ruoyi.system.entity.datashow.AdvertDayStatisticData;
import com.ruoyi.system.req.advertiser.AdvertiserCpcDataReq;
import com.ruoyi.system.req.advertiser.AdvertiserDaySumDataReq;

import java.util.List;

/**
 * 广告日数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
public interface AdvertDayDataMapper {

    /**
     * 查询广告日数据
     *
     * @param param 查询条件
     * @return 素材日数据
     */
    AdvertDayData selectBy(AdvertDayData param);

    /**
     * 查询广告日数据列表
     *
     * @param advertDayData 广告日数据
     * @return 广告日数据集合
     */
    List<AdvertDayData> selectAdvertDayDataList(AdvertDayData advertDayData);

    /**
     * 查询广告日数据列表
     *
     * @param advertDayData 广告日数据
     * @return 广告日数据集合
     */
    List<AdvertDayDataBo> selectAdvertDayDataBoList(AdvertDayData advertDayData);

    /**
     * 查询广告日数据汇总
     *
     * @param param 参数
     * @return 广告日数据汇总
     */
    AdvertDayStatisticData selectStatisticAdvertDayData(AdvertDayData param);

    /**
     * 查询广告日数据列表
     *
     * @param req 请求参数
     * @return 广告日数据统计数据
     */
    List<AdvertDayData> selectAdvertCpcDayDataList(AdvertiserCpcDataReq req);

    /**
     * 新增广告日数据
     *
     * @param advertDayData 广告日数据
     * @return 结果
     */
    int insertAdvertDayData(AdvertDayData advertDayData);

    /**
     * 修改广告日数据
     *
     * @param param 广告日数据
     * @return 结果
     */
    int updateAdvertDayData(AdvertDayData param);

    /**
     * 根据广告id列表查询广告日数据统计数据
     *
     * @param req 请求参数
     * @return 广告日数据统计数据
     */
    List<AdvertDaySumDataBo> selectAdvertDaySumData(AdvertiserCpcDataReq req);

    /**
     * 查询广告主日维度统计数据
     *
     * @param req 请求参数
     * @return 广告主日维度统计数据
     */
    List<AdvertiserDaySumDataBo> selectAdvertiserDaySumData(AdvertiserDaySumDataReq req);
}
