package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.entity.datashow.AdvertAppDayData;

import java.util.List;

/**
 * 广告媒体日数据Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
public interface AdvertAppDayDataMapper {

    /**
     * 查询广告媒体日数据
     *
     * @param param 查询条件
     * @return 广告媒体日数据
     */
    AdvertAppDayData selectBy(AdvertAppDayData param);

    /**
     * 查询广告媒体日数据列表
     *
     * @param advertAppDayData 广告媒体日数据
     * @return 广告媒体日数据集合
     */
    List<AdvertAppDayData> selectAdvertAppDayDataList(AdvertAppDayData advertAppDayData);

    /**
     * 查询广告媒体日数据汇总
     *
     * @param param 参数
     * @return 广告媒体日数据汇总
     */
    AdvertAppDayData selectStatisticAdvertAppDayData(AdvertAppDayData param);

    /**
     * 新增广告媒体日数据
     *
     * @param advertAppDayData 广告媒体日数据
     * @return 结果
     */
    int insertAdvertAppDayData(AdvertAppDayData advertAppDayData);

    /**
     * 修改广告媒体日数据
     *
     * @param param 广告媒体日数据
     * @return 结果
     */
    int updateAdvertAppDayData(AdvertAppDayData param);
}
