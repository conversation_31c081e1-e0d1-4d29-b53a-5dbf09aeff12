package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.datashow.LandpageFormSendRuleEntity;

import java.util.List;

/**
 * 落地页表单上报规则 Mapper
 *
 * <AUTHOR>
 * @date 2021-12-23
 */
public interface LandpageFormSendRuleMapper {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    int insert(LandpageFormSendRuleEntity entity);

    /**
     * 根据advertiserId删除
     *
     * @param advertiserId 广告主ID
     * @return 结果
     */
    int deleteByAdvertiserId(Long advertiserId);

    /**
     * 获取有效的规则
     *
     * @return 结果
     */
    List<LandpageFormSendRuleEntity> selectList();

    /**
     * 根据广告主ID获取
     *
     * @param advertiserId 广告主ID
     * @return 结果
     */
    LandpageFormSendRuleEntity selectByAdvertiserId(Long advertiserId);
}
