package com.ruoyi.system.mapper.oa.department;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.entity.oa.department.DepartmentEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * oa部门表 Mapper
 *
 * <AUTHOR>
 * @date 2021-10-9 11:30:37
 */
@DataSource(DataSourceType.OA)
public interface DepartmentMapper {

    /**
     * 新增记录
     */
    int insert(DepartmentEntity departmentEntity);

    /**
     * 根据id更新
     */
    int updateById(DepartmentEntity departmentEntity);

    /**
     * 根据id获取
     */
    DepartmentEntity selectById(Long id);

    /**
     * 根据id列表获取
     *
     * @param ids id列表
     * @return 结果
     */
    List<DepartmentEntity> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 查询所有部门
     *
     * @return 结果
     */
    List<DepartmentEntity> selectAllDepartment();

    /**
     * 根据公司id列表查询部门列表
     *
     * @param companyIds 公司id列表
     * @return 部门列表
     */
    List<DepartmentEntity> selectByCompanyIds(@Param("companyIds") List<Long> companyIds);

    /**
     * 查询部门Id列表
     *
     * @param departmentKeys 部门Key列表
     * @return 部门ID列表
     */
    List<Long> selectDepartmentIds(@Param("departmentKeys") List<String> departmentKeys);
}
