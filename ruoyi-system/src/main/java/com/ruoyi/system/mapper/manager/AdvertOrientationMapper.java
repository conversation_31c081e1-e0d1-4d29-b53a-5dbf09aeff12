package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.bo.advert.AdvertOrientBatchUpdateParam;
import com.ruoyi.system.bo.advert.AdvertOrientCountBo;
import com.ruoyi.system.bo.advert.AdvertOrientLandpageBo;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告定向配置Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
public interface AdvertOrientationMapper {

    /**
     * 查询广告的默认定向配置
     *
     * @param advertId 广告ID
     * @return 广告定向配置
     */
    AdvertOrientation selectDefaultOrientationByAdvertId(Long advertId);

    /**
     * 查询广告的配置ID列表
     *
     * @param advertId 广告ID
     * @return 广告定向配置ID列表
     */
    List<Long> selectIdsByAdvertId(Long advertId);

    /**
     * 查询广告定向配置
     *
     * @param id 广告定向配置ID
     * @return 广告定向配置
     */
    AdvertOrientation selectAdvertOrientationById(Long id);

    /**
     * 查询广告定向配置列表
     *
     * @param param 请求参数
     * @return 广告定向配置列表
     */
    List<AdvertOrientation> selectAdvertOrientationList(AdvertOrientation param);

    /**
     * 查询开启地域定向的广告的配置ID列表
     *
     * @param advertIds 广告ID列表
     * @return 广告定向配置ID列表
     */
    List<Long> selectIdsWithAreaTargetByAdvertIds(@Param("advertIds") List<Long> advertIds);

    /**
     * 查询广告的最大计费单价(毫)
     *
     * @param advertId 广告ID
     * @return 计费单价(毫)
     */
    Integer selectMaxMilliUnitPriceByAdvertId(@Param("advertId") Long advertId);

    /**
     * 新增广告定向配置
     *
     * @param advertOrientation 广告定向配置
     * @return 结果
     */
    int insertAdvertOrientation(AdvertOrientation advertOrientation);

    /**
     * 修改广告定向配置
     *
     * @param param 参数
     * @return 结果
     */
    int updateAdvertOrientation(AdvertOrientation param);

    /**
     * 修改广告定向配置(包含每日预算)
     *
     * @param param 参数
     * @return 结果
     */
    int updateWithDailyBudget(AdvertOrientation param);

    /**
     * 删除广告定向配置
     *
     * @param id 广告定向配置ID
     * @return 结果
     */
    int deleteAdvertOrientation(Long id);

    /**
     * 批量修改广告定向配置的地域定向
     *
     * @param ids 广告配置ID列表
     * @param areaTarget 地域定向
     * @return 结果
     */
    int batchUpdateAreaTarget(@Param("ids") List<Long> ids, @Param("areaTarget") String areaTarget);

    /**
     * 查询广告的配置数量
     *
     * @param advertIds 广告ID列表
     * @return 广告配置数量列表
     */
    List<AdvertOrientCountBo> selectOrientCountByAdvertIds(@Param("advertIds") List<Long> advertIds);

    /**
     * 查询配置的广告ID
     *
     * @param ids 配置ID列表
     * @return 广告ID列表
     */
    List<Long> selectAdvertIdsByOrientIds(@Param("ids") List<Long> ids);

    /**
     * 查询非通投配置ID列表
     *
     * @param advertIds 广告ID列表
     * @return 广告配置ID列表
     */
    List<Long> selectOrientBannedOrientIds(@Param("advertIds") List<Long> advertIds);

    /**
     * 查询配置落地页链接
     *
     * @param advertIds 广告ID列表
     * @return 配置落地页链接列表
     */
    List<AdvertOrientLandpageBo> selectLandpageByAdvertIds(@Param("advertIds") List<Long> advertIds);

    /**
     * 批量修改广告定向配置信息
     * @param param  广告定向配置
     * @return 是否成功
     */
    Boolean batchUpdateAdvertOrientation(AdvertOrientBatchUpdateParam param);

    /**
     * 根据落地页查询配置列表
     *
     * @param landpageUrl 链接
     * @return 配置列表
     */
    List<AdvertOrientLandpageBo> selectByLandpage(@Param("landpageUrl") String landpageUrl);
}
