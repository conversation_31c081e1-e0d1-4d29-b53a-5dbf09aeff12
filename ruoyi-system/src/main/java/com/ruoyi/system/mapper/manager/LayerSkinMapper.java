package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.advert.LayerSkin;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 弹层皮肤Mapper接口
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
public interface LayerSkinMapper {

    /**
     * 查询弹层皮肤
     *
     * @param id 弹层皮肤ID
     * @return 弹层皮肤
     */
    LayerSkin selectLayerSkinById(Long id);

    /**
     * 查询弹层皮肤
     *
     * @param skinCode 弹层皮肤编号
     * @return 弹层皮肤
     */
    LayerSkin selectBySkinCode(String skinCode);

    /**
     * 查询弹层皮肤
     *
     * @param skinCodeList 弹层皮肤编号列表
     * @return 弹层皮肤
     */
    List<LayerSkin> selectBySkinCodeList(@Param("list") List<String> skinCodeList);

    /**
     * 查询弹层皮肤列表
     *
     * @param layerSkin 弹层皮肤
     * @return 弹层皮肤集合
     */
    List<LayerSkin> selectLayerSkinList(LayerSkin layerSkin);

    /**
     * 新增弹层皮肤
     *
     * @param layerSkin 弹层皮肤
     * @return 结果
     */
    int insertLayerSkin(LayerSkin layerSkin);

    /**
     * 修改弹层皮肤
     *
     * @param layerSkin 弹层皮肤
     * @return 结果
     */
    int updateLayerSkin(LayerSkin layerSkin);
}
