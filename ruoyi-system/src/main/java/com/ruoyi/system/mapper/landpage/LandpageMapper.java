package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.landpage.Landpage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 落地页Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-10
 */
public interface LandpageMapper {

    /**
     * 查询落地页
     *
     * @param id 落地页ID
     * @return 落地页
     */
    Landpage selectById(Long id);

    /**
     * 查询落地页
     *
     * @param key 落地页标识
     * @return 落地页
     */
    Landpage selectByKey(@Param("key") String key);

    /**
     * 查询落地页
     *
     * @param keys 落地页标识列表
     * @return 落地页
     */
    List<Landpage> selectByKeys(@Param("keys") List<String> keys);

    /**
     * 查询落地页列表
     *
     * @param param 参数
     * @return 落地页列表
     */
    List<Landpage> selectList(Landpage param);

    /**
     * 查询落地页列表(只包含基础信息,无配置信息)
     *
     * @param param 参数
     * @return 落地页列表
     */
    List<Landpage> selectSimpleList(Landpage param);

    /**
     * 新增落地页
     *
     * @param param 参数
     * @return 结果
     */
    int insertLandpage(Landpage param);

    /**
     * 修改落地页
     *
     * @param param 参数
     * @return 结果
     */
    int updateLandpage(Landpage param);

    /**
     * 判断落地页是否存在
     *
     * @param key 落地页标识
     * @return 是否存在
     */
    Integer existByKey(@Param("key") String key);

    /**
     * 标记落地页无效
     *
     * @param landpageId 落地页ID
     * @return 结果
     */
    int invalidateLandpage(@Param("id") Long landpageId);

    /**
     * 查询落地页标签
     *
     * @return 落地页标签
     */
    List<String> getLandpageTags();
}
