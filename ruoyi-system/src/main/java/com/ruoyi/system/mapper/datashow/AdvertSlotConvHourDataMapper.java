package com.ruoyi.system.mapper.datashow;

import com.ruoyi.system.bo.landpage.ConvDataBo;
import com.ruoyi.system.entity.datashow.AdvertSlotConvHourDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告广告位维度后端转化时段数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-2-9 10:55:35
 */
public interface AdvertSlotConvHourDataMapper {

    /**
     * 新增记录
     */
    int insert(AdvertSlotConvHourDataEntity advertSlotConvHourDataEntity);

    /**
     * 根据id更新
     */
    int updateById(AdvertSlotConvHourDataEntity advertSlotConvHourDataEntity);

    /**
     * 查询数据
     *
     * @param param 查询条件
     * @return 数据
     */
    AdvertSlotConvHourDataEntity selectBy(AdvertSlotConvHourDataEntity param);

    /**
     * 统计广告-时段维度的后端转化数据
     *
     * @param curDate 日期
     * @param advertId 广告ID
     * @return 后端转化数据
     */
    List<ConvDataBo> countByDateAndAdvertId(@Param("curDate") Date curDate, @Param("advertId") Long advertId);

    /**
     * 统计广告-日期-时段维度的后端转化数据
     *
     * @param dateList 日期列表
     * @param advertIds 广告ID列表
     * @return 后端转化数据
     */
    List<ConvDataBo> countByDateListAndAdvertIds(@Param("dateList") List<Date> dateList, @Param("advertIds") List<Long> advertIds);

    /**
     * 统计日期-时段维度的后端转化数据
     *
     * @param dateList 日期列表
     * @param advertIds 广告ID列表
     * @return 后端转化数据
     */
    List<ConvDataBo> countByDateListAndAdvertIdsGroupByDateHour(@Param("dateList") List<Date> dateList, @Param("advertIds") List<Long> advertIds);
}
