package com.ruoyi.system.mapper.advertiser;

import com.ruoyi.system.entity.advertiser.AdvertiserOperateTimerEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告主操作定时任务 Mapper
 *
 * <AUTHOR>
 * @date 2023-4-20 11:39:56
 */
public interface AdvertiserOperateTimerMapper {

    /**
     * 新增记录
     */
    int insert(AdvertiserOperateTimerEntity advertiserOperateTimerEntity);

    /**
     * 根据id更新
     */
    int updateById(AdvertiserOperateTimerEntity advertiserOperateTimerEntity);

    /**
     * 执行定时任务
     *
     * @param id 定时任务ID
     * @return 影响行数
     */
    int execTimer(@Param("id") Long id);

    /**
     * 根据id获取
     */
    AdvertiserOperateTimerEntity selectById(Long id);

    /**
     * 查询待执行的定时任务
     *
     * @param advertiserId 广告主ID
     * @param operType 操作类型
     * @return 定时任务
     */
    AdvertiserOperateTimerEntity selectPlanTimerBy(@Param("advertiserId") Long advertiserId, @Param("operType") Integer operType);

    /**
     * 查询广告主待执行的定时任务
     *
     * @param advertiserIds 广告主ID列表
     * @return 定时任务列表
     */
    List<AdvertiserOperateTimerEntity> selectPlanTimerByAdvertiserIds(@Param("advertiserIds") List<Long> advertiserIds);

    /**
     * 查询可以执行的定时任务
     *
     * @return 定时任务列表
     */
    List<AdvertiserOperateTimerEntity> selectReadyTimer();
}
