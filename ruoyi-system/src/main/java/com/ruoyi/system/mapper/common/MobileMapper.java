package com.ruoyi.system.mapper.common;

import com.ruoyi.system.bo.common.MobileDataBo;
import com.ruoyi.system.entity.common.MobileEntity;
import com.ruoyi.system.req.traffic.MobileListReq;

import java.util.List;

/**
 * 设备信息表 Mapper
 *
 * <AUTHOR>
 * @date 2022-11-10 15:23:50
 */
public interface MobileMapper {

    /**
     * 查询列表
     */
    List<MobileDataBo> selectList(MobileListReq req);

    /**
     * 新增记录
     */
    int insert(MobileEntity entity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据model更新
     */
    int updateByModel(MobileEntity entity);

    /**
     * 根据id获取
     */
    MobileEntity selectById(Long id);

    /**
     * 根据手机型号查询品牌
     *
     * @param model 手机型号
     * @return 手机品牌
     */
    String selectBrandByModel(String model);

    /**
     * 查询所有的手机型号
     *
     * @return 手机型号列表
     */
    List<String> selectTotalModel();
}
