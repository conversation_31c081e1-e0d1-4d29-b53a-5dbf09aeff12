package com.ruoyi.system.mapper.privatesphere;

import com.ruoyi.system.entity.privatesphere.PrivateSphereProductEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 私域产品表 Mapper
 *
 * <AUTHOR>
 * @date 2023-2-8 10:19:19
 */
public interface PrivateSphereProductMapper {
    /**
     * 新增记录
     */
    int insert(PrivateSphereProductEntity privateSphereProductEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(PrivateSphereProductEntity privateSphereProductEntity);

    /**
     * 根据id获取
     */
    PrivateSphereProductEntity selectById(Long id);

    /**
     * 根据账号id和产品名称获取
     * @param accountId
     * @param productName
     * @return
     */
    PrivateSphereProductEntity selectByAccountIdAndName(@Param("accountId") Long accountId,@Param("productName") String productName);

    /**
     * 根据id列表查询产品列表
     * @param ids
     * @return
     */
    List<PrivateSphereProductEntity> selectListByIds(@Param("ids") List<Long> ids);


    /**
     * 根据账号id列表查询产品列表
     * @param accountIds
     * @return
     */
    List<PrivateSphereProductEntity> selectListByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * 根据产品名称获取产品id列表
     * @param productName
     * @return
     */
    List<Long> selectProductIdsByName(@Param("productName") String productName);

    /**
     * 查询产品列表
     * @return
     */
    List<PrivateSphereProductEntity> selectProductList();



}
