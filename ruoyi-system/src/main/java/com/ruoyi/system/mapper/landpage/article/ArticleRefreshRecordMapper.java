package com.ruoyi.system.mapper.landpage.article;

import com.ruoyi.system.bo.landpage.article.ArticleListParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleRefreshRecordEntity;

/**
 * 文章阅读量API接口请求记录 Mapper
 *
 * <AUTHOR>
 * @date 2024-4-15 16:42:45
 */
public interface ArticleRefreshRecordMapper {

    /**
     * 新增记录
     */
    int insert(ArticleRefreshRecordEntity articleRefreshRecordEntity);

    /**
     * 根据id更新
     */
    int updateById(ArticleRefreshRecordEntity articleRefreshRecordEntity);

    /**
     * 根据id获取
     */
    ArticleRefreshRecordEntity selectById(Long id);

    /**
     * 获取调用次数
     */
    Integer countBy(ArticleListParamBo param);
}
