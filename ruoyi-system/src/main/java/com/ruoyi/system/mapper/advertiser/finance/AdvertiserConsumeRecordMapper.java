package com.ruoyi.system.mapper.advertiser.finance;

import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeRecordEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeListReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeRecordUpdateReq;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告主消费记录表 Mapper
 *
 * <AUTHOR>
 * @date 2022-3-18 17:58:07
 */
public interface AdvertiserConsumeRecordMapper {

    /**
     * 新增记录
     */
    int insert(AdvertiserConsumeRecordEntity advertiserConsumeRecordEntity);

    /**
     * 根据ID更新广告主消费记录
     */
    int updateByReq(AdvertiserConsumeRecordUpdateReq req);

    /**
     * 根据id获取
     */
    AdvertiserConsumeRecordEntity selectById(Long id);

    /**
     * 根据广告主ID和日期查询消费记录
     *
     * @param accountId 广告主ID
     * @param curDate 日期
     * @return 广告主消费记录
     */
    AdvertiserConsumeRecordEntity selectByAccountIdAndDate(@Param("accountId") Long accountId, @Param("curDate") Date curDate);

    /**
     * 查询广告主消费记录
     *
     * @param req 参数
     * @return 广告主消费记录列表
     */
    List<AdvertiserConsumeRecordEntity> selectList(AdvertiserConsumeListReq req);

    /**
     * 查询广告主消费总金额
     *
     * @param accountId 广告主id
     * @return 广告主消费总金额
     */
    Integer sumConsumeAmount(@Param("accountId") Long accountId);

    /**
     * 批量查询广告主消费总金额
     *
     * @param accountIds 广告主ID列表
     * @return 广告主消费总金额列表
     */
    List<AdvertiserConsumeRecordEntity> sumConsumeAmountByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * 批量查询指定日期的广告主消费总金额
     *
     * @param accountIds 广告主ID列表
     * @param date 日期
     * @return 广告主消费总金额列表
     */
    List<AdvertiserConsumeRecordEntity> sumConsumeAmountByAccountIdsAndDate(@Param("accountIds") List<Long> accountIds, @Param("date") Date date);
}
