package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord;
import com.ruoyi.system.entity.landpage.LandpageFormCount;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 白酒落地页单记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
public interface BaijiuLandpageFormRecordMapper {

    /**
     * 查询落地页单记录
     *
     * @param id 落地页单记录ID
     * @return 落地页单记录
     */
    BaijiuLandpageFormRecord selectLandpageFormRecordById(Long id);

    /**
     * 根据订单号ID查询记录是否存在
     *
     * @param orderId 订单号
     * @return 是否存在(1.存在,2.不存在)
     */
    Integer existByOrderId(@Param("orderId") String orderId);

    /**
     * 查询落地页单记录列表
     *
     * @param param 查询条件
     * @return 落地页单记录集合
     */
    List<BaijiuLandpageFormRecord> selectList(BaijiuLandpageFormRecord param);

    /**
     * 查询落地页单记录列表
     *
     * @return 落地页单记录集合
     */
    List<BaijiuLandpageFormRecord> selectListByAdvertIdAndDate(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("advertIds") List<Long> advertIds);

    /**
     * 新增落地页单记录
     *
     * @param landpageFormRecord 落地页单记录
     * @return 结果
     */
    int insertLandpageFormRecord(BaijiuLandpageFormRecord landpageFormRecord);

    /**
     * 修改落地页单记录
     *
     * @param landpageFormRecord 落地页单记录
     * @return 结果
     */
    int updateLandpageFormRecord(BaijiuLandpageFormRecord landpageFormRecord);

    /**
     * 统计日期-广告位维度的白酒落地页表单数量
     *
     * @param param 查询条件
     * @return 白酒落地页表单数量
     */
    List<LandpageFormCount> countByDateAndSlotId(BaijiuLandpageFormRecord param);

    /**
     * 统计白酒落地页表单数量
     *
     * @param param 查询条件
     * @return 白酒落地页表单数量
     */
    Integer sumByDateAndSlotId(BaijiuLandpageFormRecord param);

    /**
     * 统计广告位-广告维度白酒落地页表单数量
     *
     * @param param 查询条件
     * @return 白酒落地页表单数量
     */
    List<LandpageFormCount> countByDate(BaijiuLandpageFormRecord param);

    /**
     * 更新商户订单号
     * @param id
     * @param tradeNo
     * @return
     */
    int updateTradeNo(@Param("id") Long id,@Param("tradeNo") String tradeNo);

    /**
     * 更新支付状态
     * @param record
     * @return
     */
    int updateTradeStatus(BaijiuLandpageFormRecord record);

    /**
     * 根据订单号查询
     * @param tradeNo
     * @return
     */
    BaijiuLandpageFormRecord selectByTradeNo(@Param("tradeNo")  String tradeNo);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();

    /**
     * 查询所有的媒体ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAppIds();

    /**
     * 查询所有的广告位ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalSlotIds();
}
