package com.ruoyi.system.mapper.common;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.bo.data.OrderDataBo;
import com.ruoyi.system.entity.common.AdvertOrderLogEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 业务订单明细表 Mapper
 *
 * <AUTHOR>
 * @date 2023-10-16
 */
@DataSource(DataSourceType.LOG)
public interface AdvertOrderLogMapper {

    /**
     * 新增记录
     */
    int insert(AdvertOrderLogEntity orderLogEntity);

    /**
     * 根据id更新
     */
    int updateById(AdvertOrderLogEntity orderLogEntity);

    /**
     * 根据id获取
     */
    AdvertOrderLogEntity selectById(Long id);

    /**
     * 根据订单号获取ID
     */
    Long selectIdByOrderId(String orderId);

    /**
     * 查询广告位广告的CTR和CVR(活动使用)
     *
     * @param slotId 广告位ID
     * @param advertId 广告ID
     * @param launchSeq 发券次序
     * @param mobileBrand 手机品牌
     * @param province 省份
     * @param times 最近发券次数
     * @param minId 最小ID
     * @return CTR和CVR
     */
    OrderDataBo selectCtrCvrBySlotIdAndAdvertIdAndMobileBrand(@Param("slotId") Long slotId, @Param("advertId") Long advertId,
                                                              @Param("launchSeq") Integer launchSeq, @Param("mobileBrand") String mobileBrand,
                                                              @Param("province") String province, @Param("times") Integer times,
                                                              @Param("minId") Long minId);

    /**
     * 查询广告位广告的CVR(直投使用)
     *
     * @param slotId 广告位ID
     * @param advertId 广告ID
     * @param times 最近发券次数
     * @return CVR
     */
    OrderDataBo selectCvrBySlotIdAndAdvertId(@Param("slotId") Long slotId, @Param("advertId") Long advertId,
                                             @Param("times") Integer times, @Param("minId") Long minId);

    /**
     * 查询广告的CTR和CVR(OCPC使用)
     *
     * @param advertId 广告ID
     * @param times 最近发券次数
     * @return CTR和CVR
     */
    OrderDataBo selectCtrCvrByAdvertId(@Param("advertId") Long advertId,  @Param("times") Integer times);
}
