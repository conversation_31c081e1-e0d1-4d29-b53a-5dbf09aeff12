package com.ruoyi.system.mapper.advert;

import com.ruoyi.system.entity.advert.AdvertPreConsumeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 广告消耗预扣表 Mapper
 *
 * <AUTHOR>
 * @date 2023-12-25 19:54:55
 */
public interface AdvertPreConsumeMapper {

    /**
     * 新增记录
     */
    int insert(AdvertPreConsumeEntity advertPreConsumeEntity);

    /**
     * 根据id获取
     */
    AdvertPreConsumeEntity selectById(Long id);

    /**
     * 根据日期和配置ID获取
     */
    AdvertPreConsumeEntity selectBy(@Param("date") Date date, @Param("orientId") Long orientId);

    /**
     * 预扣消耗
     *
     * @param id 数据ID
     * @param milliConsumeAdd 预扣消耗
     * @return 是否预扣成功
     */
    Integer preConsume(@Param("id") Long id, @Param("milliConsumeAdd") Integer milliConsumeAdd);
}
