package com.ruoyi.system.mapper.oa.permission;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.entity.oa.permission.PermissionEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 权限表 Mapper
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:18
 */
@DataSource(DataSourceType.OA)
public interface PermissionMapper {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    int insert(PermissionEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    int updateById(PermissionEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    PermissionEntity selectById(Long id);

    /**
     * 根据系统id查询所有权限
     *
     * @param systemId
     * @return
     */
    List<PermissionEntity> selectBySystemId(Long systemId);

    /**
     * 根据权限ID查询其所有子权限ID
     *
     * @param parentId 父权限ID
     * @return 子权限ID列表
     */
    List<Long> selectByParentId(Long parentId);

    /**
     * 查询权限所有的父权限ID列表
     *
     * @param ids 权限ID列表
     * @return 父权限ID列表
     */
    List<Long> selectParentIdsByIds(@Param("ids") List<Long> ids);

    /**
     * 根据id列表查询权限key列表
     * @param ids
     * @return
     */
    Set<String> selectPermissionKeyByIds(@Param("ids") List<Long> ids);
}
