package com.ruoyi.system.mapper.privatesphere;

import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 私域渠道数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-2-10 15:51:03
 */
public interface PrivateSphereChannelDataMapper {
    /**
     * 新增记录
     */
    int insert(PrivateSphereChannelDataEntity privateSphereChannelDataEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(PrivateSphereChannelDataEntity privateSphereChannelDataEntity);

    /**
     * 根据id获取
     */
    PrivateSphereChannelDataEntity selectById(Long id);

    /**
     * 批量新增更新
     * @param entities
     * @return
     */
    int batchInsertOrUpdate(@Param("entities") List<PrivateSphereChannelDataEntity> entities);

    /**
     * 根据渠道数据id列表查询渠道数据
     * @param dataIds
     * @return
     */
    List<PrivateSphereChannelDataEntity> selectListByDataIds(@Param("dataIds") List<Long> dataIds);

    /**
     * 根据数据id列表查询统计
     * @param dataIds
     * @return
     */
    List<PrivateSphereChannelDataEntity> statisticsByDataIds(@Param("dataIds") List<Long> dataIds);

}
