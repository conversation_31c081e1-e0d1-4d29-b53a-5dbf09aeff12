package com.ruoyi.system.mapper.quickapp;

import com.ruoyi.system.entity.quickapp.QuickappContentEntity;

import java.util.List;

/**
 * 快应用内容表 Mapper
 *
 * <AUTHOR>
 * @date 2022-4-1 17:07:39
 */
public interface QuickappContentMapper {

    /**
     * 新增记录
     */
    int insert(QuickappContentEntity quickappContentEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(QuickappContentEntity quickappContentEntity);

    /**
     * 根据id获取
     */
    QuickappContentEntity selectById(Long id);

    /**
     * 模糊搜索花
     */
    QuickappContentEntity selectByTitle(String title);

    /**
     * 查询100条花
     * @return
     */
    List<QuickappContentEntity> selectList();
}
