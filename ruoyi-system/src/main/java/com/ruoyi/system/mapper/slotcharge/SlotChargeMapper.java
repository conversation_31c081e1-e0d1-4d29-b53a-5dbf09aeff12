package com.ruoyi.system.mapper.slotcharge;

import com.ruoyi.system.entity.slotcharge.SlotChargeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* 广告位每日计费方式 Mapper
* <AUTHOR>
* @date 2022-3-7 19:13:24
*/
public interface SlotChargeMapper {

    /**
    * 新增记录
    */
    int insert(SlotChargeEntity slotChargeEntity);

    /**
    * 根据id更新
    */
    int updateById(SlotChargeEntity slotChargeEntity);

    /**
    * 根据id获取
    */
    SlotChargeEntity selectById(Long id);

    /**
     * 根据广告位id列表和日期查询
     * @param slotIds
     * @param date
     * @return
     */
    List<SlotChargeEntity> selectListBySlotIdsAndDate(@Param("slotIds") List<Long> slotIds, @Param("date") Date date);

    /**
     * 根据广告位id和日期查询
     * @param slotId
     * @param date
     * @return
     */
    SlotChargeEntity selectBySlotIdAndDate(@Param("slotId") Long slotId,@Param("date") Date date);

    /**
     * 根据广告位id列表和日期范围查询
     * @param slotIds
     * @param dates
     * @return
     */
    List<SlotChargeEntity> selectListBySlotIdsAndDateList(@Param("slotIds") List<Long> slotIds,@Param("dates") List<Date> dates);
    /**
     * 根据广告位id列表和日期范围查询
     * @param slotIds
     * @param startDate
     * @param endDate
     * @return
     */
    List<SlotChargeEntity> selectListBySlotIdsAndDateRange(@Param("slotIds") List<Long> slotIds,@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    /**
     * 分页查询列表
     * @param id
     * @param pageSize
     * @return
     */
    List<SlotChargeEntity> selectListByDate(@Param("id") Long id,@Param("pageSize") Integer pageSize,@Param("date") Date date);

    /**
     * 批量新增更新
     * @param entities
     * @return
     */
    int batchInsertOrUpdate(@Param("entities") List<SlotChargeEntity> entities);
}
