package com.ruoyi.system.mapper.slot;

import com.ruoyi.system.entity.slot.SlotRedirectHourData;
import com.ruoyi.system.req.slot.data.SlotRedirectDataParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告位投放小时数据Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-28
 */
public interface SlotRedirectHourDataMapper {

    /**
     * 查询广告位投放小时数据
     *
     * @param param 查询参数
     * @return 广告位投放小时数据
     */
    SlotRedirectHourData selectBy(SlotRedirectHourData param);

    /**
     * 查询广告位投放汇总数据
     *
     * @param param 查询参数
     * @return 广告位投放汇总数据
     */
    List<SlotRedirectHourData> groupBy(SlotRedirectDataParam param);

    /**
     * 新增广告位投放小时数据
     *
     * @param record 广告位投放小时数据
     * @return 影响行数
     */
    int insert(SlotRedirectHourData record);

    /**
     * 更新广告位投放小时数据
     *
     * @param id 记录ID
     * @param pvAdd pv增量
     * @param uvAdd uv增量
     * @return 影响行数
     */
    int update(@Param("id") Long id, @Param("pvAdd") int pvAdd, @Param("uvAdd") int uvAdd);
}
