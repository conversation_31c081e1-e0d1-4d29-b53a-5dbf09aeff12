package com.ruoyi.system.mapper.oa.post;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.entity.oa.post.PostEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * oa职位表 Mapper
 *
 * <AUTHOR>
 * @date 2021-10-9 13:43:35
 */
@DataSource(DataSourceType.OA)
public interface PostMapper {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    int insert(PostEntity entity);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    int updateById(PostEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    PostEntity selectById(Long id);

    /**
     * 根据id列表获取
     *
     * @param ids id列表
     * @param companyId 公司id，只能限定诺禾公司
     * @return 结果
     */
    List<PostEntity> selectByIds(@Param("ids") List<Long> ids, @Param("companyId") Long companyId);

    /**
     * 根据部门id列表查询职位列表
     *
     * @param departmentIds 部门ID列表
     * @return 职位列表
     */
    List<PostEntity> selectListByDepartmentIds(@Param("departmentIds") List<Long> departmentIds);

    /**
     * 查询所有职位
     *
     * @param companyId 公司id，只能限定诺禾公司
     * @return 职位列表
     */
    List<PostEntity> selectAllPost(@Param("companyId") Long companyId);

    /**
     * 根据职位名称模糊查询职位列表
     *
     * @param postName 职位名称
     * @return 职位列表
     */
    List<PostEntity> selectByPostName(@Param("postName") String postName);
}
