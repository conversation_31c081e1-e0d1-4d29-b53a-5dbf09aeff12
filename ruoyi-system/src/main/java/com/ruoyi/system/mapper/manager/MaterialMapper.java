package com.ruoyi.system.mapper.manager;

import java.util.List;
import com.ruoyi.system.entity.advert.Material;
import org.apache.ibatis.annotations.Param;

/**
 * 广告素材Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
public interface MaterialMapper {

    /**
     * 查询广告素材
     *
     * @param id 广告素材ID
     * @return 广告素材
     */
    Material selectMaterialById(Long id);

    /**
     * 查询广告素材列表
     *
     * @param material 广告素材
     * @return 广告素材集合
     */
    List<Material> selectMaterialList(Material material);

    /**
     * 查询广告素材列表
     *
     * @param advertId 广告id
     * @return 广告素材集合
     */
    List<Material> selectMaterialListByAdvertId(@Param("advertId") Long advertId);

    /**
     * 新增广告素材
     *
     * @param material 广告素材
     * @return 结果
     */
    int insertMaterial(Material material);

    /**
     * 批量新增广告素材
     *
     * @param list 素材列表
     * @return 结果
     */
    int batchInsertMaterial(@Param("list") List<Material> list);

    /**
     * 修改广告素材
     *
     * @param material 广告素材
     * @return 结果
     */
    int updateMaterial(Material material);

    /**
     * 获取已使用的创新弹层ID
     *
     * @param layerIds 弹层ID列表
     * @return 已使用的创新弹层ID列表
     */
    List<Long> selectUsedLayerId(@Param("layerIds") List<Long> layerIds);
}
