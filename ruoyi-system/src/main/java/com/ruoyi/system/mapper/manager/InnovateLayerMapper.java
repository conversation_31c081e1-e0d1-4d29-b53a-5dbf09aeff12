package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.advert.InnovateLayer;

import java.util.List;

/**
 * 创新弹层Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-09-26
 */
public interface InnovateLayerMapper {

    /**
     * 查询创新弹层
     * 
     * @param id 创新弹层ID
     * @return 创新弹层
     */
    InnovateLayer selectInnovateLayerById(Long id);

    /**
     * 查询创新弹层列表
     * 
     * @param innovateLayer 创新弹层
     * @return 创新弹层集合
     */
    List<InnovateLayer> selectInnovateLayerList(InnovateLayer innovateLayer);

    /**
     * 新增创新弹层
     * 
     * @param innovateLayer 创新弹层
     * @return 结果
     */
    int insertInnovateLayer(InnovateLayer innovateLayer);

    /**
     * 修改创新弹层
     * 
     * @param innovateLayer 创新弹层
     * @return 结果
     */
    int updateInnovateLayer(InnovateLayer innovateLayer);
}
