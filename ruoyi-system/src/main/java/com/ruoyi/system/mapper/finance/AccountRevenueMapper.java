package com.ruoyi.system.mapper.finance;

import com.ruoyi.system.entity.account.finance.AccountRevenueEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账户收益表 Mapper
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:30
 */
public interface AccountRevenueMapper {

    /**
     * 根据账号id查询
     *
     * @param accountId 账号id
     * @return 详情
     */
    AccountRevenueEntity selectByAccountId(Long accountId);

    /**
     * 增加收益
     *
     * @param accountId 账号id
     * @param revenue   收益金额
     * @param withdrawableAmount 可提现金额
     * @return 结果
     */
    int addRevenue(@Param("accountId") Long accountId, @Param("revenue") Integer revenue, @Param("withdrawableAmount") Integer withdrawableAmount);

    /**
     * 增加预付款
     *
     * @param accountId 账号id
     * @param prepayAmount 预付款
     * @return 结果
     */
    int addPrepayAmount(@Param("accountId") Long accountId, @Param("prepayAmount") Integer prepayAmount);

    /**
     * 扣减可提现金额
     *
     * @param accountId 账号id
     * @param amount    提现金额
     * @return 结果
     */
    int deductionWithdrawAmount(@Param("accountId") Long accountId,@Param("amount") Integer amount);

    /**
     * 修改媒体付款类型
     *
     * @param accountId 账号id
     * @param payType   付款类型
     * @return 结果
     */
    int updatePayType(@Param("accountId") Long accountId, @Param("payType") Integer payType);

    /**
     * 查询预付款媒体账号ID列表
     *
     * @return 预付款媒体收益列表
     */
    List<AccountRevenueEntity> selectPrepayAccountRevenue();

    /**
     * 查询预付款媒体账号ID列表
     *
     * @param accountIds 账号ID列表
     * @return 预付款媒体收益列表
     */
    List<AccountRevenueEntity> selectPrepayAccountRevenueByAccountIds(@Param("accountIds") List<Long> accountIds);
}
