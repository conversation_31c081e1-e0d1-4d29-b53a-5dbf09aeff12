package com.ruoyi.system.mapper.advertiser.finance;

import com.ruoyi.system.bo.advertiser.finance.AdvertiserBalanceStatisticBo;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserBalanceEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserBalanceUpdateReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告主账户余额表 Mapper
 *
 * <AUTHOR>
 * @date 2022-3-18 17:57:16
 */
public interface AdvertiserBalanceMapper {

    /**
     * 新增广告主账户余额
     *
     * @param entity 参数
     * @return 影响行数
     */
    int insert(AdvertiserBalanceEntity entity);

    /**
     * 更新广告主账户余额
     *
     * @param entity 参数
     * @return 影响行数
     */
    int updateById(AdvertiserBalanceEntity entity);

    /**
     * 更新广告主账户余额
     *
     * @param req 参数
     * @return 影响行数
     */
    int updateBalance(AdvertiserBalanceUpdateReq req);

    /**
     * 查询广告主的账户余额
     *
     * @param accountId 广告主ID
     * @return 账户余额
     */
    AdvertiserBalanceEntity selectByAccountId(Long accountId);

    /**
     * 查询多个广告主的账户余额总和
     *
     * @param accountIds 广告主ID列表
     * @return 账户余额总和
     */
    Integer sumByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * 查询广告主余额列表
     *
     * @param accountIds 广告主ID列表
     * @return 余额列表
     */
    List<AdvertiserBalanceEntity> selectList(@Param("accountIds") List<Long> accountIds);

    /**
     * 查询广告主余额汇总
     *
     * @param accountIds 广告主ID列表
     * @param excludeAccountIds 排除的广告主ID列表
     * @return 余额汇总
     */
    AdvertiserBalanceStatisticBo selectStatisticBalance(@Param("accountIds") List<Long> accountIds, @Param("excludeAccountIds") List<Long> excludeAccountIds);
}
