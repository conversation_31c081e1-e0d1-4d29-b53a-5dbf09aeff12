package com.ruoyi.system.mapper.landpage;

import java.util.List;
import com.ruoyi.system.entity.datashow.LandpageFormRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 落地页单记录Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-31
 */
public interface LandpageFormRecordMapper {

    /**
     * 查询落地页单记录
     *
     * @param id 落地页单记录ID
     * @return 落地页单记录
     */
    LandpageFormRecord selectLandpageFormRecordById(Long id);

    /**
     * 根据订单号ID查询记录是否存在
     *
     * @param orderId 订单号
     * @return 是否存在(1.存在,2.不存在)
     */
    Integer existByOrderId(@Param("orderId") String orderId);

    /**
     * 查询落地页单记录列表
     *
     * @param param 查询条件
     * @return 落地页单记录集合
     */
    List<LandpageFormRecord> selectList(LandpageFormRecord param);

    /**
     * 新增落地页单记录
     *
     * @param landpageFormRecord 落地页单记录
     * @return 结果
     */
    int insertLandpageFormRecord(LandpageFormRecord landpageFormRecord);

    /**
     * 修改落地页单记录
     *
     * @param landpageFormRecord 落地页单记录
     * @return 结果
     */
    int updateLandpageFormRecord(LandpageFormRecord landpageFormRecord);

    /**
     * 根据身份证查询落地页单记录列表
     *
     * @param idCardMd5 身份证
     * @param landpageTag 落地页标签
     * @return 落地页单记录集合
     */
    List<LandpageFormRecord> selectByIdCardMd5(@Param("idCardMd5") String idCardMd5, @Param("landpageTag")  String landpageTag);

    /**
     * 表单是否重复
     *
     * @param idCardMd5 身份证
     * @param landpageTag 落地页标签
     * @return 是否重复
     */
    Integer isFormRepeated(@Param("idCardMd5") String idCardMd5, @Param("landpageTag")  String landpageTag);

    /**
     * 表单15天内是否重复
     *
     * @param idCardMd5 身份证
     * @param landpageTag 落地页标签
     * @return 是否重复
     */
    Integer isFormRepeatedInHalfMonth(@Param("idCardMd5") String idCardMd5, @Param("landpageTag")  String landpageTag);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();

    /**
     * 查询所有的媒体ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAppIds();

    /**
     * 查询所有的广告位ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalSlotIds();
}
