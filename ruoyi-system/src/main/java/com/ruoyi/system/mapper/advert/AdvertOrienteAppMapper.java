package com.ruoyi.system.mapper.advert;

import com.ruoyi.system.bo.advert.AdvertOrientAppBo;
import com.ruoyi.system.domain.advert.AdvertOrienteApp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告配置定向媒体Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
public interface AdvertOrienteAppMapper {

    /**
     * 查询广告配置定向的媒体
     *
     * @param orientId 广告配置ID
     * @return 广告媒体关联
     */
    List<AdvertOrientAppBo> selectListByOrientId(Long orientId);

    /**
     * 查询广告配置定向的媒体
     *
     * @param orientId 广告配置ID
     * @return 广告媒体关联
     */
    List<AdvertOrienteApp> selectByOrientId(Long orientId);

    /**
     * 查询广告配置定向的媒体
     *
     * @param advertIds 广告ID列表
     * @return 广告媒体关联
     */
    List<AdvertOrienteApp> selectByAdvertIds(@Param("advertIds") List<Long> advertIds);

    /**
     * 查询广告配置定向的媒体
     *
     * @param orientIds 配置ID列表
     * @return 广告媒体关联
     */
    List<AdvertOrienteApp> selectByOrientIds(@Param("orientIds") List<Long> orientIds);

    /**
     * 查询广告配置定向的媒体
     *
     * @param advertId 广告ID
     * @return 广告媒体关联
     */
    List<AdvertOrienteApp> selectByAdvertId(@Param("advertId") Long advertId);

    /**
     * 查询广告配置定向的媒体
     *
     * @param advertId 广告ID
     * @return 广告媒体关联
     */
    List<AdvertOrientAppBo> selectListByAdvertId(@Param("advertId") Long advertId);

    /**
     * 删除广告配置定向的媒体
     *
     * @param orientId 广告配置ID
     * @return 结果
     */
    int deleteByOrientId(Long orientId);

    /**
     * 批量新增广告配置定向媒体
     *
     * @param list 广告配置定向媒体列表
     * @return 结果
     */
    int batchInsert(List<AdvertOrienteApp> list);
}
