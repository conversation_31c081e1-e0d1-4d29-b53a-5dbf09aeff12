package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.landpage.LandpageSkinEntity;

import java.util.List;

/**
 * 落地页皮肤表 Mapper
 *
 * <AUTHOR>
 * @date 2023-4-3 16:56:23
 */
public interface LandpageSkinMapper {

    /**
     * 新增记录
     */
    int insert(LandpageSkinEntity landpageSkinEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(LandpageSkinEntity landpageSkinEntity);

    /**
     * 根据id获取
     */
    LandpageSkinEntity selectById(Long id);

    /**
     * 查询列表
     */
    List<LandpageSkinEntity> selectList(LandpageSkinEntity param);
}
