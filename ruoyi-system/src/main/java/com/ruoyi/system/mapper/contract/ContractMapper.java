package com.ruoyi.system.mapper.contract;

import com.ruoyi.system.entity.contract.ContractEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同表 Mapper
 *
 * <AUTHOR>
 * @date 2022-11-3 17:14:28
 */
public interface ContractMapper {
    /**
     * 新增记录
     */
    int insert(ContractEntity contractEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(ContractEntity contractEntity);

    /**
     * 根据id获取
     */
    ContractEntity selectById(Long id);

    /**
     * 根据乙方账户id获取合同列表
     * @param accountId
     * @return
     */
    List<ContractEntity> selectListByAccountId(Long accountId);

    /**
     * 根据账号id列表查询最新的合同
     * @param accountIds
     * @return
     */
    List<ContractEntity> selectLatestContractByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * 根据最新的发票状态查询账号id列表
     * @param status
     * @return
     */
    List<Long> selectAccountIdsByLatestContractStatus(Integer status);

    /**
     * 根据合同编号查询合同
     * @return
     */
    ContractEntity selectByContractCode(String contractCode);

}
