package com.ruoyi.system.mapper.convert;

import com.ruoyi.system.bo.publisher.ConvertUploadSummaryDataBo;
import com.ruoyi.system.entity.convert.ConvertUploadRecordEntity;
import com.ruoyi.system.req.manager.ConvertUploadRecordListReq;

import java.util.List;

/**
 * 媒体转化上报记录 Mapper
 *
 * <AUTHOR>
 * @date 2022-10-19 10:30:00
 */
public interface ConvertUploadRecordMapper {

    /**
     * 查询列表
     */
    List<ConvertUploadRecordEntity> selectList(ConvertUploadRecordListReq req);

    /**
     * 查询汇总数据
     */
    List<ConvertUploadSummaryDataBo> selectSummaryData(ConvertUploadRecordListReq req);

    /**
     * 新增记录
     */
    int insert(ConvertUploadRecordEntity convertUploadRecordEntity);

    /**
     * 根据id更新
     */
    int updateById(ConvertUploadRecordEntity convertUploadRecordEntity);

    /**
     * 根据id获取
     */
    ConvertUploadRecordEntity selectById(Long id);
}
