package com.ruoyi.system.mapper.advertiser;

import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordHistoryEntity;

/**
 * DSP广告主消费修改记录表 Mapper
 *
 * <AUTHOR>
 * @date 2022-7-13 17:28:38
 */
public interface DspAdvertiserConsumeRecordHistoryMapper {

    /**
     * 新增记录
     */
    int insert(DspAdvertiserConsumeRecordHistoryEntity entity);

    /**
     * 根据id更新
     */
    int updateById(DspAdvertiserConsumeRecordHistoryEntity entity);

    /**
     * 根据id获取
     */
    DspAdvertiserConsumeRecordHistoryEntity selectById(Long id);
}
