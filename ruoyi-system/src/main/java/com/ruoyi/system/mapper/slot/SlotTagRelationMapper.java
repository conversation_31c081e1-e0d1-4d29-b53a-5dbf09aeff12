package com.ruoyi.system.mapper.slot;

import com.ruoyi.system.bo.slot.SlotTagCountBo;
import com.ruoyi.system.entity.slot.SlotTagRelationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广告位标签关联表 Mapper
 *
 * <AUTHOR>
 * @date 2023-5-9 17:00:13
 */
public interface SlotTagRelationMapper {

    /**
     * 删除广告位的标签
     *
     * @param slotId 广告位ID
     * @return 影响行数
     */
    int deleteBySlotId(Long slotId);

    /**
     * 查询广告位的标签数量
     *
     * @param slotIds 广告位ID列表
     * @return 广告位ID-标签数量映射
     */
    List<SlotTagCountBo> selectTagCountListBySlotIds(@Param("slotIds") List<Long> slotIds);

    /**
     * 根据广告位ID查询标签列表
     *
     * @param slotId 广告位ID
     * @return 标签ID列表
     */
    List<Long> selectTagIdsBySlotId(@Param("slotId") Long slotId);

    /**
     * 根据标签ID查询广告位ID列表
     *
     * @param tagId 标签ID
     * @return 广告位ID列表
     */
    List<Long> selectSlotIdsByTagId(@Param("tagId") Long tagId);

    /**
     * 批量新增
     *
     * @param entities 标签列表
     * @return 影响行数
     */
    int batchInsert(@Param("entities") List<SlotTagRelationEntity> entities);
}
