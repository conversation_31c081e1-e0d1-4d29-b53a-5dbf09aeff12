package com.ruoyi.system.mapper.privatesphere;

import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 私域客服渠道表 Mapper
 *
 * <AUTHOR>
 * @date 2023-3-6 15:22:11
 */
public interface PrivateSphereKefuChannelMapper {
    /**
     * 新增记录
     */
    int insert(PrivateSphereKefuChannelEntity privateSphereKefuChannelEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(PrivateSphereKefuChannelEntity privateSphereKefuChannelEntity);

    /**
     * 根据id获取
     */
    PrivateSphereKefuChannelEntity selectById(Long id);

    /**
     * 根据渠道简称查询
     * @return
     */
    PrivateSphereKefuChannelEntity selectByChannel(@Param("channel") String channel);

    /**
     * 获取所有客服渠道列表
     * @return
     */
    List<PrivateSphereKefuChannelEntity> selectAllList();

    /**
     * 根据渠道名称获取渠道id列表
     * @param channel
     * @return
     */
    List<Long> selectIdListByChannel(@Param("channel") String channel);

    /**
     * 根据id列表查询渠道信息
     * @param ids
     * @return
     */
    List<PrivateSphereKefuChannelEntity> selectListByIds(@Param("ids") List<Long> ids);

}
