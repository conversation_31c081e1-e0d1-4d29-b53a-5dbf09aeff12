package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.account.AccountRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账户关联Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-15
 */
public interface AccountRelationMapper {

    /**
     * 查询账户关联列表
     *
     * @param accountIds 账号ID列表
     * @param relationTypes 关联类型列表
     * @return 账户关联集合
     */
    List<AccountRelation> selectBySrcAccountIds(@Param("accountIds") List<Long> accountIds,
                                                @Param("relationTypes") List<Integer> relationTypes);

    /**
     * 查询账户关联列表
     *
     * @param accountIds 账号ID列表
     * @param relationTypes 关联类型列表
     * @return 账户关联集合
     */
    List<AccountRelation> selectByDestAccountIds(@Param("accountIds") List<Long> accountIds,
                                                 @Param("relationTypes") List<Integer> relationTypes);

    /**
     * 查询账户关联列表
     *
     * @param destAccountIds 终点账户ID列表
     * @param relationType 关联类型
     * @return 账户关联集合
     */
    List<AccountRelation> selectListByDestAccountAndRelationType(@Param("destAccountIds") List<Long> destAccountIds,
                                                                 @Param("relationType") Integer relationType);

    /**
     * 新增账户关联
     *
     * @param accountRelation 账户关联
     * @return 结果
     */
    int insertAccountRelation(AccountRelation accountRelation);

    /**
     * 删除账号关联
     *
     * @param destAccountId 目标账号ID
     * @param relationType 关联类型
     * @return 结果
     */
    int deleteAccountRelation(@Param("destAccountId") Long destAccountId, @Param("relationType") Integer relationType);
}
