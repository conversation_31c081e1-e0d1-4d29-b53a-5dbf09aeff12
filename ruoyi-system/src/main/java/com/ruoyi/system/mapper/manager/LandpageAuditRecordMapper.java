package com.ruoyi.system.mapper.manager;

import com.ruoyi.system.entity.manager.LandpageAuditRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 落地页送审记录Mapper接口
 *
 * <AUTHOR>
 * @date 2021-09-10
 */
@Deprecated
public interface LandpageAuditRecordMapper {

    /**
     * 查询落地页送审记录
     *
     * @param id 落地页送审记录ID
     * @return 落地页送审记录
     */
    LandpageAuditRecord selectLandpageAuditRecordById(Long id);

    /**
     * 查询落地页送审记录
     *
     * @param originUrlMd5 链接MD5
     * @return 落地页送审记录
     */
    LandpageAuditRecord selectByOriginUrl(@Param("originUrlMd5") String originUrlMd5);

    /**
     * 查询落地页送审记录列表
     *
     * @param landpageAuditRecord 落地页送审记录
     * @return 落地页送审记录集合
     */
    List<LandpageAuditRecord> selectLandpageAuditRecordList(LandpageAuditRecord landpageAuditRecord);

    /**
     * 新增落地页送审记录
     *
     * @param record 落地页送审记录
     * @return 结果
     */
    int insertLandpageAuditRecord(LandpageAuditRecord record);

    /**
     * 修改落地页送审记录
     *
     * @param landpageAuditRecord 落地页送审记录
     * @return 结果
     */
    int updateLandpageAuditRecord(LandpageAuditRecord landpageAuditRecord);

    /**
     * 判断链接是否已存在
     *
     * @param originUrlMd5 链接MD5
     * @return 是否已存在(1.是,0.否)
     */
    int checkOriginUrlExist(@Param("originUrlMd5") String originUrlMd5);
}
