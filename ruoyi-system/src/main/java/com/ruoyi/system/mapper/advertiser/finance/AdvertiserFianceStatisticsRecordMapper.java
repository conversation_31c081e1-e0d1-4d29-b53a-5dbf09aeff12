package com.ruoyi.system.mapper.advertiser.finance;

import com.ruoyi.system.entity.advertiser.finance.AdvertiserFianceStatisticsRecordEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordListReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordUpdateReq;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 广告主财务汇总记录表 Mapper
 *
 * <AUTHOR>
 * @date 2022-3-18 17:58:27
 */
public interface AdvertiserFianceStatisticsRecordMapper {

    /**
     * 新增广告主财务汇总记录
     *
     * @param record 新增参数
     * @return 影响行数
     */
    int insert(AdvertiserFianceStatisticsRecordEntity record);

    /**
     * 根据ID更新广告主财务汇总记录
     *
     * @param req 更新参数
     * @return 影响行数
     */
    int update(AdvertiserFianceStatisticsRecordUpdateReq req);

    /**
     * 查询广告主财务汇总记录列表
     *
     * @param req 参数
     * @return 广告主财务汇总记录列表
     */
    List<AdvertiserFianceStatisticsRecordEntity> selectList(AdvertiserFianceStatisticsRecordListReq req);

    /**
     * 根据广告主ID和日期查询财务汇总记录
     *
     * @param accountId 广告主ID
     * @param curDate 日期
     * @return 广告主财务汇总记录
     */
    AdvertiserFianceStatisticsRecordEntity selectByAccountIdAndDate(@Param("accountId") Long accountId, @Param("curDate") Date curDate);

    /**
     * 查询广告主最新的财务汇总记录
     *
     * @param accountId 广告主ID
     * @return 广告主财务汇总记录
     */
    AdvertiserFianceStatisticsRecordEntity selectLatestByAccountId(@Param("accountId") Long accountId);
}
