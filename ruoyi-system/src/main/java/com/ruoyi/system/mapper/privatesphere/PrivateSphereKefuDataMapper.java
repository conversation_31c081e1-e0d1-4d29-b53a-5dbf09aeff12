package com.ruoyi.system.mapper.privatesphere;

import com.ruoyi.system.bo.privatesphere.PrivateSphereKefuDataListBO;
import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 私域客服数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-3-6 18:51:08
 */
public interface PrivateSphereKefuDataMapper {
    /**
     * 新增记录
     */
    int insert(PrivateSphereKefuDataEntity privateSphereKefuDataEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(PrivateSphereKefuDataEntity privateSphereKefuDataEntity);

    /**
     * 根据id获取
     */
    PrivateSphereKefuDataEntity selectById(Long id);

    /**
     * 根据日期和产品查询
     * @param curDate
     * @param productId
     * @param kefuChannelId
     * @return
     */
    PrivateSphereKefuDataEntity selectByProductAndKefuChannelId(@Param("curDate") Date curDate, @Param("productId") Long productId , @Param("kefuChannelId") Long kefuChannelId );

    /**
     * 根据条件查询私域数据
     * @param bo
     * @return
     */
    List<PrivateSphereKefuDataEntity> selectListByParam(PrivateSphereKefuDataListBO bo);

    /**
     * 统计总数据
     * @param bo
     * @return
     */
    PrivateSphereKefuDataEntity statisticsByParam(PrivateSphereKefuDataListBO bo);

    /**
     * 根据条件查询id列表
     * @param bo
     * @return
     */
    List<Long> selectIdsByParam(PrivateSphereKefuDataListBO bo);

    /**
     * 根据客服渠道id查询数据数，删除渠道校验
     * @param kefuChannelId
     * @return
     */
    int countByKefuChannelId(Long kefuChannelId);


}
