package com.ruoyi.system.mapper.landpage.article;

import com.ruoyi.system.bo.landpage.article.ArticleDataBo;
import com.ruoyi.system.bo.landpage.article.ArticleDataParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleDataUpdateParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleHourDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 文章时段数据表 Mapper
 *
 * <AUTHOR>
 * @date 2023-12-1 15:14:16
 */
public interface ArticleHourDataMapper {

    /**
     * 新增记录
     */
    int insert(ArticleHourDataEntity articleHourDataEntity);

    /**
     * 根据id更新
     */
    int updateById(ArticleHourDataEntity articleHourDataEntity);

    /**
     * 更新数据
     */
    int update(ArticleDataUpdateParamBo param);

    /**
     * 根据id获取
     */
    ArticleHourDataEntity selectById(Long id);

    /**
     * 查询文章数据
     */
    ArticleHourDataEntity selectBy(@Param("curDate") Date curDate, @Param("curHour") Integer curHour, @Param("linkId") Long linkId, @Param("articleId") Long articleId);

    /**
     * 根据条件查询汇总数据
     */
    ArticleDataBo selectSumBy(ArticleDataParamBo param);

    /**
     * 根据条件查询日维度数据
     */
    List<ArticleDataBo> selectDayDataBy(ArticleDataParamBo param);
}
