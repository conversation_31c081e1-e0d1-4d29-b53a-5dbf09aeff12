package com.ruoyi.system.mapper.landpage;

import com.ruoyi.system.entity.landpage.IspVipLandpageFormRecordEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 运营商会员落地页记录 Mapper
 *
 * <AUTHOR>
 * @date 2023-11-7 11:41:45
 */
public interface IspVipLandpageFormRecordMapper {

    /**
     * 新增记录
     */
    int insert(IspVipLandpageFormRecordEntity ispVipLandpageFormRecordEntity);

    /**
     * 根据id更新
     */
    int updateById(IspVipLandpageFormRecordEntity ispVipLandpageFormRecordEntity);

    /**
     * 查询记录
     */
    IspVipLandpageFormRecordEntity selectById(@Param("id") Long id);

    /**
     * 查询记录
     */
    IspVipLandpageFormRecordEntity selectBy(@Param("orderId") String orderId, @Param("phone") String phone);

    /**
     * 查询记录
     */
    IspVipLandpageFormRecordEntity selectByBizOrderNoAndOrderId(@Param("bizOrderNo") String bizOrderNo, @Param("orderId") String orderId);

    /**
     * 查询记录
     */
    IspVipLandpageFormRecordEntity selectByOrderId(@Param("orderId") String orderId);
}
