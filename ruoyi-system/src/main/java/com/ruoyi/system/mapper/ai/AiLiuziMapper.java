package com.ruoyi.system.mapper.ai;

import com.ruoyi.system.entity.ai.AiLiuziEntity;
import com.ruoyi.system.req.ai.AiLiuziListReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ai留资mapper
 * <AUTHOR>
 * @date 2024/1/2 14:54
 */
public interface AiLiuziMapper {
    /**
     * 新增
     * @param entity
     * @return
     */
    int insert(AiLiuziEntity entity);

    /**
     * 根据id更新
     * @param entity
     * @return
     */
    int updateById(AiLiuziEntity entity);

    /**
     * 根据条件查询留资列表
     * @param req
     * @return
     */
    List<AiLiuziEntity> selectList(@Param("req") AiLiuziListReq req);
}
