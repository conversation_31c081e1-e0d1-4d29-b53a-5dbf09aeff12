package com.ruoyi.system.mapper.fc;

import com.ruoyi.system.entity.fc.FcLinkDayDataEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 丰巢链接日数据统计Mapper接口
 */
public interface FcLinkDayDataMapper {

    /**
     * 新增丰巢链接日数据统计
     *
     * @param fcLinkDayDataEntity 丰巢链接日数据统计
     * @return 结果
     */
    int insertFcLinkDayData(FcLinkDayDataEntity fcLinkDayDataEntity);

    /**
     * 修改丰巢链接日数据统计
     *
     * @param fcLinkDayDataEntity 丰巢链接日数据统计
     * @return 结果
     */
    int updateFcLinkDayData(FcLinkDayDataEntity fcLinkDayDataEntity);

    /**
     * 根据日期和丰巢链接key查询统计数据
     *
     * @param curDate 日期字符串
     * @param fcLinkKey 丰巢链接key
     * @return 丰巢链接日数据统计
     */
    FcLinkDayDataEntity selectFcLinkDayData(@Param("curDate") String curDate, @Param("fcLinkKey") String fcLinkKey);
} 