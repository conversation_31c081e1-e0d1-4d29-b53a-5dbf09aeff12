package com.ruoyi.system.mapper.open;

import com.ruoyi.system.entity.open.HhOcpxRecordEntity;

/**
 * 辉煌OCPX事件记录 Mapper
 *
 * <AUTHOR>
 * @date 2024-9-18 17:40:48
 */
public interface HhOcpxRecordMapper {

    /**
     * 新增记录
     */
    int insert(HhOcpxRecordEntity hhOcpxRecordEntity);

    /**
     * 根据id删除
     */
    int deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(HhOcpxRecordEntity hhOcpxRecordEntity);

    /**
     * 根据id获取
     */
    HhOcpxRecordEntity selectById(Long id);


}
