package com.ruoyi.system.mapper.advert;

import java.util.List;

import com.ruoyi.system.bo.advert.AdvertBannedAppBo;
import com.ruoyi.system.domain.advert.AdvertBannedApp;
import org.apache.ibatis.annotations.Param;

/**
 * 广告配置屏蔽媒体Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
public interface AdvertBannedAppMapper {

    /**
     * 查询广告配置屏蔽的媒体
     *
     * @param orientId 广告配置ID
     * @return 广告媒体关联
     */
    List<AdvertBannedAppBo> selectListByOrientId(Long orientId);

    /**
     * 查询广告配置屏蔽的媒体
     *
     * @param orientId 广告配置ID
     * @return 广告媒体关联
     */
    List<AdvertBannedApp> selectByOrientId(Long orientId);

    /**
     * 查询屏蔽媒体的广告
     *
     * @param appId 媒体Id
     * @return 广告媒体关联
     */
    List<AdvertBannedApp> selectByAppId(Long appId);

    /**
     * 查询广告配置屏蔽的媒体
     *
     * @param advertIds 广告ID列表
     * @return 广告媒体关联
     */
    List<AdvertBannedApp> selectByAdvertIds(@Param("advertIds") List<Long> advertIds);

    /**
     * 查询广告配置屏蔽的媒体
     *
     * @param advertId 广告ID
     * @return 广告媒体关联
     */
    List<AdvertBannedApp> selectByAdvertId(@Param("advertId") Long advertId);

    /**
     * 查询广告配置屏蔽的媒体
     *
     * @param advertId 广告ID
     * @return 广告媒体关联
     */
    List<AdvertBannedAppBo> selectListByAdvertId(@Param("advertId") Long advertId);

    /**
     * 删除广告配置屏蔽的媒体
     *
     * @param orientId 广告配置ID
     * @return 结果
     */
    int deleteByOrientId(Long orientId);

    /**
     * 批量新增广告配置屏蔽媒体
     *
     * @param list 广告配置屏蔽媒体列表
     * @return 结果
     */
    int batchInsert(List<AdvertBannedApp> list);
}
