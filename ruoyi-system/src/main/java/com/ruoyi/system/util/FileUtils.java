package com.ruoyi.system.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;

/**
 * <AUTHOR>
 * @date 2024/11/27 11:33
 */
public class FileUtils {
    private static final Logger log = LoggerFactory.getLogger(FileUtils.class);


    public static File convert(MultipartFile multipartFile) {
        try {
            FileUtil.touch(System.getProperty("user.home") + "/uploadPath");
            File file = FileUtil.touch(System.getProperty("user.home") + "/uploadPath/" + RandomUtil.randomNumbers(4) + multipartFile.getOriginalFilename());
            FileOutputStream outputStream = new FileOutputStream(file);
            outputStream.write(multipartFile.getBytes());
            outputStream.close();
            return file;
        } catch (Exception var3) {
            log.error("文件类型转换异常,e:", var3);
            return null;
        }
    }
}
