package com.ruoyi.system.util;

import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.CountMonitorTypeEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * 计数监控工具
 *
 * <AUTHOR>
 * @date 2022/05/19
 */
@Slf4j
public class CountMonitorUtils {

    /**
     * 错误数监控，到达阈值告警
     *
     * @param type 监控类型
     */
    public static void errorMonitor(final CountMonitorTypeEnum type) {
        errorMonitor(type, DingWebhookConfig.getSystemAlert(), "");
    }

    /**
     * 错误数监控，到达阈值告警
     *
     * @param type 监控类型
     * @param msg 补充告警信息
     */
    public static void errorMonitor(final CountMonitorTypeEnum type, String webhook, String msg) {
        if (null == type) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            try {
                RedisAtomicClient redisAtomicClient = SpringUtils.getBean(RedisAtomicClient.class);
                String key = EngineRedisKeyFactory.K024.join(type.getType());
                Long times = redisAtomicClient.incrBy(key, 1L, type.getInterval(), TimeUnit.MINUTES);
                if (NumberUtils.defaultLong(times) >= type.getThreshold()) {
                    RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K025.join(type.getType()), type.getInterval() * 60L);
                    if (lock != null) {
                        String sb = "计数告警\n\n" + type.getMsg();
                        if (StringUtils.isNotBlank(msg)) {
                            sb += "\n" + msg;
                        }
                        DingRobotUtil.sendText(webhook, sb);
                        SpringUtils.getBean(RedisCache.class).deleteObject(key);
                    }
                }
            } catch (Exception e) {
                log.error("CountMonitorUtils.errorMonitor error", e);
            }
        });
    }
}
