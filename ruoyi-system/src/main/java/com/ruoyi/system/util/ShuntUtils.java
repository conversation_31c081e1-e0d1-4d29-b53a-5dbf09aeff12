package com.ruoyi.system.util;

import com.ruoyi.system.domain.common.ShuntRatio;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 分流工具类
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
public class ShuntUtils {

    /**
     * 分流
     *
     * @param items 待分流集合
     * @param hash 分流哈希值
     * @return 命中分流的元素
     */
    public static  <T extends ShuntRatio> T shunt(List<T> items, Integer hash) {
        if (CollectionUtils.isEmpty(items) || null == hash) {
            return null;
        }

        // 计算分流值[0,100)
        int t = hash % 100;
        for (T item : items) {
            if (t < item.getRatio()) {
                return item;
            }
            t -= item.getRatio();
        }
        return null;
    }
}
