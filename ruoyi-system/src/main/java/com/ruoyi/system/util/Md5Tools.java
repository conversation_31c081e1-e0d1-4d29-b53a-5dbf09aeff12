package com.ruoyi.system.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 圈量签名工具类
 */
public class Md5Tools {

    public static String GenMd5Signature(List<String> data) throws NoSuchAlgorithmException {
        // 1. 字典序排序
        Collections.sort(data);

        // 2. 拼接成一个字符串
        StringBuilder encryStr = new StringBuilder();
        for (String v : data) {
            encryStr.append(v);
        }

        // 3. md5加密
        MessageDigest m = MessageDigest.getInstance("MD5");
        m.update(encryStr.toString().getBytes());
        byte[] s = m.digest();
        StringBuilder result = new StringBuilder();

        for (byte b : s) {
            result.append(Integer.toHexString((0x000000FF & b) | 0xFFFFFF00).substring(6));
        }
        return result.toString();
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        String[] dataArr = new String[5];
        dataArr[0] = "co23e51cc5cac543a9";
        dataArr[1] = "123456";
        dataArr[2] = "1623139834";
        dataArr[3] = "2f5acc3956c3459a8bafc18a97f6db3c";
        dataArr[4] = "TDys3S8Q4/jc1YhppJqcX00bCTJZ0vKTiLsKRvYUHBT6+X/Y3M864fidTzucEBtyFlJv3Gw2r/PWPQVjT0vitQ==";

        String a = GenMd5Signature(Arrays.asList(dataArr));
        System.out.println(a);
    }
}
