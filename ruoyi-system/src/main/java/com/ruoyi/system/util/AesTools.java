package com.ruoyi.system.util;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 圈量解密工具类
 */
public class AesTools {

    public static byte[] str2ByteArr(String hexStr) {
        return hexStr.getBytes();
    }

    public static String encryptCBC(String sSrc, String encodingAESKey) {
        try {
            int base = 16;
            byte[] raw = str2ByteArr(encodingAESKey);
            byte[] ivRaw = new byte[base];
            System.arraycopy(raw, 0, ivRaw, 0, base);

            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec iv = new IvParameterSpec(ivRaw);
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);

            // 1. AES加密
            byte[] original = cipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8));

            // 2. 用base64编码
            byte[] encrypted = Base64.getEncoder().encode(original);
            return new String(encrypted);
        } catch (Exception ex) {
            System.out.println(ex.toString());
        }
        return null;
    }

    public static String decryptCBC(String sSrc, String encodingAESKey) {
        try {
            int base = 16;
            byte[] raw = str2ByteArr(encodingAESKey);
            byte[] ivRaw = new byte[base];
            System.arraycopy(raw, 0, ivRaw, 0, base);

            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec iv = new IvParameterSpec(ivRaw);
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);

            // 1. 先用base64解密
            byte[] encrypted = Base64.getDecoder().decode(sSrc);
            // 2. AES解密
            try {
                byte[] original = cipher.doFinal(encrypted);
                return new String(original);
            } catch (Exception e) {
                System.out.println(e.toString());
                return null;
            }
        } catch (Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
    }

    public static void main(String[] args) throws Exception {
        // 后台配置的aes key
        String encodingAESKey = "588bc7cfb5a34507ba132cc75b6df005";
        String content = "Kuf/0j1nvhFMxVGadSBd6E9xgZqvEvAOA1BuO514GUJK/GjnfzccnUP0c0VZ/T5E6E+1ASiLqc8KTEYqoUsKqVBLUtCC1VW/qKY46snL/uGCI3VWP86Uk74q3EoMzzBHSWZXqxOACoNi+ETnktWEwbe6lP34nQweuULqrw5d9ft/mlyOEYeRODObLFJhqLdXAQll/335sXrMKCtIQV/Pw+PCncG6/gtEMscdH3T6PnBHrhoM0dPXZtdrCE819h4DaQ+OLnCXHTTpj8ljX/Ap/rOLOfBMOvJjt3Bm1bq14i+3QgpP5nPJxv50YrdE9mX3ksP1UgjbUjZxffQCNsJYob7/W83O6Mo36MwzCse6er8kqHwaBUQlcSc8S7Jly68Te+vZKJ+7C0j57T6/3VYnWoP5VJVQEfHvdaqbTL72QG89W8XuPVz5DttvUv34htX/bFEW813NOoWsrg1piXyXeFNZ9oFshShgcDN7Gymjqz0eC0cH4re934sk3m5LgFrb";

        String a = decryptCBC(content, encodingAESKey);
        System.out.println(a);
    }
}
