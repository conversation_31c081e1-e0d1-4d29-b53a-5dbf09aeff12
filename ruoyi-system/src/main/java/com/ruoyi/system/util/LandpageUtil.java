package com.ruoyi.system.util;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;

import java.util.Objects;

/**
 * 落地页工具类
 *
 * <AUTHOR>
 * @date 2022/02/09
 */
public class LandpageUtil {

    /**
     * 判断落地页链接是否包含落地页标识
     *
     * @param landpageUrl 落地页链接
     * @return 是否包含落地页标识
     */
    public static boolean hasLpk(String landpageUrl) {
        return StrUtil.containsAnyIgnoreCase(landpageUrl, "/land/", "%2Fland%2F");
    }

    /**
     * 判断落地页链接是否是快应用
     *  华为快应用: https://hapjs.org/app/com.nuohe.quickapp?lp=https://u10.fhtao.com/land/render/0AZTZ13G
     *  OV快应用: https://hapjs.org/app/com.nuohe.quickapp.snow?lp=https://u10.fhtao.com/land/render/0AZTZ13G
     *
     * @param landpageUrl 落地页链接
     * @return 是否是快应用
     */
    public static boolean isQuickApp(String landpageUrl) {
        return StrUtil.containsAnyIgnoreCase(landpageUrl, "hapjs", "quickapp");
    }

    /**
     * 从落地页链接中解析落地页标识
     *
     * @param landpageUrl 落地页链接
     * @return 落地页标识
     */
    public static String extractLpk(String landpageUrl) {
        if (StringUtils.isBlank(landpageUrl)) {
            return "";
        }
        String lpk = "";
        if(landpageUrl.contains("render")){
            lpk = ReUtil.getGroup0("(?<=land/render/)\\w+", landpageUrl);
            if(StringUtils.isBlank(lpk)){
                lpk = ReUtil.getGroup0("(?<=land/render/)\\w+", UrlUtils.urlDecode(landpageUrl));
            }
            if (StringUtils.isNotBlank(lpk)) {
                return lpk;
            }
        }

        if (landpageUrl.contains("/lp/qwtf/jump") && landpageUrl.contains("lpKey")) {
            lpk = ReUtil.getGroup0("(?<=lpKey\\=)\\w+", landpageUrl);
            if (StringUtils.isNotBlank(lpk)) {
                return lpk;
            }
        }

        if (!hasLpk(landpageUrl)) {
            return "";
        }

        if(StringUtils.isBlank(lpk)){
            lpk = ReUtil.getGroup0("(?<=land/)\\w+", landpageUrl);
        }

        if (StringUtils.isBlank(lpk) && landpageUrl.contains("land")) {
            lpk = ReUtil.getGroup0("(?<=land/)\\w+", UrlUtils.urlDecode(landpageUrl));
        }
        return lpk;
    }

    /**
     * 落地页表单上报是否成功
     *
     * @param resp 上报结果
     * @return 是否成功(1.成功, 0.失败)
     */
    public static Integer isCallbackSuccess(String resp) {
        try {
            JSONObject result = JSON.parseObject(StringUtils.defaultString(resp));
            if (null != result) {
                Integer code = result.getInteger("code");
                Boolean success = result.getBoolean("success");
                if (Objects.equals(code, 0) || Objects.equals(code, 200) || BooleanUtil.isTrue(success)) {
                    return 1;
                }
            }
        } catch (Exception ignore) {
        }
        return 0;
    }

    /**
     * 是否是能重传的错误
     *
     * @param resp 上报结果
     * @return 是否能重传
     */
    public static boolean canRetryCallback(String resp) {
        return StringUtils.isNotBlank(resp)
                && isCallbackSuccess(resp) != 1
                && !(StrUtil.containsAny(resp, "疫情", "校验", "地址", "地区", "年龄", "无法", "已经", "重复",
                        "黑名单", "不能", "证", "下单", "号码预占失败", "提交", "联通"));
    }

    /**
     * 完善省名, 兼容不同地域数据库
     *
     * @param province 省
     * @return 完整的省名
     */
    public static String fixProvinceName(String province) {
        if (StringUtils.isBlank(province)) {
            return province;
        }
        if (province.contains("内蒙古")) {
            return "内蒙古自治区";
        } else if (province.contains("广西")) {
            return "广西壮族自治区";
        } else if (province.contains("西藏")) {
            return "西藏自治区";
        } else if (province.contains("宁夏")) {
            return "宁夏回族自治区";
        } else if (province.contains("新疆")) {
            return "新疆维吾尔自治区";
        } else if (province.contains("北京") || province.contains("上海") || province.contains("天津") || province.contains("重庆")) {
            return province + "市";
        } else {
            return province + "省";
        }
    }

    /**
     * 判断是否是有效的落地页回传链接
     *
     * @param url 链接
     * @return 是否有效链接
     */
    public static boolean isValidLpCallbackUrl(String url) {
        return StringUtils.isNotBlank(url) && url.startsWith("http");
    }
}
