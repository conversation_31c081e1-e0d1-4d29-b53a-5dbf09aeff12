package com.ruoyi.system.util;

import cn.hutool.core.util.RadixUtil;

/**
 * @author: keboom
 * @date: 2025/5/20
 */
public class ShortUrlUtils {

    private static final String BASE62_ALPHABET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    public static String shortUrlIdToUrl(Long id) {

        String encodedId = RadixUtil.encode(BASE62_ALPHABET, id);

        return encodedId;
    }

    public static long shortUrlUrlToId(String idStr) {

        long decode = RadixUtil.decode(BASE62_ALPHABET, idStr);

        return decode;
    }

}
