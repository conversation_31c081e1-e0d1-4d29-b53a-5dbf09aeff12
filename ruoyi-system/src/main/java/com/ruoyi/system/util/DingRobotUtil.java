package com.ruoyi.system.util;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 钉钉机器人工具类
 *
 * <AUTHOR>
 * @date 2021/9/30
 */
@Slf4j
public class DingRobotUtil {

    /**
     * 图片内容模板
     */
    private static final String PICTURE_TEMPLATE = "#### %s\n" +
            "![picture.png](%s)\n" +
            "\n%s";

    /**
     * 发送文本
     *
     * @param webhook 钉钉机器人链接
     * @param content 文本内容
     * @return 调用结果
     */
    public static String sendText(String webhook, String content) {
        return sendText(webhook, content, null, false);
    }

    /**
     * 发送文本
     *
     * @param webhook 钉钉机器人链接
     * @param content 文本内容
     * @param atMobiles @相关人员的手机号
     * @param isAtAll 是否 @所有人
     * @return 调用结果
     */
    public static String sendText(String webhook, String content, List<String> atMobiles, boolean isAtAll) {
        if (StringUtils.isBlank(webhook) || !webhook.startsWith("http")) {
            return null;
        }
        Map<String, Object> at = new HashMap<>();
        at.put("atMobiles", CollUtil.defaultIfEmpty(atMobiles, Collections.emptyList()));
        at.put("isAtAll", isAtAll);

        Map<String, Object> params = new HashMap<>();
        params.put("msgtype", "text");
        params.put("text", buildMap("content", content));
        params.put("at", at);

        String resp = HttpClientUtil.sendPostJson(webhook,  JSON.toJSONString(params));
        JSONObject result = JSON.parseObject(resp);
        if (null != result) {
            if (!Objects.equals(result.getInteger("errcode"), 0)) {
                log.error("钉钉提醒失败, resp={}, webhook={}, content={}", resp, webhook, content);
            }
        }
        return resp;
    }

    /**
     * 发送Markdown消息
     *
     * @param webhook 钉钉机器人链接
     * @param title 标题
     * @param content 文本内容
     * @param atMobiles @相关人员的手机号
     * @param isAtAll 是否 @所有人
     * @return 调用结果
     */
    public static String sendMarkdown(String webhook, String title, String content, List<String> atMobiles, boolean isAtAll) {
        if (StringUtils.isBlank(webhook) || !webhook.startsWith("http")) {
            return null;
        }
        Map<String, Object> at = new HashMap<>();
        at.put("atMobiles", CollUtil.defaultIfEmpty(atMobiles, Collections.emptyList()));
        at.put("isAtAll", isAtAll);

        Map<String, String> markdown = new HashMap<>();
        markdown.put("title", title);
        markdown.put("text", content);

        Map<String, Object> params = new HashMap<>();
        params.put("msgtype", "markdown");
        params.put("markdown", markdown);
        params.put("at", at);

        return HttpClientUtil.sendPostJson(webhook,  JSON.toJSONString(params));
    }

    /**
     * 发送图片
     *
     * @param webhook 钉钉机器人链接
     * @param title 标题
     * @param picUrl 图片链接
     * @param extText 额外文本
     * @return 调用结果
     */
    public static String sendPicture(String webhook, String title, String picUrl, String extText) {
        Map<String, String> markdown = new HashMap<>();
        markdown.put("title", title);
        markdown.put("text", String.format(PICTURE_TEMPLATE, title, picUrl, extText));

        Map<String, String> params = new HashMap<>();
        params.put("msgtype", "markdown");
        params.put("markdown", JSON.toJSONString(markdown));

        return HttpClientUtil.sendPostJson(webhook,  JSON.toJSONString(params));
    }

    private static Map<String, Object> buildMap(String key, Object value) {
        Map<String, Object> map = new HashMap<>();
        map.put(key, value);
        return map;
    }
}
