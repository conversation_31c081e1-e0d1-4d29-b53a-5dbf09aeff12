package com.ruoyi.system.util;


import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.DateUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 广告位uv计算工具类
 *
 * 由于uv统计数据有问题，临时采用在原来基础上*百分比方式处理
 * 且只有在指定时间点内的数据才会 * 百分比
 * 需求提出人：吕斌
 *
 * <AUTHOR>
 * @date 2021/9/6 9:04 下午
 */
public class SlotReqUvCalculateUtil {

    public static Date startDate = DateUtil.beginOfDay(DateUtils.parseDate("2021-09-06"));
    public static Date endDate = DateUtil.beginOfDay(DateUtils.parseDate("2021-09-12"));

    public static Date secondStartDate = DateUtil.beginOfDay(DateUtils.parseDate("2021-09-11"));
    public static Date secondEndDate = DateUtil.beginOfDay(DateUtils.parseDate("2021-09-26"));

    /**
     * 重新计算广告位访问uv
     * @param date 数据日期
     * @param reqUv 访问uv
     * @return 计算后的访问uv
     */
    public static Integer calculateSlotReqUv(Date date, Long appId,Integer reqUv){
        if(reqUv == null){
            return 0;
        }

        if(Objects.equals(1186L,appId)){
            if(date.after(DateUtil.beginOfDay(DateUtils.parseDate("2022-02-14")))){
                return (int)Math.ceil(reqUv * 0.95);
            }
        }

        //TODO：后续数据正常，再加结束时间
        if(date.after(secondEndDate)){
            return reqUv;
        }
        if(date.after(secondStartDate)){
            return (int)Math.ceil(reqUv * 0.85);
        }
        if(date.after(startDate) && date.before(endDate)){
            return (int) Math.ceil(reqUv * 0.9);
        }

        return reqUv;
    }

}
