package com.ruoyi.system.entity.system;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 参数配置表域名为主查询配置信息
 *
 * <AUTHOR>
 * @date 2023-6-7 16:25:42
 */
@Data
public class SysConfigDomainEntity implements Serializable {

    /**
     * 参数主键
     */
    private Integer id;

    /**
     * 参数名称
     */
    private String configName;

    /**
     * 参数键名
     */
    private String configKey;

    /**
     * 参数键值
     */
    private String dataJson;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;


}

