package com.ruoyi.system.entity.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 落地页单记录对象 tb_landpage_form_record
 *
 * <AUTHOR>
 * @date 2021-08-31
 */
@Data
public class LandpageFormRecord implements Serializable {
    private static final long serialVersionUID = -2924698686677772856L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    @Excel(name = "广告ID")
    private Long advertId;

    /**
     * 媒体名称
     */
    @Excel(name = "媒体名称")
    private String appName;

    /**
     * 媒体ID
     */
    @Excel(name = "媒体ID")
    private Long appId;

    /**
     * 广告位ID
     */
    @Excel(name = "广告位ID")
    private Long slotId;

    /**
     * 渠道
     */
    @Excel(name = "渠道")
    private Integer channel;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 上报广告主
     */
    @Excel(name = "上报广告主")
    private Long targetAdvertiserId;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上报时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date callbackTime;

    /**
     * 上报状态
     */
    @Excel(name = "订单状态")
    private String callbackStatus;

    /**
     * 落地页链接
     */
    @Excel(name = "落地页链接")
    private String landpageUrl;

    /**
     * 来源链接
     */
    private String referer;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phone;

    /**
     * 省
     */
    @Excel(name = "地址(省)")
    private String province;

    /**
     * 市
     */
    @Excel(name = "地址(市)")
    private String city;

    /**
     * 区/县
     */
    @Excel(name = "地址(区)")
    private String district;

    /**
     * 行政区划代码
     */
    @Excel(name = "行政区划代码")
    private String areaNum;

    /**
     * 详细信息
     */
    @Excel(name = "详细信息")
    private String address;

    /**
     * IP
     */
    @Excel(name = "IP")
    private String ip;

    /**
     * 身份证号(加密)
     */
    @Excel(name = "身份证号信息")
    private String idCard;

    /**
     * 身份证号(md5)
     */
    private String idCardMd5;

    /**
     * 身份证实名校验:0.未校验,1.匹配,2.不匹配
     */
    private Integer idCardAudit;

    /**
     * 身份证实名认证接口:1.聚合,2.毫秒科技
     */
    private Integer auditApiType;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 生日
     */
    private Date birth;

    /**
     * 是否重复表单:0.否,1.是
     */
    private Integer isRepeated;

    /**
     * 落地页标签
     */
    private String landpageTag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    @Excel(name = "身份证信息校验状态")
    private String idCardAuditStr;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;
}
