package com.ruoyi.system.entity.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 活动皮肤对象 tb_activity_skin
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
@Data
public class ActivitySkin implements Serializable {
    private static final long serialVersionUID = 5060157124698409568L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 皮肤标识
     */
    private String skinCode;

    /**
     * 皮肤名称
     */
    private String skinName;

    /**
     * 活动皮肤类型:1.套猫,2.大转盘,3.卡包
     */
    private Integer skinType;

    /**
     * 活动类型:1.轻互动活动
     */
    private Integer activityType;

    /**
     * 缩略图
     */
    private String thumbnailImage;

    /**
     * 配置模板
     */
    private String jsTemplate;

    /**
     * 皮肤配置：全局配置，所有活动生效
     */
    private String globalConfig;

    /**
     * 皮肤配置：copy到活动，活动会自己修改
     */
    private String skinConfig;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 皮肤标识列表
     */
    private List<String> skinCodes;

    /**
     * 跳转路径
     */
    private String redirectPath;
}
