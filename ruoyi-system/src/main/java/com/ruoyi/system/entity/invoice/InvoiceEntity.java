package com.ruoyi.system.entity.invoice;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 发票表
 *
 * <AUTHOR>
 * @date 2022-10-20 14:33:50
 */
@Data
public class InvoiceEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 发票单号
     */
    private String invoiceNumber;

    /**
     * 发票金额
     */
    private Integer invoiceAmount;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 备注
     */
    private String remarkText;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;


}

