package com.ruoyi.system.entity.landpage.article;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 公众号阅读竞品文章
 *
 * <AUTHOR>
 * @date 2024-9-20 11:47:44
 */
@Data
public class ArticleCompetitorUrlEntity implements Serializable {
    private static final long serialVersionUID = 6050401432423446299L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 公众号名称
     */
    private String nickname;

    /**
     * 省份
     */
    private String provinceName;

    /**
     * 阅读数
     */
    private Integer read;

    /**
     * 来源链接
     */
    private String source;

    /**
     * 标题
     */
    private String title;

    /**
     * 公众号文章链接
     */
    private String url;

    /**
     * 文章发布时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

