package com.ruoyi.system.entity.landpage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 外部落地页日数据对象 tb_external_landpage_day_data
 *
 * <AUTHOR>
 * @date 2021-10-09
 */
public class ExternalLandpageDayData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /** 广告主ID */
    private Long advertiserId;

    /** 落地页ID */
    private String landpageId;

    /** 落地页曝光pv */
    private Integer lpExposurePv;

    /** 落地页曝光uv */
    private Integer lpExposureUv;

    /** 落地页点击pv */
    private Integer lpClickPv;

    /** 落地页点击uv */
    private Integer lpClickUv;

    /** 落地页曝光pv */
    private Integer lpExposurePvAdd;

    /** 落地页曝光uv */
    private Integer lpExposureUvAdd;

    /** 落地页点击pv */
    private Integer lpClickPvAdd;

    /** 落地页点击uv */
    private Integer lpClickUvAdd;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCurDate(Date curDate)
    {
        this.curDate = curDate;
    }

    public Date getCurDate()
    {
        return curDate;
    }
    public void setAdvertiserId(Long advertiserId)
    {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserId()
    {
        return advertiserId;
    }
    public void setLandpageId(String landpageId)
    {
        this.landpageId = landpageId;
    }

    public String getLandpageId()
    {
        return landpageId;
    }

    public Integer getLpExposurePv() {
        return lpExposurePv;
    }

    public void setLpExposurePv(Integer lpExposurePv) {
        this.lpExposurePv = lpExposurePv;
    }

    public Integer getLpExposureUv() {
        return lpExposureUv;
    }

    public void setLpExposureUv(Integer lpExposureUv) {
        this.lpExposureUv = lpExposureUv;
    }

    public Integer getLpClickPv() {
        return lpClickPv;
    }

    public void setLpClickPv(Integer lpClickPv) {
        this.lpClickPv = lpClickPv;
    }

    public Integer getLpClickUv() {
        return lpClickUv;
    }

    public void setLpClickUv(Integer lpClickUv) {
        this.lpClickUv = lpClickUv;
    }

    public Integer getLpExposurePvAdd() {
        return lpExposurePvAdd;
    }

    public void setLpExposurePvAdd(Integer lpExposurePvAdd) {
        this.lpExposurePvAdd = lpExposurePvAdd;
    }

    public Integer getLpExposureUvAdd() {
        return lpExposureUvAdd;
    }

    public void setLpExposureUvAdd(Integer lpExposureUvAdd) {
        this.lpExposureUvAdd = lpExposureUvAdd;
    }

    public Integer getLpClickPvAdd() {
        return lpClickPvAdd;
    }

    public void setLpClickPvAdd(Integer lpClickPvAdd) {
        this.lpClickPvAdd = lpClickPvAdd;
    }

    public Integer getLpClickUvAdd() {
        return lpClickUvAdd;
    }

    public void setLpClickUvAdd(Integer lpClickUvAdd) {
        this.lpClickUvAdd = lpClickUvAdd;
    }

    public void setGmtCreate(Date gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtModified(Date gmtModified)
    {
        this.gmtModified = gmtModified;
    }

    public Date getGmtModified()
    {
        return gmtModified;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("curDate", getCurDate())
            .append("advertiserId", getAdvertiserId())
            .append("landpageId", getLandpageId())
            .append("lpExposurePv", getLpExposurePv())
            .append("lpExposureUv", getLpExposureUv())
            .append("lpClickPv", getLpClickPv())
            .append("lpClickUv", getLpClickUv())
            .append("gmtCreate", getGmtCreate())
            .append("gmtModified", getGmtModified())
            .toString();
    }
}
