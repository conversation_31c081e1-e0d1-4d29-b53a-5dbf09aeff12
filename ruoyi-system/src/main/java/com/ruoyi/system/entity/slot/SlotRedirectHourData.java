package com.ruoyi.system.entity.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位投放数据对象 tb_slot_redirect_hour_data
 *
 * <AUTHOR>
 * @date 2022-04-28
 */
@Data
public class SlotRedirectHourData implements Serializable {
    private static final long serialVersionUID = -1723581959084050696L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 小时
     */
    private Integer curHour;

    /**
     * 广告位id
     */
    private Long slotId;

    /**
     * 投放类型
     */
    private Integer redirectType;

    /**
     * 投放信息
     */
    private String redirectValue;

    /**
     * 投放信息MD5
     */
    private String redirectValueMd5;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;
}
