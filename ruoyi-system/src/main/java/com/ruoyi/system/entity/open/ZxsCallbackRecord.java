package com.ruoyi.system.entity.open;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 摘星社回调记录
 *
 * <AUTHOR>
 * @date 2022-04-12
 */
@Data
public class ZxsCallbackRecord implements Serializable {
    private static final long serialVersionUID = -3616074937013442624L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单状态:1.成功,2.续订,8.退订,0.失败
     */
    private String status;

    /**
     * 计费手机
     */
    private String phone;

    /**
     * 价格，单位分
     */
    private String price;

    /**
     * 计费点
     */
    private String productId;

    /**
     * 透传参数
     */
    private String others;

    /**
     * 参数校验MD5加密串
     */
    private String sign;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}
