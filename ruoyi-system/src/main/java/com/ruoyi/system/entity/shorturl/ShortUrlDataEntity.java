package com.ruoyi.system.entity.shorturl;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短链数据表
 *
 * <AUTHOR>
 * @date 2022-10-8 14:16:45
 */
@Data
public class ShortUrlDataEntity implements Serializable {
    private static final long serialVersionUID = 6112365321544613688L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 短链ID
     */
    private Long shortUrlId;

    /**
     * 访问pv
     */
    private Integer requestPv;

    /**
     * 访问uv
     */
    private Integer requestUv;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

