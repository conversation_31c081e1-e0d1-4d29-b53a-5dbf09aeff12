package com.ruoyi.system.entity.pay;

import lombok.Data;

import java.util.Date;

/**
 * 微信支付订单表
 *
 * <AUTHOR>
 * @date 2023-1-29 17:38:06
 */
@Data
public class WxPayOrderEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 支付订单号(后端生成)
     */
    private String outTradeNo;

    /**
     * 落地页标识
     */
    private String landpageKey;

    /**
     * 诺禾订单号
     */
    private String nhOrderId;

    /**
     * 诺禾媒体ID
     */
    private Long nhAppId;

    /**
     * 诺禾广告位ID
     */
    private Long nhSlotId;

    /**
     * 诺禾用户ID
     */
    private Long nhConsumerId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 公众账号ID
     */
    private String appId;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 支付金额(分)
     */
    private Integer totalFee;

    /**
     * 支付IP
     */
    private String ip;

    /**
     * 下单referer
     */
    private String referer;

    /**
     * 下单userAgent
     */
    private String userAgent;

    /**
     * 支付状态:0.待支付,1.已支付
     */
    private Integer status;

    /**
     * 微信支付订单号
     */
    private String transactionId;

    /**
     * 用户标识
     */
    private String openid;

    /**
     * 付款银行
     */
    private String bankType;

    /**
     * 支付完成时间
     */
    private String timeEnd;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

