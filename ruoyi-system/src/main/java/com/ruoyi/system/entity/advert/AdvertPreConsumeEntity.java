package com.ruoyi.system.entity.advert;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告消耗预扣表
 *
 * <AUTHOR>
 * @date 2023-12-25 19:54:55
 */
@Data
public class AdvertPreConsumeEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告ID
     */
    private Long orientId;

    /**
     * 预扣的消耗(毫)
     */
    private Integer milliConsume;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

