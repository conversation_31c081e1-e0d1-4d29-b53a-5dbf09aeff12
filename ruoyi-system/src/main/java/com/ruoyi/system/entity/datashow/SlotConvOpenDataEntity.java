package com.ruoyi.system.entity.datashow;

import lombok.Data;

import java.util.Date;

/**
 * 媒体可见的转化数据表
 *
 * <AUTHOR>
 * @date 2023-3-10 11:10:04
 */
@Data
public class SlotConvOpenDataEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 转化
     */
    private Integer conv;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

