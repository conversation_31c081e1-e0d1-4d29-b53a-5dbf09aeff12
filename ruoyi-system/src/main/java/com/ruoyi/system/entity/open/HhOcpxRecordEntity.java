package com.ruoyi.system.entity.open;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 辉煌OCPX事件记录
 *
 * <AUTHOR>
 * @date 2024-9-18 17:40:48
 */
@Data
public class HhOcpxRecordEntity implements Serializable {
    private static final long serialVersionUID = 5370002463415805933L;

    /**
     * 点击数据上报时http的header中的user_agent
     */
    private String adAgent;

    /**
     * 广告主ID
     */
    private String aid;

    /**
     * 品牌，部分客户需要上报
     */
    private String brand;

    /**
     * 发生转化后的媒体回调地址
     */
    private String callbackUrl;

    /**
     * 广告计划ID
     */
    private String campaignId;

    /**
     * 链路编码，表明渠道侧和客户侧：示例009012，由辉煌明天提供
     */
    private String chainCode;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 转化事件jsonarray
     */
    private String eventList;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 主键id
     */
    private Long id;

    /**
     * iOS设备广告标识IDFA
     */
    private String idfa;

    /**
     * iOS设备广告标识IDFA的md5值小写
     */
    private String idfaMd5;

    /**
     * 安卓设备广告标识IMEI的md5值小写
     */
    private String imeiMd5;

    /**
     * IP
     */
    private String ip;

    /**
     * 机型，部分客户需要上报
     */
    private String model;

    /**
     * 安卓设备广告标识OAID
     */
    private String oaid;

    /**
     * 安卓设备广告标识OAID的md5值小写
     */
    private String oaidMd5;

    /**
     * 客户端操作系统类型:0.代表安卓,1.代表iOS
     */
    private Integer os;

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 广告点击时间
     */
    private Date tms;

    /**
     * 类型:1.曝光,2.点击
     */
    private Integer type;

    /**
     * 接口版本:v2
     */
    private String version;


}

