package com.ruoyi.system.entity.convert;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体转化上报规则
 *
 * <AUTHOR>
 * @date 2022-10-19 10:30:13
 */
@Data
public class ConvertUploadRuleEntity implements Serializable {
    private static final long serialVersionUID = -3333885460793594302L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 配置比例[0,100]
     */
    private Integer ratio;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人ID
     */
    private Long operatorId;

    /**
     * 修改时间
     */
    private Date operateTime;

    /**
     * 是否删除:0.未删除,1.删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

