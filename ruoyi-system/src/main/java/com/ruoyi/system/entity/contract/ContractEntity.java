package com.ruoyi.system.entity.contract;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 合同表
 *
 * <AUTHOR>
 * @date 2022-11-3 17:14:28
 */
@Data
public class ContractEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 甲方主体
     */
    private String planAName;

    /**
     * 乙方主体账号id
     */
    private Long accountId;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 合同有效期开始时间
     */
    private Date startDate;

    /**
     * 合同有效期结束时间
     */
    private Date endDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;


}

