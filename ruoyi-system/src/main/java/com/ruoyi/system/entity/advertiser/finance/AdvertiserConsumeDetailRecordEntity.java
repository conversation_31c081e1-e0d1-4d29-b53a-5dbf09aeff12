package com.ruoyi.system.entity.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主消费记录明细表
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@Data
public class AdvertiserConsumeDetailRecordEntity implements Serializable {
    private static final long serialVersionUID = -7211368150281878828L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 广告主ID
     */
    private Long accountId;

    /**
     * 消费类型:0.默认,1.表单自动分配,2.表单手动分配
     */
    private Integer consumeType;

    /**
     * 消费金额(分)
     */
    private Integer consumeAmount;

    /**
     * 落地页表单ID
     */
    private Long recordId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否扣费:0.未扣费,1.已扣费
     */
    private Integer isDone;

    /**
     * 扣费时间
     */
    private Date doneTime;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

