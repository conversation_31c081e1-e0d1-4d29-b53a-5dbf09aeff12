package com.ruoyi.system.entity.ai;

import lombok.Data;

import java.util.Date;

/**
 * ai 留资
 * <AUTHOR>
 * @date 2024/1/2 14:43
 */
@Data
public class AiLiuziEntity {
    /**
     * id
     */
    private Long id;
    /**
     * 姓名/公司名称
     */
    private String userName;
    /**
     * 用户号码
     */
    private String phone;
    /**
     * 使用场景
     */
    private String useScene;
    /**
     * 状态 0-未处理 1-有效 2-无效
     */
    private Integer status;
    /**
     * 操作人
     */
    private String operatorName;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;


}
