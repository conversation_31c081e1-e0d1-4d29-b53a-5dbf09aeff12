package com.ruoyi.system.entity.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 活动工具对象 tb_activity
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
@Data
public class Activity implements Serializable {
    private static final long serialVersionUID = 7837494045533120420L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 皮肤标识
     */
    private String skinCode;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 是否自动出券:0.否,1.是
     */
    private Integer autoJoin;

    /**
     * 活动参与次数
     */
    private Integer joinTimes;

    /**
     * 奖品列表
     */
    private String prizes;

    /**
     * 规则说明
     */
    private String ruleDesc;

    /**
     * ICP备案号
     */
    private String icpNo;

    /**
     * 配置模板
     */
    private String jsTemplate;

    /**
     * 创建人ID
     */
    private Long operatorId;

    /**
     * 创建人姓名
     */
    private String operatorName;
    /**
     * 客服号码信息
     */
    private String customerConfig;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 活动ID列表
     */
    private List<Long> ids;
}
