package com.ruoyi.system.entity.landpage;

import lombok.Data;

import java.util.Date;

/**
 * 企微囤粉表单记录
 *
 * <AUTHOR>
 * @date 2023-9-22 15:38:47
 */
@Data
public class QwtfLandpageFormRecordEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * IP
     */
    private String ip;

    /**
     * 落地页
     */
    private String landpageUrl;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 发送短信数
     */
    private Integer smsTimes;

    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * 企微好友状态
     * @see com.ruoyi.common.enums.QwtfFriendStatusEnum
     */
    private Integer friendStatus;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

