package com.ruoyi.system.entity.common;

import lombok.Data;

import java.util.Date;

/**
 * 文档表对象
 *
 * <AUTHOR>
 * @date 2022-12-1 17:08:32
 */
@Data
public class DocumentEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 文档名称
     */
    private String documentName;

    /**
     * 文档地址
     */
    private String documentUrl;

    /**
     * 文档类型:1.内部文档,2.外部文档
     */
    private Integer documentType;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

