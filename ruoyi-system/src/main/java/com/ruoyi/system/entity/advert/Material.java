package com.ruoyi.system.entity.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告素材对象 tb_material
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@Data
public class Material implements Serializable {
    private static final long serialVersionUID = 4892537673228441043L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告标题
     */
    private String advertTitle;

    /**
     * 素材图链接
     */
    private String materialImg;

    /**
     * 按钮文案
     */
    private String buttonText;

    /**
     * 弹层类型:1.普通弹层,2.创新弹层
     */
    private Integer layerType;

    /**
     * 弹层ID
     */
    private Long layerId;

    /**
     * 是否默认素材:0.否,1.是
     */
    private Integer isDefault;

    /**
     * 权重,默认为1
     */
    private Integer weight;

    /**
     * 状态:0.默认,1.已屏蔽
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;
}
