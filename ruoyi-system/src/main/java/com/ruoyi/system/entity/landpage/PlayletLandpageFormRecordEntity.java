package com.ruoyi.system.entity.landpage;

import com.ruoyi.common.enums.landpage.PlayletTradeStatus;
import lombok.Data;

import java.util.Date;

/**
 * 短剧落地页表单记录
 *
 * <AUTHOR>
 * @date 2023-8-2 16:11:47
 */
@Data
public class PlayletLandpageFormRecordEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 落地页
     */
    private String landpageUrl;

    /**
     * 支付订单号
     */
    private String tradeNo;

    /**
     * 支付金额(分)
     */
    private Integer tradeAmount;

    /**
     * 支付时间
     */
    private Date tradeTime;

    /**
     * 支付状态
     * @see PlayletTradeStatus
     */
    private Integer tradeStatus;

    /**
     * 微信交易单号
     */
    private String transactionId;

    /**
     * 订单OpenId
     */
    private String openid;

    /**
     * IP
     */
    private String ip;

    /**
     * 支付通道:1.微信,2.支付宝
     */
    private Integer payPlatform;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

