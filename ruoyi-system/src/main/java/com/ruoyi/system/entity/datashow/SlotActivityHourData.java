package com.ruoyi.system.entity.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 广告位活动维度时段数据
 *
 * <AUTHOR>
 * @date 2021-08-17
 */
@Data
public class SlotActivityHourData {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 小时
     */
    private Integer curHour;

    /**
     * 账号Id
     */
    private Long accountId;

    /**
     * 媒体Id
     */
    private Long appId;

    /**
     * 广告位Id
     */
    private Long slotId;

    /**
     * 活动Id
     */
    private Long activityId;

    /**
     * 活动访问pv
     */
    private Integer activityRequestPv;

    /**
     * 活动访问uv
     */
    private Integer activityRequestUv;

    /**
     * 活动参与pv
     */
    private Integer joinPv;

    /**
     * 活动参与uv
     */
    private Integer joinUv;

    /**
     * 券请求
     */
    private Integer adRequest;

    /**
     * 发券
     */
    private Integer adLaunch;

    /**
     * 券曝光Pv
     */
    private Integer adExposurePv;

    /**
     * 券曝光Uv
     */
    private Integer adExposureUv;

    /**
     * 券点击Pv
     */
    private Integer adClickPv;

    /**
     * 券点击Uv
     */
    private Integer adClickUv;

    /**
     * 广告消耗(分)
     */
    private Integer consume;

    /**
     * 落地页曝光Pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光Uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页点击Pv
     */
    private Integer lpClickPv;

    /**
     * 落地页点击Uv
     */
    private Integer lpClickUv;

    /**
     * 理论扣费(产生转化时的考核成本)
     */
    private Integer theoryCost;

    /**
     * 活动访问pv增量
     */
    private Integer activityRequestPvAdd;

    /**
     * 活动访问uv增量
     */
    private Integer activityRequestUvAdd;

    /**
     * 活动参与pv增量
     */
    private Integer joinPvAdd;

    /**
     * 活动参与uv增量
     */
    private Integer joinUvAdd;

    /**
     * 券请求增量
     */
    private Integer adRequestAdd;

    /**
     * 发券增量
     */
    private Integer adLaunchAdd;

    /**
     * 券曝光增量Pv
     */
    private Integer adExposurePvAdd;

    /**
     * 券曝光增量Uv
     */
    private Integer adExposureUvAdd;

    /**
     * 券点击增量Pv
     */
    private Integer adClickPvAdd;

    /**
     * 券点击增量Uv
     */
    private Integer adClickUvAdd;

    /**
     * 广告消耗增量
     */
    private Integer consumeAdd;

    /**
     * 落地页曝光增量Pv
     */
    private Integer lpExposurePvAdd;

    /**
     * 落地页曝光增量Uv
     */
    private Integer lpExposureUvAdd;

    /**
     * 落地页点击增量Pv
     */
    private Integer lpClickPvAdd;

    /**
     * 落地页点击增量Uv
     */
    private Integer lpClickUvAdd;

    /**
     * 理论扣费增量
     */
    private Integer theoryCostAdd;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 账号ID列表
     */
    private List<Long> accountIds;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;
}
