package com.ruoyi.system.entity.portal;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 官网发布信息表
 *
 * <AUTHOR>
 * @date 2022-9-27 13:58:32
 */
@Data
public class PortalPublishEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 用户id
      */
    private Long accountId;

    /**
     * 角色
     */
    private Integer type;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布内容
     */
    private String content;
    /**
     * 图片
     */
    private String img;

    /**
     * 单价
     */
    private Integer price;

    /**
     * 联系人
     */
    private String contacts;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;


}

