package com.ruoyi.system.entity.landpage;

import lombok.Data;

import java.util.Date;

/**
 * 外部落地页表单上报记录
 *
 * <AUTHOR>
 * @date 2023-4-11 15:38:53
 */
@Data
public class ExternalLandpageFormSendRecordEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 回传日期
     */
    private Date curDate;

    /**
     * 表单ID
     */
    private Long recordId;

    /**
     * 外部表单编号
     */
    private String externalNo;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 上报链接
     */
    private String url;

    /**
     * 上报结果
     */
    private String resp;

    /**
     * 是否成功:0.失败,1.成功
     */
    private Integer isSuccess;

    /**
     * 客服公司ID
     */
    private Long operAccountId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

