package com.ruoyi.system.entity.plugin;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 插件对象 tb_plugin
 *
 * <AUTHOR>
 * @date 2021-12-29
 */
@Data
public class Plugin implements Serializable {
    private static final long serialVersionUID = -5843030320982661864L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 插件类型
     * @see com.ruoyi.common.enums.plugin.PluginTypeEnum
     */
    private Integer pluginType;

    /**
     * 插件信息
     */
    private String pluginInfo;

    /**
     * 是否删除(0.未删除,1.已删除)
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
