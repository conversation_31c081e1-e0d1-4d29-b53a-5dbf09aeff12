package com.ruoyi.system.entity.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 广告配置分时段消耗数据对象 tb_orient_hour_consume_data
 * 
 * <AUTHOR>
 * @date 2021-08-30
 */
public class OrientHourConsumeData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /** 小时 */
    @Excel(name = "小时")
    private Integer curHour;

    /** 广告ID */
    @Excel(name = "广告ID")
    private Long advertId;

    /** 配置ID */
    @Excel(name = "配置ID")
    private Long orientId;

    /** 消耗(分) */
    @Excel(name = "消耗(分)")
    private Integer consume;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;

    /** 消耗增量(分) */
    private Integer consumeAdd;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCurDate() {
        return curDate;
    }

    public void setCurDate(Date curDate) {
        this.curDate = curDate;
    }

    public Integer getCurHour() {
        return curHour;
    }

    public void setCurHour(Integer curHour) {
        this.curHour = curHour;
    }

    public Long getAdvertId() {
        return advertId;
    }

    public void setAdvertId(Long advertId) {
        this.advertId = advertId;
    }

    public Long getOrientId() {
        return orientId;
    }

    public void setOrientId(Long orientId) {
        this.orientId = orientId;
    }

    public Integer getConsume() {
        return consume;
    }

    public void setConsume(Integer consume) {
        this.consume = consume;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getConsumeAdd() {
        return consumeAdd;
    }

    public void setConsumeAdd(Integer consumeAdd) {
        this.consumeAdd = consumeAdd;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("curDate", getCurDate())
            .append("curHour", getCurHour())
            .append("advertId", getAdvertId())
            .append("orientId", getOrientId())
            .append("consume", getConsume())
            .append("gmtCreate", getGmtCreate())
            .append("gmtModified", getGmtModified())
            .toString();
    }
}
