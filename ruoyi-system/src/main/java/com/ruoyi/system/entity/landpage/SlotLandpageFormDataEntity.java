package com.ruoyi.system.entity.landpage;

import lombok.Data;

import java.util.Date;

/**
 * 广告位日维度落地页表单数据表
 *
 * <AUTHOR>
 * @date 2023-2-20 10:45:27
 */
@Data
public class SlotLandpageFormDataEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 表单数量
     */
    private Integer formCount;

    /**
     * 表单消耗(分)
     */
    private Integer formConsume;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 表单数量增量
     */
    private Integer formCountAdd;

    /**
     * 表单消耗增量
     */
    private Integer formConsumeAdd;
}
