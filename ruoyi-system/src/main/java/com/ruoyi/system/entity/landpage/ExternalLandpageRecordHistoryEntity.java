package com.ruoyi.system.entity.landpage;

import lombok.Data;

import java.util.Date;

/**
 * 外部落地页表单修改历史
 *
 * <AUTHOR>
 * @date 2023-4-11 15:38:42
 */
@Data
public class ExternalLandpageRecordHistoryEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 客服公司ID
     */
    private Long operAccountId;

    /**
     * 原表单ID
     */
    private Long originRecordId;

    /**
     * 外部表单编号
     */
    private String externalNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区/县
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

