package com.ruoyi.system.entity.advert;

import lombok.Data;

import java.io.Serializable;

/**
 * 创新弹层信息
 *
 * <AUTHOR>
 * @date 2021-12-22
 */
@Data
public class InnovateLayerInfo implements Serializable {
    private static final long serialVersionUID = 1792788254200723891L;

    /**
     * 默认牌子(翻牌子)
     */
    private String defaultCardImg;

    /**
     * 利益点卡片(翻牌子)
     */
    private String benefitCardImg;

    /**
     * 谢谢参与(翻牌子)
     */
    private String thanksImg;

    /**
     * 深拷贝
     */
    public InnovateLayerInfo copy() {
        InnovateLayerInfo copy = new InnovateLayerInfo();
        copy.defaultCardImg = this.defaultCardImg;
        copy.benefitCardImg = this.benefitCardImg;
        copy.thanksImg = this.thanksImg;
        return copy;
    }
}
