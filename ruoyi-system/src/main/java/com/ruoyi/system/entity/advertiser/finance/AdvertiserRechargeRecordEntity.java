package com.ruoyi.system.entity.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主充值记录表
 *
 * <AUTHOR>
 * @date 2022-3-18 17:57:43
 */
@Data
public class AdvertiserRechargeRecordEntity implements Serializable {
    private static final long serialVersionUID = 2718951195511841048L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 广告主账号ID
     */
    private Long accountId;

    /**
     * 资金来源账号ID(划账使用)
     */
    private Long sourceAccountId;

    /**
     * 充值类型
     * @see com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum
     */
    private Integer rechargeType;

    /**
     * 充值金额(分)
     */
    private Integer rechargeAmount;

    /**
     * 申请人ID
     */
    private Long operatorId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer auditStatus;

    /**
     * 审核理由
     */
    private String auditReason;

    /**
     * 审核人ID
     */
    private Long auditorId;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

