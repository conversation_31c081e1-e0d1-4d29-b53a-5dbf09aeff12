package com.ruoyi.system.entity.oa.permission;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 权限表
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:18
 */
@Data
public class PermissionEntity implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 权限标识
     */
    private String permissionKey;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 父级权限id
     */
    private Long parentId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

