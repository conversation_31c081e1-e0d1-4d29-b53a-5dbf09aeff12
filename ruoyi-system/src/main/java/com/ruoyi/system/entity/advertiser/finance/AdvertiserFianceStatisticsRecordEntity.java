package com.ruoyi.system.entity.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主财务汇总记录表
 *
 * <AUTHOR>
 * @date 2022-3-18 17:58:27
 */
@Data
public class AdvertiserFianceStatisticsRecordEntity implements Serializable {
    private static final long serialVersionUID = 8800076508339289349L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 广告主账号ID
     */
    private Long accountId;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 充值金额(分)
     */
    private Integer rechargeAmount;

    /**
     * 消费金额(分)
     */
    private Integer consumeAmount;

    /**
     * 账户总余额(现金+返货)(分)
     */
    private Integer balanceAmount;

    /**
     * 账户现金余额(分)
     */
    private Integer cashBalance;

    /**
     * 账户返货余额(分)
     */
    private Integer rebateBalance;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

