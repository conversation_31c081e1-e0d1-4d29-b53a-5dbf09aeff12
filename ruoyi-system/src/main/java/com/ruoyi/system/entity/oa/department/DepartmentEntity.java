package com.ruoyi.system.entity.oa.department;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * oa部门表
 *
 * <AUTHOR>
 * @date 2021-10-9 11:30:37
 */
@Data
public class DepartmentEntity implements Serializable {
    private static final long serialVersionUID = 8815353789370039809L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门标识
     */
    private String departmentKey;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

