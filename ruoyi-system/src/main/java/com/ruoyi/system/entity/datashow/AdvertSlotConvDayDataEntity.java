package com.ruoyi.system.entity.datashow;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 广告广告位维度后端转化日数据表
 *
 * <AUTHOR>
 * @date 2023-2-9 10:54:16
 */
@Data
public class AdvertSlotConvDayDataEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 转化类型:1.表单提交,2.支付/充值,3.退款,4.注册/激活,5.发货,6.投诉
     */
    private Integer convType;

    /**
     * 转化数PV
     */
    private Integer convPv;

    /**
     * 转化数UV
     */
    private Integer convUv;

    /**
     * 转化金额
     */
    private BigDecimal convPrice;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 转化数PV增量
     */
    private Integer convPvAdd;

    /**
     * 转化数UV增量
     */
    private Integer convUvAdd;

    /**
     * 转化金额增量
     */
    private BigDecimal convPriceAdd;
}

