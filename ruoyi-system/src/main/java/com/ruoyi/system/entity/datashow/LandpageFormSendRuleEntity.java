package com.ruoyi.system.entity.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 落地页表单上报规则
 *
 * <AUTHOR>
 * @date 2021-12-23
 */
@Data
public class LandpageFormSendRuleEntity implements Serializable {
    private static final long serialVersionUID = 8426490241336688019L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 地域
     */
    private String area;

    /**
     * 年龄下限(包含)
     */
    private Integer ageMin;

    /**
     * 年龄上限(包含)
     */
    private Integer ageMax;

    /**
     * 每日表单数量上限
     */
    private Integer dailyLimit;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

