package com.ruoyi.system.entity.shorturl;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短链表
 *
 * <AUTHOR>
 * @date 2022-10-8 14:16:35
 */
@Data
public class ShortUrlEntity implements Serializable {
    private static final long serialVersionUID = -1409969278135037715L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 类型:1.普通链接
     */
    private Integer urlType;

    /**
     * 状态:0.正常,1.禁用,2.删除
     */
    private Integer urlStatus;

    /**
     * 原链接
     */
    private String originUrl;

    /**
     * 原链接MD5
     */
    private String originUrlMd5;

    /**
     * 短链(用于列表查询)
     */
    private String shortUrl;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

