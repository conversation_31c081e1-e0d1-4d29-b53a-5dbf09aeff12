package com.ruoyi.system.entity.advertiser;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * DSP广告主消费记录
 *
 * <AUTHOR>
 * @date 2022-7-13 17:30:15
 */
@Data
public class DspAdvertiserConsumeRecordEntity implements Serializable {
    private static final long serialVersionUID = -482184154623807553L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 计费点击pv
     */
    private Integer billingClickPv;

    /**
     * 计费点击uv
     */
    private Integer billingClickUv;

    /**
     * 消费金额(分)
     */
    private Integer consumeAmount;

    /**
     * 是否广告主可见(0.不可见,1.可见)
     */
    private Integer isVisible;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

