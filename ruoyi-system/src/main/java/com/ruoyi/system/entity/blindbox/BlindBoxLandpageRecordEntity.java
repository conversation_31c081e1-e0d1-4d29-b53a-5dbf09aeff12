package com.ruoyi.system.entity.blindbox;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 盲盒落地页提交记录表
 *
 * <AUTHOR>
 * @date 2022-6-13 11:10:16
 */
@Data
public class BlindBoxLandpageRecordEntity implements Serializable {
    private static final long serialVersionUID = -810586473116588463L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 落地页标识
     */
    private String landpageKey;

    /**
     * 来源链接
     */
    private String referer;

    /**
     * IP
     */
    private String ip;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

