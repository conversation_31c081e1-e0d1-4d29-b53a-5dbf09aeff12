package com.ruoyi.system.entity.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务日期表对象 tb_inner_log
 *
 * <AUTHOR>
 * @date 2022-03-28
 */
@Data
public class InnerLogEntity implements Serializable {
    private static final long serialVersionUID = -7907126138952843659L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 消息Key
     */
    private String msgKey;

    /**
     * 日志类型
     */
    private Integer type;

    /**
     * 日志类型描述
     */
    private String typeDesc;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 设备号
     */
    private String deviceId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 素材ID
     */
    private Long materialId;

    /**
     * 插件ID
     */
    private Long pluginId;

    /**
     * 行政区划代码
     */
    private String areaNum;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 运营商
     */
    private String isp;

    /**
     * IP
     */
    private String ip;

    /**
     * 广告位访问标识
     */
    private String srid;

    /**
     * 操作系统类型:iOS/Android/Windows
     */
    private String osType;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * 手机品牌
     */
    private String mobileBrand;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * referer
     */
    private String referer;

    /**
     * userAgent
     */
    private String userAgent;

    /**
     * 单价(分)
     */
    private Integer unitPrice;

    /**
     * 转化类型
     * @see com.ruoyi.common.enums.advert.ConvType
     */
    private Integer convType;

    /**
     * 考核指标
     */
    private Integer assessType;

    /**
     * 考核成本(分)
     */
    private Integer assessCost;

    /**
     * 自定义行为
     */
    private Integer action;

    /**
     * 发券次序
     */
    private Integer launchSeq;

    /**
     * 预估CTR
     */
    private Double pCtr;

    /**
     * 预估CVR
     */
    private Double pCvr;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 表后缀
     */
    private String tbSuffix;
}
