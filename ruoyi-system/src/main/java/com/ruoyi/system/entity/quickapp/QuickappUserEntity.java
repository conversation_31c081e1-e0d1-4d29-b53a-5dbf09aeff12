package com.ruoyi.system.entity.quickapp;

import lombok.Data;

import java.io.Serializable;
    import java.util.Date;

    /**
    * 快应用用户表
    * <AUTHOR>
    * @date 2022-8-8 10:57:00
    */
@Data
public class QuickappUserEntity implements Serializable {

        /**
        * 主键id
        */
    private Long id;

        /**
        * 用户名称
        */
    private String userName;

        /**
        * 用户头像
        */
    private String userAvatar;

        /**
        * 邮箱
        */
    private String email;

        /**
        * 密码
        */
    private String passwd;

        /**
        * 创建时间
        */
    private Date gmtCreate;

        /**
        * 修改时间
        */
    private Date gmtModified;


}

