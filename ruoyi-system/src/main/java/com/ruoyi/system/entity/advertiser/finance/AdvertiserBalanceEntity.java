package com.ruoyi.system.entity.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主账户余额表
 *
 * <AUTHOR>
 * @date 2022-3-18 17:57:16
 */
@Data
public class AdvertiserBalanceEntity implements Serializable {
    private static final long serialVersionUID = -3975110469716093857L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 广告主账号ID
     */
    private Long accountId;

    /**
     * 总金额
     */
    private Integer totalAmount;

    /**
     * 现金余额(分)
     */
    private Integer cashAmount;

    /**
     * 返货余额(分)
     */
    private Integer rebateAmount;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

