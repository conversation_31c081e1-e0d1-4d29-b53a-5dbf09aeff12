package com.ruoyi.system.entity.datashow;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 企微囤粉落地页导出记录
 *
 * <AUTHOR>
 * @date 2023-09-22
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class QwtfLandpageFormRecordExcel implements Serializable {
    private static final long serialVersionUID = -8328316197816126984L;

    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;

    /**
     * 广告ID
     */
    @ExcelProperty("广告ID")
    private Long advertId;

    /**
     * 广告名称
     */
    @ExcelProperty("广告名称")
    private String advertName;

    /**
     * 落地页链接
     */
    @ExcelProperty("落地页链接")
    private String landpageUrl;

    /**
     * 媒体名称
     */
    @ExcelProperty("媒体名称")
    private String appName;

    /**
     * 媒体ID
     */
    @ExcelProperty("媒体ID")
    private Long appId;

    /**
     * 广告位ID
     */
    @ExcelProperty("广告位ID")
    private Long slotId;

    /**
     * 广告位名称
     */
    @ExcelProperty("广告位名称")
    private String slotName;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String phone;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("提交时间")
    private Date gmtCreate;

//    /**
//     * 已发短信数
//     */
//    @ExcelProperty("已发短信数")
//    private Integer smsTimes;

    /**
     * 加好友状态
     * @see com.ruoyi.common.enums.QwtfFriendStatusEnum
     */
    @ExcelProperty("加好友状态")
    private String friendStatusStr;
}
