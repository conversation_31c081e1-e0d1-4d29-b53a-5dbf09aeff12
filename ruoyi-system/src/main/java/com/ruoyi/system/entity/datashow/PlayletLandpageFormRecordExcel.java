package com.ruoyi.system.entity.datashow;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.utils.excel.FenToYuanConverter;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短剧落地页表单导出记录
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class PlayletLandpageFormRecordExcel implements Serializable {
    private static final long serialVersionUID = 3134184128301141514L;

    @ExcelIgnore
    private Long id;

    @ExcelProperty("广告主ID")
    private Long advertiserId;

    @ExcelProperty("广告主名称")
    private String advertiserName;

    @ExcelProperty("广告ID")
    private Long advertId;

    @ExcelProperty("广告名称")
    private String advertName;

    @ExcelProperty("媒体ID")
    private Long appId;

    @ExcelProperty("媒体名称")
    private String appName;

    @ExcelProperty("广告位ID")
    private Long slotId;

    @ExcelProperty("广告位名称")
    private String slotName;

    @ExcelProperty("落地页链接")
    private String landpageUrl;

    @ExcelProperty(value = "订单金额", converter = FenToYuanConverter.class)
    private Integer tradeAmount;

    @ExcelProperty("订单状态")
    private String tradeStatusStr;

    @ExcelProperty("订单OpenId")
    private String openid;

    @ExcelProperty("交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date tradeTime;

    @ExcelProperty("支付通道")
    private String payPlatform;
}
