package com.ruoyi.system.entity.landpage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 落地页单完整记录对象
 *
 * <AUTHOR>
 * @date 2022-02-08
 */
@Data
public class LandpageFormFullRecord implements Serializable {
    private static final long serialVersionUID = 1108684934862830929L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 运营商
     */
    private String isp;

    /**
     * 上报广告主
     */
    private Long targetAdvertiserId;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date callbackTime;

    /**
     * 上报状态
     */
    private String callbackStatus;

    /**
     * 分配状态
     * @see com.ruoyi.common.enums.landpage.AssignStatus
     */
    private Integer assignStatus;

    /**
     * 上报结果
     */
    private String resp;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 来源链接
     */
    private String referer;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区/县
     */
    private String district;

    /**
     * 行政区划代码
     */
    private String areaNum;

    /**
     * 详细信息
     */
    private String address;

    /**
     * IP
     */
    private String ip;

    /**
     * 身份证号(加密)
     */
    private String idCard;

    /**
     * 身份证号(md5)
     */
    private String idCardMd5;

    /**
     * 身份证实名校验:0.未校验,1.校验通过
     */
    private Integer idCardAudit;

    /**
     * 身份证实名认证接口:1.聚合,2.毫秒科技
     */
    private Integer auditApiType;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 生日
     */
    private Date birth;

    /**
     * 是否重复表单:0.否,1.是
     */
    private Integer isRepeated;

    /**
     * 是否调用成功:0.否,1.是
     */
    private Integer isSuccess;

    /**
     * 落地页标签
     */
    private String landpageTag;

    /**
     * 表单价格(分)
     */
    private Integer formPrice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分配人ID
     */
    private Long operatorId;

    /**
     * 表单提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 表单修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 身份证审核状态
     */
    private String idCardAuditStr;

    /**
     * 身份证实名接口提供方
     */
    private String auditApiTypeStr;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;

    /**
     * 分配日期起始
     */
    private Date cbStartDate;

    /**
     * 分配日期截止
     */
    private Date cbEndDate;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 地域列表
     */
    private List<String> areaList;

    /**
     * 起始生日
     */
    private Date startBirth;

    /**
     * 结束生日
     */
    private Date endBirth;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;
}
