package com.ruoyi.system.entity.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主结算分类消费记录表
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Data
public class AdvertiserConsumeCategoryRecordEntity implements Serializable {
    private static final long serialVersionUID = 52021087069487563L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 广告主账号ID
     */
    private Long accountId;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 结算指标类型
     * @see com.ruoyi.common.enums.advert.AdvertCostBillingTypeEnum
     */
    private Integer billingType;

    /**
     * 消费金额(分)
     */
    private Integer consumeAmount;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

