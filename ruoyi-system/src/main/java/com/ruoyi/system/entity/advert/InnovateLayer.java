package com.ruoyi.system.entity.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 创新弹层对象 tb_innovate_layer
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
@Data
public class InnovateLayer implements Serializable {
    private static final long serialVersionUID = 8005393698923571206L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 弹层名称
     */
    private String layerName;

    /**
     * 弹层皮肤编号
     */
    private String skinCode;

    /**
     * 背景图
     */
    private String bgImg;

    /**
     * 轮播GIF图
     */
    private String gifImg;

    /**
     * 弹层信息
     */
    private String layerInfo;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 是否删除:0.未删除,1.已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;
}
