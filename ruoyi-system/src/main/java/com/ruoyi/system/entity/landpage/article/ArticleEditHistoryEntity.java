package com.ruoyi.system.entity.landpage.article;

import lombok.Data;

import java.util.Date;

/**
 * 文章修改记录表
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:41
 */
@Data
public class ArticleEditHistoryEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 文章ID
     */
    private Long articleId;

    /**
     * 文章名称
     */
    private String name;

    /**
     * 目标阅读量
     */
    private Integer targetRequestPv;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

