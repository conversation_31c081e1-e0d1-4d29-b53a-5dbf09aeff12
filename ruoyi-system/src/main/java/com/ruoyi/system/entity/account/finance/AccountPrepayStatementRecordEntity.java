package com.ruoyi.system.entity.account.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体预付款结算记录
 *
 * <AUTHOR>
 * @date 2022-7-29 14:45:13
 */
@Data
public class AccountPrepayStatementRecordEntity implements Serializable {
    private static final long serialVersionUID = -3259587768300137884L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 媒体账号ID
     */
    private Long accountId;

    /**
     * 预付款记录ID
     */
    private Long prepayRecordId;

    /**
     * 结算单ID
     */
    private Long statementId;

    /**
     * 结算金额(分)
     */
    private Integer amount;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

