package com.ruoyi.system.entity.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 广告位数据对象 tb_slot_hour_data
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public class SlotHourData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /** 小时 */
    private Integer curHour;

    /** 账号id */
    private Long accountId;

    /** 媒体id */
    private Long appId;

    /** 广告位id */
    private Long slotId;

    /** 广告位访问pv */
    private Integer slotRequestPv;

    /** 广告位访问uv */
    private Integer slotRequestUv;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /** 广告位访问pv增量 */
    private Integer slotRequestPvAdd;

    /** 广告位访问uv增量 */
    private Integer slotRequestUvAdd;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCurDate() {
        return curDate;
    }

    public void setCurDate(Date curDate) {
        this.curDate = curDate;
    }

    public Integer getCurHour() {
        return curHour;
    }

    public void setCurHour(Integer curHour) {
        this.curHour = curHour;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getSlotId() {
        return slotId;
    }

    public void setSlotId(Long slotId) {
        this.slotId = slotId;
    }

    public Integer getSlotRequestPv() {
        return slotRequestPv;
    }

    public void setSlotRequestPv(Integer slotRequestPv) {
        this.slotRequestPv = slotRequestPv;
    }

    public Integer getSlotRequestUv() {
        return slotRequestUv;
    }

    public void setSlotRequestUv(Integer slotRequestUv) {
        this.slotRequestUv = slotRequestUv;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getSlotRequestPvAdd() {
        return slotRequestPvAdd;
    }

    public void setSlotRequestPvAdd(Integer slotRequestPvAdd) {
        this.slotRequestPvAdd = slotRequestPvAdd;
    }

    public Integer getSlotRequestUvAdd() {
        return slotRequestUvAdd;
    }

    public void setSlotRequestUvAdd(Integer slotRequestUvAdd) {
        this.slotRequestUvAdd = slotRequestUvAdd;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("curDate", getCurDate())
            .append("curHour", getCurHour())
            .append("accountId", getAccountId())
            .append("appId", getAppId())
            .append("slotId", getSlotId())
            .append("slotRequestPv", getSlotRequestPv())
            .append("slotRequestUv", getSlotRequestUv())
            .append("gmtCreate", getGmtCreate())
            .append("gmtModified", getGmtModified())
            .toString();
    }
}
