package com.ruoyi.system.entity.datashow;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 广告广告位维度后端转化时段数据表
 *
 * <AUTHOR>
 * @date 2023-2-9 10:55:35
 */
@Data
public class AdvertSlotConvHourDataEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 小时
     */
    private Integer curHour;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 转化类型
     * @see com.ruoyi.common.enums.advert.ConvType
     */
    private Integer convType;

    /**
     * 转化数PV
     */
    private Integer convPv;

    /**
     * 转化数UV
     */
    private Integer convUv;

    /**
     * 转化金额
     */
    private BigDecimal convPrice;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 转化数PV增量
     */
    private Integer convPvAdd;

    /**
     * 转化数UV增量
     */
    private Integer convUvAdd;

    /**
     * 转化金额增量
     */
    private BigDecimal convPriceAdd;
}

