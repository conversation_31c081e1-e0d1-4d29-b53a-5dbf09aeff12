package com.ruoyi.system.entity.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位规格对象 tb_slot_spec
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Data
public class SlotSpec implements Serializable {
    private static final long serialVersionUID = -1136628123653680221L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 长(单位:像素)
     */
    private String length;

    /**
     * 宽(单位:像素)
     */
    private String width;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;
}
