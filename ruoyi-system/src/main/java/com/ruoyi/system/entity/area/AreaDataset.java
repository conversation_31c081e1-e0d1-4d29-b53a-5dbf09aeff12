package com.ruoyi.system.entity.area;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 行政区划代码对象 tb_area_dataset
 *
 * <AUTHOR>
 * @date 2021-08-31
 */
@Data
public class AreaDataset implements Serializable {
    private static final long serialVersionUID = 1947152659447708741L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 行政区划代码
     */
    private String areaNum;

    /**
     * 类型:1.省,2.市,3.区/县
     */
    private Integer areaType;

    /**
     * 名称
     */
    private String areaName;

    /**
     * 上级行政区划代码
     */
    private String parentNum;

    /**
     * 上级行政区划代码列表
     */
    private List<String> parentNums;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 地域类型列表
     */
    private List<Integer> areaTypes;

    /**
     * 地域名称列表
     */
    private List<String> areaNames;
}
