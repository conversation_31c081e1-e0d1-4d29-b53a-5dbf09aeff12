package com.ruoyi.system.entity.landpage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 外部优惠券发放记录表
 *
 * <AUTHOR>
 * @date 2024-4-18 14:07:03
 */
@Data
public class ExternalCouponRecordEntity implements Serializable {
    private static final long serialVersionUID = 6896322422710093872L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 券类型:1.捷停车
     */
    private Integer couponType;

    /**
     * 券信息
     */
    private String couponInfo;

    /**
     * 发券请求参数
     */
    private String couponParam;

    /**
     * 发券响应参数
     */
    private String couponResp;

    /**
     * 券请求唯一id
     */
    private String couponRequestId;

    /**
     * 券状态:0.未发放,1.发放成功
     */
    private Integer couponStatus;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

