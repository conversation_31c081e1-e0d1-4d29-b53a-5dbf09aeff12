package com.ruoyi.system.entity.datashow;

import lombok.Data;

/**
 * 广告日数据汇总
 *
 * <AUTHOR>
 * @date 2023-01-13
 */
@Data
public class AdvertDayStatisticData {

    /**
     * 发券pv
     */
    private Integer adLaunchPv;

    /**
     * 发券uv
     */
    private Integer adLaunchUv;

    /**
     * 广告曝光pv
     */
    private Integer exposurePv;

    /**
     * 广告曝光uv
     */
    private Integer exposureUv;

    /**
     * 广告点击pv
     */
    private Integer clickPv;

    /**
     * 广告点击uv
     */
    private Integer clickUv;

    /**
     * 广告计费点击pv
     */
    private Integer billingClickPv;

    /**
     * 广告计费点击uv
     */
    private Integer billingClickUv;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页点击pv
     */
    private Integer lpClickPv;

    /**
     * 落地页点击uv
     */
    private Integer lpClickUv;

    /**
     * 领取pv
     */
    private Integer takePv;

    /**
     * 领取uv
     */
    private Integer takeUv;

    /**
     * 消耗
     */
    private Long consume;
}
