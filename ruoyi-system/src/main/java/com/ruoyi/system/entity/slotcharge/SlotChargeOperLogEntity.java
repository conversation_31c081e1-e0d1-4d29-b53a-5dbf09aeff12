package com.ruoyi.system.entity.slotcharge;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位每日计费方式操作日志
 *
 * <AUTHOR>
 * @date 2022-3-8 16:50:40
 */
@Data
public class SlotChargeOperLogEntity implements Serializable {
    private static final long serialVersionUID = -9074111730904901430L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 广告位id
     */
    private Long slotId;

    /**
     * 计费类型
     */
    private Integer chargeType;

    /**
     * 计费价格
     */
    private Integer chargePrice;

    /**
     * 操作人员
     */
    private String operName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 操作日期
     */
    private Date curDate;

    /**
     * 广告位请求pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位请求uv
     */
    private Integer slotRequestUv;
}

