package com.ruoyi.system.entity.accounttag;

import lombok.Data;

import java.io.Serializable;
    import java.util.Date;

    /**
    * 账号标签关联表
    * <AUTHOR>
    * @date 2023-3-6 10:53:35
    */
@Data
public class AccountTagRelationEntity implements Serializable {

        /**
        * 主键id
        */
    private Long id;

        /**
        * 账号id
        */
    private Long accountId;

        /**
        * 标签类型
        */
    private Integer tagType;

        /**
        * 标签id
        */
    private Long tagId;

        /**
        * 是否删除，0未删除，1删除
        */
    private Integer isDeleted;

        /**
        * 创建时间
        */
    private Date gmtCreate;

        /**
        * 修改时间
        */
    private Date gmtModified;


}

