package com.ruoyi.system.entity.manager;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 落地页送审记录对象 tb_landpage_audit_record
 * 
 * <AUTHOR>
 * @date 2021-09-10
 */
public class LandpageAuditRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 原落地页链接 */
    @Excel(name = "原落地页URL")
    private String originUrl;

    /** 原落地页链接MD5 */
    private String originUrlMd5;

    /** 爱奇艺链接 */
    private String iqiyiUrl;

    /** 送审时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditApplyTime;

    /** 审核结果返回时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditFinishTime;

    /** 审核状态:0.审核中,1.审核通过,2.审核拒绝 */
    private Integer auditStatus;

    /** 审核拒绝原因 */
    private String auditReason;

    /** 额外信息 */
    private String extInfo;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 送审时间开始
     */
    private Date startDate;

    /**
     * 送审时间截止
     */
    private Date endDate;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setOriginUrl(String originUrl) 
    {
        this.originUrl = originUrl;
    }

    public String getOriginUrl() 
    {
        return originUrl;
    }
    public void setOriginUrlMd5(String originUrlMd5) 
    {
        this.originUrlMd5 = originUrlMd5;
    }

    public String getOriginUrlMd5() 
    {
        return originUrlMd5;
    }
    public void setIqiyiUrl(String iqiyiUrl) 
    {
        this.iqiyiUrl = iqiyiUrl;
    }

    public String getIqiyiUrl() 
    {
        return iqiyiUrl;
    }
    public void setAuditApplyTime(Date auditApplyTime) 
    {
        this.auditApplyTime = auditApplyTime;
    }

    public Date getAuditApplyTime() 
    {
        return auditApplyTime;
    }
    public void setAuditFinishTime(Date auditFinishTime) 
    {
        this.auditFinishTime = auditFinishTime;
    }

    public Date getAuditFinishTime() 
    {
        return auditFinishTime;
    }
    public void setAuditStatus(Integer auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public Integer getAuditStatus() 
    {
        return auditStatus;
    }
    public void setAuditReason(String auditReason) 
    {
        this.auditReason = auditReason;
    }

    public String getAuditReason() 
    {
        return auditReason;
    }
    public void setExtInfo(String extInfo) 
    {
        this.extInfo = extInfo;
    }

    public String getExtInfo() 
    {
        return extInfo;
    }
    public void setGmtCreate(Date gmtCreate) 
    {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtCreate() 
    {
        return gmtCreate;
    }
    public void setGmtModified(Date gmtModified) 
    {
        this.gmtModified = gmtModified;
    }

    public Date getGmtModified() 
    {
        return gmtModified;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("originUrl", getOriginUrl())
            .append("originUrlMd5", getOriginUrlMd5())
            .append("iqiyiUrl", getIqiyiUrl())
            .append("auditApplyTime", getAuditApplyTime())
            .append("auditFinishTime", getAuditFinishTime())
            .append("auditStatus", getAuditStatus())
            .append("auditReason", getAuditReason())
            .append("extInfo", getExtInfo())
            .append("gmtCreate", getGmtCreate())
            .append("gmtModified", getGmtModified())
            .toString();
    }
}
