package com.ruoyi.system.entity.open;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主回调记录
 *
 * <AUTHOR>
 * @date 2022-04-15
 */
@Data
public class AdvertiserCallbackRecord implements Serializable {
    private static final long serialVersionUID = -2483506404023660369L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 转化日期
     */
    private Date curDate;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 转化类型
     * @see com.ruoyi.common.enums.advert.ConvType
     */
    private Integer status;

    /**
     * 转化时间
     */
    private Date submitTime;

    /**
     * 额外信息
     */
    private String ext;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}
