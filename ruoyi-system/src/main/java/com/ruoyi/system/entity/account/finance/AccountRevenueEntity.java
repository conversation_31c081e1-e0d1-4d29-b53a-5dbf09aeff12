package com.ruoyi.system.entity.account.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 账户收益表
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:30
 */
@Data
public class AccountRevenueEntity implements Serializable {
    private static final long serialVersionUID = -4291854303225839163L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 总收益（单位分，累计可提现金额之和）
     */
    private Long totalRevenue;

    /**
     * 可提现金额（分）
     */
    private Integer withdrawableAmount;

    /**
     * 付款类型:1.预付款,0.后付款
     */
    private Integer payType;

    /**
     * 预付款金额(分)
     */
    private Long prepayAmount;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

