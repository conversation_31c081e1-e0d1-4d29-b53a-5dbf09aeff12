package com.ruoyi.system.entity.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 白酒落地页单记录对象 tb_landpage_form_record_baijiu
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
@Data
public class BaijiuLandpageFormRecord implements Serializable {
    private static final long serialVersionUID = 3265966765197870144L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 来源链接
     */
    private String referer;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区/县
     */
    private String district;

    /**
     * 行政区划代码
     */
    private String areaNum;

    /**
     * 详细信息
     */
    private String address;

    /**
     * IP
     */
    private String ip;

    /**
     * 支付金额
     */
    private Integer amount;
    /**
     * 支付宝订单号，用于支付回调时，跟订单进行匹配
     */
    private String tradeNo;
    /**
     * 支付时间
     */
    private Date payTime;
    /**
     * 支付状态
     */
    private String tradeStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;
}
