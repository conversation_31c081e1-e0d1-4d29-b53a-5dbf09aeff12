package com.ruoyi.system.entity.sms;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 留资短信发送记录表
 *
 * <AUTHOR>
 * @date 2022-12-5 10:09:17
 */
@Data
public class LiuziSmsSendRecordEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 留资记录id
     */
    private Long liuziRecordId;

    /**
     * 渠道类型
     * @see com.ruoyi.common.enums.SmsChannelEnum
     */
    private Integer type;

    /**
     * 短信消息id
     */
    private String msgId;

    /**
     * 模版id
     */
    private Long tpId;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 发送状态:0发送中,1成功,2失败,3未知
     * @see com.ruoyi.common.enums.SmsStatusEnum
     */
    private Integer result;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;


}

