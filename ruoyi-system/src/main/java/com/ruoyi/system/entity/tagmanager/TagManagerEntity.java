package com.ruoyi.system.entity.tagmanager;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 标签表
 *
 * <AUTHOR>
 * @date 2022-9-23 10:52:10
 */
@Data
public class TagManagerEntity implements Serializable {
    private static final long serialVersionUID = -6927293625209279111L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 父级标签id，0表示一级
     */
    private Long parentId;

    /**
     * 标签类型
     * @see com.ruoyi.common.enums.TagManagerTypeEnum
     */
    private Integer tagType;

    /**
     * 标签排序
     */
    private Integer tagSort;

    /**
     * 是否删除，0未删除，1删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

