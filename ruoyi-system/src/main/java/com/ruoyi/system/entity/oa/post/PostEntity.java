package com.ruoyi.system.entity.oa.post;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * oa职位表
 *
 * <AUTHOR>
 * @date 2021-10-9 13:43:35
 */
@Data
public class PostEntity implements Serializable {
    private static final long serialVersionUID = 2176830040218042097L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 职位名称
     */
    private String postName;

    /**
     * 职位标识
     */
    private String postKey;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

