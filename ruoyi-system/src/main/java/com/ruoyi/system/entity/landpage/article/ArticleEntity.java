package com.ruoyi.system.entity.landpage.article;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.bo.landpage.article.ArticleCacheBo;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * 文章表
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:01
 */
@Data
public class ArticleEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 聚合链接ID
     */
    private Long linkId;

    /**
     * 名称
     */
    private String name;

    /**
     * 链接
     */
    private String url;

    /**
     * 目标阅读量
     */
    private Integer targetRequestPv;

    /**
     * 阅读量补量
     */
    private Integer compensateRequestPv;

    /**
     * 初始阅读量
     */
    private Integer initRequestPv;

    /**
     * 实际阅读量
     */
    private Integer actualRequestPv;

    /**
     * 私域阅读量
     */
    private Integer syRequestPv;

    /**
     * 实际阅读量(广告主后台展示)
     */
    private Integer displayActualRequestPv;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 状态:0.正常,1.已删除
     */
    private Integer status;

    /**
     * 丰巢审核状态，0-待审核，1-通过，2-驳回
     */
    private Integer fcCheckStatus;

    /**
     * 丰巢驳回原因
     */
    private String fcRejectReason;

    /**
     * 丰巢同步状态，0-待同步，1-同步成功，2-同步失败
     */
    private Integer fcSyncStatus;

    /**
     * 丰巢审核时间
     */
    private Date fcCheckTime;

    /**
     * 同步丰巢失败原因
     */
    private String fcSyncFailReason;

    /**
     * 文章信息
     */
    private String profile;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operatorTime;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 文章是否在线
     */
    public boolean isOnline() {
        return Objects.equals(this.status, 0) && this.weight > 0 && (this.targetRequestPv + this.compensateRequestPv) > 0
                && !this.gmtCreate.before(DateUtil.beginOfDay(new Date()));
    }

    /**
     * convert ArticleEntity to ArticleCacheBo
     */
    public ArticleCacheBo getCacheBo() {
        ArticleCacheBo articleCache = new ArticleCacheBo();
        articleCache.setId(this.getId());
        articleCache.setWeight(this.getWeight());
        articleCache.setUrl(this.getUrl());
        articleCache.setTargetRequestPv(this.getTargetRequestPv());
        articleCache.setCompensateRequestPv(this.getCompensateRequestPv());
        articleCache.setGmtCreate(this.getGmtCreate());
        articleCache.setLinkId(this.getLinkId());
        return articleCache;
    }
}

