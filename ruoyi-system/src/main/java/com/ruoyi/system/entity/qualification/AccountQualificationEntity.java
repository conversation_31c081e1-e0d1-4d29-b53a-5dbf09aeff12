package com.ruoyi.system.entity.qualification;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 提现账号资质信息
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:50
 */
@Data
public class AccountQualificationEntity implements Serializable {
    private static final long serialVersionUID = 3866430566466022729L;

    /**
    * 主键id
    */
    private Long id;

    /**
    * 账号id
    */
    private Long accountId;

    /**
    * 公司名称
    */
    private String companyName;

    /**
    * 营业执照号
    */
    private String businessLicense;

    /**
    * 营业执照图
    */
    private String businessLicenseImg;

    /**
    * 开户银行
    */
    private String bankName;

    /**
    * 银行账户
    */
    private String bankAccount;

    /**
    * 开户名
    */
    private String bankAccountName;

    /**
    * 备注
    */
    private String remarkText;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModified;
}

