package com.ruoyi.system.entity.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备维度快应用数据表
 *
 * <AUTHOR>
 * @date 2022-11-14 11:45:23
 */
@Data
public class MobileHapDataEntity implements Serializable {
    private static final long serialVersionUID = 8655976342529386168L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 手机型号
     */
    private String model;

    /**
     * 发券pv
     */
    private Integer adLaunchPv;

    /**
     * 发券uv
     */
    private Integer adLaunchUv;

    /**
     * 券曝光pv
     */
    private Integer adExposurePv;

    /**
     * 券曝光uv
     */
    private Integer adExposureUv;

    /**
     * 券点击pv
     */
    private Integer adClickPv;

    /**
     * 券点击uv
     */
    private Integer adClickUv;

    /**
     * 快应用启动pv
     */
    private Integer hapLaunchPv;

    /**
     * 快应用启动uv
     */
    private Integer hapLaunchUv;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页点击pv
     */
    private Integer lpClickPv;

    /**
     * 落地页点击uv
     */
    private Integer lpClickUv;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 发券pv增量
     */
    private Integer adLaunchPvAdd;

    /**
     * 发券uv增量
     */
    private Integer adLaunchUvAdd;

    /**
     * 券曝光pv增量
     */
    private Integer adExposurePvAdd;

    /**
     * 券曝光uv增量
     */
    private Integer adExposureUvAdd;

    /**
     * 券点击pv增量
     */
    private Integer adClickPvAdd;

    /**
     * 券点击uv增量
     */
    private Integer adClickUvAdd;

    /**
     * 快应用启动pv增量
     */
    private Integer hapLaunchPvAdd;

    /**
     * 快应用启动uv增量
     */
    private Integer hapLaunchUvAdd;

    /**
     * 落地页曝光pv增量
     */
    private Integer lpExposurePvAdd;

    /**
     * 落地页曝光uv增量
     */
    private Integer lpExposureUvAdd;

    /**
     * 落地页点击pv增量
     */
    private Integer lpClickPvAdd;

    /**
     * 落地页点击uv增量
     */
    private Integer lpClickUvAdd;
}

