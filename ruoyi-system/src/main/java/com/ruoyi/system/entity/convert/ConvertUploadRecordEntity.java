package com.ruoyi.system.entity.convert;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体转化上报记录
 *
 * <AUTHOR>
 * @date 2022-10-19 10:30:00
 */
@Data
public class ConvertUploadRecordEntity implements Serializable {
    private static final long serialVersionUID = -5417584458542246451L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 转化日期
     */
    private Date curDate;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 转化时间
     */
    private Date convertTime;

    /**
     * 广告位参数
     */
    private String slotParam;

    /**
     * 上报状态:0.未上报,1.上报成功,2.上报失败
     */
    private Integer uploadStatus;

    /**
     * 上报时间
     */
    private Date uploadTime;

    /**
     * 上报结果
     */
    private String uploadResult;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

