package com.ruoyi.system.entity.landpage.article;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文章阅读量API接口请求记录
 *
 * <AUTHOR>
 * @date 2024-4-15 16:42:45
 */
@Data
public class ArticleRefreshRecordEntity implements Serializable {
    private static final long serialVersionUID = -7797861381931267802L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 聚合链接ID
     */
    private Long linkId;

    /**
     * 文章ID
     */
    private Long articleId;

    /**
     * 链接
     */
    private String url;

    /**
     * 真实阅读量
     */
    private Integer actualRequestPv;

    /**
     * 操作者ID
     */
    private Long operatorId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

