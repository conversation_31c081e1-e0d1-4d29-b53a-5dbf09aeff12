package com.ruoyi.system.entity.permission;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 职位权限关联表
 *
 * <AUTHOR>
 * @date 2022-6-23 16:23:19
 */
@Data
public class PostPermissionRelationEntity implements Serializable {
    private static final long serialVersionUID = 3359730142117620741L;

    /**
     * id
     */
    private Long id;

    /**
     * oa职位id
     */
    private Long oaPostId;

    /**
     * 权限id列表,json
     */
    private String permissionIds;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

