package com.ruoyi.system.entity.landpage.article;

import lombok.Data;

import java.util.Date;

/**
 * 文章返回拦截数据表
 *
 * <AUTHOR>
 * @date 2024-2-29 17:39:13
 */
@Data
public class ArticleRetDataEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 聚合链接ID
     */
    private Long linkId;

    /**
     * 返回拦截链接MD5
     */
    private String retUrlMd5;

    /**
     * 返回拦截链接
     */
    private String retUrl;

    /**
     * 请求PV
     */
    private Integer requestPv;

    /**
     * 请求UV
     */
    private Integer requestUv;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

