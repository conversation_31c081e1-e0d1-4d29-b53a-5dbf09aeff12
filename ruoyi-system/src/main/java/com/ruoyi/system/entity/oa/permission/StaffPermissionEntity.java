package com.ruoyi.system.entity.oa.permission;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 职员权限关联表
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:19
 */
@Data
public class StaffPermissionEntity implements Serializable {
    private static final long serialVersionUID = -899152510389157916L;

    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long staffId;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 权限id列表，json
     */
    private String permissions;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

