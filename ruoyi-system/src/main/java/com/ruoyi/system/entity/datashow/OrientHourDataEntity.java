package com.ruoyi.system.entity.datashow;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 广告配置维度小时数据表
 *
 * <AUTHOR>
 * @date 2023-7-11 19:48:52
 */
@Data
public class OrientHourDataEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 小时
     */
    private Integer curHour;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 发券pv
     */
    private Integer adLaunchPv;

    /**
     * 发券uv
     */
    private Integer adLaunchUv;

    /**
     * 券曝光pv
     */
    private Integer exposurePv;

    /**
     * 券曝光uv
     */
    private Integer exposureUv;

    /**
     * 券点击pv
     */
    private Integer clickPv;

    /**
     * 券点击uv
     */
    private Integer clickUv;

    /**
     * 券计费点击pv
     */
    private Integer billingClickPv;

    /**
     * 券计费点击uv
     */
    private Integer billingClickUv;

    /**
     * 消耗(分)
     */
    private Integer consume;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页点击pv
     */
    private Integer lpClickPv;

    /**
     * 落地页点击uv
     */
    private Integer lpClickUv;

    /**
     * 领取pv
     */
    private Integer takePv;

    /**
     * 领取uv
     */
    private Integer takeUv;

    /**
     * 表单提交
     */
    private Integer form;

    /**
     * 支付
     */
    private Integer pay;

    /**
     * 退款
     */
    private Integer refund;

    /**
     * 注册
     */
    private Integer register;

    /**
     * 发货
     */
    private Integer delivery;

    /**
     * 投诉
     */
    private Integer complain;

    /**
     * APP下载
     */
    private Integer appDownload;

    /**
     * APP激活
     */
    private Integer appActive;

    /**
     * APP注册
     */
    private Integer appRegister;

    /**
     * 支付金额
     */
    private BigDecimal payPrice;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

