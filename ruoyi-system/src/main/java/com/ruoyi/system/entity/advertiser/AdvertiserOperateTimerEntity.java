package com.ruoyi.system.entity.advertiser;

import lombok.Data;

import java.util.Date;

/**
 * 广告主操作定时任务
 *
 * <AUTHOR>
 * @date 2023-4-20 11:39:56
 */
@Data
public class AdvertiserOperateTimerEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 操作类型:1.关闭所有广告,2.开启所有广告
     */
    private Integer operType;

    /**
     * 计划时间
     */
    private Date planTime;

    /**
     * 执行时间
     */
    private Date execTime;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

