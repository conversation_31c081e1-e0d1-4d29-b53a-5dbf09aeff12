package com.ruoyi.system.entity.oa.user;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * sso账号表
 *
 * <AUTHOR>
 * @date 2022-6-2 15:43:29
 */
@Data
public class UserEntity implements Serializable {
    private static final long serialVersionUID = -736518761640080145L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 邮箱账号
     */
    private String email;

    /**
     * 密码
     */
    private String passwd;

    /**
     * 名字
     */
    private String userName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 账号状态:0.正常,1.冻结
     */
    private Integer userStatus;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

