package com.ruoyi.system.entity.fc;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 丰巢链接日数据统计实体
 */
@Data
public class FcLinkDayDataEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 丰巢链接唯一key
     */
    private String fcLinkKey;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * UV
     */
    private Long uv;

    /**
     * PV
     */
    private Long pv;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
} 