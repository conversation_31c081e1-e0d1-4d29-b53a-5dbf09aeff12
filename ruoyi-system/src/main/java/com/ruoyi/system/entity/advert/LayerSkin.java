package com.ruoyi.system.entity.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 弹层皮肤对象 tb_layer_skin
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
@Data
public class LayerSkin implements Serializable {
    private static final long serialVersionUID = -3094433750175822604L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 弹层皮肤编号
     */
    private String skinCode;

    /**
     * 弹层皮肤名称
     */
    private String skinName;

    /**
     * 弹层皮肤类型(1.刮刮卡,2.翻牌子)
     */
    private Integer skinType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;
}
