package com.ruoyi.system.entity.landpage.article;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文章聚合链接时段数据表
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:58
 */
@Data
public class ArticleAggrLinkHourDataEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 小时
     */
    private Integer curHour;

    /**
     * 聚合链接ID
     */
    private Long linkId;

    /**
     * 请求PV
     */
    private Integer requestPv;

    /**
     * 请求UV
     */
    private Integer requestUv;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

