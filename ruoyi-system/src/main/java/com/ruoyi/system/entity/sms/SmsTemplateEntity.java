package com.ruoyi.system.entity.sms;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短信模版表
 *
 * <AUTHOR>
 * @date 2022-12-5 10:09:01
 */
@Data
public class SmsTemplateEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 渠道
     * @see com.ruoyi.common.enums.SmsChannelEnum
     */
    private Integer type;
    /**
     * 模版id
     */
    private Long tpId;

    /**
     * 模版内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;


}

