package com.ruoyi.system.entity.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 广告位数据对象 tb_slot_data
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Data
public class SlotData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /** 账号id */
    private Long accountId;

    /**
     * 账号模糊搜索
     */
    private String accountSearch;

    /** 媒体id */
    private Long appId;

    /** 广告位id */
    private Long slotId;

    /** 广告位访问pv */
    private Integer slotRequestPv;

    /** 广告位访问uv */
    private Integer slotRequestUv;
    /**
     * 广告位点击pv(媒体反馈)
     */
    private Integer appSlotClickPv;
    /**
     * 广告位点击uv(媒体反馈)
     */
    private Integer appSlotClickUv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposurePv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposureUv;
    /**
     * 广告位访问uv,原始数据
     */
    private Integer slotRequestUvOriginal;

    /** 诺禾广告消耗(分) */
    private Long nhConsume;

    /** 外部广告消耗(分) */
    private Long outerConsume;

    /** 总消耗 */
    private Long totalConsume;

    /** 媒体应得收益(分) */
    private Long appRevenue;

    /** 结算款(分) */
    private Long nhCost;

    /**
     * 外部结算金额(分)
     */
    private Long outerCost;

    /**
     * 是否媒体可见(0.不可见,1.可见)
     */
    private Integer isVisible;

    /**
     * 是否可编辑(0.编辑,1.不可编辑)
     */
    private Integer isEditable;

    /**
     * 最后操作时间
     */
    private Date operateTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    private String appSearch;

    private String slotSearch;

    private List<Long> appIds;

    private List<Long> slotIds;

    private List<Long> accountIds;

    private Date startDate;

    private Date endDate;

    private String appName;

    private String slotName;

    /** 广告位访问pv增量 */
    private Integer slotRequestPvAdd;

    /** 广告位访问uv增量 */
    private Integer slotRequestUvAdd;

    /** 诺禾消耗增量 */
    private Integer nhConsumeAdd;

    /** 总消耗增量 */
    private Integer totalConsumeAdd;

    /** 总收益增量 */
    private Long totalRevenueAdd;

    /**
     * 媒体应得收益增量
     */
    private Long appRevenueAdd;

    /**
     * 诺禾结算款增量
     */
    private Long nhCostAdd;

    /**
     * 外部结算款增量
     */
    private Long outerCostAdd;

    /**
     * 是否编辑过
     */
    private Integer isEdited;

    /**
     * 负责人查询
     */
    private List<Long> managerIds;

    /**
     * 排序字段
     */
    private String orderColumn;

    /**
     * 是否升序
     */
    private Boolean isAsc;

    /**
     * 排序方式
     */
    private String orderType;
}
