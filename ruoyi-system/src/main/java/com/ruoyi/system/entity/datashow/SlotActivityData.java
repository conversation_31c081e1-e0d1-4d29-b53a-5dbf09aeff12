package com.ruoyi.system.entity.datashow;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 广告位维度活动数据对象 tb_slot_activity_data
 * 
 * <AUTHOR>
 * @date 2021-07-21
 */
public class SlotActivityData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /** 账号Id */
    @Excel(name = "账号Id")
    private Long accountId;

    /** 媒体Id */
    @Excel(name = "媒体Id")
    private Long appId;

    /** 广告位Id */
    @Excel(name = "广告位Id")
    private Long slotId;

    /** 活动Id */
    @Excel(name = "活动Id")
    private Long activityId;

    /** 活动访问pv */
    private Integer activityRequestPv;

    /** 活动访问uv */
    private Integer activityRequestUv;

    /** 活动参与pv */
    @Excel(name = "活动参与pv")
    private Integer joinPv;

    /** 活动参与uv */
    @Excel(name = "活动参与uv")
    private Integer joinUv;

    /** 券请求 */
    @Excel(name = "券请求")
    private Integer adRequest;

    /** 发券 */
    @Excel(name = "发券")
    private Integer adLaunch;

    /** 券曝光Pv */
    @Excel(name = "券曝光Pv")
    private Integer adExposurePv;

    /** 券曝光Uv */
    @Excel(name = "券曝光Uv")
    private Integer adExposureUv;

    /** 券点击Pv */
    @Excel(name = "券点击Pv")
    private Integer adClickPv;

    /** 券点击Uv */
    @Excel(name = "券点击Uv")
    private Integer adClickUv;

    /** 活动访问pv增量 */
    private Integer activityRequestPvAdd;

    /** 活动访问uv增量 */
    private Integer activityRequestUvAdd;

    /** 活动参与pv增量 */
    private Integer joinPvAdd;

    /** 活动参与uv增量 */
    private Integer joinUvAdd;

    /** 券请求增量 */
    private Integer adRequestAdd;

    /** 发券增量 */
    private Integer adLaunchAdd;

    /** 券曝光增量Pv */
    private Integer adExposurePvAdd;

    /** 券曝光增量Uv */
    private Integer adExposureUvAdd;

    /** 券点击增量Pv */
    private Integer adClickPvAdd;

    /** 券点击增量Uv */
    private Integer adClickUvAdd;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtCreate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCurDate(Date curDate) 
    {
        this.curDate = curDate;
    }

    public Date getCurDate() 
    {
        return curDate;
    }
    public void setAccountId(Long accountId)
    {
        this.accountId = accountId;
    }

    public Long getAccountId()
    {
        return accountId;
    }
    public void setAppId(Long appId)
    {
        this.appId = appId;
    }

    public Long getAppId()
    {
        return appId;
    }
    public void setSlotId(Long slotId)
    {
        this.slotId = slotId;
    }

    public Long getSlotId()
    {
        return slotId;
    }
    public void setActivityId(Long activityId)
    {
        this.activityId = activityId;
    }

    public Long getActivityId()
    {
        return activityId;
    }

    public Integer getActivityRequestPv() {
        return activityRequestPv;
    }

    public void setActivityRequestPv(Integer activityRequestPv) {
        this.activityRequestPv = activityRequestPv;
    }

    public Integer getActivityRequestUv() {
        return activityRequestUv;
    }

    public void setActivityRequestUv(Integer activityRequestUv) {
        this.activityRequestUv = activityRequestUv;
    }

    public void setJoinPv(Integer joinPv)
    {
        this.joinPv = joinPv;
    }

    public Integer getJoinPv()
    {
        return joinPv;
    }
    public void setJoinUv(Integer joinUv)
    {
        this.joinUv = joinUv;
    }

    public Integer getJoinUv()
    {
        return joinUv;
    }

    public Integer getAdRequest() {
        return adRequest;
    }

    public void setAdRequest(Integer adRequest) {
        this.adRequest = adRequest;
    }

    public Integer getAdRequestAdd() {
        return adRequestAdd;
    }

    public void setAdRequestAdd(Integer adRequestAdd) {
        this.adRequestAdd = adRequestAdd;
    }

    public void setAdLaunch(Integer adLaunch)
    {
        this.adLaunch = adLaunch;
    }

    public Integer getAdLaunch()
    {
        return adLaunch;
    }

    public Integer getJoinPvAdd() {
        return joinPvAdd;
    }

    public void setJoinPvAdd(Integer joinPvAdd) {
        this.joinPvAdd = joinPvAdd;
    }

    public Integer getJoinUvAdd() {
        return joinUvAdd;
    }

    public void setJoinUvAdd(Integer joinUvAdd) {
        this.joinUvAdd = joinUvAdd;
    }

    public Integer getAdLaunchAdd() {
        return adLaunchAdd;
    }

    public void setAdLaunchAdd(Integer adLaunchAdd) {
        this.adLaunchAdd = adLaunchAdd;
    }

    public Integer getAdExposurePv() {
        return adExposurePv;
    }

    public void setAdExposurePv(Integer adExposurePv) {
        this.adExposurePv = adExposurePv;
    }

    public Integer getAdExposureUv() {
        return adExposureUv;
    }

    public void setAdExposureUv(Integer adExposureUv) {
        this.adExposureUv = adExposureUv;
    }

    public Integer getAdClickPv() {
        return adClickPv;
    }

    public void setAdClickPv(Integer adClickPv) {
        this.adClickPv = adClickPv;
    }

    public Integer getAdClickUv() {
        return adClickUv;
    }

    public void setAdClickUv(Integer adClickUv) {
        this.adClickUv = adClickUv;
    }

    public Integer getActivityRequestPvAdd() {
        return activityRequestPvAdd;
    }

    public void setActivityRequestPvAdd(Integer activityRequestPvAdd) {
        this.activityRequestPvAdd = activityRequestPvAdd;
    }

    public Integer getActivityRequestUvAdd() {
        return activityRequestUvAdd;
    }

    public void setActivityRequestUvAdd(Integer activityRequestUvAdd) {
        this.activityRequestUvAdd = activityRequestUvAdd;
    }

    public Integer getAdExposurePvAdd() {
        return adExposurePvAdd;
    }

    public void setAdExposurePvAdd(Integer adExposurePvAdd) {
        this.adExposurePvAdd = adExposurePvAdd;
    }

    public Integer getAdExposureUvAdd() {
        return adExposureUvAdd;
    }

    public void setAdExposureUvAdd(Integer adExposureUvAdd) {
        this.adExposureUvAdd = adExposureUvAdd;
    }

    public Integer getAdClickPvAdd() {
        return adClickPvAdd;
    }

    public void setAdClickPvAdd(Integer adClickPvAdd) {
        this.adClickPvAdd = adClickPvAdd;
    }

    public Integer getAdClickUvAdd() {
        return adClickUvAdd;
    }

    public void setAdClickUvAdd(Integer adClickUvAdd) {
        this.adClickUvAdd = adClickUvAdd;
    }

    public void setGmtCreate(Date gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtCreate() 
    {
        return gmtCreate;
    }
    public void setGmtModified(Date gmtModified) 
    {
        this.gmtModified = gmtModified;
    }

    public Date getGmtModified() 
    {
        return gmtModified;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("curDate", getCurDate())
            .append("accountId", getAccountId())
            .append("appId", getAppId())
            .append("slotId", getSlotId())
            .append("activityId", getActivityId())
            .append("joinPv", getJoinPv())
            .append("joinUv", getJoinUv())
            .append("adLanuch", getAdLaunch())
            .append("gmtCreate", getGmtCreate())
            .append("gmtModified", getGmtModified())
            .toString();
    }
}
