package com.ruoyi.system.entity.account;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 账户关联对象 tb_account_relation
 *
 * <AUTHOR>
 * @date 2021-07-15
 */
@Data
public class AccountRelation implements Serializable {
    private static final long serialVersionUID = -3865428648396864341L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 起点账户
     */
    private Long srcAccount;

    /**
     * 终点账户
     */
    private Long destAccount;

    /**
     * 关系类型:1.商务负责人(内部);2.广告代理(外部);3.运营负责人(内部)
     */
    private Integer relationType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;
}
