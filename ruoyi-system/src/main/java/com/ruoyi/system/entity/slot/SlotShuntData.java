package com.ruoyi.system.entity.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位切量数据对象 tb_slot_shunt_data
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
@Data
public class SlotShuntData implements Serializable {
    private static final long serialVersionUID = 1824021841886344119L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告位id
     */
    private Long slotId;

    /**
     * 广告位切量计划ID
     */
    private Long taskId;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;
}
