package com.ruoyi.system.entity.permission;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 权限表
 *
 * <AUTHOR>
 * @date 2022-6-23 16:24:50
 */
@Data
public class PermissionEntity implements Serializable {
    private static final long serialVersionUID = -657387875135002319L;

    /**
     * id
     */
    private Long id;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限标识
     */
    private String permissionKey;

    /**
     * 父级权限id
     */
    private Long parentId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

