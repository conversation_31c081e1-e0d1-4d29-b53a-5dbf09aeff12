package com.ruoyi.system.entity.landpage;

import lombok.Data;

import java.util.Date;

/**
 * 信用卡落地页表单记录
 *
 * <AUTHOR>
 * @date 2023-6-2 17:14:04
 */
@Data
public class CreditCardLandpageFormRecordEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 落地页
     */
    private String landpageUrl;

    /**
     * 来源链接
     */
    private String referer;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 身份证号(加密)
     */
    private String idCard;

    /**
     * 身份证号(md5)
     */
    private String idCardMd5;

    /**
     * 生日
     */
    private Date birth;

    /**
     * 信用额度(元)
     */
    private Integer creditLimit;

    /**
     * IP
     */
    private String ip;

    /**
     * 身份证实名认证接口:1.聚合,2.毫秒科技
     */
    private Integer auditApiType;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

