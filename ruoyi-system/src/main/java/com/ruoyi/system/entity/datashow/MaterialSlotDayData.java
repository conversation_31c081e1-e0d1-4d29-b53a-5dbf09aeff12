package com.ruoyi.system.entity.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 素材广告位日数据对象 tb_material_slot_day_data
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
@Data
public class MaterialSlotDayData implements Serializable {
    private static final long serialVersionUID = 4876973718575384500L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 素材ID
     */
    private Long materialId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 素材曝光pv
     */
    private Integer exposurePv;

    /**
     * 素材曝光uv
     */
    private Integer exposureUv;

    /**
     * 素材点击pv
     */
    private Integer clickPv;

    /**
     * 素材点击uv
     */
    private Integer clickUv;

    /**
     * 消耗(分)
     */
    private Integer consume;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 素材曝光pv增量
     */
    private Integer exposurePvAdd;

    /**
     * 素材曝光uv增量
     */
    private Integer exposureUvAdd;

    /**
     * 素材点击pv增量
     */
    private Integer clickPvAdd;

    /**
     * 素材点击uv增量
     */
    private Integer clickUvAdd;

    /**
     * 消耗增量(分)
     */
    private Integer consumeAdd;
}
