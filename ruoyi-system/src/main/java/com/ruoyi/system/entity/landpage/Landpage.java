package com.ruoyi.system.entity.landpage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 落地页对象 tb_landpage
 *
 * <AUTHOR>
 * @date 2022-02-10
 */
@Data
public class Landpage implements Serializable {
    private static final long serialVersionUID = 7925736046844559335L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 落地页标识
     */
    private String key;

    /**
     * 落地页名称
     */
    private String name;

    /**
     * 落地页链接
     */
    private String url;

    /**
     * 落地页标签
     */
    private String tag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 落地页页面配置
     */
    private String pageConfig;

    /**
     * 皮肤类型
     * @see com.ruoyi.common.enums.landpage.LandPageSkinTypeEnum
     */
    private Integer skinType;

    /**
     * 域名
     */
    private String domain;

    /**
     * 是否无效:0.有效,1.无效
     */
    private Integer isInvalid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
