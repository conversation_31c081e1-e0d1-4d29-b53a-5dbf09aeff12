package com.ruoyi.system.entity.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位配置对象 tb_slot_config
 *
 * <AUTHOR>
 * @date 2021-07-15
 */
@Data
public class SlotConfig implements Serializable {
    private static final long serialVersionUID = 6962361085256926398L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告位
     */
    private Long slotId;

    /**
     * 活动投放类型
     * @see com.ruoyi.common.enums.slot.SlotRedirectType
     */
    private Integer redirectType;

    /**
     * 活动ID或者活动链接
     */
    private String redirectValue;

    /**
     * 域名配置
     */
    private String domainConfig;

    /**
     * 返回拦截配置
     */
    private String retConfig;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 开关配置
     */
    private String switchConfig;

    /**
     * 兜底直投广告
     */
    private String degradedOrientIds;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;
}
