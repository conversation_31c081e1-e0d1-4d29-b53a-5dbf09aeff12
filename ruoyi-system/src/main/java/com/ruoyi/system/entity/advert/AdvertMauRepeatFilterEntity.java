package com.ruoyi.system.entity.advert;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告MAU去重对象
 *
 * <AUTHOR>
 * @date 2024-11-5 11:37:22
 */
@Data
public class AdvertMauRepeatFilterEntity implements Serializable {
    private static final long serialVersionUID = -9124724450449255881L;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 最后点击时间
     */
    private Date lastClickTime;

    /**
     * 小程序openId
     */
    private String openId;

    /**
     * 广告配置ID
     */
    private Long orientId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

