package com.ruoyi.system.entity.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 落地页表单上报记录
 *
 * <AUTHOR>
 * @date 2021-12-18 16:22:06
 */
@Data
public class LandpageFormSendRecordEntity implements Serializable {
    private static final long serialVersionUID = 4712827452255473979L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 回传记录ID
     */
    private Long recordId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 上报链接
     */
    private String url;

    /**
     * 上报结果
     */
    private String resp;

    /**
     * 是否上报成功
     */
    private Integer isSuccess;

    /**
     * 表单价格
     */
    private Integer formPrice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分配人ID
     */
    private Long operatorId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

