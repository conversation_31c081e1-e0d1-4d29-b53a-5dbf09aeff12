package com.ruoyi.system.entity.withdraw;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 提现记录
 *
 * <AUTHOR>
 * @date 2021-9-9 16:57:24
 */
@Data
public class WithdrawRecordEntity implements Serializable {
    private static final long serialVersionUID = -7034068624441139472L;

    /**
    * 主键id
    */
    private Long id;

    /**
    * 账号id
    */
    private Long accountId;

    /**
    * 提现金额（分）
    */
    private Integer withdrawAmount;

    /**
    * 提现状态
    */
    private Integer withdrawStatus;

    /**
     * 提现单oss地址
     */
    private String withdrawFile;

    /**
    * 扩展信息，记录资质等信息
    */
    private String extraInfo;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModified;
}

