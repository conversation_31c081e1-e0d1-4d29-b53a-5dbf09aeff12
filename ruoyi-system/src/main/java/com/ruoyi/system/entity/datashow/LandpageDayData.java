package com.ruoyi.system.entity.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 落地页维度日数据表
 *
 * <AUTHOR>
 * @date 2022-9-21 16:17:57
 */
@Data
public class LandpageDayData implements Serializable {
    private static final long serialVersionUID = 3698175860058315923L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 落地页标识
     */
    private String landpageKey;

    /**
     * 发券pv
     */
    private Integer adLaunchPv;

    /**
     * 发券uv
     */
    private Integer adLaunchUv;

    /**
     * 券曝光pv
     */
    private Integer adExposurePv;

    /**
     * 券曝光uv
     */
    private Integer adExposureUv;

    /**
     * 券点击pv
     */
    private Integer adClickPv;

    /**
     * 券点击uv
     */
    private Integer adClickUv;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页点击pv
     */
    private Integer lpClickPv;

    /**
     * 落地页点击uv
     */
    private Integer lpClickUv;

    /**
     * 落地页参与pv
     */
    private Integer lpJoinPv;

    /**
     * 落地页参与uv
     */
    private Integer lpJoinUv;

    /**
     * 弹窗曝光pv
     */
    private Integer popupExposurePv;

    /**
     * 弹窗曝光uv
     */
    private Integer popupExposureUv;

    /**
     * 弹窗点击pv
     */
    private Integer popupClickPv;

    /**
     * 弹窗点击uv
     */
    private Integer popupClickUv;

    /**
     * 激活
     */
    private Integer register;

    /**
     * 支付
     */
    private Integer pay;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 发券pv增量
     */
    private Integer adLaunchPvAdd;

    /**
     * 发券uv增量
     */
    private Integer adLaunchUvAdd;

    /**
     * 券曝光pv增量
     */
    private Integer adExposurePvAdd;

    /**
     * 券曝光uv增量
     */
    private Integer adExposureUvAdd;

    /**
     * 券点击pv增量
     */
    private Integer adClickPvAdd;

    /**
     * 券点击uv增量
     */
    private Integer adClickUvAdd;

    /**
     * 落地页曝光pv增量
     */
    private Integer lpExposurePvAdd;

    /**
     * 落地页曝光uv增量
     */
    private Integer lpExposureUvAdd;

    /**
     * 落地页点击pv增量
     */
    private Integer lpClickPvAdd;

    /**
     * 落地页点击uv增量
     */
    private Integer lpClickUvAdd;

    /**
     * 落地页参与pv增量
     */
    private Integer lpJoinPvAdd;

    /**
     * 落地页参与uv增量
     */
    private Integer lpJoinUvAdd;

    /**
     * 弹窗曝光pv增量
     */
    private Integer popupExposurePvAdd;

    /**
     * 弹窗曝光uv增量
     */
    private Integer popupExposureUvAdd;

    /**
     * 弹窗点击pv增量
     */
    private Integer popupClickPvAdd;

    /**
     * 弹窗点击uv增量
     */
    private Integer popupClickUvAdd;

    /**
     * 激活增量
     */
    private Integer registerAdd;

    /**
     * 支付增量
     */
    private Integer payAdd;
}

