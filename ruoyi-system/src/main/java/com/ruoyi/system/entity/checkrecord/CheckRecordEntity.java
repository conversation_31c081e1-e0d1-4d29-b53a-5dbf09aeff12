package com.ruoyi.system.entity.checkrecord;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 提现记录
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:02
 */
@Data
public class CheckRecordEntity implements Serializable {
    private static final long serialVersionUID = 4934535653454423726L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 提现记录id
     */
    private Long withdrawRecordId;

    /**
     * 审核人账号id tb_account.id
     */
    private Long checkAccountId;

    /**
     * 审核状态
     */
    private Integer checkStatus;

    /**
     * 复合审核状态
     * @see com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus
     */
    private Integer complexAuditStatus;

    /**
     * 拒绝理由
     */
    private String refuseReason;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 部门负责人审核人ID
     */
    private Long leaderAuditorId;

    /**
     * 部门负责人审核时间
     */
    private Date leaderAuditTime;

    /**
     * 部门负责人审核理由
     */
    private String leaderAuditReason;

    /**
     * 业务负责人审核人ID
     */
    private Long ceoAuditorId;

    /**
     * 业务负责人审核时间
     */
    private Date ceoAuditTime;

    /**
     * 业务负责人审核理由
     */
    private String ceoAuditReason;

    /**
     * 财务审核人ID
     */
    private Long financeAuditorId;

    /**
     * 财务审核时间
     */
    private Date financeAuditTime;

    /**
     * 财务审核理由
     */
    private String financeAuditReason;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

