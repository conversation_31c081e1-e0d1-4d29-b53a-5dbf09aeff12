package com.ruoyi.system.entity.appdata;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体月账单数据表
 *
 * <AUTHOR>
 * @date 2021-9-9 16:55:56
 */
@Data
public class AppMonthDataEntity implements Serializable {
    private static final long serialVersionUID = 2909751319238074653L;

    /**
    * 主键id
    */
    private Long id;

    /**
    * 月份日期
    */
    private Integer monthDate;

    /**
    * 账号id
    */
    private Long accountId;

    /**
    * 媒体id
    */
    private Long appId;

    /**
    * 广告位访问pv
    */
    private Integer slotRequestPv;

    /**
    * 广告位访问uv
    */
    private Integer slotRequestUv;
    /**
     * 广告位点击pv(媒体反馈)
     */
    private Integer appSlotClickPv;
    /**
     * 广告位点击uv(媒体反馈)
     */
    private Integer appSlotClickUv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposurePv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposureUv;

    /**
     * 广告位访问uv 处理后的
     */
    private Integer slotRequestUvCalculate;

    /**
    * 活动参与pv
    */
    private Integer joinPv;

    /**
    * 活动参与uv
    */
    private Integer joinUv;

    /**
    * 总消耗(分)
    */
    private Integer totalConsume;

    /**
    * 诺禾广告消耗(分)
    */
    private Integer nhConsume;

    /**
    * 外部广告消耗(分)
    */
    private Integer outerConsume;

    /**
    * 媒体应得收益(分)
    */
    private Integer appRevenue;

    /**
    * 结算状态
    */
    private Integer confirmStatus;

    /**
    * 提现记录id tb_withdraw_record.id
    */
    private Long withdrawRecordId;

    /**
     * 诺禾结算款(分)
     */
    private Long nhCost;

    /**
     * 外部结算款(分)
     */
    private Long outerCost;

    /**
     * 付款类型:1.预付款,0.后付款
     */
    private Integer payType;

    /**
     * 预付款欠款金额
     */
    private Long prepayAmount;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModified;
}

