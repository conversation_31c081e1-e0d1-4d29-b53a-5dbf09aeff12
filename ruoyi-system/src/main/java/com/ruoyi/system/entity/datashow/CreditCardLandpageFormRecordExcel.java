package com.ruoyi.system.entity.datashow;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 信用卡表单导出记录
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class CreditCardLandpageFormRecordExcel implements Serializable {
    private static final long serialVersionUID = 3134184128301141514L;

    @ExcelIgnore
    private Long id;

    @ExcelProperty("广告ID")
    private Long advertId;

    @ExcelProperty("广告主ID")
    private Long advertiserId;

    @ExcelProperty("广告主名称")
    private String advertiserName;

    @ExcelProperty("媒体ID")
    private Long appId;

    @ExcelProperty("媒体名称")
    private String appName;

    @ExcelProperty("广告位ID")
    private Long slotId;

    @ExcelProperty("广告位名称")
    private String slotName;

    @ExcelProperty("落地页链接")
    private String landpageUrl;

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("身份证号")
    private String idCard;

    @ExcelProperty("申办额度")
    private Integer creditLimit;

    @ExcelProperty("详细地址")
    private String address;

    @ExcelProperty("手机号")
    private String phone;

    @ExcelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @ExcelProperty("身份证实名接口提供方")
    private String auditApiTypeStr;
}
