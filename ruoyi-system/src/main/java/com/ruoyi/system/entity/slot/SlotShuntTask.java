package com.ruoyi.system.entity.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.slot.SlotShuntStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位切量计划表对象 tb_slot_shunt_task
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
@Data
public class SlotShuntTask implements Serializable {
    private static final long serialVersionUID = 2757016045131733264L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告位
     */
    private Long slotId;

    /**
     * 切量类型:1.pv,2.uv
     */
    private Integer shuntType;

    /**
     * 计划名称
     */
    private String taskName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 活动投放类型
     * @see com.ruoyi.common.enums.slot.SlotRedirectType
     */
    private Integer redirectType;

    /**
     * 活动ID、链接或者广告ID
     */
    private String redirectValue;

    /**
     * 分流比例[0,100]
     */
    private Integer shuntRatio;

    /**
     * 状态
     * @see SlotShuntStatusEnum
     */
    private Integer taskStatus;

    /**
     * 切量上限
     */
    private Integer threshold;

    /**
     * 实际执行时间
     */
    private Date executeTime;

    /**
     * 实际完成时间
     */
    private Date finishTime;

    /**
     * 实际取消时间
     */
    private Date cancelTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;
}
