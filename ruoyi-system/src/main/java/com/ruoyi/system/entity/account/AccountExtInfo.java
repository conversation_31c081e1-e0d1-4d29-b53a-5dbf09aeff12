package com.ruoyi.system.entity.account;

import lombok.Data;

import java.io.Serializable;

/**
 * 账号补充信息
 *
 * <AUTHOR>
 * @date 2021/10/8
 */
@Data
public class AccountExtInfo implements Serializable {
    private static final long serialVersionUID = -5510994661493361447L;

    /**
     * 营业执照注册号
     */
    private String businessLicense;

    /**
     * 营业执照图片
     */
    private String businessLicenseImg;

    /**
     * 公钥
     */
    private String accessKey;

    /**
     * 私钥
     */
    private String secretKey;

    /**
     * 落地页数据回传链接
     */
    private String lpCallbackUrl;

    /**
     * 是否回传媒体信息(0.不传,1.传)
     */
    private Integer appRet;

    /**
     * 表单价格(分)
     */
    private Integer formPrice;

    /**
     * 计费类型:0.有效表单,1.毛表单
     */
    private Integer priceType;

    /**
     * 表单权重
     */
    private Double formWeight;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 广告主离线转实时日期
     */
    private String otoDate;

    /**
     * 结算类型
     */
    private Integer consumeType;
}
