package com.ruoyi.system.entity.fc;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 丰巢链接-文章聚合链接关联表(FcLinkArticleAggrRel)实体类
 *
 * <AUTHOR>
 * @since 2025-06-05 16:37:16
 */
@Data
public class FcLinkArticleAggrRelEntity implements Serializable {
    private static final long serialVersionUID = 190729409524974304L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 丰巢链接唯一key
     */
    private String fcLinkKey;
    /**
     * 文章聚合链接id
     */
    private Long articleAggrLinkId;
    /**
     * 状态:0.正常,1.已删除
     */
    private Integer status;
    /**
     * 操作人ID
     */
    private Long operatorId;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * 创建人ID
     */
    private Long creatorId;


}

