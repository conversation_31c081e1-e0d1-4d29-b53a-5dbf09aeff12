package com.ruoyi.system.entity.common;

import lombok.Data;

import java.util.Date;

/**
 * 行业资质要求表
 *
 * <AUTHOR>
 * @date 2023-3-6 17:34:59
 */
@Data
public class IndustryQualificationRequireEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 行业ID
     */
    private Long industryId;

    /**
     * 资质名称
     */
    private String qualificationName;

    /**
     * 是否必填:0.非必填,1.必填
     */
    private Integer isMust;

    /**
     * 状态:0.禁用,1.启用
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

