package com.ruoyi.system.entity.slotdata;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 广告位月账单数据表
* <AUTHOR>
* @date 2021-9-9 16:56:55
*/
@Data
public class SlotMonthDataEntity implements Serializable {
    private static final long serialVersionUID = 5921787858478259306L;

    /**
    * 主键id
    */
    private Long id;

    /**
    * 月份日期
    */
    private Integer monthDate;

    /**
    * 账号id
    */
    private Long accountId;

    /**
    * 媒体id
    */
    private Long appId;

    /**
    * 广告位id
    */
    private Long slotId;

    /**
    * 广告位访问pv
    */
    private Integer slotRequestPv;

    /**
    * 广告位访问uv
    */
    private Integer slotRequestUv;

    /**
     * 广告位点击pv(媒体反馈)
     */
    private Integer appSlotClickPv;
    /**
     * 广告位点击uv(媒体反馈)
     */
    private Integer appSlotClickUv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposurePv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposureUv;

    /**
    * 广告位访问uv 处理后的
    */
    private Integer slotRequestUvCalculate;

    /**
    * 活动参与pv
    */
    private Integer joinPv;

    /**
    * 活动参与uv
    */
    private Integer joinUv;

    /**
    * 总消耗(分)
    */
    private Integer totalConsume;

    /**
    * 诺禾广告消耗(分)
    */
    private Integer nhConsume;

    /**
    * 外部广告消耗(分)
    */
    private Integer outerConsume;

    /**
     * 媒体应得收入(分)
     */
    private Integer appRevenue;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModified;
}

