package com.ruoyi.system.entity.slot;

import lombok.Data;

import java.util.Date;

/**
 * 投流日数据
 *
 * <AUTHOR>
 * @date 2023-7-31 11:55:54
 */
@Data
public class BiddingDayDataEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 投流账户ID
     */
    private String advertiserId;

    /**
     * 账户消耗(分)
     */
    private Long consume;

    /**
     * 回传转化数
     */
    private Integer conv;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

