package com.ruoyi.system.entity.advertiser;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * DSP广告主消费修改记录表
 *
 * <AUTHOR>
 * @date 2022-7-13 17:28:38
 */
@Data
public class DspAdvertiserConsumeRecordHistoryEntity implements Serializable {
    private static final long serialVersionUID = -4322187517847066164L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 修改前数据快照
     */
    private String beforeEdit;

    /**
     * 修改后数据快照
     */
    private String afterEdit;

    /**
     * 操作来源:1.CRM消费记录
     */
    private Integer source;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

