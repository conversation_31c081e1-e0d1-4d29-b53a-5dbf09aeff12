package com.ruoyi.system.entity.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 白酒落地页单记录对象 tb_landpage_form_record_liuzi
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
@Data
public class LiuziLandpageFormRecord implements Serializable {
    private static final long serialVersionUID = 5185817948180736001L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID ，当落地页是非诺禾自建站时，此id就是其他自建站的id
     */
    private Long advertId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 来源链接
     */
    private String referer;

    /**
     * 姓名
     */
    private String name;

    /**
     * 来源 0客户提交1自造
     */
    private Integer origin;

    /**
     * 手机号
     */
    private String phone;

    /**
     * IP
     */
    private String ip;

    /**
     * 支付金额(分)
     */
    private Integer payAmount;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付订单号
     */
    private String outTradeNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告ID/推广页ID列表
     */
    private List<Long> advertIds;

    /**
     * 姓名/手机号搜索
     */
    private String infoSearch;

    /**
     * 数据来源落地页类型
     * @see com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum
     */
    private Integer landpageType;
    /**
     * 加好友状态
     * @see com.ruoyi.common.enums.QwFriendStatusEnum
     */
    private Integer friendStatus;
}
