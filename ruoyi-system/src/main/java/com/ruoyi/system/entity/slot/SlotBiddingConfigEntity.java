package com.ruoyi.system.entity.slot;

import lombok.Data;

import java.util.Date;

/**
 * 广告位投流配置表
 *
 * <AUTHOR>
 * @date 2023-9-7 11:44:30
 */
@Data
public class SlotBiddingConfigEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告位
     */
    private Long slotId;

    /**
     * 上报价格(分)
     */
    private Integer convPrice;

    /**
     * 冷启动个数，默认1
     */
    private Integer coldStart;

    /**
     * 消耗类型:1.真实消耗(广告CPC消耗),2.理论消耗(广告考核成本)
     */
    private Integer consumeType;

    /**
     * 是否生效:0.未生效,1.已生效
     */
    private Integer isEnable;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

