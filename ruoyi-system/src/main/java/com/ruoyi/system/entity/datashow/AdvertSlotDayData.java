package com.ruoyi.system.entity.datashow;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 广告媒体日数据对象 tb_advert_app_day_data
 *
 * <AUTHOR>
 * @date 2021-10-15
 */
@Data
public class AdvertSlotDayData {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 发券pv
     */
    private Integer adLaunchPv;

    /**
     * 发券uv
     */
    private Integer adLaunchUv;

    /**
     * 广告曝光pv
     */
    private Integer exposurePv;

    /**
     * 广告曝光uv
     */
    private Integer exposureUv;

    /**
     * 广告点击pv
     */
    private Integer clickPv;

    /**
     * 广告点击uv
     */
    private Integer clickUv;

    /**
     * 广告计费点击pv
     */
    private Integer billingClickPv;

    /**
     * 广告计费点击uv
     */
    private Integer billingClickUv;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页点击pv
     */
    private Integer lpClickPv;

    /**
     * 落地页点击uv
     */
    private Integer lpClickUv;

    /**
     * 消耗(分)
     */
    private Integer consume;

    /**
     * 领取pv
     */
    private Integer takePv;

    /**
     * 领取uv
     */
    private Integer takeUv;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 广告发券pv增量
     */
    private Integer adLaunchPvAdd;

    /**
     * 广告发券uv增量
     */
    private Integer adLaunchUvAdd;

    /**
     * 广告曝光pv增量
     */
    private Integer exposurePvAdd;

    /**
     * 广告曝光uv增量
     */
    private Integer exposureUvAdd;

    /**
     * 广告点击pv增量
     */
    private Integer clickPvAdd;

    /**
     * 广告点击uv增量
     */
    private Integer clickUvAdd;

    /**
     * 广告计费点击pv增量
     */
    private Integer billingClickPvAdd;

    /**
     * 广告计费点击uv增量
     */
    private Integer billingClickUvAdd;

    /**
     * 落地页曝光pv增量
     */
    private Integer lpExposurePvAdd;

    /**
     * 落地页曝光uv增量
     */
    private Integer lpExposureUvAdd;

    /**
     * 落地页点击pv增量
     */
    private Integer lpClickPvAdd;

    /**
     * 落地页点击uv增量
     */
    private Integer lpClickUvAdd;

    /**
     * 消耗增量(分)
     */
    private Integer consumeAdd;

    /**
     * 领取pv增量
     */
    private Integer takePvAdd;

    /**
     * 领取uv增量
     */
    private Integer takeUvAdd;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 广告id列表
     */
    private List<Long> advertIds;
}
