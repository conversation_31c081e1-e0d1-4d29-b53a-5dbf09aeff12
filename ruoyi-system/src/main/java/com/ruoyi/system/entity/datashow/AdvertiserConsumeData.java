package com.ruoyi.system.entity.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告主日消耗数据
 *
 * <AUTHOR>
 * @date 2021-08-19
 */
@Data
public class AdvertiserConsumeData implements Serializable {
    private static final long serialVersionUID = -6676370767199298249L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 消耗(分)
     */
    private Long consume;

    /**
     * 预算(分)
     */
    private Long budget;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    private List<Long> advertiserIds;

    private Date startDate;

    private Date endDate;

    /**
     * 消耗增量(分)
     */
    private Integer consumeAdd;
}
