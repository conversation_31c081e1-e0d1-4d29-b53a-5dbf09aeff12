package com.ruoyi.system.entity.landpage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 外部落地页表单记录
 *
 * <AUTHOR>
 * @date 2021-10-09
 */
@Data
public class ExternalLandpageRecord {

    /**
     * 主键
     */
    private Long id;

    /**
     * 提交日期
     */
    private Date submitDate;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 外部表单编号
     */
    private String externalNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号(加密)
     */
    private String idCard;

    /**
     * 身份证号(MD5)
     */
    private String idCardMd5;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区/县
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 导入类型
     * @see com.ruoyi.common.enums.landpage.ImportTypeEnum
     */
    private Integer importType;

    /**
     * 是否导出过:0.未导出,1.已导出
     */
    private Integer isExported;

    /**
     * 额外信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}
