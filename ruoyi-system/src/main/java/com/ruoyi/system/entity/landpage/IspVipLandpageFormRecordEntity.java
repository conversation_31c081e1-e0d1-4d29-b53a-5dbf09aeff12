package com.ruoyi.system.entity.landpage;

import com.ruoyi.common.enums.landpage.IspVipProductTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * 运营商会员落地页记录
 *
 * <AUTHOR>
 * @date 2023-11-7 11:41:45
 */
@Data
public class IspVipLandpageFormRecordEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 产品类型
     * @see IspVipProductTypeEnum
     */
    private Integer productType;

    /**
     * 订购订单号
     */
    private String bizOrderNo;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 订购状态:0.未订购,1.成功,2.失败
     */
    private Integer status;

    /**
     * 订购结果
     */
    private String msg;

    /**
     * 补充信息
     */
    private String ext;

    /**
     * IP
     */
    private String ip;

    /**
     * 落地页
     */
    private String landpageUrl;

    /**
     * 来源链接
     */
    private String referer;

    /**
     * userAgent
     */
    private String userAgent;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

