package com.ruoyi.system.entity.slot;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位开关设置
 *
 * <AUTHOR>
 * @date 2022-01-26
 */
@Data
public class SlotSwitchConfig implements Serializable {
    private static final long serialVersionUID = -2327485039716525290L;

    /**
     * 落地页返回挽留开关
     */
    private Integer lpRet;

    /**
     * 数据校准开关
     */
    private Integer adjust;

    /**
     * 自动结算开关
     */
    private Integer autoCharge;
}
