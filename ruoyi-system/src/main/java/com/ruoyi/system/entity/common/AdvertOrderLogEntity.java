package com.ruoyi.system.entity.common;

import lombok.Data;

import java.util.Date;

/**
 * 业务订单明细表
 *
 * <AUTHOR>
 * @date 2023-10-16
 */
@Data
public class AdvertOrderLogEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告位访问标识
     */
    private String srid;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 小时
     */
    private Integer curHour;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 素材ID
     */
    private Long materialId;

    /**
     * 计费单价(分)
     */
    private Integer unitPrice;

    /**
     * 设备号
     */
    private String deviceId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 运营商
     */
    private String isp;

    /**
     * IP
     */
    private String ip;

    /**
     * 操作系统类型:iOS/Android
     */
    private String osType;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * 手机品牌
     */
    private String mobileBrand;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * 发券
     */
    private Integer adLaunch;

    /**
     * 发券次序
     */
    private Integer launchSeq;

    /**
     * 券曝光
     */
    private Integer adExposure;

    /**
     * 券点击
     */
    private Integer adClick;

    /**
     * 实际扣费(分)
     */
    private Integer cpcCost;

    /**
     * 落地页曝光
     */
    private Integer lpExposure;

    /**
     * 落地页转化
     */
    private Integer lpClick;

    /**
     * 考核指标
     */
    private Integer assessType;

    /**
     * 理论扣费(产生转化时的考核成本)
     */
    private Integer theoryCost;

    /**
     * 领取
     */
    private Integer take;

    /**
     * 支付
     */
    private Integer pay;

    /**
     * 退款
     */
    private Integer refund;

    /**
     * 注册
     */
    private Integer register;

    /**
     * 预估CTR
     */
    private Double pCtr;

    /**
     * 预估CVR
     */
    private Double pCvr;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

