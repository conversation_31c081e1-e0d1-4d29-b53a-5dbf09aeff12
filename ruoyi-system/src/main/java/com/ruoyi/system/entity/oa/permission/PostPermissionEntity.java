package com.ruoyi.system.entity.oa.permission;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 职位权限关联表
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:18
 */
@Data
public class PostPermissionEntity implements Serializable {
    private static final long serialVersionUID = -9188578421972038467L;

    /**
     * id
     */
    private Long id;

    /**
     * 职位id
     */
    private Long postId;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 权限id列表，json
     */
    private String permissions;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

