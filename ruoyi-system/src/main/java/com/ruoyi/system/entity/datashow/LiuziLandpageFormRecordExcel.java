package com.ruoyi.system.entity.datashow;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 留资落地页单记录对象 tb_landpage_form_record_liuzi
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class LiuziLandpageFormRecordExcel implements Serializable {

    private static final long serialVersionUID = -1398070098593429092L;
    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 自建站类型
     */
    @ExcelProperty("自建站类型")
    private String landpageTypeName;
    /**
     * 推广页名称
     */
    @ExcelProperty("推广页名称")
    private String extendName;

    /**
     * 广告ID
     */
    @ExcelProperty("广告ID")
    private Long advertId;

    /**
     * 媒体名称
     */
    @ExcelProperty("媒体名称")
    private String appName;

    /**
     * 媒体ID
     */
    @ExcelProperty("媒体ID")
    private Long appId;

    /**
     * 广告位ID
     */
    @ExcelProperty("广告位ID")
    private Long slotId;

    /**
     * 广告位名称
     */
    @ExcelProperty("广告位名称")
    private String slotName;

    /**
     * 订单号
     */
    @ExcelIgnore
    private String orderId;

    /**
     * 用户ID
     */
    @ExcelIgnore
    private Long consumerId;

    /**
     * 落地页链接
     */
    @ExcelProperty("落地页链接")
    private String landpageUrl;

    /**
     * 来源链接
     */
    @ExcelIgnore
    private String referer;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String name;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String phone;


    /**
     * IP
     */
    @ExcelProperty("ip")
    private String ip;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("提交时间")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private Date gmtModified;

    /**
     * 支付金额
     */
    @ExcelProperty("支付金额")
    private String payAmount;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("支付时间")
    private Date payTime;

    /**
     * 短信条数
     */
    @ExcelProperty("短信条数")
    private Integer smsCount;

    /**
     * 发短信状态
     */
    @ExcelProperty("发短信状态")
    private String smsResult;

    /**
     * 加好友状态
     * @see com.ruoyi.common.enums.QwFriendStatusEnum
     */
    @ExcelProperty("加好友状态")
    private String friendStatusStr;
}
