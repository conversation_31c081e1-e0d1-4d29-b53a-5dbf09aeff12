package com.ruoyi.system.entity.landpage;

import lombok.Data;

import java.util.Date;

/**
 * 广告主日维度表单数据表
 *
 * <AUTHOR>
 * @date 2023-2-15 11:35:07
 */
@Data
public class AdvertiserFormDataEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 表单数量
     */
    private Integer formCount;

    /**
     * 成功表单数量
     */
    private Integer successFormCount;

    /**
     * 表单价格总和(分)
     */
    private Integer consume;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 表单数量增量
     */
    private Integer formCountAdd;

    /**
     * 成功表单数量增量
     */
    private Integer successFormCountAdd;

    /**
     * 表单价格总和增量
     */
    private Integer consumeAdd;
}

