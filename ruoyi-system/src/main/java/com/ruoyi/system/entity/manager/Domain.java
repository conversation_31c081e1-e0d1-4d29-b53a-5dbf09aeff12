package com.ruoyi.system.entity.manager;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 域名对象 tb_domain
 *
 * <AUTHOR>
 * @date 2021-09-16
 */
@Data
public class Domain implements Serializable {
    private static final long serialVersionUID = 3747475609231185988L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 域名
     */
    private String domain;

    /**
     * 域名类型:1.广告位域名,2.活动域名,3.落地页域名
     *
     * @see com.ruoyi.common.enums.domain.DomainType
     */
    private Integer domainType;

    /**
     * 是否启用Https:0.不启用,1.启用
     */
    private Integer httpsEnable;

    /**
     * 域名状态:0.正常,1.不可用
     */
    private Integer domainStatus;

    /**
     * 备案号
     */
    private String icpNo;

    /**
     * 备案主体
     */
    private String icpSubject;

    /**
     * 域名到期时间
     */
    private Date domainExpire;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 支付宝状态
     */
    private Integer alipayStatus;

    /**
     * 微信状态
     */
    private Integer wxStatus;

    /**
     * 排序字段
     */
    private String orderColumn;

    /**
     * 是否升序
     */
    private Boolean isAsc;

    /**
     * 域名列表
     */
    private List<String> domains;


    /**
     * * 域名类型:1.广告位域名,2.活动域名,3.落地页域名
     *
     * @param domainType
     * @return
     */
    public static String getDomainStr(Integer domainType) {
        if (Objects.equals(domainType, 1)) {
            return "广告位域名！！！";
        }
        if (Objects.equals(domainType, 2)) {
            return "活动域名";
        }
        if (Objects.equals(domainType, 3)) {
            return "落地页域名";
        }
        return "其他";
    }
}
