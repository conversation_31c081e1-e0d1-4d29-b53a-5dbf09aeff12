package com.ruoyi.system.entity.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告计费类型维度时段数据表
 *
 * <AUTHOR>
 * @date 2022-10-21 14:03:32
 */
@Data
public class AdvertChargeHourDataEntity implements Serializable {
    private static final long serialVersionUID = -2834327444294343949L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 小时
     */
    private Integer curHour;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 计费类型:1.CPC,2.OCPC
     */
    private Integer chargeType;

    /**
     * 发券pv
     */
    private Integer adLaunchPv;

    /**
     * 发券uv
     */
    private Integer adLaunchUv;

    /**
     * 券曝光pv
     */
    private Integer exposurePv;

    /**
     * 券曝光uv
     */
    private Integer exposureUv;

    /**
     * 券点击pv
     */
    private Integer clickPv;

    /**
     * 券点击uv
     */
    private Integer clickUv;

    /**
     * 券点击pv
     */
    private Integer billingClickPv;

    /**
     * 券点击uv
     */
    private Integer billingClickUv;

    /**
     * 消耗(分)
     */
    private Integer consume;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页点击pv
     */
    private Integer lpClickPv;

    /**
     * 落地页点击uv
     */
    private Integer lpClickUv;

    /**
     * 激活
     */
    private Integer register;

    /**
     * 支付
     */
    private Integer pay;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 发券pv增量
     */
    private Integer adLaunchPvAdd;

    /**
     * 发券uv增量
     */
    private Integer adLaunchUvAdd;

    /**
     * 券曝光pv增量
     */
    private Integer exposurePvAdd;

    /**
     * 券曝光uv增量
     */
    private Integer exposureUvAdd;

    /**
     * 券点击pv增量
     */
    private Integer clickPvAdd;

    /**
     * 券点击uv增量
     */
    private Integer clickUvAdd;

    /**
     * 券点击pv增量
     */
    private Integer billingClickPvAdd;

    /**
     * 券点击uv增量
     */
    private Integer billingClickUvAdd;

    /**
     * 消耗增量
     */
    private Integer consumeAdd;

    /**
     * 落地页曝光pv增量
     */
    private Integer lpExposurePvAdd;

    /**
     * 落地页曝光uv增量
     */
    private Integer lpExposureUvAdd;

    /**
     * 落地页点击pv增量
     */
    private Integer lpClickPvAdd;

    /**
     * 落地页点击uv增量
     */
    private Integer lpClickUvAdd;

    /**
     * 激活增量
     */
    private Integer registerAdd;

    /**
     * 支付增量
     */
    private Integer payAdd;
}

