package com.ruoyi.system.entity.slotcharge;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位每日计费方式
 *
 * <AUTHOR>
 * @date 2022-3-7 19:13:24
 */
@Data
public class SlotChargeEntity implements Serializable {
    private static final long serialVersionUID = 8098674414885393614L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告位id
     */
    private Long slotId;

    /**
     * 计费类型
     */
    private Integer chargeType;

    /**
     * 计费价格
     */
    private Integer chargePrice;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

