package com.ruoyi.system.entity.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告对象 tb_advert
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@Data
public class Advert implements Serializable {
    private static final long serialVersionUID = 7787880039378780992L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告分类
     * @see com.ruoyi.common.enums.advert.AdvertCategory
     */
    private Integer advertCategory;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 广告缩略图
     */
    private String thumbnailImg;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 考核指标
     */
    private Integer assessType;

    /**
     * 考核成本(分)
     */
    private Integer assessCost;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 点击监测链接
     */
    private String clickCallbackUrl;

    /**
     * 落地页数据回传链接
     */
    private String lpCallbackUrl;

    /**
     * 每日预算
     */
    private Integer dailyBudget;

    /**
     * 开始投放日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startServingDate;

    /**
     * 结束投放日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date stopServingDate;

    /**
     * 投放开关
     * @see com.ruoyi.common.enums.common.SwitchStatusEnum
     */
    private Integer servingSwitch;

    /**
     * 状态
     * @see com.ruoyi.common.enums.advert.AdvertStatusEnum
     */
    private Integer advertStatus;

    /**
     * 额外信息
     */
    private String extInfo;

    /**
     * 创建人ID
     */
    private Long operatorId;

    /**
     * 创建人名称
     */
    private String operatorName;

    /**
     * 是否无效:0.有效,1.无效
     */
    private Integer isInvalid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 创建时间起始
     */
    private Date gmtCreateStart;

    /**
     * 创建时间截止
     */
    private Date gmtCreateEnd;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 广告ID列表
     */
    private List<Long> ids;

    /**
     * 计费类型
     */
    private Integer billingType;

    /**
     * 成本
     */
    private Integer cost;

    /**
     * 搜索值
     */
    private String searchValue;
}
