package com.ruoyi.system.entity.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位数据修改记录对象
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
@Data
public class SlotDataEditHistory implements Serializable {
    private static final long serialVersionUID = -7815352961264255216L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告位id
     */
    private Long slotId;

    /**
     * 修改前快照
     */
    private String beforeEdit;

    /**
     * 修改后快照
     */
    private String afterEdit;

    /**
     * 操作来源:1.运营每日任务,2.媒体广告位数据修改,3.广告位列表-结算设置,4.媒体月账单-调账
     */
    private Integer source;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
