package com.ruoyi.system.entity.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 媒体应用对象 tb_app
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Data
public class App implements Serializable {
    private static final long serialVersionUID = -6257984119598392404L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 主体类型:1.Android,2.iOS,3.wap,4.小程序,5.公众号
     */
    private Integer appType;

    /**
     * 应用标识
     */
    private String appKey;

    /**
     * 应用秘钥
     */
    private String appSecret;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 创建时间起始
     */
    private Date startDate;

    /**
     * 创建时间截止
     */
    private Date endDate;

    /**
     * 媒体列表
     */
    private List<Long> appIds;

    /**
     * 搜索值
     */
    private String searchValue;

    /**
     * 账号ID列表
     */
    private List<Long> accountIds;
}
