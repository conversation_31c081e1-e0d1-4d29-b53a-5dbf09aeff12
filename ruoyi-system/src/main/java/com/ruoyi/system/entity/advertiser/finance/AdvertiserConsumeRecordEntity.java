package com.ruoyi.system.entity.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主消费记录表
 *
 * <AUTHOR>
 * @date 2022-3-18 17:58:07
 */
@Data
public class AdvertiserConsumeRecordEntity implements Serializable {
    private static final long serialVersionUID = -6833274512020785668L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 广告主账号ID
     */
    private Long accountId;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 消费金额(分)
     */
    private Integer consumeAmount;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

