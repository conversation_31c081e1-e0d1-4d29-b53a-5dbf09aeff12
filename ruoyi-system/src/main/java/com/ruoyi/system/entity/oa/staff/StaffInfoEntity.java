package com.ruoyi.system.entity.oa.staff;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * oa员工信息表
 *
 * <AUTHOR>
 * @date 2021-10-9 14:14:04
 */
@Data
public class StaffInfoEntity implements Serializable {
    private static final long serialVersionUID = 7578952150622090721L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 员工角色
     * @see cn.com.nuohe.oa.constant.enums.UserRoleEnum
     */
    private Integer userRole;

    /**
     * 性别,1男,2女
     */
    private Integer sex;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 个人邮箱
     */
    private String personalEmail;

    /**
     * 民族
     */
    private String nation;

    /**
     * 政治面貌
     * @see cn.com.nuohe.oa.constant.enums.PoliticsStatusEnum
     */
    private Integer politicsStatus;

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 婚姻状况
     * @see cn.com.nuohe.oa.constant.enums.MaritalStatusEnum
     */
    private Integer maritalStatus;

    /**
     * 户口所在地
     */
    private String hometown;

    /**
     * 现居住地
     */
    private String nowResidence;

    /**
     * 紧急联系人
     */
    private String urgentContact;

    /**
     * 联系电话
     */
    private String urgentContactPhone;

    /**
     * 紧急联系人关系
     */
    private String urgentContactRelation;

    /**
     * 毕业时间
     */
    private Date graduateDate;

    /**
     * 毕业学校
     */
    private String graduateSchool;

    /**
     * 所学专业
     */
    private String specialty;

    /**
     * 毕业证书编号
     */
    private String certificateNumber;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 身份证地址
     */
    private String idCardAddress;

    /**
     * 身份证正面照片
     */
    private String idCardFront;

    /**
     * 身份证反面照片
     */
    private String idCardReverse;

    /**
     * 职位id
     */
    private Long postId;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 入职日期
     */
    private Date entryDate;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

