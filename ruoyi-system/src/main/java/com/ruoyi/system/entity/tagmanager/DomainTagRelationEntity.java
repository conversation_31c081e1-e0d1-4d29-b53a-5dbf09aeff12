package com.ruoyi.system.entity.tagmanager;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 域名标签关联表
 *
 * <AUTHOR>
 * @date 2022-12-26 14:32:58
 */
@Data
public class DomainTagRelationEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 域名id
     */
    private Long domainId;

    /**
     * 域名标签id
     */
    private Long tagId;

    /**
     * 是否删除，0未删除，1删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;


}

