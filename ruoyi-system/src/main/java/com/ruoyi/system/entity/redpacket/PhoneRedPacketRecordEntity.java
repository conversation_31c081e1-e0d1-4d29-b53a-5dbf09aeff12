package com.ruoyi.system.entity.redpacket;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 表单用户领红包记录表
 *
 * <AUTHOR>
 * @date 2022-5-26 17:50:17
 */
@Data
public class PhoneRedPacketRecordEntity implements Serializable {
    private static final long serialVersionUID = -6132971499779981090L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 下单手机号
     */
    private String phone;

    /**
     * 表单姓名
     */
    private String name;

    /**
     * 红包金额(分)
     */
    private Integer amount;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    private String alipayName;

    /**
     * IP
     */
    private String ip;

    /**
     * 浏览器UA
     */
    private String userAgent;

    /**
     * 打款状态:0.未打款,1.已打款,2.无效信息
     */
    private Integer transferStatus;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operatorTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 提交信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

