package com.ruoyi.system.entity.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 活动推广计划对象 tb_activity_plan
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
@Data
public class ActivityPlan implements Serializable {
    private static final long serialVersionUID = 2705741351692993717L;

    /**
     * 活动推广计划ID
     */
    private Long id;

    /**
     * 活动工具ID
     */
    private Long activityId;

    /**
     * 状态:0.开启,1.关闭
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long operatorId;

    /**
     * 创建人名称
     */
    private String operatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    private List<Long> activityIds;
}
