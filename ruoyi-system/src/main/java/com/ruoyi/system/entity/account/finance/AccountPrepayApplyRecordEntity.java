package com.ruoyi.system.entity.account.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体预付款申请记录
 *
 * <AUTHOR>
 * @date 2022-7-29 14:44:56
 */
@Data
public class AccountPrepayApplyRecordEntity implements Serializable {
    private static final long serialVersionUID = 4834514477123668806L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 媒体账号ID
     */
    private Long accountId;

    /**
     * 预付款申请金额(分)
     */
    private Integer applyPrepayAmount;

    /**
     * 预付款欠款金额(分)
     */
    private Long prepayAmount;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行账户
     */
    private String bankAccount;

    /**
     * 开户名
     */
    private String bankAccountName;

    /**
     * 发票列表
     */
    private String invoiceList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核状态
     * @see com.ruoyi.common.enums.publisher.PrepayAuditStatus
     */
    private Integer auditStatus;

    /**
     * 复合审核状态
     * @see com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus
     */
    private Integer complexAuditStatus;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 审核人ID
     */
    private Long auditorId;

    /**
     * 部门负责人审核人ID
     */
    private Long leaderAuditorId;

    /**
     * 部门负责人审核时间
     */
    private Date leaderAuditTime;

    /**
     * 部门负责人审核理由
     */
    private String leaderAuditReason;

    /**
     * 业务负责人审核人ID
     */
    private Long ceoAuditorId;

    /**
     * 业务负责人审核时间
     */
    private Date ceoAuditTime;

    /**
     * 业务负责人审核理由
     */
    private String ceoAuditReason;

    /**
     * 财务审核人ID
     */
    private Long financeAuditorId;

    /**
     * 财务审核时间
     */
    private Date financeAuditTime;

    /**
     * 财务审核理由
     */
    private String financeAuditReason;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 预付款主体列表，1-易联智能，2-易联短剧，3-诺禾，4-玩时，5-悦投，6-中情，7-易联视界，8-其他
     */
    private String prepaySubjectList;
}

