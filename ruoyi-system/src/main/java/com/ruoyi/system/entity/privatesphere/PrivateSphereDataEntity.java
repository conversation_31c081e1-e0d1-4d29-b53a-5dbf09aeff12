package com.ruoyi.system.entity.privatesphere;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 私域数据表
 *
 * <AUTHOR>
 * @date 2023-2-10 14:18:11
 */
@Data
public class PrivateSphereDataEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 外呼总数
     */
    private Integer callCount;

    /**
     * 接通数
     */
    private Integer connectCount;

    /**
     * 进入人工数
     */
    private Integer personCount;

    /**
     * 入群数
     */
    private Integer entryCount;

    /**
     * 数据成本
     */
    private Float dataCost;
    /**
     * 数据成本数
     */
    private Integer dataCostCount;

    /**
     * AI+线路成本
     */
    private Integer lineCost;

    /**
     * 人工成本单价
     */
    private Integer personCost;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;


}

