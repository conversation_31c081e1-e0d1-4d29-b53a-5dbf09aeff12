package com.ruoyi.system.entity.advert;

import lombok.Data;

import java.util.Date;

/**
 * 广告结算对象 tb_advert_cost
 *
 * <AUTHOR>
 * @date 2022-02-17
 */
@Data
public class AdvertCost {
    private static final long serialVersionUID = -1341245400412350835L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 当天日期
     */
    private Date curDate;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 计费类型
     * @see com.ruoyi.common.enums.advert.AdvertCostBillingTypeEnum
     */
    private Integer billingType;

    /**
     * 成本
     */
    private Integer cost;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}
