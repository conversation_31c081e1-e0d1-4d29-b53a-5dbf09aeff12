package com.ruoyi.system.entity.account;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 账号对象 tb_account
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Data
public class Account implements Serializable {
    private static final long serialVersionUID = 8723910543017934784L;

    /**
     * 账号ID
     */
    private Long id;

    /**
     * 主体类型
     * @see com.ruoyi.common.enums.account.AccountMainType
     */
    private Integer mainType;

    /**
     * 邮箱账号
     */
    private String email;

    /**
     * 密码
     */
    private String passwd;

    /**
     * 重复密码
     */
    private String repeatPasswd;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 验证码
     */
    private String code;

    /**
     * 账号状态:0.正常,1.冻结
     */
    private Integer status;

    /**
     * 管理员类型
     * @see com.ruoyi.common.enums.account.AdminTypeEnum
     */
    private Integer adminType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 注册时间起始
     */
    private Date startDate;

    /**
     * 注册时间截止
     */
    private Date endDate;

    /**
     * 账号ID列表
     */
    private List<Long> ids;

    /**
     * 主体类型列表
     */
    private List<Integer> mainTypes;

    /**
     * 补充信息
     */
    private String extInfo;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 代理商ID列表
     */
    private List<Long> agentIds;

    /**
     * 搜索关键词
     */
    private String searchValue;

    /**
     * 排除的ID列表
     */
    private List<Long> excludeIds;
}
