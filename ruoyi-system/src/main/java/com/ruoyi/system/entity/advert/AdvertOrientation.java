package com.ruoyi.system.entity.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.advert.LandpageTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告定向配置对象 tb_advert_orientation
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@Data
public class AdvertOrientation implements Serializable {
    private static final long serialVersionUID = -2674317799223418403L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 配置名称
     */
    private String orientName;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 投放开关:0.关闭,1.开启
     */
    private Integer servingSwitch;

    /**
     * 落地页类型
     * @see LandpageTypeEnum
     */
    private Integer landpageType;

    /**
     * 自定义落地页链接
     */
    private String landpageUrl;

    /**
     * 计费类型
     * @see com.ruoyi.common.enums.advert.ChargeTypeEnum
     */
    private Integer chargeType;

    /**
     * 计费单价/兜底CPC价格(分)
     */
    private Integer unitPrice;

    /**
     * 计费单价(毫)
     */
    private Integer milliUnitPrice;

    /**
     * OCPC转化类型
     * @see com.ruoyi.common.enums.advert.OcpcConvTypeEnum
     */
    private Integer ocpcConvType;

    /**
     * OCPC转化成本(分)
     */
    private Integer ocpcConvCost;

    /**
     * 每日预算(分)
     */
    private Integer dailyBudget;

    /**
     * 地域定向
     */
    private String areaTarget;

    /**
     * 设备定向
     * @see com.ruoyi.common.enums.advert.DeviceTargetType
     */
    private Integer deviceTarget;

    /**
     * 系统定向
     * @see com.ruoyi.common.enums.advert.OsTargetType
     */
    private Integer osTarget;

    /**
     * 流量定向
     * @see com.ruoyi.common.enums.advert.FlowTargetType
     */
    private Integer flowTarget;

    /**
     * 运营商定向
     * @see com.ruoyi.common.enums.advert.IspTargetType
     */
    private Integer ispTarget;

    /**
     * 投放时段:0.不限,非0.自定义
     */
    private Integer servingHour;

    /**
     * 支持的操作系统(Android/iOS/其他,逗号分隔)
     */
    private String platform;

    /**
     * 是否默认配置:0.否,1.是
     */
    private Integer isDefault;

    /**
     * 屏蔽的媒体Id列表
     */
    private List<Long> bannedAppIds;

    /**
     * 定向的媒体Id列表
     */
    private List<Long> orienteAppIds;

    /**
     * 权重
     */
    private Double weight;

    /**
     * 创建人ID
     */
    private Long operatorId;

    /**
     * 创建人名称
     */
    private String operatorName;

    /**
     * 是否删除:0.未删除,1.已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 配置ID列表
     */
    private List<Long> ids;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 创建时间起始
     */
    private Date gmtCreateStart;

    /**
     * 创建时间截止
     */
    private Date gmtCreateEnd;

    /**
     * 配置ID/名称搜索
     */
    private String orientSearch;
}
