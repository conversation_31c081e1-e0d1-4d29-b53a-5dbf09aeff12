package com.ruoyi.system.entity.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告位对象 tb_slot
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Data
public class Slot implements Serializable {
    private static final long serialVersionUID = -4180064463120487350L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 应用id
     */
    private Long appId;

    /**
     * 投放链接
     */
    private String slotUrl;

    /**
     * 广告位规格
     */
    private Long slotSpecId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 投放方式:1.H5投放
     */
    private Integer sckType;

    /**
     * 状态:0.开启,1.关闭
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 广告位规格
     */
    private SlotSpec slotSpec;

    private List<Long> accountIds;

    private List<Long> appIds;

    private List<Long> slotIds;

    private List<Long> notSlotIds;

    private String appName;

    /**
     * 创建时间起始
     */
    private Date startDate;

    /**
     * 创建时间截止
     */
    private Date endDate;

    /**
     * 搜索值
     */
    private String searchValue;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 排序字段
     */
    private String orderColumn;

    /**
     * 是否升序
     */
    private Boolean isAsc;

    /**
     * 负责人查询
     */
    private List<Long> managerIds;
}
