package com.ruoyi.system.entity.datashow;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.utils.excel.FenToYuanConverter;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 落地页单记录对象 tb_landpage_form_record
 *
 * <AUTHOR>
 * @date 2021-08-31
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class LandpageFormRecordExcel implements Serializable {
    private static final long serialVersionUID = -498989488993326847L;

    /** 主键 */
    @ExcelIgnore
    private Long id;

    /** 广告ID */
    @ExcelProperty("广告ID")
    private Long advertId;

    /** 媒体名称 */
    @ExcelProperty("媒体名称")
    private String appName;

    /** 媒体ID */
    @ExcelProperty("媒体ID")
    private Long appId;

    /** 广告位ID */
    @ExcelProperty("广告位ID")
    private Long slotId;

    /** 广告位名称 */
    @ExcelProperty("广告位名称")
    private String slotName;

    /** 渠道 */
    @ExcelProperty("渠道")
    private Integer channel;

    /** 订单号 */
    @ExcelIgnore
    private String orderId;

    /** 上报广告主 */
    @ExcelProperty("上报广告主")
    private Long targetAdvertiserId;

    /** 上报广告主名称 */
    @ExcelProperty("上报广告主名称")
    private String targetAdvertiserName;

    /** 表单价格 */
    @ExcelProperty(value = "表单价格(元)", converter = FenToYuanConverter.class)
    private Integer formPrice;

    /** 运营商 */
    @ExcelProperty("运营商")
    private String isp;

    /** 上报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("上报时间")
    private Date callbackTime;

    /** 上报状态 */
    @ExcelProperty("订单状态")
    private String callbackStatus;

    /** 落地页链接 */
    @ExcelProperty("落地页链接")
    private String landpageUrl;

    /** 姓名 */
    @ExcelProperty("姓名")
    private String name;

    /** 手机号 */
    @ExcelProperty("手机号")
    private String phone;

    /** 省 */
    @ExcelProperty("地址(省)")
    private String province;

    /** 市 */
    @ExcelProperty("地址(市)")
    private String city;

    /** 区/县 */
    @ExcelProperty("地址(区)")
    private String district;

    /** 行政区划代码 */
    @ExcelProperty("行政区划代码")
    private String areaNum;

    /** 详细地址 */
    @ExcelProperty("详细地址")
    private String address;

    /** IP */
    @ExcelProperty("IP")
    private String ip;

    /** 身份证号 */
    @ExcelProperty("身份证号")
    private String idCard;

    @ExcelProperty("年龄")
    private Integer age;

    @ExcelProperty("操作人ID")
    private Long operatorId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("提交时间")
    private Date gmtCreate;

    @ExcelProperty("身份证信息校验状态")
    private String idCardAuditStr;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("后端转化")
    private String advertiserCallback;

    @ExcelProperty("身份证实名接口提供方")
    private String auditApiTypeStr;
}
