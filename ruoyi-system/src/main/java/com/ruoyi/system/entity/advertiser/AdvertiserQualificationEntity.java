package com.ruoyi.system.entity.advertiser;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主资质信息
 *
 * <AUTHOR>
 * @date 2022-4-28 17:11:16
 */
@Data
public class AdvertiserQualificationEntity implements Serializable {
    private static final long serialVersionUID = -1571465591192711099L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 资质名称
     */
    private String qualificationName;

    /**
     * 资质图
     */
    private String qualificationImg;

    /**
     * 营业执照有效期 9999-12-31代表永久
     */
    private Date expireTime;

    /**
     * 备注
     */
    private String remarkText;

    /**
     * 资质类型:0.默认资质,1.行业资质,2.自选资质
     */
    private Integer qualificationType;

    /**
     * 行业ID
     */
    private Long industryId;

    /**
     * 资质要求ID
     */
    private Long qualificationRequireId;

    /**
     * 提交审核时间
     */
    private Date applicationTime;

    /**
     * 审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer auditStatus;

    /**
     * 审核人ID
     */
    private Long auditor;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核理由
     */
    private String auditReason;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

