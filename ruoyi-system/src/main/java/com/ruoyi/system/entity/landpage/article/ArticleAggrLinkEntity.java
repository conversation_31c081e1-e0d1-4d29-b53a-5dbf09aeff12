package com.ruoyi.system.entity.landpage.article;

import lombok.Data;

import java.util.Date;

/**
 * 文章聚合链接表
 *
 * <AUTHOR>
 * @date 2023-12-1 15:12:44
 */
@Data
public class ArticleAggrLinkEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 链接唯一标识
     */
    private String key;

    /**
     * 链接名称
     */
    private String name;

    /**
     * 链接地址
     */
    private String url;

    /**
     * 返回拦截配置
     */
    private String retConfig;

    /**
     * 私域广告位
     */
    private String sySlot;

    /**
     * 状态:0.正常,1.已删除
     */
    private Integer status;

    /**
     * 广告主id
     */
    private Long advertiserId;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operatorTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

