package com.ruoyi.system.entity.blindbox;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 盲盒落地页表
 *
 * <AUTHOR>
 * @date 2022-6-13 11:10:01
 */
@Data
public class BlindBoxLandpageEntity implements Serializable {
    private static final long serialVersionUID = 6171258489622991306L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 落地页标识
     */
    private String landpageKey;

    /**
     * 广告主落地页
     */
    private String targetLandpage;

    /**
     * 底部文案
     */
    private String footer;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}

