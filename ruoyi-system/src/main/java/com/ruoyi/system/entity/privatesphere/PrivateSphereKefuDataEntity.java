package com.ruoyi.system.entity.privatesphere;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 私域客服数据表
 *
 * <AUTHOR>
 * @date 2023-3-6 18:51:08
 */
@Data
public class PrivateSphereKefuDataEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 客服渠道id
     */
    private Long kefuChannelId;
    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 产品id
     */
    private Long productId;
    /**
     * 客服账号id
     */
    private Long sspAccountId;

    /**
     * 外呼总数
     */
    private Integer callCount;

    /**
     * 接通数
     */
    private Integer connectCount;

    /**
     * 进入人工数
     */
    private Integer personCount;

    /**
     * 真实入群数
     */
    private Integer entryCount;

    /**
     * 反馈入群数
     */
    private Integer feedbackEntryCount;

    /**
     * 数据成本单价
     */
    private Float dataCost;

    /**
     * 数据成本数
     */
    private Integer dataCostCount;

    /**
     * AI+线路成本
     */
    private Integer lineCost;

    /**
     * 人工成本单价
     */
    private Integer personCost;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;


}

