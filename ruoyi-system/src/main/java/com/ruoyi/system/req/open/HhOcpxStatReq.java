package com.ruoyi.system.req.open;

import lombok.Data;

import java.io.Serializable;

/**
 * 辉煌OCPX曝光点击埋点参数
 *
 * <AUTHOR>
 * @date 2024/09/18
 */
@Data
public class HhOcpxStatReq implements Serializable {
    private static final long serialVersionUID = 8094631569425715347L;

    /**
     * 类型:1.曝光,2.点击
     */
    private Integer type;

    /**
     * 发生转化后的媒体回调地址
     */
    private String callbackUrl;

    /**
     * 链路编码，表明渠道侧和客户侧：示例009012，由辉煌明天提供
     */
    private String chainCode;

    /**
     * 客户端操作系统类型:0.代表安卓,1.代表iOS
     */
    private Integer os;

    /**
     * iOS设备广告标识 idfa
     */
    private String idfa;

    /**
     * iOS设备广告标识idfa的md5值小写
     */
    private String idfaMd5;

    /**
     * 安卓设备广告标识IMEI的md5值小写
     */
    private String imeiMd5;

    /**
     * 安卓设备广告标识oaid原值
     */
    private String oaid;

    /**
     * 安卓设备广告标识oaid的md5值小写
     */
    private String oaidMd5;

    /**
     * 点击数据上报时http的header中的user_agent
     */
    private String adAgent;

    /**
     * IP
     */
    private String  ip;

    /**
     * 广告点击时间,UTC时间戳,毫秒数;
     */
    private Long tms;

    /**
     * 广告主ID
     */
    private String aid;

    /**
     * 广告计划ID
     */
    private String campaignId;

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 接口版本
     */
    private String version;

    /**
     * 机型
     */
    private String  model;

    /**
     * 品牌
     */
    private String brand;
}
