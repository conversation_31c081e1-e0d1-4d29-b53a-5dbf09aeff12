package com.ruoyi.system.req.advert;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 广告配置列表查询条件
 *
 * <AUTHOR>
 * @date 2023/7/5
 */
@Data
public class AdvertOrientationListReq {

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告ID/广告名称搜索
     */
    private String advertSearch;

    /**
     * 配置ID/配置名称搜索
     */
    private String orientSearch;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 代理商ID列表
     */
    private List<Long> agentIds;

    /**
     * 创建时间起始
     */
    private Date gmtCreateStart;

    /**
     * 创建时间截止
     */
    private Date gmtCreateEnd;

    /**
     * 创建人名称
     */
    private String operatorName;
}
