package com.ruoyi.system.req.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 广告位切量请求参数
 *
 * <AUTHOR>
 * @date 2022-02-28
 */
@Data
public class SlotCutTaskReq {

    /**
     * 广告位
     */
    private Long slotId;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveTime;

    /**i
     * 切换的活动ID或者活动链接
     */
    private String redirectValue;

    /**
     * 切换的pv阈值
     */
    private Long threshold;

    /**
     * 循环天数
     */
    private Integer days;
}
