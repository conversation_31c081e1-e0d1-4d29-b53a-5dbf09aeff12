package com.ruoyi.system.req.wis.article;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 文章聚合链接列表查询请求参数
 *
 * <AUTHOR>
 * @date 2023/12/04
 */
@Data
public class WisArticleAggrLinkAddReq implements Serializable {

    private static final long serialVersionUID = -9035310487423312894L;
    /**
     * 投放链接名称
     */
    @NotEmpty(message = "投放链接不能为空")
    private String name;
    /**
     * 链接id
     */
    private Long id;
}
