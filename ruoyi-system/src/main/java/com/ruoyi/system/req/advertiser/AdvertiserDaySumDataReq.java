package com.ruoyi.system.req.advertiser;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告位日数据请求参数
 *
 * <AUTHOR>
 * @date 2022/7/14
 */
@Data
public class AdvertiserDaySumDataReq implements Serializable {
    private static final long serialVersionUID = -6042855573895106023L;

    /**
     * 起始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;
}
