package com.ruoyi.system.req.advert;

import java.util.Date;

/**
 * 广告广告位维度日数据请求参数
 *
 * <AUTHOR>
 * @date 2021/10/18
 */
public class AdvertSlotDayDataReq {

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 媒体搜索
     */
    private String appSearch;

    /**
     * 广告位搜索
     */
    private String slotSearch;

    public Long getAdvertId() {
        return advertId;
    }

    public void setAdvertId(Long advertId) {
        this.advertId = advertId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getAppSearch() {
        return appSearch;
    }

    public void setAppSearch(String appSearch) {
        this.appSearch = appSearch;
    }

    public String getSlotSearch() {
        return slotSearch;
    }

    public void setSlotSearch(String slotSearch) {
        this.slotSearch = slotSearch;
    }
}
