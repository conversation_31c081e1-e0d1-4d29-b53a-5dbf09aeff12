package com.ruoyi.system.req.advert;

import com.ruoyi.common.enums.advert.DeviceTargetType;
import com.ruoyi.common.enums.advert.FlowTargetType;
import com.ruoyi.common.enums.advert.IspTargetType;
import com.ruoyi.common.enums.advert.OsTargetType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 广告定向投放配置批量修改请求参数
 *
 * <AUTHOR> <PERSON>
 * @Date 2023/9/25 16:34
 */

@Data
public class AdvertOrientationBatchModifyReq implements Serializable {

    private static final long serialVersionUID = -3360662331286272307L;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 配置ID列表
     */
    private List<Long> orientIds;

    /**
     * 地域定向
     */
    private Set<String> areaTarget;

    /**
     * 设备定向列表
     *
     * @see DeviceTargetType
     */
    private List<Integer> deviceTargets;

    /**
     * 运营商定向列表
     *
     * @see IspTargetType
     */
    private List<Integer> ispTargets;

    /**
     * 系统定向列表
     *
     * @see OsTargetType
     */
    private List<Integer> osTargets;

    /**
     * 流量定向列表
     *
     * @see FlowTargetType
     */
    private List<Integer> flowTargets;


    /**
     * 投放时段
     */
    private List<Integer> servingHours;
}
