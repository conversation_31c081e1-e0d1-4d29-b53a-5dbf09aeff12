package com.ruoyi.system.req.permission;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 更新职位权限请求参数
 *
 * <AUTHOR>
 * @date 2022/6/24 3:29 下午
 */
@Data
public class UpdatePostPermissionParam implements Serializable {
    private static final long serialVersionUID = -4591094889731811606L;

    /**
     * 职位id
     */
    @NotNull(message = "职位id不能为空")
    private Long postId;

    /**
     * 父权限ID
     */
    @NotNull(message = "父权限ID不能为空")
    private Long parentId;

    /**
     * 权限id列表
     */
    private List<Long> permissionIds;
}
