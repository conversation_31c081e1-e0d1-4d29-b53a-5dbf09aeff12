package com.ruoyi.system.req.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告主消费记录请求参数
 *
 * <AUTHOR>
 * @date 2021/8/19
 */
@Data
public class AdvertiserConsumeReq implements Serializable {
    private static final long serialVersionUID = 6301647665393789955L;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 代理商ID列表
     */
    private List<Long> agentIds;

    /**
     * 起始日期
     */
    private Date startDate;

    /**
     * 截止日期
     */
    private Date endDate;
}
