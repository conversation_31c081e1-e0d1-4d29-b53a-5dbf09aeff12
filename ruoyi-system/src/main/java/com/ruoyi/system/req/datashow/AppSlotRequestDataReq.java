package com.ruoyi.system.req.datashow;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 媒体反馈广告位数据展示请求参数
 *
 * <AUTHOR>
 * @date 2021/9/1 2:09 下午
 */
@Data
public class AppSlotRequestDataReq implements Serializable {
    private static final long serialVersionUID = 8723185001517002037L;

    @NotNull(message = "id")
    private Long id;
    /**
     * 广告位点击pv(媒体反馈)
     */
    @NotNull(message = "广告位点击pv不能为空")
    private Integer appSlotClickPv;
    /**
     * 广告位点击uv(媒体反馈)
     */
    @NotNull(message = "广告位点击uv不能为空")
    private Integer appSlotClickUv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    @NotNull(message = "广告位曝光pv不能为空")
    private Integer appSlotExposurePv;
    /**
     * 广告位曝光uv(媒体反馈)
     */
    @NotNull(message = "广告位曝光uv不能为空")
    private Integer appSlotExposureUv;
}
