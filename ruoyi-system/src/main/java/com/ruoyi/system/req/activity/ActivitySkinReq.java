package com.ruoyi.system.req.activity;

import lombok.Data;

/**
 * 活动皮肤请求参数
 *
 * <AUTHOR>
 * @date 2023/05/08
 */
@Data
public class ActivitySkinReq {

    /**
     * 皮肤ID
     */
    private Long id;

    /**
     * 皮肤标识
     */
    private String skinCode;

    /**
     * 皮肤名称
     */
    private String skinName;

    /**
     * 活动皮肤类型:1.套猫,2.大转盘,3.卡包
     */
    private Integer skinType;

    /**
     * 活动类型:1.轻互动活动
     */
    private Integer activityType;

    /**
     * 缩略图
     */
    private String thumbnailImage;

    /**
     * 配置模板
     */
    private String jsTemplate;

    /**
     * 皮肤配置：全局配置，所有活动生效
     */
    private String globalConfig;

    /**
     * 皮肤配置：copy到活动，活动会自己修改
     */
    private String skinConfig;

    /**
     * 跳转路径
     */
    private String redirectPath;
}
