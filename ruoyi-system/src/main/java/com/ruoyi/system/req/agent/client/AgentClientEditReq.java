package com.ruoyi.system.req.agent.client;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 代理商修改客户请求参数
 *
 * <AUTHOR>
 * @date 2022/10/26
 */
@Data
public class AgentClientEditReq {

    /**
     * 广告主ID
     */
    @NotNull(message = "广告主ID不能为空")
    private Long advertiserId;

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    private String email;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空")
    private String companyName;

    /**
     * 营业执照注册号
     */
    @NotBlank(message = "营业执照注册号不能为空")
    private String businessLicense;

    /**
     * 营业执照图片
     */
    @NotBlank(message = "营业执照未上传")
    private String businessLicenseImg;

    /**
     * 公司地址
     */
    @NotBlank(message = "公司地址不能为空")
    private String address;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空")
    private String contact;

    /**
     * 手机
     */
    @NotBlank(message = "手机不能为空")
    private String phone;
}
