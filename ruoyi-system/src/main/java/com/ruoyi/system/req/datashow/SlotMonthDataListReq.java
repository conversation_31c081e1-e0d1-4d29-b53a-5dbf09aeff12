package com.ruoyi.system.req.datashow;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 修改月账单
 * <AUTHOR>
 * @date 2022/1/6 11:56 上午
 */
@Data
public class SlotMonthDataListReq implements Serializable {
    private static final long serialVersionUID = -9025262737007100858L;
    /**
     * 媒体id
     */
    @NotNull(message = "媒体id不能为空")
    private Long appId;
    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private Date startDate;
    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private Date endDate;
    /**
     * 广告位id列表
     */
    private List<Long> slotIds;
}
