package com.ruoyi.system.req.account;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 账号标签更新请求参数
 * <AUTHOR>
 * @date 2023/3/6 10:57
 */
@Data
public class AccountTagUpdateReq implements Serializable {
    private static final long serialVersionUID = -8195960302265027926L;
    /**
     * 账号id
     */
    @NotNull(message = "账号id不能为空")
    private Long accountId;
    /**
     * 标签类型
     */
    @NotNull(message = "标签类型不能为空")
    private Integer tagType;
    /**
     * 标签id列表
     */
    private List<Long> tagIds;
}
