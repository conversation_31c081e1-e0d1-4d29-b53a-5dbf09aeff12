package com.ruoyi.system.req.callback;

import lombok.Data;

import java.io.Serializable;

/**
 * 枫雪云被动接收上行和状态报告
 * <AUTHOR>
 * @date 2022/10/28 2:30 下午
 */
@Data
public class SmsCallbackFengXueYunReq implements Serializable {
    private static final long serialVersionUID = -6171855704403915971L;
    /**
     * 0=上行 1=状态报告
     */
    private Integer flag;
    /**
     * 消息id
     * flag=1时，消息ID与提交时响应的消息ID匹配；flag=0时，表示该条上行的消息ID
     */
    private String mid;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 状态
     */
    private String stat;

}
