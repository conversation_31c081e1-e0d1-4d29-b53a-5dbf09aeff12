package com.ruoyi.system.req.slot;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位数据更新请求参数
 *
 * <AUTHOR>
 * @date 2022/03/09
 */
@Data
public class SlotDataUpdateReq implements Serializable {
    private static final long serialVersionUID = -5809736121866744081L;

    /**
     * 广告位数据ID
     */
    private Long slotDataId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;
    /**
     * 广告位点击pv(媒体反馈)
     */
    private Integer appSlotClickPv;
    /**
     * 广告位点击uv(媒体反馈)
     */
    private Integer appSlotClickUv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposurePv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposureUv;

    /**
     * 诺禾结算款
     */
    private Long nhCost;

    /**
     * 外部结算款
     */
    private Long outerCost;

    /**
     * 修改来源(1.运营每日任务,2.媒体广告位数据修改,3.广告位列表-结算设置,4.媒体月账单-调账)
     */
    private Integer source;
}
