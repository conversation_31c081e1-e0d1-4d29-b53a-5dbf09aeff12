package com.ruoyi.system.req.manager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 域名替换参数
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Data
public class DomainReplaceReq implements Serializable {
    private static final long serialVersionUID = 2294293712419707881L;

    /**
     * 广告位ID列表（逗号分隔）
     */
    @NotBlank(message = "广告位ID不能为空")
    private String slotIds;

    /**
     * 域名类型
     */
    @NotNull(message = "域名类型不能为空")
    private Integer domainType;

    /**
     * 替换域名
     */
    @NotBlank(message = "替换域名不能为空")
    private String domain;
}
