package com.ruoyi.system.req.advertiser.qualification;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * [代理商平台]客户信息修改参数
 *
 * <AUTHOR>
 * @date 2023/03/29
 */
@Data
public class WisAgentCompanyInfoReq {

    /**
     * 营业执照注册号
     */
    private String businessLicense;

    /**
     * 营业执照照片
     */
    private String businessLicenseImg;

    /**
     * 营业执照有效期 9999-12-31代表永久
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireTime;

    /**
     * 联系人名称
     */
    private String contract;

    /**
     * 手机号码
     */
    private String phone;
}
