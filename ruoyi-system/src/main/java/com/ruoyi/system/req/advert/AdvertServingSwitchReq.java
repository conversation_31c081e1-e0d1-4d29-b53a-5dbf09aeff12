package com.ruoyi.system.req.advert;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 广告投放开关请求参数
 *
 * <AUTHOR>
 * @date 2022/03/28
 */
@Data
public class AdvertServingSwitchReq implements Serializable {
    private static final long serialVersionUID = 6634140709780680897L;

    /**
     * 广告ID
     */
    @NotNull(message = "广告ID不能为空")
    private Long id;

    /**
     * 投放开关:0.关闭,1.开启
     */
    @NotNull(message = "投放开关不能为空")
    private Integer servingSwitch;
}
