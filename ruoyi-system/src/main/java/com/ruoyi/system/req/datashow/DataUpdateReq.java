package com.ruoyi.system.req.datashow;

import com.ruoyi.common.enums.InnerLogType;

import java.util.Date;

/**
 * 数据更新参数
 *
 * <AUTHOR>
 * @date 2021/7/22
 */
public class DataUpdateReq {

    /**
     * 埋点类型
     */
    private InnerLogType type;

    /**
     * 日期
     */
    private Date date;

    /**
     * 日期
     */
    private String dateStr;

    /**
     * 小时
     */
    private Integer hour;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 素材ID
     */
    private Long materialId;

    /**
     * 增加的PV
     */
    private int pv;

    /**
     * 增加的广告位UV
     */
    private int slotUv;

    /**
     * 增加的媒体UV
     */
    private int appUv;

    /**
     * 增加的小时广告位UV
     */
    private int hourSlotUv;


    public InnerLogType getType() {
        return type;
    }

    public void setType(InnerLogType type) {
        this.type = type;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public Integer getHour() {
        return hour;
    }

    public void setHour(Integer hour) {
        this.hour = hour;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getSlotId() {
        return slotId;
    }

    public void setSlotId(Long slotId) {
        this.slotId = slotId;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getAdvertId() {
        return advertId;
    }

    public void setAdvertId(Long advertId) {
        this.advertId = advertId;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    public int getPv() {
        return pv;
    }

    public void setPv(int pv) {
        this.pv = pv;
    }

    public int getSlotUv() {
        return slotUv;
    }

    public void setSlotUv(int slotUv) {
        this.slotUv = slotUv;
    }

    public int getAppUv() {
        return appUv;
    }

    public void setAppUv(int appUv) {
        this.appUv = appUv;
    }

    public int getHourSlotUv() {
        return hourSlotUv;
    }

    public void setHourSlotUv(int hourSlotUv) {
        this.hourSlotUv = hourSlotUv;
    }
}
