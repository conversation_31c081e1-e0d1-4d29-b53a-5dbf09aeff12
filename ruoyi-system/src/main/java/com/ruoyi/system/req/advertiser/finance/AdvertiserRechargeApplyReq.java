package com.ruoyi.system.req.advertiser.finance;

import com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 广告主充值申请参数
 *
 * <AUTHOR>
 * @date 2022/3/21 1:58 下午
 */
@Data
public class AdvertiserRechargeApplyReq implements Serializable {
    private static final long serialVersionUID = 8457228677544572375L;

    /**
     * 广告主id
     */
    @NotNull(message = "广告主不能为空")
    private Long accountId;

    /**
     * 充值类型
     * @see AdvertiserRechargeTypeEnum
     */
    @NotNull(message = "充值类型不能为空")
    private Integer rechargeType;

    /**
     * 充值金额(分)
     */
    @NotNull(message = "充值金额不能为空")
    private Integer rechargeAmount;

    /**
     * 备注
     */
    private String remark;
}
