package com.ruoyi.system.req.invoice;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 发票新增请求参数
 *
 * <AUTHOR>
 * @date 2022/10/20 2:41 下午
 */
@Data
public class InvoiceListReq implements Serializable {
    private static final long serialVersionUID = -7441539289976897595L;

    /**
     * 账号Id/邮箱搜索
     */
    private String accountSearch;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 状态，1欠票，2已结算
     */
    private Integer status;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;
}
