package com.ruoyi.system.req.slot;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位返回挽留配置请求参数
 *
 * <AUTHOR>
 * @date 2021-12-28
 */
@Data
public class RetConfigReq implements Serializable {
    private static final long serialVersionUID = 8574470243091460861L;

    /**
     * 返回挽留开关(1.开启,0.关闭)
     */
    private Integer isOpen;

    /**
     * 插件类型
     */
    private Integer pluginType;

    /**
     * 链接
     */
    private String url;

    /**
     * 活动ID
     */
    private Long activityId;
}
