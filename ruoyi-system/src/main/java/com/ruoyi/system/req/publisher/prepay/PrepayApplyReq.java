package com.ruoyi.system.req.publisher.prepay;

import com.ruoyi.system.bo.invoice.InvoiceUrlBO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 媒体预付款申请参数
 *
 * <AUTHOR>
 * @date 2022-07-29
 */
@Data
public class PrepayApplyReq {

    /**
     * 媒体账号ID
     */
    @NotNull(message = "媒体账号ID不能为空")
    private Long accountId;

    /**
     * 预付款申请金额(分)
     */
    @NotNull(message = "预付款申请金额不能为空")
    private Integer applyPrepayAmount;

    /**
     * 发票列表
     */
    private List<InvoiceUrlBO> invoiceList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预付款主体，多个逗号隔开
     */
    @NotEmpty(message = "预付款主体不能为空")
    private List<Integer> prepaySubjectList;
}
