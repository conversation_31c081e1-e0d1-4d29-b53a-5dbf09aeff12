package com.ruoyi.system.req.manager;

import lombok.Data;

import java.io.Serializable;

/**
 * 域名请求参数
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Data
public class DomainListReq implements Serializable {
    private static final long serialVersionUID = -334914779309721192L;

    /**
     * 域名
     */
    private String domain;

    /**
     * 域名类型
     */
    private Integer domainType;

    /**
     * 微信状态
     */
    private Integer wxStatus;

    /**
     * 支付宝状态
     */
    private Integer alipayStatus;

    /**
     * 备案主体
     */
    private String icpSubject;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序字段
     */
    private String orderColumn;

    /**
     * 是否升序
     */
    private Boolean isAsc;

    /**
     * 广告位ID
     */
    private Long slotId;
}
