package com.ruoyi.system.req.landpage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 落地页行为埋点请求参数
 *
 * <AUTHOR>
 * @date 2022/09/22
 */
@Data
public class LandpageDataReq implements Serializable {
    private static final long serialVersionUID = -1423176157849018457L;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 落地页搜索
     */
    private String landpageSearch;

    /**
     * 落地页标识列表
     */
    private List<String> landpageKeyList;
}
