package com.ruoyi.system.req.advertiser.qualification;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * [广告主/代理商平台]资质信息参数
 *
 * <AUTHOR>
 * @date 2022/4/29 4:32 下午
 */
@Data
public class WisQualificationInfoReq implements Serializable {
    private static final long serialVersionUID = -9196158100106686157L;

    /**
     * 资质ID
     */
    private Long qualificationId;

    /**
     * 资质名称
     */
    private String qualificationName;

    /**
     * 资质图
     */
    private List<String> qualificationImgs;

    /**
     * 营业执照有效期 9999-12-31代表永久
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireTime;

    /**
     * 资质要求ID
     */
    private Long qualificationRequireId;
}
