package com.ruoyi.system.req.tag;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 标签管理编辑请求参数
 * <AUTHOR>
 * @date 2022/9/23 2:03 下午
 */
@Data
public class TagManagerReq implements Serializable {
    private static final long serialVersionUID = 1850032496480772225L;

    /**
     * 标签类型
     * @see com.ruoyi.common.enums.TagManagerTypeEnum
     */
    @NotNull(message = "标签类型不能为空")
    private Integer tagType;
    /**
     * 一级标签列表
     */
    private List<TagEditReq> tabList;
}
