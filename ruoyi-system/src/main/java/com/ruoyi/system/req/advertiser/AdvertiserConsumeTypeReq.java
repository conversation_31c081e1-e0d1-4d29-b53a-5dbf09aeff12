package com.ruoyi.system.req.advertiser;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 广告主结算类型请求参数
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
@Data
public class AdvertiserConsumeTypeReq {

    /**
     * 广告主ID
     */
    @NotNull(message = "广告主不能为空")
    private Long advertiserId;

    /**
     * 结算类型
     */
    @NotNull(message = "结算类型不能为空")
    private Integer consumeType;
}
