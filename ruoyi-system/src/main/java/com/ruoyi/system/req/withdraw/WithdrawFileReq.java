package com.ruoyi.system.req.withdraw;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 提现单上传
 * <AUTHOR>
 * @date 2021/9/14 7:38 下午
 */
@Data
public class WithdrawFileReq implements Serializable {
    private static final long serialVersionUID = 1100857257694736280L;
    /**
     * 提现记录id
     */
    @NotNull(message = "提现id不能为空")
    private Long id;
    /**
     * 提现单文件地址
     */
    @NotNull(message = "提现单文件不能为空")
    private String withdrawFile;
}
