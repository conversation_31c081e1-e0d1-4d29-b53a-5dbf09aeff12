package com.ruoyi.system.req.privatesphere.product;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 私域产品新增参数
 * <AUTHOR>
 * @date 2023/2/8 10:29
 */
@Data
public class PrivateSphereProductAddReq implements Serializable {
    private static final long serialVersionUID = 1481720438117314701L;
    /**
     * 公司id
     */
    @NotNull(message = "账号id不能为空")
    private Long accountId;
    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String productName;
}
