package com.ruoyi.system.req.slot;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 解析广告位数据excel参数
 * <AUTHOR>
 * @date 2023/5/24 15:09
 */
@Data
public class SlotDataAnalysisExcelReq implements Serializable {
    private static final long serialVersionUID = -4645282320213465439L;
    @NotNull(message = "广告位id不能为空")
    private Long slotId;
    private MultipartFile file;
}
