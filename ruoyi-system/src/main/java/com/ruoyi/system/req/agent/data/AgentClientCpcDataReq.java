package com.ruoyi.system.req.agent.data;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主cpc数据请求参数
 *
 * <AUTHOR>
 * @date 2022/4/7 7:57 下午
 */
@Data
public class AgentClientCpcDataReq implements Serializable {
    private static final long serialVersionUID = 7366112684407091183L;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 起始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;
}
