package com.ruoyi.system.req.open;

import lombok.Data;

/**
 * APP广告主回调请求参数
 *
 * <AUTHOR>
 * @date 2023/06/09
 */
@Data
public class AppAdvertiserCallbackReq {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * OAID的MD5
     */
    private String oaidMd5;

    /**
     * IMEI的MD5
     */
    private String imeiMd5;

    /**
     * IDFA的MD5
     */
    private String idfaMd5;

    /**
     * 客户端IP
     */
    private String ip;

    /**
     * 客户端userAgent
     */
    private String userAgent;

    /**
     * 系统类型:Android/iOS
     */
    private String osType;

    /**
     * 系统版本
     */
    private String osVersion;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * 广告主标识
     */
    private String accessKey;

    /**
     * 转化类型
     * @see com.ruoyi.common.enums.advert.ConvType
     */
    private Integer status;

    /**
     * 毫秒时间戳(毫秒)
     */
    private Long timestamp;
}
