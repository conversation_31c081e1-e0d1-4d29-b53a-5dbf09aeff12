package com.ruoyi.system.req.withdraw;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 提现申请请求参数
 * <AUTHOR>
 * @date 2021/9/15 5:12 下午
 */
@Data
public class WithdrawApplyReq implements Serializable {
    private static final long serialVersionUID = 6757046469944315361L;

    /**
     * 媒体月账单id列表
     */
    @Size(min = 1,message = "媒体月账单id不能为空")
    private List<Long> appMonthDataIds;

    /**
     * 提现金额
     */
    @Min(value = 10000)
    private Integer withdrawAmount;
    /**
     * 提现记录id(重新提交时传递)
     */
    private Long id;

}
