package com.ruoyi.system.req.privatesphere.data;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 数据列表查询
 *
 * <AUTHOR>
 * @date 2023/2/9 11:09
 */
@Data
public class PrivateSphereDataListReq implements Serializable {
    private static final long serialVersionUID = -2655014999036039623L;

    /**
     * 公司ID列表
     */
    private List<Long> accountIds;

    /**
     * 产品ID列表
     */
    private List<Long> productIds;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 新增起始时间
     */
    private Date startDate;

    /**
     * 新增结束时间
     */
    private Date endDate;
}
