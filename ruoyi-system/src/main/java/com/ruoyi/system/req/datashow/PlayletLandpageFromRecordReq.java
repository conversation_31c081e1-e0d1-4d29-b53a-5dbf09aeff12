package com.ruoyi.system.req.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 短剧落地页表单记录请求参数
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Data
public class PlayletLandpageFromRecordReq implements Serializable {
    private static final long serialVersionUID = 3617484588459091837L;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;

    /**
     * 订单状态:0.未支付,1.支付成功
     */
    private Integer tradeStatus;

    /**
     * 交易单号
     */
    private String transactionId;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 支付通道:1.微信,2.支付宝
     */
    private Integer payPlatform;
}
