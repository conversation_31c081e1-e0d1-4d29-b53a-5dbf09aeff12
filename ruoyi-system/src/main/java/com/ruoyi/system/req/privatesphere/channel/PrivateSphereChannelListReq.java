package com.ruoyi.system.req.privatesphere.channel;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 渠道列表查询
 *
 * <AUTHOR>
 * @date 2023/2/9 11:09
 */
@Data
public class PrivateSphereChannelListReq implements Serializable {
    private static final long serialVersionUID = 515614948564147035L;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 渠道名称
     */
    private String channel;

    /**
     * 产品ID列表
     */
    private List<Long> productIds;

    /**
     * 新增起始时间
     */
    private Date startDate;

    /**
     * 新增结束时间
     */
    private Date endDate;
}
