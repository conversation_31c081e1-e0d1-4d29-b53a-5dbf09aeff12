package com.ruoyi.system.req.advert;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 配置CPC修改参数
 *
 * <AUTHOR>
 * @date 2023/07/18
 */
@Data
public class AdvertOrientationCpcModifyReq implements Serializable {
    private static final long serialVersionUID = 6613496662999396962L;

    /**
     * 配置ID
     */
    private Long id;

    /**
     * CPC价格(分)
     */
    private Integer unitPrice;

    /**
     * CPC价格(毫)
     */
    private Integer milliUnitPrice;
}
