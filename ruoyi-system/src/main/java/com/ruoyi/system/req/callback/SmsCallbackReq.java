package com.ruoyi.system.req.callback;

import lombok.Data;

import java.io.Serializable;

/**
 * 短信回调请求参数
 * <AUTHOR>
 * @date 2022/12/7 11:45
 */
@Data
public class SmsCallbackReq implements Serializable {
    private static final long serialVersionUID = 1564847125927857953L;
    /**
     * 消息id
     */
    private String msgId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 回复时间
     */
    private String reportTime;
    /**
     * 状态码
     */
    private String code;
    /**
     * 状态描述
     */
    private String msg;
    /**
     * 自定义信息 消息发送记录id
     */
    private String extend;
}
