package com.ruoyi.system.req.datashow;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 数据更新参数
 *
 * <AUTHOR>
 * @date 2021/09/29
 */
@Data
public class DataStatReq implements Serializable {
    private static final long serialVersionUID = -5903589385239651718L;

    /**
     * 日期
     */
    private Date date;

    /**
     * 日期
     */
    private String dateStr;

    /**
     * 小时
     */
    private Integer hour;

    /**
     * 时刻,当日分钟/15
     */
    private Integer quarter;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 插件ID
     */
    private Long pluginId;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 素材ID
     */
    private Long materialId;

    /**
     * 计费类型
     */
    private Integer chargeType;

    /**
     * 广告单价
     */
    private Integer unitPrice;

    /**
     * 考核成本(分)
     */
    private Integer assessCost;

    /**
     * 落地页标识
     */
    private String landpageKey;

    /**
     * 转化类型
     */
    private Integer convType;

    /**
     * 转化扩展参数
     */
    private JSONObject convExt;

    /**
     * 设备型号
     */
    private String mobileModel;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 其他参数
     */
    private Map<String, Object> otherParam;
}
