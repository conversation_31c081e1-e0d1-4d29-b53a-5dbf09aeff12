package com.ruoyi.system.req.advert;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 广告CPC修改参数
 *
 * <AUTHOR>
 * @date 2022/06/20
 */
@Data
public class AdvertCpcModifyReq implements Serializable {
    private static final long serialVersionUID = -2453502341439322705L;

    /**
     * 广告id
     */
    private Long id;

    /**
     * CPC价格(分)
     */
    private Integer unitPrice;

    /**
     * CPC价格(毫)
     */
    private Integer milliUnitPrice;
}
