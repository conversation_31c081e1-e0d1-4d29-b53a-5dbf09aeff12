package com.ruoyi.system.req.wis.article;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 文章列表查询请求参数
 *
 * <AUTHOR>
 * @date 2023/12/01
 */
@Data
public class WisArticleListReq implements Serializable {
    private static final long serialVersionUID = 110505157857272212L;

    /**
     * 文章聚合链接ID
     */
    @NotNull(message = "链接ID不能为空")
    private Long linkId;

    /**
     * 名称/链接查询
     */
    private String name;

    /**
     * 链接
     */
    private String url;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;
}
