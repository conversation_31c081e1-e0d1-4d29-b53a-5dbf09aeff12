package com.ruoyi.system.req.sms;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 留资短信配置详情
 *
 * <AUTHOR>
 * @date 2022/12/5 14:39
 */
@Data
public class LiuziSmsConfigInfoReq implements Serializable {
    private static final long serialVersionUID = -1401396243847212820L;

    /**
     * 自建站类型
     * @see com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum
     */
    @NotNull(message = "自建站类型不能为空")
    private Integer landpageType;

    /**
     * 配置类型，1默认配置，2自定义配置
     * @see com.ruoyi.common.enums.landpage.LiuziSmsConfigTypeEnum
     */
    @NotNull(message = "配置类型不能为空")
    private Integer configType;

    /**
     * 短信内容
     */
    @NotNull(message = "短信内容不能为空")
    private String content;

    /**
     * 推广页id，默认传0
     */
    @NotNull(message = "推广页id不能为空")
    private List<Long> advertIds;

    /**
     * 短信模版id
     */
    @Deprecated
    private Long tpId;

    /**
     * 企微兜底开关:true.开启,false.关闭
     */
    private Boolean qwSwitch;
}
