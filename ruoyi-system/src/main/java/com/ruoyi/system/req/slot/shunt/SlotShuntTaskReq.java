package com.ruoyi.system.req.slot.shunt;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 广告位切量计划请求参数
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
@Data
public class SlotShuntTaskReq implements Serializable {
    private static final long serialVersionUID = 4466471326147774893L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告位ID
     */
    @NotNull(message = "广告位ID不能为空")
    private Long slotId;

    /**
     * 切量类型:1.pv,2.uv
     */
    @NotNull(message = "切量类型不能为空")
    private Integer shuntType;

    /**
     * 计划名称
     */
    @NotBlank(message = "计划名称不能为空")
    private String taskName;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 切量类型(活动投放类型)
     * @see com.ruoyi.common.enums.slot.SlotRedirectType
     */
    @NotNull(message = "切量类型不能为空")
    private Integer redirectType;

    /**
     * 活动ID、链接或者广告ID
     */
    @NotBlank(message = "投放值不能为空")
    private String redirectValue;

    /**
     * 分流比例[0,100]
     */
    @NotNull(message = "分流比例不能为空")
    @Min(value = 0, message = "分流比例不能为负数")
    @Max(value = 100, message = "分流比例不能超过100%")
    private Integer shuntRatio;

    /**
     * 切量上限
     */
    @NotNull(message = "切量上限不能为空")
    @Min(value = 0, message = "切量上限不能为负数")
    private Long threshold;
}
