package com.ruoyi.system.req.advertiser.finance;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告主消费记录更新请求参数
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
@Data
public class AdvertiserConsumeRecordUpdateReq implements Serializable {
    private static final long serialVersionUID = -7119413290030678487L;

    /**
     * 广告主消费记录表ID
     */
    private Long id;

    /**
     * 消费金额增量(分)
     */
    private Integer consumeAmountAdd;
}
