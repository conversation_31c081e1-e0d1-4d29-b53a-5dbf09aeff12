package com.ruoyi.system.req.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告主消费记录列表请求参数
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@Data
public class AdvertiserConsumeListReq implements Serializable {
    private static final long serialVersionUID = -4676692348107269601L;

    /**
     * 广告主id
     */
    private Long accountId;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 不可见日期列表
     */
    private List<Date> invisibleDateList;

    /**
     * 广告主ID列表
     */
    private List<Long> accountIds;
}
