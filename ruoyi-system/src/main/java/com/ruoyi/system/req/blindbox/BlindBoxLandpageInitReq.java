package com.ruoyi.system.req.blindbox;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 盲盒落地页初始化请求参数
 *
 * <AUTHOR>
 * @date 2022/06/13
 */
@Data
public class BlindBoxLandpageInitReq implements Serializable {
    private static final long serialVersionUID = -5946192623442911238L;

    /**
     * 落地页标识
     */
    @NotBlank(message = "落地页标识不能为空")
    private String landpageKey;

    /**
     * 订单号
     */
    private String orderId;
}
