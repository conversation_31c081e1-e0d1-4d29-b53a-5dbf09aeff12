package com.ruoyi.system.req.advertiser.finance;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 广告主消费数据更新请求参数
 *
 * <AUTHOR>
 * @date 2022/07/15
 */
@Data
public class AdvertiserConsumeUpdateReq implements Serializable {
    private static final long serialVersionUID = 836404149516249798L;

    /**
     * 广告主ID
     */
    @NotNull(message = "广告主ID不能为空")
    private Long advertiserId;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空")
    private Date curDate;

    /**
     * 计费点击PV
     */
    @NotNull(message = "计费点击PV不能为空")
    private Integer billingClickPv;

    /**
     * 计费点击UV
     */
    @NotNull(message = "计费点击UV不能为空")
    private Integer billingClickUv;

    /**
     * 总消费(分)
     */
    @NotNull(message = "总消费不能为空")
    private Integer consumeAmount;
}
