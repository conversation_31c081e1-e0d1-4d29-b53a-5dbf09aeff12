package com.ruoyi.system.req.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 广告主广告关闭请求参数
 *
 * <AUTHOR>
 * @date 2022/03/28
 */
@Data
public class AdvertiserAdvertCloseReq implements Serializable {
    private static final long serialVersionUID = -1504190556591626385L;

    /**
     * 广告主ID
     */
    @NotNull(message = "广告主ID不能为空")
    private Long advertiserId;

    /**
     * 广告状态
     * @see com.ruoyi.common.enums.common.SwitchStatusEnum
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 指定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planTime;
}
