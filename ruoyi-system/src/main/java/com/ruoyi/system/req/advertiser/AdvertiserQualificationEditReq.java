package com.ruoyi.system.req.advertiser;

import com.ruoyi.system.req.advertiser.qualification.WisQualificationInfoReq;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 广告主资质编辑请求参数
 *
 * <AUTHOR>
 * @date 2022/4/28 5:54 下午
 */
@Data
public class AdvertiserQualificationEditReq implements Serializable {
    private static final long serialVersionUID = -2719137305042383341L;

    /**
     * 资质列表
     */
    private List<WisQualificationInfoReq> qualificationList;
}
