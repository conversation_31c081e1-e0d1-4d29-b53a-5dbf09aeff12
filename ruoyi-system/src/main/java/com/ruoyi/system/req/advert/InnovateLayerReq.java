package com.ruoyi.system.req.advert;

import com.ruoyi.system.entity.advert.InnovateLayerInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * 创新弹层请求参数
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
@Data
public class InnovateLayerReq implements Serializable {
    private static final long serialVersionUID = 6663761609999851591L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 弹层名称
     */
    private String layerName;

    /**
     * 弹层皮肤编号
     */
    private String skinCode;

    /**
     * 背景图
     */
    private String bgImg;

    /**
     * 轮播GIF图
     */
    private String gifImg;

    /**
     * 弹层信息
     */
    private InnovateLayerInfo layerInfo;
}
