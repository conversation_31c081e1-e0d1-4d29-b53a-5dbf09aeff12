package com.ruoyi.system.req.advertiser;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 账号管理请求参数
 *
 * <AUTHOR>
 * @date 2021/10/8
 */
@Data
public class AdvertiserReq {

    /**
     * 账号类型(搜索用,1.直客,2.代理商,3.子广告主)
     */
    private Integer accountType;

    /**
     * 账号类型
     */
    private Integer mainType;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 代理商ID列表
     */
    private List<Long> agentIds;

    /**
     * 注册时间起始
     */
    private Date startDate;

    /**
     * 注册时间截止
     */
    private Date endDate;

    /**
     * 负责人查询
     */
    private List<Long> managerIds;

    /**
     * 合同状态
     * @see com.ruoyi.common.enums.contract.ContractStatusEnum
     */
    private Integer contractStatus;

    /**
     * 资质审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer qualificationAuditStatus;
}
