package com.ruoyi.system.req.slot;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位数据校准设置请求
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Data
public class SlotAdjustSwitchReq implements Serializable {
    private static final long serialVersionUID = -4733927317012166717L;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 状态:1.开启,0.关闭
     */
    private Integer status;
}
