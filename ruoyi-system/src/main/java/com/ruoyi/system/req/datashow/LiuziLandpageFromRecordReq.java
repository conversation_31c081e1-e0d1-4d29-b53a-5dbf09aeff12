package com.ruoyi.system.req.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 留资落地页表单记录请求参数
 *
 * <AUTHOR>
 * @date 2022/01/24
 */
@Data
public class LiuziLandpageFromRecordReq implements Serializable {
    private static final long serialVersionUID = -3789383561030169415L;

    /**
     * 广告ID/推广页ID列表
     */
    private List<Long> advertIds;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;

    /**
     * 姓名/手机号搜索
     */
    private String infoSearch;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 自建站类型
     * @see com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum
     */
    private Integer landpageType;
    /**
     * 加好友状态
     * @see com.ruoyi.common.enums.QwFriendStatusEnum
     */
    private Integer friendStatus;
}
