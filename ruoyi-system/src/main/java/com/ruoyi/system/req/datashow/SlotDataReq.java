package com.ruoyi.system.req.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告位数据展示请求参数
 *
 * <AUTHOR>
 * @date 2021/9/1 2:09 下午
 */
@Data
public class SlotDataReq implements Serializable {
    private static final long serialVersionUID = -167917084510551078L;

    /**
     * 媒体名称，模糊搜索
     */
    private String appSearch;

    /**
     * 广告位名称，模糊搜索
     */
    private String slotSearch;

    /**
     * 账号id/邮箱模糊搜索
     */
    private String accountSearch;

    /**
     * 起始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 负责人查询
     */
    private List<Long> managerIds;

    /**
     * 排序字段
     */
    private String orderColumn;

    /**
     * 是否升序
     */
    private Boolean isAsc;
}
