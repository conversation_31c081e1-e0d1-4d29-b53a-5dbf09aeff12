package com.ruoyi.system.req.sms;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 短信内容新增请求参数
 * <AUTHOR>
 * @date 2023/1/9 14:36
 */
@Data
public class SmsContentAddReq implements Serializable {
    private static final long serialVersionUID = 2591868917232591523L;
    /**
     * 短信渠道
     * @see com.ruoyi.common.enums.SmsChannelEnum
     */
    @NotNull(message = "渠道类型不能为空")
    private Integer type;
    /**
     * 模版id
     */
    @NotNull(message = "模版id不能为空")
    private Long tpId;
    /**
     * 短信内容,签名+内容
     */
    @NotNull(message = "短信内容不能为空")
    private String content;

}
