package com.ruoyi.system.req.contract;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 账号合同请求参数
 * <AUTHOR>
 * @date 2022/11/3 5:46 下午
 */
@Data
public class AddContractReq implements Serializable {
    private static final long serialVersionUID = 2639356658623796541L;

    /**
     * 甲方主体
     */
    @NotNull(message = "甲方主体不能为空")
    private String planAName;

    /**
     * 乙方主体账号id
     */
    @NotNull(message = "乙方主体不能为空")
    private Long accountId;

    /**
     * 合同编号
     */
    @NotNull(message = "合同编号不能为空")
    private String contractCode;

    /**
     * 合同有效期开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private Date startDate;

    /**
     * 合同有效期结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private Date endDate;

    /**
     * 备注
     */
    private String remark;
}
