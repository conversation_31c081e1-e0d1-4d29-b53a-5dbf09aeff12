package com.ruoyi.system.req.engine;

import lombok.Data;

/**
 * 运营商会员落地页回调参数
 *
 * <AUTHOR>
 * @date 2023/11/24
 */
@Data
public class IspVipGhCallbackReq {

    /**
     * 渠道订单号
     */
    private String macOrderId;

    /**
     * 平台订单号
     */
    private String orderId;

    /**
     * 订单状态:0.待处理,1.订购中,2.订购成功,3.订购失败
     */
    private Integer status;

    /**
     * 渠道ID
     */
    private String macId;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 签名
     */
    private String sign;

    /**
     * 失败原因
     */
    private String message;
}
