package com.ruoyi.system.req.activity;

import com.ruoyi.system.bo.activity.skin.JsTemplate;
import com.ruoyi.system.domain.manager.Prize;
import lombok.Data;

import java.util.List;

/**
 * 活动工具请求参数
 *
 * <AUTHOR>
 * @date 2021/7/16
 */
@Data
public class ActivityReq {

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动皮肤
     */
    private String skinCode;

    /**
     * 是否自动出券:0.否,1.是
     */
    private Integer autoJoin;

    /**
     * 奖品列表
     */
    private List<Prize> prizeList;

    /**
     * 配置模板
     */
    private JsTemplate jsTemplate;

    /**
     * 规则说明
     */
    private String ruleDesc;

    /**
     * 备案号(底部文案)
     */
    private String icpNo;

    /**
     * 客服号码
     */
    private ActivityCustomerReq customerConfig;
}
