package com.ruoyi.system.req.manager;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 域名更新状态
 *
 * <AUTHOR>
 * @date 2022/7/12 4:22 下午
 */
@Data
public class DomainUpdateStatusReq implements Serializable {
    private static final long serialVersionUID = -8376508980012154888L;

    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 微信状态
     * @see com.ruoyi.common.enums.domain.DomainStatus
     */
    @NotNull(message = "微信状态不能为空")
    private Integer wxStatus;

    /**
     * 支付宝状态
     * @see com.ruoyi.common.enums.domain.DomainStatus
     */
    @NotNull(message = "支付宝状态不能为空")
    private Integer alipayStatus;
}
