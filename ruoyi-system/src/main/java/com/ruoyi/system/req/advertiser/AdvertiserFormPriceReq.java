package com.ruoyi.system.req.advertiser;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 广告主表单价格请求参数
 *
 * <AUTHOR>
 * @date 2022/03/14
 */
@Data
public class AdvertiserFormPriceReq {

    /**
     * 广告主ID
     */
    @NotNull(message = "广告主ID不能为空")
    private Long advertiserId;

    /**
     * 表单价格(分)
     */
    @NotNull(message = "表单价格不能为空")
    @Min(value = 0, message = "表单价格不能为负数")
    private Integer formPrice;

    /**
     * 权重
     */
    @NotNull(message = "权重不能为空")
    @Min(value = 0, message = "权重不能为负数")
    private Double weight;

    /**
     * 计费类型:0.有效表单,1.毛表单
     */
    @NotNull(message = "计费类型不能为空")
    private Integer priceType;
}
