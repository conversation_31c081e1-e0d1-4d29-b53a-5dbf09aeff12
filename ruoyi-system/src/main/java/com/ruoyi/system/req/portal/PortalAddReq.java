package com.ruoyi.system.req.portal;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增发布记录请求参数
 * <AUTHOR>
 * @date 2022/9/27 1:44 下午
 */
@Data
public class PortalAddReq implements Serializable {
    private static final long serialVersionUID = -7481408404845092801L;
    /**w
     * 角色类型
     * @see com.ruoyi.common.enums.portal.PortalContentTypeEnum
     */
    @NotNull(message = "角色不能为空")
    private Integer type;

    /**
     * 发布内容
     */
    @NotNull(message = "内容不能为空")
    private String content;

    /**
     * 联系人
     */
    @NotNull(message = "联系人不能为空")
    private String contacts;

    /**
     * 手机号
     */
    @NotNull(message = "手机号不能为空")
    private String phone;


    /**
     * 标题
     */
    @NotNull(message = "标题不能为空")
    private String title;
    /**
     * 单价
     */
    @NotNull(message = "价格不能为空")
    private Integer price;

    /**
     * 图片
     */
    @NotNull(message = "图片不能为空")
    private String img;
}
