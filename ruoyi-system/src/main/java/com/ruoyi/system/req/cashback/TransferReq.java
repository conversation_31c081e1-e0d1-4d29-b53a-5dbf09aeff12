package com.ruoyi.system.req.cashback;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 转账请求参数
 * <AUTHOR>
 * @date 2022/5/31 3:56 下午
 */
@Data
public class TransferReq implements Serializable {
    private static final long serialVersionUID = -7483706352238669194L;
    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    private Integer transferStatus;
    /**
     * 备注
     */
    private String remark;

}
