package com.ruoyi.system.req.slot;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位投流配置开关请求参数
 *
 * <AUTHOR>
 * @date 2023/9/7
 */
@Data
public class SlotBiddingConfigSwitchReq implements Serializable {
    private static final long serialVersionUID = 7842879482686804306L;

    /**
     * 广告位
     */
    private Long slotId;

    /**
     * 是否生效:0.未生效,1.已生效
     */
    private Integer isEnable;
}
