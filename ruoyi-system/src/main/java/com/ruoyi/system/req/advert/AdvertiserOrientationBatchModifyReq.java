package com.ruoyi.system.req.advert;

import com.ruoyi.common.enums.advert.DeviceTargetType;
import com.ruoyi.common.enums.advert.FlowTargetType;
import com.ruoyi.common.enums.advert.IspTargetType;
import com.ruoyi.common.enums.advert.OsTargetType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 广告主广告配置定向批量修改
 *
 * <AUTHOR> Yang
 * @Date 2023/9/25 20:27
 */
@Data
public class AdvertiserOrientationBatchModifyReq implements Serializable {

    private static final long serialVersionUID = 7881320189028549108L;


    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 地域定向
     */
    private Set<String> areaTarget;

    /**
     * 设备定向列表
     *
     * @see DeviceTargetType
     */
    private List<Integer> deviceTargets;

    /**
     * 运营商定向列表
     *
     * @see IspTargetType
     */
    private List<Integer> ispTargets;

    /**
     * 系统定向列表
     *
     * @see OsTargetType
     */
    private List<Integer> osTargets;

    /**
     * 流量定向列表
     *
     * @see FlowTargetType
     */
    private List<Integer> flowTargets;


    /**
     * 投放时段
     */
    private List<Integer> servingHours;
}
