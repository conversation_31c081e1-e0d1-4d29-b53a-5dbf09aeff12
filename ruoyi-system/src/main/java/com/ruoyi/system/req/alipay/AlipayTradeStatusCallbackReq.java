package com.ruoyi.system.req.alipay;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付宝支付结果回调
 * <AUTHOR>
 * @date 2023/4/24 19:27
 */
@Data
public class AlipayTradeStatusCallbackReq implements Serializable {
    private static final long serialVersionUID = -3701659595492882311L;
    /**
     * 通知时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date notify_time;
    /**
     * 通知类型
     */
    private String notify_type;
    /**
     * 通知校验 ID
     */
    private String notify_id;
    /**
     * 支付宝交易号。支付宝交易凭证号
     */
    private String trade_no;
    /**
     * 开发者的 app_id。支付宝分配给开发者的应用 APPID。
     */
    private String app_id;
    /**
     * 商户订单号
     */
    private String out_trade_no;
    /**
     * 交易状态
     * WAIT_BUYER_PAY 交易创建，等待买家付款。
     * TRADE_CLOSED 未付款交易超时关闭，或支付完成后全额退款。
     * TRADE_SUCCESS 交易支付成功。
     * TRADE_FINISHED 交易结束，不可退款。
     */
    private String trade_status;
    /**
     * 订单金额。本次交易支付的订单金额，单位为人民币（元）。支持小数点后两位。
     */
    private float total_amount;
    /**
     * 交易 付款时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmt_payment;
}
