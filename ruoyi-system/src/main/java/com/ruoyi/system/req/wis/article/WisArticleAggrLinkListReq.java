package com.ruoyi.system.req.wis.article;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 文章聚合链接列表查询请求参数
 *
 * <AUTHOR>
 * @date 2023/12/15 11:17
 */
@Data
public class WisArticleAggrLinkListReq implements Serializable {
    private static final long serialVersionUID = 3137356823520770396L;

    /**
     * 名称
     */
    private String name;

    /**
     * 起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 今日文章数: 0.全部, 1.大于0
     */
    private Integer todayArticleCount;
}
