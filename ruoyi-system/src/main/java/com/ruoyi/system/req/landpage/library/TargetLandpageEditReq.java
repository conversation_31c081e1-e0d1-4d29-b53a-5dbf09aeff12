package com.ruoyi.system.req.landpage.library;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 修改目标落地页请求参数
 *
 * <AUTHOR>
 * @date 2022/07/06
 */
@Data
public class TargetLandpageEditReq implements Serializable {
    private static final long serialVersionUID = -7939883011224072284L;

    /**
     * 落地页标识
     */
    @NotBlank(message = "落地页标识不能为空")
    private String key;

    /**
     * 落地页链接
     */
    @NotBlank(message = "落地页链接不能为空")
    private String targetLandpage;

    /**
     * 底部文案
     */
    private String footer;
}
