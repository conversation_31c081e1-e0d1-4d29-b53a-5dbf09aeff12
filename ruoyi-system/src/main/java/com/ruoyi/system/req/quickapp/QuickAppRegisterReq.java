package com.ruoyi.system.req.quickapp;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 快应用注册请求参数
 * <AUTHOR>
 * @date 2022/8/8 11:06 上午
 */
@Data
public class QuickAppRegisterReq implements Serializable {
    private static final long serialVersionUID = 8757728215432523683L;
    /**
     * 用户名
     */
    @NotNull(message = "用户名不能为空")
    private String userName;

    /**
     * 邮箱
     */
    @NotNull(message = "邮箱不能为空")
    private String email;
    /**
     * 密码
     */
    @NotNull(message = "密码不能为空")
    private String passwd;
    /**
     * 验证码
     */
    @NotNull(message = "验证码不能为空")
    private String code;
}
