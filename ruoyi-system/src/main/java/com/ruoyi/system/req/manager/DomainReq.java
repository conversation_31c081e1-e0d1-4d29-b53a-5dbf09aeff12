package com.ruoyi.system.req.manager;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 域名请求参数
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Data
public class DomainReq implements Serializable {
    private static final long serialVersionUID = -7096454353386617571L;

    /**
     * 域名ID
     */
    private Long id;

    /**
     * 域名
     */
    private String domain;

    /**
     * 域名类型
     */
    private Integer domainType;

    /**
     * 是否启用Https:0.不启用,1.启用
     */
    private Integer httpsEnable;

    /**
     * 域名过期时间
     */
    private Date domainExpire;

    /**
     * 备案号
     */
    private String icpNo;

    /**
     * 备案主体
     */
    private String icpSubject;

    /**
     * 备注
     */
    private String remark;
}
