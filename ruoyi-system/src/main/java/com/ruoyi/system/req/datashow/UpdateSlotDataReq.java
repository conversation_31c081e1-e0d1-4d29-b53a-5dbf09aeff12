package com.ruoyi.system.req.datashow;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 修改广告位数据请求对象
 * <AUTHOR>
 * @date 2021/12/22 5:48 下午
 */
@Data
public class UpdateSlotDataReq implements Serializable {
    private static final long serialVersionUID = -3766342739673252703L;

    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;

    /**
     * 诺禾结算款
     */
    private Long nhCost;

    /**
     * 外部结算款
     */
    private Long outerCost;

    /**
     * 媒体应得收益(分)
     */
    private Long appRevenue;
}
