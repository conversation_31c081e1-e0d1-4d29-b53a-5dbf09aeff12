package com.ruoyi.system.req.advertiser.qualification;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 广告主资质审核参数
 *
 * <AUTHOR>
 * @date 2023/03/29
 */
@Data
public class AdvertiserQualificationAuditReq {

    /**
     * 资质ID
     */
    @NotNull(message = "资质ID不能为空")
    private Long id;

    /**
     * 审核状态
     */
    @NotNull(message = "审核状态不能为空")
    @Min(value = 1, message = "无效的审核状态")
    @Max(value = 2, message = "无效的审核状态")
    private Integer auditStatus;

    /**
     * 审核理由
     */
    private String auditReason;
}
