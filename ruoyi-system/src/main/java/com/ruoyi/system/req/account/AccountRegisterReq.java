package com.ruoyi.system.req.account;

import lombok.Data;

import java.util.Date;

/**
 * 账号注册请求参数
 *
 * <AUTHOR>
 * @date 2021/10/8
 */
@Data
public class AccountRegisterReq {

    /**
     * 主体类型
     *
     * @see com.ruoyi.common.enums.account.AccountMainType
     */
    private Integer mainType;

    /**
     * 邮箱账号
     */
    private String email;

    /**
     * 密码
     */
    private String passwd;

    /**
     * 重复密码
     */
    private String repeatPasswd;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 验证码
     */
    private String code;

    /**
     * 营业执照注册号
     */
    private String businessLicense;

    /**
     * 营业执照图片
     */
    private String businessLicenseImg;

    /**
     * 营业执照有效期 9999-12-31代表永久
     */
    private Date businessExpireTime;

    /**
     * 结算类型
     */
    private Integer consumeType;
    /**
     * 商务号码
     */
    private String businessPhone;
}
