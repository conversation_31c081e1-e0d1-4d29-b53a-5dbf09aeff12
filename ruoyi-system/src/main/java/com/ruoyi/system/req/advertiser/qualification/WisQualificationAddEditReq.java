package com.ruoyi.system.req.advertiser.qualification;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * [广告主/代理商平台]资质新增编辑参数
 *
 * <AUTHOR>
 * @date 2023/03/30
 */
@Data
public class WisQualificationAddEditReq {

    /**
     * 行业ID
     */
    @NotNull(message = "行业ID不能为空")
    private Long industryId;

    /**
     * 资质列表
     */
    @NotEmpty(message = "资质不能为空")
    private List<WisQualificationInfoReq> qualificationList;
}
