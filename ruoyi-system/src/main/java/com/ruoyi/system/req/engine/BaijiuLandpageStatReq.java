package com.ruoyi.system.req.engine;

import lombok.Data;

import java.io.Serializable;

/**
 * 白酒落地页埋点请求参数
 *
 * <AUTHOR>
 * @date 2021/8/19
 */
@Data
public class BaijiuLandpageStatReq implements Serializable {
    private static final long serialVersionUID = 5723997244569303391L;

    /**
     * 埋点类型(20.落地页弹层,21.落地页返回挽留)
     */
    private Integer type;

    /**
     * 行为类型(1.曝光,2.点击)
     */
    private Integer action;

    /**
     * 订单号
     */
    private String orderId;
}
