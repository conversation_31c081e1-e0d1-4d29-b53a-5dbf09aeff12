package com.ruoyi.system.req.slot;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位投流配置请求参数
 *
 * <AUTHOR>
 * @date 2023/9/7
 */
@Data
public class SlotBiddingConfigReq implements Serializable {
    private static final long serialVersionUID = -3719965361770246976L;

    /**
     * 广告位
     */
    private Long slotId;

    /**
     * 上报价格(分)
     */
    private Integer convPrice;

    /**
     * 冷启动个数，默认1
     */
    private Integer coldStart;

    /**
     * 消耗类型:1.真实消耗,2.理论消耗
     */
    private Integer consumeType;

    /**
     * 是否生效:0.未生效,1.已生效
     */
    private Integer isEnable;
}
