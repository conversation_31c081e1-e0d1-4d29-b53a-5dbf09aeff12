package com.ruoyi.system.req.advert;

import com.ruoyi.system.bo.advert.AdvertExtInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告修改请求参数
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Data
public class AdvertModifyReq implements Serializable {
    private static final long serialVersionUID = -5847160738926500293L;

    /**
     * 广告ID
     */
    private Long id;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 广告分类
     * @see com.ruoyi.common.enums.advert.AdvertCategory
     */
    private Integer advertCategory;

    /**
     * 每日预算(分)
     */
    private Integer dailyBudget;

    /**
     * 开始投放日期
     */
    private Date startServingDate;

    /**
     * 结束投放日期
     */
    private Date stopServingDate;

    /**
     * 考核指标
     */
    private Integer assessType;

    /**
     * 考核成本(分)
     */
    private Integer assessCost;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 点击监测链接
     */
    private String clickCallbackUrl;

    /**
     * 结算指标类型
     * @see com.ruoyi.common.enums.advert.AdvertCostBillingTypeEnum
     */
    private Integer billingType;

    /**
     * 成本
     */
    private Integer cost;

    /**
     * 额外信息
     */
    private AdvertExtInfo extInfo;

    /**
     * 权重
     */
    private Double weight;

    /**
     * 广告缩略图
     */
    private String thumbnailImg;
}
