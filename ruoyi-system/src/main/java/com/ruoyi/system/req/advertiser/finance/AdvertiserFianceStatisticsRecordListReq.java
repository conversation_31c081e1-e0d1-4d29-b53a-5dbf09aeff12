package com.ruoyi.system.req.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告主财务汇总记录列表参数
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
@Data
public class AdvertiserFianceStatisticsRecordListReq implements Serializable {
    private static final long serialVersionUID = -7096586851439296799L;

    /**
     * 广告主ID
     */
    private Long accountId;

    /**
     * 广告主ID列表
     */
    private List<Long> accountIds;

    /**
     * 起始日期
     */
    private Date startDate;

    /**
     * 截止日期
     */
    private Date endDate;

    /**
     * 排除的广告主ID列表
     */
    private List<Long> excludeAccountIds;
}
