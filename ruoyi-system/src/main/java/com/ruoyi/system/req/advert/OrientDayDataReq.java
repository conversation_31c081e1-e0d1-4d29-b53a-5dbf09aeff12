package com.ruoyi.system.req.advert;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 配置日数据查询参数
 *
 * <AUTHOR>
 * @date 2023/07/12
 */
@Data
public class OrientDayDataReq {

    /**
     * 广告搜索
     */
    private String advertSearch;

    /**
     * 配置搜索
     */
    private String orientSearch;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 代理商ID列表
     */
    private List<Long> agentIds;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 是否是导出
     */
    private Boolean isExport;

    /**
     * 负责人查询
     */
    private List<Long> managerIds;

    /**
     * 排序字段
     */
    private String orderColumn;

    /**
     * 是否升序
     */
    private Boolean isAsc;
}
