package com.ruoyi.system.req.callback;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.domain.adengine.SlotBiddingConfigDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 转化上报参数
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
@Data
public class ConvCallbackParam implements Serializable {
    private static final long serialVersionUID = 7125057794787367187L;

    /**
     * 日期
     */
    private Date date;

    /**
     * 广告位参数
     */
    private JSONObject slotParam;

    /**
     * 广告位投流配置
     */
    private SlotBiddingConfigDto biddingConfig;
}
