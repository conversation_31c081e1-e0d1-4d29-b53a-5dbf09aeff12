package com.ruoyi.system.req.engine;

import lombok.Data;

/**
 * 落地页表单参数
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
@Data
public class LandPageFormReq {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 验证码
     */
    private String verificationCode;

    /**
     * 是否开启短信验证false未开启true开启
     */
    private Boolean enabledSmsCode;

    /**
     * 身份证
     */
    private String idCard;

    /**
     * 地址代码
     */
    private String areaNum;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 外部回传参数
     */
    private String aid;

    /**
     * 外部回传参数
     */
    private String cid;

    /**
     * 外部回传参数
     */
    private String exposure_id;

    /**
     * 落地页标识
     */
    private String lpk;
}