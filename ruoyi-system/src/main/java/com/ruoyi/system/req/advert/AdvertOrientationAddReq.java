package com.ruoyi.system.req.advert;

import com.ruoyi.common.enums.advert.DeviceTargetType;
import com.ruoyi.common.enums.advert.FlowTargetType;
import com.ruoyi.common.enums.advert.IspTargetType;
import com.ruoyi.common.enums.advert.OsTargetType;
import com.ruoyi.system.req.slot.AppSlotReq;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 广告定向投放配置新增请求参数
 *
 * <AUTHOR>
 * @date 2023/7/3
 */
@Data
public class AdvertOrientationAddReq {

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置名称
     */
    private String orientName;

    /**
     * 落地页类型
     */
    private Integer landpageType;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 权重
     */
    private Double weight;

    /**
     * 计费类型
     * @see com.ruoyi.common.enums.advert.ChargeTypeEnum
     */
    private Integer chargeType;

    /**
     * 计费单价(分)
     */
    private Integer unitPrice;

    /**
     * 计费单价(毫)
     */
    private Integer milliUnitPrice;

    /**
     * OCPC转化类型
     * @see com.ruoyi.common.enums.advert.OcpcConvTypeEnum
     */
    private Integer ocpcConvType;

    /**
     * OCPC转化成本(分)
     */
    private Integer ocpcConvCost;

    /**
     * 每日预算
     */
    private Integer dailyBudget;

    /**
     * 屏蔽媒体列表
     */
    private List<AppSlotReq> bannedApps;

    /**
     * 定向媒体列表
     */
    private List<AppSlotReq> orientApps;

    /**
     * 定向流量包ID列表
     */
    private List<Long> orientTrafficIds;

    /**
     * 地域定向
     */
    private Set<String> areaTarget;

    /**
     * 设备定向列表
     * @see DeviceTargetType
     */
    private List<Integer> deviceTargets;

    /**
     * 流量定向列表
     * @see FlowTargetType
     */
    private List<Integer> flowTargets;

    /**
     * 系统定向列表
     * @see OsTargetType
     */
    private List<Integer> osTargets;

    /**
     * 运营商定向列表
     * @see IspTargetType
     */
    private List<Integer> ispTargets;

    /**
     * 投放时段
     */
    private List<Integer> servingHours;
}
