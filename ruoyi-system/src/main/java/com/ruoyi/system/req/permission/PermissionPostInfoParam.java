package com.ruoyi.system.req.permission;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 职位权限详情vo
 *
 * <AUTHOR>
 * @date 2022/6/24 3:17 下午
 */
@Data
public class PermissionPostInfoParam implements Serializable {
    private static final long serialVersionUID = 2452640713250053020L;

    /**
     * 职位id
     */
    @NotNull(message = "职位id不能为空")
    private Long postId;
}
