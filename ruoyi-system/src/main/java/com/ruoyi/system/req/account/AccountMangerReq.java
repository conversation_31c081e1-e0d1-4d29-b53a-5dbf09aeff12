package com.ruoyi.system.req.account;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 账号指派负责人请求参数
 *
 * <AUTHOR>
 * @date 2022/4/26
 */
@Data
public class AccountMangerReq {

    /**
     * 账号ID
     */
    @NotNull(message = "账号ID不能为空")
    private Long accountId;

    /**
     * 商务列表
     */
    private List<Long> bdManagerIds;

    /**
     * 媒体运营列表
     */
    private List<Long> operationManagerIds;
}
