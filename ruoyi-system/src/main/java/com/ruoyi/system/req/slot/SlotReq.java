package com.ruoyi.system.req.slot;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告位请求参数
 *
 * <AUTHOR>
 * @date 2021/7/15
 */
@Data
public class SlotReq implements Serializable {
    private static final long serialVersionUID = 8926286468544640948L;

    /**
     * 广告位ID
     */
    private Long id;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 所属媒体
     */
    private String searchKey;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 状态:0.开启,1.关闭
     */
    private Integer status;

    /**
     * 是否广告位分流投放
     */
    private Integer isRedirectShunting;

    /**
     * 创建时间起始
     */
    private Date startDate;

    /**
     * 创建时间截止
     */
    private Date endDate;

    /**
     * 排序字段
     */
    private String orderColumn;

    /**
     * 是否升序
     */
    private Boolean isAsc;

    /**
     * 负责人查询
     */
    private List<Long> managerIds;
}
