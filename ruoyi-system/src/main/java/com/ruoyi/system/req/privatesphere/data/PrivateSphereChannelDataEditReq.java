package com.ruoyi.system.req.privatesphere.data;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 私域数据编辑参数
 * <AUTHOR>
 * @date 2023/2/10 14:24
 */
@Data
public class PrivateSphereChannelDataEditReq implements Serializable {

    private static final long serialVersionUID = -466130583834881484L;
    /**
     * 渠道id
     */
    @NotNull(message = "渠道id不能为空")
    private Long channelId;

    /**
     * 入群数
     */
    @NotNull(message = "入群数不能为空")
    private Integer entryGroupCount;

    /**
     * 退款数
     */
    @NotNull(message = "退款数不能为空")
    private Integer refundAmount;

    /**
     * 转化金额
     */
    @NotNull(message = "转化金额不能为空")
    private Integer converAmount;

}
