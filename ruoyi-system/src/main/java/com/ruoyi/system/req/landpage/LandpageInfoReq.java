package com.ruoyi.system.req.landpage;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 落地页详情
 *
 * <AUTHOR>
 * @date 2022/9/21 11:49 上午
 */
@Data
public class LandpageInfoReq implements Serializable {
    private static final long serialVersionUID = 453598946582147289L;

    /**
     * 落地页标识
     */
    @NotNull(message = "key不能为空")
    private String key;

    /**
     * 订单号
     */
    @NotNull(message = "nadkey不能为空")
    private String orderId;

    /**
     * 来源
     */
    private String referer;

    /**
     * 客户端:browser.快应用启动失败跳转浏览器
     */
    private String client;
}
