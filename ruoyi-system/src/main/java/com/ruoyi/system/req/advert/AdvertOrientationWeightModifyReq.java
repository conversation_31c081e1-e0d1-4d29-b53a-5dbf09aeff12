package com.ruoyi.system.req.advert;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 广告配置权重修改参数
 *
 * <AUTHOR>
 * @date 2023/07/17
 */
@Data
public class AdvertOrientationWeightModifyReq implements Serializable {
    private static final long serialVersionUID = 7279379797200830574L;

    /**
     * 配置ID
     */
    @NotNull(message = "配置ID不能为空")
    private Long id;

    /**
     * 权重
     */
    @NotNull(message = "权重不能为空")
    private Double weight;
}
