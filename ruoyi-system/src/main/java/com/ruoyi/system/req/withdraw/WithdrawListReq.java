package com.ruoyi.system.req.withdraw;

import com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 提现列表请求参数
 *
 * <AUTHOR>
 * @date 2021/9/14 7:38 下午
 */
@Data
public class WithdrawListReq implements Serializable {
    private static final long serialVersionUID = -5686527562313443941L;

    /**
     * 账号邮箱/id模糊搜索
     */
    private String accountSearch;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 提现状态
     *
     * @see WithdrawCheckStatusEnum
     */
    private Integer withdrawStatus;

    /**
     * 账号id列表
     */
    private Set<Long> accountIds;
}
