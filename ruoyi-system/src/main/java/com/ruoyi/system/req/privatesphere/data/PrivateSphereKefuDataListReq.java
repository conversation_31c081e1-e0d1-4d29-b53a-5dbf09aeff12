package com.ruoyi.system.req.privatesphere.data;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客服渠道数据列表查询
 * <AUTHOR>
 * @date 2023/2/9 11:09
 */
@Data
public class PrivateSphereKefuDataListReq implements Serializable {
    private static final long serialVersionUID = -6636323459131463818L;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * 客服渠道
     */
    private String channel;
    /**
     * 新增起始时间
     */
    private Date startDate;
    /**
     * 新增结束时间
     */
    private Date endDate;
}
