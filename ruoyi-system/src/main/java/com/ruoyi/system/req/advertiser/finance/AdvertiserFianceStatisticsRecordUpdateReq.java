package com.ruoyi.system.req.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主财务汇总记录更新参数
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
@Data
public class AdvertiserFianceStatisticsRecordUpdateReq implements Serializable {
    private static final long serialVersionUID = -5708849301368379107L;

    /**
     * 广告主财务汇总记录ID
     */
    private Long id;

    /**
     * 充值金额增量(分)
     */
    private Integer rechargeAmountAdd;

    /**
     * 消费金额增量(分)
     */
    private Integer consumeAmountAdd;

    /**
     * 账户现金余额增量(分)
     */
    private Integer cashBalanceAdd;

    /**
     * 账户返货余额增量(分)
     */
    private Integer rebateBalanceAdd;
}
