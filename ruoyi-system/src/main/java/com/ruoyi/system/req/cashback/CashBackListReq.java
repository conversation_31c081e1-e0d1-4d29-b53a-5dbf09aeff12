package com.ruoyi.system.req.cashback;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 号卡返现列表请求参数
 * <AUTHOR>
 * @date 2022/5/30 5:41 下午
 */
@Data
public class CashBackListReq implements Serializable {
    private static final long serialVersionUID = -6836140877392592858L;
    /**
     * 支付宝查询参数
     */
    private String alipaySearch;
    /**
     * 表单查询参数
     */
    private String formSearch;
    /**
     * 提交开始时间
     */
    private Date submitStartTime;
    /**
     * 提交结束时间
     */
    private Date submitEndTime;
    /**
     * 打款开始时间
     */
    private Date operatorStartTime;
    /**
     * 打款结束时间
     */
    private Date operatorEndTime;

    /**
     * 打款状态:0.未打款,1.已打款,2.无效信息
     */
    private Integer transferStatus;

}
