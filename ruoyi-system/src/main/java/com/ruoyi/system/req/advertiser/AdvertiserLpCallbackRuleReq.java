package com.ruoyi.system.req.advertiser;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 落地页表单规则请求参数
 *
 * <AUTHOR>
 * @date 2021/12/23
 */
@Data
public class AdvertiserLpCallbackRuleReq implements Serializable {
    private static final long serialVersionUID = 3136373163738868644L;

    /**
     * 广告主ID
     */
    @NotNull(message = "广告主ID不能为空")
    private Long advertiserId;

    /**
     * 地域列表
     */
    private List<String> areaList;

    /**
     * 年龄下限(包含)
     */
    private Integer ageMin;

    /**
     * 年龄上限(包含)
     */
    private Integer ageMax;

    /**
     * 每日表单量上限
     */
    private Integer dailyLimit;
}
