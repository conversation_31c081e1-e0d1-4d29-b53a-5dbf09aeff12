package com.ruoyi.system.req.advert;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 广告列表查询条件
 *
 * <AUTHOR>
 * @date 2021/8/10
 */
@Data
public class AdvertListReq {

    /**
     * 广告ID/广告名称搜索
     */
    private String advertSearch;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 代理商ID列表
     */
    private List<Long> agentIds;

    /**
     * 创建时间起始
     */
    private Date gmtCreateStart;

    /**
     * 创建时间截止
     */
    private Date gmtCreateEnd;

    /**
     * 创建人名称
     */
    private String operatorName;

    /**
     * 投放开关
     * @see com.ruoyi.common.enums.common.SwitchStatusEnum
     */
    private Integer servingSwitch;
}
