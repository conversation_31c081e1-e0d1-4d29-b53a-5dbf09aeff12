package com.ruoyi.system.req.agent.client;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 代理商新增客户请求参数
 *
 * <AUTHOR>
 * @date 2022/10/26
 */
@Data
public class AgentClientAddReq {

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    private String email;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String passwd;

    /**
     * 重复密码
     */
    private String repeatPasswd;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空")
    private String companyName;

    /**
     * 营业执照注册号
     */
    @NotBlank(message = "营业执照注册号不能为空")
    private String businessLicense;

    /**
     * 营业执照图片
     */
    @NotBlank(message = "营业执照未上传")
    private String businessLicenseImg;

    /**
     * 营业执照有效期 9999-12-31代表永久
     */
    private Date businessExpireTime;

    /**
     * 公司地址
     */
    @NotBlank(message = "公司地址不能为空")
    private String address;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 手机
     */
    private String phone;
}
