package com.ruoyi.system.req.agent.finance;

import com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 代理商充值申请参数
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@Data
public class WisAgentRechargeReq implements Serializable {
    private static final long serialVersionUID = 3814584284887598231L;

    /**
     * 代理商ID(内部参数)
     */
    private Long agentId;

    /**
     * 广告主ID
     */
    @NotNull(message = "广告主ID不能为空")
    private Long advertiserId;

    /**
     * 充值类型
     */
    @NotNull(message = "充值类型不能为空")
    @Min(value = 3, message = "无效的充值类型")
    @Max(value = 4, message = "无效的充值类型")
    private Integer rechargeType;

    /**
     * 充值金额(分)
     *
     * @see AdvertiserRechargeTypeEnum
     */
    @NotNull(message = "充值金额不能为空")
    private Integer rechargeAmount;

    /**
     * 备注
     */
    private String remark;
}
