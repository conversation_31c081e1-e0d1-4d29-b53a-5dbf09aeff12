package com.ruoyi.system.req.engine;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 短剧落地页支付参数
 *
 * <AUTHOR>
 * @date 2023/08/03
 */
@Data
public class PlayletLandpagePayReq {

    /**
     * 支付订单号
     */
    private String tradeNo;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tradeTime;

    /**
     * 微信交易订单号
     */
    private String transactionId;

    /**
     * 订单OpenId
     */
    private String openid;
}
