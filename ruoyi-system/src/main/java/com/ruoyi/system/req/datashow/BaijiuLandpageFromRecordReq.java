package com.ruoyi.system.req.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 白酒落地页表单记录请求参数
 *
 * <AUTHOR>
 * @date 2022/01/24
 */
@Data
public class BaijiuLandpageFromRecordReq implements Serializable {
    private static final long serialVersionUID = -6367663845520642114L;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;

    /**
     * 手机号/姓名查询
     */
    private String phone;
}
