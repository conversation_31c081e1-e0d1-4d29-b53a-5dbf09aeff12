package com.ruoyi.system.req.slot;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位数据校准设置请求
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Data
public class SlotSwitchConfigReq implements Serializable {
    private static final long serialVersionUID = 878762502465819004L;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 数据校准开关(0.关闭,1.开启)
     */
    private Integer adjustSwitch;

    /**
     * 自动结算开关(0.关闭,1.开启)
     */
    private Integer autoChargeSwitch;
}
