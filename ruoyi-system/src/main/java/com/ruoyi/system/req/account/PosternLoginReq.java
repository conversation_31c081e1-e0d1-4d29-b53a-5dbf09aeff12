package com.ruoyi.system.req.account;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 后门登录请求参数
 *
 * <AUTHOR>
 * @date 2022/10/17 5:59 下午
 */
@Data
public class PosternLoginReq implements Serializable {
    private static final long serialVersionUID = 5007554198904233689L;

    /**
     * 账号id
     */
    @NotNull(message = "账号id不能为空")
    private Long accountId;
}
