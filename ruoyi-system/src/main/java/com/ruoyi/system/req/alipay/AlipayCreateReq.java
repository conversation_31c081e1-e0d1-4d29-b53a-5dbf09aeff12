package com.ruoyi.system.req.alipay;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 支付宝创建订单请求参数
 * <AUTHOR>
 * @date 2023/4/24 14:04
 */
@Data
public class AlipayCreateReq implements Serializable {
    private static final long serialVersionUID = -6398588933586635725L;
    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 落地页标识
     */
    @NotNull(message ="参数错误")
    private String lpk;
    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private String userId;

}
