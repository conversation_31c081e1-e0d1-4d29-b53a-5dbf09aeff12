package com.ruoyi.system.req.advert;

import com.ruoyi.system.bo.advert.AdvertExtInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告创建请求参数
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Data
public class AdvertCreateReq implements Serializable {
    private static final long serialVersionUID = 7093197088812264713L;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告分类
     * @see com.ruoyi.common.enums.advert.AdvertCategory
     */
    private Integer advertCategory;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 计费类型
     */
    private Integer chargeType;

    /**
     * 广告出价(分)
     */
    private Integer unitPrice;

    /**
     * 广告出价(毫)
     */
    private Integer milliUnitPrice;

    /**
     * 每日预算(分)
     */
    private Integer dailyBudget;

    /**
     * 开始投放日期
     */
    private Date startServingDate;

    /**
     * 结束投放日期
     */
    private Date stopServingDate;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 点击监测链接
     */
    private String clickCallbackUrl;

    /**
     * 广告标题
     */
    private String advertTitle;

    /**
     * 领取文案
     */
    private String buttonText;

    /**
     * 广告banner
     */
    private String materialImg;

    /**
     * 广告缩略图
     */
    private String thumbnailImg;

    /**
     * 额外信息
     */
    private AdvertExtInfo extInfo;

    /**
     * 设备定向列表
     * @see com.ruoyi.common.enums.advert.DeviceTargetType
     */
    private List<Integer> deviceTargets;

    /**
     * 流量定向列表
     * @see com.ruoyi.common.enums.advert.FlowTargetType
     */
    private List<Integer> flowTargets;
}
