package com.ruoyi.system.req.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 企微囤粉落地页表单记录请求参数
 *
 * <AUTHOR>
 * @date 2023/09/22
 */
@Data
public class QwtfLandpageFromRecordReq implements Serializable {
    private static final long serialVersionUID = -1634585693172942285L;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;

    /**
     * 手机号搜索
     */
    private String phone;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 加好友状态:1.未加好友,3.已加好友
     */
    private Integer friendStatus;
}
