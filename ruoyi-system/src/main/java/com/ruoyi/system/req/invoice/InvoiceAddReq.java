package com.ruoyi.system.req.invoice;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 发票新增请求参数
 * <AUTHOR>
 * @date 2022/10/20 2:41 下午
 */
@Data
public class InvoiceAddReq implements Serializable {
    private static final long serialVersionUID = 11828608294245836L;
    /**
     * 媒体账号id
     */
    @NotNull(message = "媒体账号不能为空")
    private Long accountId;

    /**
     * 发票单号
     */
    @NotNull(message = "发票单号不能为空")
    private String invoiceNumber;

    /**
     * 发票金额
     */
    @NotNull(message = "发票金额不能为空")
    private Integer invoiceAmount;

    /**
     * 备注
     */
    private String remarkText;
}
