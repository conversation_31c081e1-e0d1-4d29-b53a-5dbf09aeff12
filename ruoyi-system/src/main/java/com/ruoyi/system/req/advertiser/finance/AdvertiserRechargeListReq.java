package com.ruoyi.system.req.advertiser.finance;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告主充值记录列表请求参数
 *
 * <AUTHOR>
 * @date 2022/3/21 1:58 下午
 */
@Data
public class AdvertiserRechargeListReq implements Serializable {
    private static final long serialVersionUID = -6194496448974022869L;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 代理商ID列表
     */
    private List<Long> agentIds;

    /**
     * 账号ID列表
     */
    private List<Long> accountIds;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 充值类型
     */
    private Integer rechargeType;

    /**
     * 充值类型列表
     */
    private List<Integer> rechargeTypes;

    /**
     * 资金来源账户ID
     */
    private Long sourceAccountId;

    /**
     * 排除的广告主ID列表
     */
    private List<Long> excludeAccountIds;
}
