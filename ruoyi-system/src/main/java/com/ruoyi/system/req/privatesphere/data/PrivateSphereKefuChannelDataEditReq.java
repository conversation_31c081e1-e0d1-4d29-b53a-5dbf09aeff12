package com.ruoyi.system.req.privatesphere.data;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 私域客服渠道数据编辑参数
 * <AUTHOR>
 * @date 2023/2/10 14:24
 */
@Data
public class PrivateSphereKefuChannelDataEditReq implements Serializable {

    private static final long serialVersionUID = 2119198421959340245L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空")
    private Date curDate;

    /**
     * 账号id
     */
    @NotNull(message = "账号id不能为空")
    private Long accountId;

    /**
     * 客服渠道id
     */
    @NotNull(message = "客服渠道id不能为空")
    private Long kefuChannelId;

    /**
     * 产品id
     */
    @NotNull(message = "产品id不能为空")
    private Long productId;
    /**
     * 客服账号id
     */
    @NotNull(message = "客服公司id不能为空")
    private Long sspAccountId;
    /**
     * 外呼总数
     */
    @NotNull(message = "外呼总数不能为空")
    private Integer callCount;

    /**
     * 接通数
     */
    @NotNull(message = "接通数不能为空")
    private Integer connectCount;

    /**
     * 进入人工数
     */
    @NotNull(message = "进入人工数不能为空")
    private Integer personCount;

    /**
     * 入群数
     */
    @NotNull(message = "入群数不能为空")
    private Integer entryCount;

    /**
     * 反馈入群数
     */
    @NotNull(message = "反馈入群数不能为空")
    private Integer feedbackEntryCount;

    /**
     * 数据成本
     */
    @NotNull(message = "数据成本单价不能为空")
    private Float dataCost;
    /**
     * 数据成本数
     */
    @NotNull(message = "数据成本数不能为空")
    private Integer dataCostCount;

    /**
     * AI+线路成本
     */
    @NotNull(message = "线路成本不能为空")
    private Integer lineCost;

    /**
     * 人工成本单价
     */
    @NotNull(message = "人工成本单价不能为空")
    private Integer personCost;
    /**
     * 渠道数据列表
     */
    @NotEmpty(message = "渠道数据不能为空")
    @Valid
    private List<PrivateSphereChannelDataEditReq> channelDataList;

}
