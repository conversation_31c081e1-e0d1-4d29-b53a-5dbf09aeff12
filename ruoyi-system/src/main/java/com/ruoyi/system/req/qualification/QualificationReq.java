package com.ruoyi.system.req.qualification;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 提现账号资质信息 请求参数
 * <AUTHOR>
 * @date 2021/9/9 5:19 下午
 */
@Data
public class QualificationReq implements Serializable {
    private static final long serialVersionUID = 4889321900819913582L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 公司名称
     */
    @NotNull(message = "公司名称不能为空")
    private String companyName;

    /**
     * 营业执照号
     */
    @NotNull(message = "营业执照号不能为空")
    private String businessLicense;

    /**
     * 营业执照图
     */
    @NotNull(message = "营业执照不能为空")
    private String businessLicenseImg;

    /**
     * 开户银行
     */
    @NotNull(message = "开户银行不能为空")
    private String bankName;

    /**
     * 银行账户
     */
    @NotNull(message = "银行账户不能为空")
    private String bankAccount;

    /**
     * 开户名
     */
    @NotNull(message = "开户名不能为空")
    private String bankAccountName;

    /**
     * 备注
     */
    private String remarkText;
}
