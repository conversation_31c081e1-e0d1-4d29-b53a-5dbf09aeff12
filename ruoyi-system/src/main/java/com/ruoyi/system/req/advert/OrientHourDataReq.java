package com.ruoyi.system.req.advert;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 配置时段数据查询参数
 *
 * <AUTHOR>
 * @date 2023/07/12
 */
@Data
public class OrientHourDataReq {

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 日期
     */
    private Date date;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 是否是导出
     */
    private Boolean isExport;
}
