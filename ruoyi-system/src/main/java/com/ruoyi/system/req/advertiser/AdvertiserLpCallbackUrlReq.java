package com.ruoyi.system.req.advertiser;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 广告主表单价格请求参数
 *
 * <AUTHOR>
 * @date 2022/03/14
 */
@Data
public class AdvertiserLpCallbackUrlReq {

    /**
     * 广告主ID
     */
    @NotNull(message = "广告主ID不能为空")
    private Long advertiserId;

    /**
     * 回传链接
     */
    @NotBlank(message = "回传链接不能为空")
    private String lpCallbackUrl;
}
