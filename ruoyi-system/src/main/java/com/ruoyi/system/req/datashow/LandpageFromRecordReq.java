package com.ruoyi.system.req.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 落地页表单记录请求参数
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
@Data
public class LandpageFromRecordReq implements Serializable {
    private static final long serialVersionUID = 6628835302982378155L;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;

    /**
     * 分配日期起始
     */
    private Date cbStartDate;

    /**
     * 分配日期截止
     */
    private Date cbEndDate;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告主ID列表
     */
    private List<Long> slotIds;

    /**
     * 分配状态
     * @see com.ruoyi.common.enums.landpage.AssignStatus
     */
    private Integer assignStatus;

    /**
     * 姓名
     */
    private String name;

    /**
     * 分配的广告主ID列表
     */
    private List<Long> targetAdvertiserIds;

    /**
     * 地域搜索(逗号分割)
     */
    private String areaSearch;

    /**
     * 落地页标签
     */
    private String landpageTag;

    /**
     * 最小年龄
     */
    private Integer ageMin;

    /**
     * 最大年龄
     */
    private Integer ageMax;
}
