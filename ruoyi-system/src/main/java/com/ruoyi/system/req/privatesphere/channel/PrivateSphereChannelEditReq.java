package com.ruoyi.system.req.privatesphere.channel;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 私域渠道编辑参数
 * <AUTHOR>
 * @date 2023/2/8 10:29
 */
@Data
public class PrivateSphereChannelEditReq implements Serializable {
    private static final long serialVersionUID = -775018447355223189L;

    private Long id;

    /**
     * 公司id
     */
    @NotNull(message = "账号id不能为空")
    private Long accountId;
    /**
     * 产品名称
     */
    @NotNull(message = "产品id不能为空")
    private Long productId;
    /**
     * 渠道名称
     */
    @NotNull(message = "渠道名称不能为空")
    private String channel;
    /**
     * 渠道号
     */
    @NotEmpty(message = "渠道号不能为空")
    private List<String> channelNumber;
}
