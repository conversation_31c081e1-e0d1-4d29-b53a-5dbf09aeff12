package com.ruoyi.system.req.quickapp;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 发布动态
 * <AUTHOR>
 * @date 2022/8/8 1:43 下午
 */
@Data
public class QuickAppPublishReq implements Serializable {
    private static final long serialVersionUID = -2898435145734947292L;
    /**
     * 图片
     */
    @NotNull(message = "图片不能为空")
    private String images;
    /**
     * 动态内容
     */
    @NotNull(message = "内容不能为空")
    private String content;

    @NotNull(message = "请登录")
    private String token;
}
