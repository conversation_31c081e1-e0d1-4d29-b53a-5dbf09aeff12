package com.ruoyi.system.req.slot;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位及账单数据更新请求参数
 *
 * <AUTHOR>
 * @date 2022/03/09
 */
@Data
public class SlotMonthDataUpdateReq implements Serializable {
    private static final long serialVersionUID = -5809736121866744081L;

    /**
     * 广告位数据ID
     */
    private Long slotId;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;

    /**
     * 诺禾结算款
     */
    private Long nhCost;

    /**
     * 外部结算款
     */
    private Long outerCost;
}
