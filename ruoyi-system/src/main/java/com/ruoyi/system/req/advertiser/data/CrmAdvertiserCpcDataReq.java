package com.ruoyi.system.req.advertiser.data;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * CRM后台广告主CPC数据请求参数
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
@Data
public class CrmAdvertiserCpcDataReq implements Serializable {
    private static final long serialVersionUID = 2736698976716298835L;

    /**
     * 起始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 代理商ID列表
     */
    private List<Long> agentIds;

    /**
     * 广告主落地页
     */
    private String landpageUrl;

    /**
     * 广告id列表(内部字段)
     */
    private List<Long> advertIds;
}
