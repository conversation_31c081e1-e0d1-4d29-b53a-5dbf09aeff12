package com.ruoyi.system.req.ai;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * ai 留资请求参数
 * <AUTHOR>
 * @date 2024/1/2 14:35
 */
@Data
public class AiLiuziReq implements Serializable {
    private static final long serialVersionUID = 8953285879125913670L;
    /**
     * 姓名/公司名称
     */
    @NotEmpty(message = "姓名/公司名称不能为空")
    private String userName;
    /**
     * 用户号码
     */
    @NotEmpty(message = "用户号码不能为空")
    private String phone;
    /**
     * 使用场景
     */
    private String useScene;
}
