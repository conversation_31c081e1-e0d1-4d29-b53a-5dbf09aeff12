package com.ruoyi.system.req.engine;

import lombok.Data;

import java.io.Serializable;

/**
 * 诺禾广告请求参数
 *
 * <AUTHOR>
 * @date 2021/8/12
 */
@Data
public class NhAdvertReq implements Serializable {
    private static final long serialVersionUID = -3277191104728328161L;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 应用标识
     */
    private String appKey;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 设备号
     */
    private String deviceId;

    /**
     * 插件ID
     */
    private Long pluginId;

    /**
     * 广告位请求ID
     */
    private String srid;

    /**
     * 用户在蜂窝网络下的运营商
     */
    private String isp;

    /**
     * 广告排序类型：1.ARPU排序
     */
    private Integer sortType;
}
