package com.ruoyi.system.req.landpage.article;

import com.ruoyi.system.bo.landpage.article.ArticleUrlSchemeBo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 文章更新Url Scheme请求参数
 *
 * <AUTHOR>
 * @date 2024/08/05
 */
@Data
public class ArticleUrlSchemeUpdateReq implements Serializable {
    private static final long serialVersionUID = -6130010275350009444L;

    /**
     * 文章UrlScheme列表
     */
    List<ArticleUrlSchemeBo> list;
}
