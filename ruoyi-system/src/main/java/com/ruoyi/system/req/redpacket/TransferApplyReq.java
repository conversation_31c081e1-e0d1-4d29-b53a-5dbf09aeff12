package com.ruoyi.system.req.redpacket;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 提交收款信息请求参数
 *
 * <AUTHOR>
 * @date 2022/4/26
 */
@Data
public class TransferApplyReq implements Serializable {
    private static final long serialVersionUID = 5154023896785182618L;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String code;

    /**
     * 下单手机号
     */
    @NotBlank(message = "下单手机号不能为空")
    private String phone;

    /**
     * 支付宝账号
     */
    @NotBlank(message = "支付宝账号不能为空")
    private String alipayAccount;

    /**
     * 收款姓名
     */
    @NotBlank(message = "收款姓名")
    private String alipayName;
}
