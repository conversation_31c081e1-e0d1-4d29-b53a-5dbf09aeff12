package com.ruoyi.system.req.slot.shunt;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 广告位切量计划查询参数
 *
 * <AUTHOR>
 * @date 2022/4/22
 */
@Data
public class SlotShuntTaskParam {

    /**
     * 广告位Id
     */
    private Long slotId;

    /**
     * 计划名称
     */
    private String taskName;

    /**
     * 状态
     */
    private Integer taskStatus;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 状态列表
     */
    private List<Integer> taskStatusList;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;
}
