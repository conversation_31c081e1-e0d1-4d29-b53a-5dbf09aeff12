package com.ruoyi.system.req.advert;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 广告维度时段数据请求参数
 *
 * <AUTHOR>
 * @date 2021/10/18
 */
@Data
public class AdvertHourDataReq {

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 日期
     */
    private Date date;

    /**
     * 广告搜索
     */
    private String advertSearch;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 代理商ID列表
     */
    private List<Long> agentIds;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 负责人查询
     */
    private List<Long> managerIds;
}
