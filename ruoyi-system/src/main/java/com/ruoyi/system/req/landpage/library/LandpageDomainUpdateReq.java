package com.ruoyi.system.req.landpage.library;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 修改落地页域名请求参数
 *
 * <AUTHOR>
 * @date 2022/11/03
 */
@Data
public class LandpageDomainUpdateReq implements Serializable {
    private static final long serialVersionUID = -7939883011224072284L;

    /**
     * 落地页标识
     */
    @NotBlank(message = "落地页标识不能为空")
    private String key;

    /**
     * 替换的域名
     */
    @NotBlank(message = "替换的域名不能为空")
    private String domain;
}
