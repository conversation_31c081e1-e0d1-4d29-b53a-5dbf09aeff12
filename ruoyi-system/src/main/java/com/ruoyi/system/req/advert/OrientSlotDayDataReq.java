package com.ruoyi.system.req.advert;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 配置分广告位日数据查询参数
 *
 * <AUTHOR>
 * @date 2023/07/12
 */
@Data
public class OrientSlotDayDataReq {

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 是否是导出
     */
    private Boolean isExport;
}
