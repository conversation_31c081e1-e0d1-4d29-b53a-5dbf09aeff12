package com.ruoyi.system.req.ai;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * ai留资更新状态请求参数
 * <AUTHOR>
 * @date 2024/1/2 15:40
 */
@Data
public class AiLiuziUpdateStatusReq implements Serializable {
    private static final long serialVersionUID = 5081625719165260684L;
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 状态 0-未处理 1-有效 2-无效
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}
