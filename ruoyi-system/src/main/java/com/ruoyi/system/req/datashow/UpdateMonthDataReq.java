package com.ruoyi.system.req.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/1/6 12:01 下午
 */
@Data
public class UpdateMonthDataReq implements Serializable {
    private static final long serialVersionUID = -9025262737007100858L;

    private Long slotId;
    private Date curDate;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;
    /**
     * 广告位访问pv
     */
    private Integer slotRequestUv;
    /**
     * 诺禾结算款
     */
    private Long nhCost;
    /**
     * 外部结算款
     */
    private Long outerCost;
    /**
     * 媒体应得收入
     */
    private Integer appRevenue;

    /**
     * 计费类型
     * @see com.ruoyi.common.enums.slot.SlotChargeTypeEnum
     */
    private Integer chargeType;

    /**
     * 计费价格
     */
    private Integer chargePrice;

}
