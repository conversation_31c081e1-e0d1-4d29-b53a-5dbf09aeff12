package com.ruoyi.system.req.tag;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 媒体标签编辑
 * <AUTHOR>
 * @date 2022/9/23 11:32 上午
 */
@Data
public class TagEditReq implements Serializable {
    /**
     * 主键id ,新增时为空
     */
    private Long id;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签排序 ，一级标签为0即可
     */
    private Integer tagSort;
    /**
     * 二级标签列表
     */
    private List<TagEditReq> subTags;
}
