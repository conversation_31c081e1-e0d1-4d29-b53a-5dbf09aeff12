package com.ruoyi.system.req.account;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 账号请求参数
 *
 * <AUTHOR>
 * @date 2021/7/15
 */
@Data
public class AccountReq {

    /**
     * 账号ID
     */
    private Long id;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 媒体查询关键词
     */
    private String appSearch;

    /**
     * 注册时间起始
     */
    private Date startDate;

    /**
     * 注册时间截止
     */
    private Date endDate;

    /**
     * 负责人查询
     */
    private List<Long> managerIds;

    /**
     * 合同状态
     * @see com.ruoyi.common.enums.contract.ContractStatusEnum
     */
    private Integer contractStatus;
}
