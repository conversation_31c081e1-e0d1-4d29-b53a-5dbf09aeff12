package com.ruoyi.system.req.contract;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 更新账号合同请求参数
 * <AUTHOR>
 * @date 2022/11/3 5:46 下午
 */
@Data
public class UpdateContractReq implements Serializable {

    private static final long serialVersionUID = 7280572052331525286L;
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 合同有效期开始时间
     */
    @NotNull(message = "开始时间为空")
    private Date startDate;

    /**
     * 合同有效期结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private Date endDate;

    /**
     * 备注
     */
    private String remark;
}
