package com.ruoyi.system.req.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 媒体反馈广告位数据展示请求参数
 *
 * <AUTHOR>
 * @date 2021/9/1 2:09 下午
 */
@Data
public class ExportAppSlotRequestDataReq implements Serializable {

    private static final long serialVersionUID = 2643694783485367345L;
    /**
     * 日期
     */
    @NotNull(message = "日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;
    /**
     * 广告位id
     */
    @NotNull(message = "广告位id不能为空")
    private Long slotId;
    /**
     * 广告位点击pv(媒体反馈)
     */
    @NotNull(message = "广告位点击pv不能为空")
    private Integer appSlotClickPv;
    /**
     * 广告位点击uv(媒体反馈)
     */
    @NotNull(message = "广告位点击uv不能为空")
    private Integer appSlotClickUv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    @NotNull(message = "广告位曝光pv不能为空")
    private Integer appSlotExposurePv;
    /**
     * 广告位曝光uv(媒体反馈)
     */
    @NotNull(message = "广告位曝光uv不能为空")
    private Integer appSlotExposureUv;
}
