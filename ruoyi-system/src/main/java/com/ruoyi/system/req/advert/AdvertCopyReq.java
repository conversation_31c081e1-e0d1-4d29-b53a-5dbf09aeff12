package com.ruoyi.system.req.advert;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 广告复制请求参数
 *
 * <AUTHOR>
 * @date 2022/4/6 2:02 下午
 */
@Data
public class AdvertCopyReq implements Serializable {
    private static final long serialVersionUID = 8268886019067396429L;

    /**
     * 广告id
     */
    @NotNull(message = "广告id不能为空")
    private Long id;

    /**
     * 广告主id
     */
    @NotNull(message = "广告主id不能为空")
    private Long advertiserId;
}
