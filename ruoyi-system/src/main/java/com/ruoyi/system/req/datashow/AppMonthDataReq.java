package com.ruoyi.system.req.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 媒体数据展示请求参数
 *
 * <AUTHOR>
 * @date 2021/9/1 2:09 下午
 */
@Data
public class AppMonthDataReq implements Serializable {
    private static final long serialVersionUID = 2639993051591169196L;

    /**
     * 媒体账号搜索
     */
    private String accountSearch;

    /**
     * 媒体名称，模糊搜索
     */
    private String appSearch;

    /**
     * 起始月 日期
     */
    private Integer startMonth;

    /**
     * 结束月 日期
     */
    private Integer endMonth;

    /**
     * 结算状态 ,全部 传null
     * @see com.ruoyi.common.enums.ConfirmStatusEnum
     */
    private Integer confirmStatus;

    /**
     * 账号id列表
     */
    private List<Long> accountIds;

    /**
     * 媒体id列表
     */
    private List<Long> appIds;

    /**
     * 付款类型:1.预付款,0.后付款
     */
    private Integer payType;
}
