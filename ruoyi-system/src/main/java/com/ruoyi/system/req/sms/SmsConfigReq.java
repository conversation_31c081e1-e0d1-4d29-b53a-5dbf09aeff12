package com.ruoyi.system.req.sms;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 短信配置请求参数
 * <AUTHOR>
 * @date 2022/12/5 14:38
 */
@Data
public class SmsConfigReq implements Serializable {
    private static final long serialVersionUID = 3910009000782100945L;

    /**
     * 是否开启
     */
    @NotNull(message = "开关不能为空")
    private Boolean open;
    /**
     * 短信配置
     */
    private List<LiuziSmsConfigInfoReq> config;
}
