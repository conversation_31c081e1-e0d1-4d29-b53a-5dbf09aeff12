package com.ruoyi.system.req.privatesphere.channel;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 私域客服渠道编辑参数
 * <AUTHOR>
 * @date 2023/2/8 10:29
 */
@Data
public class PrivateSphereKefuChannelEditReq implements Serializable {

    private static final long serialVersionUID = -4054063336198209315L;
    private Long id;

    /**
     * 公司id
     */
    @NotNull(message = "账号id不能为空")
    private Long accountId;
    /**
     * 产品名称
     */
    @NotNull(message = "产品id不能为空")
    private Long productId;
    /**
     * 客服公司
     */
    @NotNull(message = "客服公司不能为空")
    private Long sspAccountId;
    /**
     * 客服渠道简称
     */
    @NotEmpty(message = "渠道号不能为空")
    private String channel;
}
