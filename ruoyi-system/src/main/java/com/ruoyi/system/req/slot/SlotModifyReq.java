package com.ruoyi.system.req.slot;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 广告位配置请求参数
 *
 * <AUTHOR>
 * @date 2021-07-15
 */
@Data
public class SlotModifyReq implements Serializable {
    private static final long serialVersionUID = 550109381138339219L;

    /**
     * 广告位Id
     */
    private Long id;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 活动投放类型
     * @see com.ruoyi.common.enums.slot.SlotRedirectType
     */
    private Integer redirectType;

    /**
     * 活动投放参数
     */
    private String redirectValue;

    /**
     * 是否开启域名自动替换
     * 目前仅对支付宝落地页生效
     * @see com.ruoyi.common.enums.common.SwitchStatusEnum
     */
    private Integer autoReplace;

    /**
     * 返回挽留设置
     */
    private RetConfigReq retConfig;
}
