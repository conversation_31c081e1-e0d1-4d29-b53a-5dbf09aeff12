package com.ruoyi.system.req.advert;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 广告权重修改参数
 *
 * <AUTHOR>
 * @date 2023/05/15
 */
@Data
public class AdvertWeightModifyReq implements Serializable {
    private static final long serialVersionUID = -7622888778639572543L;

    /**
     * 广告id
     */
    @NotNull(message = "广告id不能为空")
    private Long id;

    /**
     * 权重
     */
    @NotNull(message = "权重不能为空")
    private Double weight;
}
