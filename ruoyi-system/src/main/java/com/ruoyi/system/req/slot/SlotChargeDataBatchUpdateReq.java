package com.ruoyi.system.req.slot;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 广告位结算设置数据批量修改请求参数
 * <AUTHOR>
 * @date 2023/5/23 10:02
 */
@Data
public class SlotChargeDataBatchUpdateReq implements Serializable {
    private static final long serialVersionUID = -5848777410458307506L;
    @NotNull(message = "广告位id不能为空")
    private Long slotId;
    /**
     * 数据列表
     */
    @NotEmpty(message = "数据不能为空")
    private List<SlotChargeDataUpdateReq> list;
}
