package com.ruoyi.system.req.invoice;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 发票新增请求参数
 * <AUTHOR>
 * @date 2022/10/20 2:41 下午
 */
@Data
public class InvoiceEditReq implements Serializable {
    private static final long serialVersionUID = -3695065891588454943L;
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 发票金额
     */
    @NotNull(message = "发票金额不能为空")
    private Integer invoiceAmount;
}
