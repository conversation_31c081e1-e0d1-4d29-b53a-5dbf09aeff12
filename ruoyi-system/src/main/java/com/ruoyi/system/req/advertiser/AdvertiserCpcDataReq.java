package com.ruoyi.system.req.advertiser;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告主cpc数据请求参数
 *
 * <AUTHOR>
 * @date 2022/4/7 7:57 下午
 */
@Data
public class AdvertiserCpcDataReq implements Serializable {
    private static final long serialVersionUID = 7366112684407091183L;

    /**
     * 起始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 不可见日期列表(内部字段)
     */
    private List<Date> invisibleDateList;

    /**
     * 广告主id(内部字段)
     */
    private Long advertiserId;

    /**
     * 广告id列表(内部字段)
     */
    private List<Long> advertIds;
}
