package com.ruoyi.system.req.landpage.library;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 落地页库请求参数
 *
 * <AUTHOR>
 * @date 2022/05/25
 */
@Data
public class LandpageLibraryReq implements Serializable {
    private static final long serialVersionUID = 3348891580467865414L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 落地页标识
     */
    private String key;

    /**
     * 落地页名称
     */
    private String name;

    /**
     * 落地页链接
     */
    @NotBlank(message = "落地页链接不能为空")
    private String url;

    /**
     * 落地页标签
     */
    private String tag;

    /**
     * 备注
     */
    private String remark;
}
