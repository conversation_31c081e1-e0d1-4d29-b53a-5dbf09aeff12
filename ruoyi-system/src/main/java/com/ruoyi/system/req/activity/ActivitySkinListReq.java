package com.ruoyi.system.req.activity;

import lombok.Data;

import java.io.Serializable;

/**
 * 活动皮肤列表请求参数
 *
 * <AUTHOR>
 * @date 2022-03-30
 */
@Data
public class ActivitySkinListReq implements Serializable {
    private static final long serialVersionUID = 8251766324161283704L;

    /**
     * 皮肤标识
     */
    private String skinCode;

    /**
     * 皮肤名称
     */
    private String skinName;

    /**
     * 活动皮肤类型:1.套猫,2.大转盘,3.卡包
     */
    private Integer skinType;

    /**
     * 活动类型:1.轻互动活动
     */
    private Integer activityType;
}
