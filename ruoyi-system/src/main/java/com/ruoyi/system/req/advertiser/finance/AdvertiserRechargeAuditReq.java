package com.ruoyi.system.req.advertiser.finance;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 广告主充值审核参数
 *
 * <AUTHOR>
 * @date 2022/09/14
 */
@Data
public class AdvertiserRechargeAuditReq implements Serializable {
    private static final long serialVersionUID = -6582187134588075766L;

    /**
     * 充值申请ID
     */
    @NotNull(message = "充值申请ID不能为空")
    private Long id;

    /**
     * 审核状态
     */
    @NotNull(message = "审核状态不能为空")
    @Min(value = 1, message = "无效的审核状态")
    @Max(value = 2, message = "无效的审核状态")
    private Integer auditStatus;

    /**
     * 审核理由
     */
    private String auditReason;
}
