package com.ruoyi.system.req.blindbox;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 盲盒落地页表单请求参数
 *
 * <AUTHOR>
 * @date 2022/06/13
 */
@Data
public class BlindBoxLandpageFormReq implements Serializable {
    private static final long serialVersionUID = -8701483918024608886L;

    /**
     * 落地页标识
     */
    @NotBlank(message = "落地页标识不能为空")
    private String landpageKey;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;
}
