package com.ruoyi.system.req.wx;

import lombok.Data;

/**
 * 微信支付下单参数
 *
 * <AUTHOR>
 * @date 2023/01/18
 */
@Data
public class WxOrderReq {

    /**
     * 落地页标识
     */
    private String lpk;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 支付类型:JSAPI-JSAPI支付(或小程序支付),NATIVE-Native支付,APP-app支付,MWEB-H5支付
     */
    private String tradeType;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 用户授权code(JSAPI使用，用于获取openid)
     */
    private String code;
}
