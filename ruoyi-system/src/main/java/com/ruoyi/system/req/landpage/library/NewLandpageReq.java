package com.ruoyi.system.req.landpage.library;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 新落地页请求参数
 *
 * <AUTHOR>
 * @date 2022/09/20
 */
@Data
public class NewLandpageReq implements Serializable {

    private static final long serialVersionUID = -3437350898375813452L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 落地页名称
     */
    @NotBlank(message = "落地页名称不能为空")
    private String name;
    /**
     * 落地页域名
     */
    @NotBlank(message = "域名不能为空")
    private String domain;

    /**
     * 落地页标签
     */
    @NotBlank(message = "标签不能为空")
    private String tag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 落地页皮肤ID
     */
    private Integer skinType;

    /**
     * 落地页页面配置
     */
    @NotBlank(message = "页面配置不能为空")
    private String pageConfig;
}
