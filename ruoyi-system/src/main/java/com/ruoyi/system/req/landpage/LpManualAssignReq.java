package com.ruoyi.system.req.landpage;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 表单手动分配参数
 *
 * <AUTHOR>
 * @date 2022/02/09
 */
@Data
public class LpManualAssignReq implements Serializable {
    private static final long serialVersionUID = 6937224226245720862L;

    /**
     * 表单ID列表
     */
    private List<Long> recordIds;

    /**
     * 广告主分配列表
     */
    private List<AdvertiserRate> list;

    /**
     * 最小时间间隔(分钟)
     */
    private Integer min;

    /**
     * 最大时间间隔(分钟)
     */
    private Integer max;

    @Data
    public static class AdvertiserRate {

        /**
         * 广告主ID
         */
        private Long advertiserId;

        /**
         * 比例[0,100]
         */
        private Integer rate;

        /**
         * 表单价格(分)
         */
        private Integer formPrice;

        /**
         * 备注
         */
        private String remark;
    }
}
