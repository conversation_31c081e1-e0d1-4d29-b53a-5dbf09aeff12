package com.ruoyi.system.req.manager;

import java.util.Date;

/**
 * 落地页送审请求参数
 *
 * <AUTHOR>
 * @date 2021/9/10
 */
public class LandpageAuditRecordReq {

    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 送审时间起始
     */
    private Date startDate;

    /**
     * 送审时间截止
     */
    private Date endDate;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 原链接
     */
    private String originUrl;

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getOriginUrl() {
        return originUrl;
    }

    public void setOriginUrl(String originUrl) {
        this.originUrl = originUrl;
    }
}
