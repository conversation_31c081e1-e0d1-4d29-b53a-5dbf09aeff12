package com.ruoyi.system.req.callback;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 圈量回调事件
 *
 * <AUTHOR>
 * @date 2022/4/19 10:21 上午
 */
@Data
public class QLCallbackEventParam implements Serializable {
    private static final long serialVersionUID = 258595035114102877L;

    /**
     * 圈量SCRM提供的AppKey
     */
    @JsonProperty("app_key")
    private String appKey;

    /**
     * 圈量SCRM上填写的Token
     */
    private String token;

    /**
     * 随机字符串
     */
    private String nonce;

    /**
     * 回调时间戳
     */
    private String timestamp;

    /**
     * 加密后的回调内容（解密后是一个json结构的字符串）
     */
    @JsonProperty("encoding_content")
    private String encodingContent;

    /**
     * 签名
     */
    private String signature;
}
