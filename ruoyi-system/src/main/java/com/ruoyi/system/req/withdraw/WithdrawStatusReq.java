package com.ruoyi.system.req.withdraw;

import com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新提现状态请求参数
 *
 * <AUTHOR>
 * @date 2021/9/14 7:38 下午
 */
@Data
public class WithdrawStatusReq implements Serializable {
    private static final long serialVersionUID = 5603233725379244335L;

    /**
     * 提现记录id
     */
    @NotNull(message = "提现id不能为空")
    private Long id;

    /**
     * 提现状态
     *
     * @see WithdrawCheckStatusEnum
     */
    @NotNull(message = "审核状态不能为空")
    private Integer withdrawStatus;

    /**
     * 审核原因
     */
    private String auditReason;
}
