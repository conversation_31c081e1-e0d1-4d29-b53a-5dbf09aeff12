package com.ruoyi.system.req.datashow;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 信用卡落地页表单记录请求参数
 *
 * <AUTHOR>
 * @date 2023/06/02
 */
@Data
public class CreditCardLandpageFromRecordReq implements Serializable {
    private static final long serialVersionUID = -4730528448224306260L;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;
}
