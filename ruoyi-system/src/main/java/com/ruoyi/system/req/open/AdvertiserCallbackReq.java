package com.ruoyi.system.req.open;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 广告主回调请求参数
 *
 * <AUTHOR>
 * @date 2022/04/15
 */
@Data
public class AdvertiserCallbackReq {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单号(兼容)
     */
    private String orderId;

    /**
     * 转化类型
     * @see com.ruoyi.common.enums.advert.ConvType
     */
    @NotNull(message = "转化类型不能为空")
    private Integer status;

    /**
     * 广告主标识
     */
    @NotBlank(message = "广告主标识不能为空")
    private String accessKey;

    /**
     * 转化时间戳(毫秒)
     */
    @NotNull(message = "转化时间不能为空")
    private Long submitTime;

    /**
     * 毫秒时间戳(毫秒)
     */
    @NotNull(message = "时间戳不能为空")
    private Long timestamp;

    /**
     * 签名
     */
    @NotBlank(message = "签名不能为空")
    private String sign;

    /**
     * 额外信息
     */
    private JSONObject ext;
}
