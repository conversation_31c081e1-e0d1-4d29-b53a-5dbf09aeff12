package com.ruoyi.system.req.shorturl;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 批量生成短链请求参数
 *
 * <AUTHOR>
 * @Date 2024/4/30 13:42
 */
@Data
public class ShortUrlBatchGenerateReq implements Serializable {

    private static final long serialVersionUID = 6733262504789206202L;

    /**
     * 类型
     *
     * @see com.ruoyi.common.enums.ShortUrlTypeEnum
     */
    private Integer urlType;

    /**
     * 原链接
     */
    private List<String> originUrlList;
}