package com.ruoyi.system.req.slot;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 广告位分成数据更新请求参数
 *
 * <AUTHOR>
 * @date 2021/9/2
 */
@Validated
@Data
public class SlotChargeDataUpdateReq implements Serializable {
    private static final long serialVersionUID = -9087356270719913278L;

    /** 广告位ID */
    @NotNull(message = "广告位id不能为空")
    private Long slotId;

    @NotNull(message = "诺禾结算金额不能为空")
    private Long nhCost;

    @NotNull(message = "外部结算金额不能为空")
    private Long outerCost;

    @NotNull(message = "计费方式不能为空")
    private Integer chargeType;

    @NotNull(message = "计费价格不能为空")
    private Integer chargePrice;

    @NotNull(message = "日期不能为空")
    private Date curDate;
    /**
     * 广告位请求pv
     */
    private Integer slotRequestPv;
    /**
     * 广告位请求uv
     */
    private Integer slotRequestUv;

    /**
     * 广告位点击pv(媒体反馈)
     */
//    @NotNull(message = "广告位点击pv不能为空")
    private Integer appSlotClickPv;
    /**
     * 广告位点击uv(媒体反馈)
     */
//    @NotNull(message = "广告位点击uv不能为空")
    private Integer appSlotClickUv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
//    @NotNull(message = "广告位曝光pv不能为空")
    private Integer appSlotExposurePv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
//    @NotNull(message = "广告位曝光pv不能为空")
    private Integer appSlotExposureUv;
}
