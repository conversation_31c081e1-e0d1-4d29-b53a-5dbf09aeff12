package com.ruoyi.system.message.rocketmq.message;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 刷新缓存消息内容
 *
 * <AUTHOR>
 * @date 2021/8/23
 */
@Data
public class RefreshCacheMessage implements Serializable {
    private static final long serialVersionUID = 4062089633395643253L;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告id列表
     */
    private List<Long> advertIds;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 配置ID列表
     */
    private List<Long> orientIds;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 配置Key
     */
    private String configKey;

    /**
     * 域名
     */
    private String domain;

    /**
     * 落地页标识
     */
    private String landpageKey;

    /**
     * 短链ID
     */
    private Long shortUrlId;

    /**
     * 广告主ID
     */
    private Long advertiserId;
}
