package com.ruoyi.system.message.rocketmq.producer;

import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.ruoyi.system.message.rocketmq.config.RocketMqConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DefaultMqProducer {

    @Autowired
    private RocketMqConfig mqConfig;

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ProducerBean buildProducer() {
        ProducerBean producer = new ProducerBean();
        producer.setProperties(mqConfig.getMqProperty());
        return producer;
    }
}
