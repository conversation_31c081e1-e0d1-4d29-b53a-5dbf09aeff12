package com.ruoyi.system.message.rocketmq.producer;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.system.message.rocketmq.config.RocketMqConstants;
import com.ruoyi.system.message.rocketmq.consumer.handler.InnerLogHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 日志消息发送生产者
 */
@Component
public class LogMqProducer extends RocketMqProducer {

    @Autowired
    private InnerLogHandler innerLogHandler;

    private static final AtomicInteger COUNT = new AtomicInteger(0);

    /**
     * 发送消息
     */
    public void sendMsg(InnerLogType logType, JSONObject json) {
        String msgKey = IdUtil.fastSimpleUUID();
        JSONObject message = (JSONObject) json.clone();
        message.put("type", logType.getType());
        message.put("time", DateUtils.getTime());
        message.put("msgKey", msgKey);
        // 节省成本，暂时不使用消息队列，直接用异步线程池执行
//        sendMsgAsync(getTag(), message.toString(), msgKey);
        GlobalThreadPool.logExecutorService.submit(() -> {
            innerLogHandler.consumer(message.toString());
            // 如果埋点线程堆积了，日志线程睡眠5分钟
            if (COUNT.incrementAndGet() >= 5000) {
                COUNT.set(0);
                if (degradeJudge()) {
                    ThreadUtil.sleep(300000);
                    COUNT.set(4000);
                }
            }
        });
    }

    public String getTag() {
        return RocketMqConstants.TAG_INNERLOG;
    }

    @Override
    public String getTopic() {
        return RocketMqConstants.TOPIC_LOG;
    }

    /**
     * 根据埋点线程池判断是否降级
     */
    private boolean degradeJudge() {
        try {
            ThreadPoolExecutor te = (ThreadPoolExecutor) GlobalThreadPool.statExecutorService;
            return te.getQueue().size() >= 1000;
        } catch (Exception e) {
            logger.error("degradeJudge error", e);
        }
        return false;
    }
}
