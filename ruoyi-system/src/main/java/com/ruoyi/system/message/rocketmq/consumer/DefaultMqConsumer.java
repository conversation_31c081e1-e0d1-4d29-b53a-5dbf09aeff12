package com.ruoyi.system.message.rocketmq.consumer;

import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.ruoyi.system.message.rocketmq.config.RocketMqConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Configuration
public class DefaultMqConsumer {

    @Autowired
    private RocketMqConfig mqConfig;

    @Autowired
    private RocketMqMessageListener messageListener;

    @Bean(name = "refreshConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean buildRefreshConsumer() {
        ConsumerBean consumerBean = new ConsumerBean();
        //配置文件
        Properties properties = mqConfig.getMqProperty();
        properties.setProperty(PropertyKeyConst.GROUP_ID, mqConfig.getRefresh().getGroupId());
        //将消费者线程数固定为2个 20为默认值
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "2");
        properties.setProperty(PropertyKeyConst.MessageModel, mqConfig.getRefresh().getMessageModel());
        consumerBean.setProperties(properties);
        //订阅关系
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic(mqConfig.getRefresh().getTopic());
        subscription.setExpression("*");
        subscriptionTable.put(subscription, messageListener);
        //订阅多个topic如上面设置
        consumerBean.setSubscriptionTable(subscriptionTable);
        return consumerBean;
    }

    @Bean(name = "billingConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean buildBillingConsumer() {
        ConsumerBean consumerBean = new ConsumerBean();
        //配置文件
        Properties properties = mqConfig.getMqProperty();
        properties.setProperty(PropertyKeyConst.GROUP_ID, mqConfig.getBilling().getGroupId());
        //将消费者线程数固定为1个 20为默认值
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "1");
        properties.setProperty(PropertyKeyConst.MessageModel, mqConfig.getBilling().getMessageModel());
        consumerBean.setProperties(properties);
        //订阅关系
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic(mqConfig.getBilling().getTopic());
        subscription.setExpression("*");
        subscriptionTable.put(subscription, messageListener);
        //订阅多个topic如上面设置
        consumerBean.setSubscriptionTable(subscriptionTable);
        return consumerBean;
    }

    @Bean(name = "logConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean buildLogConsumer() {
        ConsumerBean consumerBean = new ConsumerBean();
        //配置文件
        Properties properties = mqConfig.getMqProperty();
        properties.setProperty(PropertyKeyConst.GROUP_ID, mqConfig.getLog().getGroupId());
        //将消费者线程数固定为1个 20为默认值
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "1");
        properties.setProperty(PropertyKeyConst.MessageModel, mqConfig.getLog().getMessageModel());
        consumerBean.setProperties(properties);
        //订阅关系
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic(mqConfig.getLog().getTopic());
        subscription.setExpression("*");
        subscriptionTable.put(subscription, messageListener);
        //订阅多个topic如上面设置
        consumerBean.setSubscriptionTable(subscriptionTable);
        return consumerBean;
    }
}
