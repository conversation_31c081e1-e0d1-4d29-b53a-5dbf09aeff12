package com.ruoyi.system.message.rocketmq.producer;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.system.domain.billing.BillingMessage;
import com.ruoyi.system.message.rocketmq.config.RocketMqConstants;
import org.springframework.stereotype.Component;

/**
 * 计费消息发送生产者
 */
@Component
public class BillingMqProducer extends RocketMqProducer {

    /**
     * 发送消息
     */
    public void sendMsg(BillingMessage message) {
        sendMsgAsync(getTag(), JSON.toJSONString(message), IdUtil.fastSimpleUUID());
    }

    public String getTag() {
        return RocketMqConstants.TAG_BILLING;
    }

    @Override
    public String getTopic() {
        return RocketMqConstants.TOPIC_BILLING;
    }
}
