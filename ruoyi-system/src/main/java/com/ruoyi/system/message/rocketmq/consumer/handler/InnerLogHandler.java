package com.ruoyi.system.message.rocketmq.consumer.handler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UserAgentUtils;
import com.ruoyi.system.entity.common.AdvertOrderLogEntity;
import com.ruoyi.system.entity.common.InnerLogEntity;
import com.ruoyi.system.mapper.common.InnerLogMapper;
import com.ruoyi.system.message.rocketmq.config.RocketMqConstants;
import com.ruoyi.system.message.rocketmq.consumer.AbstractMessageResultHandler;
import com.ruoyi.system.message.rocketmq.consumer.RocketMqBatchMessageListener;
import com.ruoyi.system.message.rocketmq.consumer.RocketMqMessageListener;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.common.AdvertOrderLogService;
import com.ruoyi.system.service.common.MobileService;
import com.ruoyi.system.service.engine.cache.MobileCacheService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 日志消息处理器
 *
 * <AUTHOR>
 * @date 2021/8/23
 */
@Component
public class InnerLogHandler extends AbstractMessageResultHandler {

    @Autowired
    private InnerLogMapper innerLogMapper;

    @Autowired
    private AreaService areaService;

    @Autowired
    private MobileCacheService mobileCacheService;

    @Autowired
    private MobileService mobileService;

    @Autowired
    private AdvertOrderLogService advertOrderLogService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public String getListenTag() {
        return RocketMqConstants.TAG_INNERLOG;
    }

    @Override
    public void consumer(String message) {
        InnerLogEntity record =  null;
        try {
            record = parseMessage(message);
            if (null == record) {
                return;
            }

            // 补充设备信息
            if (StringUtils.isBlank(record.getMobileBrand()) && StringUtils.isNotBlank(record.getMobileModel())
                    && !StrUtil.containsAnyIgnoreCase(record.getMobileModel(), "build", "HMSCore", "Android")
                    && !StrUtil.equalsAnyIgnoreCase(record.getMobileModel(), "5G", "wv", "15", "Get")
                    && StrUtil.equalsIgnoreCase(record.getOsType(), "Android")
                    && !mobileCacheService.isMightContain(record.getMobileModel())) {
                String brand = UserAgentUtils.guessBrand(record.getUserAgent(), record.getMobileModel());
                mobileService.insert(record.getMobileModel(), brand);
                mobileCacheService.putBoolFilter(record.getMobileModel());
            }
            // 新增业务日志
            innerLogMapper.insert(record);
        } catch (Exception e) {
            logger.error("新增业务日志异常, message={}", message, e);
        }
        // 更新业务订单日志
        updateOrderLog(record);
        // 埋点
        stat(record);
    }

    @Override
    public void batchConsumer(List<String> messages) {
        Map<String, List<InnerLogEntity>> recordGroup = null;
        try {
            recordGroup = messages.stream().map(this::parseMessage).filter(Objects::nonNull).collect(Collectors.groupingBy(InnerLogEntity::getTbSuffix, Collectors.toList()));
        } catch (Exception e) {
            logger.error("批量解析业务日志异常, messages={}", JSON.toJSONString(messages), e);
        }
        if (null == recordGroup) {
            return;
        }
        // 补充设备信息
        recordGroup.values().forEach(records -> {
            records.forEach(record -> {
                try {
                    if (StringUtils.isBlank(record.getMobileBrand()) && StringUtils.isNotBlank(record.getMobileModel())
                            && !StrUtil.containsAnyIgnoreCase(record.getMobileModel(), "build", "HMSCore", "Android")
                            && !StrUtil.equalsAnyIgnoreCase(record.getMobileModel(), "5G", "wv", "15", "Get")
                            && StrUtil.equalsIgnoreCase(record.getOsType(), "Android")
                            && !mobileCacheService.isMightContain(record.getMobileModel())) {
                        String brand = UserAgentUtils.guessBrand(record.getUserAgent(), record.getMobileModel());
                        mobileService.insert(record.getMobileModel(), brand);
                        mobileCacheService.putBoolFilter(record.getMobileModel());
                    }
                } catch (Exception e) {
                    logger.error("补充设备信息异常, mobileModel={}", record.getMobileModel(), e);
                }

                // 更新业务订单日志
                updateOrderLog(record);

                // 埋点
                stat(record);
            });
        });
        // 批量新增业务日志
        recordGroup.forEach((tbSuffix, records) -> {
            try {
                innerLogMapper.batchInsert(tbSuffix, records);
            } catch (Exception e) {
                logger.error("批量新增业务日志异常, messages={}", JSON.toJSONString(messages), e);
            }
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 在消息处理器中注册
        RocketMqMessageListener.registerCallback(this);
        RocketMqBatchMessageListener.registerCallback(this);
    }

    private InnerLogEntity parseMessage(String message) {
        JSONObject json = JSON.parseObject(message);

        // 查询业务日志类型
        InnerLogType type = InnerLogType.getByType(json.getInteger("type"));
        if (null == type) {
            return null;
        }

        // 新增业务日志数据
        InnerLogEntity record = JSON.parseObject(message, InnerLogEntity.class);
        record.setGmtCreate(DateUtil.parseDateTime(json.getString("time")));
        record.setType(type.getType());
        record.setTypeDesc(type.getDesc());
        record.setReferer(StringUtils.defaultString(StrUtil.subPre(record.getReferer(), 255)));
        record.setUserAgent(StrUtil.subPre(record.getUserAgent(), 255));
        record.setIp(StrUtil.subPre(record.getIp(), 32));
        record.setTbSuffix(DateUtil.format(record.getGmtCreate(), "yyyyMMdd"));

        // IP解析
        if (StringUtils.isBlank(record.getProvince())) {
            Optional.ofNullable(areaService.ipAnalysisLocal(record.getIp())).ifPresent(ipArea -> {
                record.setProvince(ipArea.getProvince());
                record.setCity(ipArea.getCity());
                record.setAreaNum(ipArea.getCityAreaNum());
                record.setIsp(ipArea.getIsp());
            });
        }
        // UserAgent解析
        Optional.ofNullable(UserAgentUtils.analysisUserAgent(record.getUserAgent())).ifPresent(device -> {
            record.setOsType(StrUtil.subPre(device.getOsType(), 10));
            record.setOsVersion(StrUtil.subPre(device.getOsVersion(), 10));
            record.setMobileBrand(mobileCacheService.getBrandByModel(device.getModel()));
            record.setMobileModel(StrUtil.subPre(device.getModel(), 16));
        });

        // 非空字段处理
        record.setSlotId(NumberUtils.defaultLong(record.getSlotId()));
        record.setSrid(StringUtils.defaultString(record.getSrid()));
        record.setActivityId(NumberUtils.defaultLong(record.getActivityId()));
        record.setAdvertId(NumberUtils.defaultLong(record.getAdvertId()));
        record.setOrderId(StringUtils.defaultString(record.getOrderId()));
        record.setDeviceId(StringUtils.defaultString(record.getDeviceId()));
        record.setOsType(StringUtils.defaultString(record.getOsType()));
        record.setOsVersion(StringUtils.defaultString(record.getOsVersion()));
        record.setMobileBrand(StringUtils.defaultString(record.getMobileBrand()));
        record.setMobileModel(StringUtils.defaultString(record.getMobileModel()));
        return record;
    }

    /**
     * 更新业务订单日志
     *
     * @param record 业务日志
     */
    private void updateOrderLog(InnerLogEntity record) {
        if (null == record || record.getType() < InnerLogType.ADVERT_LAUNCH.getType()) {
            return;
        }

        AdvertOrderLogEntity updateAdvertOrderLog = new AdvertOrderLogEntity();

        try {
            updateAdvertOrderLog.setId(getOrInitAdvertOrderLogId(record));

            InnerLogType type = InnerLogType.getByType(record.getType());
            switch (type) {
                case ADVERT_LAUNCH:
                    updateAdvertOrderLog.setAdLaunch(1);
                    updateAdvertOrderLog.setUnitPrice(record.getUnitPrice());
                    updateAdvertOrderLog.setLaunchSeq(NumberUtils.defaultInt(record.getLaunchSeq(), 1));
                    break;
                case ADVERT_EXPOSURE:
                    updateAdvertOrderLog.setAdExposure(1);
                    break;
                case ADVERT_CLICK:
                    updateAdvertOrderLog.setAdClick(1);
                    break;
                case ADVERT_BILLING:
                    updateAdvertOrderLog.setCpcCost(record.getUnitPrice());
                    break;
                case LANDPAGE_EXPOSURE:
                    updateAdvertOrderLog.setLpExposure(1);
                    break;
                case LANDPAGE_CLICK:
                    updateAdvertOrderLog.setLpClick(1);
                    updateAdvertOrderLog.setTheoryCost(record.getAssessCost());
                    break;
                case BLIND_BOX_POPUP_CLICK:
                    updateAdvertOrderLog.setTake(1);
                    break;
                case CONVERT_EVENT:
                    if (Objects.equals(record.getConvType(), ConvType.PAY.getType())) {
                        updateAdvertOrderLog.setPay(1);
                    } else if (Objects.equals(record.getConvType(), ConvType.REFUND.getType())) {
                        updateAdvertOrderLog.setRefund(1);
                    } else if (Objects.equals(record.getConvType(), ConvType.REGISTER.getType())) {
                        updateAdvertOrderLog.setRegister(1);
                    } else {
                        return;
                    }
                    break;
                default:
                    return;
            }
        } catch (Exception e) {
            logger.error("更新业务订单日志异常", e);
        }
        if (null != updateAdvertOrderLog.getId()) {
            advertOrderLogService.updateById(updateAdvertOrderLog);
        }
    }

    /**
     * 查询或初始化业务日志ID
     *
     * @param record 业务日志
     * @return 业务日志ID
     */
    private Long getOrInitAdvertOrderLogId(InnerLogEntity record) {
        String key = EngineRedisKeyFactory.K103.join(record.getOrderId());
        Long orderLogId = redisCache.getCacheObject(key);
        if (null != orderLogId) {
            return orderLogId;
        }

        orderLogId = advertOrderLogService.selectIdByOrderId(record.getOrderId());
        if (null == orderLogId) {
            AdvertOrderLogEntity orderLog = new AdvertOrderLogEntity();
            orderLog.setSrid(record.getSrid());
            orderLog.setOrderId(record.getOrderId());
            orderLog.setCurDate(DateUtil.beginOfDay(record.getGmtCreate()));
            orderLog.setCurHour(DateUtil.hour(record.getGmtCreate(), true));
            orderLog.setAdvertId(record.getAdvertId());
            orderLog.setSlotId(record.getSlotId());
            orderLog.setAppId(record.getAppId());
            orderLog.setActivityId(record.getActivityId());
            orderLog.setOrientId(record.getOrientId());
            orderLog.setMaterialId(record.getMaterialId());
            orderLog.setUnitPrice(record.getUnitPrice());
            orderLog.setDeviceId(record.getDeviceId());
            orderLog.setConsumerId(record.getConsumerId());
            orderLog.setProvince(record.getProvince());
            orderLog.setCity(record.getCity());
            orderLog.setIsp(record.getIsp());
            orderLog.setIp(record.getIp());
            orderLog.setOsType(record.getOsType());
            orderLog.setOsVersion(record.getOsVersion());
            orderLog.setMobileBrand(record.getMobileBrand());
            orderLog.setMobileModel(record.getMobileModel());
            orderLog.setPCtr(record.getPCtr());
            orderLog.setPCvr(record.getPCvr());
            orderLog.setAssessType(record.getAssessType());
            advertOrderLogService.insert(orderLog);
            orderLogId = advertOrderLogService.selectIdByOrderId(record.getOrderId());
        }
        redisCache.setCacheObject(key, orderLogId, 10, TimeUnit.MINUTES);
        return orderLogId;
    }

    private void stat(InnerLogEntity record) {
        if (!SpringEnvironmentUtils.isProd()) {
            return;
        }
        try {
            String uid = null != record.getConsumerId() ? String.valueOf(record.getConsumerId()) : record.getDeviceId();
            // action: type.appId.slotId.activityId.advertId.orientId.materialId.province.city.isp.os.mobile.convType
            String action = record.getType() +
                    "." + Convert.toStr(record.getAppId(), "0") +
                    "." + Convert.toStr(record.getSlotId(), "0") +
                    "." + Convert.toStr(record.getActivityId(), "0") +
                    "." + Convert.toStr(record.getAdvertId(), "0") +
                    "." + Convert.toStr(record.getOrientId(), "0") +
                    "." + Convert.toStr(record.getMaterialId(), "0") +
                    "." + StringUtils.defaultIfBlank(record.getProvince(), "0") +
                    "." + StringUtils.defaultIfBlank(record.getCity(), "0") +
                    "." + StringUtils.defaultIfBlank(record.getIsp(), "0") +
                    "." + StringUtils.defaultIfBlank(record.getOsType(), "0") +
                    "." + StringUtils.defaultIfBlank(record.getMobileBrand(), "0") +
                    "." + Convert.toStr(record.getConvType(), "0");
            HttpUtil.createGet(StrUtil.format("http://172.20.216.118:8905/stat?scene=3&uid={}&action={}",
                    uid, action)).executeAsync();
        } catch (Exception e) {
            logger.error("广告埋点异常, record={}", JSON.toJSONString(record), e);
        }
    }
}
