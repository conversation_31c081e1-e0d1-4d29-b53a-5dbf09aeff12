package com.ruoyi.system.message.rocketmq.consumer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;

public abstract class AbstractMessageResultHandler implements InitializingBean {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 获取消费tag
     */
    public abstract String getListenTag();

    /**
     * 消费消息
     */
    public abstract void consumer(String message);

    /**
     * 批量消费消息
     */
    public void batchConsumer(List<String> messages) {}
}
