package com.ruoyi.system.message.rocketmq.producer;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.system.message.rocketmq.message.RefreshCacheMessage;
import org.springframework.stereotype.Component;

import static com.ruoyi.system.message.rocketmq.config.RocketMqConstants.*;

/**
 * 检查预算消息发送生产者
 */
@Component
public class CheckBudgetMqProducer extends RocketMqProducer {

    /**
     * 发送消息
     */
    public void sendMsg(RefreshCacheMessage message) {
        sendMsgAsync(getTag(), JSON.toJSONString(message), IdUtil.fastSimpleUUID());
    }

    public String getTag() {
        return TAG_CHECK_BUDGET;
    }

    @Override
    public String getTopic() {
        return TOPIC_REFRESH;
    }
}
