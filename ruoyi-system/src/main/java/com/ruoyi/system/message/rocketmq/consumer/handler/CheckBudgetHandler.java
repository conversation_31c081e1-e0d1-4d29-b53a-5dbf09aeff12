package com.ruoyi.system.message.rocketmq.consumer.handler;

import com.alibaba.fastjson.JSON;
import com.ruoyi.system.message.rocketmq.config.RocketMqConstants;
import com.ruoyi.system.service.billing.BudgetService;
import com.ruoyi.system.message.rocketmq.consumer.AbstractMessageResultHandler;
import com.ruoyi.system.message.rocketmq.consumer.RocketMqMessageListener;
import com.ruoyi.system.message.rocketmq.message.RefreshCacheMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 检查预算消息处理器
 *
 * <AUTHOR>
 * @date 2021/8/26
 */
@Component
public class CheckBudgetHandler extends AbstractMessageResultHandler {

    @Autowired
    private BudgetService budgetService;

    @Override
    public String getListenTag() {
        return RocketMqConstants.TAG_CHECK_BUDGET;
    }

    @Override
    public void consumer(String message) {
        RefreshCacheMessage msg = JSON.parseObject(message, RefreshCacheMessage.class);
        if (null != msg) {
            budgetService.checkBudget(msg.getAdvertId());
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 在消息处理器中注册
        RocketMqMessageListener.registerCallback(this);
    }
}
