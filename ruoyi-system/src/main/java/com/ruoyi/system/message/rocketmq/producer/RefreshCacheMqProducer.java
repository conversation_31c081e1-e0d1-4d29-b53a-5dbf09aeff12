package com.ruoyi.system.message.rocketmq.producer;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.system.message.rocketmq.message.RefreshCacheMessage;
import com.ruoyi.system.message.rocketmq.config.RocketMqConstants;
import org.springframework.stereotype.Component;

/**
 * 刷新缓存消息发送生产者
 */
@Component
public class RefreshCacheMqProducer extends RocketMqProducer {

    /**
     * 发送消息
     */
    public void sendMsg(RefreshCacheMessage message) {
        sendMsgAsync(getTag(), JSON.toJSONString(message), IdUtil.fastSimpleUUID());
    }

    public String getTag() {
        return RocketMqConstants.TAG_ADVERT_CACHE;
    }

    @Override
    public String getTopic() {
        return RocketMqConstants.TOPIC_REFRESH;
    }
}
