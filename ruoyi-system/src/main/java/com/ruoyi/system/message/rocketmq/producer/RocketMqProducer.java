package com.ruoyi.system.message.rocketmq.producer;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.ruoyi.common.utils.GlobalThreadPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

public abstract class RocketMqProducer {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ProducerBean rocketMqProducer;

    public void sendMsg(String tag, String body, String uniqueKey) {
        Message msg = new Message(getTopic(), tag, uniqueKey, body.getBytes(StandardCharsets.UTF_8));
        try {
            rocketMqProducer.send(msg);
        } catch (Exception e) {
            logger.error("RocketMQ消息发送失败", e);
        }
    }

    public void sendMsgAsync(String tag, String body, String uniqueKey) {
        GlobalThreadPool.executorService.execute(() -> {
            try {
                Message msg = new Message(getTopic(), tag, uniqueKey, body.getBytes(StandardCharsets.UTF_8));
                rocketMqProducer.send(msg);
            } catch (Exception e) {
                logger.error("RocketMQ消息异步发送失败", e);
            }
        });
    }

    public abstract String getTopic();
}
