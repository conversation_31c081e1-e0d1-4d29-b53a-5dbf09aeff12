package com.ruoyi.system.message.rocketmq.consumer.handler;

import com.alibaba.fastjson.JSON;
import com.ruoyi.system.message.rocketmq.config.RocketMqConstants;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.engine.cache.ActivityCacheService;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.service.engine.cache.AdvertiserCacheService;
import com.ruoyi.system.service.engine.cache.DomainCacheService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.engine.cache.ShortUrlCacheService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import com.ruoyi.system.message.rocketmq.consumer.AbstractMessageResultHandler;
import com.ruoyi.system.message.rocketmq.consumer.RocketMqMessageListener;
import com.ruoyi.system.message.rocketmq.message.RefreshCacheMessage;
import com.ruoyi.system.service.system.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 刷新缓存消息处理器
 *
 * <AUTHOR>
 * @date 2021/8/4
 */
@Component
public class RefreshCacheHandler extends AbstractMessageResultHandler {

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private ActivityCacheService activityCacheService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private DomainCacheService domainCacheService;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private ShortUrlCacheService shortUrlCacheService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private AdvertiserCacheService advertiserCacheService;

    @Override
    public String getListenTag() {
        return RocketMqConstants.TAG_ADVERT_CACHE;
    }

    @Override
    public void consumer(String message) {
        RefreshCacheMessage msg = JSON.parseObject(message, RefreshCacheMessage.class);
        if (null != msg) {
            advertCacheService.refreshAdvertCache(msg.getAdvertId());
            advertCacheService.refreshAdvertCache(msg.getAdvertIds());
            advertCacheService.refreshAdvertOrientationCache(msg.getOrientId());
            advertCacheService.refreshAdvertOrientationCache(msg.getOrientIds());
            slotCacheService.refreshSlotCache(msg.getSlotId());
            activityCacheService.refreshActivityCache(msg.getActivityId());
            domainCacheService.refreshDomainCache(msg.getDomain());
            landpageCacheService.refreshLandpageCache(msg.getLandpageKey());
            shortUrlCacheService.refreshShortUrlCache(msg.getShortUrlId());
            sysConfigService.refreshConfigCache(msg.getConfigKey());
            whitelistService.refreshCache(msg.getConfigKey());
            advertiserCacheService.refreshAdvertiserCache(msg.getAdvertiserId());
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 在消息处理器中注册
        RocketMqMessageListener.registerCallback(this);
    }
}
