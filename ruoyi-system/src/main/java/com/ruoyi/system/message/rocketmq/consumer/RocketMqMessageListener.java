package com.ruoyi.system.message.rocketmq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import static com.ruoyi.system.message.rocketmq.config.RocketMqConstants.TAG_INNERLOG;

@Component
public class RocketMqMessageListener implements MessageListener {

    private static final Logger logger = LoggerFactory.getLogger(RocketMqMessageListener.class);

    private static final Map<String, AbstractMessageResultHandler> HANDLER_CALLBACK_MAP = new ConcurrentHashMap<>();

    @Override
    public Action consume(Message message, ConsumeContext context) {
        try {
            AbstractMessageResultHandler handler = HANDLER_CALLBACK_MAP.get(message.getTag());
            if (handler != null) {
                String body = new String(message.getBody(), StandardCharsets.UTF_8);
                if (!Objects.equals(message.getTag(), TAG_INNERLOG)) {
                    logger.info("Tag:{}, MsgID:{}, MsgKey:{}, Msg:{}",
                            message.getTag(), message.getMsgID(), message.getKey(), body);
                }
                handler.consumer(body);
            }
            return Action.CommitMessage;
        } catch (Exception e) {
            logger.error("RocketMQ消费失败", e);
            return Action.ReconsumeLater;
        }
    }

    public static void registerCallback(AbstractMessageResultHandler handler) {
        // 当不同topic存在相同的tag时，HANDLER_CALLBACK_MAP 会将key覆盖，只有一个topic下的tag被业务处理
        // 上述代码修改较为复杂，故先简单判断key重复的情况
        String key = handler.getListenTag();
        if (HANDLER_CALLBACK_MAP.containsKey(key)) {
            throw new IllegalStateException(String.format("当前RocketMq已有tag[%s], 请勿重复添加!", handler.getListenTag()));
        }
        HANDLER_CALLBACK_MAP.put(key, handler);
    }
}
