package com.ruoyi.system.message.rocketmq.consumer;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.batch.BatchMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Component
public class RocketMqBatchMessageListener implements BatchMessageListener {

    private static final Logger logger = LoggerFactory.getLogger(RocketMqBatchMessageListener.class);

    private static final Map<String, AbstractMessageResultHandler> HANDLER_CALLBACK_MAP = new ConcurrentHashMap<>();

    @Override
    public Action consume(List<Message> list, ConsumeContext consumeContext) {
        try {
            Map<String, List<Message>> messageGroup = list.stream().collect(Collectors.groupingBy(Message::getTag, Collectors.toList()));
            messageGroup.forEach((tag, messages) -> {
                AbstractMessageResultHandler handler = HANDLER_CALLBACK_MAP.get(tag);
                if (handler != null) {
                    List<String> body = messages.stream().map(message -> new String(message.getBody(), StandardCharsets.UTF_8)).collect(Collectors.toList());
                    handler.batchConsumer(body);
                }
            });
            return Action.CommitMessage;
        } catch (Exception e) {
            logger.error("RocketMQ批量消费失败", e);
            return Action.ReconsumeLater;
        }
    }

    public static void registerCallback(AbstractMessageResultHandler handler) {
        // 当不同topic存在相同的tag时，HANDLER_CALLBACK_MAP 会将key覆盖，只有一个topic下的tag被业务处理
        // 上述代码修改较为复杂，故先简单判断key重复的情况
        String key = handler.getListenTag();
        if (HANDLER_CALLBACK_MAP.containsKey(key)) {
            throw new IllegalStateException(String.format("当前RocketMq已有tag[%s], 请勿重复添加!", handler.getListenTag()));
        }
        HANDLER_CALLBACK_MAP.put(key, handler);
    }
}
