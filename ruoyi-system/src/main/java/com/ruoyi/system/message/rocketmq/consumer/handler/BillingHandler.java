package com.ruoyi.system.message.rocketmq.consumer.handler;

import com.alibaba.fastjson.JSON;
import com.ruoyi.system.domain.billing.BillingMessage;
import com.ruoyi.system.message.rocketmq.config.RocketMqConstants;
import com.ruoyi.system.service.billing.BillingService;
import com.ruoyi.system.message.rocketmq.consumer.AbstractMessageResultHandler;
import com.ruoyi.system.message.rocketmq.consumer.RocketMqMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 计费消息处理器
 *
 * <AUTHOR>
 * @date 2021/8/23
 */
@Component
public class BillingHandler extends AbstractMessageResultHandler {

    @Autowired
    private BillingService billingService;

    @Override
    public String getListenTag() {
        return RocketMqConstants.TAG_BILLING;
    }

    @Override
    public void consumer(String message) {
        billingService.billing(JSON.parseObject(message, BillingMessage.class));
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 在消息处理器中注册
        RocketMqMessageListener.registerCallback(this);
    }
}
