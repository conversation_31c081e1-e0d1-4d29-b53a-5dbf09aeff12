package com.ruoyi.system.service.adengine.filter;

import cn.hutool.core.util.RandomUtil;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.entity.datashow.MobileHapDataEntity;
import com.ruoyi.system.service.adengine.AdvertFilter;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 快应用优选过滤
 * 对快应用唤起率过低的设备，概率出券
 */
@Slf4j
@Component
public class HapPreferFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();
        MobileHapDataEntity data = context.getMobileHapData();
        try {
            // 仅针对快应用广告进行优选过滤
            if (!LandpageUtil.isQuickApp(ad.getLandpageUrl())) {
                return true;
            }
            // 设备优先的阈值: 券点击pv/uv >= 15
            if (null != data && data.getAdClickUv() >= 15 && data.getAdClickPv() >= 15) {
                double rate = ((double) data.getHapLaunchPv()) / data.getAdClickPv();
                if (rate < 0.01) {
                    // 快应用唤起率不足1%，切1%的量
                    return RandomUtil.randomInt(0, 100) < 1;
                } else if (rate < 0.1) {
                    // 快应用唤起率不足10%的，切10%的量
                    return RandomUtil.randomInt(0, 100) < 10;
                } else if (rate < 0.3) {
                    // 快应用唤起率不足30%的，切20%的量
                    return RandomUtil.randomInt(0, 100) < 20;
                }  else if (rate < 0.5) {
                    // 快应用唤起率不足50%的，切80%的量
                    return RandomUtil.randomInt(0, 100) < 80;
                }
            }
        } catch (Exception e) {
            log.error("快应用优选过滤异常, landpageUrl={}, userAgent={}", ad.getLandpageUrl(), context.getUserAgent(), e);
        }
        return true;
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.HAP_PREFER;
    }
}
