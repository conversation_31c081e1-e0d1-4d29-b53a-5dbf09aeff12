package com.ruoyi.system.service.open.impl;

import com.ruoyi.system.entity.open.ZxsCallbackRecord;
import com.ruoyi.system.mapper.open.ZxsCallbackRecordMapper;
import com.ruoyi.system.service.open.ZxsCallbackRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 摘星社回调记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-12
 */
@Service
public class ZxsCallbackRecordServiceImpl implements ZxsCallbackRecordService {

    @Autowired
    private ZxsCallbackRecordMapper zxsCallbackRecordMapper;

    @Override
    public int insert(ZxsCallbackRecord record) {
        return zxsCallbackRecordMapper.insert(record);
    }
}
