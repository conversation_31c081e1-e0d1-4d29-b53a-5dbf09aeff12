package com.ruoyi.system.service.engine.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.ConvertUploadStatus;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.adengine.SlotBiddingConfigDto;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.convert.ConvertUploadRecordEntity;
import com.ruoyi.system.entity.convert.ConvertUploadRuleEntity;
import com.ruoyi.system.service.convert.ConvertUploadRecordService;
import com.ruoyi.system.service.convert.ConvertUploadRuleService;
import com.ruoyi.system.service.datasource.SlotConvOpenDataService;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.open.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 外部回调接口实现
 *
 * <AUTHOR>
 * @date 2022/01/05
 */
@Slf4j
@Service
public class CallbackServiceImpl implements CallbackService {

    private static final JSONObject EMPTY_JSON = new JSONObject(0);

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private FanliService fanliService;

    @Autowired
    private ConvertUploadRuleService convertUploadRuleService;

    @Autowired
    private ConvertUploadRecordService convertUploadRecordService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private SlotConvOpenDataService slotConvOpenDataService;

    @Autowired
    private JizhunService jizhunService;

    @Override
    public void cacheParameter(HttpServletRequest request, String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }
        JSONObject param = new JSONObject();
        Optional.ofNullable(IpUtils.getIpAddr(request)).ifPresent(s -> param.put("ip", s));
        // 爱奇艺参数
        Optional.ofNullable(request.getParameter("impress_id")).ifPresent(s -> param.put("impressId", s));
        // 美团参数
        Optional.ofNullable(request.getParameter("aid")).ifPresent(s -> param.put("aid", s));
        Optional.ofNullable(request.getParameter("cid")).ifPresent(s -> param.put("cid", s));
        Optional.ofNullable(request.getParameter("exposure_id")).ifPresent(s -> param.put("exposure_id", s));
        // 喜马拉雅参数
        Optional.ofNullable(request.getParameter("callback")).ifPresent(s -> param.put("callback", UrlUtils.urlDecode(s)));
        Optional.ofNullable(request.getParameter("callbackBase64")).ifPresent(s -> param.put("callback", Base64.decodeStr(UrlUtils.urlDecode(s))));
        // 趣头条参数
        Optional.ofNullable(request.getParameter("callback_url")).ifPresent(s -> param.put("qttCallback", UrlUtils.urlDecode(s)));
        Optional.ofNullable(request.getParameter("qttCallbackBase64")).ifPresent(s -> param.put("qttCallback", Base64.decodeStr(UrlUtils.urlDecode(s))));
        // vivo参数
        Optional.ofNullable(request.getParameter("requestid")).ifPresent(s -> param.put("requestId", s));
        if (param.containsKey("requestId")) {
            Optional.ofNullable(request.getParameter("adid")).ifPresent(s -> param.put("creativeId", s));
            // 自己定义的，用于透传vivo账号的advertiserId
            Optional.ofNullable(request.getParameter("adextra")).ifPresent(s -> param.put("adextra", s));
        }
        // 快手参数
        Optional.ofNullable(request.getParameter("callback")).ifPresent(s -> param.put("ksCallback", s));
        Optional.ofNullable(request.getParameter("callbackBase64")).ifPresent(s -> param.put("ksCallback", Base64.decodeStr(s)));
        // 微博参数
        Optional.ofNullable(request.getParameter("mark_id")).ifPresent(s -> param.put("markId", s));
        // 广点通参数
        Optional.ofNullable(request.getParameter("qz_gdt")).ifPresent(s -> param.put("qqClickId", s));
        Optional.ofNullable(request.getParameter("gdt_vid")).ifPresent(s -> param.put("qqClickId", s));
        // 七猫参数
        Optional.ofNullable(request.getParameter("iam")).ifPresent(s -> param.put("iam", s));
        Optional.ofNullable(request.getParameter("dim")).ifPresent(s -> param.put("dim", s));
        Optional.ofNullable(request.getParameter("oaidm")).ifPresent(s -> param.put("oaidm", s));
        Optional.ofNullable(request.getParameter("aim")).ifPresent(s -> param.put("aim", s));
        Optional.ofNullable(request.getParameter("clickid")).ifPresent(s -> param.put("clickid", s));
        if (param.containsKey("clickid") && StringUtils.isBlank(request.getParameter("adid"))) {
            param.put("os", "Android");
            if (StrUtil.contains(request.getHeader("User-Agent"), "iPhone")) {
                param.put("os", "iOS");
            }
        }
        // 巨量参数
//        Optional.ofNullable(request.getParameter("clickid")).ifPresent(s -> param.put("clickid", s));
        if (param.containsKey("clickid")) {
            Optional.ofNullable(request.getParameter("adid")).ifPresent(s -> param.put("adid", s));
            Optional.ofNullable(request.getParameter("creativeid")).ifPresent(s -> param.put("creativeid", s));
            Optional.ofNullable(request.getParameter("creativetype")).ifPresent(s -> param.put("creativetype", s));
            Optional.ofNullable(request.getParameter("oceanTrackId")).ifPresent(s -> param.put("oceanTrackId", s));
            Optional.ofNullable(request.getParameter("oceanRequestId")).ifPresent(s -> param.put("oceanRequestId", s));
        }
        // 芒果参数
        Optional.ofNullable(request.getParameter("did")).ifPresent(s -> param.put("mgtvDid", s));
        Optional.ofNullable(request.getParameter("convertid")).ifPresent(s -> param.put("mgtvConvertid", s));
        if (param.containsKey("mgtvDid")) {
            param.put("mgtvOs", "0");
            if (StrUtil.contains(request.getHeader("User-Agent"), "iPhone")) {
                param.put("mgtvOs", "1");
            }
        }
        // oppo参数
        Optional.ofNullable(request.getParameter("tid")).ifPresent(s -> param.put("tid", UrlUtils.urlEncode(UrlUtils.urlDecode(s)).replaceAll("\\+", "%2B")));
        Optional.ofNullable(request.getParameter("pageId")).ifPresent(s -> param.put("pageId", s));
        Optional.ofNullable(request.getParameter("lbid")).ifPresent(s -> param.put("lbid", s));
        Optional.ofNullable(request.getParameter("ownerId")).ifPresent(s -> param.put("ownerId", s));
        if (param.containsKey("tid")) {
            Optional.ofNullable(IpUtils.getIpAddr(request)).ifPresent(s -> param.put("ip", s));
        }
        // 百度参数
        Optional.ofNullable(request.getParameter("bd_vid")).ifPresent(s -> param.put("bd_vid", s));
        Optional.ofNullable(request.getParameter("bd_token")).ifPresent(s -> param.put("bd_token", s));
        // 返利参数
        Optional.ofNullable(request.getParameter("click_id")).ifPresent(s -> param.put("click_id", s));
        // 优酷参数
        Optional.ofNullable(request.getParameter("trackid")).ifPresent(s -> param.put("trackid", s));
        if (param.containsKey("trackid")) {
            Optional.ofNullable(request.getParameter("creativeid")).ifPresent(s -> param.put("creativeid", s));
            Optional.ofNullable(IpUtils.getIpAddr(request)).ifPresent(s -> param.put("client_ip", s));
        }
        // 小米参数
        Optional.ofNullable(request.getParameter("conversionId")).ifPresent(s -> param.put("conversionId", s));
        if (param.containsKey("conversionId")) {
            Optional.ofNullable(request.getParameter("webConversionId")).ifPresent(s -> param.put("webConversionId", s));
            Optional.ofNullable(request.getParameter("conversion_debug")).ifPresent(s -> param.put("conversion_debug", s));
            Optional.ofNullable(request.getParameter("logExtra")).ifPresent(s -> param.put("logExtra", s));
            Optional.ofNullable(request.getParameter("convType")).ifPresent(s -> param.put("convType", s));
        }
        // 极准参数
        Optional.ofNullable(request.getParameter("jcid")).ifPresent(s -> param.put("jcid", s));
        // 集集媒体
        Optional.ofNullable(request.getParameter("taskIdentification")).ifPresent(s -> param.put("taskIdentification", s));
        // 三维推
        Optional.ofNullable(request.getParameter("requestId")).ifPresent(s -> param.put("requestId", s));
        // 灯火
        Optional.ofNullable(request.getParameter("request")).ifPresent(s -> param.put("request", s));
        // Sigmob
        Optional.ofNullable(request.getParameter("get_callback")).ifPresent(s -> param.put("get_callback", s));
        // 搜狐
        Optional.ofNullable(request.getParameter("shcallback")).ifPresent(s -> param.put("shcallback", UrlUtils.urlEncode(s.replace(" ", "+"))));
        // 捷停车
        Optional.ofNullable(request.getParameter("jtcAdRequestId")).ifPresent(s -> param.put("jtcAdRequestId", s));

        // 固定收益上报，账户标识
        Optional.ofNullable(request.getParameter("hu")).ifPresent(s -> param.put("hu", s));

        // 缓存数据，1天
        redisCache.setCacheObject(EngineRedisKeyFactory.K033.join(orderId), param.toString(), 1, TimeUnit.DAYS);
    }

    @Override
    public void cacheActualSlotUrl(HttpServletRequest request, String srid) {
        if (StringUtils.isBlank(srid)) {
            return;
        }
        try {
            // 获取访问域名
            String domain = request.getServerName();
            // 获取http还是https
            String scheme = request.getScheme();
            // 获取访问路径
            String path = request.getRequestURI();
            // 获取访问参数
            String queryString = request.getQueryString();
            // 构造完整的广告位链接
            String slotUrl = scheme + "://" + domain + path + "?" + queryString;
            redisCache.setCacheObject(EngineRedisKeyFactory.K134.join(srid), slotUrl, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("缓存真实投放的广告位链接异常, srid={}", srid, e);
        }
    }

    @Override
    public JSONObject getParameterFromCache(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return EMPTY_JSON;
        }
        String value = redisCache.getCacheObject(EngineRedisKeyFactory.K033.join(orderId));
        return null != value ? Optional.ofNullable(JSON.parseObject(value)).orElse(EMPTY_JSON) : EMPTY_JSON;
    }

    /**
     * 判断是否要跳过上报
     */
    @Override
    public boolean jumpCallback(Order order, SlotBiddingConfigDto biddingConfig, String hu) {
        // 广告位固定收益设置
        Date date = DateUtil.beginOfDay(order.getGmtCreate());
        String dateStr = DateUtil.formatDate(date);
        Long slotId = order.getSlotId();

        // 2.广告位已经上报给快手产生的成本累计【已经成本】
        long slotCost = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K066.join(dateStr, slotId, hu)));

        // 3.上报后远期成本：判断广告位真实消耗>=【已经成本】+1*【广告位当前固定收益元】
        long slotCostFuture = slotCost + biddingConfig.getConvPrice();

        // 4.当前广告位：广告主转化个数、当前广告位广告消耗【分】
        long slotAdUp = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K065.join(dateStr, slotId, hu)));

        // 5.广告消耗
        long slotConsume;
        if (biddingConfig.isTrueConsume()) {
            // 真实消耗(CPC计费累加)
            slotConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K087.join(dateStr, slotId, hu)));
        } else {
            // 理论消耗(考核成本累加)
            slotConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K077.join(dateStr, slotId, hu)));
        }

        // 广告位多账户列表
        if (StringUtils.isNotBlank(hu)) {
            redisCache.addCacheSet(EngineRedisKeyFactory.K089.join(dateStr, slotId), hu);
        }

        // 不上报逻辑：一、非冷启动；二、广告位真实消耗< 上报后远期成本
        int coldStart = NumberUtils.defaultInt(biddingConfig.getColdStart(), 1);
        if (slotAdUp > coldStart && slotConsume < slotCostFuture) {
            log.info("slotId为{},账户为{}, 上报跳过, 自动化控成本限制, orderId={}, advertId={}, slotCost={}, slotConsume={}, costThreshold={}, curCost={}, slotAdUp={}",
                    slotId, hu, order.getOrderId(), order.getAdvertId(), slotCost,
                    NumberUtils.fenToYuan(slotConsume), NumberUtils.fenToYuan(biddingConfig.getConvPrice()), NumberUtils.fenToYuan(slotCostFuture), slotAdUp);
            return true;
        } else {
            log.info("slotId为{},账户为{}, 正常上报, 自动化控成本限制, orderId={}, advertId={}, slotCost={}, slotConsume={}, costThreshold={}, curCost={}, slotAdUp={}",
                    slotId, hu, order.getOrderId(), order.getAdvertId(), slotCost,
                    NumberUtils.fenToYuan(slotConsume), NumberUtils.fenToYuan(biddingConfig.getConvPrice()), NumberUtils.fenToYuan(slotCostFuture), slotAdUp);
        }
        return false;
    }

    /**
     * 累计统计广告位已经上报产生的成本：上报个数*浮动当前上报成本
     *
     * @param slotId
     */
    @Override
    public void callBackCost(Long slotId, SlotBiddingConfigDto biddingConfig, String hu) {
        // 当日广告位上报后：产生的成本累计
        redisAtomicClient.incrBy(EngineRedisKeyFactory.K066.join(DateUtil.today(), slotId, hu), biddingConfig.getConvPrice(), 1, TimeUnit.DAYS);
    }

    @Override
    public Pair<Integer, String> callbackFanli(Order order, JSONObject param) {
        String orderId = order.getOrderId();
        Long slotId = order.getSlotId();

        try {
            if (StringUtils.isBlank(param.getString("click_id"))) {
                return Pair.of(ConvertUploadStatus.NOT_YET.getStatus(), "");
            }

            String resp = fanliService.behaviorUpload(orderId, slotId, param);
            log.info("返利接口上报, orderId={}, slotId={}, param={}, resp={}", orderId, slotId, param, resp);
            JSONObject result = JSON.parseObject(resp);
            int uploadStatus = ConvertUploadStatus.FAIL.getStatus();
            if (null != result && Objects.equals(result.getInteger("status"), 1)) {
                uploadStatus = ConvertUploadStatus.SUCCESS.getStatus();
            }
            return Pair.of(uploadStatus, resp);
        } catch (Exception e) {
            log.error("返利接口上报异常, orderId={}, slotId={}, param={}", orderId, slotId, param, e);
        }
        return Pair.of(ConvertUploadStatus.FAIL.getStatus(), "");
    }

    @Override
    public Pair<Integer, String> callbackJizhun(Order order, JSONObject param) {
        String orderId = order.getOrderId();
        Long slotId = order.getSlotId();

        try {
            if (StringUtils.isBlank(param.getString("jcid"))) {
                return Pair.of(ConvertUploadStatus.NOT_YET.getStatus(), "");
            }

            String resp = jizhunService.behaviorUpload(order.getOrderId(), param);
            log.info("极准接口上报, orderId={}, slotId={}, param={}, resp={}", order.getOrderId(), order.getSlotId(), param, resp);
            JSONObject result = JSON.parseObject(resp);
            int uploadStatus = ConvertUploadStatus.FAIL.getStatus();
            if (null != result && Objects.equals(result.getInteger("code"), 200)) {
                uploadStatus = ConvertUploadStatus.SUCCESS.getStatus();
            }
            return Pair.of(uploadStatus, resp);
        } catch (Exception e) {
            log.error("极准接口上报异常, orderId={}, slotId={}, param={}", orderId, slotId, param, e);
        }
        return Pair.of(ConvertUploadStatus.FAIL.getStatus(), "");
    }

    @Override
    public void callbackAndIncrData(Order order, Function<Pair<Order, JSONObject>, Pair<Integer, String>> callback) {
        Long slotId = order.getSlotId();
        String orderId = order.getOrderId();
        Date now = new Date();
        Date curDate = DateUtil.beginOfDay(now);

        GlobalThreadPool.executorService.submit(() -> {
            // 查询上报规则
            ConvertUploadRuleEntity rule = convertUploadRuleService.selectBySlotId(slotId);
            if (null == rule) {
                return;
            }
            // 获取广告位参数
            JSONObject param = getParameterFromCache(orderId);

            // 转化上报记录
            ConvertUploadRecordEntity record = new ConvertUploadRecordEntity();
            record.setCurDate(curDate);
            record.setSlotId(slotId);
            record.setOrderId(orderId);
            record.setAdvertId(order.getAdvertId());
            record.setConvertTime(now);
            record.setSlotParam(param.toString());
            record.setUploadStatus(ConvertUploadStatus.NOT_YET.getStatus());
            convertUploadRecordService.insert(record);

            // 用于判断上报的随机整数[0,100)
            int nonce = RandomUtil.randomInt(0, 100);
            // 根据规则判断是否进行上报和统计
            if (nonce >= rule.getRatio()) {
                return;
            }

            // 回调上报接口
            Integer uploadStatus = ConvertUploadStatus.NOT_YET.getStatus();
            String uploadResult = "";
            if (null != callback) {
                Pair<Integer, String> resp = callback.apply(Pair.of(order, param));
                if (null != resp) {
                    uploadStatus = resp.getKey();
                    uploadResult = resp.getValue();
                }
            }

            // 回调接口结果处理
            if (ConvertUploadStatus.isSuccess(uploadStatus)) {
                slotConvOpenDataService.incr(slotId, curDate);
            }
            ConvertUploadRecordEntity updateRecord = new ConvertUploadRecordEntity();
            updateRecord.setId(record.getId());
            updateRecord.setUploadStatus(uploadStatus);
            updateRecord.setUploadTime(new Date());
            updateRecord.setUploadResult(StrUtil.sub(uploadResult, 0, 255));
            convertUploadRecordService.updateById(updateRecord);
        });
    }
}
