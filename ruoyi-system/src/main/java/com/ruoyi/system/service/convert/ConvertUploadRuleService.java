package com.ruoyi.system.service.convert;

import com.ruoyi.system.entity.convert.ConvertUploadRuleEntity;
import com.ruoyi.system.req.manager.ConvertUploadRuleListReq;
import com.ruoyi.system.req.manager.ConvertUploadRuleReq;

import java.util.List;

/**
 * 媒体转化上报规则 Service
 *
 * <AUTHOR>
 * @date 2022-10-19 10:30:13
 */
public interface ConvertUploadRuleService {

    /**
     * 查询列表
     */
    List<ConvertUploadRuleEntity> selectList(ConvertUploadRuleListReq req);

    /**
     * 查询历史修改记录
     */
    List<ConvertUploadRuleEntity> selectHistory(Long slotId);

    /**
     * 根据广告位ID查询
     */
    ConvertUploadRuleEntity selectBySlotId(Long slotId);

    /**
     * 新增记录
     */
    int insert(ConvertUploadRuleEntity entity);

    /**
     * 更新规则
     */
    int update(ConvertUploadRuleReq req);
}
