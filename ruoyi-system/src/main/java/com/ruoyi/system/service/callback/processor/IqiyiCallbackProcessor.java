package com.ruoyi.system.service.callback.processor;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.IQIYI;

/**
 * 爱奇艺上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Slf4j
@Service
public class IqiyiCallbackProcessor implements CallbackProcessor, InitializingBean {

    /**
     * 爱奇艺上报延迟时间
     */
    private static final List<Integer> delayTimes = Arrays.asList(0, 5, 15, 30, 60, 180, 300, 600, 900);

    /**
     * 异步操作任务调度线程池
     */
    private final ScheduledExecutorService scheduledExecutor = SpringUtils.getBean("scheduledExecutorService");

    @Autowired
    private RedisCache redisCache;

    @Override
    public CallbackProcessorTypeEnum getType() {
        return IQIYI;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("impressId"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            String impressId = context.getParam().getSlotParam().getString("impressId");
            String imId = impressId.contains(",") ? impressId.substring(0, impressId.indexOf(",")) : impressId;

            String key = EngineRedisKeyFactory.K026.join(imId);
            redisCache.setCacheObject(key, "1", 1, TimeUnit.HOURS);

            delayTimes.forEach(delayTime -> {
                scheduledExecutor.schedule(() -> {
                    try {
                        if (!redisCache.hasKey(key)) {
                            return;
                        }

                        String resp = HttpUtil.get("http://tc.cupid.iqiyi.com/dsp_lpapi?event_type=200&impress_id=" + imId);
                        if (resp.contains("200") && resp.contains(imId)) {
                            redisCache.deleteObject(key);
                            log.info("{}接口上报, impress_id={}, resp={}", getType().getName(), imId, resp);
                        }
                    } catch (Exception e) {
                        log.error("{}接口上报异常, impress_id={}", imId, e);
                    }
                }, delayTime, TimeUnit.SECONDS);
            });
            return true;
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
