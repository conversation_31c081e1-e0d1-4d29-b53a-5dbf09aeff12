package com.ruoyi.system.service.callback.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.MGTV;

/**
 * 芒果TV上报处理器
 *
 * <AUTHOR>
 * @date 2023/11/1
 */
@Slf4j
@Service
public class MgtvCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final String CONV_URL_TEMPLATE = "http://py.da.mgtv.com/dsp/conv?did={}&idfa={}&mm={}&oaid=&os={}&convertid={}&type=1&event_type={}&conv_time={}";

    @Override
    public CallbackProcessorTypeEnum getType() {
        return MGTV;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("mgtvDid"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            JSONObject param = context.getParam().getSlotParam();
            String did = param.getString("mgtvDid");
            String convertId = param.getString("mgtvConvertid");
            String os = param.getString("mgtvOs");
            String mm = "00000000-0000-0000-0000-000000000000";
            String idfa = Objects.equals(os, "1") ? mm : "";
            // event_type:1.下载,2.安装,3.激活,4.注册,5.付费,6.到达落地页,7.按钮点击,8.跳转到页面
            int eventType = 5;

            String url = StrUtil.format(CONV_URL_TEMPLATE, did, idfa, mm, os, convertId, eventType, System.currentTimeMillis());
            String resp = HttpUtil.createGet(url).execute().body();
            log.info("{}接口上报, url={}, resp={}", getType().getName(), url, resp);
            return true;
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
