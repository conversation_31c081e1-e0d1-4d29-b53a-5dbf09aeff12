package com.ruoyi.system.service.oa.permission.impl;

import com.ruoyi.system.entity.oa.permission.StaffPermissionEntity;
import com.ruoyi.system.mapper.oa.permission.StaffPermissionMapper;
import com.ruoyi.system.service.oa.permission.StaffPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 用户权限关联表 Service
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:19
 */
@Service
public class StaffPermissionServiceImpl implements StaffPermissionService {

    @Autowired
    private StaffPermissionMapper staffPermissionMapper;

    @Override
    public Boolean save(StaffPermissionEntity entity) {
        if (null == entity) {
            return false;
        }
        return null == entity.getId() ? insert(entity) : updateById(entity);
    }

    @Override
    public Boolean insert(StaffPermissionEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return staffPermissionMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return staffPermissionMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(StaffPermissionEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return staffPermissionMapper.updateById(entity) > 0;
    }

    @Override
    public StaffPermissionEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return staffPermissionMapper.selectById(id);
    }

    @Override
    public StaffPermissionEntity selectByStaffIdAndSystemId(Long staffId, Long systemId) {
        if (null == staffId || null == systemId) {
            return null;
        }
        return staffPermissionMapper.selectByStaffIdAndSystemId(staffId, systemId);
    }
}
