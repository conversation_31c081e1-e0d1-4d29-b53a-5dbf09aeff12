package com.ruoyi.system.service.oa.post.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.PostStaffNumBo;
import com.ruoyi.system.entity.oa.post.PostEntity;
import com.ruoyi.system.mapper.oa.post.PostMapper;
import com.ruoyi.system.service.oa.post.PostService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * oa职位表 Service
 *
 * <AUTHOR>
 * @date 2021-10-9 14:20:23
 */
@Service
public class PostServiceImpl implements PostService {

    @Autowired
    private PostMapper postMapper;

    @Override
    public PostEntity selectById(Long id){
        if(Objects.isNull(id)){
            return null;
        }
        return postMapper.selectById(id);
    }

    @Override
    public Map<Long, String> selectPostNameMap(List<Long> postIds) {
        if(CollectionUtils.isEmpty(postIds)){
            return Collections.emptyMap();
        }
        List<PostEntity> oaPostEntities = postMapper.selectByIds(postIds, 1L);
        return oaPostEntities.stream().collect(Collectors.toMap(PostEntity::getId, PostEntity::getPostKey));
    }

    @Override
    public List<PostEntity> selectListByDepartmentIds(List<Long> departmentIds) {
        if(CollectionUtils.isEmpty(departmentIds)){
            return Collections.emptyList();
        }
        return postMapper.selectListByDepartmentIds(departmentIds);
    }

    @Override
    public List<PostEntity> selectAllPost() {
        //限定诺禾公司，线上测试环境公司id统一
        return postMapper.selectAllPost(1L);
    }

    @Override
    public List<PostEntity> selectPostListByIds(List<Long> postIds) {
        if(CollectionUtils.isEmpty(postIds)){
            return Collections.emptyList();
        }
        return postMapper.selectByIds(postIds, 1L);
    }


    @Override
    public List<PostEntity> selectByPostName(String postName) {
        if(StringUtils.isBlank(postName)){
            return Collections.emptyList();
        }
        return postMapper.selectByPostName(postName);
    }
}
