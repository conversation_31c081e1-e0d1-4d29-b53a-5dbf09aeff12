package com.ruoyi.system.service.convert.impl;

import com.ruoyi.system.bo.publisher.ConvertUploadSummaryDataBo;
import com.ruoyi.system.req.manager.ConvertUploadRecordListReq;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.convert.ConvertUploadRecordService;
import com.ruoyi.system.entity.convert.ConvertUploadRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.convert.ConvertUploadRecordMapper;

/**
 * 媒体转化上报记录 Service
 *
 * <AUTHOR>
 * @date 2022-10-19 10:30:00
 */
@Service
public class ConvertUploadRecordServiceImpl implements ConvertUploadRecordService {

    @Autowired
    private ConvertUploadRecordMapper convertUploadRecordMapper;

    @Override
    public List<ConvertUploadRecordEntity> selectList(ConvertUploadRecordListReq req) {
        return convertUploadRecordMapper.selectList(req);
    }

    @Override
    public List<ConvertUploadSummaryDataBo> selectSummaryData(ConvertUploadRecordListReq req) {
        return convertUploadRecordMapper.selectSummaryData(req);
    }

    @Override
    public int insert(ConvertUploadRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return convertUploadRecordMapper.insert(entity);
    }

    @Override
    public Boolean updateById(ConvertUploadRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return convertUploadRecordMapper.updateById(entity) > 0;
    }

    @Override
    public ConvertUploadRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return convertUploadRecordMapper.selectById(id);
    }
}
