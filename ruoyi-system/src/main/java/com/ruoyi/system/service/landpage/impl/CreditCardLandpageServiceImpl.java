package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.InnerLogUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.bo.thirdparty.IdCardAuditBo;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.CreditCardLandpageFormRecordEntity;
import com.ruoyi.system.message.rocketmq.producer.LogMqProducer;
import com.ruoyi.system.req.engine.CreditCardLandpageFormReq;
import com.ruoyi.system.service.common.IdCardService;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.service.landpage.CreditCardLandpageFormRecordService;
import com.ruoyi.system.service.landpage.CreditCardLandpageService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.service.thirdparty.IdCardAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.InnerLogType.LANDPAGE_CLICK;

/**
 * 信用卡落地页服务实现
 *
 * <AUTHOR>
 * @date 2023/06/02
 */
@Slf4j
@Service
public class CreditCardLandpageServiceImpl implements CreditCardLandpageService {

    @Autowired
    private OrderService orderService;

    @Autowired
    private IdCardService idCardService;

    @Autowired
    private DataStatService dataStatService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private LogMqProducer logMqProducer;

    @Autowired
    private IdCardAuditService idCardAuditService;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private CreditCardLandpageFormRecordService creditCardLandpageFormRecordService;

    @Override
    public void formSubmit(CreditCardLandpageFormReq req) {
        // 查询订单
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            throw new CustomException("链接已失效");
        }
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);

        // 查询身份证实名认证缓存
        IdCardAuditBo auditResult = idCardAuditService.idCardAuditCache(req.getName(), req.getIdCard());
        if (IdCardAuditBo.isNotMatch(auditResult)) {
            throw new CustomException("身份证号与姓名不匹配，请检查");
        }
        final String idCardMd5 = Md5Utils.hash(req.getIdCard());

        // 身份证实名校验
        if (null == auditResult) {
            // 防刷限制
            String today = DateUtil.today();
            Long idCardCount = redisAtomicClient.incrBy(EngineRedisKeyFactory.K068.join(today, idCardMd5), 1, 1, TimeUnit.DAYS);
            Long phoneCount = redisAtomicClient.incrBy(EngineRedisKeyFactory.K069.join(today, req.getPhone()), 1, 1, TimeUnit.DAYS);
            if (idCardCount > 5L || phoneCount > 5L) {
                log.info("信用卡落地页表单提交失败, 防刷限制, req={}", JSON.toJSONString(req));
                return;
            }
            auditResult = idCardAuditService.idCardAudit(req.getName(), req.getIdCard());
        }
        if (!IdCardAuditBo.isMatch(auditResult)) {
            throw new CustomException("身份证号与姓名不匹配，请检查");
        }

        // 一分钟内重复提交过滤
        if (isDuplicateForm(req)) {
            throw new CustomException("重复提交");
        }

        // 实名认证接口:1.聚合,2.毫秒科技
        final int auditApiType = auditResult.getApiType();

        // 添加转化记录
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        String referer = ServletUtils.getRequest().getHeader("referer");

        // 打印inner日志
        JSONObject logJson = new JSONObject();
        logJson.put("activityId", order.getActivityId());
        logJson.put("slotId", order.getSlotId());
        logJson.put("appId", order.getAppId());
        logJson.put("consumerId", order.getConsumerId());
        logJson.put("orientId", order.getOrientId());
        logJson.put("advertId", order.getAdvertId());
        logJson.put("materialId", order.getMaterialId());
        if (null != adSnapshot) {
            logJson.put("chargeType", adSnapshot.getChargeType());
            logJson.put("landpageUrl", adSnapshot.getLandpageUrl());
            logJson.put("originLandpageUrl", adSnapshot.getOriginLandpageUrl());
            logJson.put("srid", adSnapshot.getSrid());
            logJson.put("pluginId", adSnapshot.getPluginId());
        }
        logJson.put("ip", IpUtils.getIpAddr(ServletUtils.getRequest()));
        logJson.put("userAgent", ServletUtils.getRequest().getHeader("User-Agent"));
        logJson.put("referer", referer);
        logJson.put("orderId", order.getOrderId());
        logJson.put("idCardMd5", idCardMd5);
        logJson.put("phone", req.getPhone());
        logJson.put("name", req.getName());
        logJson.put("address", req.getAddress());
        logJson.put("idCard", idCardService.encrypt(req.getIdCard()));
        InnerLogUtils.log(LANDPAGE_CLICK, logJson);
        logMqProducer.sendMsg(LANDPAGE_CLICK, logJson);

        // 落地页数据
        dataStatService.handleAsync(LANDPAGE_CLICK, logJson);

        // 落地页转化记录
        CreditCardLandpageFormRecordEntity record = new CreditCardLandpageFormRecordEntity();
        record.setOrderId(order.getOrderId());
        record.setAppId(order.getAppId());
        record.setSlotId(order.getSlotId());
        record.setAdvertiserId(advertCacheService.queryAdvertiserId(order.getAdvertId()));
        record.setAdvertId(order.getAdvertId());
        record.setConsumerId(order.getConsumerId());
        record.setIdCard(idCardService.encrypt(req.getIdCard()));
        record.setLandpageUrl(StrUtil.sub(Optional.ofNullable(adSnapshot).map(AdSnapshot::getLandpageUrl).orElse(""), 0, 255));
        record.setReferer(StrUtil.sub(referer, 0, 255));
        record.setName(req.getName());
        record.setPhone(req.getPhone());
        record.setAddress(req.getAddress());
        record.setIdCardMd5(idCardMd5);
        record.setBirth(IdcardUtil.getBirthDate(req.getIdCard()));
        record.setCreditLimit(req.getCreditLimit());
        record.setAuditApiType(auditApiType);
        record.setIp(StrUtil.sub(ip, 0, 32));
        creditCardLandpageFormRecordService.insert(record);
    }

    /**
     * 是否重复表单
     *
     * @param req 请求参数
     * @return true.重复表单,false.非重复表单
     */
    private boolean isDuplicateForm(CreditCardLandpageFormReq req) {
        try {
            String key = EngineRedisKeyFactory.K070.join(SecureUtil.md5(req.getOrderId() + req.getName() + req.getIdCard() + req.getPhone() + req.getCreditLimit() + req.getAddress()));
            return null == redisAtomicClient.getLock(key, 60);
        } catch (Exception e) {
            log.error("CreditCardLandpageServiceImpl.isDuplicateForm error, req={}", JSON.toJSONString(req), e);
        }
        return false;
    }
}
