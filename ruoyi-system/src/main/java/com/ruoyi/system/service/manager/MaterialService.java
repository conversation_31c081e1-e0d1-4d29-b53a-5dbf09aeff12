package com.ruoyi.system.service.manager;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.entity.advert.Material;
import com.ruoyi.system.req.advert.MaterialReq;
import com.ruoyi.system.vo.advert.MaterialVO;

/**
 * 广告素材Service接口
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
public interface MaterialService {

    /**
     * 查询广告素材
     *
     * @param id 广告素材ID
     * @return 广告素材
     */
    Material selectMaterialById(Long id);

    /**
     * 查询广告素材列表
     *
     * @param material 广告素材
     * @return 广告素材集合
     */
    List<Material> selectMaterialList(Material material);

    /**
     * 查询广告对应的可投素材列表
     *
     * @param advertIds 广告ID列表
     * @return 广告ID-素材列表映射
     */
    Map<Long, List<MaterialVO>> selectValidByAdvertIds(List<Long> advertIds);

    /**
     * 新增广告素材
     *
     * @param req 广告素材
     * @return 结果
     */
    int insertMaterial(MaterialReq req);

    /**
     * 修改广告素材
     *
     * @param req 广告素材
     * @return 结果
     */
    int updateMaterial(MaterialReq req);

    /**
     * 修改广告素材状态
     *
     * @param req 广告素材
     * @return 结果
     */
    int updateMaterialStatus(MaterialReq req);

    /**
     * 修改广告素材权重
     *
     * @param req 广告素材
     * @return 结果
     */
    int updateMaterialWeight(MaterialReq req);

    /**
     * 更新创新弹层的素材图
     *
     * @param layerId 弹层ID
     * @param materialImg 素材图链接
     * @return 结果
     */
    int updateMaterialByLayer(Long layerId, String materialImg);

    /**
     * 获取已使用的创新弹层ID
     *
     * @param layerIds 弹层ID列表
     * @return 已使用的创新弹层ID列表
     */
    List<Long> selectUsedLayerId(List<Long> layerIds);
}
