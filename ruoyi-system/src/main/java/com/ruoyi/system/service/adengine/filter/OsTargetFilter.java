package com.ruoyi.system.service.adengine.filter;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.common.enums.advert.OsTargetType;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.service.adengine.AdvertFilter;
import org.springframework.stereotype.Component;

/**
 * 系统定向过滤
 */
@Component
public class OsTargetFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();

        // 不限系统类型
        if (null == ad.getOsTarget() || 0 == ad.getOsTarget()) {
            return true;
        }
        boolean isIOS = StrUtil.containsAnyIgnoreCase(context.getUserAgent(), "iPhone", "iPod", "iPad");
        boolean isAndroid = StrUtil.containsAnyIgnoreCase(context.getUserAgent(), "Android");
        // iOS系统判断
        return (isIOS && OsTargetType.contains(ad.getOsTarget(), OsTargetType.IOS.getType()))
                // Android系统判断
                || (isAndroid && OsTargetType.contains(ad.getOsTarget(), OsTargetType.ANDROID.getType()))
                // 其他系统判断
                || (!isIOS && !isAndroid && OsTargetType.contains(ad.getOsTarget(), OsTargetType.OTHER.getType()));
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.OS_TARGET;
    }
}
