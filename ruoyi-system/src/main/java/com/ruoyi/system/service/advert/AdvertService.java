package com.ruoyi.system.service.advert;

import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.req.advert.AdvertCreateReq;
import com.ruoyi.system.vo.advert.AdvertProportionVO;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * 广告Service接口
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
public interface AdvertService {

    /**
     * 查询广告
     *
     * @param id 广告ID
     * @return 广告
     */
    Advert selectAdvertById(Long id);

    /**
     * 查询广告名称
     *
     * @param id 广告ID
     * @return 广告名称
     */
    String selectAdvertNameById(Long id);

    /**
     * 查询广告的指定属性
     *
     * @param id 广告ID
     * @return 广告的指定属性
     */
    <R> R selectById(Long id, Function<Advert, R> mapper);

    /**
     * 查询广告的指定属性列表
     *
     * @param ids 广告ID列表
     * @return 广告的指定属性列表
     */
    <R> List<R> selectByIds(List<Long> ids, Function<Advert, R> mapper);

    /**
     * 查询广告列表
     *
     * @param advert 广告
     * @return 广告集合
     */
    List<Advert> selectAdvertList(Advert advert);

    /**
     * 获取广告/配置使用的域名
     *
     * @param domains 域名列表
     * @return 域名-广告ID集合映射
     */
    Map<String, Set<Long>> getDomainAdvertMapS(List<String> domains);

    /**
     * 查询广告ID列表
     *
     * @param param 广告查询条件
     * @return 广告ID列表
     */
    List<Long> selectAdvertIds(Advert param);

    /**
     * 查询广告ID列表
     * 注:包含已失效广告
     *
     * @param searchValue 广告ID/名称搜索值
     * @return 广告ID列表
     */
    List<Long> selectAdvertIdsBySearchValue(String searchValue);

    /**
     * 根据广告主ID查询广告ID列表
     *
     * @param advertiserId 广告主ID
     * @return 广告ID列表
     */
    List<Long> selectAdvertIdsByAdvertiserId(Long advertiserId);

    /**
     * 根据广告主ID查询广告ID列表
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告ID列表
     */
    List<Long> selectAdvertIdsByAdvertiserIds(List<Long> advertiserIds);

    /**
     * 根据广告主ID查询广告ID映射
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告ID-广告主ID映射列表
     */
    Map<Long, Long> selectAdvertIdMapByAdvertiserIds(List<Long> advertiserIds);

    /**
     * 根据广告ID查询广告主ID映射
     *
     * @param advertIds 广告ID列表
     * @return 广告ID-广告主ID映射列表
     */
    Map<Long, Long> selectAdvertiserIdMapByAdvertIds(List<Long> advertIds);

    /**
     * 查询可投放的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectValidAdvertIds();

    /**
     * 批量查询广告
     *
     * @param ids 广告ID列表
     * @return 广告Id-广告信息映射
     */
    Map<Long, Advert> selectAdvertMapByIds(List<Long> ids);

    /**
     * 批量查询广告名称
     *
     * @param advertIds 广告ID列表
     * @return 广告Id-广告名称映射
     */
    Map<Long, String> selectAdvertNameMap(List<Long> advertIds);

    /**
     * 新增广告
     *
     * @param req 广告
     * @return 广告ID
     */
    Long insertAdvert(AdvertCreateReq req);

    /**
     * 更新落地页链接
     *
     * @param id 广告id
     * @param landpageUrl 落地页链接
     * @return 影响行数
     */
    int updateLandPageUrl(Long id, String landpageUrl);

    /**
     * 修改广告
     *
     * @param advert 广告
     * @return 结果
     */
    int updateAdvert(Advert advert);

    /**
     * 修改广告状态
     *
     * @param advert 广告
     * @return 结果
     */
    int updateAdvertStatus(Advert advert);

    /**
     * 根据广告ID列表批量更新广告开关
     *
     * @param ids 广告ID列表
     * @param servingSwitch 开关状态
     * @return 更新结果
     */
    int updateSwitchByIds(List<Long> ids, Integer servingSwitch);

    /**
     * 修改广告状态
     *
     * @param advertId 广告ID
     * @param advertStatus 广告状态
     * @return 结果
     */
    int updateAdvertStatus(Long advertId, Integer advertStatus);

    /**
     * 重置预算不足的广告的状态
     *
     * @param advertIds 广告ID列表
     */
    void resetAdvertStatus(List<Long> advertIds);

    /**
     * 根据id复制广告
     *
     * @param id 广告id
     * @param advertiserId 广告主id
     * @return 新的广告ID
     */
    Long copyAdvert(Long id, Long advertiserId);

    /**
     * 查询广告落地页
     *
     * @param advertId 广告ID
     * @return 落地页
     */
    String selectLandpageUrlByAdvertId(Long advertId);

    /**
     * 标记广告无效
     *
     * @param advertId 广告ID
     * @return 结果
     */
    int invalidateAdvert(Long advertId);

    /**
     * 禁用广告主下的所有广告
     *
     * @param advertiserId 广告主ID
     * @return 影响行数
     */
    int disableAdvertByAdvertiserId(Long advertiserId);

    /**
     * 恢复广告主下的所有广告
     *
     * @param advertiserId 广告主ID
     * @return 影响行数
     */
    int enableAdvertByAdvertiserId(Long advertiserId);

    /**
     * 发送消息检查预算
     *
     * @param advertId 广告ID
     */
    void sendCheckBudgetMsg(Long advertId);

    /**
     * 发送消息检查预算(异步)
     *
     * @param advertiserId 广告主ID
     */
    void sendCheckBudgetMsgByAdvertiserId(Long advertiserId);

    /**
     * 发送消息刷新缓存(异步)
     *
     * @param advertiserId 广告主ID
     */
    void sendRefreshCacheMsgByAdvertiserId(Long advertiserId);

    /**
     * 查询广告主下特定开关状态的广告ID列表
     *
     * @param advertiserId 广告主ID
     * @param status 开关状态
     * @return 广告ID列表
     */
    List<Long> selectAllOpenOrCloseAdvertNameByAdvertiserId(Long advertiserId,Integer status);

    /**
     * 查询广告主下特定开关状态的广告名称列表
     *
     * @param advertiserId 广告主ID
     * @param status 开关状态
     * @return 广告名称列表
     */
    List<String> selectAllOpenAdvertNameByAdvertiserId(Long advertiserId,Integer status);

    /**
     * 获取直投广告在广告位上的排序和投放占比
     *
     * @param slotId 广告位ID
     * @param orientIds 配置ID列表
     * @return 排序后的直投广告占比列表
     */
    List<AdvertProportionVO> getDirectAdvertOrder(Long slotId, List<Long> orientIds);
}
