package com.ruoyi.system.service.sms;

import com.ruoyi.system.entity.sms.LiuziSmsSendRecordCountBO;
import com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity;

import java.util.List;
import java.util.Map;

/**
 * 留资短信发送记录表 Service
 *
 * <AUTHOR>
 * @date 2022-12-5 10:09:17
 */
public interface LiuziSmsSendRecordService {
    /**
     * 新增记录
     */
    Boolean insert(LiuziSmsSendRecordEntity entity);

    /**
     * 批量新增
     * @param entities
     * @return
     */
    Boolean batchInsert(List<LiuziSmsSendRecordEntity> entities);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(LiuziSmsSendRecordEntity entity);

    /**
     * 根据id获取
     */
    LiuziSmsSendRecordEntity selectById(Long id);

    /**
     * 根据留资记录id查询
     *
     * @param liuziRecordId
     * @return
     */
    List<LiuziSmsSendRecordEntity> selectListByLiuziRecordId(Long liuziRecordId);

    /**
     * 根据留资记录id列表查询短信条数
     *
     * @param liuziRecordIds
     * @return
     */
    List<LiuziSmsSendRecordCountBO> countByLiuziRecordIds(List<Long> liuziRecordIds);

    /**
     * 根据id更新状态
     * @param id
     * @param status
     * @return
     */
    Boolean updateSmsStatusById(Long id,Integer status);

    /**
     * 根据渠道和消息id更新短信状态
     * @param type 渠道
     * @see com.ruoyi.common.enums.SmsChannelEnum
     * @param msgId 消息id
     * @param status 状态
     * @return
     */
    Boolean updateSmsStatusByMsgIdAndType(Integer type,String msgId,Integer status);

    /**
     * 根据渠道和消息id查询短信发送记录
     * @param type
     * @param msgId
     * @return
     */
    LiuziSmsSendRecordEntity selectByTypeAndMsgId(Integer type,String msgId);

    /**
     * 根据留资记录id列表查询短信发送记录
     *
     * @param liuziRecordIds 留资记录ID列表
     * @return 留资记录ID,短信发送记录映射
     */
    Map<Long, List<LiuziSmsSendRecordEntity>> selectMapByLiuziRecordIds(List<Long> liuziRecordIds);
}
