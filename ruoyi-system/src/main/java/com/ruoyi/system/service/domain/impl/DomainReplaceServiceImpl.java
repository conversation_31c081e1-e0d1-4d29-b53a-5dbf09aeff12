package com.ruoyi.system.service.domain.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.domain.DomainReplaceService;
import com.ruoyi.system.service.engine.cache.DomainCacheService;
import com.ruoyi.system.util.LandpageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Set;

import static com.ruoyi.common.enums.domain.DomainType.ACTIVITY_DOMAIN;
import static com.ruoyi.common.enums.domain.DomainType.LANDPAGE_DOMAIN;

/**
 * 域名替换服务实现
 *
 * <AUTHOR>
 * @date 2021/9/16
 */
@Service
public class DomainReplaceServiceImpl implements DomainReplaceService {

    @Autowired
    private DomainCacheService domainCacheService;

    @Autowired
    private WhitelistService whitelistService;

    @Override
    public String doReplaceDomain(String originUrl, String targetDomain) {
        if (StringUtils.isBlank(originUrl) || StringUtils.isBlank(targetDomain)) {
            return originUrl;
        }

        String tempUrl;
        StringBuilder sb = new StringBuilder();
        if (originUrl.contains("http://")) {
            // 截取 http:// 之后的地址
            tempUrl = originUrl.substring(7);
            sb.append("http://");
        } else if (originUrl.contains("https://")) {
            // 截取 https:// 之后的地址
            tempUrl = originUrl.substring(8);
            sb.append("https://");
        } else {
            tempUrl = originUrl;
            sb.append("http://");
        }

        // 第一个 "/" 的下标地址
        int index = tempUrl.indexOf("/");
        sb.append(targetDomain).append(tempUrl.substring(index));
        return sb.toString();
    }

    @Override
    public String doReplaceLandpageDomain(String landpageUrl, JSONObject domainConfig, String userAgent) {
        if (!LandpageUtil.hasLpk(landpageUrl) || null == domainConfig || LandpageUtil.isQuickApp(landpageUrl)
                || StrUtil.containsAnyIgnoreCase(landpageUrl, "ifr.html", "weixin://", "ifr://", "api://")) {
            return landpageUrl;
        }
        boolean isAutoReplace = SwitchStatusEnum.isSwitchOn(domainConfig.getInteger("autoReplace"));
        String landpageDomain = domainConfig.getString(LANDPAGE_DOMAIN.getKey());
        if (isAutoReplace) {
            String originDomain = UrlUtils.extractDomain(landpageUrl);
            // 白名单的域名不替换
            if (whitelistService.contains(WhitelistType.NOT_REPLACE_DOMAIN, originDomain)) {
                return landpageUrl;
            }

            boolean isAlipay = StrUtil.containsAnyIgnoreCase(userAgent, "AliApp", "Alipay");
            if (isAlipay) {
                Set<String> domainPool = domainCacheService.selectAlipayValidDomainListCache(LANDPAGE_DOMAIN.getType());
                if (CollectionUtils.isNotEmpty(domainPool)) {
                    if (StringUtils.isNotBlank(landpageDomain)) {
                        if (!domainPool.contains(landpageDomain)) {
                            if (domainPool.contains(originDomain)) {
                                return landpageUrl;
                            }
                            landpageDomain = domainPool.iterator().next();
                        }
                    } else {
                        if (!domainPool.contains(originDomain)) {
                            landpageDomain = domainPool.iterator().next();
                        }
                    }
                }
            } else {
                boolean isWechat = StrUtil.containsAnyIgnoreCase(userAgent, "MicroMessenger");
                if (isWechat) {
                    Set<String> domainPool = domainCacheService.selectWechatValidDomainListCache(LANDPAGE_DOMAIN.getType());
                    if (CollectionUtils.isNotEmpty(domainPool)) {
                        if (StringUtils.isNotBlank(landpageDomain)) {
                            if (!domainPool.contains(landpageDomain)) {
                                if (domainPool.contains(originDomain)) {
                                    return landpageUrl;
                                }
                                landpageDomain = domainPool.iterator().next();
                            }
                        } else {
                            if (!domainPool.contains(originDomain)) {
                                landpageDomain = domainPool.iterator().next();
                            }
                        }
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(landpageDomain)) {
            landpageUrl = doReplaceDomain(landpageUrl, landpageDomain);
        }
        return landpageUrl;
    }

    @Override
    public String doReplaceActivityDomain(HttpServletRequest request, String activityUrl, JSONObject domainConfig) {
        if (null == domainConfig || StrUtil.containsAnyIgnoreCase(activityUrl, "ifr.html")) {
            return activityUrl;
        }
        boolean isAutoReplace = SwitchStatusEnum.isSwitchOn(domainConfig.getInteger("autoReplace"));
        String activityDomain = domainConfig.getString(ACTIVITY_DOMAIN.getKey());
        if (isAutoReplace) {
            boolean isAlipay = StrUtil.containsAnyIgnoreCase(request.getHeader("User-Agent"), "AliApp", "Alipay");
            if (isAlipay) {
                Set<String> domainPool = domainCacheService.selectAlipayValidDomainListCache(ACTIVITY_DOMAIN.getType());
                if (CollectionUtils.isNotEmpty(domainPool)) {
                    String originDomain = UrlUtils.extractDomain(activityUrl);
                    if (StringUtils.isNotBlank(activityDomain)) {
                        if (!domainPool.contains(activityDomain)) {
                            if (domainPool.contains(originDomain)) {
                                return activityUrl;
                            }
                            activityDomain = domainPool.iterator().next();
                        }
                    } else {
                        if (!domainPool.contains(originDomain)) {
                            activityDomain = domainPool.iterator().next();
                        }
                    }
                }
            } else {
                boolean isWechat = StrUtil.containsAnyIgnoreCase(request.getHeader("User-Agent"), "MicroMessenger");
                if (isWechat) {
                    Set<String> domainPool = domainCacheService.selectWechatValidDomainListCache(ACTIVITY_DOMAIN.getType());
                    if (CollectionUtils.isNotEmpty(domainPool)) {
                        String originDomain = UrlUtils.extractDomain(activityUrl);
                        if (StringUtils.isNotBlank(activityDomain)) {
                            if (!domainPool.contains(activityDomain)) {
                                if (domainPool.contains(originDomain)) {
                                    return activityUrl;
                                }
                                activityDomain = domainPool.iterator().next();
                            }
                        } else {
                            if (!domainPool.contains(originDomain)) {
                                activityDomain = domainPool.iterator().next();
                            }
                        }
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(activityDomain)) {
            activityUrl = doReplaceDomain(activityUrl, activityDomain);
        }
        return activityUrl;
    }
}
