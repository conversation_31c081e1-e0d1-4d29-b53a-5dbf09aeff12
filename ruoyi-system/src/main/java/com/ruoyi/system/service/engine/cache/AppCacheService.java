package com.ruoyi.system.service.engine.cache;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.AppCacheDto;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.tagmanager.TagManagerEntity;
import com.ruoyi.system.mapper.manager.AppMapper;
import com.ruoyi.system.service.app.AppTagRelationService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 媒体缓存服务
 *
 * <AUTHOR>
 * @date 2021/8/19
 */
@Slf4j
@Service
public class AppCacheService {

    @Autowired
    private AppMapper appMapper;

    @Autowired
    private AppTagRelationService appTagRelationService;

    @Autowired
    private TagManagerService tagManagerService;

    /**
     * 媒体缓存
     */
    private final LoadingCache<String, Optional<AppCacheDto>> APP_KEY_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(200)
            .build(new CacheLoader<String, Optional<AppCacheDto>>() {
                @Override
                public Optional<AppCacheDto> load(String appKey) {
                    App app = appMapper.selectAppByAppKey(appKey);
                    if (null == app) {
                        return Optional.empty();
                    }
                    return Optional.of(convertTo(app));
                }

                @Override
                public ListenableFuture<Optional<AppCacheDto>> reload(String appKey, Optional<AppCacheDto> oldValue) {
                    ListenableFutureTask<Optional<AppCacheDto>> task = ListenableFutureTask.create(() -> load(appKey));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 媒体标签缓存
     */
    private final LoadingCache<Long, Set<String>> APP_TAG_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(10, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Long, Set<String>>() {
                @Override
                public Set<String> load(Long appId) {
                    List<Long> tagIds = appTagRelationService.selectTagIdsByAppId(appId);
                    if (CollectionUtils.isEmpty(tagIds)) {
                        return Collections.emptySet();
                    }
                    List<TagManagerEntity> tags = tagManagerService.selectByIds(tagIds);
                    return tags.stream().map(TagManagerEntity::getTagName).collect(Collectors.toSet());
                }

                @Override
                public ListenableFuture<Set<String>> reload(Long appId, Set<String> oldValue) {
                    ListenableFutureTask<Set<String>> task = ListenableFutureTask.create(() -> load(appId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 查询媒体缓存
     *
     * @param appKey 应用标识
     * @return 媒体缓存
     */
    public AppCacheDto getAppCache(String appKey) {
        if (StringUtils.isBlank(appKey) || appKey.length() > 64 || !StringUtils.isAlphanumeric(appKey)) {
            return null;
        }

        try {
            Optional<AppCacheDto> appOpt = APP_KEY_CACHE.get(appKey);
            return appOpt.orElse(null);
        } catch (ExecutionException e) {
            log.error("查询媒体缓存异常, appKey={}", appKey, e);
        }
        return null;
    }

    /**
     * 查询媒体是否存在指定标签
     *
     * @param appId 媒体ID
     * @return true.存在,false.不存在
     */
    public boolean isTagExistByAppId(Long appId, String tagName) {
        if (null != appId && StringUtils.isNotBlank(tagName)) {
            try {
                return APP_TAG_CACHE.get(appId).contains(tagName);
            } catch (ExecutionException e) {
                log.error("查询媒体标签缓存异常, appId={}", appId, e);
            }
        }
        return false;
    }

    /**
     * 查询媒体标签集合
     *
     * @param appId 媒体ID
     * @return 标签集合
     */
    public Set<String> getAppTagSetByAppId(Long appId) {
        if (null != appId) {
            try {
                return APP_TAG_CACHE.get(appId);
            } catch (ExecutionException e) {
                log.error("查询媒体标签集合缓存异常, appId={}", appId, e);
            }
        }
        return Collections.emptySet();
    }

    /**
     * convert App to AppVO
     */
    private AppCacheDto convertTo(App app) {
        return BeanUtil.copyProperties(app, AppCacheDto.class);
    }
}
