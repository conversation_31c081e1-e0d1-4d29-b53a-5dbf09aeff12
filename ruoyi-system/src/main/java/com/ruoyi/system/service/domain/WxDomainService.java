package com.ruoyi.system.service.domain;

import java.util.function.Consumer;

/**
 * 微信域名接口
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
public interface WxDomainService {

    /**
     * 加入待检测队列(低优先级)
     *
     * @param url 待检测链接
     */
    void addCheckQueue(String url);

    /**
     * 异步检查链接是否被微信拦截并提醒(高优先级)
     *
     * @param url      链接
     * @param notify   通知
     * @param callback 回调
     */
    void checkWxBlockAsync(String url, boolean notify, Consumer<Boolean> callback);

    /**
     * 异步将链接加入到落地页巡查
     *
     * @param advertId  广告ID
     * @param url       链接
     * @param userAgent userAgent
     */
    void addUrlToPatrolAsync(String advertId, String url, String userAgent);

    /**
     * 根据域名查询关text信息
     *
     * @param text
     * @param urlDomain
     * @return
     */
    String getStringFromDB(String text, String urlDomain);
}
