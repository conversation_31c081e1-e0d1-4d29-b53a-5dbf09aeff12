package com.ruoyi.system.service.manager.impl;

import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SearchConditionService;
import com.ruoyi.system.service.manager.SlotService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.ruoyi.common.constant.BizConstants.INVALID_ID_LIST;
import static com.ruoyi.common.enums.account.DataPermissionType.hasFullPermission;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * 查询条件过滤实现
 *
 * <AUTHOR>
 * @date 2023-09-20
 */
@Service
public class SearchConditionServiceImpl implements SearchConditionService {

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private AccountRelationService accountRelationService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Override
    public List<Long> filterAdvertIdsByDatePermission(final List<Long> originAdvertIds) {
        DataPermissionBo permission = dataPermissionManager.selectAdvert();
        if (hasFullPermission(permission.getType())) {
            return originAdvertIds;
        }
        if (!hasPartialPermission(permission.getType())) {
            return INVALID_ID_LIST;
        }
        List<Long> advertIds = mergeParamIds(originAdvertIds, permission.getValues());
        return handleList(advertIds);
    }

    @Override
    public List<Long> filterAdvertIdsByManagerIds(final List<Long> originAdvertIds, final List<Long> managerIds) {
        if (CollectionUtils.isEmpty(managerIds)) {
            return originAdvertIds;
        }
        List<Long> advertiserIds = accountRelationService.selectBySrcAccountIds(managerIds);
        List<Long> advertIds = mergeParamIds(originAdvertIds, advertService.selectAdvertIdsByAdvertiserIds(advertiserIds));
        return handleList(advertIds);
    }

    @Override
    public List<Long> filterAdvertIdsByAdvertSearch(final List<Long> originAdvertIds, String advertSearch) {
        if (StringUtils.isBlank(advertSearch)) {
            return originAdvertIds;
        }
        List<Long> advertIds = mergeParamIds(originAdvertIds, advertService.selectAdvertIdsBySearchValue(advertSearch));
        return handleList(advertIds);
    }

    @Override
    public List<Long> filterAdvertIdsByAdvertiserOrAgent(final List<Long> originAdvertIds, final List<Long> advertiserIds, final List<Long> agentIds) {
        if (CollectionUtils.isEmpty(advertiserIds) && CollectionUtils.isEmpty(agentIds)) {
            return originAdvertIds;
        }
        List<Long> tmpAdvertiserIds = advertiserIds;
        if (CollectionUtils.isNotEmpty(agentIds)) {
            tmpAdvertiserIds = mergeParamIds(tmpAdvertiserIds, agentService.selectAdvertiserIdsByAgentIds(agentIds));
            if (CollectionUtils.isEmpty(tmpAdvertiserIds)) {
                return INVALID_ID_LIST;
            }
        }
        List<Long> advertIds = mergeParamIds(originAdvertIds, advertService.selectAdvertIdsByAdvertiserIds(tmpAdvertiserIds));
        return handleList(advertIds);
    }

    @Override
    public List<Long> filterOrientIdsByOrientSearch(List<Long> originOrientIds, String orientSearch) {
        if (StringUtils.isBlank(orientSearch)) {
            return originOrientIds;
        }
        AdvertOrientation orientParam = new AdvertOrientation();
        orientParam.setOrientSearch(orientSearch);
        List<Long> orientIds = mergeParamIds(originOrientIds, ListUtils.mapToList(advertOrientationService.selectAdvertOrientationList(orientParam), AdvertOrientation::getId));
        return handleList(orientIds);
    }

    @Override
    public List<Long> filterAppIdsByAppName(List<Long> originAppIds, String appName) {
        if (StringUtils.isBlank(appName)) {
            return originAppIds;
        }
        List<Long> appIds = mergeParamIds(originAppIds, appService.selectAppIdsByAppName(appName));
        return handleList(appIds);
    }

    @Override
    public List<Long> filterSlotIdsByAppSearch(List<Long> originSlotIds, String appSearch) {
        if (StringUtils.isBlank(appSearch)) {
            return originSlotIds;
        }
        List<Long> appIds = appService.selectAppIdsBySearchValue(appSearch);
        if (CollectionUtils.isEmpty(appIds)) {
            return INVALID_ID_LIST;
        }
        List<Long> slotIds = mergeParamIds(originSlotIds, slotService.selectSlotIdsByAppIds(appIds));
        return handleList(slotIds);
    }

    @Override
    public List<Long> filterSlotIdsBySlotSearch(List<Long> originSlotIds, String slotSearch) {
        if (StringUtils.isBlank(slotSearch)) {
            return originSlotIds;
        }

        List<Long> slotIds = mergeParamIds(originSlotIds, slotService.selectSlotIdsBySearchValue(slotSearch));
        return handleList(slotIds);
    }

    /**
     * 如果列表为空返回无效ID列表
     */
    private List<Long> handleList(List<Long> list) {
        return CollectionUtils.isEmpty(list) ? INVALID_ID_LIST : list;
    }

    /**
     * 合并查询条件中的ID列表
     * 如果mergeList为空，则返回originList
     * 如果originList为空，则返回mergeList
     * 如果originList不为空，则取originList和mergeList的交集
     *
     * @param originList 原列表(可能为空)
     * @param mergeList 待合并的列表
     * @return 合并后的ID列表
     */
    private List<Long> mergeParamIds(List<Long> originList, List<Long> mergeList) {
        if (CollectionUtils.isEmpty(mergeList)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(originList)) {
            return new ArrayList<>(mergeList);
        }
        List<Long> resultList = new ArrayList<>(originList);
        resultList.retainAll(mergeList);
        return resultList;
    }
}
