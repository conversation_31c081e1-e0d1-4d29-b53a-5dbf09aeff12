package com.ruoyi.system.service.open.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.service.open.FanliService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 返利对接Service接口实现
 *
 * <AUTHOR>
 * @date 2022/10/19
 */
@Slf4j
@Service
public class FanliServiceImpl implements FanliService {

    private static final String CONVERT_URL = "http://qilin.idai888.com/qw/forward/callback?click_id={}&convert_type={}&convert_time={}";

    @Override
    public String behaviorUpload(String orderId, Long slotId, JSONObject param) {
        String url = StrUtil.format(CONVERT_URL, param.getString("click_id"), 2, System.currentTimeMillis() / 1000);
        return HttpUtil.createGet(url).execute().body();
    }
}
