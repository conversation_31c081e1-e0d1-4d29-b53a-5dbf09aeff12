package com.ruoyi.system.service.callback.processor;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.MEITUAN;

/**
 * 美团上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/17
 */
@Slf4j
@Service
public class MeituanCallbackProcessor implements CallbackProcessor, InitializingBean {


    @Override
    public CallbackProcessorTypeEnum getType() {
        return MEITUAN;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("aid")) && StringUtils.isNotBlank(param.getString("cid"))
                || StringUtils.isNotBlank(param.getString("exposure_id"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            // 拼接参数
            JSONObject param = context.getParam().getSlotParam();
            String key = String.format("aid=%s&cid=%s&event_type=1&event_value=0&timestamp=%s&exposure_id=%s",
                    param.getString("aid"), param.getString("cid"), System.currentTimeMillis(), param.getString("exposure_id"));
            // 签名
            String sign = Md5Utils.hash(key + "meituanwm");
            String url = "https://adxproxy.waimai.meituan.com/conversionData/callback?" + key + "&sign=" + sign;
            String resp = HttpUtil.get(url);
            log.info("{}接口上报, url={}, resp={}", getType().getName(), url, resp);
            if (null != resp) {
                return true;
            }
            log.error("{}接口上报失败, url={}, resp={}", getType().getName(), url, resp);
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
