package com.ruoyi.system.service.callback.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.QTT;

/**
 * 趣头条上报处理器
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
@Slf4j
@Service
public class QttCallbackProcessor implements CallbackProcessor, InitializingBean {

    @Override
    public CallbackProcessorTypeEnum getType() {
        return QTT;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StrUtil.startWith(param.getString("qttCallback"), "http", true);
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        String callbackUrl = param.getString("qttCallback") + "&op2=26";
        try {
            String resp = HttpUtil.get(callbackUrl);
            log.info("{}接口上报, callbackUrl={}, resp={}", getType().getName(), callbackUrl, resp);
            return StringUtils.isNotBlank(resp);
        } catch (Exception e) {
            log.error("{}接口上报异常, callbackUrl={}", getType().getName(), callbackUrl, e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
