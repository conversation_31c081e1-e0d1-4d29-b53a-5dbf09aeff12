package com.ruoyi.system.service.app.impl;

import com.ruoyi.common.enums.TagManagerTypeEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.apptag.AppTagRelationEntity;
import com.ruoyi.system.mapper.app.AppTagRelationMapper;
import com.ruoyi.system.service.app.AppTagRelationService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 媒体标签关联表 Service
 *
 * <AUTHOR>
 * @date 2022-9-23 16:00:26
 */
@Service
public class AppTagRelationServiceImpl implements AppTagRelationService {

    @Autowired
    private AppTagRelationMapper appTagRelationMapper;

    @Autowired
    private TagManagerService tagManagerService;

    @Override
    public Boolean insert(AppTagRelationEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return appTagRelationMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return appTagRelationMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(AppTagRelationEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return appTagRelationMapper.updateById(entity) > 0;
    }

    @Override
    public AppTagRelationEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return appTagRelationMapper.selectById(id);
    }


    @Override
    public List<Long> selectTagIdsByTagIds(List<Long> tagIds) {
        if(CollectionUtils.isEmpty(tagIds)){
            return Collections.emptyList();
        }
        return appTagRelationMapper.selectTagIdsByTagIds(tagIds);
    }

    @Override
    public int countByTagId(Long tagId) {
        if(NumberUtils.isNullOrLteZero(tagId)){
            return 0;
        }
        return appTagRelationMapper.countByTagId(tagId);
    }

    @Override
    public List<Long> selectTagIdsByAppId(Long appId) {
        if(NumberUtils.isNullOrLteZero(appId)){
            return Collections.emptyList();
        }
        return appTagRelationMapper.selectTagIdsByAppId(appId);
    }

    @Override
    public Boolean deleteByAppId(Long appId) {
        return appTagRelationMapper.deleteByAppId(appId) > 0;
    }

    @Override
    public Boolean batchInsert(List<AppTagRelationEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return false;
        }
        return appTagRelationMapper.batchInsert(entities) > 0;
    }

    @Override
    public List<Long> selectAppIdsByParentTagName(String parentTagName) {
        if (StringUtils.isEmpty(parentTagName)) {
            return Collections.emptyList();
        }
        List<Long> parentTagIds = tagManagerService.selectTagIdsByFirstTagName(TagManagerTypeEnum.APP_TAG.getType(), parentTagName);
        List<Long> tagIds = tagManagerService.selectIdsByParentIds(parentTagIds);
        if (CollectionUtils.isEmpty(tagIds)) {
            return Collections.emptyList();
        }
        return appTagRelationMapper.selectAppIdsByTagIds(tagIds);
    }
}
