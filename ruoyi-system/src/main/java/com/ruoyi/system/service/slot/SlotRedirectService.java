package com.ruoyi.system.service.slot;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * 广告位投放Service接口
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
public interface SlotRedirectService {

    /**
     * 广告位投放数据统计(异步)
     *
     * @param slotId 广告位ID
     * @param date 日期
     * @param hour 小时
     * @param deviceId 设备号
     * @param redirectType 投放类型
     * @param redirectValue 投放信息
     */
    void statistics(Long slotId, Date date, Integer hour, String deviceId, Integer redirectType, String redirectValue);

    /**
     * 广告位点击监测
     *
     * @param request 请求对象
     */
    void clickCallback(HttpServletRequest request);
}
