package com.ruoyi.system.service.open.impl;

import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.system.entity.open.AdvertiserCallbackRecord;
import com.ruoyi.system.mapper.open.AdvertiserCallbackRecordMapper;
import com.ruoyi.system.service.open.AdvertiserCallbackRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ruoyi.common.enums.advert.ConvType.isSubmit;

/**
 * 广告主回调记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-15
 */
@Service
public class AdvertiserCallbackRecordServiceImpl implements AdvertiserCallbackRecordService {

    @Autowired
    private AdvertiserCallbackRecordMapper advertiserCallbackRecordMapper;

    @Override
    public AdvertiserCallbackRecord selectBy(AdvertiserCallbackRecord param) {
        return advertiserCallbackRecordMapper.selectBy(param);
    }

    @Override
    public int insert(AdvertiserCallbackRecord record) {
        return advertiserCallbackRecordMapper.insert(record);
    }

    @Override
    public Map<String, String> selectMapByOrderNo(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> typeMap = ConvType.getHaoKaMap();
        List<AdvertiserCallbackRecord> list = advertiserCallbackRecordMapper.selectStatusByOrderNo(orderNoList);
        Map<String, String> map = new HashMap<>();
        list.forEach(record -> {
            if (isSubmit(record.getStatus())) {
                return;
            }
            String conv = typeMap.getOrDefault(record.getStatus(), String.valueOf(record.getStatus()));
            if (map.containsKey(record.getOrderNo())) {
                map.put(record.getOrderNo(), map.get(record.getOrderNo()) + "," + conv);
            } else {
                map.put(record.getOrderNo(), conv);
            }
        });
        return map;
    }
}
