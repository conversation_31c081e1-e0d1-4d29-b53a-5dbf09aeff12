package com.ruoyi.system.service.blindbox.impl;

import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.blindbox.BlindBoxLandpageRecordService;
import com.ruoyi.system.entity.blindbox.BlindBoxLandpageRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.blindbox.BlindBoxLandpageRecordMapper;

/**
 * 盲盒落地页提交记录表 Service
 *
 * <AUTHOR>
 * @date 2022-6-13 11:10:16
 */
@Service
public class BlindBoxLandpageRecordServiceImpl implements BlindBoxLandpageRecordService {

    @Autowired
    private BlindBoxLandpageRecordMapper blindBoxLandpageRecordMapper;

    @Override
    public Boolean insert(BlindBoxLandpageRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        entity.setReferer(StrUtil.sub(entity.getReferer(), 0, 255));
        return blindBoxLandpageRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(BlindBoxLandpageRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return blindBoxLandpageRecordMapper.updateById(entity) > 0;
    }

    @Override
    public BlindBoxLandpageRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return blindBoxLandpageRecordMapper.selectById(id);
    }
}
