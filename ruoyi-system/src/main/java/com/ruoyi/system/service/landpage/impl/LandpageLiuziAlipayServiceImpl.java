package com.ruoyi.system.service.landpage.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.LandpageLiuziAlipayService;
import com.ruoyi.system.entity.landpage.LandpageLiuziAlipayEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.landpage.LandpageLiuziAlipayMapper;

/**
 * 支付宝留资落地页表 Service
 *
 * <AUTHOR>
 * @date 2022-12-5 10:08:00
 */
@Service
public class LandpageLiuziAlipayServiceImpl implements LandpageLiuziAlipayService {

    @Autowired
    private LandpageLiuziAlipayMapper landpageLiuziAlipayMapper;

    @Override
    public Boolean insert(LandpageLiuziAlipayEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return landpageLiuziAlipayMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return landpageLiuziAlipayMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(LandpageLiuziAlipayEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return landpageLiuziAlipayMapper.updateById(entity) > 0;
    }

    @Override
    public LandpageLiuziAlipayEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return landpageLiuziAlipayMapper.selectById(id);
    }

    @Override
    public List<LandpageLiuziAlipayEntity> selectListByLandpageIds(List<Long> landpageIds) {
        if(CollectionUtils.isEmpty(landpageIds)){
            return Collections.emptyList();
        }
        return landpageLiuziAlipayMapper.selectListByLandpageIds(landpageIds);
    }

    @Override
    public Boolean batchInsertOrUpdate(List<LandpageLiuziAlipayEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return false;
        }
        return landpageLiuziAlipayMapper.batchInsertOrUpdate(entities);
    }

    @Override
    public Map<Long, String> selectLandpageNameMap(List<Long> landpageIds) {
        List<LandpageLiuziAlipayEntity> list = selectListByLandpageIds(landpageIds);
        return list.stream().collect(Collectors.toMap(LandpageLiuziAlipayEntity::getLandpageId, LandpageLiuziAlipayEntity::getLandpageName));
    }
}
