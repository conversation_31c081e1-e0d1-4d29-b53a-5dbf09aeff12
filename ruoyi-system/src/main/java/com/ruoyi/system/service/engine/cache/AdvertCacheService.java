package com.ruoyi.system.service.engine.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.enums.IsDefaultEnum;
import com.ruoyi.common.enums.advert.AdvertStatusEnum;
import com.ruoyi.common.enums.advert.LandpageTypeEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.advert.AdvertExtInfo;
import com.ruoyi.system.bo.advert.AdvertOrientBO;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertOcpcCacheDto;
import com.ruoyi.system.domain.engine.MaterialCacheDto;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.mapper.manager.AdvertMapper;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.manager.MaterialService;
import com.ruoyi.system.service.traffic.TrafficPackageService;
import com.ruoyi.system.vo.advert.MaterialVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.advert.ChargeTypeEnum.CPC;
import static com.ruoyi.common.enums.common.MapConfigEnum.ADVERT_OCPC_CONV_MAP;
import static com.ruoyi.common.enums.common.SwitchStatusEnum.ON;
import static com.ruoyi.common.enums.common.SwitchStatusEnum.isSwitchOff;
import static java.util.stream.Collectors.toList;

/**
 * 广告缓存服务
 *
 * <AUTHOR>
 * @date 2021/8/12
 */
@Slf4j
@Service
public class AdvertCacheService implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private AdvertMapper advertMapper;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private MaterialService materialService;

    @Autowired
    private TrafficPackageService trafficPackageService;

    @Autowired
    private MapConfigService mapConfigService;

    /**
     * 广告本地缓存
     */
    private final LoadingCache<Long, Optional<AdvertCacheDto>> ADVERT_ORIENT_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Optional<AdvertCacheDto>>() {

                @Override
                public Optional<AdvertCacheDto> load(Long orientId) {
                    AdvertCacheDto advertCacheDto = buildAdvertCacheDto(orientId);
                    return Optional.ofNullable(advertCacheDto);
                }

                @Override
                public ListenableFuture<Optional<AdvertCacheDto>> reload(Long orientId, Optional<AdvertCacheDto> oldValue) {
                    ListenableFutureTask<Optional<AdvertCacheDto>> task = ListenableFutureTask.create(() -> load(orientId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告OCPC配置缓存
     */
    private final LoadingCache<Long, AdvertOcpcCacheDto> ADVERT_OCPC_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Long, AdvertOcpcCacheDto>() {

                @Override
                public AdvertOcpcCacheDto load(Long orientId) {
                    AdvertOcpcCacheDto ocpcDto = new AdvertOcpcCacheDto();
                    ocpcDto.setOrientId(orientId);

                    AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(orientId);
                    if (null != orient) {
                        ocpcDto.setAdvertId(orient.getAdvertId());
                        ocpcDto.setUnitPrice(orient.getUnitPrice());
                        ocpcDto.setOcpcConvType(orient.getOcpcConvType());
                        ocpcDto.setOcpcConvCost(orient.getOcpcConvCost());
                        ocpcDto.setConvThreshold(Math.max(NumberUtils.defaultInt(mapConfigService.getValue(ADVERT_OCPC_CONV_MAP, orient.getAdvertId(), Integer.class), 5), 1));
                    }
                    return ocpcDto;
                }

                @Override
                public ListenableFuture<AdvertOcpcCacheDto> reload(Long advertId, AdvertOcpcCacheDto oldValue) {
                    ListenableFutureTask<AdvertOcpcCacheDto> task = ListenableFutureTask.create(() -> load(advertId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告点击监测链接缓存
     */
    private final LoadingCache<Long, String> CLICK_URL_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(200)
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, String>() {

                @Override
                public String load(Long advertId) {
                    Advert advert = advertMapper.selectAdvertById(advertId);
                    if (null != advert) {
                        return StringUtils.defaultString(advert.getClickCallbackUrl());
                    }
                    return StringUtils.EMPTY;
                }

                @Override
                public ListenableFuture<String> reload(Long advertId, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(advertId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告主ID缓存
     */
    private final LoadingCache<Long, Long> ADVERTISER_ID_CACHE = CacheBuilder
            .newBuilder()
            .expireAfterWrite(3, TimeUnit.DAYS)
            .build(new CacheLoader<Long, Long>() {

                @Override
                public Long load(Long advertId) {
                    return NumberUtils.defaultLong(advertService.selectById(advertId, Advert::getAdvertiserId), 0L);
                }

                @Override
                public ListenableFuture<Long> reload(Long advertId, Long oldValue) {
                    ListenableFutureTask<Long> task = ListenableFutureTask.create(() -> load(advertId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });


    /**
     * 查询所有广告缓存(返回的是可修改的拷贝对象)
     */
    public List<AdvertCacheDto> queryTotalAdvertCache() {
        return ADVERT_ORIENT_CACHE.asMap().values().stream()
                .filter(Optional::isPresent)
                .map(advertCacheDto -> advertCacheDto.get().copy())
                .collect(toList());
    }

    /**
     * 查询广告缓存(返回的是可修改的拷贝对象)
     */
    public List<AdvertCacheDto> queryAdvertCache(Long advertId) {
        return ADVERT_ORIENT_CACHE.asMap().values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .filter(advertCacheDto -> Objects.equals(advertCacheDto.getAdvertId(), advertId) && IsDefaultEnum.isDefault(advertCacheDto.getIsDefaultOrient()))
                .map(AdvertCacheDto::copy)
                .collect(toList());
    }

    /**
     * 查询广告缓存(返回的是可修改的拷贝对象)
     */
    public List<AdvertCacheDto> queryAdvertCache(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyList();
        }
        return ADVERT_ORIENT_CACHE.asMap().values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .filter(advertCacheDto -> advertIds.contains(advertCacheDto.getAdvertId()) && IsDefaultEnum.isDefault(advertCacheDto.getIsDefaultOrient()))
                .map(AdvertCacheDto::copy)
                .collect(toList());
    }

    /**
     * 批量查询广告缓存(返回的是可修改的拷贝对象)
     */
    public List<AdvertCacheDto> batchQueryAdvertCache(List<Long> orientIds) {
        if (CollectionUtils.isEmpty(orientIds)) {
            return Collections.emptyList();
        }
        try {
            return ADVERT_ORIENT_CACHE.getAll(orientIds).values().stream()
                    .filter(Optional::isPresent)
                    .map(advertCacheDto -> advertCacheDto.get().copy())
                    .collect(toList());
        } catch (ExecutionException e) {
            log.error("查询广告缓存异常, orientIds={}", JSON.toJSONString(orientIds), e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询广告缓存(返回的对象千万不能修改)
     */
    public AdvertCacheDto queryAdvert(Long orientId) {
        try {
            return ADVERT_ORIENT_CACHE.get(orientId).orElse(null);
        } catch (ExecutionException e) {
            log.error("查询广告缓存异常, orientId={}", orientId, e);
        }
        return null;
    }

    /**
     * 查询广告OCPC缓存
     */
    public AdvertOcpcCacheDto queryAdvertOcpcCache(Long orientId) {
        try {
            return ADVERT_OCPC_CACHE.get(orientId);
        } catch (ExecutionException e) {
            log.error("查询广告OCPC缓存, orientId={}", orientId, e);
        }
        return new AdvertOcpcCacheDto();
    }


    /**
     * 查询点击监测链接
     */
    public String queryClickCallbackUrl(Long advertId) {
        if (null == advertId) {
            return null;
        }

        try {
            return CLICK_URL_CACHE.get(advertId);
        } catch (ExecutionException e) {
            log.error("查询点击监测链接缓存异常，advertId={}", advertId, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 查询广告主ID
     */
    public Long queryAdvertiserId(Long advertId) {
        if (null != advertId) {
            try {
                return ADVERTISER_ID_CACHE.get(advertId);
            } catch (ExecutionException e) {
                log.error("查询查询广告主ID缓存异常，advertId={}", advertId, e);
            }
        }
        return 0L;
    }

    /**
     * 刷新广告缓存
     *
     * @param advertId 广告ID
     */
    public void refreshAdvertCache(Long advertId) {
        if (null == advertId) {
            return;
        }
        // 查询广告下的配置并刷新本地缓存
        List<Long> orientIds = advertOrientationService.selectIdsByAdvertId(advertId);
        if (CollectionUtils.isNotEmpty(orientIds)) {
            orientIds.forEach(orientId -> {
                ADVERT_ORIENT_CACHE.refresh(orientId);
                ADVERT_OCPC_CACHE.invalidate(orientId);
            });
        }
        // 刷新广告监测链接缓存
        CLICK_URL_CACHE.refresh(advertId);
    }

    /**
     * 批量刷新广告缓存
     *
     * @param advertIds 广告ID列表
     */
    public void refreshAdvertCache(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return;
        }
        // 查询广告下的配置并刷新本地缓存
        List<Long> orientIds = advertOrientationService.selectIdsByAdvertIds(advertIds);
        if (CollectionUtils.isNotEmpty(orientIds)) {
            orientIds.forEach(orientId -> {
                ADVERT_ORIENT_CACHE.refresh(orientId);
                ADVERT_OCPC_CACHE.invalidate(orientId);
            });
        }
        // 刷新广告监测链接缓存
        advertIds.forEach(CLICK_URL_CACHE::refresh);
    }

    /**
     * 刷新广告配置缓存
     *
     * @param orientId 配置ID
     */
    public void refreshAdvertOrientationCache(Long orientId) {
        if (null != orientId) {
            ADVERT_ORIENT_CACHE.refresh(orientId);
            ADVERT_OCPC_CACHE.invalidate(orientId);
        }
    }

    /**
     * 批量刷新广告配置缓存
     *
     * @param orientIds 配置ID列表
     */
    public void refreshAdvertOrientationCache(List<Long> orientIds) {
        if (CollectionUtils.isNotEmpty(orientIds)) {
            orientIds.forEach(orientId -> {
                ADVERT_ORIENT_CACHE.refresh(orientId);
                ADVERT_OCPC_CACHE.invalidate(orientId);
            });
        }
    }

    /**
     * 初始化广告引擎需要使用的缓存
     */
    private void initAdvertCache() {
        // 查询有效广告列表
        List<Long> validAdvertIds = advertService.selectValidAdvertIds();
        if (CollectionUtils.isEmpty(validAdvertIds)) {
            return;
        }

        // 查询广告信息并缓存
        ADVERT_ORIENT_CACHE.invalidateAll();
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(validAdvertIds);
        Map<Long, List<MaterialVO>> materialMap = materialService.selectValidByAdvertIds(validAdvertIds);
        Map<Long, Set<Long>> bannedAppMap = advertOrientationService.selectOrientBannedApp(validAdvertIds);
        Map<Long, AdvertOrientBO> orientAppMap = advertOrientationService.selectOrientInfo(validAdvertIds);
        Map<Long, Set<Long>> trafficMap = trafficPackageService.selectTrafficSlotByAdvertIds(validAdvertIds);
        List<AdvertOrientation> orients = advertOrientationService.selectListByAdvertIds(validAdvertIds);

        for (AdvertOrientation orient : orients) {
            // 过滤不可投放广告配置
            if (isSwitchOff(orient.getServingSwitch())) {
                continue;
            }

            Long advertId = orient.getAdvertId();
            Advert advert = advertMap.get(advertId);
            if (null == advert) {
                continue;
            }
            List<MaterialVO> materials = materialMap.get(advertId);
            if (CollectionUtils.isEmpty(materials)) {
                continue;
            }

            // 广告信息
            AdvertCacheDto advertCacheDto = convertTo(advert);
            advertCacheDto.setOrientId(orient.getId());
            advertCacheDto.setOrientName(orient.getOrientName());
            advertCacheDto.setChargeType(CPC.getType());
            advertCacheDto.setUnitPrice(orient.getUnitPrice());
            advertCacheDto.setMilliUnitPrice(orient.getMilliUnitPrice());
            advertCacheDto.setServingSwitch(advertCacheDto.getServingSwitch() & orient.getServingSwitch());
            advertCacheDto.setWeight(orient.getWeight());
            advertCacheDto.setOrderFactor(advertCacheDto.getUnitPrice() * orient.getWeight());
            advertCacheDto.setIsDefaultOrient(orient.getIsDefault());
            advertCacheDto.setOrientBudget(orient.getDailyBudget());
            // 使用配置的落地页
            if (LandpageTypeEnum.isCustom(orient.getLandpageType()) && StringUtils.isNotBlank(orient.getLandpageUrl())) {
                advertCacheDto.setLandpageUrl(orient.getLandpageUrl());
            }
            advertCacheDto.setOriginLandpageUrl(advertCacheDto.getLandpageUrl());
            // 屏蔽媒体
            advertCacheDto.setBannedAppIds(bannedAppMap.get(orient.getId()));
            // 定向媒体广告位
            Optional.ofNullable(orientAppMap.get(orient.getId())).ifPresent(bo -> {
                // 定向媒体
                advertCacheDto.setOrientAppIds(bo.getAppIds());
                //定向广告位
                advertCacheDto.setOrientSlotMap(bo.getAppSlotMap());
            });
            // 定向流量包
            advertCacheDto.setOrientSlotIds(trafficMap.get(advertId));
            // 地域定向
            advertCacheDto.setAreaTargetSet(JSON.parseObject(orient.getAreaTarget(), new TypeReference<Set<String>>() {}));
            // 可投素材列表
            advertCacheDto.setMaterialMap(buildMaterialMap(materials));
            // 设备定向
            advertCacheDto.setDeviceTarget(orient.getDeviceTarget());
            // 流量定向
            advertCacheDto.setFlowTarget(orient.getFlowTarget());
            // 系统定向
            advertCacheDto.setOsTarget(orient.getOsTarget());
            // 运营商定向
            advertCacheDto.setIspTarget(orient.getIspTarget());
            // 投放时段
            advertCacheDto.setServingHour(orient.getServingHour());
            ADVERT_ORIENT_CACHE.put(orient.getId(), Optional.of(advertCacheDto));
        }
    }

    /**
     * 初始化广告引擎需要使用的缓存
     */
    private void initClickUrlCache() {
        Advert param = new Advert();
        param.setAdvertStatus(AdvertStatusEnum.NORMAL.getStatus());
        param.setServingSwitch(ON.getStatus());
        param.setIsInvalid(0);
        List<Advert> adverts = advertMapper.selectAdvertList(param);
        if (CollectionUtils.isNotEmpty(adverts)) {
            adverts.forEach(ad -> CLICK_URL_CACHE.put(ad.getId(), StringUtils.defaultString(ad.getClickCallbackUrl())));
        }
    }

    /**
     * 构造广告缓存Dto
     */
    private AdvertCacheDto buildAdvertCacheDto(Long orientId) {
        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(orientId);
        if (null == orient || Objects.equals(orient.getIsDeleted(), 1)) {
            return null;
        }

        Long advertId = orient.getAdvertId();
        Advert advert = advertMapper.selectAdvertById(advertId);
        if (null == advert) {
            return null;
        }
        List<Long> advertIds = Collections.singletonList(advertId);

        // 广告信息
        AdvertCacheDto advertCacheDto = convertTo(advert);
        advertCacheDto.setOrientId(orientId);
        advertCacheDto.setOrientName(orient.getOrientName());
        advertCacheDto.setChargeType(CPC.getType());
        advertCacheDto.setUnitPrice(orient.getUnitPrice());
        advertCacheDto.setMilliUnitPrice(orient.getMilliUnitPrice());
        advertCacheDto.setIsDefaultOrient(orient.getIsDefault());
        advertCacheDto.setServingSwitch(advertCacheDto.getServingSwitch() & orient.getServingSwitch());
        advertCacheDto.setWeight(orient.getWeight());
        advertCacheDto.setOrderFactor(advertCacheDto.getUnitPrice() * orient.getWeight());
        advertCacheDto.setOrientBudget(orient.getDailyBudget());
        // 使用配置的落地页
        if (LandpageTypeEnum.isCustom(orient.getLandpageType()) && StringUtils.isNotBlank(orient.getLandpageUrl())) {
            advertCacheDto.setLandpageUrl(orient.getLandpageUrl());
        }
        advertCacheDto.setOriginLandpageUrl(advertCacheDto.getLandpageUrl());
        // 广告可投素材
        Map<Long, List<MaterialVO>> materialMap = materialService.selectValidByAdvertIds(advertIds);
        if (!materialMap.containsKey(advertId)) {
            return null;
        }
        advertCacheDto.setMaterialMap(buildMaterialMap(materialMap.get(advertId)));
        // 屏蔽媒体
        Map<Long, Set<Long>> bannedAppMap = advertOrientationService.selectOrientBannedApp(advertIds);
        advertCacheDto.setBannedAppIds(bannedAppMap.get(orient.getId()));
        // 媒体广告位定向
        Map<Long, AdvertOrientBO> orientAppMap = advertOrientationService.selectOrientInfo(advertIds);
        Optional.ofNullable(orientAppMap.get(orient.getId())).ifPresent(bo -> {
            advertCacheDto.setOrientAppIds(bo.getAppIds());
            advertCacheDto.setOrientSlotMap(bo.getAppSlotMap());
        });
        // 流量包定向
        Map<Long, Set<Long>> trafficMap = trafficPackageService.selectTrafficSlotByAdvertIds(advertIds);
        advertCacheDto.setOrientSlotIds(trafficMap.get(advertId));
        // 地域定向
        advertCacheDto.setAreaTargetSet(JSON.parseObject(orient.getAreaTarget(), new TypeReference<Set<String>>() {}));
        // 设备定向
        advertCacheDto.setDeviceTarget(orient.getDeviceTarget());
        // 流量定向
        advertCacheDto.setFlowTarget(orient.getFlowTarget());
        // 系统定向
        advertCacheDto.setOsTarget(orient.getOsTarget());
        // 运营商定向
        advertCacheDto.setIspTarget(orient.getIspTarget());
        // 投放时段
        advertCacheDto.setServingHour(orient.getServingHour());
        return advertCacheDto;
    }

    /**
     * convert Advert to AdvertCacheDto
     */
    private AdvertCacheDto convertTo(Advert advert) {
        AdvertCacheDto advertCacheDto = new AdvertCacheDto();
        advertCacheDto.setAdvertId(advert.getId());
        advertCacheDto.setAssessType(advert.getAssessType());
        advertCacheDto.setAssessCost(advert.getAssessCost());
        advertCacheDto.setAdvertCategory(advert.getAdvertCategory());
        advertCacheDto.setAdvertName(advert.getAdvertName());
        advertCacheDto.setThumbnailImg(advert.getThumbnailImg());
        advertCacheDto.setLandpageUrl(advert.getLandpageUrl());
        advertCacheDto.setAdvertStatus(advert.getAdvertStatus());
        advertCacheDto.setServingSwitch(advert.getServingSwitch());
        advertCacheDto.setStartServingDate(advert.getStartServingDate());
        advertCacheDto.setStopServingDate(advert.getStopServingDate());
        advertCacheDto.setAdvertBudget(advert.getDailyBudget());
        advertCacheDto.setAdvertiserId(advert.getAdvertiserId());
        advertCacheDto.setExtInfo(JSON.parseObject(advert.getExtInfo(), AdvertExtInfo.class));
        return advertCacheDto;
    }

    /**
     * convert Material to MaterialCacheDto
     */
    private MaterialCacheDto convertTo(MaterialVO material) {
        MaterialCacheDto materialCacheDto = new MaterialCacheDto();
        materialCacheDto.setMaterialId(material.getId());
        materialCacheDto.setWeight(material.getWeight());
        materialCacheDto.setAdvertTitle(material.getAdvertTitle());
        materialCacheDto.setButtonText(material.getButtonText());
        materialCacheDto.setLayerId(material.getLayerId());
        materialCacheDto.setLayerType(material.getLayerType());
        materialCacheDto.setMaterialImg(material.getMaterialImg());
        materialCacheDto.setGifImg(material.getGifImg());
        materialCacheDto.setLayerInfo(material.getLayerInfo());
        materialCacheDto.setSkinType(material.getSkinType());
        return materialCacheDto;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        if (contextRefreshedEvent.getApplicationContext().getParent() == null) {
            log.info("AdvertCacheService.initAdvertCache started.");
            initAdvertCache();
            log.info("AdvertCacheService.initAdvertCache finished.");

            log.info("AdvertCacheService.initClickUrlCache started.");
            initClickUrlCache();
            log.info("AdvertCacheService.initClickUrlCache finished.");
        }
    }

    /**
     * 根据素材权重构造TreeMap
     *
     * @param materials 素材列表
     * @return 素材TreeMap
     */
    private TreeMap<Integer, MaterialCacheDto> buildMaterialMap(List<MaterialVO> materials) {
        TreeMap<Integer, MaterialCacheDto> materialMap = new TreeMap<>();
        for (MaterialVO material : materials) {
            if (material.getWeight() < 1) {
                continue;
            }
            int lastWeight = materialMap.size() == 0 ? 0 : materialMap.lastKey();
            materialMap.put(material.getWeight() + lastWeight, convertTo(material));
        }
        return materialMap;
    }
}
