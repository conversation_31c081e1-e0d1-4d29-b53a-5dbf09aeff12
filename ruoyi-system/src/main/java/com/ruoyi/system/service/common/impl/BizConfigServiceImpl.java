package com.ruoyi.system.service.common.impl;

import cn.hutool.core.convert.Convert;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.system.service.common.BizConfigService;
import com.ruoyi.system.service.system.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 业务配置服务接口实现
 *
 * <AUTHOR>
 * @date 2022/11/09
 */
@Service
public class BizConfigServiceImpl implements BizConfigService {

    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public String getValue(BizConfigEnum config) {
        return sysConfigService.selectConfigCacheByKey(config.getKey());
    }

    @Override
    public <T> T getValue(BizConfigEnum config, Class<T> vClass) {
        String value = getValue(config);
        return null != value ? Convert.convert(vClass, value) : null;
    }
}
