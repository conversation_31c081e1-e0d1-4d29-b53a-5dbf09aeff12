package com.ruoyi.system.service.oa.user;

import com.ruoyi.system.entity.oa.user.UserEntity;

import java.util.List;
import java.util.Map;

/**
 * sso账号表 Service
 *
 * <AUTHOR>
 * @date 2022-6-2 15:43:29
 */
public interface UserService {

    /**
     * 根据邮箱查询账号信息
     *
     * @param email 邮箱
     * @return 账号信息
     */
    UserEntity selectByEmail(String email);

    /**
     * 根据邮箱查询账号ID
     *
     * @param email 邮箱
     * @return 账号ID
     */
    Long selectUserIdByEmail(String email);

    /**
     * 根据邮箱列表查询账号ID
     *
     * @param emails 邮箱列表
     * @return 邮箱-账号ID列表
     */
    Map<String, Long> selectMapByEmails(List<String> emails);

    /**
     * 根据邮箱模糊查询用户
     *
     * @param email
     * @return
     */
    List<UserEntity> selectByLikeEmail(String email);

    /**
     * 根据id查询用户信息
     * @param id
     * @return
     */
    UserEntity selectById(Long id);
}
