package com.ruoyi.system.service.miniapp;

/**
 * 微信小程序service
 *
 * <AUTHOR>
 * @date 2022/10/9 10:45 上午
 */
public interface MiniAppService {

    /**
     * 获取小程序短链
     * 详情 https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/url-scheme/urlscheme.generate.html
     *
     * @param path 小程序路径
     * @param query 通过 scheme 码进入小程序时的 query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：`!#$&'()*+,/:;=?@-._~%`` ，参数拼接用key=value的方式，不能用json
     * @param expireInterval 到期失效的 scheme 码的失效间隔天数
     * @return 小程序短链
     */
    String getUrlScheme(String path,String query,Integer expireInterval);
}
