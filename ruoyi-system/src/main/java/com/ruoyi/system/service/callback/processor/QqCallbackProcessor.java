package com.ruoyi.system.service.callback.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.QQ;

/**
 * 广点通上报处理器
 * 文档: https://developers.e.qq.com/docs/guide/conversion/new_version/Web_api
 * 行为:表单预约.RESERVATION,卡号激活.COMPLETE_ORDER,首充付费.PURCHASE,加群.ADD_GROUP
 *
 * <AUTHOR>
 * @date 2024/1/17
 */
@Slf4j
@Service
public class QqCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final String TEMPLATE_URL_CONV = "http://tracking.e.qq.com/conv";

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public CallbackProcessorTypeEnum getType() {
        return QQ;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("qqClickId"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            long timestamp = System.currentTimeMillis() / 1000;

            JSONObject trace = new JSONObject();
            trace.put("click_id", context.getParam().getSlotParam().getString("qqClickId"));

            JSONObject action = new JSONObject();
            action.put("action_time", timestamp);
            action.put("action_type", getActionType(context.getExt()));
            action.put("trace", trace);
            action.put("url", UrlUtils.extractDomain(getSlotUrl(context.getOrder())));     // 一定要和配置的投放链接域名一致，不然联调不通过

            JSONArray actions = new JSONArray();
            actions.add(action);

            JSONObject param = new JSONObject();
            param.put("actions", actions);

            String resp = HttpUtil.createPost(StrUtil.format(TEMPLATE_URL_CONV))
                    .header("Content-Type", "application/json")
                    .header("cache-control", "no-cache")
                    .body(param.toString())
                    .execute().body();

            log.info("{}接口上报, slotId={}, req={}, resp={}", getType().getName(), context.getOrder().getSlotId(), param, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getInteger("code"), 0)) {
                return true;
            }
            log.error("{}接口上报失败, slotId={}, req={}, resp={}", getType().getName(), context.getOrder().getSlotId(), param, resp);
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }

    /**
     * 获取上报的行为
     */
    private String getActionType(Map<String, String> ext) {
        // 行为:表单预约.RESERVATION,卡号激活.COMPLETE_ORDER,首充付费.PURCHASE,加群.ADD_GROUP
        return StringUtils.defaultString(null != ext ? ext.get("actionType") : null, "RESERVATION");
    }

    /**
     * 获取真实投放的广告位链接
     */
    private String getSlotUrl(Order order) {
        return slotCacheService.getSlotCache(order.getSlotId()).getSlotUrl();
//        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
//        if (null == adSnapshot) {
//            return slotCacheService.getSlotCache(order.getSlotId()).getSlotUrl();
//        }
//        String slotUrl = redisCache.getCacheObject(EngineRedisKeyFactory.K134.join(adSnapshot.getSrid()));
//        slotUrl = slotUrl.substring(0, slotUrl.indexOf("&sid=")) + "&sid=" + order.getSlotId();
//        slotUrl = slotUrl.replace("http://", "https://");
//        return slotUrl;
    }
}
