package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.system.service.landpage.QwtfWxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 企微囤粉微信服务实现
 *
 * <AUTHOR>
 * @date 2023-9-25
 */
@Slf4j
@Service
public class QwtfWxServiceImpl implements QwtfWxService {

    // 企微appId
    @Value("${qwtf.server.corpid}")
    private String corpid;

    // 企微secret
    @Value("${qwtf.server.corpsecret}")
    private String corpsecret;

    // 小程序appId
    @Value("${qwtf.client.appId}")
    private String clientAppId;

    // 小程序secret
    @Value("${qwtf.client.secret}")
    private String clientSecret;

    @Autowired
    private RedisCache redisCache;

    @Override
    public String getAccessToken(String appId, String secret) {
        try {
            String key = EngineRedisKeyFactory.K096.join(appId);
            String accessToken = redisCache.getCacheObject(key);
            if (StringUtils.isNotBlank(accessToken)) {
                return accessToken;
            }

            JSONObject param = new JSONObject();
            param.put("grant_type", "client_credential");
            param.put("appid", appId);
            param.put("secret", secret);
            String resp = HttpUtil.post("https://api.weixin.qq.com/cgi-bin/stable_token", param.toString());
            log.info("企微囤粉小程序获取accessToken, appId={}, resp={}", appId, resp);
            if (StringUtils.isNotBlank(resp)) {
                JSONObject result = JSON.parseObject(resp);
                accessToken = result.getString("access_token");
                if (StringUtils.isNotBlank(accessToken)) {
                    redisCache.setCacheObject(key, accessToken, result.getIntValue("expires_in") - 60, TimeUnit.SECONDS);
                    return accessToken;
                }
                log.error("企微囤粉小程序获取accessToken失败, appId={}, resp={}", appId, resp);
            }
        } catch (Exception e) {
            log.error("企微囤粉小程序获取accessToken异常, appId={}", appId, e);
        }
        return "";
    }

    @Override
    public String getCorpAccessToken(String corpid, String corpsecret) {
        try {
            String key = EngineRedisKeyFactory.K096.join(corpid);
            String accessToken = redisCache.getCacheObject(key);
            if (StringUtils.isNotBlank(accessToken)) {
                return accessToken;
            }

            String resp = HttpUtil.get(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={}&corpsecret={}", corpid, corpsecret));
            log.info("企微囤粉企微获取accessToken, corpid={}, resp={}", corpid, resp);
            if (StringUtils.isNotBlank(resp)) {
                JSONObject result = JSON.parseObject(resp);
                accessToken = result.getString("access_token");
                if (StringUtils.isNotBlank(accessToken)) {
                    redisCache.setCacheObject(key, accessToken, result.getIntValue("expires_in"), TimeUnit.SECONDS);
                    return accessToken;
                }
                log.error("企微囤粉企微获取accessToken失败, corpid={}, resp={}", corpid, resp);
            }
        } catch (Exception e) {
            log.error("企微囤粉企微获取accessToken异常, corpid={}", corpid, e);
        }
        return "";
    }

    @Override
    public String getUnionId(String code) {
        try {
            String resp = HttpUtil.get(StrUtil.format("https://api.weixin.qq.com/sns/jscode2session?appid={}&secret={}&js_code={}&grant_type=authorization_code", clientAppId, clientSecret, code));
            log.info("企微囤粉小程序获取unionId, code={}, resp: {}", code, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && result.containsKey("unionid")) {
                return result.getString("unionid");
            }
            log.error("企微囤粉小程序获取unionId失败, {}", resp);
        } catch (Exception e) {
            log.error("企微囤粉小程序获取unionId异常", e);
        }
        return "";
    }

    @Override
    public String getUserPhoneNumber(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }

        try {
            JSONObject param = new JSONObject();
            param.put("code", code);
            String resp = HttpUtil.post("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + getAccessToken(clientAppId, clientSecret), param.toString());
            log.info("企微囤粉小程序获取手机号, code={}, resp={}", code, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getInteger("errcode"), 0) && null != result.getJSONObject("phone_info")) {
                return result.getJSONObject("phone_info").getString("phoneNumber");
            }
            log.error("企微囤粉小程序获取手机号失败, {}", resp);
        } catch (Exception e) {
            log.error("企微囤粉小程序获取手机号异常", e);
        }
        return "";
    }

    @Override
    public String getUrlScheme(String path, String query, Integer expireInterval) {
        try {
            String env = "release"; //正式版
            if (SpringEnvironmentUtils.isTest()) {
                env = "trial";//体验版
            } else if (SpringEnvironmentUtils.isDev()) {
                env = "develop"; //开发版
            }

            JSONObject jumpWxa = new JSONObject();
            jumpWxa.put("path", path);
            jumpWxa.put("query", query);
            jumpWxa.put("env_version", env);

            JSONObject param = new JSONObject();
            param.put("jump_wxa", jumpWxa);
            param.put("is_expire", true);
            param.put("expire_type", 1);
            param.put("expire_interval", expireInterval);

            String resp = HttpUtil.post("https://api.weixin.qq.com/wxa/generatescheme?access_token=" + getAccessToken(clientAppId, clientSecret), param.toString());
            log.info("企微囤粉小程序获取scheme码, path={}, query={}, expireInterval={}, resp={}", path, query, expireInterval, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getInteger("errcode"), 0)) {
                return result.getString("openlink");
            }
            log.error("企微囤粉小程序获取scheme码失败, path={}, query={}, expireInterval={}, resp={}", path, query, expireInterval, resp);
        } catch (Exception e) {
            log.error("企微囤粉小程序获取scheme码异常, path={}, query={}, expireInterval={}", path, query, expireInterval, e);
        }
        return "";
    }

    @Override
    public List<String> getExternalContactList(String userid) {
        if (StringUtils.isBlank(userid)) {
            return Collections.emptyList();
        }
        try {
            String resp = HttpUtil.get(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list?access_token={}&userid={}", getCorpAccessToken(corpid, corpsecret), userid));
//            log.info("企微囤粉企微获取客户列表: {}", resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result) {
                if (Objects.equals(result.getInteger("errcode"), 0) && null != result.getJSONArray("external_userid")) {
                    return result.getJSONArray("external_userid").toJavaList(String.class);
                } else if (Objects.equals(result.getInteger("errcode"), 84061)) {
                    return Collections.emptyList();
                }
            }
            log.error("企微囤粉企微获取客户列表失败, userid={}, {}", userid, resp);
        } catch (Exception e) {
            log.error("企微囤粉企微获取客户列表异常, userid={}", userid, e);
        }
        return Collections.emptyList();
    }

    @Override
    public String getExternalContactUnionId(String externalUserid) {
        if (StringUtils.isBlank(externalUserid)) {
            return "";
        }
        try {
            String key = EngineRedisKeyFactory.K100.join(externalUserid);
            String unionId = redisCache.getCacheObject(key);
            if (StringUtils.isNotBlank(unionId)) {
                return unionId;
            }

            String resp = HttpUtil.get(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get?access_token={}&external_userid={}", getCorpAccessToken(corpid, corpsecret), externalUserid));
            log.info("企微囤粉企微取客户详情, externalUserid={}, resp={}", externalUserid, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getInteger("errcode"), 0) && null != result.getJSONObject("external_contact")) {
                unionId = result.getJSONObject("external_contact").getString("unionid");
                if (StringUtils.isNotBlank(unionId)) {
                    redisCache.setCacheObject(key, unionId, 3, TimeUnit.DAYS);
                }
                return unionId;
            }
            log.error("企微囤粉企微获取客户详情失败, externalUserid={}, resp={}", externalUserid, resp);
        } catch (Exception e) {
            log.error("企微囤粉企微获取客户详情异常, externalUserid={}", externalUserid, e);
        }
        return "";
    }

    @Override
    public JSONObject getExternalContact(String externalUserid) {
        if (StringUtils.isBlank(externalUserid)) {
            return null;
        }
        try {
            String key = EngineRedisKeyFactory.K102.join(externalUserid);
            String value = redisCache.getCacheObject(key);
            if (StringUtils.isNotBlank(value)) {
                return JSON.parseObject(value);
            }

            String resp = HttpUtil.get(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get?access_token={}&external_userid={}", getCorpAccessToken(corpid, corpsecret), externalUserid));
            log.info("企微囤粉企微取客户详情, externalUserid={}, resp={}", externalUserid, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getInteger("errcode"), 0)) {
                redisCache.setCacheObject(key, resp, 3, TimeUnit.DAYS);
                return result;
            }
            log.error("企微囤粉企微获取客户详情失败, externalUserid={}, resp={}", externalUserid, resp);
        } catch (Exception e) {
            log.error("企微囤粉企微获取客户详情异常, externalUserid={}", externalUserid, e);
        }
        return null;
    }
}
