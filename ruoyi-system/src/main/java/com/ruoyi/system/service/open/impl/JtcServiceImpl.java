package com.ruoyi.system.service.open.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity;
import com.ruoyi.system.service.open.JtcService;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jcajce.provider.digest.SM3;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 捷停车对接Service接口实现
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Slf4j
@Service
public class JtcServiceImpl implements JtcService {

    @Autowired
    private RedisCache redisCache;

    @Override
    public Boolean convAndGrantCoupon(Order order, ExternalCouponRecordEntity record, String jtcAdRequestId, String jtcCoupons) {
        if (StringUtils.isBlank(jtcAdRequestId) || StringUtils.isBlank(jtcCoupons)) {
            return false;
        }

        try {
            // 避免优惠券重复发放
            String redisKey = EngineRedisKeyFactory.K140.join("jtc", order.getOrderId());
            if (redisCache.hasKey(redisKey)) {
                return false;
            }
            String phone = StringUtils.defaultString(redisCache.getCacheObject(EngineRedisKeyFactory.K142.join(order.getOrderId())));

            // 参数记录
            JSONObject couponInfo = new JSONObject();
            couponInfo.put("jtcAdRequestId", jtcAdRequestId);
            couponInfo.put("jtcCoupons", jtcCoupons);
            couponInfo.put("phone", phone);
            record.setCouponInfo(couponInfo.toString());
            record.setCouponRequestId(jtcAdRequestId);

            // 调用接口发放优惠券
            String url = "https://api.jparking.cn";
            String jtcAppId = "1231969570290139138";
            String jtcAppKey = "zdk3hjenijyw9hix";
//            if (!SpringEnvironmentUtils.isProd()) {
//                url = "https://jtc-test.jslife.com.cn/open-api/";
//                jtcAppId = "1231934502293577730";
//                jtcAppKey = "t6ml6g8b482tfbqk";
//            }

            JSONObject bizContent = new JSONObject();
            bizContent.put("jtcAdRequestId", jtcAdRequestId);
            if (StringUtils.isNotBlank(jtcCoupons)) {
                bizContent.put("jtcCoupons", jtcCoupons);
            }
            if (StringUtils.isNotBlank(phone)) {
                bizContent.put("phone", phone);
                bizContent.put("carNo", "1");
            }

            JSONObject body = new JSONObject();
            body.put("app_id", jtcAppId);
            body.put("method", "syt.ad.dsp.cpa.convert");
            body.put("timestamp", System.currentTimeMillis());
            body.put("abilityCode", "dsp.ad.service");
            body.put("format", "json");
            body.put("biz_content", bizContent);
            body.put("sign", sign(bizContent, jtcAppKey));
            String bodyStr = body.toString();

            String resp = HttpUtil.createPost(url)
                    .header("Content-Type", "application/json")
                    .body(bodyStr)
                    .execute().body();
            log.info("捷停车优惠券发放, slotId={}, req={}, resp={}", order.getSlotId(), bodyStr, resp);
            record.setCouponParam(bodyStr);
            record.setCouponResp(resp);

            JSONObject result = JSON.parseObject(resp);
            boolean isSuccess = null != result && Objects.equals(result.getInteger("code"), 10000);
            if (isSuccess) {
                redisCache.setCacheObject(redisKey, jtcAdRequestId, 7, TimeUnit.DAYS);
            } else {
                log.error("捷停车优惠券发放失败, slotId={}, req={}, resp={}", order.getSlotId(), bodyStr, resp);
            }
            return isSuccess;
        } catch (Exception e) {
            log.error("捷停车优惠券发放异常", e);
        }
        return false;
    }

    private String sign(JSONObject bizContent, String jtcAppKey) {
        String signStr = bizContent.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .map(s -> s.getKey() + "=" + s.getValue()).collect(Collectors.joining("&"));
        return getSM3Hash(signStr + "&key=" + jtcAppKey);
    }

    private static String getSM3Hash(String str) {
        SM3.Digest sm3 = new SM3.Digest();
        byte[] res = sm3.digest(str.getBytes(StandardCharsets.UTF_8));
        return Hex.toHexString(res);
    }
}
