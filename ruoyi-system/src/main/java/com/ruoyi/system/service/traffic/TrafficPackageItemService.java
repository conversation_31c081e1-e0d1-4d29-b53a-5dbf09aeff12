package com.ruoyi.system.service.traffic;

import com.ruoyi.system.entity.traffic.TrafficPackageItemEntity;

import java.util.List;
import java.util.Map;

/**
 * 流量包组成 Service
 *
 * <AUTHOR>
 * @date 2022-8-23 17:57:44
 */
public interface TrafficPackageItemService {

    /**
     * 更新或者新增
     *
     * @param trafficPackageId 流量包ID
     * @param slotIds 广告位ID列表
     * @return 影响行数
     */
    int updateOrInsert(Long trafficPackageId, List<Long> slotIds);

    /**
     * 查询流量包组成
     *
     * @param trafficPackageId 流量包ID
     * @return 流量包组成列表
     */
    List<TrafficPackageItemEntity> selectList(Long trafficPackageId);

    /**
     * 查询流量包广告位映射
     *
     * @param trafficPackageIds 流量包ID列表
     * @return 流量包ID-广告位ID列表映射
     */
    Map<Long, List<Long>> selectTrafficSlotMap(List<Long> trafficPackageIds);

    /**
     * 根据媒体ID查询流量包ID列表
     *
     * @param appIds 媒体ID列表
     * @return 流量包ID列表
     */
    List<Long> selectTrafficPackageIdsByAppIds(List<Long> appIds);

    /**
     * 查询所有的媒体ID列表
     *
     * @return 媒体ID列表
     */
    List<Long> selectTotalAppIds();
}
