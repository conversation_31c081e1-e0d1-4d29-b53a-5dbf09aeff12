package com.ruoyi.system.service.manager.impl;

import com.ruoyi.system.entity.advert.LayerSkin;
import com.ruoyi.system.mapper.manager.LayerSkinMapper;
import com.ruoyi.system.service.manager.LayerSkinService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 弹层皮肤Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-09-26
 */
@Service
public class LayerSkinServiceImpl implements LayerSkinService {

    @Autowired
    private LayerSkinMapper layerSkinMapper;

    /**
     * 查询弹层皮肤
     * 
     * @param id 弹层皮肤ID
     * @return 弹层皮肤
     */
    @Override
    public LayerSkin selectLayerSkinById(Long id) {
        return layerSkinMapper.selectLayerSkinById(id);
    }

    /**
     * 查询弹层皮肤列表
     * 
     * @param layerSkin 弹层皮肤
     * @return 弹层皮肤
     */
    @Override
    public List<LayerSkin> selectLayerSkinList(LayerSkin layerSkin) {
        return layerSkinMapper.selectLayerSkinList(layerSkin);
    }

    @Override
    public Map<String, String> selectSkinNameMap() {
        List<LayerSkin> list = layerSkinMapper.selectLayerSkinList(new LayerSkin());
        return list.stream().collect(Collectors.toMap(LayerSkin::getSkinCode, LayerSkin::getSkinName, (oldVal, newVal) -> newVal));
    }

    /**
     * 新增弹层皮肤
     * 
     * @param layerSkin 弹层皮肤
     * @return 结果
     */
    @Override
    public int insertLayerSkin(LayerSkin layerSkin) {
        return layerSkinMapper.insertLayerSkin(layerSkin);
    }

    /**
     * 修改弹层皮肤
     * 
     * @param layerSkin 弹层皮肤
     * @return 结果
     */
    @Override
    public int updateLayerSkin(LayerSkin layerSkin) {
        return layerSkinMapper.updateLayerSkin(layerSkin);
    }
}
