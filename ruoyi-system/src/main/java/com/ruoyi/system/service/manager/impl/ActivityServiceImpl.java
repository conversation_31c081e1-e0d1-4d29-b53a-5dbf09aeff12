package com.ruoyi.system.service.manager.impl;

import com.ruoyi.system.entity.activity.Activity;
import com.ruoyi.system.mapper.manager.ActivityMapper;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.manager.ActivityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 活动工具Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
@Slf4j
@Service
public class ActivityServiceImpl implements ActivityService {

    @Autowired
    private ActivityMapper activityMapper;

    @Autowired
    private RefreshCacheService refreshCacheService;

    /**
     * 查询活动工具
     *
     * @param id 活动工具ID
     * @return 活动工具
     */
    @Override
    public Activity selectActivityById(Long id) {
        return activityMapper.selectActivityById(id);
    }

    /**
     * 查询活动工具列表
     *
     * @param activity 活动工具
     * @return 活动工具
     */
    @Override
    public List<Activity> selectActivityList(Activity activity) {
        return activityMapper.selectActivityList(activity);
    }

    @Override
    public List<Activity> selectSimpleActivityList(Activity param) {
        return activityMapper.selectSimpleActivityList(param);
    }

    /**
     * 新增活动工具
     *
     * @param activity 活动工具
     * @return 结果
     */
    @Override
    public int insertActivity(Activity activity) {
        return activityMapper.insertActivity(activity);
    }

    /**
     * 修改活动工具
     *
     * @param activity 活动工具
     * @return 结果
     */
    @Override
    public int updateActivity(Activity activity) {
        int result = activityMapper.updateActivity(activity);
        if (result > 0) {
            refreshCacheService.sendRefreshActivityCacheMsg(activity.getId());
        }
        return result;
    }

    @Override
    public List<Activity> selectTotalOpenActivity() {
        return activityMapper.selectTotalOpenActivity();
    }

    @Override
    public Map<Long, String> selectActivityNameMap(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        Activity param = new Activity();
        param.setIds(ids);
        List<Activity> list = activityMapper.selectSimpleActivityList(param);
        return list.stream().collect(Collectors.toMap(Activity::getId, Activity::getActivityName, (o, n) -> n));
    }
}
