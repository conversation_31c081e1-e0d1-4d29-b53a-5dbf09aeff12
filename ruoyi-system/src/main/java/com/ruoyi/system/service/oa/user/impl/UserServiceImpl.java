package com.ruoyi.system.service.oa.user.impl;

import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.oa.user.UserEntity;
import com.ruoyi.system.mapper.oa.user.UserMapper;
import com.ruoyi.system.service.oa.user.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * sso账号表 Service
 *
 * <AUTHOR>
 * @date 2022-6-2 15:43:29
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public UserEntity selectByEmail(String email) {
        if(StringUtils.isBlank(email)){
            return null;
        }
        return userMapper.selectByEmail(email);
    }

    @Override
    public Long selectUserIdByEmail(String email) {
        return Optional.ofNullable(selectByEmail(email)).map(UserEntity::getId).orElse(null);
    }

    @Override
    public Map<String, Long> selectMapByEmails(List<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return Collections.emptyMap();
        }
        return userMapper.selectByEmails(emails).stream().collect(Collectors.toMap(UserEntity::getEmail, UserEntity::getId, (v1, v2) -> v2));
    }

    @Override
    public List<UserEntity> selectByLikeEmail(String email) {
        if(StringUtils.isBlank(email)){
            return Collections.emptyList();
        }
        return userMapper.selectByLikeEmail(email);
    }

    @Override
    public UserEntity selectById(Long id) {
        if(NumberUtils.isNullOrLteZero(id)){
            return null;
        }
        return userMapper.selectById(id);
    }
}
