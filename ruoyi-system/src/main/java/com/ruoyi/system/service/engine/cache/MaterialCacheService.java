package com.ruoyi.system.service.engine.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.advert.Material;
import com.ruoyi.system.mapper.manager.MaterialMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 广告素材缓存服务
 *
 * <AUTHOR>
 * @date 2022/06/28
 */
@Slf4j
@Service
public class MaterialCacheService {

    @Autowired
    private MaterialMapper materialMapper;

    /**
     * 素材名称缓存
     */
    private final LoadingCache<Long, String> MATERIAL_NAME_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(2000)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long materialId) {
                    Material material = materialMapper.selectMaterialById(materialId);
                    return null != material ? StringUtils.defaultString(material.getAdvertTitle()) : "";
                }

                @Override
                public ListenableFuture<String> reload(Long materialId, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(materialId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 查询广告素材名称缓存
     *
     * @param materialId 广告素材ID
     * @return 广告素材
     */
    public String selectMaterialNameCache(Long materialId) {
        if (null != materialId) {
            try {
                return MATERIAL_NAME_CACHE.get(materialId);
            } catch (ExecutionException e) {
                log.error("查询素材名称缓存异常, materialId={}", materialId, e);
            }
        }
        return "";
    }
}
