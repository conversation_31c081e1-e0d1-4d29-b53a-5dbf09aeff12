package com.ruoyi.system.service.oa.permission;

import com.ruoyi.system.entity.oa.permission.StaffPermissionEntity;

/**
 * 职员权限关联表 Service
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:19
 */
public interface StaffPermissionService {

    /**
     * 保存记录
     *
     * @param entity 记录
     * @return 结果
     */
    Boolean save(StaffPermissionEntity entity);

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    Boolean insert(StaffPermissionEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    Boolean updateById(StaffPermissionEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    StaffPermissionEntity selectById(Long id);

    /**
     * 获取职员对应系统的权限
     *
     * @param staffId 职员ID
     * @param systemId 系统ID
     * @return 权限
     */
    StaffPermissionEntity selectByStaffIdAndSystemId(Long staffId, Long systemId);
}
