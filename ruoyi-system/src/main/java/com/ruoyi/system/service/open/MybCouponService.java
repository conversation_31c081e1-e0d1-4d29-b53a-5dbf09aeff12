package com.ruoyi.system.service.open;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.domain.order.Order;

/**
 * 蚂蚁保投放后发放优惠券Service接口
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
public interface MybCouponService {

    /**
     * 根据不同产品发放对应优惠券
     *
     * @param slotParam 广告位参数
     * @param pageConfig 落地页配置
     * @return 是否发放成功
     */
    boolean grandCoupon(JSONObject slotParam, JSONObject pageConfig, Order order);
}
