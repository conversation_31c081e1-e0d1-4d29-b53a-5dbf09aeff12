package com.ruoyi.system.service.callback.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.enums.open.QimaoConfigEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.QIMAO;
import static com.ruoyi.common.enums.open.QimaoConfigEnum.getBySlotId;

/**
 * 七猫上报处理器
 * 配置: {@link com.ruoyi.common.enums.open.QimaoConfigEnum}
 *
 * <AUTHOR>
 * @date 2023/11/2
 */
@Slf4j
@Service
public class QimaoCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final String CONV_URL_TEMPLATE = "http://stats.ipinyou.com/mcvt?a={}&os={}&iam={}&dim={}&oaidm={}&aim={}&ts={}&gl={}&ck={}";

    @Override
    public CallbackProcessorTypeEnum getType() {
        return QIMAO;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("clickid")) && StringUtils.isBlank(param.getString("adid"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            JSONObject param = context.getParam().getSlotParam();

            String os = param.getString("os");
            String iam = param.getString("iam");
            if (StrUtil.containsAny(iam, "__", "[", "]")) {
                iam = "";
            }
            String dim = param.getString("dim");
            if (StrUtil.containsAny(dim, "__", "[", "]")) {
                dim = "";
            }
            String oaidm = param.getString("oaidm");
            if (StrUtil.containsAny(oaidm, "__", "[", "]")) {
                oaidm = "";
            }
            String aim = param.getString("aim");
            if (StrUtil.containsAny(aim, "__", "[", "]")) {
                aim = "";
            }
            String ck = param.getString("clickid");

            QimaoConfigEnum config = getBySlotId(context.getOrder().getSlotId());
            String url = StrUtil.format(CONV_URL_TEMPLATE, config.getA(), os, iam, dim, oaidm, aim, System.currentTimeMillis(), config.getGl(), ck);
            String resp = HttpUtil.createGet(url).execute().body();
            log.info("{}接口上报, url={}, resp={}", getType().getName(), url, resp);
            return true;
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 因为跟巨量参数冲突，暂时先排除
//        ConvCallbackService.register(this);
    }
}
