package com.ruoyi.system.service.open;

import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity;

/**
 * 捷停车对接Service接口
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
public interface JtcService {

    /**
     * 转化上报并发放优惠券
     *
     * @param order 订单
     * @param record 优惠券方法记录
     * @return 优惠券是否发放成功
     */
    Boolean convAndGrantCoupon(Order order, ExternalCouponRecordEntity record, String jtcAdRequestId, String jtcCoupons);
}
