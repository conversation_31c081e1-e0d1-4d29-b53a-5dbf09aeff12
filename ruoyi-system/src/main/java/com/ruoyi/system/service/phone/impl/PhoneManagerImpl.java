package com.ruoyi.system.service.phone.impl;

import com.ruoyi.system.req.phone.Md5PhoneReq;
import com.ruoyi.system.req.phone.Md5PhoneResultDto;
import com.ruoyi.system.req.phone.Md5PhoneResultListDto;
import com.ruoyi.system.service.phone.PhoneManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 号码服务
 * <AUTHOR>
 * @date 2023/8/17 13:38
 */
@Service
public class PhoneManagerImpl implements PhoneManager {


    @Value("${md5.phone.decode.url}")
    private String md5PhoneDecodeUrl;

    @Autowired
    private RestTemplate restTemplate;
    /**
     * 调服务解析md5手机号
     *
     * @param md5List
     * @return <md5,phone>
     */
    @Override
    public Map<String, String> decodeMd5Phone(List<String> md5List) {
        try{
            if (CollectionUtils.isEmpty(md5List)) {
                return Collections.emptyMap();
            }
            Md5PhoneReq req = new Md5PhoneReq();
            req.setMd5Phones(new ArrayList<>(md5List));
            ResponseEntity<Md5PhoneResultListDto> md5PhoneResultDtoResponseEntity = restTemplate.postForEntity(md5PhoneDecodeUrl + "/api/md5/phone", req, Md5PhoneResultListDto.class);
            Md5PhoneResultListDto body = md5PhoneResultDtoResponseEntity.getBody();
            if (Objects.nonNull(body) && Objects.equals(body.getCode(), 200)) {
                List<Md5PhoneResultDto> data = body.getData();
                return data.stream().collect(Collectors.toMap(Md5PhoneResultDto::getMd5Phone, Md5PhoneResultDto::getPhone, (v1, v2) -> v1));
            }
            return Collections.emptyMap();
        }catch (Exception e){
            return decodeMd5Phone(md5List);
        }
    }
}
