package com.ruoyi.system.service.billing.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.datashow.AdvertiserConsumeData;
import com.ruoyi.system.service.advertiser.AdvertiserBudgetService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.billing.BudgetService;
import com.ruoyi.system.entity.datashow.AdvertDayConsumeData;
import com.ruoyi.system.entity.datashow.OrientDayConsumeData;
import com.ruoyi.system.service.datasource.AdvertDayConsumeDataService;
import com.ruoyi.system.service.datasource.AdvertiserConsumeDataService;
import com.ruoyi.system.service.datasource.OrientDayConsumeDataService;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.ruoyi.common.enums.advert.AdvertStatusEnum.*;

/**
 * 预算服务实现
 *
 * <AUTHOR>
 * @date 2021/8/26
 */
@Service
public class BudgetServiceImpl implements BudgetService {

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private AdvertDayConsumeDataService advertDayConsumeDataService;

    @Autowired
    private OrientDayConsumeDataService orientDayConsumeDataService;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private AdvertiserBudgetService advertiserBudgetService;

    @Autowired
    private AdvertiserConsumeDataService advertiserConsumeDataService;

    @Override
    public void checkBudget(Long advertId) {
        Advert advert = advertService.selectAdvertById(advertId);
        if (null == advert) {
            return;
        }
        if (!isAdvertValid(advert.getAdvertStatus()) && !isBudgetRunOut(advert.getAdvertStatus())) {
            return;
        }

        // 默认为有效状态，只判断预算不足的情况，最终和原广告状态比对
        int advertStatus = NORMAL.getStatus();
        Date today = DateUtil.beginOfDay(new Date());
        int maxMilliUnitPrice = NumberUtils.defaultInt(advertOrientationService.selectMaxMilliUnitPriceByAdvertId(advertId));

        // 检查广告预算
        if (null != advert.getDailyBudget()) {
            if (advert.getDailyBudget() <= 0) {
                advertStatus = INVALID_WITHOUT_BUDGET.getStatus();
            } else {
                AdvertDayConsumeData advertData = advertDayConsumeDataService.selectByDateAndAdvertId(today, advertId);
                if (null != advertData && advertData.getConsume() * 100 + maxMilliUnitPrice >= advert.getDailyBudget() * 100L) {
                    advertStatus = INVALID_WITHOUT_BUDGET.getStatus();
                }
            }
            // 更新广告状态
            if (Objects.equals(advertStatus, INVALID_WITHOUT_BUDGET.getStatus())
                    && !Objects.equals(advert.getAdvertStatus(), advertStatus)) {
                advertService.updateAdvertStatus(advertId, advertStatus);
                return;
            }
        }

        // 检查配置预算
        if (isAdvertValid(advertStatus)) {
            List<AdvertOrientation> orients = advertOrientationService.selectListByAdvertId(advertId);
            // 只判断只有一个默认配置的情况，如果是多个配置，则通过投放时的广告过滤进行处理
            if (CollUtil.size(orients) == 1) {
                for (AdvertOrientation orient : orients) {
                    if (null == orient.getDailyBudget()) {
                        continue;
                    }
                    if (orient.getDailyBudget() <= 0) {
                        advertStatus = INVALID_WITHOUT_BUDGET.getStatus();
                        break;
                    }
                    OrientDayConsumeData orientData = orientDayConsumeDataService.selectByDateAndOrientId(today, orient.getId());
                    if (null != orientData) {
                        if (null != orient.getMilliUnitPrice() && orient.getMilliUnitPrice() % 100 > 0
                                && orientData.getConsume() * 100 + orient.getMilliUnitPrice() > orient.getDailyBudget() * 100L) {
                            advertStatus = INVALID_WITHOUT_BUDGET.getStatus();
                            break;
                        } else if (orientData.getConsume() + orient.getUnitPrice() > orient.getDailyBudget()) {
                            advertStatus = INVALID_WITHOUT_BUDGET.getStatus();
                            break;
                        }
                    }
                }
                // 更新广告状态
                if (Objects.equals(advertStatus, INVALID_WITHOUT_BUDGET.getStatus())
                        && !Objects.equals(advert.getAdvertStatus(), advertStatus)) {
                    advertService.updateAdvertStatus(advertId, advertStatus);
                    return;
                }
            }
        }

        // 检查广告主余额
        Integer balance = advertiserBalanceService.selectAdjustTotalAmountByAccountId(advert.getAdvertiserId());
        if (balance * 100L < maxMilliUnitPrice) {
            advertStatus = INVALID_WITHOUT_BALANCE.getStatus();

            // 更新广告状态
            if (!Objects.equals(advert.getAdvertStatus(), advertStatus)) {
                advertService.updateAdvertStatus(advertId, advertStatus);
                return;
            }
        }

        // 检查广告主日预算
        Long advertiserBudget = advertiserBudgetService.selectBudgetByAccountId(advert.getAdvertiserId());
        if (null != advertiserBudget) {
            if (advertiserBudget <= 0) {
                advertStatus = INVALID_WITHOUT_ADVERTISER_BUDGET.getStatus();
            } else {
                AdvertiserConsumeData advertiserConsumeData = advertiserConsumeDataService.selectByDateAndAdvertiserId(today, advert.getAdvertiserId());
                if (null != advertiserConsumeData && advertiserConsumeData.getConsume() * 100 + maxMilliUnitPrice > advertiserBudget * 100L) {
                    advertStatus = INVALID_WITHOUT_ADVERTISER_BUDGET.getStatus();
                }
            }
            // 更新广告状态
            if (Objects.equals(advertStatus, INVALID_WITHOUT_ADVERTISER_BUDGET.getStatus())
                    && !Objects.equals(advert.getAdvertStatus(), advertStatus)) {
                advertService.updateAdvertStatus(advertId, advertStatus);
                return;
            }
        }

        // 更新广告状态
        if (!Objects.equals(advert.getAdvertStatus(), advertStatus)) {
            advertService.updateAdvertStatus(advertId, advertStatus);
        }
    }
}
