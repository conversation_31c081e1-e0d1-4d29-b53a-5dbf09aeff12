package com.ruoyi.system.service.common.impl;

import com.ruoyi.common.enums.common.IdWorkerType;
import com.ruoyi.system.mapper.system.IdMakerMapper;
import com.ruoyi.system.service.common.IdWorkerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 发号器服务实现
 *
 * <AUTHOR>
 * @date 2021/7/3
 */
@Service
public class IdWorkerServiceImpl implements IdWorkerService {

    @Resource
    private DataSourceTransactionManager transactionManager;

    @Autowired
    private IdMakerMapper idMakerMapper;

    private final ConcurrentMap<String, Queue<Long>> bizIdSequenceQueue = new ConcurrentHashMap<>();

    @Override
    public Long getNextID(IdWorkerType type) {
        String bizType = type.getType();

        Queue<Long> idQueue = bizIdSequenceQueue.get(bizType);
        if (idQueue == null) {
            idQueue = new LinkedList<>();
            Queue<Long> temp = bizIdSequenceQueue.putIfAbsent(bizType, idQueue);
            if (temp != null) {
                idQueue = temp;
            }
        }
        synchronized (idQueue) {
            if (idQueue.isEmpty()) {
                int batchSize = getBatchSize(type);
                Long firstId = getMysqlID(bizType, batchSize);
                for (int i = 0; i < batchSize; i++) {
                    idQueue.add(firstId++);
                }
            }
            return idQueue.poll();
        }
    }

    @Override
    public Long getGroupIDBySize(IdWorkerType type, int batchSize) {
        return getMysqlID(type.getType(), batchSize);
    }

    private Long getMysqlID(String bizType, int batchSize) {
        DefaultTransactionDefinition dd = new DefaultTransactionDefinition();
        dd.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        TransactionStatus ts = transactionManager.getTransaction(dd);
        try {
            Long mysqlId = idMakerMapper.selectForUpdate(bizType);
            if (mysqlId == null) {
                mysqlId = 1L;
                long initNum = mysqlId + batchSize;
                idMakerMapper.insert(bizType, initNum);
                return mysqlId;
            }
            int updateRows = idMakerMapper.incrBy(bizType, batchSize);
            if (updateRows > 0) {
                return mysqlId;
            } else {
                throw new IllegalStateException("key:" + bizType + "does not exist!");
            }
        } catch (Exception e) {
            ts.setRollbackOnly();
            throw e;
        } finally {
            transactionManager.commit(ts);
        }
    }

    private int getBatchSize(IdWorkerType type) {
        switch (type) {
            case CONSUMER_ID:
                return 1000;
            case ORDER_ID:
                return 1000;
            default:
                return 10;
        }
    }
}
