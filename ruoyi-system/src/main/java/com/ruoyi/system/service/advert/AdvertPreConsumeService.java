package com.ruoyi.system.service.advert;

import com.ruoyi.system.entity.advert.AdvertPreConsumeEntity;

import java.util.Date;

/**
 * 广告消耗预扣表 Service
 *
 * <AUTHOR>
 * @date 2023-12-25 19:54:55
 */
public interface AdvertPreConsumeService {

    /**
     * 新增记录
     */
    Boolean insert(AdvertPreConsumeEntity entity);

    /**
     * 根据id获取
     */
    AdvertPreConsumeEntity selectById(Long id);

    /**
     * 根据日期和配置ID获取
     */
    AdvertPreConsumeEntity selectBy(Date date, Long orientId);

    /**
     * 预扣消耗
     *
     * @param id 数据ID
     * @param milliConsumeAdd 预扣消耗
     * @return 是否预扣成功
     */
    boolean preConsume(Long id, Integer milliConsumeAdd);
}
