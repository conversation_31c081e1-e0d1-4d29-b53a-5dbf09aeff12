package com.ruoyi.system.service.engine.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.AdvertClickKeyEnum;
import com.ruoyi.common.enums.DeviceUidEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UserAgentUtils;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.service.consumer.ConsumerService;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.DeviceUidService;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.service.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 设备唯一标识接口实现
 *
 * <AUTHOR>
 * @date 2023/06/08
 */
@Slf4j
@Service
public class DeviceUidServiceImpl implements DeviceUidService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ConsumerService consumerService;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private OrderService orderService;

    @Override
    public void cacheParameter(Map<String, String> paramMap, Long appId, String deviceId) {
        if (MapUtil.isEmpty(paramMap)) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            try {
                // 缓存广告点击监测标识和consumerId的映射
                Long consumerId = consumerService.getOrCreateConsumer(appId, deviceId);
                Arrays.stream(AdvertClickKeyEnum.values()).forEach(e -> {
                    if (StrUtil.isNotBlank(paramMap.get(e.getKey()))) {
                        redisCache.setCacheObject(EngineRedisKeyFactory.K075.join(e.getKey(), paramMap.get(e.getKey())), String.valueOf(consumerId), 1, TimeUnit.DAYS);
                    }
                });

                // 获取点击监测中缓存的设备信息
                JSONObject deviceUid = new JSONObject();
                Arrays.stream(AdvertClickKeyEnum.values()).forEach(e -> copyDeviceUid(paramMap, e.getKey(), deviceUid));

                // 完善设备信息JSON
                Arrays.stream(DeviceUidEnum.values()).forEach(e -> Optional.ofNullable(getUidMd5(paramMap, e.getKey())).ifPresent(uid -> deviceUid.put(e.getKey(), uid)));

                // 缓存consumerId和设备信息的映射
                redisCache.setCacheObject(EngineRedisKeyFactory.K072.join("cid", consumerId), deviceUid.toString(), 1, TimeUnit.DAYS);
            } catch (Exception e) {
                log.error("DeviceUidServiceImpl.cacheParameter error, appId={}, deviceId={}, paramMap={}", appId, deviceId, JSON.toJSONString(paramMap), e);
            }
        });
    }

    @Override
    public void cacheParameter(Map<String, String> paramMap) {
        if (MapUtil.isEmpty(paramMap)) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            try {
                // 获取点击监测中缓存的设备唯一标识
                JSONObject deviceUid = new JSONObject();
                Arrays.stream(AdvertClickKeyEnum.values()).forEach(e -> copyDeviceUid(paramMap, e.getKey(), deviceUid));

                // 完善设备信息JSON
                Arrays.stream(DeviceUidEnum.values()).forEach(e -> Optional.ofNullable(getUidMd5(paramMap, e.getKey())).ifPresent(uid -> deviceUid.put(e.getKey(), uid)));
                String value = deviceUid.toString();

                // 缓存广告点击监测标识和设备信息的映射
                Arrays.stream(AdvertClickKeyEnum.values()).forEach(e -> {
                    if (StrUtil.isNotBlank(paramMap.get(e.getKey()))) {
                        redisCache.setCacheObject(EngineRedisKeyFactory.K072.join(e.getKey(), paramMap.get(e.getKey())), value, 1, TimeUnit.DAYS);
                    }
                });

                // 缓存consumerId和设备信息的映射
                Optional.ofNullable(getConsumerIdByAdvertClickKey(paramMap)).ifPresent(consumerId -> redisCache.setCacheObject(EngineRedisKeyFactory.K072.join("cid", consumerId), value, 1, TimeUnit.DAYS));

                // 缓存广告主+设备号和订单号的映射
                Optional.ofNullable(getOrderIdByAdvertClickKey(paramMap)).ifPresent(orderId -> {
                    Order order = orderService.selectByOrderId(orderId);
                    if (null == order) {
                        return;
                    }
                    Long advertiserId = advertCacheService.queryAdvertiserId(order.getAdvertId());
                    if (null == advertiserId || advertiserId < 1L) {
                        return;
                    }
                    Arrays.stream(DeviceUidEnum.values()).forEach(e -> {
                        if (StrUtil.isNotBlank(deviceUid.getString(e.getKey()))) {
                            redisCache.setCacheObject(EngineRedisKeyFactory.K073.join(advertiserId, deviceUid.getString(e.getKey())), orderId, 1, TimeUnit.DAYS);
                        }
                    });
                });
            } catch (Exception e) {
                log.error("DeviceUidServiceImpl.cacheParameter error, paramMap={}", JSON.toJSONString(paramMap), e);
            }
        });
    }

    @Override
    public void cacheAdvertClickUid(Order order, JSONObject logJson) {
        try {
            String orderId = order.getOrderId();
            Long consumerId = order.getConsumerId();
            Long advertiserId = advertCacheService.queryAdvertiserId(order.getAdvertId());
            if (null == advertiserId || advertiserId < 1L) {
                return;
            }

            // 缓存IP+UA和orderId的映射
            String ipUaKey = genUidKeyByIpAndUa(logJson.getString("ip"),  logJson.getString("userAgent"));
            if (StrUtil.isNotBlank(ipUaKey)) {
                redisCache.setCacheObject(EngineRedisKeyFactory.K073.join(advertiserId, ipUaKey), orderId, 1, TimeUnit.DAYS);
            }

            // 缓存广告点击监测标识和orderId的映射
            JSONObject paramMap = callbackService.getParameterFromCache(orderId);
            Arrays.stream(AdvertClickKeyEnum.values()).forEach(e -> {
                if (StrUtil.isNotBlank(paramMap.getString(e.getKey()))) {
                    redisCache.setCacheObject(EngineRedisKeyFactory.K076.join(e.getKey(), paramMap.getString(e.getKey())), orderId, 1, TimeUnit.DAYS);
                }
            });

            // 根据用户ID获取设备信息
            String value = redisCache.getCacheObject(EngineRedisKeyFactory.K072.join("cid", consumerId));
            JSONObject deviceUid = JSON.parseObject(value);
            if (null == deviceUid) {
                return;
            }

            // 缓存广告主+设备号和订单号的映射
            Arrays.stream(DeviceUidEnum.values()).forEach(e -> {
                if (StrUtil.isNotBlank(deviceUid.getString(e.getKey()))) {
                    redisCache.setCacheObject(EngineRedisKeyFactory.K073.join(advertiserId, deviceUid.getString(e.getKey())), orderId, 1, TimeUnit.DAYS);
                }
            });
        } catch (Exception e) {
            log.error("DeviceUidServiceImpl.cacheAdvertClickUid error, orderId={}", order.getOrderId(), e);
        }
    }

    @Override
    public String matchOrderId(Long advertiserId, String ip, String userAgent, List<String> uids) {
        if (null == advertiserId || CollectionUtils.isEmpty(uids)) {
            return "";
        }
        // 通过设备唯一标识匹配订单号
        for (String uid : uids) {
            String key = EngineRedisKeyFactory.K073.join(advertiserId, handleCallbackUidMd5(uid));
            String orderId = redisCache.getCacheObject(key);
            if (StrUtil.isNotBlank(orderId)) {
                redisCache.expire(key, 3, TimeUnit.DAYS);
                return orderId;
            }
        }

        // 通过IP+UA匹配订单号
        String ipUaKey = genUidKeyByIpAndUa(ip, userAgent);
        if (StrUtil.isNotBlank(ipUaKey)) {
            String key = EngineRedisKeyFactory.K073.join(advertiserId, ipUaKey);
            String orderId = redisCache.getCacheObject(key);
            if (StringUtils.isNotBlank(orderId)) {
                redisCache.expire(key, 3, TimeUnit.DAYS);
                return orderId;
            }
        }
        return "";
    }

    @Override
    public String matchOrderId(Long advertiserId, String ip, String osType, String osVersion, String mobileModel) {
        if (null == advertiserId || StrUtil.isBlank(ip)) {
            return "";
        }
        // 通过IP+UA匹配订单号
        String ipUaKey = SecureUtil.md5(ip + mobileModel + osType + osVersion);
        if (StrUtil.isNotBlank(ipUaKey)) {
            String key = EngineRedisKeyFactory.K073.join(advertiserId, ipUaKey);
            String orderId = redisCache.getCacheObject(key);
            if (StringUtils.isNotBlank(orderId)) {
                redisCache.expire(key, 3, TimeUnit.DAYS);
                return orderId;
            }
        }
        return "";
    }

    /**
     * 根据IP和userAgent生成唯一标识
     */
    private String genUidKeyByIpAndUa(String ip, String userAgent) {
        if (StrUtil.isBlank(ip)) {
            return "";
        }
        // UserAgent解析
        UserAgentUtils.UserAgentDevice device = UserAgentUtils.analysisUserAgent(userAgent);
        if (null != device && StrUtil.isNotBlank(device.getModel())) {
            String osType = StringUtils.defaultString(device.getOsType());
            String osVersion = StringUtils.defaultString(device.getOsVersion());
            return SecureUtil.md5(ip + device.getModel() + osType + osVersion);
        }
        return "";
    }

    /**
     * 从参数获取设备唯一标识的MD5值
     * uid_o5=__OAID_MD5__&uid_m5=__IMEI__&uid_f5=__IDFA__
     *
     * @param uidKey 参数key
     * @return 设备唯一标识MD5
     */
    private String getUidMd5(Map<String, String> paramMap, String uidKey) {
        String value = paramMap.getOrDefault(uidKey, "");
        return StrUtil.isBlank(value) || value.contains("__") ? null : (value.length() == 32 ? value.toLowerCase() : SecureUtil.md5(value));
    }

    /**
     * 处理回传的回传设备唯一标识
     *
     * @param uid 设备唯一标识
     * @return 处理后的设备唯一标识
     */
    private String handleCallbackUidMd5(String uid) {
        return StrUtil.length(uid) == 32 ? uid.toLowerCase() : (StrUtil.isNotBlank(uid) ? SecureUtil.md5(uid) : "");
    }

    /**
     * 获取点击监测缓存的设备唯一标识
     *
     * @param paramKey 投放参数
     * @param deviceUid 设备唯一标识
     */
    private void copyDeviceUid(Map<String, String> paramMap, String paramKey, JSONObject deviceUid) {
        if (!paramMap.containsKey(paramKey)) {
            return;
        }
        String value = redisCache.getCacheObject(EngineRedisKeyFactory.K072.join(paramKey, paramMap.get(paramKey)));
        if (JSONUtil.isTypeJSON(value)) {
            BeanUtil.copyProperties(JSON.parseObject(value), deviceUid, CopyOptions.create().ignoreNullValue().ignoreError());
        }
    }

    /**
     * 根据广告点击监测标识获取consumerId
     *
     * @param paramMap 点击监测参数
     * @return consumerId
     */
    private Long getConsumerIdByAdvertClickKey(Map<String, String> paramMap) {
        String consumerIdStr = "";
        for (AdvertClickKeyEnum e : AdvertClickKeyEnum.values()) {
            if (!StrUtil.isNumeric(consumerIdStr) && StrUtil.isNotBlank(paramMap.get(e.getKey()))) {
                consumerIdStr = redisCache.getCacheObject(EngineRedisKeyFactory.K075.join(e.getKey(), paramMap.get(e.getKey())));
            }
        }
        return StrUtil.isNumeric(consumerIdStr) ? Long.valueOf(consumerIdStr) : null;
    }

    /**
     * 根据广告点击监测标识获取orderId
     *
     * @param paramMap 点击监测参数
     * @return orderId
     */
    private String getOrderIdByAdvertClickKey(Map<String, String> paramMap) {
        String orderId = null;
        for (AdvertClickKeyEnum e : AdvertClickKeyEnum.values()) {
            if (StrUtil.isBlank(orderId) && StrUtil.isNotBlank(paramMap.get(e.getKey()))) {
                orderId = redisCache.getCacheObject(EngineRedisKeyFactory.K076.join(e.getKey(), paramMap.get(e.getKey())));
            }
        }
        return orderId;
    }
}
