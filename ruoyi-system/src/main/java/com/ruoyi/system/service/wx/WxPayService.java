package com.ruoyi.system.service.wx;

import java.util.Map;

/**
 * 微信支付Service
 *
 * <AUTHOR>
 * @date 2023-3-21
 */
public interface WxPayService {

    /**
     * 获取微信公众号AccessToken
     *
     * @return accessToken
     */
    String getAccessToken();

    /**
     * 获取jsapi_ticket
     *
     * @return jsapi_ticket
     */
    String getJsapiTicket();

    /**
     * 获取微信支付JSAPI参数
     *
     * @param payAppId 支付的公众账号ID
     * @param prepayId 预支付交易会话标识
     * @return 微信支付JSAPI参数
     */
    Map<String, String> getJSParams(String payAppId, String prepayId);

    /**
     * 获取JSAPI配置参数
     *
     * @param url 链接
     * @return 配置参数
     */
    Map<String, String> getJSConfig(String url);

    /**
     * 通过用户授权code获取用户openid
     *
     * @param code 用户授权code
     * @return 用户openid
     */
    String getOpenid(String code);
}
