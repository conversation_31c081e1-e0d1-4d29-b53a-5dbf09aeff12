package com.ruoyi.system.service.engine.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.local.RequestThreadLocal;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.service.engine.ActivityJoinService;
import com.ruoyi.system.service.engine.cache.ActivityCacheService;
import com.ruoyi.system.vo.activity.ActivityVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 活动参与服务实现
 *
 * <AUTHOR>
 * @date 2021/9/26
 */
@Slf4j
@Service
public class ActivityJoinServiceImpl implements ActivityJoinService {

    @Autowired
    private ActivityCacheService activityCacheService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public boolean canJoin() {
        Long activityId = RequestThreadLocal.get().getActivityId();
        Long consumerId = RequestThreadLocal.get().getConsumerId();

        try {
            // 获取活动的参与次数
            ActivityVO activityVO = activityCacheService.getActivityCache(activityId);
            if (null == activityVO || null == activityVO.getJoinTimes()) {
                return false;
            }

            // 获取用户在活动的参与次数
            String key = Constants.ACTIVITY_JOIN_TIMES_KEY + DateUtil.today() + ":" + activityId;
            Integer times = redisCache.getCacheMapValue(key, String.valueOf(consumerId));
            // 校验是否还能参与
            return null == times || times < activityVO.getJoinTimes();
        } catch (Exception e) {
            log.error("校验是否活动是否能够参与异常, activityId={}, consumerId={}", activityId, consumerId, e);
        }
        return false;
    }

    @Override
    public void minusJoinTimes() {
        Long activityId = RequestThreadLocal.get().getActivityId();
        Long consumerId = RequestThreadLocal.get().getConsumerId();

        try {
            // 获取活动的参与次数
            ActivityVO activityVO = activityCacheService.getActivityCache(activityId);
            if (null == activityVO || null == activityVO.getJoinTimes()) {
                return;
            }

            // 获取用户在活动的剩余参与次数
            String key = Constants.ACTIVITY_JOIN_TIMES_KEY + DateUtil.today() + ":" + activityId;
            String userKey = String.valueOf(consumerId);
            Integer times = redisCache.getCacheMapValue(key, userKey);
            if (null == times) {
                times = activityVO.getJoinTimes();
            }
            if (times > 0) {
                redisCache.setCacheMapValue(key, userKey, times - 1);
                if (Objects.equals(times, activityVO.getJoinTimes())) {
                    redisCache.expire(key, 86400);
                }
            }
        } catch (Exception e) {
            log.error("活动参与次数减一异常, activityId={}, consumerId={}", activityId, consumerId, e);
        }
    }

    @Override
    public Integer getRemainJoinTimes(Long activityId) {
        Long consumerId = RequestThreadLocal.get().getConsumerId();
        return getRemainJoinTimes(activityId, consumerId);
    }

    @Override
    public Integer getRemainJoinTimes(Long activityId, Long consumerId) {
        if (null == activityId || null == consumerId) {
            return null;
        }
        try {
            // 获取用户在活动的剩余参与次数
            String key = Constants.ACTIVITY_JOIN_TIMES_KEY + DateUtil.today() + ":" + activityId;
            return redisCache.getCacheMapValue(key, String.valueOf(consumerId));
        } catch (Exception e) {
            log.error("活动已参与次数异常, activityId={}, consumerId={}", activityId, consumerId, e);
        }
        return null;
    }
}
