package com.ruoyi.system.service.engine;

/**
 * 活动参与服务接口
 *
 * <AUTHOR>
 * @date 2021/9/26
 */
public interface ActivityJoinService {

    /**
     * 是否能参与活动（校验参与次数）
     *
     * @return true.能,false.不能
     */
    boolean canJoin();

    /**
     * 参与次数减一
     */
    void minusJoinTimes();

    /**
     * 获取活动剩余参与次数
     *
     * @param activityId 活动ID
     * @return 活动剩余参与次数
     */
    Integer getRemainJoinTimes(Long activityId);

    /**
     * 获取活动剩余参与次数
     *
     * @param activityId 活动ID
     * @param consumerId 用户ID
     * @return 活动剩余参与次数
     */
    Integer getRemainJoinTimes(Long activityId, Long consumerId);
}
