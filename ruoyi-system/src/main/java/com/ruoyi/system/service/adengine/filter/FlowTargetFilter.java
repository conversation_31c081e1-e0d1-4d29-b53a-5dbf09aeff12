package com.ruoyi.system.service.adengine.filter;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.common.enums.advert.FlowTargetType;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.service.adengine.AdvertFilter;
import org.springframework.stereotype.Component;

/**
 * 流量定向过滤
 */
@Component
public class FlowTargetFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();

        // 不限流量类型
        if (null == ad.getFlowTarget() || 0 == ad.getFlowTarget()) {
            return true;
        }
        boolean isWechat = StrUtil.containsAnyIgnoreCase(context.getUserAgent(), "MicroMessenger", "miniProgram");
        boolean isAlipay = StrUtil.containsAnyIgnoreCase(context.getUserAgent(), "AliApp", "Alipay");
        // 微信环境判断
        return (isWechat && FlowTargetType.contains(ad.getFlowTarget(), FlowTargetType.WECHAT.getType()))
                // 支付宝环境判断
                || (isAlipay && FlowTargetType.contains(ad.getFlowTarget(), FlowTargetType.ALIPAY.getType()))
                // 其他环境判断
                || (!isWechat && !isAlipay && FlowTargetType.contains(ad.getFlowTarget(), FlowTargetType.OTHER.getType()));
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.FLOW_TARGET;
    }
}
