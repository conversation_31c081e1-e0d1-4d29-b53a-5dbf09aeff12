package com.ruoyi.system.service.billing.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.InnerLogUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.SlotBiddingConfigDto;
import com.ruoyi.system.domain.billing.BillingMessage;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.advert.AdvertPreConsumeEntity;
import com.ruoyi.system.entity.datashow.*;
import com.ruoyi.system.manager.advertiser.AdvertiserConsumeManager;
import com.ruoyi.system.message.rocketmq.producer.LogMqProducer;
import com.ruoyi.system.service.advert.AdvertPreConsumeService;
import com.ruoyi.system.service.billing.BillingService;
import com.ruoyi.system.service.datasource.*;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.service.engine.cache.AdvertiserCacheService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import static cn.hutool.core.util.BooleanUtil.isTrue;
import static com.ruoyi.common.enums.InnerLogType.ADVERTISER_BILLING_CONSUME;
import static com.ruoyi.common.enums.InnerLogType.ADVERT_BILLING;
import static com.ruoyi.common.enums.advert.AdvertCostBillingTypeEnum.CPC;
import static com.ruoyi.common.enums.advert.AdvertStatusEnum.INVALID_WITHOUT_ADVERTISER_BUDGET;
import static com.ruoyi.common.enums.advert.AdvertStatusEnum.INVALID_WITHOUT_BUDGET;

/**
 * 计费服务实现
 *
 * <AUTHOR>
 * @date 2021/8/23
 */
@Slf4j
@Service
public class BillingServiceImpl implements BillingService {

    @Autowired
    private OrderService orderService;

    @Autowired
    public TransactionTemplate transactionTemplate;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private AdvertDayConsumeDataService advertDayConsumeDataService;

    @Autowired
    private OrientDayConsumeDataService orientDayConsumeDataService;

    @Autowired
    private OrientHourConsumeDataService orientHourConsumeDataService;

    @Autowired
    private AdvertiserConsumeDataService advertiserConsumeDataService;

    @Autowired
    private AdvertiserConsumeManager advertiserConsumeManager;

    @Autowired
    private DataStatService dataStatService;

    @Autowired
    private LogMqProducer logMqProducer;

    @Autowired
    private AdvertiserCacheService advertiserCacheService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private AdvertPreConsumeService advertPreConsumeService;

    @Override
    public void billing(BillingMessage message) {
        if (null == message || StringUtils.isBlank(message.getOrderId())) {
            return;
        }

        // 查询订单
        String orderId = message.getOrderId();
        Order order = orderService.selectByOrderId(orderId);
        if (null == order) {
            return;
        }
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
        if (null == adSnapshot) {
            return;
        }
        if (null == adSnapshot.getUnitPrice() || adSnapshot.getUnitPrice() < 0) {
            log.error("计费失败，无效的计费单价。orderId={}, unitPrice={}", orderId, adSnapshot.getUnitPrice());
            return;
        }
        if (null == order.getSlotId()) {
            log.error("计费失败，无效的广告位ID。orderId={}, order={}", orderId, JSON.toJSONString(order));
            return;
        }
        Long orientId = order.getOrientId();
        Long advertId = order.getAdvertId();

        // 分布式锁限制订单5分钟内计费一次
        RedisLock lock = redisAtomicClient.getLock(Constants.BILLING_REPEAT_KEY + orderId,
                300, 3, 200);
        if (lock == null) {
            log.error("计费失败，五分钟内重复计费。orderId={}", orderId);
            return;
        }

        // 发送计费消息的时间
        Date time = DateUtil.date(NumberUtils.defaultLong(message.getTimestamp(), System.currentTimeMillis()));
        Date date = DateUtil.beginOfDay(time);
        final int hour = DateUtil.hour(time, true);

        // 获取计费单价
        final Integer unitPrice = getUnitPrice(order, adSnapshot, date);

        try {
            // 查询广告缓存(不可进行修改)
            AdvertCacheDto advertCacheDto = queryAdvertCache(order.getOrientId());
            String advertName = advertCacheDto.getAdvertName();
            Long advertiserId = advertCacheDto.getAdvertiserId();

            // 广告预算
            final Integer advertBudget = advertCacheDto.getAdvertBudget();
            // advertBudget 为空表示广告无预算上限
            if (null != advertBudget && advertBudget < unitPrice) {
                stopAdvertServing(advertId, advertName);
                return;
            }

            // 统计消耗
            // 初始化广告消耗数据
            AdvertDayConsumeData advertData = advertDayConsumeDataService.selectByDateAndAdvertId(date, advertId);
            if (null == advertData) {
                advertData = new AdvertDayConsumeData();
                advertData.setCurDate(date);
                advertData.setAdvertiserId(advertiserId);
                advertData.setAdvertId(advertId);
                advertDayConsumeDataService.insertAdvertDayConsumeData(advertData);
                advertData = advertDayConsumeDataService.selectByDateAndAdvertId(date, advertId);
            } else {
                // 无广告预算，暂停广告且本次不计费
                if (null != advertBudget && advertData.getConsume() + unitPrice > advertBudget) {
                    stopAdvertServing(advertId, advertName);
                    return;
                }
                // 接近无广告预算，暂停广告但本次计费
                if (null != advertBudget && unitPrice > 0 && advertData.getConsume() + unitPrice * 2 >= advertBudget) {
                    stopAdvertServing(advertId, advertName);
                }
            }

            // 初始化广告配置消耗数据
            OrientDayConsumeData orientData = orientDayConsumeDataService.selectByDateAndOrientId(date, orientId);
            if (null == orientData) {
                orientData = new OrientDayConsumeData();
                orientData.setCurDate(date);
                orientData.setAdvertId(advertId);
                orientData.setOrientId(orientId);
                orientDayConsumeDataService.insertOrientDayConsumeData(orientData);
                orientData = orientDayConsumeDataService.selectByDateAndOrientId(date, orientId);
            }
            // 配置预算
            final Integer orientBudget = advertCacheDto.getOrientBudget();
            // orientBudget 为空表示配置无预算上限
            if (null != orientBudget && orientData.getConsume() + unitPrice > orientBudget) {
                log.info("广告配置无预算，不计费, advertId={}, orientId={}", advertId, orientId);
                stopAdvertOrientServing(advertId, advertName, orientId, advertCacheDto.getOrientName());
                return;
            }

            // 查询广告主预算
            Long advertiserBudget = advertiserCacheService.getAdvertiserBudget(advertiserId);
            // 初始化广告主消耗数据
            AdvertiserConsumeData advertiserData = advertiserConsumeDataService.selectByDateAndAdvertiserId(date, advertiserId);
            if (null == advertiserData) {
                advertiserData = new AdvertiserConsumeData();
                advertiserData.setCurDate(date);
                advertiserData.setAdvertiserId(advertiserId);
                advertiserData.setBudget(advertiserBudget);
                advertiserConsumeDataService.insertAdvertiserConsumeData(advertiserData);
                advertiserData = advertiserConsumeDataService.selectByDateAndAdvertiserId(date, advertiserId);
            }
            // 广告主预算不足停止广告投放
            if (null != advertiserBudget && advertiserData.getConsume() + unitPrice > advertiserBudget) {
                stopAdvertiserServing(advertiserId, advertId, advertiserBudget);
                return;
            }

            // 构造日志参数
            JSONObject logJson = new JSONObject();
            logJson.put("orderId", orderId);
            logJson.put("slotId", order.getSlotId());
            logJson.put("appId", order.getAppId());
            logJson.put("consumerId", order.getConsumerId());
            logJson.put("activityId", order.getActivityId());
            logJson.put("srid", adSnapshot.getSrid());
            logJson.put("ip", adSnapshot.getIp());
            logJson.put("chargeType", adSnapshot.getChargeType());
            logJson.put("unitPrice", unitPrice);
            logJson.put("orientId", orientId);
            logJson.put("advertId", advertId);
            logJson.put("materialId", order.getMaterialId());

            // 更新数据
            final Long updateAdvertDataId = advertData.getId();
            final Long updateOrientDataId = orientData.getId();
            final Long updateOrientHourDataId = getOrInitOrientHourDataId(date, hour, advertId, orientId);
            final Long updateAdvertiserDataId = advertiserData.getId();
            Boolean success = transactionTemplate.execute(status -> {
                int result;

                try {
                    // 更新广告主消耗数据
                    AdvertiserConsumeData updateAdvertiserData = new AdvertiserConsumeData();
                    updateAdvertiserData.setId(updateAdvertiserDataId);
                    updateAdvertiserData.setConsumeAdd(unitPrice);
                    result = advertiserConsumeDataService.addConsumeData(updateAdvertiserData);
                    if (result == 0) {
                        status.setRollbackOnly();
                        return false;
                    }

                    // 更新广告消耗数据
                    AdvertDayConsumeData updateAdvertData = new AdvertDayConsumeData();
                    updateAdvertData.setId(updateAdvertDataId);
                    updateAdvertData.setConsumeAdd(unitPrice);
                    updateAdvertData.setDailyBudget(advertBudget);
                    result = advertDayConsumeDataService.addConsumeData(updateAdvertData);
                    if (result == 0) {
                        status.setRollbackOnly();
                        return false;
                    }

                    // 更新配置消耗数据
                    OrientDayConsumeData updateOrientData = new OrientDayConsumeData();
                    updateOrientData.setId(updateOrientDataId);
                    updateOrientData.setConsumeAdd(unitPrice);
                    updateOrientData.setDailyBudget(orientBudget);
                    result = orientDayConsumeDataService.addConsumeData(updateOrientData);
                    if (result == 0) {
                        status.setRollbackOnly();
                        return false;
                    }
                    // 更新配置分时段消耗数据
                    OrientHourConsumeData updateOrientHourData = new OrientHourConsumeData();
                    updateOrientHourData.setId(updateOrientHourDataId);
                    updateOrientHourData.setConsumeAdd(unitPrice);
                    result = orientHourConsumeDataService.addConsumeData(updateOrientHourData);
                    if (result == 0) {
                        status.setRollbackOnly();
                        return false;
                    }

                    return true;
                } catch (Exception e) {
                    log.error("计费更新数据异常，advertId={}, orderId={}", advertId,  orderId, e);
                    return false;
                }
            });

            if (!isTrue(success)) {
                log.error("计费失败，更新消耗数据异常，orderId={}, advertId={}, orientId={}", orderId, advertId, orientId);
                stopAdvertServingByError(advertId);
                lock.unlock();
                return;
            }

            // 打印日志
            InnerLogUtils.log(ADVERT_BILLING, logJson);
            logMqProducer.sendMsg(ADVERT_BILLING, logJson);

            // 更新消耗数据
            dataStatService.handleAsync(ADVERT_BILLING, logJson);

            // CPC结算广告主的CPC消费
            // 注:暂时先不放到一个事务里，后面再优化
            if (advertiserCacheService.isCpcAdvertiser(advertiserId)) {
                boolean result = advertiserConsumeManager.cpcConsume(date, advertiserId, advertId, unitPrice);
                if (result) {
                    logJson.put("billingType", CPC.getType());
                    InnerLogUtils.log(ADVERTISER_BILLING_CONSUME, logJson);
                    logMqProducer.sendMsg(ADVERTISER_BILLING_CONSUME, logJson);
                } else if (unitPrice > 0) {
                    log.error("cpcConsume失败, orderId={}, advertiserId={}, advertId={}, unitPrice={}", orderId, advertiserId, advertId, unitPrice);
                }
            }
        } catch (Exception e) {
            log.error("计费异常，orderId={}", orderId, e);
            lock.unlock();
        }
    }

    /**
     * 广告无预算，关闭广告投放
     */
    private void stopAdvertServing(Long advertId, String advertName) {
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K041.join(advertId), 60);
        if (null == lock) {
            return;
        }
        advertService.updateAdvertStatus(advertId, INVALID_WITHOUT_BUDGET.getStatus());
        log.info("广告无预算，关闭广告投放, advertId={}", advertId);

        GlobalThreadPool.executorService.submit(() -> {
            String sb = "广告无预算，停止投放\n" +
                    "\n广告ID: " + advertId +
                    "\n广告名称: " + advertName;
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb);
        });
    }

    /**
     * 广告扣费异常，停止投放并检查预算
     */
    private void stopAdvertServingByError(Long advertId) {
        // 停止投放
        advertService.updateAdvertStatus(advertId, INVALID_WITHOUT_BUDGET.getStatus());
        log.info("广告扣费异常，关闭广告投放, advertId={}", advertId);
        // 检查预算
        advertService.sendCheckBudgetMsg(advertId);
    }

    /**
     * 配置无预算提醒
     */
    private void stopAdvertOrientServing(Long advertId, String advertName, Long orientId, String orientName) {
        GlobalThreadPool.executorService.submit(() -> {
            RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K041.join(advertId, orientId), 60);
            if (null == lock) {
                return;
            }
            String sb = "广告配置无预算，停止投放\n" +
                    "\n广告ID: " + advertId +
                    "\n广告名称: " + advertName +
                    "\n配置ID: " + orientId +
                    "\n配置名称: " + orientName;
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb);
        });
    }

    /**
     * 广告主无预算，关闭广告投放
     */
    private void stopAdvertiserServing(Long advertiserId, Long advertId, Long budget) {
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K060.join(advertId), 60);
        if (null == lock) {
            return;
        }

        advertService.updateAdvertStatus(advertId, INVALID_WITHOUT_ADVERTISER_BUDGET.getStatus());
        log.info("广告主无预算，关闭广告投放, advertiserId={}, advertId={}, budget={}", advertiserId, advertId, budget);

        // 发送消息检查该广告主下其他广告
        advertService.sendCheckBudgetMsgByAdvertiserId(advertiserId);

        GlobalThreadPool.executorService.submit(() -> {
            String sb = "广告主无预算，停止投放\n" +
                    "\n广告主ID: " + advertiserId +
                    "\n广告主名称: " + advertiserCacheService.getAdvertiserName(advertiserId) +
                    "\n广告ID: " + advertId +
                    "\n广告名称: " + advertService.selectAdvertNameById(advertId);
            DingRobotUtil.sendText(DingWebhookConfig.getBalanceAlert(), sb);
        });
    }

    /**
     * 查询或初始化广告配置分时段消耗数据ID
     *
     * @param date 日期
     * @param hour 时段
     * @param orientId 配置ID
     * @return 数据ID
     */
    private Long getOrInitOrientHourDataId(Date date, Integer hour, Long advertId, Long orientId) {
        String key = EngineRedisKeyFactory.K081.join(DateUtil.formatDate(date), hour, orientId);
        Long orientHourDataId = redisCache.getCacheObject(key);
        if (null != orientHourDataId) {
            return orientHourDataId;
        }

        OrientHourConsumeData orientHourData = orientHourConsumeDataService.selectByDateHourAndOrientId(date, hour, orientId);
        if (null == orientHourData) {
            // 初始化广告配置分时段消耗数据
            orientHourData = new OrientHourConsumeData();
            orientHourData.setCurDate(date);
            orientHourData.setCurHour(hour);
            orientHourData.setAdvertId(advertId);
            orientHourData.setOrientId(orientId);
            orientHourConsumeDataService.insertOrientHourConsumeData(orientHourData);
            orientHourData = orientHourConsumeDataService.selectByDateHourAndOrientId(date, hour, orientId);
        }
        redisCache.setCacheObject(key, orientHourData.getId(), 1, TimeUnit.HOURS);
        return orientHourData.getId();
    }

    /**
     * 固定收益上报2.0
     * 注:
     *  如果理论消耗 - 实际消耗 > 100元，则unitPrice = 广告考核成本
     *  如果实际消耗 - 理论消耗 > 100元，则unitPrice = 0.01元
     *
     * @param orderId 订单号
     * @param unitPrice 原单价
     * @param slotId 广告位ID
     * @param advertId 广告ID
     * @param date 日期
     * @param assessCost 广告考核成本
     * @return 优化后的计费单价
     */
    private Integer optimizeUnitPrice(String orderId, Integer unitPrice, Long slotId, Long advertId, Date date, Integer assessCost) {
        try {
            // 判断是否属于固定收益上报广告位
            SlotBiddingConfigDto biddingConfig = slotCacheService.getSlotBiddingConfigCache(slotId);
            if (biddingConfig.isConfigDisable()) {
                return unitPrice;
            }

            // 账户标识
            String hu = callbackService.getParameterFromCache(orderId).getString("hu");

            // 计算单价
            String dateStr = DateUtil.formatDate(date);
            long eConsume;
            long actConsume;
            if (StringUtils.isNotBlank(hu)) {
                eConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K079.join(dateStr, slotId, advertId, hu)));
                actConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K085.join(dateStr, slotId, advertId, hu)));
            } else {
                eConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K079.join(dateStr, slotId, advertId)));
                actConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K085.join(dateStr, slotId, advertId)));
            }
            if (null != assessCost && eConsume - actConsume > 10000) {
                return assessCost;
            } else if (actConsume - eConsume > 10000) {
                return 1;
            }
        } catch (Exception e) {
            log.error("optimizeUnitPrice error, unitPrice={}, slotId={}, advertId={}, assessCost={}", unitPrice, slotId, advertId, assessCost, e);
        }
        return unitPrice;
    }

    /**
     * 查不到广告缓存的情况下，手动构建广告缓存对象
     */
    private AdvertCacheDto queryAdvertCache(Long orientId) {
        AdvertCacheDto advertCacheDto = advertCacheService.queryAdvert(orientId);
        if (null != advertCacheDto) {
            return advertCacheDto;
        }

        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(orientId);
        Advert advert = advertService.selectAdvertById(orient.getAdvertId());

        advertCacheDto = new AdvertCacheDto();
        advertCacheDto.setOrientId(orientId);
        advertCacheDto.setOrientName(orient.getOrientName());
        advertCacheDto.setAdvertId(advert.getId());
        advertCacheDto.setAdvertName(advert.getAdvertName());
        advertCacheDto.setAdvertBudget(advert.getDailyBudget());
        advertCacheDto.setAdvertiserId(advert.getAdvertiserId());

        return advertCacheDto;
    }

    /**
     * 获取计费单价
     * 注:会存在膨胀之后不够剩余预算无法扣费的情况
     */
    private Integer getUnitPrice(Order order, AdSnapshot adSnapshot, Date date) {
        try {
            if (null == adSnapshot.getMilliUnitPrice() || adSnapshot.getMilliUnitPrice() % 100 == 0) {
                return adSnapshot.getUnitPrice();
            }
            int milli = adSnapshot.getMilliUnitPrice() % 100;
            Long preConsumeId = getOrInitAdvertPreConsumeId(date, order.getOrientId());
            // 补偿之间的预扣消耗
            if (advertPreConsumeService.preConsume(preConsumeId, -milli)) {
                return adSnapshot.getUnitPrice();
            }
            // 补偿失败，预扣消耗(厘膨胀10倍，毫100倍)
            int multiple = milli % 10 > 0 ? 100 : 10;
            if (advertPreConsumeService.preConsume(preConsumeId, milli * (multiple - 1))) {
                return adSnapshot.getUnitPrice() + milli * multiple / 100;
            }
            // 预扣失败，破罐破摔
            advertPreConsumeService.preConsume(preConsumeId, milli);
            return adSnapshot.getUnitPrice();

            // 固定收益上报优化价格
            // 有潜在bug，先去掉
            // 1.如果用考核成本扣费，但在广告预算不限配置预算不足的情况下，考核成本大于剩余预算可能会导致扣费失败但是还是发券
            // 2.广告计费支持4位小数后，与该逻辑冲突
//        return optimizeUnitPrice(order.getOrderId(), unitPrice, order.getSlotId(), order.getAdvertId(), date, adSnapshot.getAssessCost());
        } catch (Exception e) {
            log.error("广告获取计费单价异常, orderId={}, orientId={}, milliUnitPrice={}", order.getOrderId(), order.getOrientId(), adSnapshot.getMilliUnitPrice(), e);
        }
        return adSnapshot.getUnitPrice();
    }

    /**
     * 查询或初始化广告预扣消耗ID
     *
     * @param date 日期
     * @param orientId 配置ID
     * @return 数据ID
     */
    private Long getOrInitAdvertPreConsumeId(Date date, Long orientId) {
        String key = EngineRedisKeyFactory.K022.join("AdvertPreConsumeEntity", DateUtil.formatDate(date), orientId);
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        AdvertPreConsumeEntity data = advertPreConsumeService.selectBy(date, orientId);
        if (null == data) {
            data = new AdvertPreConsumeEntity();
            data.setCurDate(date);
            data.setOrientId(orientId);
            advertPreConsumeService.insert(data);
            data = advertPreConsumeService.selectBy(date, orientId);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
        return data.getId();
    }
}
