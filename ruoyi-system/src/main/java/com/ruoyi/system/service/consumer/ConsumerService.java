package com.ruoyi.system.service.consumer;

import com.ruoyi.system.domain.consumer.Consumer;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @date 2021/8/17
 */
public interface ConsumerService {

    /**
     * 根据consumerId查询用户
     *
     * @param consumerId 用户ID
     * @return 用户
     */
    Consumer selectByConsumerId(Long consumerId);

    /**
     * 根据appId和设备号查询用户
     *
     * @param appId 媒体ID
     * @param deviceId 设备号
     * @return 用户
     */
    Consumer selectByAppAndDevice(Long appId, String deviceId);

    /**
     * 查询或者创建用户
     *
     * @param appId 媒体Id
     * @param deviceId 设备号
     * @return 用户ID
     */
    Long getOrCreateConsumer(Long appId, String deviceId);
}
