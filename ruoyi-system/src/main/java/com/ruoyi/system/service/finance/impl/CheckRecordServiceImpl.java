package com.ruoyi.system.service.finance.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.finance.CheckRecordService;
import com.ruoyi.system.entity.checkrecord.CheckRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import com.ruoyi.system.mapper.finance.CheckRecordMapper;

/**
* 提现记录 Service
* <AUTHOR>
* @date 2021-9-9 16:58:02
*/
@Service
public class CheckRecordServiceImpl implements CheckRecordService{

    @Autowired
    private CheckRecordMapper checkRecordMapper;

    @Override
	public Boolean save(CheckRecordEntity entity){
        if (Objects.isNull(entity)) {
            return false;
        }
        if (null == entity.getId()) {
            return checkRecordMapper.insert(entity) > 0;
        }
        return checkRecordMapper.update(entity) > 0;
    }

    @Override
    public CheckRecordEntity selectByWithdrawId(Long withdrawId) {
        if (null == withdrawId) {
            return null;
        }
        return checkRecordMapper.selectByWithdrawId(withdrawId);
    }

    @Override
    public List<CheckRecordEntity> selectListByWithdrawIds(List<Long> withdrawIds) {
        if(CollectionUtils.isEmpty(withdrawIds)){
            return Collections.emptyList();
        }

        return checkRecordMapper.selectListByWithdrawIds(withdrawIds);
    }
}
