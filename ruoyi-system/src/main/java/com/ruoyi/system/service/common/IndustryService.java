package com.ruoyi.system.service.common;

import com.ruoyi.system.entity.common.IndustryEntity;

import java.util.List;
import java.util.Map;

/**
 * 行业管理表 Service
 *
 * <AUTHOR>
 * @date 2023-3-6 17:34:38
 */
public interface IndustryService {

    /**
     * 查询列表
     */
    List<IndustryEntity> selectList(IndustryEntity param);

    /**
     * 查询启用的行业列表
     */
    List<IndustryEntity> selectEnableList();

    /**
     * 查询行业名称映射
     */
    Map<Long, String> selectIndustryNameMap(List<Long> ids);

    /**
     * 新增记录
     */
    int insert(IndustryEntity entity);

    /**
     * 根据id更新
     */
    int updateById(IndustryEntity entity);

    /**
     * 根据id获取
     */
    IndustryEntity selectById(Long id);

    /**
     * 是否存在相同的行业名称
     *
     * @param industryName 行业名称
     * @return 是否存在
     */
    boolean isIndustryNameExist(String industryName);

    /**
     * 获取相似的行业名称
     *
     * @param industryName 行业名称
     * @return 相似的行业名称列表
     */
    List<String> getSimilarName(String industryName);
}
