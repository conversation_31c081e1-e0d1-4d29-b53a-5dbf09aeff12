package com.ruoyi.system.service.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.advert.InnovateLayer;
import com.ruoyi.system.entity.advert.InnovateLayerInfo;
import com.ruoyi.system.entity.advert.LayerSkin;
import com.ruoyi.system.mapper.manager.InnovateLayerMapper;
import com.ruoyi.system.mapper.manager.LayerSkinMapper;
import com.ruoyi.system.req.advert.InnovateLayerReq;
import com.ruoyi.system.service.manager.InnovateLayerService;
import com.ruoyi.system.vo.advert.InnovateLayerVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 创新弹层Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
@Service
public class InnovateLayerServiceImpl implements InnovateLayerService {

    @Autowired
    private InnovateLayerMapper innovateLayerMapper;

    @Autowired
    private LayerSkinMapper layerSkinMapper;

    /**
     * 查询创新弹层
     *
     * @param id 创新弹层ID
     * @return 创新弹层
     */
    @Override
    public InnovateLayer selectInnovateLayerById(Long id) {
        if (null == id) {
            return null;
        }
        return innovateLayerMapper.selectInnovateLayerById(id);
    }

    @Override
    public InnovateLayerVO selectInnovateVOLayerById(Long id) {
        InnovateLayer layer = selectInnovateLayerById(id);
        if (null == layer) {
            return null;
        }
        Map<String, LayerSkin> map = selectLayerSkinMap(Collections.singletonList(layer.getSkinCode()));
        return convertTo(layer, map);
    }

    /**
     * 查询创新弹层列表
     *
     * @param req 请求参数
     * @return 创新弹层
     */
    @Override
    public List<InnovateLayer> selectInnovateLayerList(InnovateLayerReq req) {
        InnovateLayer param = new InnovateLayer();
        param.setId(req.getId());
        param.setLayerName(req.getLayerName());
        return innovateLayerMapper.selectInnovateLayerList(param);
    }

    @Override
    public Map<Long, InnovateLayerVO> selectInnovateLayerMap() {
        List<InnovateLayer> layerList = innovateLayerMapper.selectInnovateLayerList(new InnovateLayer());
        if (CollectionUtils.isEmpty(layerList)) {
            return Collections.emptyMap();
        }
        Map<String, LayerSkin> map = selectLayerSkinMap(layerList.stream().map(InnovateLayer::getSkinCode).collect(Collectors.toList()));
        return layerList.stream().collect(Collectors.toMap(InnovateLayer::getId, layer -> convertTo(layer, map), (o, n) -> n));
    }

    /**
     * 新增创新弹层
     *
     * @param req 请求参数
     * @return 结果
     */
    @Override
    public int insertInnovateLayer(InnovateLayerReq req) {
        LayerSkin skin = layerSkinMapper.selectBySkinCode(req.getSkinCode());
        if (null == skin) {
            throw new CustomException("无效的弹层皮肤");
        }

        LoginUser user = SecurityUtils.getLoginUser();

        InnovateLayer param = BeanUtil.copyProperties(req, InnovateLayer.class);
        param.setOperatorId(user.getCrmAccountId());
        param.setOperatorName(user.getUserName());
        if (null != req.getLayerInfo()) {
            param.setLayerInfo(JSON.toJSONString(req.getLayerInfo()));
        }
        return innovateLayerMapper.insertInnovateLayer(param);
    }

    /**
     * 修改创新弹层
     *
     * @param req 请求参数
     * @return 结果
     */
    @Override
    public int updateInnovateLayer(InnovateLayerReq req) {
        InnovateLayer layer = innovateLayerMapper.selectInnovateLayerById(req.getId());
        if (null == layer) {
            throw new CustomException("无效的弹层ID");
        }
        LayerSkin skin = layerSkinMapper.selectBySkinCode(req.getSkinCode());
        if (null == skin) {
            throw new CustomException("无效的弹层皮肤");
        }

        LoginUser user = SecurityUtils.getLoginUser();

        InnovateLayer param = BeanUtil.copyProperties(req, InnovateLayer.class);
        param.setOperatorId(user.getCrmAccountId());
        param.setOperatorName(user.getUserName());
        if (null != req.getLayerInfo()) {
            param.setLayerInfo(JSON.toJSONString(req.getLayerInfo()));
        }
        return innovateLayerMapper.updateInnovateLayer(param);
    }

    @Override
    public int deleteInnovateLayer(Long id) {
        InnovateLayer layer = new InnovateLayer();
        layer.setId(id);
        layer.setIsDeleted(1);
        return innovateLayerMapper.updateInnovateLayer(layer);
    }

    /**
     * convert InnovateLayer to InnovateLayerVO
     */
    private InnovateLayerVO convertTo(InnovateLayer layer, Map<String, LayerSkin> skinMap) {
        if (null == layer) {
            return null;
        }

        InnovateLayerVO layerVO = BeanUtil.copyProperties(layer, InnovateLayerVO.class, "layerInfo");
        if (StringUtils.isNotBlank(layer.getLayerInfo())) {
            layerVO.setLayerInfo(JSON.parseObject(layer.getLayerInfo(), InnovateLayerInfo.class));
        }
        if (MapUtils.isNotEmpty(skinMap)) {
            LayerSkin skin = skinMap.getOrDefault(layer.getSkinCode(), new LayerSkin());
            layerVO.setSkinType(skin.getSkinType());
        }
        return layerVO;
    }

    private Map<String, LayerSkin> selectLayerSkinMap(List<String> skinCodeList) {
        if (CollectionUtils.isEmpty(skinCodeList)) {
            return Collections.emptyMap();
        }

        List<LayerSkin> list = layerSkinMapper.selectBySkinCodeList(skinCodeList);
        return list.stream().collect(Collectors.toMap(LayerSkin::getSkinCode, Function.identity(), (oldVal, newVal) -> newVal));
    }
}
