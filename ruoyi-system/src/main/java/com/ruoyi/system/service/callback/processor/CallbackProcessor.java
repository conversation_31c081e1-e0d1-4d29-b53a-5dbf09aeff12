package com.ruoyi.system.service.callback.processor;

import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.system.domain.callback.ConvCallbackContext;

/**
 * 转化上报处理器接口
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
public interface CallbackProcessor {

    /**
     * 获取处理器类型
     *
     * @return 处理器类型
     */
    CallbackProcessorTypeEnum getType();

    /**
     * 参数校验
     *
     * @param context 上下文
     * @return true.校验通过,false.校验不通过
     */
    default boolean validate(ConvCallbackContext context) {
        return true;
    }

    /**
     * 转化上报
     *
     * @param context 上下文
     * @return 是否上报成功
     */
    boolean process(ConvCallbackContext context);
}
