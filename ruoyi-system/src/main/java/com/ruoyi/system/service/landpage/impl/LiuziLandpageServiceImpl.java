package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.InnerLogUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord;
import com.ruoyi.system.manager.liuzi.AsyncAlipayLandpageFormDataManager;
import com.ruoyi.system.mapper.landpage.LiuziLandpageFormRecordMapper;
import com.ruoyi.system.message.rocketmq.producer.LogMqProducer;
import com.ruoyi.system.req.engine.LiuziLandPageFormReq;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.landpage.LiuziLandpageService;
import com.ruoyi.system.service.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

import static com.ruoyi.common.enums.InnerLogType.LANDPAGE_CLICK;

/**
 * 留资落地页服务实现
 *
 * <AUTHOR>
 * @date 2022/01/24
 */
@Slf4j
@Service
public class LiuziLandpageServiceImpl implements LiuziLandpageService {

    @Autowired
    private OrderService orderService;

    @Autowired
    private LiuziLandpageFormRecordMapper liuziLandpageFormRecordMapper;

    @Autowired
    private DataStatService dataStatService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private LogMqProducer logMqProducer;

    @Autowired
    private AsyncAlipayLandpageFormDataManager asyncAlipayLandpageFormDataManager;

    @Autowired
    private StatService statService;

    @Autowired
    private ConvCallbackService convCallbackService;


    @Override
    public void formSubmit(LiuziLandPageFormReq req) {
        // 查询订单
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            throw new CustomException("链接已失效");
        }
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);

        // 添加转化记录
        HttpServletRequest request = ServletUtils.getRequest();
        String referer = request.getHeader("referer");
        String ip = IpUtils.getIpAddr(request);
        GlobalThreadPool.executorService.execute(() -> {
            if (StringUtils.isNotBlank(order.getOrderId())) {
                if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K047.join(order.getOrderId()), 60)) {
                    log.info("留资落地页表单提交重复(分布式锁限制), advertId={}, orderId={}, phone={}",
                            order.getAdvertId(), order.getOrderId(), req.getPhone());
                    return;
                }
            }

            // 落地页转化记录
            LiuziLandpageFormRecord record = new LiuziLandpageFormRecord();
            record.setPhone(req.getPhone());
            record.setName(req.getName());
            record.setAdvertId(order.getAdvertId());
            record.setOrderId(order.getOrderId());
            record.setConsumerId(order.getConsumerId());
            record.setAppId(order.getAppId());
            record.setSlotId(order.getSlotId());
            record.setIp(StrUtil.subPre(ip, 32));
            record.setReferer(StrUtil.subPre(referer, 255));
            if (null != adSnapshot) {
                record.setLandpageUrl(adSnapshot.getLandpageUrl());
            }
            liuziLandpageFormRecordMapper.insertLandpageFormRecord(record);
            //发送短信
            asyncAlipayLandpageFormDataManager.sendSmsContent(LiuziLandPageTypeEnum.NUO_HE.getType(),req.getPhone(), order.getAdvertId(), record.getId(),adSnapshot.getLandpageUrl());
        });

        // 打印inner日志
        JSONObject logJson = new JSONObject();
        logJson.put("activityId", order.getActivityId());
        logJson.put("slotId", order.getSlotId());
        logJson.put("appId", order.getAppId());
        logJson.put("consumerId", order.getConsumerId());
        logJson.put("orientId", order.getOrientId());
        logJson.put("advertId", order.getAdvertId());
        logJson.put("materialId", order.getMaterialId());
        if (null != adSnapshot) {
            logJson.put("chargeType", adSnapshot.getChargeType());
            logJson.put("landpageUrl", adSnapshot.getLandpageUrl());
            logJson.put("originLandpageUrl", adSnapshot.getOriginLandpageUrl());
            logJson.put("srid", adSnapshot.getSrid());
            logJson.put("pluginId", adSnapshot.getPluginId());
        }
        logJson.put("ip", ip);
        logJson.put("userAgent", request.getHeader("User-Agent"));
        logJson.put("referer", referer);
        logJson.put("orderId", order.getOrderId());
        logJson.put("phone", req.getPhone());
        logJson.put("name", req.getName());
        InnerLogUtils.log(LANDPAGE_CLICK, logJson);
        logMqProducer.sendMsg(LANDPAGE_CLICK, logJson);

        // 落地页数据
        dataStatService.handleAsync(LANDPAGE_CLICK, logJson);
    }

    @Override
    public void phoneFormSubmit(String orderId, String phone) {
        // 查询订单
        Order order = orderService.selectByOrderId(orderId);
        if (null == order) {
            throw new CustomException("链接已失效");
        }
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);

        // 添加转化记录
        HttpServletRequest request = ServletUtils.getRequest();
        String referer = request.getHeader("referer");
        String ip = IpUtils.getIpAddr(request);
        GlobalThreadPool.executorService.execute(() -> {
            if (StringUtils.isNotBlank(order.getOrderId())) {
                if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K047.join(order.getOrderId()), 60)) {
                    log.info("留资落地页表单提交重复(分布式锁限制), advertId={}, orderId={}, phone={}",
                            order.getAdvertId(), order.getOrderId(), phone);
                    return;
                }
            }

            // 落地页转化记录
            LiuziLandpageFormRecord record = new LiuziLandpageFormRecord();
            record.setPhone(phone);
            record.setName("");
            record.setAdvertId(order.getAdvertId());
            record.setOrderId(orderId);
            record.setConsumerId(order.getConsumerId());
            record.setAppId(order.getAppId());
            record.setSlotId(order.getSlotId());
            record.setIp(StrUtil.subPre(ip, 32));
            record.setReferer(StrUtil.subPre(referer, 255));
            if (null != adSnapshot) {
                record.setLandpageUrl(adSnapshot.getLandpageUrl());
            }
            liuziLandpageFormRecordMapper.insertLandpageFormRecord(record);
            //发送短信
            asyncAlipayLandpageFormDataManager.sendSmsContent(LiuziLandPageTypeEnum.NUO_HE.getType(), phone, order.getAdvertId(), record.getId(), adSnapshot.getLandpageUrl());
        });

        // 打印转化日志
        statService.landpageClick(orderId, MapUtil.of("phone", phone));

        // 上报媒体
        convCallbackService.handleAsync(LANDPAGE_CLICK, null, order);
    }

    @Override
    public void formPaySubmit(String outTradeNo, String payAmount, String payTime) {
        if (StringUtils.isBlank(outTradeNo)) {
            return;
        }

        // 限制请求频率
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K055.join(outTradeNo), 5);
        if (lock == null) {
            return;
        }

        // 查询留资订单
        LiuziLandpageFormRecord record = liuziLandpageFormRecordMapper.selectByOutTradeNo(outTradeNo);
        if (null == record || null != record.getPayTime()) {
            return;
        }

        // 打印日志
        statService.landpageClick(record.getOrderId());
        statService.convertEvent(record.getOrderId(), ConvType.PAY.getType());

        // 更新留资表单
        LiuziLandpageFormRecord updateRecord = new LiuziLandpageFormRecord();
        updateRecord.setId(record.getId());
        updateRecord.setPayAmount(Integer.valueOf(payAmount));
        updateRecord.setPayTime(DateUtil.parse(payTime, "yyyyMMddHHmmss"));
        liuziLandpageFormRecordMapper.updateLandpageFormRecord(updateRecord);

        // 发送短信
        asyncAlipayLandpageFormDataManager.sendSmsContent(LiuziLandPageTypeEnum.NUO_HE.getType(), record.getPhone(), record.getAdvertId(), record.getId(), record.getLandpageUrl());

        // 上报媒体
        GlobalThreadPool.executorService.submit(() -> {
            Order order = orderService.selectByOrderId(record.getOrderId());
            convCallbackService.handleAsync(LANDPAGE_CLICK, null, order);
        });
    }
}
