package com.ruoyi.system.service.manager.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.activity.ActivitySkin;
import com.ruoyi.system.mapper.manager.ActivitySkinMapper;
import com.ruoyi.system.service.manager.ActivitySkinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 活动皮肤Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
@Service
public class ActivitySkinServiceImpl implements ActivitySkinService {

    @Autowired
    private ActivitySkinMapper activitySkinMapper;

    /**
     * 查询活动皮肤
     *
     * @param id 活动皮肤ID
     * @return 活动皮肤
     */
    @Override
    public ActivitySkin selectActivitySkinById(Long id) {
        return activitySkinMapper.selectActivitySkinById(id);
    }

    /**
     * 查询活动皮肤列表
     *
     * @param activitySkin 活动皮肤
     * @return 活动皮肤
     */
    @Override
    public List<ActivitySkin> selectActivitySkinList(ActivitySkin activitySkin) {
        return activitySkinMapper.selectActivitySkinList(activitySkin);
    }

    /**
     * 新增活动皮肤
     *
     * @param activitySkin 活动皮肤
     * @return 结果
     */
    @Override
    public int insertActivitySkin(ActivitySkin activitySkin) {
        return activitySkinMapper.insertActivitySkin(activitySkin);
    }

    /**
     * 修改活动皮肤
     *
     * @param activitySkin 活动皮肤
     * @return 结果
     */
    @Override
    public int updateActivitySkin(ActivitySkin activitySkin) {
        return activitySkinMapper.updateActivitySkin(activitySkin);
    }

    @Override
    public List<ActivitySkin> selectTotalActivitySkinList() {
        return activitySkinMapper.selectSimpleActivitySkinList(new ActivitySkin());
    }

    @Override
    public Map<String, String> selectSkinNameMap() {
        List<ActivitySkin> list = activitySkinMapper.selectSimpleActivitySkinList(new ActivitySkin());
        return list.stream().collect(Collectors.toMap(ActivitySkin::getSkinCode, ActivitySkin::getSkinName, (oldVal, newVal) -> newVal));
    }

    @Override
    public Map<String, ActivitySkin> selectSkinMap() {
        List<ActivitySkin> list = activitySkinMapper.selectSimpleActivitySkinList(new ActivitySkin());
        return list.stream().collect(Collectors.toMap(ActivitySkin::getSkinCode, Function.identity(), (oldVal, newVal) -> newVal));
    }

    @Override
    public ActivitySkin selectBySkinCode(String skinCode) {
        if (StringUtils.isBlank(skinCode)) {
            return null;
        }
        return activitySkinMapper.selectBySkinCode(skinCode);
    }

    @Override
    public String selectSkinNameBySkinCode(String skinCode) {
        return activitySkinMapper.selectSkinNameBySkinCode(skinCode);
    }
}
