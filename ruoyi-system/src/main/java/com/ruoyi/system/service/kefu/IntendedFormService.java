package com.ruoyi.system.service.kefu;

import com.ruoyi.system.bo.kefu.IntendedFormBo;
import com.ruoyi.system.bo.kefu.IntendedFormImportErrorBo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 意向客户服务接口
 *
 * <AUTHOR>
 * @date 2023/04/10
 */
public interface IntendedFormService {

    /**
     * 意向用户Excel解析
     *
     * @param file excel文件
     * @return 意向用户列表
     */
    List<IntendedFormBo> analysisExcel(MultipartFile file);

    /**
     * 数据校验
     *
     * @param list 数据列表
     * @return 错误列表
     */
    List<IntendedFormImportErrorBo> dataVerify(List<IntendedFormBo> list);

    /**
     * 表单处理
     *
     * @param operAccountId 客服公司ID
     * @param list 表单列表
     */
    void handleForm(Long operAccountId, List<IntendedFormBo> list);
}
