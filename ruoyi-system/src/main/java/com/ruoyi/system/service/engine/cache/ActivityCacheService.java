package com.ruoyi.system.service.engine.cache;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.codec.Base62;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.system.domain.manager.Prize;
import com.ruoyi.system.entity.activity.Activity;
import com.ruoyi.system.entity.activity.ActivitySkin;
import com.ruoyi.system.mapper.manager.ActivityMapper;
import com.ruoyi.system.mapper.manager.ActivitySkinMapper;
import com.ruoyi.system.vo.activity.ActivityCustomerVO;
import com.ruoyi.system.vo.activity.ActivityVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 活动缓存服务
 *
 * <AUTHOR>
 * @date 2022/06/14
 */
@Slf4j
@Service
public class ActivityCacheService {

    @Autowired
    private ActivityMapper activityMapper;

    @Autowired
    private ActivitySkinMapper activitySkinMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 活动信息缓存
     */
    private final LoadingCache<Long, Optional<ActivityVO>> ACTIVITY_INFO_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Optional<ActivityVO>>() {
                @Override
                public Optional<ActivityVO> load(Long activityId) {
                    Activity activity = activityMapper.selectActivityById(activityId);
                    if (null == activity) {
                        return Optional.empty();
                    }

                    ActivityVO activityVO = new ActivityVO();
                    activityVO.setId(activity.getId());
                    activityVO.setActivityName(activity.getActivityName());
                    activityVO.setSkinCode(activity.getSkinCode());
                    activityVO.setAutoJoin(activity.getAutoJoin());
                    activityVO.setJoinTimes(activity.getJoinTimes());
                    activityVO.setRuleDesc(activity.getRuleDesc());
                    activityVO.setIcpNo(activity.getIcpNo());
                    activityVO.setCustomerConfig(JSON.parseObject(activity.getCustomerConfig(), ActivityCustomerVO.class));
                    if (StringUtils.isNotBlank(activity.getPrizes())) {
                        activityVO.setPrizeList(JSON.parseArray(activity.getPrizes(), Prize.class));
                    }
                    // 活动皮肤配置
                    JSONObject jsTemplate = null;
                    ActivitySkin skin = activitySkinMapper.selectBySkinCode(activity.getSkinCode());
                    if (null != skin) {
                        jsTemplate = JSON.parseObject(skin.getJsTemplate());
                        activityVO.setGlobalConfig(skin.getGlobalConfig());
                    }
                    if (StringUtils.isNotBlank(activity.getJsTemplate())) {
                        if (null == jsTemplate) {
                            jsTemplate = JSON.parseObject(activity.getJsTemplate());
                        } else {
                            BeanUtil.copyProperties(JSON.parseObject(activity.getJsTemplate()), jsTemplate, CopyOptions.create().ignoreNullValue().ignoreError());
                        }
                    }
                    activityVO.setJsTemplate(JSON.toJSONString(jsTemplate));
                    return Optional.of(activityVO);
                }

                @Override
                public ListenableFuture<Optional<ActivityVO>> reload(Long activityId, Optional<ActivityVO> oldValue) {
                    ListenableFutureTask<Optional<ActivityVO>> task = ListenableFutureTask.create(() -> load(activityId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 活动跳转路径缓存
     */
    private final LoadingCache<Long, String> ACTIVITY_PATH_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long activityId) {
                    String skinCode = activityMapper.selectSkinCodeById(activityId);
                    ActivitySkin skin = activitySkinMapper.selectBySkinCode(StringUtils.defaultString(skinCode));
                    return Optional.ofNullable(skin).map(ActivitySkin::getRedirectPath).orElse("") + "/" + Base62.encode(String.valueOf(activityId));
                }

                @Override
                public ListenableFuture<String> reload(Long activityId, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(activityId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 查询活动缓存
     *
     * @param activityId 活动Id
     * @return 活动信息
     */
    public ActivityVO getActivityCache(Long activityId) {
        if (null == activityId) {
            return null;
        }
        try {
            // 预览活动
            if (StrUtil.equalsIgnoreCase(ServletUtils.getParameter("mode"), "preview")) {
                String key = EngineRedisKeyFactory.K064.join(activityId);
                String value = redisCache.getCacheObject(key);
                if (StringUtils.isNotBlank(value)) {
                    return JSON.parseObject(value, ActivityVO.class);
                }
            }
        } catch (Exception e) {
            log.error("查询活动预览缓存异常", e);
        }
        try {
            // 真实活动
            Optional<ActivityVO> actOpt = ACTIVITY_INFO_CACHE.get(activityId);
            return actOpt.orElse(null);
        } catch (Exception e) {
            log.error("查询活动缓存异常, activityId={}", activityId, e);
        }
        return null;
    }

    /**
     * 查询活动跳转路径缓存
     *
     * @param activityId 活动ID
     * @return 活动跳转路径
     */
    public String getRedirectPathCache(Long activityId) {
        if (null == activityId) {
            return "";
        }
        try {
            return ACTIVITY_PATH_CACHE.get(activityId);
        } catch (Exception e) {
            log.error("查询活动跳转路径缓存异常, activityId={}", activityId, e);
        }
        return "";
    }

    /**
     * 刷新活动缓存
     *
     * @param activityId 活动ID
     */
    public void refreshActivityCache(Long activityId) {
        Optional.ofNullable(activityId).ifPresent(ACTIVITY_INFO_CACHE::refresh);
    }
}
