package com.ruoyi.system.service.open.impl;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity;
import com.ruoyi.system.service.open.YtxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 亿通行对接Service接口实现
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Slf4j
@Service
public class YtxServiceImpl implements YtxService {

    @Autowired
    private RedisCache redisCache;

    @Override
    public Boolean grantCoupon(Order order, ExternalCouponRecordEntity record, String poolCode) {
        if (StringUtils.isBlank(poolCode)) {
            return false;
        }

        try {
            // 获取openId
            String openId = redisCache.getCacheObject(EngineRedisKeyFactory.K141.join(order.getOrderId()));
            if (StringUtils.isBlank(openId)) {
                return false;
            }

            // 避免优惠券重复发放
            String redisKey = EngineRedisKeyFactory.K140.join("ytx", order.getOrderId());
            if (redisCache.hasKey(redisKey)) {
                return false;
            }

            // 参数记录
            JSONObject couponInfo = new JSONObject();
            couponInfo.put("openId", openId);
            record.setCouponInfo(couponInfo.toString());
            record.setCouponRequestId(order.getOrderId());

            // 调用接口发放优惠券
            String url = "https://apigw.ruubypay.com/markting_redenvelopegateway/redenvelope/grantV2";
            String appId = "";
            String secret = "";
            if (!SpringEnvironmentUtils.isProd()) {
                url = "https://apigw-ft.ruubypay.com/markting_redenvelopegateway/redenvelope/grantV2";
                appId = "cc5dec82209c45888620eabec3a29b50";
                secret = "a123456";
            }

            JSONObject reqData = new JSONObject();
            reqData.put("orderNo", order.getOrderId());
            reqData.put("poolCode", poolCode);
            reqData.put("openId", openId);
            reqData.put("num", 1);
            reqData.put("amount", 2);
            reqData.put("remark", "红包");

            JSONObject body = new JSONObject();
            body.put("ts", String.valueOf(System.currentTimeMillis()));
            body.put("appId", appId);
            body.put("appSecret", SecureUtil.md5(appId + SecureUtil.md5(secret) + body.getString("ts")));
            body.put("reqData", reqData);
            String bodyStr = body.toString();

            String resp = HttpUtil.createPost(url)
                    .header("Content-Type", "application/json")
                    .body(bodyStr)
                    .execute().body();
            log.info("亿通行优惠券发放, slotId={}, req={}, resp={}", order.getSlotId(), bodyStr, resp);
            record.setCouponParam(bodyStr);
            record.setCouponResp(resp);

            JSONObject result = JSON.parseObject(resp);
            boolean isSuccess = null != result && Objects.equals(result.getString("subCode"), "0000") && result.getJSONObject("subData").getBooleanValue("success");
            if (isSuccess) {
                redisCache.setCacheObject(redisKey, order.getOrderId(), 7, TimeUnit.DAYS);
            } else {
                log.error("亿通行优惠券发放失败, slotId={}, req={}, resp={}", order.getSlotId(), bodyStr, resp);
            }
            return isSuccess;
        } catch (Exception e) {
            log.error("亿通行优惠券发放异常", e);
        }
        return false;
    }
}
