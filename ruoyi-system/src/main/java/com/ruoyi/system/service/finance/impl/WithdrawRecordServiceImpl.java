package com.ruoyi.system.service.finance.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.constant.SspRedisKeyFactory;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.ConfirmStatusEnum;
import com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus;
import com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum;
import com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.account.OaStaffBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.finance.AccountRevenueEntity;
import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.entity.checkrecord.CheckRecordEntity;
import com.ruoyi.system.entity.qualification.AccountQualificationEntity;
import com.ruoyi.system.entity.withdraw.WithdrawRecordEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.account.OaStaffManger;
import com.ruoyi.system.mapper.appdata.AppMonthDataMapper;
import com.ruoyi.system.mapper.withdraw.WithdrawRecordMapper;
import com.ruoyi.system.req.withdraw.WithdrawApplyReq;
import com.ruoyi.system.req.withdraw.WithdrawFileReq;
import com.ruoyi.system.req.withdraw.WithdrawListReq;
import com.ruoyi.system.req.withdraw.WithdrawStatusReq;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.finance.AccountRevenueService;
import com.ruoyi.system.service.finance.CheckRecordService;
import com.ruoyi.system.service.finance.WithdrawRecordService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.qualification.AccountQualificationService;
import com.ruoyi.system.vo.withdraw.SspWithdrawListVO;
import com.ruoyi.system.vo.withdraw.WithdrawAppDataListVO;
import com.ruoyi.system.vo.withdraw.WithdrawInfoVO;
import com.ruoyi.system.vo.withdraw.WithdrawListVO;
import com.ruoyi.system.vo.withdraw.WithdrawQualificationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.util.BooleanUtil.isTrue;
import static com.ruoyi.common.constant.OaConstants.FINANCE_AUDITOR_LIST;
import static com.ruoyi.common.constant.OaConstants.PERMISSION_WITHDRAW_BUSINESS_AUDIT;
import static com.ruoyi.common.constant.OaConstants.PERMISSION_WITHDRAW_DEPARTMENT_AUDIT;
import static com.ruoyi.common.enums.account.AccountMainType.isCrmUser;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;
import static com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum.CHECK_REFUSE;
import static com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum.CHECK_SUCCESS;
import static com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum.WAIT_CHECK;
import static com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum.isCheckSuccess;
import static com.ruoyi.common.enums.publisher.WithdrawCheckStatusEnum.isWaitCheck;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.CEO_READY;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.CEO_REFUSE;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.FINANCE_READY;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.FINANCE_REFUSE;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.LEADER_READY;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.LEADER_REFUSE;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.getByCheckStatus;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.getCeoAuditStatus;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.getFinanceAuditStatus;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.getLeaderAuditStatus;
import static com.ruoyi.common.enums.publisher.WithdrawComplexAuditStatus.isReadyToAudit;

/**
 * 提现记录 Service
 *
 * <AUTHOR>
 * @date 2021-9-9 16:57:24
 */
@Slf4j
@Service
public class WithdrawRecordServiceImpl implements WithdrawRecordService{

    @Autowired
    private WithdrawRecordMapper withdrawRecordMapper;
    @Autowired
    private AccountService accountService;
    @Autowired
    private AccountQualificationService accountQualificationService;
    @Autowired
    private CheckRecordService checkRecordService;
    @Autowired
    private AppMonthDataService appMonthDataService;
    @Autowired
    private AccountRevenueService accountRevenueService;
    @Autowired
    private AppMonthDataMapper appMonthDataMapper;
    @Autowired
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private OaStaffManger oaStaffManger;
    @Autowired
    public TransactionTemplate transactionTemplate;

    /**
     * 最少可提现金额(分)
     */
    private static final int  MIN_WITHDRAW_AMOUNT = 10000;


    @Override
	public Boolean insert(WithdrawRecordEntity entity){
        if(Objects.isNull(entity)){
            return false;
        }
        return withdrawRecordMapper.insert(entity)>0;
    }

    @Override
    public Boolean updateById(WithdrawRecordEntity entity){
        if(Objects.isNull(entity)){
            return false;
        }
        return withdrawRecordMapper.updateById(entity)>0;
    }

    @Override
    public PageInfo<SspWithdrawListVO> selectWithdrawListForSsp(Long accountId) {
        WithdrawListReq req = new WithdrawListReq();
        req.setAccountIds(Sets.newHashSet(accountId));
        List<WithdrawRecordEntity> records = withdrawRecordMapper.selectListByCondition(req);
        if (CollectionUtils.isEmpty(records)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        Account account = accountService.selectAccountById(accountId);
        // 获取最新的审核记录
        List<CheckRecordEntity> checkRecords = checkRecordService.selectListByWithdrawIds(ListUtils.mapToList(records, WithdrawRecordEntity::getId));
        Map<Long, CheckRecordEntity> checkRecordMap = checkRecords.stream().collect(Collectors.toMap(CheckRecordEntity::getWithdrawRecordId, Function.identity(), (v1, v2) -> v2));

        return PageInfoUtils.dto2Vo(records, entity -> {
            SspWithdrawListVO vo = BeanUtil.copyProperties(entity, SspWithdrawListVO.class);
            vo.setEmail(account.getEmail());
            // 资质信息
            Optional.ofNullable(JSON.parseObject(entity.getExtraInfo(), AccountQualificationEntity.class)).ifPresent(qualification -> {
                vo.setBankAccount(qualification.getBankAccount());
                vo.setBankName(qualification.getBankName());
                vo.setBankAccountName(qualification.getBankAccountName());
                vo.setCompanyName(qualification.getCompanyName());
            });
            // 审核记录
            Optional.ofNullable(checkRecordMap.get(entity.getId())).ifPresent(checkRecord -> {
                vo.setRefuseReason(checkRecord.getRefuseReason());
            });
            return vo;
        });
    }

    @Override
    public PageInfo<WithdrawListVO> selectWithdrawList(WithdrawListReq req,boolean isExport) {
        LoginUser user = SecurityUtils.getLoginUser();
        Set<Long> accountIds = new HashSet<>();
        if (isCrmUser(user.getMainType())) {
            if (StringUtils.isNotEmpty(req.getAccountSearch())) {
                List<Long> ids = accountService.selectIdsByIdOrEmail(req.getAccountSearch());
                if(CollectionUtils.isEmpty(ids)){
                    return PageInfoUtils.buildReturnList(Collections.emptyList());
                }
                accountIds.addAll(ids);
            }
            if (StringUtils.isNotEmpty(req.getCompanyName())) {
                List<Long> ids = accountQualificationService.selectAccountIdsByCompanyName(req.getCompanyName());
                if(CollectionUtils.isEmpty(ids)){
                    return PageInfoUtils.buildReturnList(Collections.emptyList());
                }
                accountIds.addAll(ids);
            }
            // 数据权限控制
            DataPermissionBo permission = dataPermissionManager.selectAccount();
            if (hasPartialPermission(permission.getType())) {
                if (CollectionUtils.isEmpty(permission.getValues())) {
                    return PageInfoUtils.buildReturnList(Collections.emptyList());
                }
                if (CollectionUtils.isEmpty(accountIds)) {
                    accountIds.addAll(permission.getValues());
                } else {
                    accountIds.retainAll(permission.getValues());
                }
                if (CollectionUtils.isEmpty(accountIds)) {
                    return PageInfoUtils.buildReturnList(Collections.emptyList());
                }
            }
        }else{
            accountIds.add(user.getCrmAccountId());
        }
        req.setAccountIds(accountIds);
        req.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        if(!isExport){
            TableSupport.startPage();
        }
        List<WithdrawRecordEntity> recordEntities = withdrawRecordMapper.selectListByCondition(req);
        if (CollectionUtils.isEmpty(recordEntities)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        List<Long> withdrawIds = recordEntities.stream().map(WithdrawRecordEntity::getId).collect(Collectors.toList());
        List<CheckRecordEntity> checkRecordEntities = checkRecordService.selectListByWithdrawIds(withdrawIds);
        List<Long> checkAccountIds = checkRecordEntities.stream().map(CheckRecordEntity::getCheckAccountId).collect(Collectors.toList());
        //获取最新的审核记录
        Map<Long, CheckRecordEntity> checkMap = checkRecordEntities.stream().collect(Collectors.toMap(CheckRecordEntity::getWithdrawRecordId, Function.identity(), (v1, v2) -> v2));

        accountIds = recordEntities.stream().map(WithdrawRecordEntity::getAccountId).collect(Collectors.toSet());
        accountIds.addAll(checkAccountIds);
        Map<Long, Account> accountMap = accountService.selectListByIds(Lists.newArrayList(accountIds)).stream().collect(Collectors.toMap(Account::getId, Function.identity(), (v1, v2) -> v1));
        Map<Long, String> operatorNameMap = accountService.selectAccountContactMap(getOperatorIds(checkRecordEntities));
        return getWithdrawListVOPageInfo(recordEntities, checkMap, accountMap, operatorNameMap);
    }

    private PageInfo<WithdrawListVO> getWithdrawListVOPageInfo(List<WithdrawRecordEntity> recordEntities, Map<Long, CheckRecordEntity> checkMap,
                                                               Map<Long, Account> accountMap, Map<Long, String> operatorNameMap) {

        LoginUser user = SecurityUtils.getLoginUser();
        OaStaffBo staff = oaStaffManger.selectByEmail(user.getEmail());
        String postKey = isCrmUser(user.getMainType()) && null != staff.getPost() ? staff.getPost().getPostKey() : "";

        return PageInfoUtils.dto2Vo(recordEntities, entity -> {
            WithdrawListVO vo = BeanUtil.copyProperties(entity, WithdrawListVO.class);
            AccountQualificationEntity qualificationEntity = JSON.parseObject(entity.getExtraInfo(), AccountQualificationEntity.class);
            if(Objects.nonNull(qualificationEntity)){
                vo.setBankAccount(qualificationEntity.getBankAccount());
                vo.setBankName(qualificationEntity.getBankName());
                vo.setBankAccountName(qualificationEntity.getBankAccountName());
                vo.setCompanyName(qualificationEntity.getCompanyName());
            }
            Account account = accountMap.get(entity.getAccountId());
            vo.setAccountId(entity.getAccountId());
            if (Objects.nonNull(account)) {
                vo.setEmail(account.getEmail());
            }

            CheckRecordEntity checkRecord = checkMap.get(entity.getId());
            if (Objects.isNull(checkRecord) || isWaitCheck(vo.getWithdrawStatus()) && !isReadyToAudit(checkRecord.getComplexAuditStatus())) {
                vo.setComplexAuditStatus(getByCheckStatus(vo.getWithdrawStatus()));
                vo.setLeaderAuditStatus(WAIT_CHECK.getStatus());
                vo.setCeoAuditStatus(WAIT_CHECK.getStatus());
                vo.setFinanceAuditStatus(WAIT_CHECK.getStatus());
                if (user.isAdmin() || staff.getOaPermissionKeys().contains(PERMISSION_WITHDRAW_DEPARTMENT_AUDIT)) {
                    vo.setCanAudit(1);
                } else {
                    vo.setCanAudit(0);
                }
                return vo;
            }
            vo.setCheckDate(null == checkRecord.getAuditTime() ? checkRecord.getGmtCreate() : checkRecord.getAuditTime());
            vo.setCheckAccountName(operatorNameMap.get(checkRecord.getCheckAccountId()));
            vo.setRefuseReason(checkRecord.getRefuseReason());
            vo.setLeaderAuditorName(operatorNameMap.get(checkRecord.getLeaderAuditorId()));
            vo.setLeaderAuditTime(checkRecord.getLeaderAuditTime());
            vo.setLeaderAuditReason(checkRecord.getLeaderAuditReason());
            vo.setCeoAuditorName(operatorNameMap.get(checkRecord.getCeoAuditorId()));
            vo.setCeoAuditTime(checkRecord.getCeoAuditTime());
            vo.setCeoAuditReason(checkRecord.getCeoAuditReason());
            vo.setFinanceAuditorName(operatorNameMap.get(checkRecord.getFinanceAuditorId()));
            vo.setFinanceAuditTime(checkRecord.getFinanceAuditTime());
            vo.setFinanceAuditReason(checkRecord.getFinanceAuditReason());
            vo.setComplexAuditStatus(NumberUtils.defaultInt(checkRecord.getComplexAuditStatus(), getByCheckStatus(vo.getWithdrawStatus())));
            vo.setLeaderAuditStatus(getLeaderAuditStatus(vo.getComplexAuditStatus()).getStatus());
            vo.setCeoAuditStatus(getCeoAuditStatus(vo.getComplexAuditStatus()).getStatus());
            vo.setFinanceAuditStatus(getFinanceAuditStatus(vo.getComplexAuditStatus()).getStatus());

            // 审核权限判断
            if (user.isAdmin() && isWaitCheck(vo.getWithdrawStatus())
                    || Objects.equals(vo.getComplexAuditStatus(), LEADER_READY.getStatus()) && staff.getOaPermissionKeys().contains(PERMISSION_WITHDRAW_DEPARTMENT_AUDIT)
                    || Objects.equals(vo.getComplexAuditStatus(), CEO_READY.getStatus()) && staff.getOaPermissionKeys().contains(PERMISSION_WITHDRAW_BUSINESS_AUDIT)
                    || Objects.equals(vo.getComplexAuditStatus(), FINANCE_READY.getStatus()) && FINANCE_AUDITOR_LIST.contains(postKey)) {
                vo.setCanAudit(1);
            } else {
                vo.setCanAudit(0);
            }
            return vo;
        });
    }

    @Override
    public boolean checkWithdraw(WithdrawStatusReq req) {
        RedisLock lock = redisAtomicClient.getLock(SspRedisKeyFactory.K002.join(req.getId()), 3);
        if (lock == null) {
            throw new CustomException(ErrorCode.RESUBMIT);
        }

        LoginUser user = SecurityUtils.getLoginUser();
        // 非CRM用户权限限制
        if (!isCrmUser(user.getMainType())) {
            throw new CustomException(ErrorCode.E105001);
        }

        WithdrawRecordEntity withdrawRecordEntity = withdrawRecordMapper.selectById(req.getId());
        if(Objects.isNull(withdrawRecordEntity)){
            throw new CustomException(ErrorCode.E106001);
        }
        if (!isWaitCheck(withdrawRecordEntity.getWithdrawStatus())) {
            throw new CustomException(ErrorCode.E106005);
        }

        CheckRecordEntity oldCheckRecord = checkRecordService.selectByWithdrawId(req.getId());
        if (null == oldCheckRecord || !isReadyToAudit(oldCheckRecord.getComplexAuditStatus())) {
            oldCheckRecord = new CheckRecordEntity();
        }

        // 审核权限判断
        Integer complexAuditStatus = null != oldCheckRecord.getComplexAuditStatus() ? oldCheckRecord.getComplexAuditStatus() : getByCheckStatus(withdrawRecordEntity.getWithdrawStatus());
        OaStaffBo staff = oaStaffManger.selectByEmail(user.getEmail());
        String postKey = null != staff.getPost() ? staff.getPost().getPostKey() : "";
        if (!(user.isAdmin() && isWaitCheck(withdrawRecordEntity.getWithdrawStatus())
                || Objects.equals(complexAuditStatus, WithdrawComplexAuditStatus.LEADER_READY.getStatus()) && staff.getOaPermissionKeys().contains(PERMISSION_WITHDRAW_DEPARTMENT_AUDIT)
                || Objects.equals(complexAuditStatus, WithdrawComplexAuditStatus.CEO_READY.getStatus()) && staff.getOaPermissionKeys().contains(PERMISSION_WITHDRAW_BUSINESS_AUDIT)
                || Objects.equals(complexAuditStatus, WithdrawComplexAuditStatus.FINANCE_READY.getStatus()) && FINANCE_AUDITOR_LIST.contains(postKey))) {
            throw new CustomException(ErrorCode.E105001);
        }

        boolean isCheckSuccess = isCheckSuccess(req.getWithdrawStatus());
        return isTrue(transactionTemplate.execute(status -> {
            CheckRecordEntity checkRecord = checkRecordService.selectByWithdrawId(req.getId());
            if (null == checkRecord || !isReadyToAudit(checkRecord.getComplexAuditStatus())) {
                checkRecord = new CheckRecordEntity();
                checkRecord.setWithdrawRecordId(req.getId());
            }
            checkRecord.setCheckAccountId(user.getCrmAccountId());
            checkRecord.setAuditTime(new Date());
            if (!isCheckSuccess) {
                checkRecord.setRefuseReason(req.getAuditReason());
            }

            // 部门负责人审核
            if (Objects.equals(complexAuditStatus, WithdrawComplexAuditStatus.LEADER_READY.getStatus())) {
                checkRecord.setCheckStatus(isCheckSuccess ? WAIT_CHECK.getStatus() : CHECK_REFUSE.getStatus());
                checkRecord.setComplexAuditStatus(isCheckSuccess ? WithdrawComplexAuditStatus.CEO_READY.getStatus() : LEADER_REFUSE.getStatus());
                checkRecord.setLeaderAuditorId(checkRecord.getCheckAccountId());
                checkRecord.setLeaderAuditTime(checkRecord.getAuditTime());
                checkRecord.setLeaderAuditReason(req.getAuditReason());
                checkRecordService.save(checkRecord);
                if (isCheckSuccess) {
                    return true;
                }
            }
            // 业务负责人审核
            else if (Objects.equals(complexAuditStatus, PrepayComplexAuditStatus.CEO_READY.getStatus())) {
                checkRecord.setCheckStatus(isCheckSuccess ? WAIT_CHECK.getStatus() : CHECK_REFUSE.getStatus());
                checkRecord.setComplexAuditStatus(isCheckSuccess ? WithdrawComplexAuditStatus.FINANCE_READY.getStatus() : CEO_REFUSE.getStatus());
                checkRecord.setCeoAuditorId(checkRecord.getCheckAccountId());
                checkRecord.setCeoAuditTime(checkRecord.getAuditTime());
                checkRecord.setCeoAuditReason(req.getAuditReason());
                checkRecordService.save(checkRecord);
                if (isCheckSuccess) {
                    return true;
                }
            }
            // 财务审核
            else {
                checkRecord.setCheckStatus(isCheckSuccess ? CHECK_SUCCESS.getStatus() : CHECK_REFUSE.getStatus());
                checkRecord.setComplexAuditStatus(isCheckSuccess ? WithdrawComplexAuditStatus.APPROVE.getStatus() : FINANCE_REFUSE.getStatus());
                checkRecord.setFinanceAuditorId(checkRecord.getCheckAccountId());
                checkRecord.setFinanceAuditTime(checkRecord.getAuditTime());
                checkRecord.setFinanceAuditReason(req.getAuditReason());
                checkRecordService.save(checkRecord);
            }

            boolean result = withdrawRecordMapper.updateWithdrawStatus(req) > 0;
            if (!result) {
                status.setRollbackOnly();
            }

            //提现拒绝后，账号可提现金额累加回去
            if (result && Objects.equals(req.getWithdrawStatus(), CHECK_REFUSE.getStatus())) {
                accountRevenueService.deductionWithdrawAmount(withdrawRecordEntity.getAccountId(), -withdrawRecordEntity.getWithdrawAmount());
            }
            //提现审核通过后，媒体月账单状态改为已提现
            if (result && Objects.equals(req.getWithdrawStatus(), CHECK_SUCCESS.getStatus())) {
                appMonthDataService.updateConfirmStatus(req.getId(), ConfirmStatusEnum.WITHDRAW.getStatus());
            }
            return result;
        }));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long withdrawApply(WithdrawApplyReq req) {
        if(CollectionUtils.isEmpty(req.getAppMonthDataIds())){
            throw new CustomException(ErrorCode.ARGS);
        }


        LoginUser user = SecurityUtils.getLoginUser();

        AccountRevenueEntity revenueEntity = accountRevenueService.selectByAccountId(user.getCrmAccountId());

        if(Objects.isNull(revenueEntity) || revenueEntity.getWithdrawableAmount()< MIN_WITHDRAW_AMOUNT){
            throw new CustomException(ErrorCode.E106003);
        }

        RedisLock lock = redisAtomicClient.getLock(SspRedisKeyFactory.K001.join(user.getCrmAccountId()), 3);
        if (lock == null) {
            throw new CustomException(ErrorCode.RESUBMIT);
        }
        //查询未绑定且确认结算的月账单
        List<AppMonthDataEntity> dataEntities = appMonthDataService.selectNoWithdrawByAccountId(user.getCrmAccountId(), req.getAppMonthDataIds(),req.getId());
        if(req.getAppMonthDataIds().size() != dataEntities.size()){
            throw new CustomException(ErrorCode.E106002);
        }

        //校验金额
        int withdrawAmount = dataEntities.stream().mapToInt(AppMonthDataEntity::getAppRevenue).sum();
        if(req.getWithdrawAmount().intValue() != withdrawAmount){
            throw new CustomException(ErrorCode.E106004);
        }

        AccountQualificationEntity qualificationEntity = accountQualificationService.selectByAccountId(user.getCrmAccountId());
        if(Objects.isNull(qualificationEntity)){
            throw new CustomException(ErrorCode.E104001);
        }

        WithdrawRecordEntity withdrawRecordEntity = new WithdrawRecordEntity();
        withdrawRecordEntity.setWithdrawAmount(req.getWithdrawAmount());
        withdrawRecordEntity.setWithdrawStatus(WithdrawCheckStatusEnum.WAIT_CHECK.getStatus());
        withdrawRecordEntity.setAccountId(user.getCrmAccountId());
        withdrawRecordEntity.setExtraInfo(JSON.toJSONString(qualificationEntity));
        boolean result;
        if(NumberUtils.isNullOrLteZero(req.getId())){
            insert(withdrawRecordEntity);

            result = appMonthDataService.relevanceWithdrawRecord(req.getAppMonthDataIds(),withdrawRecordEntity.getId(), ConfirmStatusEnum.NO_WITHDRAW.getStatus()) > 0;
        }else{
            //重新提交
            withdrawRecordEntity.setGmtCreate(new Date());
            withdrawRecordEntity.setId(req.getId());
            withdrawRecordEntity.setWithdrawFile("");
            updateById(withdrawRecordEntity);

            //重新绑定关联记录
            //先查询出原来关联的账单id列表
            List<AppMonthDataEntity> entityList = appMonthDataMapper.selectByWithdrawId(req.getId());
            List<Long> appMonthDataIds = entityList.stream().map(AppMonthDataEntity::getId).collect(Collectors.toList());
            appMonthDataIds.removeAll(req.getAppMonthDataIds());
            //取消绑定提现关联记录
            appMonthDataService.relevanceWithdrawRecord(appMonthDataIds,0L,ConfirmStatusEnum.CONFIRM.getStatus());
            //绑定新关联记录
            result = appMonthDataService.relevanceWithdrawRecord(req.getAppMonthDataIds(), req.getId(),ConfirmStatusEnum.NO_WITHDRAW.getStatus()) > 0;
        }

        if(result){
            //扣减可提现金额
            accountRevenueService.deductionWithdrawAmount(withdrawRecordEntity.getAccountId(),req.getWithdrawAmount());
        }
        //返回提现id
        return withdrawRecordEntity.getId();

    }

    @Override
    public WithdrawInfoVO withdrawInfo(Long withdrawId) {
        WithdrawRecordEntity withdrawRecordEntity = withdrawRecordMapper.selectById(withdrawId);
        if(Objects.isNull(withdrawRecordEntity)){
            throw new CustomException(ErrorCode.E106001);
        }
        WithdrawQualificationVO qualificationVO = JSON.parseObject(withdrawRecordEntity.getExtraInfo(), WithdrawQualificationVO.class);
        Account account = accountService.selectAccountById(withdrawRecordEntity.getAccountId());
        if(Objects.isNull(account)){
            throw new CustomException(ErrorCode.E104002);
        }
        qualificationVO.setContact(account.getContact());
        qualificationVO.setEmail(account.getEmail());
        //结算单明细
        List<WithdrawAppDataListVO> dataLists = appMonthDataService.selectByWithdrawId(withdrawId);

        WithdrawInfoVO vo = new WithdrawInfoVO();
        vo.setQualificationInfo(qualificationVO);
        vo.setDataLists(dataLists);
        vo.setWithdrawAmount(withdrawRecordEntity.getWithdrawAmount());
        vo.setWithdrawFile(withdrawRecordEntity.getWithdrawFile());
        return vo;
    }

    @Override
    public boolean updateWithdrawFile(WithdrawFileReq req) {
        return withdrawRecordMapper.updateWithdrawFile(req) > 0;
    }

    @Override
    public Integer sumWithdrawAmountByAccountId(Long accountId) {
        Map<Long, Integer> map = sumWithdrawAmountByAccountId(Collections.singletonList(accountId));
        return map.getOrDefault(accountId, 0);
    }

    @Override
    public Map<Long, Integer> sumWithdrawAmountByAccountId(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<WithdrawRecordEntity> list = withdrawRecordMapper.sumWithdrawAmountByAccountId(accountIds);
        return list.stream().collect(Collectors.toMap(WithdrawRecordEntity::getAccountId, WithdrawRecordEntity::getWithdrawAmount, (v1, v2) -> v2));
    }

    /**
     * 获取申请人/审核人ID列表
     */
    private List<Long> getOperatorIds(List<CheckRecordEntity> list) {
        List<Long> operatorIds = new ArrayList<>();
        for (CheckRecordEntity record : list) {
            if (null != record.getCheckAccountId()) {
                operatorIds.add(record.getCheckAccountId());
            }
            if (null != record.getLeaderAuditorId()) {
                operatorIds.add(record.getLeaderAuditorId());
            }
            if (null != record.getCeoAuditorId()) {
                operatorIds.add(record.getCeoAuditorId());
            }
            if (null != record.getFinanceAuditorId()) {
                operatorIds.add(record.getFinanceAuditorId());
            }
        }
        return operatorIds;
    }
}
