package com.ruoyi.system.service.advert.impl;

import com.ruoyi.system.bo.advert.AdvertOrientAppBo;
import com.ruoyi.system.domain.advert.AdvertOrienteApp;
import com.ruoyi.system.mapper.advert.AdvertOrienteAppMapper;
import com.ruoyi.system.service.advert.AdvertOrientAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 广告定向媒体Service实现
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Service
public class AdvertOrientAppServiceImpl implements AdvertOrientAppService {

    @Autowired
    private AdvertOrienteAppMapper advertOrienteAppMapper;

    @Override
    public List<AdvertOrientAppBo> selectListByOrientId(Long orientId) {
        return advertOrienteAppMapper.selectListByOrientId(orientId);
    }

    @Override
    public List<AdvertOrienteApp> selectByOrientId(Long orientId) {
        return advertOrienteAppMapper.selectByOrientId(orientId);
    }

    @Override
    public Map<Long, List<AdvertOrientAppBo>> selectMapByAdvertId(Long advertId) {
        List<AdvertOrientAppBo> list = advertOrienteAppMapper.selectListByAdvertId(advertId);
        return list.stream().collect(Collectors.groupingBy(AdvertOrientAppBo::getOrientId));
    }
}
