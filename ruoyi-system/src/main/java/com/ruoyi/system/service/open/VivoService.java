package com.ruoyi.system.service.open;

import com.ruoyi.system.vo.open.VivoTokenData;

/**
 * vivo对接Service接口
 *
 * <AUTHOR>
 * @date 2022/05/23
 */
public interface VivoService {

    /**
     * 获取并缓存令牌
     *
     * @param client 应用ID
     * @param code 授权码
     * @param state 开发者标识
     */
    void getAndCacheToken(String client, String code, String state);

    /**
     * 获取访问令牌
     *
     * @param clientId 应用ID
     * @param advertiserId vivo平台的广告主ID
     * @return 访问令牌
     */
    String getAccessToken(String clientId, String advertiserId);

    /**
     * 刷新令牌
     *
     * @param clientId 应用ID
     * @param refreshToken 刷新用的令牌
     * @param advertiserId vivo平台的广告主ID
     * @return 令牌数据
     */
    VivoTokenData refreshToken(String clientId, String refreshToken, String advertiserId);

    /**
     * 用户行为上传
     */
    String behaviorUpload(String orderId, Long slotId, String requestId, String creativeId, String adextra);
}
