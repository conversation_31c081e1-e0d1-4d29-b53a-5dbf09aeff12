package com.ruoyi.system.service.manager.impl;

import com.ruoyi.system.entity.plugin.Plugin;
import com.ruoyi.system.mapper.manager.PluginMapper;
import com.ruoyi.system.service.manager.PluginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 插件Service接口实现
 *
 * <AUTHOR>
 * @date 2021-12-29
 */
@Service
public class PluginServiceImpl implements PluginService {

    @Autowired
    private PluginMapper pluginMapper;

    @Override
    public Plugin selectById(Long id) {
        if (null == id) {
            return null;
        }
        return pluginMapper.selectById(id);
    }

    @Override
    public List<Plugin> selectList(Plugin param) {
        return pluginMapper.selectList(param);
    }

    @Override
    public int insertPlugin(Plugin plugin) {
        return pluginMapper.insertPlugin(plugin);
    }

    @Override
    public int updatePlugin(Plugin plugin) {
        return pluginMapper.updatePlugin(plugin);
    }
}
