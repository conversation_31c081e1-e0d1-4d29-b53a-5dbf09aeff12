package com.ruoyi.system.service.manager;

import com.ruoyi.system.entity.advert.InnovateLayer;
import com.ruoyi.system.req.advert.InnovateLayerReq;
import com.ruoyi.system.vo.advert.InnovateLayerVO;

import java.util.List;
import java.util.Map;

/**
 * 创新弹层Service接口
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
public interface InnovateLayerService {

    /**
     * 查询创新弹层
     *
     * @param id 创新弹层ID
     * @return 创新弹层
     */
    InnovateLayer selectInnovateLayerById(Long id);

    /**
     * 查询创新弹层VO
     *
     * @param id 创新弹层ID
     * @return 创新弹层VO
     */
    InnovateLayerVO selectInnovateVOLayerById(Long id);

    /**
     * 查询创新弹层列表
     *
     * @param req 请求参数
     * @return 创新弹层集合
     */
    List<InnovateLayer> selectInnovateLayerList(InnovateLayerReq req);

    /**
     * 查询创新弹层映射
     * 注:创新弹层数量较少，所以就不传ID列表查询了
     *
     * @return 创新弹层ID-创新弹层映射
     */
    Map<Long, InnovateLayerVO> selectInnovateLayerMap();

    /**
     * 新增创新弹层
     *
     * @param req 请求参数
     * @return 结果
     */
    int insertInnovateLayer(InnovateLayerReq req);

    /**
     * 修改创新弹层
     *
     * @param req 请求参数
     * @return 结果
     */
    int updateInnovateLayer(InnovateLayerReq req);

    /**
     * 删除创新弹层
     *
     * @param id 创新弹层ID
     * @return 结果
     */
    int deleteInnovateLayer(Long id);
}
