package com.ruoyi.system.service.engine.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.service.domain.DomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.domain.DomainType.ACTIVITY_DOMAIN;
import static com.ruoyi.common.enums.domain.DomainType.LANDPAGE_DOMAIN;
import static com.ruoyi.common.enums.domain.DomainType.SLOT_DOMAIN;

/**
 * 域名缓存服务
 *
 * <AUTHOR>
 * @date 2022/09/16
 */
@Slf4j
@Service
public class DomainCacheService implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private DomainService domainService;

    /**
     * 域名缓存
     */
    private final LoadingCache<String, Optional<Domain>> DOMAIN_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .expireAfterWrite(12, TimeUnit.HOURS)
            .maximumSize(500)
            .build(new CacheLoader<String, Optional<Domain>>() {
                @Override
                public Optional<Domain> load(String domain) {
                    Domain domainDO = domainService.selectDomain(domain);
                    return Optional.ofNullable(domainDO);
                }

                @Override
                public ListenableFuture<Optional<Domain>> reload(String domain, Optional<Domain> oldValue) {
                    ListenableFutureTask<Optional<Domain>> task = ListenableFutureTask.create(() -> load(domain));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 支付宝可用域名缓存
     */
    private final LoadingCache<Integer, Set<String>> ALIPAY_DOMAIN_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .expireAfterWrite(12, TimeUnit.HOURS)
            .maximumSize(3)
            .build(new CacheLoader<Integer, Set<String>>() {
                @Override
                public Set<String> load(Integer key) {
                    return domainService.selectAlipayValidDomainList(key);
                }

                @Override
                public ListenableFuture<Set<String>> reload(Integer key, Set<String> oldValue) {
                    ListenableFutureTask<Set<String>> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 微信可用域名缓存
     */
    private final LoadingCache<Integer, Set<String>> WECHAT_DOMAIN_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .expireAfterWrite(12, TimeUnit.HOURS)
            .maximumSize(3)
            .build(new CacheLoader<Integer, Set<String>>() {
                @Override
                public Set<String> load(Integer key) {
                    return domainService.selectWechatValidDomainList(key);
                }

                @Override
                public ListenableFuture<Set<String>> reload(Integer key, Set<String> oldValue) {
                    ListenableFutureTask<Set<String>> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });


    /**
     * 查询域名备案号缓存
     *
     * @param domain 域名
     * @return 备案号
     */
    public String selectIcpNo(String domain) {
        Domain domainDO = selectDomainCache(domain);
        return Optional.ofNullable(domainDO).map(Domain::getIcpNo).orElse(StringUtils.EMPTY);
    }

    /**
     * 查询域名缓存
     *
     * @param domain 域名
     * @return 域名信息
     */
    public Domain selectDomainCache(String domain) {
        if (StringUtils.isBlank(domain)) {
            return null;
        }
        try {
            return DOMAIN_CACHE.get(domain).orElse(null);
        } catch (Exception e) {
            log.error("查询域名缓存异常, domain={}", domain, e);
        }
        return null;
    }

    /**
     * 查询支付宝可用域名列表缓存(用于域名自动替换)
     *
     * @param domainType 域名类型
     * @return 支付宝可用域名集合
     */
    public Set<String> selectAlipayValidDomainListCache(Integer domainType) {
        try {
            return ALIPAY_DOMAIN_CACHE.get(domainType);
        } catch (ExecutionException e) {
            log.error("查询支付宝可用域名列表缓存失败", e);
        }
        return Collections.emptySet();
    }

    /**
     * 查询微信可用落地页域名列表缓存(用于域名自动替换)
     *
     * @param domainType 域名类型
     * @return 微信可用域名集合
     */
    public Set<String> selectWechatValidDomainListCache(Integer domainType) {
        try {
            return WECHAT_DOMAIN_CACHE.get(domainType);
        } catch (ExecutionException e) {
            log.error("查询微信可用域名列表缓存失败", e);
        }
        return Collections.emptySet();
    }

    /**
     * 刷新域名缓存
     *
     * @param domain 域名
     */
    public void refreshDomainCache(String domain) {
        if (StringUtils.isBlank(domain)) {
            return;
        }
        DOMAIN_CACHE.refresh(domain);
        ALIPAY_DOMAIN_CACHE.refresh(SLOT_DOMAIN.getType());
        ALIPAY_DOMAIN_CACHE.refresh(ACTIVITY_DOMAIN.getType());
        ALIPAY_DOMAIN_CACHE.refresh(LANDPAGE_DOMAIN.getType());
        WECHAT_DOMAIN_CACHE.refresh(SLOT_DOMAIN.getType());
        WECHAT_DOMAIN_CACHE.refresh(ACTIVITY_DOMAIN.getType());
        WECHAT_DOMAIN_CACHE.refresh(LANDPAGE_DOMAIN.getType());
    }

    /**
     * 初始化域名缓存
     */
    private void initDomainCache() {
        // 初始化支付宝/微信可用域名列表缓存
        ALIPAY_DOMAIN_CACHE.put(ACTIVITY_DOMAIN.getType(), domainService.selectAlipayValidDomainList(ACTIVITY_DOMAIN.getType()));
        ALIPAY_DOMAIN_CACHE.put(LANDPAGE_DOMAIN.getType(), domainService.selectAlipayValidDomainList(LANDPAGE_DOMAIN.getType()));
        WECHAT_DOMAIN_CACHE.put(ACTIVITY_DOMAIN.getType(), domainService.selectWechatValidDomainList(ACTIVITY_DOMAIN.getType()));
        WECHAT_DOMAIN_CACHE.put(LANDPAGE_DOMAIN.getType(), domainService.selectWechatValidDomainList(LANDPAGE_DOMAIN.getType()));
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        if (contextRefreshedEvent.getApplicationContext().getParent() == null) {
            log.info("DomainCacheService.initCacheCache started.");
            initDomainCache();
            log.info("DomainCacheService.initCacheCache finished.");
        }
    }
}
