package com.ruoyi.system.service.callback.processor;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.JJXQ;

/**
 * 集集星球上报处理器
 *
 * <AUTHOR>
 * @date 2023/11/1
 */
@Slf4j
@Service
public class JjxqCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final String CONV_URL = "https://datacenter.zhisanzhao.com/dataCenter/gateway/task/cpa/callback";

    @Override
    public CallbackProcessorTypeEnum getType() {
        return JJXQ;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("taskIdentification"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            JSONObject body = new JSONObject();
            body.put("taskIdentification", context.getParam().getSlotParam().getString("taskIdentification"));
            String resp = HttpUtil.post(CONV_URL, body.toString());
            log.info("{}接口上报, req={}, resp={}", getType().getName(), body, resp);
            return true;
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
