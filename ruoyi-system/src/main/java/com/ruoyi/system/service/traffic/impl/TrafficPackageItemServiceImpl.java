package com.ruoyi.system.service.traffic.impl;

import com.ruoyi.system.service.manager.SlotService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.traffic.TrafficPackageItemService;
import com.ruoyi.system.entity.traffic.TrafficPackageItemEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.traffic.TrafficPackageItemMapper;

/**
 * 流量包组成 Service
 *
 * <AUTHOR>
 * @date 2022-8-23 17:57:44
 */
@Service
public class TrafficPackageItemServiceImpl implements TrafficPackageItemService {

    @Autowired
    private TrafficPackageItemMapper trafficPackageItemMapper;

    @Autowired
    private SlotService slotService;

    @Override
    public int updateOrInsert(Long trafficPackageId, List<Long> slotIds) {
        if (null == trafficPackageId || CollectionUtils.isEmpty(slotIds)) {
            return 0;
        }
        // 先删除
        trafficPackageItemMapper.deleteBy(trafficPackageId);
        // 再新增
        Map<Long, Long> slotAppMap = slotService.selectAppIdMap(slotIds);
        List<TrafficPackageItemEntity> items = slotIds.stream().map(slotId -> {
            TrafficPackageItemEntity item = new TrafficPackageItemEntity();
            item.setTrafficPackageId(trafficPackageId);
            item.setAppId(slotAppMap.get(slotId));
            item.setSlotId(slotId);
            return item;
        }).collect(Collectors.toList());
        return trafficPackageItemMapper.batchInsert(items);
    }

    @Override
    public List<TrafficPackageItemEntity> selectList(Long trafficPackageId) {
        if (null == trafficPackageId) {
            return Collections.emptyList();
        }
        return trafficPackageItemMapper.selectByTrafficPackageIds(Collections.singletonList(trafficPackageId));
    }

    @Override
    public Map<Long, List<Long>> selectTrafficSlotMap(List<Long> trafficPackageIds) {
        if (CollectionUtils.isEmpty(trafficPackageIds)) {
            return Collections.emptyMap();
        }
        List<TrafficPackageItemEntity> items = trafficPackageItemMapper.selectByTrafficPackageIds(trafficPackageIds);
        return items.stream().collect(Collectors.groupingBy(TrafficPackageItemEntity::getTrafficPackageId,Collectors.mapping(TrafficPackageItemEntity::getSlotId, Collectors.toList())));
    }

    @Override
    public List<Long> selectTrafficPackageIdsByAppIds(List<Long> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return Collections.emptyList();
        }
        return trafficPackageItemMapper.selectTrafficPackageIdsByAppIds(appIds);
    }

    @Override
    public List<Long> selectTotalAppIds() {
        return trafficPackageItemMapper.selectTotalAppIds();
    }
}
