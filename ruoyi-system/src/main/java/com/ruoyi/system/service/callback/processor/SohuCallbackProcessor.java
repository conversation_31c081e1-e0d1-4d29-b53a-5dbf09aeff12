package com.ruoyi.system.service.callback.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.SOHU;

/**
 * 搜狐上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/29
 */
@Slf4j
@Service
public class SohuCallbackProcessor implements CallbackProcessor, InitializingBean {

    @Override
    public CallbackProcessorTypeEnum getType() {
        return SOHU;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("shcallback"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            String shcallback = UrlUtils.urlDecode(context.getParam().getSlotParam().getString("shcallback"));
            String url = StrUtil.format("http://t.ads.sohu.com/count/ac?shcallback={}&event_type={}", shcallback, 2002);
            String resp = HttpUtil.get(url);
            log.info("{}接口上报, shcallback={}, resp={}", getType().getName(), shcallback, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getInteger("code"), 0)) {
                return true;
            }
            log.error("{}接口上报失败, shcallback={}, resp={}", getType().getName(), shcallback, resp);
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
