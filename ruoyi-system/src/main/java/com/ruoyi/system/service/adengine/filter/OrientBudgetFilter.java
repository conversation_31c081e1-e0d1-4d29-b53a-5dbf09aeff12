package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.service.adengine.AdvertFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 配置每日预算过滤
 */
@Component
public class OrientBudgetFilter implements AdvertFilter {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();
        String dateStr = context.getDateStr();

        if (null == ad.getOrientBudget()) {
            return true;
        }
        if (ad.getOrientBudget() <= 0) {
            return false;
        }

        // 直投发券预扣: 发券消耗 + 单价 <= 配置预算 时可以正常发券
        if (Objects.equals(context.getActivityId(), 0L)) {
            Long launchConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K080.join(dateStr, ad.getAdvertId(), ad.getOrientId())));
            if (launchConsume + ad.getUnitPrice() > ad.getOrientBudget()) {
                return false;
            }
        }

        // 预算控制
        Long consume = redisAtomicClient.getLong(EngineRedisKeyFactory.K082.join(dateStr, ad.getOrientId()));
        if (null == consume) {
            return true;
        }
        if (null != ad.getMilliUnitPrice() && ad.getMilliUnitPrice() % 100 > 0) {
            return consume + ad.getUnitPrice() <= ad.getOrientBudget() &&
                    consume * 100 + ad.getMilliUnitPrice() <= ad.getOrientBudget() * 100L;
        }
        return consume + ad.getUnitPrice() <= ad.getOrientBudget();
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.ORIENT_BUDGET;
    }
}
