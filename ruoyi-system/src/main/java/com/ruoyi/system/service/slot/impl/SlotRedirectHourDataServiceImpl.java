package com.ruoyi.system.service.slot.impl;

import com.ruoyi.system.entity.slot.SlotRedirectHourData;
import com.ruoyi.system.mapper.slot.SlotRedirectHourDataMapper;
import com.ruoyi.system.req.slot.data.SlotRedirectDataParam;
import com.ruoyi.system.service.slot.SlotRedirectHourDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广告位投放小时数据Service接口实现
 *
 * <AUTHOR>
 * @date 2022-04-28
 */
@Slf4j
@Service
public class SlotRedirectHourDataServiceImpl implements SlotRedirectHourDataService {

    @Autowired
    private SlotRedirectHourDataMapper slotRedirectHourDataMapper;

    @Override
    public SlotRedirectHourData selectBy(SlotRedirectHourData param) {
        return slotRedirectHourDataMapper.selectBy(param);
    }

    @Override
    public List<SlotRedirectHourData> groupBy(SlotRedirectDataParam param) {
        return slotRedirectHourDataMapper.groupBy(param);
    }

    @Override
    public int insert(SlotRedirectHourData record) {
        return slotRedirectHourDataMapper.insert(record);
    }

    @Override
    public int update(Long id, int pv, int uv) {
        return slotRedirectHourDataMapper.update(id, pv, uv);
    }
}
