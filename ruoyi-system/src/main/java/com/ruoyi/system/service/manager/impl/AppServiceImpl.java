package com.ruoyi.system.service.manager.impl;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.mapper.manager.AppMapper;
import com.ruoyi.system.service.manager.AppService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 媒体应用Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Service
public class AppServiceImpl implements AppService {

    @Autowired
    private AppMapper appMapper;

    @Override
    public App selectAppById(Long id) {
        if (null == id) {
            return null;
        }
        return appMapper.selectAppById(id);
    }

    @Override
    public String selectAppNameById(Long id) {
        return Optional.ofNullable(selectAppById(id)).map(App::getAppName).orElse("");
    }

    @Override
    public List<App> selectAppList(App app) {
        return appMapper.selectAppList(app);
    }

    @Override
    public List<Long> selectAccountIdList(App app) {
        return appMapper.selectAccountIdList(app);
    }

    @Override
    public List<App> selectTotalAppList() {
        App app = new App();

        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();
        app.setAccountId(user.getCrmAccountId());

        return appMapper.selectSimpleAppList(app);
    }

    @Override
    public int insertApp(App app) {
        // 设置 appKey 和 appSecret
        app.setAppKey(IdUtils.fastSimpleUUID());
        app.setAppSecret(IdUtils.fastSimpleUUID());
        return appMapper.insertApp(app);
    }

    @Override
    public int updateApp(App app) {
        return appMapper.updateApp(app);
    }

    @Override
    public Map<Long, Integer> groupByAccountId(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<Map<String, Long>> list = appMapper.groupByAccountId(accountIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> result = new HashMap<>(list.size());
        for (Map<String, Long> map : list) {
            result.put(map.get("accountId"), map.getOrDefault("count", 0L).intValue());
        }
        return result;
    }

    @Override
    public List<App> selectSimpleInfoByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return appMapper.selectSimpleInfoByIds(ids);
    }

    @Override
    public Map<Long, String> selectAppNameMap(List<Long> appIds) {
        if(CollectionUtils.isEmpty(appIds)){
            return Collections.emptyMap();
        }
        List<App> apps = selectSimpleInfoByIds(appIds);

        return apps.stream().collect(Collectors.toMap(App::getId, App::getAppName,(v1,v2) ->v2));
    }

    @Override
    public Map<Long, String> selectAppNameMap() {
        List<App> apps = appMapper.selectAppIdAndName();
        return apps.stream().collect(Collectors.toMap(App::getId, App::getAppName, (v1, v2) -> v2));
    }

    @Override
    public List<Long> selectAppIdsByAppName(String appName) {
        if (StringUtils.isBlank(appName)) {
            return Collections.emptyList();
        }

        App app = new App();
        app.setAppName(appName);
        return appMapper.selectAppIdList(app);
    }

    @Override
    public List<Long> selectAppIdsBySearchValue(String searchValue) {
        if (StringUtils.isBlank(searchValue)) {
            return Collections.emptyList();
        }

        App app = new App();
        app.setSearchValue(searchValue);
        return appMapper.selectAppIdList(app);
    }
}
