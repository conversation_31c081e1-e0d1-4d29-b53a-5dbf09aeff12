package com.ruoyi.system.service.engine;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.domain.order.Order;

import java.util.List;
import java.util.Map;

/**
 * 设备唯一标识接口
 *
 * <AUTHOR>
 * @date 2023/06/08
 */
public interface DeviceUidService {

    /**
     * 缓存广告位设备唯一标识
     *
     * @param paramMap 广告位参数
     * @param appId 媒体ID
     * @param deviceId 设备ID
     */
    void cacheParameter(Map<String, String> paramMap, Long appId, String deviceId);

    /**
     * 缓存设备唯一标识(用于点击监测链接)
     *
     * @param paramMap 参数
     */
    void cacheParameter(Map<String, String> paramMap);

    /**
     * 缓存点击广告的设备
     *
     * @param order 订单号
     * @param logJson 日志参数
     */
    void cacheAdvertClickUid(Order order, JSONObject logJson);

    /**
     * 匹配订单号
     *
     * @param advertiserId 广告主ID
     * @param ip IP
     * @param userAgent userAgent
     * @param uids 设备唯一标识列表
     * @return 匹配到的订单号
     */
    String matchOrderId(Long advertiserId, String ip, String userAgent, List<String> uids);

    /**
     * 匹配订单号
     *
     * @param advertiserId 广告主ID
     * @param ip IP
     * @param osType 系统类型
     * @param osVersion 系统版本
     * @param mobileModel 手机型号
     * @return 匹配到的订单号
     */
    String matchOrderId(Long advertiserId, String ip, String osType, String osVersion, String mobileModel);
}
