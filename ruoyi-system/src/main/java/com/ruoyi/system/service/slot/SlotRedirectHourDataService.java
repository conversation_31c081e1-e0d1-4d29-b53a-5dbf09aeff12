package com.ruoyi.system.service.slot;

import com.ruoyi.system.entity.slot.SlotRedirectHourData;
import com.ruoyi.system.req.slot.data.SlotRedirectDataParam;

import java.util.List;

/**
 * 广告位投放小时数据Service接口
 *
 * <AUTHOR>
 * @date 2022-04-28
 */
public interface SlotRedirectHourDataService {

    /**
     * 查询广告位投放小时数据列表
     *
     * @param param 查询参数
     * @return 广告位投放小时数据
     */
    SlotRedirectHourData selectBy(SlotRedirectHourData param);

    /**
     * 查询广告位投放汇总数据
     *
     * @param param 查询参数
     * @return 广告位投放汇总数据
     */
    List<SlotRedirectHourData> groupBy(SlotRedirectDataParam param);

    /**
     * 新增广告位投放小时数据
     *
     * @param record 广告位投放小时数据
     * @return 影响行数
     */
    int insert(SlotRedirectHourData record);

    /**
     * 更新广告位投放小时数据
     *
     * @param id 数据ID
     * @param pv PV增量
     * @param uv UV增量
     * @return 影响行数
     */
    int update(Long id, int pv, int uv);
}
