package com.ruoyi.system.service.common.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.TextUtils;
import com.ruoyi.system.entity.common.IndustryEntity;
import com.ruoyi.system.mapper.common.IndustryMapper;
import com.ruoyi.system.service.common.IndustryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 行业管理表 Service
 *
 * <AUTHOR>
 * @date 2023-3-6 17:34:38
 */
@Service
public class IndustryServiceImpl implements IndustryService {

    @Autowired
    private IndustryMapper industryMapper;

    @Override
    public List<IndustryEntity> selectList(IndustryEntity param) {
        return industryMapper.selectList(param);
    }

    @Override
    public List<IndustryEntity> selectEnableList() {
        IndustryEntity param = new IndustryEntity();
        param.setEnableStatus(1);
        return selectList(param);
    }

    @Override
    public Map<Long, String> selectIndustryNameMap(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<IndustryEntity> list = industryMapper.selectByIds(ids);
        return list.stream().collect(Collectors.toMap(IndustryEntity::getId, IndustryEntity::getIndustryName));
    }

    @Override
    public int insert(IndustryEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return industryMapper.insert(entity);
    }

    @Override
    public int updateById(IndustryEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return industryMapper.updateById(entity);
    }

    @Override
    public IndustryEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return industryMapper.selectById(id);
    }

    @Override
    public boolean isIndustryNameExist(String industryName) {
        return null != industryMapper.existByIndustryName(industryName);
    }

    @Override
    public List<String> getSimilarName(String industryName) {
        if (StringUtils.isBlank(industryName)) {
            return Collections.emptyList();
        }
        List<IndustryEntity> list = selectList(null);
        return list.stream().filter(industry -> TextUtils.similar(industryName, industry.getIndustryName()) > 0.66)
                .map(IndustryEntity::getIndustryName).collect(Collectors.toList());
    }
}
