package com.ruoyi.system.service.portal.impl;

import com.ruoyi.system.entity.portal.PortalPublishEntity;
import com.ruoyi.system.mapper.portal.PortalPublishMapper;
import com.ruoyi.system.service.portal.PortalPublishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 官网发布信息表 Service
 *
 * <AUTHOR>
 * @date 2022-9-27 13:58:33
 */
@Service
public class PortalPublishServiceImpl implements PortalPublishService {
    @Autowired
    private PortalPublishMapper portalPublishMapper;

    @Override
    public Boolean insert(PortalPublishEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return portalPublishMapper.insert(entity) > 0;
    }

    @Override
    public List<PortalPublishEntity> listByType(Integer type) {
        return portalPublishMapper.listByType(type);
    }
}
