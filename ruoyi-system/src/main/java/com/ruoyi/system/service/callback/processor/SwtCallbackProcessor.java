package com.ruoyi.system.service.callback.processor;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.SWT;

/**
 * 三维推上报处理器
 *
 * <AUTHOR>
 * @date 2023/10/31
 */
@Slf4j
@Service
public class SwtCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final String API_URL = "http://api.interactive.xianyujoy.cn/statAdEvent";
    private static final String AD_KEY = "231031163033280";

    @Override
    public CallbackProcessorTypeEnum getType() {
        return SWT;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("requestId")) && StringUtils.isBlank(param.getString("creativeId"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("requestId", context.getParam().getSlotParam().getString("requestId"));
            map.put("eventType", "registered"); // 事件类型: open_page.落地页打开,registered.注册, pay.支付
            map.put("referrerUrl", "Unknown");
            map.put("device", NumberUtils.defaultLong(context.getOrder().getConsumerId()));
            map.put("ip", "127.0.0.1");
            map.put("ua", "Android");
            map.put("sign", sign(map));

            String resp = HttpUtil.post(API_URL, map);
            log.info("{}接口上报, req={}, resp={}", getType().getName(), JSON.toJSONString(map), resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getString("code"), "000000")) {
                return true;
            }
            log.error("{}接口上报失败, req={}, resp={}", getType().getName(), JSON.toJSONString(map), resp);
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }

    /**
     * 签名
     */
    private String sign(Map<String, Object> map) {
        return SecureUtil.md5(map.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&")) + "&adKey=" + AD_KEY).toUpperCase();
    }
}
