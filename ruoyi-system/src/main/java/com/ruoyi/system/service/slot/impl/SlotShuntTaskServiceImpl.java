package com.ruoyi.system.service.slot.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.slot.SlotShuntTaskBO;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slot.SlotShuntTask;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.mapper.manager.SlotShuntTaskMapper;
import com.ruoyi.system.req.slot.shunt.SlotShuntTaskParam;
import com.ruoyi.system.service.slot.SlotShuntTaskService;
import com.ruoyi.system.util.ShuntUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.slot.SlotShuntStatusEnum.*;
import static com.ruoyi.common.enums.slot.SlotShuntType.isPvShunt;
import static com.ruoyi.common.enums.slot.SlotShuntType.isUvShunt;

/**
 * 广告位切量计划Service实现
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
@Slf4j
@Service
public class SlotShuntTaskServiceImpl implements SlotShuntTaskService {

    @Autowired
    private SlotShuntTaskMapper slotShuntTaskMapper;

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    /**
     * 切量计划缓存
     */
    private final LoadingCache<Pair<Long, String>, List<SlotShuntTaskBO>> TASK_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Pair<Long, String>, List<SlotShuntTaskBO>>() {
                @Override
                public List<SlotShuntTaskBO> load(Pair<Long, String> key) {
                    String value = redisCache.getCacheObject(EngineRedisKeyFactory.K030.join(key.getKey(), key.getValue()));
                    if (StringUtils.isNotBlank(value)) {
                        List<SlotShuntTaskBO> tasks = JSON.parseArray(value, SlotShuntTaskBO.class);
                        if (null != tasks) {
                            return tasks;
                        }
                    }
                    return Collections.emptyList();
                }

                @Override
                public ListenableFuture<List<SlotShuntTaskBO>> reload(Pair<Long, String> key, List<SlotShuntTaskBO> oldValue) {
                    ListenableFutureTask<List<SlotShuntTaskBO>> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    @Override
    public SlotShuntTaskBO shunt(Long appId, Long slotId, String deviceId) {
        try {
            List<SlotShuntTaskBO> tasks = TASK_CACHE.get(Pair.of(slotId, DateUtil.today()));
            if (CollectionUtils.isEmpty(tasks)) {
                return null;
            }
            Date now = new Date();
            tasks = tasks.stream().filter(task -> {
                boolean result = !now.before(task.getStartTime()) && task.getEndTime().after(now);
                if (!result) {
                    return false;
                }
                Long current = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K031.join(task.getId())));
                return current + 1 <= task.getThreshold();
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tasks)) {
                return null;
            }

            Integer shuntType = tasks.get(0).getShuntType();
            if (isPvShunt(shuntType)) {
                return ShuntUtils.shunt(tasks, RandomUtil.randomInt(0, 100));
            } else if (isUvShunt(shuntType)) {
                return ShuntUtils.shunt(tasks, Math.abs((deviceId + appId).hashCode()));
            }
        } catch (Exception e) {
            log.error("广告位切量计划分流异常, slotId={}, deviceId={}", slotId, deviceId, e);
        }
        return null;
    }

    @Override
    public List<SlotShuntTask> selectList(SlotShuntTaskParam param) {
        return slotShuntTaskMapper.selectList(param);
    }

    @Override
    public int countByParam(SlotShuntTaskParam param) {
        return slotShuntTaskMapper.countByParam(param);
    }

    @Override
    public SlotShuntTask selectById(Long id) {
        if (null == id) {
            return null;
        }
        return slotShuntTaskMapper.selectById(id);
    }

    @Override
    public Map<Long, Integer> selectSlotShuntMap(List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }

        SlotShuntTaskParam param = new SlotShuntTaskParam();
        param.setSlotIds(slotIds);
        param.setTaskStatus(EXECUTE.getStatus());
        List<SlotShuntTask> list = slotShuntTaskMapper.selectList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(SlotShuntTask::getSlotId, s -> 1, (v1, v2) -> v1));
    }

    @Override
    public int addTask(SlotShuntTask param) {
        Slot slot = slotMapper.selectSlotById(param.getSlotId());
        if (null == slot) {
            return 0;
        }
        param.setRedirectValue(StrUtil.trim(param.getRedirectValue()));
        return slotShuntTaskMapper.insert(param);
    }

    @Override
    public int updateTask(SlotShuntTask param) {
        SlotShuntTask task = slotShuntTaskMapper.selectById(param.getId());
        if (null == task || !canModify(task.getTaskStatus())) {
            return 0;
        }
        Slot slot = slotMapper.selectSlotById(param.getSlotId());
        if (null == slot) {
            return 0;
        }
        param.setRedirectValue(StrUtil.trim(param.getRedirectValue()));
        return slotShuntTaskMapper.update(param);
    }

    @Override
    public int executeTask(Long id) {
        if (null == id) {
            return 0;
        }
        SlotShuntTask task = slotShuntTaskMapper.selectById(id);
        if (null == task || !canExecute(task.getTaskStatus())) {
            return 0;
        }
        SlotShuntTask param = new SlotShuntTask();
        param.setId(id);
        param.setTaskStatus(EXECUTE.getStatus());
        param.setExecuteTime(new Date());
        int result = slotShuntTaskMapper.update(param);

        // 更新缓存
        updateTaskCache(task.getSlotId());
        return result;
    }

    @Override
    public int finishTask(Long id) {
        if (null == id) {
            return 0;
        }
        SlotShuntTask task = slotShuntTaskMapper.selectById(id);
        if (null == task || !canFinish(task.getTaskStatus())) {
            return 0;
        }

        // 更新任务状态
        SlotShuntTask param = new SlotShuntTask();
        param.setId(id);
        param.setTaskStatus(TERMINATED.getStatus());
        param.setFinishTime(new Date());
        int result = slotShuntTaskMapper.update(param);

        // 更新缓存
        updateTaskCache(task.getSlotId());
        return result;
    }

    @Override
    public int cancelTask(Long id) {
        if (null == id) {
            return 0;
        }
        SlotShuntTask task = slotShuntTaskMapper.selectById(id);
        if (null == task || !canCancel(task.getTaskStatus())) {
            return 0;
        }

        // 更新任务状态
        SlotShuntTask param = new SlotShuntTask();
        param.setId(id);
        param.setTaskStatus(TERMINATED.getStatus());
        param.setCancelTime(new Date());
        int result = slotShuntTaskMapper.update(param);

        // 更新缓存
        updateTaskCache(task.getSlotId());
        return result;
    }

    @Override
    public int deleteTask(Long id) {
        if (null == id) {
            return 0;
        }
        SlotShuntTask task = slotShuntTaskMapper.selectById(id);
        if (null == task || !canDelete(task.getTaskStatus())) {
            return 0;
        }

        // 更新计划状态
        SlotShuntTask param = new SlotShuntTask();
        param.setId(id);
        param.setTaskStatus(DELETED.getStatus());
        return slotShuntTaskMapper.update(param);
    }

    @Override
    public Integer selectUsedShuntType(Long slotId, Date date) {
        if (null == slotId) {
            return null;
        }
        if (null == date) {
            date = new Date();
        }

        SlotShuntTaskParam param = new SlotShuntTaskParam();
        param.setSlotId(slotId);
        param.setStartDate(DateUtil.beginOfDay(date));
        param.setEndDate(DateUtil.endOfDay(date));
        param.setTaskStatusList(Arrays.asList(READY.getStatus(), EXECUTE.getStatus()));
        List<SlotShuntTask> tasks = slotShuntTaskMapper.selectList(param);
        return CollectionUtils.isNotEmpty(tasks) ? tasks.get(tasks.size() - 1).getShuntType() : null;
    }

    @Override
    public Integer selectUsedShuntRatio(Long slotId, Date date) {
        if (null == slotId) {
            return 0;
        }
        if (null == date) {
            date = new Date();
        }

        SlotShuntTaskParam param = new SlotShuntTaskParam();
        param.setSlotId(slotId);
        param.setStartDate(DateUtil.beginOfDay(date));
        param.setEndDate(DateUtil.endOfDay(date));
        param.setTaskStatusList(Arrays.asList(READY.getStatus(), EXECUTE.getStatus()));
        List<SlotShuntTask> list = slotShuntTaskMapper.selectList(param);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return list.stream().mapToInt(SlotShuntTask::getShuntRatio).sum();
    }

    @Override
    public List<SlotShuntTask> selectTotalValidList() {
        return slotShuntTaskMapper.selectTotalValidList();
    }

    @Override
    public List<SlotShuntTask> selectExecuteList(Long slotId) {
        return slotShuntTaskMapper.selectExecuteList(slotId);
    }

    /**
     * 更新计划缓存
     *
     * @param slotId 广告位ID
     */
    private void updateTaskCache(Long slotId) {
        String key = EngineRedisKeyFactory.K030.join(slotId, DateUtil.today());
        List<SlotShuntTask> tasks = selectExecuteList(slotId);
        if (CollectionUtils.isEmpty(tasks)) {
            redisCache.deleteObject(key);
            return;
        }
        List<SlotShuntTaskBO> taskList = tasks.stream().map(task -> {
            SlotShuntTaskBO taskBO = BeanUtil.copyProperties(task, SlotShuntTaskBO.class);
            taskBO.setRatio(task.getShuntRatio());
            return taskBO;
        }).collect(Collectors.toList());

        redisCache.setCacheObject(key, JSON.toJSONString(taskList), 1, TimeUnit.DAYS);
    }
}
