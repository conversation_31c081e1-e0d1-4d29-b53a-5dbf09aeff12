package com.ruoyi.system.service.engine.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.service.engine.SlotUpService;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 固定收益上报接口实现
 *
 * <AUTHOR>
 * @date 2023/7/27
 */
@Slf4j
@Service
public class SlotUpServiceImpl implements SlotUpService {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private SlotCacheService slotCacheService;

    @Override
    public void slotUpAddCostMulti(Order order, String hu) {
        if (null == order || null == order.getSlotId()) {
            return;
        }

        try {
            Long slotId = order.getSlotId();
            String date = DateUtil.today();
            hu = StringUtils.defaultString(hu);

            // 冷启动数量：默认1
            int coldStart = getColdStart(slotId);

            // 广告位：落地页转化个数缓存+1
            redisAtomicClient.incrBy(EngineRedisKeyFactory.K065.join(date, slotId, hu), 1, 1, TimeUnit.DAYS);
            long slotAdUp = redisAtomicClient.incrBy(EngineRedisKeyFactory.K065.join(slotId, hu), 1, 90, TimeUnit.DAYS);  // 历史累计转化

            // 落地页转化产生的理论消耗缓存(包含冷启动期)
            Integer assessCost = getAssessCost(order.getOrientId());
            redisAtomicClient.incrBy(EngineRedisKeyFactory.K079.join(date, order.getSlotId(), order.getAdvertId(), hu), assessCost, 1, TimeUnit.DAYS);

            // 广告位上：转化的个数【起步1】>=冷启动个数【默认1】【一般不会触发】
            if (slotAdUp < coldStart) {
                order.setSkipCoolFlag(false);
                return;
            }

            // 上报个数足够，已经度过冷启动了
            order.setSkipCoolFlag(true);

            // 落地页转化产生的理论消耗缓存(不包含冷启动期)
            redisAtomicClient.incrBy(EngineRedisKeyFactory.K077.join(date, order.getSlotId(), hu), assessCost, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("slotUpAddCostMulti error, orderId={}, slotId={}, advertId={}, hu={}", order.getOrderId(), order.getSlotId(), order.getAdvertId(), hu, e);
        }
    }

    /**
     * 获取冷启动数量(默认1)
     *
     * @param slotId 广告位ID
     * @return 冷启动数量
     */
    private Integer getColdStart(Long slotId) {
        return NumberUtils.defaultInt(slotCacheService.getSlotBiddingConfigCache(slotId).getColdStart(), 1);
    }

    /**
     * 获取广告考核成本(分)
     *
     * @param orientId 配置ID
     * @return 考核成本
     */
    private Integer getAssessCost(Long orientId) {
        AdvertCacheDto advertCacheDto = advertCacheService.queryAdvert(orientId);
        return null != advertCacheDto ? advertCacheDto.getAssessCost() : 0;
    }
}
