package com.ruoyi.system.service.open.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.open.VivoConfigEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.service.open.VivoService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.open.VivoResp;
import com.ruoyi.system.vo.open.VivoTokenData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.open.VivoClientEnum.getClientSecretByClientId;
import static com.ruoyi.common.enums.open.VivoConfigEnum.getConfig;

/**
 * Vivo对接Service接口实现
 * 配置: {@link com.ruoyi.common.enums.open.VivoConfigEnum}
 *
 * <AUTHOR>
 * @date 2022/05/23
 */
@Slf4j
@Service
public class VivoServiceImpl implements VivoService {

    private static final String OAUTH_URL = "https://open-ad.vivo.com.cn/OAuth";
    private static final String CALLBACK_URL = UrlUtils.urlEncode("https://actengine.ydns.cn/open/oauth2/vivo/callback");
    private static final String TOKEN_URL = "https://marketing-api.vivo.com.cn/openapi/v1/oauth2/token";
    private static final String REFRESH_TOKEN = "https://marketing-api.vivo.com.cn/openapi/v1/oauth2/refreshToken";
    private static final String BEHAVIOR_UPLOAD_URL = "https://marketing-api.vivo.com.cn/openapi/v1/advertiser/behavior/upload";

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SlotMapper slotMapper;

    @Override
    public void getAndCacheToken(String client, String code, String state) {
        String resp = HttpUtil.get(TOKEN_URL + "?client_id=" + client + "&client_secret=" + getClientSecretByClientId(client) + "&grant_type=code&code=" + code);
        log.info("vivo获取token, client={}, code={}, resp={}", client, code, resp);

        VivoResp<VivoTokenData> result = JSON.parseObject(resp, new TypeReference<VivoResp<VivoTokenData>>() {});
        if (null == result || !Objects.equals(result.getCode(), 0)) {
            return;
        }
        // 缓存token信息
        redisCache.setCacheObject(CrmRedisKeyFactory.K020.join(client, state), JSON.toJSONString(result.getData()), 365, TimeUnit.DAYS);
        redisCache.setCacheObject(CrmRedisKeyFactory.K030.join(state), client, 365, TimeUnit.DAYS);
    }

    @Override
    public String getAccessToken(String clientId, String advertiserId) {
        String key = CrmRedisKeyFactory.K020.join(clientId, advertiserId);
        VivoTokenData data = JSON.parseObject(redisCache.getCacheObject(key), VivoTokenData.class);
        if (null != data) {
            if (System.currentTimeMillis() >= data.getTokenDate() && StringUtils.isNotBlank(data.getRefreshToken())) {
                refreshToken(clientId, data.getRefreshToken(), advertiserId);
                data = JSON.parseObject(redisCache.getCacheObject(key), VivoTokenData.class);
            }
        }
        if (null != data) {
            Long refreshTokenDate = data.getRefreshTokenDate();
            GlobalThreadPool.executorService.submit(() -> {
                if (System.currentTimeMillis() >= refreshTokenDate + 3600000 * 24) {
                    String sbr = "vivo回传将在24小时后失效，请重新授权登录\n" +
                            "\n地址: " + OAUTH_URL + "?clientId=" + clientId + "&state=" + advertiserId + "&redirectUri=" + CALLBACK_URL;
                    DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sbr);
                }
            });
            return data.getAccessToken();
        }
        return null;
    }

    @Override
    public VivoTokenData refreshToken(String clientId, String refreshToken, String advertiserId) {
        String clientSecret = getClientSecretByClientId(clientId);
        String resp = HttpUtil.get(REFRESH_TOKEN + "?client_id=" + clientId + "&client_secret=" + clientSecret + "&refresh_token=" + refreshToken);
        log.info("vivo刷新token, refreshToken={}, resp={}", refreshToken, resp);

        VivoResp<VivoTokenData> result = JSON.parseObject(resp, new TypeReference<VivoResp<VivoTokenData>>() {});
        if (null == result || !Objects.equals(result.getCode(), 0)) {
            return null;
        }
        // 缓存token信息
        redisCache.setCacheObject(CrmRedisKeyFactory.K020.join(clientId, advertiserId), JSON.toJSONString(result.getData()), 365, TimeUnit.DAYS);
        redisCache.setCacheObject(CrmRedisKeyFactory.K030.join(advertiserId), clientId, 365, TimeUnit.DAYS);
        return result.getData();
    }

    @Override
    public String behaviorUpload(String orderId, Long slotId, String requestId, String creativeId, String adextra) {
        VivoConfigEnum config = getConfig(adextra);
        String clientId = redisCache.getCacheObject(CrmRedisKeyFactory.K030.join(adextra));
        if (StringUtils.isBlank(clientId)) {
            log.error("vivo获取clientId失败, slotId={}, adextra={}", slotId, adextra);
            return null;
        }

        String accessToken = getAccessToken(clientId, adextra);
        if (StringUtils.isBlank(accessToken)) {
            log.error("vivo行为数据上报失败，accessToken为空, clientId={}, orderId={}", clientId, orderId);
            return null;
        }

        long timestamp = System.currentTimeMillis();

        JSONObject param = new JSONObject();
        param.put("srcType", "Web");
        param.put("srcId", config.getSrcId());
        param.put("pageUrl", getSlotUrl(slotId));

        JSONObject ele = new JSONObject();
        ele.put("cvType", "SUBMIT");
        ele.put("cvTime", timestamp);
        ele.put("requestId", requestId);
        ele.put("creativeId", creativeId);

        JSONArray arr = new JSONArray();
        arr.add(ele);
        param.put("dataList", arr);

        String url = BEHAVIOR_UPLOAD_URL + "?access_token=" + accessToken + "&timestamp=" + timestamp + "&nonce=" + orderId + "&advertiser_id=" + adextra;
        String resp = HttpUtil.post(url, param.toString());
        log.info("vivo接口上报, orderId={}, slotId={}, param={}, resp={}", orderId, slotId, param, resp);
        return resp;
    }

    /**
     * 获取广告位链接
     */
    private String getSlotUrl(Long slotId) {
        Slot slot = slotMapper.selectSlotById(slotId);
        return null != slot ? slot.getSlotUrl() : "";
    }
}
