package com.ruoyi.system.service.oa.permission;


import com.ruoyi.system.entity.oa.permission.PermissionEntity;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 权限表 Service
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:18
 */
public interface PermissionService {


    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    PermissionEntity selectById(Long id);

    /**
     * 查询系统的所有权限
     *
     * @param systemId 系统ID
     * @return 权限列表
     */
    List<PermissionEntity> selectBySystemId(Long systemId);

    /**
     * 根据权限ID查询其所有子权限ID
     *
     * @param parentId 父权限ID
     * @return 子权限ID列表
     */
    List<Long> selectByParentId(Long parentId);

    /**
     * 查询权限所有的父权限ID列表(除根节点外)
     *
     * @param ids 权限ID列表
     * @return 父权限ID列表
     */
    List<Long> selectParentIdsByIds(List<Long> ids);

    /**
     * 查询系统的所有权限并分组
     *
     * @param systemId 系统ID
     * @return 权限ID-子权限列表
     */
    Map<Long, List<PermissionEntity>> selectParentMapBySystemId(Long systemId);

    /**
     * 根据id列表查询权限key列表
     * @param ids
     * @return
     */
    Set<String> selectPermissionKeyByIds(List<Long> ids);
}
