package com.ruoyi.system.service.fc.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.BizConstants;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.fc.FcMaterialReq;
import com.ruoyi.common.utils.fc.FcMaterialResponse;
import com.ruoyi.common.utils.fc.FcUtil;
import com.ruoyi.system.bo.landpage.article.ArticleCacheBo;
import com.ruoyi.system.entity.fc.FcLinkEntity;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.mapper.fc.FcLinkMapper;
import com.ruoyi.system.req.fc.FcCheckArticleReq;
import com.ruoyi.system.req.fc.GetArticleListReq;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.datasource.processor.FcLinkDayProcessor;
import com.ruoyi.system.service.fc.FcLinkArticleAggrRelService;
import com.ruoyi.system.service.fc.FcLinkService;
import com.ruoyi.system.service.landpage.article.ArticleService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.vo.fc.FcArticleVo;
import com.ruoyi.system.vo.fc.FcLinkVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 丰巢聚合链接表(FcLink)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-05 15:38:00
 */
@Service
@Slf4j
public class FcLinkServiceImpl implements FcLinkService {
    @Resource
    private FcLinkMapper fcLinkMapper;
    @Resource
    private RedisCache redisCache;
    @Resource
    private ArticleService articleService;
    @Resource
    private FcLinkArticleAggrRelService fcLinkArticleAggrRelService;
    @Resource
    private ISysConfigService sysConfigService;
    @Autowired
    private DataStatService dataStatService;
    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public FcLinkEntity queryById(Long id) {
        return this.fcLinkMapper.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param fcLinkEntity      筛选条件
     * @param pageRequest 分页对象
     * @return 查询结果
     */
    @Override
    public Page<FcLinkEntity> queryByPage(FcLinkEntity fcLinkEntity, PageRequest pageRequest) {
        long total = this.fcLinkMapper.count(fcLinkEntity);
        return new PageImpl<>(this.fcLinkMapper.queryAllByLimit(fcLinkEntity, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     *
     * @param fcLinkEntity 实例对象
     * @return 实例对象
     */
    @Override
    public FcLinkEntity insert(FcLinkEntity fcLinkEntity) {
        this.fcLinkMapper.insert(fcLinkEntity);
        return fcLinkEntity;
    }

    /**
     * 修改数据
     *
     * @param fcLinkEntity 实例对象
     * @return 实例对象
     */
    @Override
    public FcLinkEntity update(FcLinkEntity fcLinkEntity) {
        this.fcLinkMapper.update(fcLinkEntity);
        return this.queryById(fcLinkEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.fcLinkMapper.deleteById(id) > 0;
    }

    @Override
    public Result addFcLink(String name) {
        LoginUser operator = SecurityUtils.getLoginUser();
        String key = generateKey();
        String url = sysConfigService.selectConfigCacheByKey(BizConfigEnum.DEFAULT_FC_URL.getKey()) + "/fc/wz/" + key;
        FcLinkEntity fcLinkEntity = new FcLinkEntity();
        fcLinkEntity.setName(name);
        fcLinkEntity.setKey(key);
        fcLinkEntity.setUrl(url);
        fcLinkEntity.setCreatorId(operator.getCrmAccountId());
        fcLinkEntity.setOperatorId(operator.getCrmAccountId());
        fcLinkMapper.insert(fcLinkEntity);
        return ResultBuilder.success();
    }

    @Override
    public String generateKey() {
        Set<String> existKeys = fcLinkMapper.selectTotalKey();
        String key = RandomUtil.randomString(6);
        while (existKeys.contains(key)) {
            key = RandomUtil.randomString(6);
        }
        return key;
    }

    @Override
    public Result removeFcLink(Long id) {
        fcLinkMapper.updateStatusById(id, 1);
        return ResultBuilder.success();
    }

    @Override
    public ArticleCacheBo getArticleByfcLinkKey(String userId, String fcLinkKey) {
        if ( Objects.isNull(fcLinkKey)) {
            return null;
        }
        String today = DateUtil.today();
        String redisKey = EngineRedisKeyFactory.k149.join(today, fcLinkKey);
        String fcNoArticleRedisKey = EngineRedisKeyFactory.k154.join(today, fcLinkKey);
        Map<String, ArticleCacheBo> articleMap = redisCache.getCacheMap(redisKey);
        if (MapUtil.isEmpty(articleMap) && !redisCache.hasKey(redisKey) && !redisCache.hasKey(fcNoArticleRedisKey)) {
            // 重新构造缓存
            articleMap = initArticleCache(today, fcLinkKey);
        }
        if (MapUtil.isEmpty(articleMap)) {
            return null;
        }

        // 过滤重复曝光的文章
        Set<Long> exposureArticleIds = redisCache.getCacheSet(EngineRedisKeyFactory.K113.join(today, userId));
        if (CollectionUtils.isNotEmpty(exposureArticleIds)) {
            List<ArticleCacheBo> tmpArticleList = articleMap.values().stream()
                    .filter(article -> !exposureArticleIds.contains(article.getId()))
                    .collect(Collectors.toList());
            // 随机获取未曝光文章
            if (CollectionUtils.isNotEmpty(tmpArticleList) && tmpArticleList.size() < articleMap.values().size()) {
                return getRandomArticle(tmpArticleList);
            }
        }
        // 随机获取文章
        return getRandomArticle(articleMap.values());
    }

    @Override
    public void deleteFcLinkArticleCache(String fcLinkKey) {
        if ( StringUtils.isEmpty(fcLinkKey) ) {
            return;
        }
        String today = DateUtil.today();
        String redisKey = EngineRedisKeyFactory.k149.join(today, fcLinkKey);
        redisCache.deleteObject(redisKey);
    }

    @Override
    public void deleteFcLinkAggrLinkCaChe(String fcLinkKey) {
        if ( StringUtils.isEmpty(fcLinkKey) ) {
            return;
        }
        String redisKey = EngineRedisKeyFactory.k150.join(fcLinkKey);
        redisCache.deleteObject(redisKey);
    }

    @Override
    public void deleteFcLinkArticleCacheByArticleId(Long articleId) {
        if ( Objects.isNull(articleId) ) {
            return;
        }
        Set<String> fcLinkIds = fcLinkArticleAggrRelService.selectFcLinkKeysByArticleId(articleId);
        if (!CollectionUtils.isEmpty(fcLinkIds)) {
            fcLinkIds.forEach(this::deleteFcLinkArticleCache);
        }
    }

    @Override
    public Result fcCheckArticle(FcCheckArticleReq fcCheckArticleReq) {
        if ( Objects.isNull(fcCheckArticleReq) || Objects.isNull(fcCheckArticleReq.getCheckType())
                || Objects.isNull(fcCheckArticleReq.getArticleId()) ) {
            return ResultBuilder.paramFail();
        }
        if ( Objects.equals(fcCheckArticleReq.getCheckType(), 2) && StringUtils.isEmpty(fcCheckArticleReq.getRefuseReason()) ) {
            return ResultBuilder.paramFail();
        }

        ArticleEntity articleEntity = articleService.selectById(fcCheckArticleReq.getArticleId());
        if ( Objects.isNull(articleEntity) ) {
            return ResultBuilder.fail("未查询到该文章");
        }
        if ( !Objects.equals(articleEntity.getFcCheckStatus() , 0) ) {
            return ResultBuilder.fail("文章状态异常，请刷新重试！");
        }
        // 通过
        if ( Objects.equals(fcCheckArticleReq.getCheckType(), 1) ) {
            articleService.updateFcCheckStatus(fcCheckArticleReq.getArticleId(), 1, null);

            if  ( !Objects.equals(articleEntity.getFcSyncStatus(), 1) ) {
                String syncResult = syncFcArticle(articleEntity);
                boolean isSuccess = Objects.equals(syncResult, "同步成功");
                articleService.updateFcSyncStatus(fcCheckArticleReq.getArticleId(), 
                    isSuccess ? 1 : 2, 
                    isSuccess ? null : syncResult);
                // 同步成功删除丰巢文章缓存
                if ( isSuccess ) {
                    deleteFcLinkArticleCacheByArticleId(fcCheckArticleReq.getArticleId());
                }
            }
        // 拒绝
        }else if ( Objects.equals(fcCheckArticleReq.getCheckType(), 2) ) {
            articleService.updateFcCheckStatus(fcCheckArticleReq.getArticleId(), 2, fcCheckArticleReq.getRefuseReason());
        }
        return ResultBuilder.success();
    }

    /**
     * 同步文章到丰巢
     * @param articleEntity 文章实体
     * @return 同步结果
     */
    private String syncFcArticle(ArticleEntity articleEntity) {
        String cacheFcToken = getCacheFcToken();
        if (StringUtils.isEmpty(cacheFcToken)) {
            return "token获取失败";
        }

        FcMaterialReq fcMaterialReq = new FcMaterialReq(BizConstants.FC_MATERIAL_ID_PREFIX + articleEntity.getId(), articleEntity.getName(), articleEntity.getUrl());
        try {
            FcMaterialResponse fcMaterialResponse = FcUtil.addPdbIdeaMaterial(cacheFcToken, Collections.singletonList(fcMaterialReq));
            if (fcMaterialResponse == null) {
                return "同步失败：响应为空";
            }

            // 检查响应码
            if (!"501000".equals(fcMaterialResponse.getCode()) && !"0".equals(fcMaterialResponse.getCode())) {
                return "同步失败：" + fcMaterialResponse.getChnDesc();
            }

            // 检查数据
            if (fcMaterialResponse.getData() == null) {
                return "同步失败：响应数据为空";
            }

            // 检查业务响应码
            if ( !"0".equals(fcMaterialResponse.getData().getCode()) ) {
                return "同步失败：" + fcMaterialResponse.getData().getMsg();
            }

            // 检查内层数据
            if (fcMaterialResponse.getData().getData() == null) {
                return "同步失败：内层响应数据为空";
            }

            // 检查失败数量
            if (Objects.nonNull(fcMaterialResponse.getData().getData().getFailCount()) && fcMaterialResponse.getData().getData().getFailCount() > 0) {
                // 获取失败详情
                List<FcMaterialResponse.FailDetailInfo> failDetails = fcMaterialResponse.getData().getData().getFailDetailInfo();
                if (failDetails != null && !failDetails.isEmpty()) {
                    String failReason = failDetails.get(0).getFailReason();
                    return StringUtils.isEmpty(failReason) ? "同步失败：未知原因" : "同步失败：" + failReason;
                }
                return "同步失败：未知原因";
            }

            // 检查成功数量
            if ( Objects.nonNull(fcMaterialResponse.getData().getData().getSuccessCount()) &&  fcMaterialResponse.getData().getData().getSuccessCount() > 0) {
                return "同步成功";
            }

            return "同步失败：未成功同步任何数据";
        } catch (Exception e) {
            log.error("同步文章到丰巢失败", e);
            return "请联系开发";
        }
    }

    /**
     * 获取丰巢token
     * @return
     */
    private String getCacheFcToken() {
        String redisKey = EngineRedisKeyFactory.k151.join();
        String token  = redisCache.getCacheObject(redisKey);
        if ( !StringUtils.isEmpty(token) ) {
            return token;
        }
        Map<String, String> tokenResponse = FcUtil.getToken();
        if ( Objects.isNull(tokenResponse) || StringUtils.isEmpty(tokenResponse.get(FcUtil.ACCESS_TOKEN)) || StringUtils.isEmpty(tokenResponse.get(FcUtil.EXPIRES_IN)) ) {
            return null;
        }
        Integer timeOut = Integer.parseInt(tokenResponse.get(FcUtil.EXPIRES_IN)) >  100 ?
                Integer.parseInt(tokenResponse.get(FcUtil.EXPIRES_IN)) - 100 : Integer.parseInt(tokenResponse.get(FcUtil.EXPIRES_IN));
        redisCache.setCacheObject(redisKey, tokenResponse.get(FcUtil.ACCESS_TOKEN), timeOut, TimeUnit.SECONDS);
        return tokenResponse.get(FcUtil.ACCESS_TOKEN);
    }

    @Override
    public List<String> getFcLinkKeysByStatus(Integer status) {
        return fcLinkMapper.getFcLinkKeysByStatus(status);
    }

    @Override
    public List<FcArticleVo> getArticleByStatusAndTime(GetArticleListReq req) {
        return fcLinkMapper.getArticleByStatusAndTime(req);
    }

    @Override
    public List<FcLinkVo> getFcLinkListOrderByIdDesc(String searchKey) {
        return fcLinkMapper.getFcLinkListOrderByIdDesc(searchKey);
    }

    @Override
    public List<FcLinkVo> getFcLinkInfoByArticleAggrLinkIdList(List<Long> articleAggrLinkIdList, List<Long> fcLinkIdList) {
        return fcLinkMapper.getFcLinkInfoByArticleAggrLinkIdList(articleAggrLinkIdList, fcLinkIdList);
    }

    @Override
    public void fcLinkStatistic(String fcLinkKey, String userId) {
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> map = new HashMap<>();
        map.put(FcLinkDayProcessor.FC_LINK_KEY, fcLinkKey);
        map.put(FcLinkDayProcessor.USER_ID, userId);
        jsonObject.put("otherParam",  map);
        dataStatService.handleAsync(InnerLogType.FC_LINK_REQUEST,jsonObject);
    }


    /**
     * 根据权重随机获取文章
     * @param articleList
     * @return
     */
    private ArticleCacheBo getRandomArticle(Collection<ArticleCacheBo> articleList) {
        if ( CollectionUtils.isEmpty(articleList) ) {
            return null;
        }
        int weightSum = articleList.stream().mapToInt(ArticleCacheBo::getWeight).sum();
        if ( weightSum <= 0 ) {
            return null;
        }
        int nonce = RandomUtil.randomInt(weightSum);// 生成 [0, weightSum) 的随机数

        for (ArticleCacheBo article : articleList) {
            if (nonce < article.getWeight()) {
                return article;
            }
            nonce -= article.getWeight();
        }
        return null;
    }



    /**
     * 初始化丰巢链接的文章缓存
     * @param today
     * @param fcLinkKey
     * @return
     */
    private Map<String, ArticleCacheBo> initArticleCache(String today, String fcLinkKey) {
        // 获取聚合链接集合
        List<Long> articleAggrLinkIdList = getArticleAggrLinkIdListByFcLinkKey(fcLinkKey);
        if ( CollectionUtils.isEmpty(articleAggrLinkIdList) ) {
            cacheFcNoArticle(today, fcLinkKey);
            return null;
        }
        // 查询文章列表
        List<ArticleEntity> articles = articleService.selectListByLinkIds(articleAggrLinkIdList);
        if (CollectionUtils.isEmpty(articles)) {
            cacheFcNoArticle(today, fcLinkKey);
            return null;
        }
        // 构造文章缓存
        Map<String, Integer> articleRequestMap  = new HashMap<>();
        articleAggrLinkIdList.forEach(articleAggrLinkId -> {
            String redisKey = EngineRedisKeyFactory.K111.join(today, articleAggrLinkId);
            articleRequestMap.putAll(redisCache.getCacheMap(redisKey));
        });
        Map<String, ArticleCacheBo> articleMap = articles.stream()
                .filter(article -> {
                    if (!article.isOnline()) {
                        return false;
                    }
                    // 丰巢只返回同步成功的文章
                    if ( !Objects.equals(article.getFcSyncStatus(), 1) ) {
                        return false;
                    }
                    Integer requestPv = articleRequestMap.getOrDefault(String.valueOf(article.getId()), 0);
                    return requestPv < article.getTargetRequestPv() + NumberUtils.defaultInt(article.getCompensateRequestPv());
                })
                .collect(Collectors.toMap(article -> String.valueOf(article.getId()), ArticleEntity::getCacheBo, (o, n) -> n));
        String redisKey = EngineRedisKeyFactory.k149.join(today, fcLinkKey);
        redisCache.setCacheMap(redisKey, articleMap);
        redisCache.expire(redisKey, 1, TimeUnit.HOURS);
        if ( MapUtil.isEmpty(articleMap) ) {
            cacheFcNoArticle(today, fcLinkKey);
        }
        return articleMap;
    }

    /**
     * 缓存丰巢无文章可出标识
     * @param today
     * @param fcLinkKey
     */
    private void cacheFcNoArticle(String today, String fcLinkKey) {
        String redisKey = EngineRedisKeyFactory.k154.join(today, fcLinkKey);
        redisCache.setCacheObject(redisKey, "1");
        redisCache.expire(redisKey, 5, TimeUnit.MINUTES);
    }



    /**
     * 根据丰巢链接key获取聚合链接集合
     * @param fcLinkKey
     * @return
     */
    private List<Long> getArticleAggrLinkIdListByFcLinkKey(String fcLinkKey) {
        if ( Objects.isNull(fcLinkKey) ) {
            return null;
        }
        String redisKey = EngineRedisKeyFactory.k150.join(fcLinkKey);
        List<Long> cacheList = redisCache.getCacheList(redisKey);
        // 如果缓存为空且不存在该key，则重新构造缓存
        if ( CollectionUtils.isEmpty(cacheList) && !redisCache.hasKey(redisKey)) {
            cacheList = new ArrayList<>(fcLinkArticleAggrRelService.selectAggrIdsByFcLinkKey(fcLinkKey));
            // 只有当集合不为空时才存入Redis，避免Spring Redis rightPushAll方法的空集合异常
            if (CollectionUtils.isNotEmpty(cacheList)) {
                redisCache.setCacheList(redisKey, cacheList);
                redisCache.expire(redisKey, 2, TimeUnit.HOURS);
            }
        }
        return cacheList;
    }

    @Override
    public FcLinkEntity queryByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        return fcLinkMapper.queryByKey(key);
    }


}
