package com.ruoyi.system.service.callback.processor;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.SIGMOB;

/**
 * Sigmob上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
@Slf4j
@Service
public class SigmobCallbackProcessor implements CallbackProcessor, InitializingBean {

    @Autowired
    private RedisCache redisCache;

    @Override
    public CallbackProcessorTypeEnum getType() {
        return SIGMOB;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("get_callback")) || StringUtils.isNotBlank(param.getString("get_callback_md5"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            String getCallback = context.getParam().getSlotParam().getString("get_callback");
            if (StringUtils.isBlank(getCallback)) {
                String getCallbackMd5 = context.getParam().getSlotParam().getString("get_callback_md5");
                getCallback = redisCache.getCacheObject(EngineRedisKeyFactory.K122.join(getCallbackMd5));
            }

            HttpResponse resp = HttpUtil.createGet(getCallback).execute();
            log.info("{}接口上报, req={}, status={}", getType().getName(), getCallback, resp.getStatus());
            if (Objects.equals(resp.getStatus(), 200)) {
                return true;
            }
            log.error("{}接口上报失败, req={}, status={}, resp={}", getType().getName(), getCallback, resp.getStatus(), resp.body());
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
