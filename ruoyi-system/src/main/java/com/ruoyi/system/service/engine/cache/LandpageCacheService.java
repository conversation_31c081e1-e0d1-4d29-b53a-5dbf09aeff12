package com.ruoyi.system.service.engine.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.blindbox.BlindBoxLandpageEntity;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.service.blindbox.BlindBoxLandpageService;
import com.ruoyi.system.service.landpage.LandpageLibraryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 落地页缓存服务
 *
 * <AUTHOR>
 * @date 2022/09/16
 */
@Slf4j
@Service
public class LandpageCacheService {

    @Autowired
    private LandpageLibraryService landpageLibraryService;

    @Autowired
    private BlindBoxLandpageService blindBoxLandpageService;

    /**
     * 落地页缓存
     */
    private final LoadingCache<String, Optional<Landpage>> LANDPAGE_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .expireAfterWrite(12, TimeUnit.HOURS)
            .maximumSize(500)
            .build(new CacheLoader<String, Optional<Landpage>>() {
                @Override
                public Optional<Landpage> load(String key) {
                    Landpage landpage = landpageLibraryService.selectByKey(key);
                    return Optional.ofNullable(landpage);
                }

                @Override
                public ListenableFuture<Optional<Landpage>> reload(String lpk, Optional<Landpage> oldValue) {
                    ListenableFutureTask<Optional<Landpage>> task = ListenableFutureTask.create(() -> load(lpk));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 盲盒落地页缓存
     */
    private final LoadingCache<String, Optional<BlindBoxLandpageEntity>> BLINDBOX_LANDPAGE_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(500)
            .build(new CacheLoader<String, Optional<BlindBoxLandpageEntity>>() {
                @Override
                public Optional<BlindBoxLandpageEntity> load(String key) {
                    BlindBoxLandpageEntity landpage = blindBoxLandpageService.selectByLandpageKey(key);
                    return Optional.ofNullable(landpage);
                }

                @Override
                public ListenableFuture<Optional<BlindBoxLandpageEntity>> reload(String lpk, Optional<BlindBoxLandpageEntity> oldValue) {
                    ListenableFutureTask<Optional<BlindBoxLandpageEntity>> task = ListenableFutureTask.create(() -> load(lpk));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告主落地页缓存
     */
    private final LoadingCache<String, String> TARGET_LANDPAGE_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) {
                    BlindBoxLandpageEntity blindBoxLandpage = blindBoxLandpageService.selectByLandpageKey(key);
                    if (null != blindBoxLandpage) {
                        return StringUtils.defaultString(blindBoxLandpage.getTargetLandpage());
                    }
                    Landpage landpage = landpageLibraryService.selectByKey(key);
                    if (null != landpage) {
                        JSONObject pageConfig = JSON.parseObject(landpage.getPageConfig());
                        if (null != pageConfig) {
                            return StringUtils.defaultString(pageConfig.getString("targetLandpage"));
                        }
                    }
                    return "";
                }

                @Override
                public ListenableFuture<String> reload(String lpk, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(lpk));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 落地页缓存
     */
    private final LoadingCache<String, String> LANDPAGE_TAG_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .expireAfterWrite(12, TimeUnit.HOURS)
            .maximumSize(500)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) {
                    Landpage landpage = landpageLibraryService.selectByKey(key);
                    return Optional.ofNullable(landpage).map(Landpage::getTag).orElse(StringUtils.EMPTY);
                }

                @Override
                public ListenableFuture<String> reload(String lpk, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(lpk));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 落地页支付金额缓存
     */
    private final LoadingCache<String, Integer> LANDPAGE_PAY_MONEY_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .expireAfterWrite(12, TimeUnit.HOURS)
            .maximumSize(500)
            .build(new CacheLoader<String, Integer>() {
                @Override
                public Integer load(String key) {
                    Landpage landpage = landpageLibraryService.selectByKey(key);
                    if (null == landpage) {
                        return 0;
                    }
                    JSONObject pageConfig = JSON.parseObject(landpage.getPageConfig());
                    if (null == pageConfig || !pageConfig.getBooleanValue("paySwitch")) {
                        return 0;
                    }
                    return (int) (pageConfig.getDoubleValue("payMoney") * 100);
                }

                @Override
                public ListenableFuture<Integer> reload(String lpk, Integer oldValue) {
                    ListenableFutureTask<Integer> task = ListenableFutureTask.create(() -> load(lpk));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 根据标识查询落地页
     *
     * @param key 落地页标识
     * @return 落地页详情
     */
    public Landpage selectLandpageCache(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        try {
            return LANDPAGE_CACHE.get(key).orElse(null);
        } catch (Exception e) {
            log.error("查询落地页缓存异常, key={}", key, e);
        }
        return null;
    }

    /**
     * 根据标识查询落地页配置
     *
     * @param key 落地页标识
     * @return 落地页配置
     */
    public String selectLandpagePageConfigCache(String key) {
        Landpage landpage = selectLandpageCache(key);
        return null != landpage ? landpage.getPageConfig() : null;
    }

    /**
     * 根据标识查询盲盒落地页
     *
     * @param key 落地页标识
     * @return 落地页详情
     */
    public BlindBoxLandpageEntity selectBlindBoxLandpageCache(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        try {
            return BLINDBOX_LANDPAGE_CACHE.get(key).orElse(null);
        } catch (Exception e) {
            log.error("查询盲盒落地页缓存异常, key={}", key, e);
        }
        return null;
    }

    /**
     * 根据标识查询广告主落地页
     *
     * @param key 落地页标识
     * @return 广告主落地页
     */
    public String selectTargetLandpageCache(String key) {
        if (StringUtils.isNotBlank(key)) {
            try {
                return TARGET_LANDPAGE_CACHE.get(key);
            } catch (Exception e) {
                log.error("查询广告主落地页缓存异常, key={}", key, e);
            }
        }
        return "";
    }

    /**
     * 根据标识查询落地页支付金额
     *
     * @param key 落地页标识
     * @return 落地页支付金额
     */
    public Integer selectLandpagePayMoneyCache(String key) {
        if (StringUtils.isNotBlank(key)) {
            try {
                return LANDPAGE_PAY_MONEY_CACHE.get(key);
            } catch (Exception e) {
                log.error("查询落地页支付金额缓存异常, key={}", key, e);
            }
        }
        return 0;
    }

    /**
     * 根据标识查询落地页标签
     *
     * @param key 落地页标识
     * @return 落地页标签
     */
    public String getLandpageTag(String key) {
        if (StringUtils.isBlank(key)) {
            return StringUtils.EMPTY;
        }
        try {
            return LANDPAGE_TAG_CACHE.get(key);
        } catch (Exception e) {
            log.error("查询落地页缓存异常, key={}", key, e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 刷新落地页缓存
     *
     * @param key 落地页标识
     */
    public void refreshLandpageCache(String key) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        LANDPAGE_CACHE.refresh(key);
        LANDPAGE_TAG_CACHE.refresh(key);
        TARGET_LANDPAGE_CACHE.refresh(key);
        BLINDBOX_LANDPAGE_CACHE.refresh(key);
        LANDPAGE_PAY_MONEY_CACHE.refresh(key);
    }
}
