package com.ruoyi.system.service.oa.permission;

import com.ruoyi.system.entity.oa.permission.PostPermissionEntity;

/**
 * 职位权限关联表 Service
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:18
 */
public interface PostPermissionService {

    /**
     * 保存记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    Boolean save(PostPermissionEntity entity);

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    Boolean insert(PostPermissionEntity entity);

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    Boolean updateById(PostPermissionEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    PostPermissionEntity selectById(Long id);

    /**
     * 根据职位ID和系统ID查询权限
     *
     * @param postId 职位ID
     * @param systemId 系统ID
     * @return 职位权限
     */
    PostPermissionEntity selectByPostIdAndSystemId(Long postId, Long systemId);
}
