package com.ruoyi.system.service.callback.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.enums.open.VivoConfigEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.open.VivoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.VIVO;

/**
 * VIVO上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Slf4j
@Service
public class VivoCallbackProcessor implements CallbackProcessor, InitializingBean {

    @Autowired
    private VivoService vivoService;

    @Override
    public CallbackProcessorTypeEnum getType() {
        return VIVO;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("requestId")) && StringUtils.isNotBlank(param.getString("creativeId"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            Order order = context.getOrder();
            JSONObject param = context.getParam().getSlotParam();
            String requestId = param.getString("requestId");
            String creativeId = param.getString("creativeId");
            String adextra = param.getString("adextra");
            String hu = param.getString("hu");
            if (StringUtils.isBlank(adextra) && StringUtils.isNotBlank(hu) && !hu.startsWith("__")) {
                adextra = VivoConfigEnum.getAdvertiserIdByUuid(hu);
            }

            String resp = vivoService.behaviorUpload(order.getOrderId(), order.getSlotId(), requestId, creativeId, adextra);
            log.info("{}接口上报, requestId={}, creativeId={}, adextra={}, resp={}", getType().getName(), requestId, creativeId, adextra, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getInteger("code"), 0)) {
                return true;
            }
            log.error("{}接口上报失败, requestId={}, creativeId={}, adextra={}, resp={}", getType().getName(), requestId, creativeId, adextra, resp);
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
