package com.ruoyi.system.service.common;

import com.ruoyi.common.enums.common.WhitelistType;

import java.util.List;

/**
 * 白名单服务接口
 *
 * <AUTHOR>
 * @date 2021/9/9
 */
public interface WhitelistService {

    /**
     * 获取白名单列表所有元素(本地缓存)
     *
     * @param type 白名单类型
     * @return 白名单列表
     */
    <E> List<E> list(WhitelistType type);

    /**
     * 获取白名单列表所有元素
     *
     * @param type 白名单类型
     * @param clazz 元素类型
     * @return 白名单列表
     */
    <E> List<E> list(WhitelistType type, Class<E> clazz);

    /**
     * 判断白名单中是否存在
     *
     * @param type 白名单类型
     * @param e 元素
     * @return true.存在,false.不存在
     */
    <E> boolean contains(WhitelistType type, E e);

    /**
     * 添加白名单
     *
     * @param type 白名单类型
     * @param e 元素
     * @return true.添加成功,false.添加失败
     */
    <E> boolean add(WhitelistType type, E e);

    /**
     * 移除白名单
     *
     * @param type 白名单类型
     * @param e 元素
     * @return true.删除成功,false.删除失败
     */
    <E>boolean remove(WhitelistType type, E e);

    /**
     * 更新白名单
     *
     * @param type 白名单类型
     * @param list 白名单列表
     * @return true.更新成功,false.更新失败
     */
    <E> boolean update(WhitelistType type, List<E> list);

    /**
     * 清空白名单
     *
     * @param type 白名单类型
     * @return true.清空成功,false.清空失败
     */
    boolean clear(WhitelistType type);

    /**
     * 刷新白名单缓存
     *
     * @param key 白名单类型key
     */
    void refreshCache(String key);
}
