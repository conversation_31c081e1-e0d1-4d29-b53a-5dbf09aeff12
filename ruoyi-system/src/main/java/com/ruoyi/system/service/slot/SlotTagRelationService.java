package com.ruoyi.system.service.slot;

import java.util.List;
import java.util.Map;

/**
 * 广告位标签关联表 Service
 *
 * <AUTHOR>
 * @date 2023-5-9 17:00:13
 */
public interface SlotTagRelationService {

    /**
     * 查询广告位的标签数量
     *
     * @param slotIds 广告位ID列表
     * @return 广告位ID-标签数量映射
     */
    Map<Long, Integer> selectTagCountMapBySlotIds(List<Long> slotIds);

    /**
     * 根据广告位ID查询标签列表
     *
     * @param slotId 广告位ID
     * @return 标签ID列表
     */
    List<Long> selectTagIdsBySlotId(Long slotId);

    /**
     * 更新标签
     *
     * @param slotId 广告位ID
     * @param tagIds 标签ID列表
     * @return 是否更新成功
     */
    Boolean updateTags(Long slotId, List<Long> tagIds);

    /**
     * 根据标签名查询广告位ID列表
     * 注:不考虑标签重名的情况
     *
     * @param tagName 标签名称
     * @return 广告位ID列表
     */
    List<Long> selectSlotIdsByTagName(String tagName);
}
