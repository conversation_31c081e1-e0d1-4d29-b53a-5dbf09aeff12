package com.ruoyi.system.service.order;

import com.ruoyi.system.domain.order.Order;

import java.util.Date;
import java.util.List;

/**
 * 订单服务接口
 *
 * <AUTHOR>
 * @date 2021/8/18
 */
public interface OrderService {

    /**
     * 生成一个订单号
     * 规则:
     *  表后缀: consumerId % tbCount
     *  orderId生成规则: 分布式ID自增 + 表后缀
     *
     * @param consumerId 用户ID
     * @return 订单号
     */
    String generateOrderId(Long consumerId);

    /**
     * 查询用户的订单
     *
     * @param consumerId 用户ID
     * @param date 日期(该日期之后的所有订单)
     * @return 订单列表
     */
    List<Order> selectByConsumerIdAndDate(Long consumerId, Date date);

    /**
     * 根据订单号查询订单(缓存)
     *
     * @param orderId 订单号
     * @return 用户
     */
    Order selectByOrderId(String orderId);

    /**
     * 创建订单
     *
     * @param param 参数
     * @return 是否创建成功
     */
    boolean createOrder(Order param);

    /**
     * 更新订单
     *
     * @param param 参数
     * @return 是否更新成功
     */
    boolean updateOrder(Order param);

    /**
     * 缓存订单
     *
     * @param order 订单
     */
    void cacheOrder(Order order);

    /**
     * 判断是否是用户当日的首个订单号
     *
     * @param consumerId 用户ID
     * @param date 日期(该日期之后的所有订单)
     * @param orderId 订单号ID
     * @return 是否是用户当日的首个订单号
     */
    boolean isFirstOrderIdByConsumerIdAndDate(Long consumerId, Date date, String orderId);
}
