package com.ruoyi.system.service.domain.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.common.BizSwitchEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.service.domain.WxDomainService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static com.ruoyi.common.enums.domain.DomainStatus.isNormal;

/**
 * 微信域名接口实现
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Slf4j
@Service
public class WxDomainServiceImpl implements WxDomainService, InitializingBean {

    /**
     * 微信监控线程池(内存队列,抢占)
     */
    public static ExecutorService wxMonitorExecutor = new ThreadPoolExecutor(1, 1,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    /**
     * 微信监控线程池(哒哒检测，持久化队列)
     */
    public static ExecutorService wxCheckExecutor = new ThreadPoolExecutor(1, 1,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    /**
     * 微信监控线程池(ALAPI，持久化队列)
     */
    public static ExecutorService wxCheck2Executor = new ThreadPoolExecutor(1, 1,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    @Autowired
    private DomainService domainService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private StringRedisTemplate StringRedisTemplate;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Override
    public void addCheckQueue(String url) {
        if (StringUtils.isBlank(url)) {
            return;
        }
        StringRedisTemplate.opsForList().rightPush(EngineRedisKeyFactory.K090.toString(), url);
    }

    /**
     * 检查链接是否被微信拦截
     *
     * @param url 链接
     * @return true.拦截, false.正常
     */
    private boolean isWxBlock(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        // 非正式环境不检测
        if (!SpringEnvironmentUtils.isProd()) {
            return false;
        }
        // 过滤不检测的链接
        if (StrUtil.containsAnyIgnoreCase(url, "baicai")) {
            return false;
        }

        try {
            String resp = HttpUtil.get("http://wx.rrbay.com/pro/wxUrlCheck2.ashx?key=f118b4a75c506776047bd32ff2a63190&url=" + url);
            log.info("微信域名检测-哒哒检测, url={}, resp={}", url, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && BooleanUtil.isTrue(result.getBoolean("State"))) {
                if (Objects.equals(result.getString("Code"), "101")) {
                    return true;
                } else if (Objects.equals(result.getString("Code"), "102")) {
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("检查链接是否被微信拦截异常-哒哒检测, url={}", url, e);
        }

        try {
            HttpRequest request = HttpUtil.createGet("http://mp.weixinbridge.com/mp/wapredirect?url=" + url, true);
            String resp = request.execute().body();
            return StringUtils.isNotBlank(resp) && StrUtil.containsAny(resp, "已停止访问该网页", "非微信官方网页");
        } catch (IORuntimeException ignore) {
        } catch (Exception e) {
            log.error("检查链接是否被微信拦截异常-mp, url={}", url, e);
        }
        return false;
    }

    /**
     * 检查链接是否被微信拦截(ALAPI)
     *
     * @param url 链接
     * @return true.拦截, false.正常
     */
    private boolean isWxBlockByAlapi(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        // 非正式环境不检测
        if (!SpringEnvironmentUtils.isProd()) {
            return false;
        }
        // 过滤不检测的链接
        if (StrUtil.containsAnyIgnoreCase(url, "baicai")) {
            return false;
        }
        // 开关判断
        if (redisCache.hasKey(EngineRedisKeyFactory.K015.join(BizSwitchEnum.WX_CHECK_API.getKey()))) {
            return false;
        }

        try {
            if (!StrUtil.startWithIgnoreCase(url, "http")) {
                url = "http://" + url;
            }
            if (url.contains("&")) {
                url = UrlUtils.urlEncode(url);
            }
            String resp = HttpUtil.get("https://v3.alapi.cn/api/urlcheck/wx?token=cHwXisq9V0INLuvq&url=" + url);
            log.info("微信域名检测-ALAPI, {}", resp);
            if (JSONUtil.isTypeJSONObject(resp)) {
                JSONObject result = JSON.parseObject(resp);
                if (Objects.equals(result.getInteger("code"), 102)) {
                    // 每日请求次数已超过 10000，如需继续使用请升级高级套餐
                    redisCache.setCacheObject(EngineRedisKeyFactory.K094.join(DateUtil.today()), "1", 1, TimeUnit.DAYS);
                    return false;
                } else if (!Objects.equals(result.getInteger("code"), 429) && null != result.get("data")
                        && null != result.getJSONObject("data").getInteger("status")) {
                    // 状态：1.正常,2.拦截
                    Integer status = result.getJSONObject("data").getInteger("status");
                    if (Objects.equals(status, 1)) {
                        return false;
                    } else if (Objects.equals(status, 2)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查链接是否被微信拦截异常-ALAPI, url={}", url, e);
        }
        return false;
    }

    @Override
    public void checkWxBlockAsync(String url, boolean notify, Consumer<Boolean> callback) {
        wxMonitorExecutor.submit(() -> {
            while (true) {
                boolean useAlApi = false;
                RedisLock lock = null;
                // 优先使用ALAPI
                if (!redisCache.hasKey(EngineRedisKeyFactory.K094.join(DateUtil.today()))) {
                    // ALAPI-频次限制(1QPS)
                    lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K133.join("1"), 1, 60, 500);
                    if (null != lock) {
                        useAlApi = true;
                    }
                } else {
                    // 哒哒检测-频次限制(0.05QPS)
                    lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K131.toString(), 21, 100, 3000);
                }
                if (null == lock) {
                    continue;
                }

                // 相同域名5分钟内不会重复查询
                if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K132.join(UrlUtils.extractDomain(url)), 295)) {
                    lock.unlock();
                    return;
                }

//                boolean isBlock = useAlApi ? isWxBlockByAlapi(url) : isWxBlock(url);
                boolean isBlock = useAlApi && isWxBlockByAlapi(url);
                try {
                    if (isBlock) {
                        // 1.打印错误日志
                        String text = url + " 被微信拦截";
                        log.error(text);
                        String urlDomain = UrlUtils.extractDomain(url);
                        if (notify) {
                            // 2.其他文本数据：广告位、广告、备注数据
                            text = getStringFromDB(text, urlDomain);
                            // 3.钉钉告警
                            DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), text);
                        }
                        // 4.设置域名为微信不可用状态
                        domainService.updateWxDisable(urlDomain);
                    }
                    // 执行回调
                    Optional.ofNullable(callback).ifPresent(cb -> cb.accept(isBlock));
                } catch (Exception e) {
                    log.error("微信拦截监测异常, url={}, isBlock={}", url, isBlock, e);
                }
                return;
            }
        });
    }

    /**
     * 获取text通知：广告位、广告、备注数据
     *
     * @param text
     * @param urlDomain
     * @return
     */
    @Override
    public String getStringFromDB(String text, String urlDomain) {
        Domain domain = domainService.selectDomain(urlDomain);
        if (null == domain) {
            return text;
        }
        text = Domain.getDomainStr(domain.getDomainType()) + "，被禁用，" + text;

        List<String> domains = new ArrayList<>();
        domains.add(urlDomain);
        Map<String, Set<Long>> domainSlotMap = slotService.getDomainSlotMapS(domains);
        Map<String, Set<Long>> domainAdvertMap = advertService.getDomainAdvertMapS(domains);
        if (MapUtils.isNotEmpty(domainSlotMap) && !CollectionUtils.isEmpty(domainSlotMap.get(urlDomain))) {
            Set<Long> slotIdSet = domainSlotMap.get(urlDomain);
            text = text + "，关联广告位：" + Joiner.on(",").join(slotIdSet);
        }
        if (MapUtils.isNotEmpty(domainAdvertMap) && !CollectionUtils.isEmpty(domainAdvertMap.get(urlDomain))) {
            Set<Long> advertIdSet = domainAdvertMap.get(urlDomain);
            text = text + "，关联广告：" + Joiner.on(",").join(advertIdSet);
            ;
        }
        String remark = domain.getRemark();
        if (StringUtils.isNotEmpty(remark)) {
            text = text + "，备注：" + remark;
        }
        return text;
    }

    @Override
    public void addUrlToPatrolAsync(String advertId, String url, String userAgent) {
        GlobalThreadPool.executorService.submit(() -> {
            if (StringUtils.isNotBlank(userAgent) && !StrUtil.containsIgnoreCase(userAgent, "micromessenger")) {
                return;
            }
            if (StringUtils.isBlank(url)) {
                return;
            }
            if (StrUtil.containsAnyIgnoreCase(url, "weixin://", "ifr://", "hapjs", "quickapp", "ifr.html", "lego.zidg.com", "api://")) {
                return;
            }
            int curQuarter = DateUtil.thisHour(true) * 4 + DateUtil.thisMinute() / 15;
            String key = EngineRedisKeyFactory.K053.join(curQuarter);
            redisCache.setCacheMapValue(key, advertId, url);
            redisCache.expire(key, 20, TimeUnit.MINUTES);
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        wxCheckExecutor.submit(() -> {
            while (true) {
                try {
                    // 频次限制(0.05QPS)
                    RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K131.toString(), 21, 60, 5000);
                    if (null == lock) {
                        continue;
                    }

                    // 从Redis队列获取待检测的链接
                    String url = StringRedisTemplate.opsForList().leftPop(EngineRedisKeyFactory.K090.toString());
                    if (StringUtils.isBlank(url)) {
                        lock.unlock();
                        continue;
                    }

                    // 检查域名缓存，避免重复调用接口
                    String urlDomain = UrlUtils.extractDomain(url);
                    String redisKey = EngineRedisKeyFactory.K091.join(urlDomain);
                    Integer redisValue = redisCache.getCacheObject(redisKey);
                    if (null != redisValue) {
                        lock.unlock();
                        continue;
                    }

                    // 相同域名15分钟内不会重复查询
                    if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K132.join(urlDomain), 900)) {
                        lock.unlock();
                        continue;
                    }

                    // 检查域名状态，避免重复调用接口
                    Domain domainDO = domainService.selectDomain(urlDomain);
                    if (null != domainDO && !isNormal(domainDO.getWxStatus())) {
                        redisCache.setCacheObject(redisKey, 1, 1, TimeUnit.DAYS);
                        lock.unlock();
                        continue;
                    }

                    // 检测链接
//                    boolean isBlock = isWxBlock(url);
                    boolean isBlock = false;
                    if (isBlock) {
                        // 1.打印错误日志
                        String text = url + " 被微信拦截";
                        log.error(text);
                        // 2.其他文本数据：广告位、广告、备注数据
                        text = getStringFromDB(text, urlDomain);
                        // 3.钉钉告警
                        DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), text);
                        // 4.设置域名为微信不可用状态
                        domainService.updateWxDisable(urlDomain);
                        // 5.设置缓存
                        redisCache.setCacheObject(redisKey, 1, 1, TimeUnit.DAYS);
                    } else {
                        redisCache.setCacheObject(redisKey, 0, 5, TimeUnit.MINUTES);
                    }
                } catch (Exception e) {
                    log.error("微信域名检测异常", e);
                }
            }
        });

        wxCheck2Executor.submit(() -> {
            while (true) {
                try {
                    // 频次限制(1QPS)
                    RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K133.toString(), 1, 60, 500);
                    if (null == lock) {
                        continue;
                    }

                    // 当日接口已经限制不能使用
                    if (redisCache.hasKey(EngineRedisKeyFactory.K094.join(DateUtil.today()))) {
                        lock.unlock();
                        ThreadUtil.sleep(10, TimeUnit.MINUTES);
                        continue;
                    }

                    // 从Redis队列获取待检测的链接
                    String url = StringRedisTemplate.opsForList().leftPop(EngineRedisKeyFactory.K090.toString());
                    if (StringUtils.isBlank(url)) {
                        lock.unlock();
                        continue;
                    }

                    // 检查域名缓存，避免重复调用接口
                    String urlDomain = UrlUtils.extractDomain(url);
                    String redisKey = EngineRedisKeyFactory.K091.join(urlDomain);
                    Integer redisValue = redisCache.getCacheObject(redisKey);
                    if (null != redisValue) {
                        lock.unlock();
                        continue;
                    }

                    // 相同域名15分钟内不会重复查询
                    if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K132.join(urlDomain), 900)) {
                        lock.unlock();
                        continue;
                    }

                    // 检查域名状态，避免重复调用接口
                    Domain domainDO = domainService.selectDomain(urlDomain);
                    if (null != domainDO && !isNormal(domainDO.getWxStatus())) {
                        redisCache.setCacheObject(redisKey, 1, 1, TimeUnit.DAYS);
                        lock.unlock();
                        continue;
                    }

                    // 检测链接
                    boolean isBlock = isWxBlockByAlapi(url);
                    if (isBlock) {
                        // 1.打印错误日志
                        String text = url + " 被微信拦截";
                        log.error(text);
                        // 2.其他文本数据：广告位、广告、备注数据
                        text = getStringFromDB(text, urlDomain);
                        // 3.钉钉告警
                        DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), text);
                        // 4.设置域名为微信不可用状态
                        domainService.updateWxDisable(urlDomain);
                        // 5.设置缓存
                        redisCache.setCacheObject(redisKey, 1, 1, TimeUnit.DAYS);
                    } else {
                        redisCache.setCacheObject(redisKey, 0, 5, TimeUnit.MINUTES);
                    }
                } catch (Exception e) {
                    log.error("微信域名检测异常-ALAPI", e);
                }
            }
        });
    }
}
