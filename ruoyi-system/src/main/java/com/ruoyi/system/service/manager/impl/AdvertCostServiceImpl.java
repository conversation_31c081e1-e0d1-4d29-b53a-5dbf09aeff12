package com.ruoyi.system.service.manager.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.entity.advert.AdvertCost;
import com.ruoyi.system.mapper.manager.AdvertCostMapper;
import com.ruoyi.system.service.manager.AdvertCostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/16 3:21 下午
 */
@Service
public class AdvertCostServiceImpl implements AdvertCostService {

    @Autowired
    private AdvertCostMapper advertCostMapper;

    @Override
    public int insertOrUpdateAdvertCost(AdvertCost advertCost) {
        return advertCostMapper.insertOrUpdateAdvertCost(advertCost);
    }

    @Override
    public AdvertCost selectById(Long advertId) {
        return advertCostMapper.selectById(advertId, DateUtil.formatDate(new Date()));
    }

    @Override
    public Map<Long, AdvertCost> selectMapByDateAndType(Date curDate, Integer type) {
        if (null == curDate) {
            return Collections.emptyMap();
        }
        AdvertCost param = new AdvertCost();
        param.setCurDate(curDate);
        param.setBillingType(type);
        List<AdvertCost> list = advertCostMapper.selectList(param);
        return list.stream().collect(Collectors.toMap(AdvertCost::getAdvertId, Function.identity(), (o, n) -> n));
    }

    @Override
    public List<AdvertCost> selectListByYesterday(Long id, Integer pageSize) {
        Date yesterday = DateUtils.addDays(new Date(),-1);
        return advertCostMapper.selectListByYesterday(DateUtil.formatDate(yesterday) ,id,pageSize);
    }

    @Override
    public int batchInsertOrUpdate(List<AdvertCost> advertCosts) {
        return advertCostMapper.batchInsertOrUpdate(advertCosts);
    }
}
