package com.ruoyi.system.service.slot;

import com.ruoyi.system.entity.slot.SlotShuntData;
import com.ruoyi.system.req.slot.shunt.SlotShuntDataParam;
import com.ruoyi.system.req.slot.shunt.SlotShuntDataUpdateParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告位切量数据Service接口
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
public interface SlotShuntDataService {

    /**
     * 查询广告位切量数据列表
     *
     * @param param 查询参数
     * @return 广告位切量数据列表
     */
    List<SlotShuntData> selectList(SlotShuntDataParam param);

    /**
     * 查询广告位切量数据映射
     *
     * @param taskIds 广告位切量计划ID列表
     * @return 切量计划ID-切量数据映射
     */
    Map<Long, SlotShuntData> selectMap(List<Long> taskIds);

    /**
     * 查询广告位切量数据
     *
     * @param taskId 广告位切量计划ID
     * @param curDate 日期
     * @return 广告位切量数据
     */
    SlotShuntData selectByTaskIdAndDate(Long taskId, Date curDate);

    /**
     * 查询广告位切量数据
     *
     * @param id 广告位切量数据ID
     * @return 广告位切量数据
     */
    SlotShuntData selectById(Long id);

    /**
     * 新增广告位切量数据
     *
     * @param record 广告位切量数据
     * @return 影响行数
     */
    int insert(SlotShuntData record);

    /**
     * 更新广告位切量数据
     *
     * @param param 更新参数
     * @return 影响行数
     */
    int update(SlotShuntDataUpdateParam param);

    /**
     * 广告位切量数据统计(异步)
     *
     * @param slotId 广告位ID
     * @param deviceId 设备号
     * @param taskId 切量计划ID
     * @param shuntType 切量类型
     * @param date 日期
     */
    void statistics(Long slotId, String deviceId, Long taskId, Integer shuntType, Date date);
}
