package com.ruoyi.system.service.adengine;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.domain.adengine.IpAreaDto;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.req.engine.NhAdvertReq;
import com.ruoyi.system.vo.engine.NhAdvertResp;

import java.util.List;

/**
 * 广告引擎接口
 *
 * <AUTHOR>
 * @date 2021/8/11
 */
public interface AdvertEngineService {

    /**
     * 请求诺禾广告
     *
     * @param req 广告请求参数
     * @param logJson 日志内容
     * @return 广告结果
     */
    NhAdvertResp getNhAdvert(NhAdvertReq req, JSONObject logJson);

    /**
     * 请求直投广告
     *
     * @param orderId 订单号
     * @param consumerId 用户ID
     * @param accountId 媒体账户ID
     * @param appId 媒体ID
     * @param slotId 广告位ID
     * @param deviceId 设备ID
     * @param srid 广告位唯一标识
     * @param advertId 广告ID
     * @param ip IP地址
     * @param userAgent userAgent
     * @param ipArea IP解析的地域信息
     * @param extParam 扩展参数
     * @param isDegraded 是否降级(降级不打印券请求日志)
     * @return <订单, 直投广告落地页>
     */
    Pair<Order, String> getNhDirectAdvert(String orderId, Long consumerId, Long accountId, Long appId, Long slotId, String deviceId,
                                          String srid, Long advertId, String ip, String userAgent, IpAreaDto ipArea, JSONObject extParam,
                                          boolean isDegraded);

    /**
     * 请求直投广告
     *
     * @param orderId 订单号
     * @param consumerId 用户ID
     * @param accountId 媒体账户ID
     * @param appId 媒体ID
     * @param slotId 广告位ID
     * @param deviceId 设备ID
     * @param srid 广告位唯一标识
     * @param advertIds 广告ID列表
     * @param ip IP地址
     * @param userAgent userAgent
     * @param ipArea IP解析的地域信息
     * @param extParam 扩展参数
     * @return <订单, 直投广告落地页>
     */
    Pair<Order, String> getNhDirectAdvert(String orderId, Long consumerId, Long accountId, Long appId, Long slotId, String deviceId,
                             String srid, List<Long> advertIds, String ip, String userAgent, IpAreaDto ipArea, JSONObject extParam);

    /**
     * 请求直投广告配置
     *
     * @param orderId 订单号
     * @param consumerId 用户ID
     * @param accountId 媒体账户ID
     * @param appId 媒体ID
     * @param slotId 广告位ID
     * @param deviceId 设备ID
     * @param srid 广告位唯一标识
     * @param orientIds 广告配置ID列表
     * @param ip IP地址
     * @param userAgent userAgent
     * @param ipArea IP解析的地域信息
     * @param extParam 扩展参数
     * @return <订单, 直投广告落地页>
     */
    Pair<Order, String> getNhDirectAdvertOrient(String orderId, Long consumerId, Long accountId, Long appId, Long slotId, String deviceId,
                                   String srid, List<Long> orientIds, String ip, String userAgent, IpAreaDto ipArea, JSONObject extParam);

    /**
     * 兜底请求直投广告配置
     *
     * @param orderId 订单号
     * @param consumerId 用户ID
     * @param accountId 媒体账户ID
     * @param appId 媒体ID
     * @param slotId 广告位ID
     * @param deviceId 设备ID
     * @param srid 广告位唯一标识
     * @param orientIds 广告配置ID列表
     * @param ip IP地址
     * @param userAgent userAgent
     * @param ipArea IP解析的地域信息
     * @param extParam 扩展参数
     * @return <订单, 直投广告落地页>
     */
    Pair<Order, String> getNhDirectAdvertOrientByDegraded(String orderId, Long consumerId, Long accountId, Long appId, Long slotId,
                                             String deviceId, String srid, List<Long> orientIds,
                                             String ip, String userAgent, IpAreaDto ipArea, JSONObject extParam);

    /**
     * CDN空白页(公众号文章使用)
     *
     * @param url 原链接
     * @return 加了空白页的链接
     */
    String cdnRedirect(String url);
}
