package com.ruoyi.system.service.manager;

import com.ruoyi.system.entity.app.App;

import java.util.List;
import java.util.Map;

/**
 * 媒体应用Service接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface AppService {

    /**
     * 查询媒体应用
     *
     * @param id 媒体应用ID
     * @return 媒体应用
     */
    App selectAppById(Long id);

    /**
     * 查询媒体应用名称
     *
     * @param id 媒体应用ID
     * @return 媒体应用名称
     */
    String selectAppNameById(Long id);

    /**
     * 查询媒体应用列表
     *
     * @param app 媒体应用
     * @return 媒体应用集合
     */
    List<App> selectAppList(App app);

    /**
     * 查询媒体应用对应的账号ID列表
     *
     * @param app 媒体应用
     * @return 账号Id列表
     */
    List<Long> selectAccountIdList(App app);

    /**
     * 查询媒体应用列表
     *
     * @return 媒体应用集合
     */
    List<App> selectTotalAppList();

    /**
     * 新增媒体应用
     *
     * @param app 媒体应用
     * @return 结果
     */
    int insertApp(App app);

    /**
     * 修改媒体应用
     *
     * @param app 媒体应用
     * @return 结果
     */
    int updateApp(App app);

    /**
     * 获取媒体账号对应的媒体数量
     *
     * @param accountIds 媒体账号ID列表
     * @return 媒体账号ID-媒体数量映射
     */
    Map<Long, Integer> groupByAccountId(List<Long> accountIds);

    /**
     * 批量查询媒体简易信息
     *
     * @param ids 媒体ID列表
     * @return 媒体列表
     */
    List<App> selectSimpleInfoByIds(List<Long> ids);

    /**
     * 根据appId列表获取媒体名称
     * @param appIds 媒体列表
     * @return <appId,appName>
     */
    Map<Long,String> selectAppNameMap(List<Long> appIds);

    /**
     * 查询媒体ID-媒体名称映射
     *
     * @return 媒体ID-媒体名称映射
     */
    Map<Long, String> selectAppNameMap();

    /**
     * 根据媒体名称查询媒体ID列表
     *
     * @param appName 媒体名称
     * @return 媒体ID列表
     */
    List<Long> selectAppIdsByAppName(String appName);

    /**
     * 查询媒体ID列表
     *
     * @param searchValue 媒体ID/媒体名称
     * @return 媒体ID列表
     */
    List<Long> selectAppIdsBySearchValue(String searchValue);
}
