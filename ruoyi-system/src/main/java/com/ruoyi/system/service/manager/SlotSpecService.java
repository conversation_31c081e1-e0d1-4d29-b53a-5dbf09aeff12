package com.ruoyi.system.service.manager;

import java.util.List;
import com.ruoyi.system.entity.slot.SlotSpec;

/**
 * 广告位规格Service接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface SlotSpecService {

    /**
     * 查询广告位规格
     *
     * @param id 广告位规格ID
     * @return 广告位规格
     */
    SlotSpec selectSlotSpecById(Long id);

    /**
     * 查询广告位规格列表
     *
     * @param slotSpec 广告位规格
     * @return 广告位规格集合
     */
    List<SlotSpec> selectSlotSpecList(SlotSpec slotSpec);

    /**
     * 查询广告位规格列表
     *
     * @param slotSpec 广告位规格
     * @return 广告位规格集合
     */
    List<SlotSpec> selectTotalSlotSpecList(SlotSpec slotSpec);

    /**
     * 新增广告位规格
     *
     * @param slotSpec 广告位规格
     * @return 结果
     */
    int insertSlotSpec(SlotSpec slotSpec);

    /**
     * 修改广告位规格
     *
     * @param slotSpec 广告位规格
     * @return 结果
     */
    int updateSlotSpec(SlotSpec slotSpec);
}
