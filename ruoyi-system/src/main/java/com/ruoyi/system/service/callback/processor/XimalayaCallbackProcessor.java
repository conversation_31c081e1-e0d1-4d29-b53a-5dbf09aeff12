package com.ruoyi.system.service.callback.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.XIMALAYA;

/**
 * 喜马拉雅上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Slf4j
@Service
public class XimalayaCallbackProcessor implements CallbackProcessor, InitializingBean {

    @Override
    public CallbackProcessorTypeEnum getType() {
        return XIMALAYA;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StrUtil.startWith(param.getString("callback"), "http", true);
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            String eventType = StringUtils.defaultString(context.getParam().getSlotParam().getString("event_type"), "2");
            String callbackUrl = context.getParam().getSlotParam().getString("callback") + "&event_type=" + eventType;
            String resp = HttpUtil.get(callbackUrl);
            log.info("{}接口上报, url={}, resp={}", getType().getName(), callbackUrl, resp);
            if (null != resp) {
                return true;
            }
            log.error("{}接口上报失败, url={}", getType().getName(), callbackUrl);
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
