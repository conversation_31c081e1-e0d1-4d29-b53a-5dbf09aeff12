package com.ruoyi.system.service.manager;

import com.ruoyi.system.bo.slot.SlotDomainConfigBo;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;

import java.util.List;
import java.util.Map;

/**
 * 广告位配置Service接口
 *
 * <AUTHOR>
 * @date 2021-07-15
 */
public interface SlotConfigService {

    /**
     * 查询广告位配置
     *
     * @param id 广告位配置ID
     * @return 广告位配置
     */
    SlotConfig selectSlotConfigById(Long id);

    /**
     * 查询广告位配置
     *
     * @param slotId 广告位ID
     * @return 广告位配置
     */
    SlotConfig selectBySlotId(Long slotId);

    /**
     * 查询广告位域名配置
     *
     * @param slotId 广告位ID
     * @return 广告位域名配置
     */
    SlotDomainConfigBo selectDomainConfigBySlotId(Long slotId);

    /**
     * 查询广告位开关配置
     *
     * @param slotId 广告位ID
     * @return 广告位开关配置
     */
    SlotSwitchConfig selectSwitchConfigBySlotId(Long slotId);

    /**
     * 查询广告位配置列表
     *
     * @param slotConfig 广告位配置
     * @return 广告位配置集合
     */
    List<SlotConfig> selectSlotConfigList(SlotConfig slotConfig);

    /**
     * 保存广告位配置
     *
     * @param config 广告位配置
     * @return 结果
     */
    int save(SlotConfig config);

    /**
     * 新增广告位配置
     *
     * @param slotConfig 广告位配置
     * @return 结果
     */
    int insertSlotConfig(SlotConfig slotConfig);

    /**
     * 修改广告位配置
     *
     * @param slotConfig 广告位配置
     * @return 结果
     */
    int updateSlotConfig(SlotConfig slotConfig);

    /**
     * 修改广告位配置
     *
     * @param config 广告位配置
     * @return 结果
     */
    int updateById(SlotConfig config);

    /**
     * 查询广告位配置映射
     *
     * @param slotIds 广告位Id列表
     * @return 广告位Id-广告位配置映射
     */
    Map<Long, SlotConfig> selectSlotConfigMap(List<Long> slotIds);
}
