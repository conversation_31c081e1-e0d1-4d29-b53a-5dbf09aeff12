package com.ruoyi.system.service.advert;

import com.ruoyi.system.bo.advert.AdvertOrientBO;
import com.ruoyi.system.bo.advert.AdvertOrientBatchUpdateParam;
import com.ruoyi.system.bo.advert.AdvertOrientLandpageBo;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.req.advert.AdvertOrientationAddReq;
import com.ruoyi.system.req.advert.AdvertOrientationModifyReq;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 广告定向配置Service接口
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
public interface AdvertOrientationService {

    /**
     * 查询广告的默认定向配置
     *
     * @param advertId 广告ID
     * @return 广告定向配置
     */
    AdvertOrientation selectDefaultOrientationByAdvertId(Long advertId);

    /**
     * 查询广告的配置ID列表
     *
     * @param advertId 广告ID
     * @return 广告定向配置ID列表
     */
    List<Long> selectIdsByAdvertId(Long advertId);

    /**
     * 查询广告的配置ID列表
     *
     * @param advertIds 广告ID列表
     * @return 广告定向配置ID列表
     */
    List<Long> selectIdsByAdvertIds(List<Long> advertIds);

    /**
     * 查询开启地域定向的广告的配置ID列表
     *
     * @param advertIds 广告ID列表
     * @return 广告定向配置ID列表
     */
    List<Long> selectIdsWithAreaTargetByAdvertIds(List<Long> advertIds);

    /**
     * 查询广告定向配置
     *
     * @param id 广告定向配置ID
     * @return 广告定向配置
     */
    AdvertOrientation selectAdvertOrientationById(Long id);

    /**
     * 查询广告定向配置列表
     *
     * @param param 请求参数
     * @return 广告定向配置列表
     */
    List<AdvertOrientation> selectAdvertOrientationList(AdvertOrientation param);

    /**
     * 根据配置ID列表查询广告定向配置列表
     *
     * @param ids 配置ID列表
     * @return 广告定向配置列表
     */
    List<AdvertOrientation> selectListByIds(List<Long> ids);

    /**
     * 根据配置ID列表查询广告定向配置映射
     *
     * @param ids 配置ID列表
     * @return 配置ID-配置信息映射
     */
    Map<Long, AdvertOrientation> selectMapByIds(List<Long> ids);

    /**
     * 根据配置ID列表查询广告定向配置名称
     *
     * @param ids 配置ID列表
     * @return 配置ID-配置名称映射
     */
    Map<Long, String> selectOrientNameMap(List<Long> ids);

    /**
     * 根据广告ID列表查询广告定向配置列表
     *
     * @param advertIds 广告ID列表
     * @return 广告定向配置列表
     */
    List<AdvertOrientation> selectListByAdvertIds(List<Long> advertIds);

    /**
     * 查询广告定向配置列表
     *
     * @param advertId 广告ID
     * @return 广告定向配置集合
     */
    List<AdvertOrientation> selectListByAdvertId(Long advertId);

    /**
     * 查询广告的最大计费单价(毫)
     *
     * @param advertId 广告ID
     * @return 计费单价(毫)
     */
    Integer selectMaxMilliUnitPriceByAdvertId(Long advertId);

    /**
     * 查询广告定向配置列表
     *
     * @param advertIds 广告ID列表
     * @return 广告定向配置集合
     */
    List<AdvertOrientation> selectDefaultByAdvertIds(List<Long> advertIds);

    /**
     * 查询配置的屏蔽媒体
     *
     * @param advertIds 广告ID列表
     * @return 配置ID-屏蔽媒体集合映射
     */
    Map<Long, Set<Long>> selectOrientBannedApp(List<Long> advertIds);

    /**
     * 查询配置的定向媒体
     *
     * @param advertIds 广告ID列表
     * @return 配置ID-定向媒体集合映射
     */
    Map<Long, Set<Long>> selectOrientApp(List<Long> advertIds);

    /**
     * 查询配置的定向媒体
     *
     * @param advertIds 广告ID列表
     * @return 配置ID-定向媒体集合映射
     */
    Map<Long, AdvertOrientBO> selectOrientInfo(List<Long> advertIds);

    /**
     * 查询配置的定向媒体
     *
     * @param advertIds 广告ID列表
     * @return 广告ID-定向广告位集合映射
     */
    Map<Long, Set<Long>> selectAdvertOrientSlotMap(List<Long> advertIds);

    /**
     * 查询配置的定向媒体
     *
     * @param orientIds 配置ID列表
     * @return 配置ID-定向广告位集合映射
     */
    Map<Long, Set<Long>> selectAdvertOrientSlotMapByOrientIds(List<Long> orientIds);

    /**
     * 查询广告定向媒体映射
     *
     * @param advertIds 广告ID列表
     * @return 广告ID-定向媒体集合映射
     */
    Map<Long, Set<Long>> selectAdvertOrientAppMap(List<Long> advertIds);

    /**
     * 查询广告配置定向媒体映射
     *
     * @param orientIds 配置ID列表
     * @return 配置ID-定向媒体集合映射
     */
    Map<Long, Set<Long>> selectAdvertOrientAppMapByOrientIds(List<Long> orientIds);

    /**
     * 新增广告定向配置
     *
     * @param req 广告定向配置
     * @return 结果
     */
    Long insertAdvertOrientation(AdvertOrientationAddReq req);

    /**
     * 修改广告定向配置
     *
     * @param req 广告定向配置
     * @return 广告ID
     */
    Long updateAdvertOrientation(AdvertOrientationModifyReq req);

    /**
     * 修改广告定向配置
     *
     * @param param 参数
     * @return 结果
     */
    int updateAdvertOrientation(AdvertOrientation param);

    /**
     * 删除广告定向配置
     *
     * @param id 广告定向配置ID
     * @return 结果
     */
    boolean deleteAdvertOrientation(Long id);

    /**
     * 复制广告定向配置
     *
     * @param originOrientId 被复制的配置ID
     * @param advertId 广告ID，null是配置复制，不为null则是广告复制
     * @return 结果
     */
    Long copyAdvertOrientation(Long originOrientId, Long advertId);

    /**
     * 批量修改广告定向配置的地域定向
     *
     * @param orientIds 广告配置ID列表
     * @param areaTarget 地域定向
     * @return 结果
     */
    int batchUpdateAreaTarget(List<Long> orientIds, Set<String> areaTarget);

    /**
     * 修改广告定向配置的落地页
     *
     * @param orientId 配置ID
     * @param landpageUrl 落地页链接
     * @return 结果
     */
    int updateLandpageUrl(Long orientId, String landpageUrl);

    /**
     * 修改配置每日预算
     *
     * @param advertOrientation 广告定向配置
     * @return 结果
     */
    int updateDailyBudget(AdvertOrientation advertOrientation);

    /**
     * 查询广告的配置数量
     *
     * @param advertIds 广告ID列表
     * @return 广告ID-配置数量映射
     */
    Map<Long, Integer> selectCountMapByAdvertIds(List<Long> advertIds);

    /**
     * 查询配置的广告ID
     *
     * @param ids 配置ID列表
     * @return 广告ID列表
     */
    List<Long> selectAdvertIdsByOrientIds(List<Long> ids);

    /**
     * 查询非通投配置ID集合
     *
     * @param advertIds 广告ID列表
     * @return 广告配置ID集合
     */
    Set<Long> selectOrientBannedOrientIds(List<Long> advertIds);

    /**
     * 查询配置落地页链接
     *
     * @param advertIds 广告ID列表
     * @return 配置落地页链接列表
     */
    List<AdvertOrientLandpageBo> selectLandpageByAdvertIds(List<Long> advertIds);

    /**
     * 批量修改广告定向配置信息
     * @param param 广告定向配置
     * @return 是否成功
     */
    Boolean batchUpdateAdvertOrientation(AdvertOrientBatchUpdateParam param);

    /**
     * 根据落地页查询配置列表
     *
     * @param landpageUrl 链接
     * @return 配置列表
     */
    List<AdvertOrientLandpageBo> selectByLandpage(String landpageUrl);
}
