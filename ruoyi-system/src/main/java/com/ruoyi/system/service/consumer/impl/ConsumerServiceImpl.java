package com.ruoyi.system.service.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.consumer.Consumer;
import com.ruoyi.system.mapper.consumer.ConsumerMapper;
import com.ruoyi.system.service.common.IdWorkerService;
import com.ruoyi.system.service.consumer.ConsumerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static cn.hutool.core.util.BooleanUtil.isTrue;
import static com.ruoyi.common.enums.common.IdWorkerType.CONSUMER_ID;

/**
 * 用户服务实现
 *
 * <AUTHOR>
 * @date 2021/8/17
 */
@Slf4j
@Service
public class ConsumerServiceImpl implements ConsumerService {

    @Value("${tb.sharding.count.consumer}")
    private Integer tbCount;

    @Autowired
    private IdWorkerService idWorkerService;

    @Autowired
    private ConsumerMapper consumerMapper;

    @Autowired
    private RedisCache redisCache;

    @Override
    public Consumer selectByConsumerId(Long consumerId) {
        if (null == consumerId) {
            return null;
        }

        Consumer param = new Consumer();
        param.setConsumerId(consumerId);
        param.setTbSuffix(String.format("%04d", getTbSuffix(consumerId)));
        return consumerMapper.selectByConsumerId(param);
    }

    @Override
    public Consumer selectByAppAndDevice(Long appId, String deviceId) {
        if (null == appId || StringUtils.isEmpty(deviceId)) {
            return null;
        }

        // 表后缀: (appId + "-" + deviceId).hashCode() % tbCount
        int tbSuffix = getTbSuffix(appId, deviceId);

        Consumer param = new Consumer();
        param.setAppId(appId);
        param.setDeviceId(deviceId);
        param.setTbSuffix(String.format("%04d", tbSuffix));
        return consumerMapper.selectByAppAndDevice(param);
    }

    @Override
    public Long getOrCreateConsumer(Long appId, String deviceId) {
        // 查询缓存
        Long consumerId = getConsumerIdCache(appId, deviceId);
        if (null != consumerId) {
            return consumerId;
        }

        // 表后缀: (appId + "-" + deviceId).hashCode() % tbCount
        int tbSuffix = getTbSuffix(appId, deviceId);

        // 先查询
        Consumer param = new Consumer();
        param.setAppId(appId);
        param.setDeviceId(deviceId);
        param.setTbSuffix(String.format("%04d", tbSuffix));
        Consumer consumer = consumerMapper.selectByAppAndDevice(param);
        if (null != consumer) {
            cacheConsumerId(appId, deviceId, consumer.getConsumerId());
            return consumer.getConsumerId();
        }

        // 查询不到，新增用户
        // consumerId生成规则: 分布式ID自增 + 表后缀
        consumerId = idWorkerService.getNextID(CONSUMER_ID) * 10000 + tbSuffix;

        // 先缓存
        if (!isTrue(cacheConsumerId(appId, deviceId, consumerId))) {
            consumerId = getConsumerIdCache(appId, deviceId);
            if (null != consumerId) {
                return consumerId;
            }
        }

        // 再异步落库
        param.setConsumerId(consumerId);
        GlobalThreadPool.insertExecutorService.submit(() -> {
            try {
                consumerMapper.insertConsumer(param);
            } catch (DuplicateKeyException e) {
                log.error("新增用户冲突，param={}", JSON.toJSONString(param));
                // 并发导致新增冲突，重新查询一下
                Optional.ofNullable(consumerMapper.selectByAppAndDevice(param)).ifPresent(c -> cacheConsumerId(appId, deviceId, c.getConsumerId()));
            } catch (Exception e) {
                log.error("新增用户异常，param={}", JSON.toJSONString(param), e);
            }
        });
        return consumerId;
    }

    /**
     * 获取用户信息对应的表后缀
     * 规则: (appId + "-" + deviceId).hashCode() % tbCount
     *
     * @param appId 媒体ID
     * @param deviceId 设备号
     * @return 表后缀
     */
    private int getTbSuffix(Long appId, String deviceId) {
        return Math.abs((appId + "-" + deviceId).hashCode()) % tbCount;
    }

    /**
     * 获取用户对应的表后缀
     * 规则: consumerId后四位
     *
     * @param consumerId 用户ID
     * @return 表后缀
     */
    private int getTbSuffix(Long consumerId) {
        return (int) (consumerId % 10000);
    }

    /**
     * 查询consumerId缓存
     *
     * @param appId 媒体ID
     * @param deviceId 设备ID
     * @return consumerId
     */
    private Long getConsumerIdCache(Long appId, String deviceId) {
        return redisCache.getCacheObject(EngineRedisKeyFactory.K048.join(appId, deviceId));
    }

    /**
     * 缓存consumerId
     *
     * @param appId 媒体ID
     * @param deviceId 设备ID
     * @param consumerId 被缓存的consumerId
     * @return 是否设置成功
     */
    private Boolean cacheConsumerId(Long appId, String deviceId, Long consumerId) {
        String key = EngineRedisKeyFactory.K048.join(appId, deviceId);
        return redisCache.setCacheObjectIfAbsent(key, consumerId, 3, TimeUnit.MINUTES);
    }
}
