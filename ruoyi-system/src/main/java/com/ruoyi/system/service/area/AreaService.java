package com.ruoyi.system.service.area;

import com.ruoyi.system.domain.adengine.IpAreaDto;
import com.ruoyi.system.domain.manager.Area;
import com.ruoyi.system.domain.manager.AreaVO;

import java.util.List;

/**
 * 地域服务
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
public interface AreaService {

    /**
     * 根据关键词查询省市
     *
     * @param searchKey 关键词
     * @return 省市列表
     */
    List<AreaVO> searchProvinceAndCity(String searchKey);

    /**
     * 查询行政区划代码(缓存)
     *
     * @param areaName 地域名称
     * @return 行政区划代码
     */
    String queryAreaNumByName(String areaName);

    /**
     * 批量查询行政区划代码(省市)
     *
     * @param areaNames 地域名称列表
     * @return 行政区划代码列表
     */
    List<String> queryAreaNumByName(List<String> areaNames);

    /**
     * 查询地域名称(缓存)
     *
     * @param areaNum 行政区划代码
     * @return 地域名称
     */
    String queryAreaNameByNum(String areaNum);

    /**
     * 查询地域
     *
     * @param areaNum 行政区划代码
     * @return 地域
     */
    Area queryAreaByAreaNum(String areaNum);

    /**
     * 查询地域(联通/移动)
     *
     * @param tag 标签
     * @param areaNum 行政区划代码
     * @return 地域
     */
    Area queryAreaByAreaNumLtYdCache(String tag, String areaNum);

    /**
     * 查询所有的省市(缓存)
     *
     * @param tag 标签
     * @param areaNum 行政区划代码
     * @return 省市列表
     */
    List<AreaVO> queryAreaListCache(String tag, String areaNum);

    /**
     * 查询所有的省市区
     *
     * @return 省市区列表
     */
    List<AreaVO> queryTotalAreaCache();

    /**
     * 查询所有的省市
     *
     * @return 省市列表
     */
    List<AreaVO> queryTotalProvinceAndCity();

    /**
     * 查询所有的省
     *
     * @return 省列表
     */
    List<String> queryTotalProvince();

    /**
     * IP解析
     *
     * @param ip ip地址
     * @return 解析结果
     */
    IpAreaDto ipAnalysis(String ip);

    /**
     * IP解析
     *
     * @param ip ip地址
     * @return 解析结果
     */
    IpAreaDto ipAnalysisLocal(String ip);
}
