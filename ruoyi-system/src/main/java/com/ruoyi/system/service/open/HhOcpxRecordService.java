package com.ruoyi.system.service.open;

import com.ruoyi.system.entity.open.HhOcpxRecordEntity;

/**
 * 辉煌OCPX事件记录 Service
 *
 * <AUTHOR>
 * @date 2024-9-18 17:40:48
 */
public interface HhOcpxRecordService {

    /**
     * 新增记录
     */
    Boolean insert(HhOcpxRecordEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(HhOcpxRecordEntity entity);

    /**
     * 根据id获取
     */
    HhOcpxRecordEntity selectById(Long id);
}
