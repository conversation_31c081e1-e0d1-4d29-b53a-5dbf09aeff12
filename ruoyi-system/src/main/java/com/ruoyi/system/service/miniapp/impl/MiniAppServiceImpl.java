package com.ruoyi.system.service.miniapp.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.scheme.WxMaGenerateSchemeRequest;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.system.service.miniapp.MiniAppService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 微信小程序服务
 *
 * <AUTHOR>
 * @date 2022/10/9 10:46 上午
 */
@Slf4j
@Service
public class MiniAppServiceImpl implements MiniAppService {

    @Autowired
    private WxMaService wxMaService;

    @Override
    public String getUrlScheme(String path,String query,Integer expireInterval) {
        try {
            long startTime = System.currentTimeMillis();
            String env = "release"; //正式版
            if(SpringEnvironmentUtils.isTest()){
                env = "trial";//体验版
            }else if(SpringEnvironmentUtils.isDev()){
                env = "develop"; //开发版
            }
            WxMaGenerateSchemeRequest.JumpWxa jumpWxa = WxMaGenerateSchemeRequest.JumpWxa.newBuilder().path(path).envVersion(env).query(query).build();
            WxMaGenerateSchemeRequest request = WxMaGenerateSchemeRequest.newBuilder().jumpWxa(jumpWxa).expireType(1).expireInterval(expireInterval).build();
            String urlScheme = wxMaService.getWxMaSchemeService().generate(request);
            long time = System.currentTimeMillis() - startTime;
            if( time > 200){
                //生成时间超过200ms，打印日志
                log.warn("生成 urlScheme time: {}ms", time);
            }
            return urlScheme;
        } catch (WxErrorException e) {
            log.error("生成小程序url scheme 失败,path:{},query:{},e:",path,query,e);
        }
        return "";
    }
}
