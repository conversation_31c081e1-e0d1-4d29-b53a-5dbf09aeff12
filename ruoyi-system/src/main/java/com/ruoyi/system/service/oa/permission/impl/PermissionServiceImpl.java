package com.ruoyi.system.service.oa.permission.impl;

import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.oa.permission.PermissionEntity;
import com.ruoyi.system.mapper.oa.permission.PermissionMapper;
import com.ruoyi.system.service.oa.permission.PermissionService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限表 Service
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:18
 */
@Service
public class PermissionServiceImpl implements PermissionService {

    @Autowired
    private PermissionMapper permissionMapper;


    @Override
    public PermissionEntity selectById(Long id){
        if(Objects.isNull(id)){
            return null;
        }
        return permissionMapper.selectById(id);
    }

    @Override
    public List<PermissionEntity> selectBySystemId(Long systemId) {
        if(NumberUtils.isNullOrLteZero(systemId)){
            return Collections.emptyList();
        }
        return permissionMapper.selectBySystemId(systemId);
    }

    @Override
    public List<Long> selectByParentId(Long parentId) {
        if (null == parentId) {
            return Collections.emptyList();
        }
        return permissionMapper.selectByParentId(parentId);
    }

    @Override
    public List<Long> selectParentIdsByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return permissionMapper.selectParentIdsByIds(ids);
    }

    @Override
    public Map<Long, List<PermissionEntity>> selectParentMapBySystemId(Long systemId) {
        List<PermissionEntity> list = selectBySystemId(systemId);
        return list.stream().collect(Collectors.groupingBy(PermissionEntity::getParentId));
    }

    @Override
    public Set<String> selectPermissionKeyByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptySet();
        }
        return permissionMapper.selectPermissionKeyByIds(ids);
    }
}
