package com.ruoyi.system.service.ai;

import com.ruoyi.system.entity.ai.AiLiuziEntity;
import com.ruoyi.system.req.ai.AiLiuziListReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/2 15:15
 */
public interface AiLiuziService {

    /**
     * 新增
     * @param entity
     * @return
     */
    boolean insert(AiLiuziEntity entity);

    /**
     * 根据id更新
     *
     * @param entity
     * @return
     */
    boolean updateById(AiLiuziEntity entity);

    /**
     * 根据条件查询留资列表
     * @param req
     * @return
     */
    List<AiLiuziEntity> selectList(AiLiuziListReq req);
}
