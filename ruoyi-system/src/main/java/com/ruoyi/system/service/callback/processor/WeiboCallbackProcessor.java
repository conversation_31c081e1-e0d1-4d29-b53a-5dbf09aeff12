package com.ruoyi.system.service.callback.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.open.WeiboService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.WEIBO;

/**
 * 微博上报处理器
 *
 * <AUTHOR>
 * @date 2024/01/15
 */
@Slf4j
@Service
public class WeiboCallbackProcessor implements CallbackProcessor, InitializingBean {

    @Autowired
    private WeiboService weiboService;

    @Override
    public CallbackProcessorTypeEnum getType() {
        return WEIBO;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("markId"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            String orderId = context.getOrder().getOrderId();
            Long slotId = context.getOrder().getSlotId();
            String markId = context.getParam().getSlotParam().getString("markId");
            String resp = weiboService.behaviorUpload(orderId, slotId, markId);
            log.info("{}接口上报, markId={}, resp={}", getType().getName(), markId, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result) {
                return true;
            }
            log.error("{}接口上报失败, markId={}, resp={}", getType().getName(), markId, resp);
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
