package com.ruoyi.system.service.system.impl;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.system.SysConfigDomainService;
import com.ruoyi.system.entity.system.SysConfigDomainEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.system.SysConfigDomainMapper;

/**
 * 参数配置表域名为主查询配置信息 Service
 *
 * <AUTHOR>
 * @date 2023-6-7 16:25:42
 */
@Service
public class SysConfigDomainServiceImpl implements SysConfigDomainService {
    @Autowired
    private SysConfigDomainMapper sysConfigDomainMapper;

    @Override
    public Boolean insert(SysConfigDomainEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return sysConfigDomainMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Integer id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return sysConfigDomainMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(SysConfigDomainEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return sysConfigDomainMapper.updateById(entity) > 0;
    }

    @Override
    public SysConfigDomainEntity selectById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return sysConfigDomainMapper.selectById(id);
    }

    @Override
    public SysConfigDomainEntity selectByKey(String configKey) {
        if (StringUtils.isEmpty(configKey)) {
            return null;
        }
        return sysConfigDomainMapper.selectByKey(configKey);
    }


}
