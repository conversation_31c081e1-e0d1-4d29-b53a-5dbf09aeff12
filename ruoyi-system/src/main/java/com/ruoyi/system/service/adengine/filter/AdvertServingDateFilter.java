package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.system.service.adengine.AdvertFilter;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 广告投放周期过滤
 */
@Component
public class AdvertServingDateFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();
        Date today = context.getToday();
        return !today.before(ad.getStartServingDate()) && !today.after(ad.getStopServingDate());
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.SERVING_DATE;
    }
}
