package com.ruoyi.system.service.blindbox;

import com.ruoyi.system.entity.blindbox.BlindBoxLandpageRecordEntity;

/**
 * 盲盒落地页提交记录表 Service
 *
 * <AUTHOR>
 * @date 2022-6-13 11:10:16
 */
public interface BlindBoxLandpageRecordService {

    /**
     * 新增记录
     */
    Boolean insert(BlindBoxLandpageRecordEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(BlindBoxLandpageRecordEntity entity);

    /**
     * 根据id获取
     */
    BlindBoxLandpageRecordEntity selectById(Long id);
}
