package com.ruoyi.system.service.engine.cache;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.enums.common.EnableStatusEnum;
import com.ruoyi.common.enums.common.MapConfigEnum;
import com.ruoyi.common.enums.plugin.PluginSwitchEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.SlotBiddingConfigDto;
import com.ruoyi.system.domain.adengine.SlotCacheDto;
import com.ruoyi.system.domain.slot.AreaTargetRedirectItem;
import com.ruoyi.system.domain.slot.ShuntRedirectItem;
import com.ruoyi.system.entity.plugin.Plugin;
import com.ruoyi.system.entity.plugin.PluginInfo;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slot.SlotBiddingConfigEntity;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.entity.tagmanager.TagManagerEntity;
import com.ruoyi.system.mapper.manager.SlotConfigMapper;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.manager.PluginService;
import com.ruoyi.system.service.slot.SlotBiddingConfigService;
import com.ruoyi.system.service.slot.SlotTagRelationService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import com.ruoyi.system.vo.slot.RetConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.slot.SlotRedirectType.*;

/**
 * 广告位缓存服务
 *
 * <AUTHOR>
 * @date 2021/8/13
 */
@Slf4j
@Service
public class SlotCacheService {

    /**
     * 默认空广告位投流配置
     */
    private static final SlotBiddingConfigDto EMPTY_SLOT_BIDDING_CONFIG = new SlotBiddingConfigDto();

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private SlotConfigMapper slotConfigMapper;

    @Autowired
    private PluginService pluginService;

    @Autowired
    private SlotTagRelationService slotTagRelationService;

    @Autowired
    private TagManagerService tagManagerService;

    @Autowired
    private SlotBiddingConfigService slotBiddingConfigService;

    @Autowired
    private MapConfigService mapConfigService;

    /**
     * 广告位信息缓存
     */
    private final LoadingCache<Long, Optional<SlotCacheDto>> SLOT_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Optional<SlotCacheDto>>() {
                @Override
                public Optional<SlotCacheDto> load(Long slotId) {
                    Slot slot = slotMapper.selectSlotById(slotId);
                    if (null == slot) {
                        return Optional.empty();
                    }
                    SlotConfig slotConfig = slotConfigMapper.selectBySlotId(slotId);
                    return Optional.of(convertTo(slot, slotConfig));
                }

                @Override
                public ListenableFuture<Optional<SlotCacheDto>> reload(Long slotId, Optional<SlotCacheDto> oldValue) {
                    ListenableFutureTask<Optional<SlotCacheDto>> task = ListenableFutureTask.create(() -> load(slotId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告位标签缓存
     */
    private final LoadingCache<Long, Set<String>> SLOT_TAG_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(10, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Long, Set<String>>() {
                @Override
                public Set<String> load(Long slotId) {
                    Slot slot = slotMapper.selectSlotById(slotId);
                    if (null == slot) {
                        return Collections.emptySet();
                    }
                    List<Long> tagIds = slotTagRelationService.selectTagIdsBySlotId(slotId);
                    if (CollectionUtils.isEmpty(tagIds)) {
                        return Collections.emptySet();
                    }
                    List<TagManagerEntity> tags = tagManagerService.selectByIds(tagIds);
                    return tags.stream().map(TagManagerEntity::getTagName).collect(Collectors.toSet());
                }

                @Override
                public ListenableFuture<Set<String>> reload(Long slotId, Set<String> oldValue) {
                    ListenableFutureTask<Set<String>> task = ListenableFutureTask.create(() -> load(slotId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告位投流配置缓存
     */
    private final LoadingCache<Long, Optional<SlotBiddingConfigDto>> SLOT_BIDDING_CONFIG_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Long, Optional<SlotBiddingConfigDto>>() {
                @Override
                public Optional<SlotBiddingConfigDto> load(Long slotId) {
                    SlotBiddingConfigEntity config = slotBiddingConfigService.selectBySlotId(slotId);
                    if (null != config) {
                        return Optional.ofNullable(BeanUtil.copyProperties(config, SlotBiddingConfigDto.class));
                    }
                    // 查询白名单配置,后面移除
                    SlotBiddingConfigDto configDto = new SlotBiddingConfigDto();
                    configDto.setSlotId(slotId);
                    String mapConfig = mapConfigService.getMap(MapConfigEnum.SLOT_KS_COST_MAP, Long.class, String.class).get(slotId);
                    if (StringUtils.isNotBlank(mapConfig)) {
                        configDto.setIsEnable(EnableStatusEnum.ENABLE.getStatus());
                        configDto.setColdStart(1);
                        if (mapConfig.contains("_")) {
                            configDto.setConsumeType(2);
                            configDto.setConvPrice(Integer.parseInt(mapConfig.split("_")[0]) * 100);
                        } else {
                            configDto.setConsumeType(1);
                            configDto.setConvPrice(Integer.parseInt(mapConfig) * 100);
                        }
                    }
                    return Optional.of(configDto);
                }

                @Override
                public ListenableFuture<Optional<SlotBiddingConfigDto>> reload(Long slotId, Optional<SlotBiddingConfigDto> oldValue) {
                    ListenableFutureTask<Optional<SlotBiddingConfigDto>> task = ListenableFutureTask.create(() -> load(slotId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 兜底直投广告缓存
     */
    private final LoadingCache<Long, List<Long>> SLOT_DEGRADE_ORIENT_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(10, TimeUnit.HOURS)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build(new CacheLoader<Long, List<Long>>() {
                @Override
                public List<Long> load(Long slotId) {
                    SlotConfig config = slotConfigMapper.selectBySlotId(slotId);
                    if (null != config) {
                        List<Long> orientIds = JSON.parseArray(config.getDegradedOrientIds(), Long.class);
                        if (CollectionUtils.isNotEmpty(orientIds)) {
                            return orientIds;
                        }
                    }
                    return Collections.emptyList();
                }

                @Override
                public ListenableFuture<List<Long>> reload(Long slotId, List<Long> oldValue) {
                    ListenableFutureTask<List<Long>> task = ListenableFutureTask.create(() -> load(slotId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 查询广告位缓存
     *
     * @param slotId 广告位ID
     * @return 广告位缓存
     */
    public SlotCacheDto getSlotCache(Long slotId) {
        if (null != slotId) {
            try {
                return SLOT_CACHE.get(slotId).orElse(null);
            } catch (ExecutionException e) {
                log.error("查询广告位缓存异常, slotId={}", slotId, e);
            }
        }
        return null;
    }

    /**
     * 查询广告位是否存在指定标签
     *
     * @param slotId 广告位ID
     * @return true.存在,false.不存在
     */
    public boolean isTagExistBySlotId(Long slotId, String tagName) {
        if (null == slotId || StringUtils.isEmpty(tagName)) {
            return false;
        }
        try {
            return SLOT_TAG_CACHE.get(slotId).contains(tagName);
        } catch (ExecutionException e) {
            log.error("查询广告位标签缓存异常, slotId={}", slotId, e);
        }
        return false;
    }

    /**
     * 查询广告位投流配置缓存
     *
     * @param slotId 广告位ID
     * @return 广告位投流配置缓存
     */
    public SlotBiddingConfigDto getSlotBiddingConfigCache(Long slotId) {
        if (null != slotId) {
            try {
                return SLOT_BIDDING_CONFIG_CACHE.get(slotId).orElse(EMPTY_SLOT_BIDDING_CONFIG);
            } catch (ExecutionException e) {
                log.error("查询广告位投流配置缓存异常, slotId={}", slotId, e);
            }
        }
        return EMPTY_SLOT_BIDDING_CONFIG;
    }

    /**
     * 查询兜底直投广告配置ID列表
     *
     * @param slotId 广告位ID
     * @return 直投广告配置ID列表
     */
    public List<Long> getDegradedDirectAdvertOrientIds(Long slotId) {
        if (null != slotId) {
            try {
                return SLOT_DEGRADE_ORIENT_CACHE.get(slotId);
            } catch (ExecutionException e) {
                log.error("查询兜底直投广告配置ID列表缓存异常, slotId={}", slotId, e);
            }
        }
        return Collections.emptyList();
    }


    /**
     * 刷新广告位缓存
     *
     * @param slotId 广告位ID
     */
    public void refreshSlotCache(Long slotId) {
        if (null != slotId) {
            SLOT_CACHE.refresh(slotId);
            SLOT_TAG_CACHE.refresh(slotId);
            SLOT_BIDDING_CONFIG_CACHE.refresh(slotId);
            SLOT_DEGRADE_ORIENT_CACHE.refresh(slotId);
        }
    }

    /**
     * convert Slot to SlotCacheDto
     */
    private SlotCacheDto convertTo(Slot slot, SlotConfig slotConfig) {
        SlotCacheDto slotVO = new SlotCacheDto();
        slotVO.setId(slot.getId());
        slotVO.setAccountId(slot.getAccountId());
        slotVO.setAppId(slot.getAppId());
        slotVO.setStatus(slot.getStatus());
        slotVO.setSlotUrl(slot.getSlotUrl());

        // 广告位配置
        if (null != slotConfig) {
            slotVO.setRedirectType(slotConfig.getRedirectType());
            slotVO.setRedirectValue(slotConfig.getRedirectValue());

            // 活动投放设置
            if (isAreaTargetRedirect(slotConfig.getRedirectType())) {
                slotVO.setRedirectItems(JSON.parseArray(slotConfig.getRedirectValue(), AreaTargetRedirectItem.class));
            } else if (isShuntRedirect(slotConfig.getRedirectType())) {
                // 分流投放包装成地域定向投放
                AreaTargetRedirectItem redirectItem = new AreaTargetRedirectItem();
                redirectItem.setRedirectType(SHUNT.getType());
                redirectItem.setTargetArea(Collections.emptySet());
                redirectItem.setRedirectValue(JSON.parseArray(slotConfig.getRedirectValue(), ShuntRedirectItem.class));

                slotVO.setRedirectType(AREA_TARGET.getType());
                slotVO.setRedirectItems(Collections.singletonList(redirectItem));
            }

            // 域名设置
            slotVO.setDomainConfig(JSON.parseObject(slotConfig.getDomainConfig()));

            // 返回拦截设置
            if (StringUtils.isNotBlank(slotConfig.getRetConfig())) {
                RetConfigVO retConfig = JSON.parseObject(slotConfig.getRetConfig(), RetConfigVO.class);
                if (PluginSwitchEnum.isOn(retConfig.getIsOpen())) {
                    Long pluginId = retConfig.getPluginId();
                    Plugin plugin = pluginService.selectById(pluginId);
                    if (null != plugin) {
                        retConfig.setPluginType(plugin.getPluginType());
                        PluginInfo pluginInfo = JSON.parseObject(plugin.getPluginInfo(), PluginInfo.class);
                        if (null != pluginInfo) {
                            retConfig.setActivityId(pluginInfo.getActivityId());
                            retConfig.setUrl(pluginInfo.getUrl());
                        }
                    }
                }
                slotVO.setRetConfig(retConfig);
            }

            // 开关设置
            if (StringUtils.isNotBlank(slotConfig.getSwitchConfig())) {
                SlotSwitchConfig switchConfig = JSON.parseObject(slotConfig.getSwitchConfig(), SlotSwitchConfig.class);
                slotVO.setSwitchConfig(switchConfig);
            }
        }
        return slotVO;
    }
}
