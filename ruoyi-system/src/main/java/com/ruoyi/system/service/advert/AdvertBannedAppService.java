package com.ruoyi.system.service.advert;

import com.ruoyi.system.bo.advert.AdvertBannedAppBo;
import com.ruoyi.system.domain.advert.AdvertBannedApp;

import java.util.List;
import java.util.Map;

/**
 * 广告屏蔽媒体Service
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
public interface AdvertBannedAppService {

    /**
     * 查询配置的屏蔽媒体列表
     *
     * @param orientId 配置ID
     * @return 屏蔽媒体列表
     */
    List<AdvertBannedAppBo> selectListByOrientId(Long orientId);

    /**
     * 查询配置的屏蔽媒体列表
     *
     * @param orientId 配置ID
     * @return 屏蔽媒体列表
     */
    List<AdvertBannedApp> selectByOrientId(Long orientId);

    /**
     * 查询配置的屏蔽媒体列表
     *
     * @param advertId 广告ID
     * @return 配置ID-屏蔽媒体列表映射
     */
    Map<Long, List<AdvertBannedAppBo>> selectMapByAdvertId(Long advertId);
}
