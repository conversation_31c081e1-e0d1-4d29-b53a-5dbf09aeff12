package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.system.service.adengine.AdvertFilter;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 重复曝光过滤
 */
@Component
public class RepeatExposureFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        Set<Long> repeatAdvertIds = context.getRepeatAdvertIds();
        Set<Long> repeatOrientIds = context.getRepeatOrientIds();
        AdvertCacheDto ad = context.getAdvertCacheDto();
        return (CollectionUtils.isEmpty(repeatAdvertIds) || !repeatAdvertIds.contains(ad.getAdvertId()))
                && (CollectionUtils.isEmpty(repeatOrientIds) || !repeatOrientIds.contains(ad.getOrientId()));
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.REPEAT_EXPOSURE;
    }
}
