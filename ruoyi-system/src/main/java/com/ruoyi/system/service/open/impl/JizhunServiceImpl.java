package com.ruoyi.system.service.open.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.service.open.JizhunService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 极准对接Service接口实现
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
@Slf4j
@Service
public class JizhunServiceImpl implements JizhunService {

    private static final String CONV_URL = "https://api-extra.hzjizhun.com/hdt-trk/hdt/adv-cvt";
    private static final String DEV_KEY = "64a624700bc5b0e46e521a13";
    private static final String DEV_SECRET = "dywyASxmgCRmylosScwUQEtkfajIcijK";
    private static final String AUTHORIZATION = "Basic " + Base64.encode(DEV_KEY + ":" + DEV_SECRET);

    @Override
    public String behaviorUpload(String orderId, JSONObject param) {
        JSONObject body = new JSONObject();
        body.put("event", "event2");
        body.put("jcid", param.getString("jcid"));

        return HttpUtil.createPost(CONV_URL)
                .header("Authorization", "Basic NjRhNjI0NzAwYmM1YjBlNDZlNTIxYTEzOmR5d3lBU3htZ0NSbXlsb3NTY3dVUUV0a2ZhakljaWpL")
                .header("content-type", "application/json;charset=UTF-8")
                .body(body.toString())
                .execute().body();
    }

    @Override
    public String baijiuFormReport(String orderId, JSONObject param) {
        JSONObject body = new JSONObject();
        body.put("event", "event2");
        body.put("jcid", param.getString("jcid"));

        return HttpUtil.createPost(CONV_URL)
                .header("Authorization", "Basic NjRhNjI0NzAwYmM1YjBlNDZlNTIxYTEzOmR5d3lBU3htZ0NSbXlsb3NTY3dVUUV0a2ZhakljaWpL")
                .header("content-type", "application/json;charset=UTF-8")
                .body(body.toString())
                .execute().body();
    }
}
