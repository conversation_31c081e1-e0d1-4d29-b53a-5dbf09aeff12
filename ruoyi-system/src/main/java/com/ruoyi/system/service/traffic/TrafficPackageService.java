package com.ruoyi.system.service.traffic;

import com.ruoyi.system.bo.traffic.TrafficPackageListBo;
import com.ruoyi.system.entity.traffic.TrafficPackageEntity;
import com.ruoyi.system.req.traffic.TrafficPackageListReq;
import com.ruoyi.system.req.traffic.TrafficPackageReq;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 流量包 Service
 *
 * <AUTHOR>
 * @date 2022-8-23 10:31:21
 */
public interface TrafficPackageService {

    /**
     * 查询列表
     */
    List<TrafficPackageListBo> selectList(TrafficPackageListReq req);

    /**
     * 根据广告定向配置ID查询列表
     */
    List<TrafficPackageListBo> selectByOrientId(Long orientId);

    /**
     * 根据广告的流量包列表
     *
     * @param advertId 广告ID
     * @return 配置ID-流量包列表
     */
    Map<Long, List<TrafficPackageListBo>> selectByAdvertId(Long advertId);

    /**
     * 查询广告定向的流量包
     *
     * @param advertIds 广告ID列表
     * @return 广告ID-广告位ID集合映射
     */
    Map<Long, Set<Long>> selectTrafficSlotByAdvertIds(List<Long> advertIds);

    /**
     * 查询广告配置定向的流量包
     *
     * @param orientIds 配置ID列表
     * @return 配置ID-广告位ID集合映射
     */
    Map<Long, Set<Long>> selectTrafficSlotByOrientIds(List<Long> orientIds);

    /**
     * 新增流量包
     */
    Boolean insert(TrafficPackageReq req);

    /**
     * 更新流量包
     */
    Boolean update(TrafficPackageReq req);

    /**
     * 根据id获取
     */
    TrafficPackageEntity selectById(Long id);

    /**
     * 判断流量包名称是否重复
     *
     * @param name 流量包名称
     * @return 流量包名称是否重复
     */
    boolean isNameDuplicate(String name);
}
