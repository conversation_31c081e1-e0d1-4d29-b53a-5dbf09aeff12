package com.ruoyi.system.service.manager.impl;

import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.system.entity.account.AccountRelation;
import com.ruoyi.system.mapper.manager.AccountMapper;
import com.ruoyi.system.mapper.manager.AccountRelationMapper;
import com.ruoyi.system.service.manager.AccountRelationService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountRelationType.*;

/**
 * 账户关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-15
 */
@Service
public class AccountRelationServiceImpl implements AccountRelationService {

    @Autowired
    private AccountRelationMapper accountRelationMapper;

    @Autowired
    private AccountMapper accountMapper;

    @Override
    public Map<Long, List<Long>> selectByAccountIds(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }

        List<AccountRelation> rels = accountRelationMapper.selectByDestAccountIds(accountIds, Arrays.asList(BD_MANAGER.getType(), OPERATION_MANAGER.getType()));
        if (CollectionUtils.isEmpty(rels)) {
            return Collections.emptyMap();
        }

        // 过滤禁用的账号
        List<Long> srcAccountIds = ListUtils.mapToList(rels, AccountRelation::getSrcAccount);
        srcAccountIds = accountMapper.filterNormalAccountByIds(srcAccountIds);

        Map<Long, List<Long>> map = new HashMap<>();
        for (AccountRelation rel : rels) {
            if (!map.containsKey(rel.getDestAccount())) {
                map.put(rel.getDestAccount(), new ArrayList<>());
            }
            if(srcAccountIds.contains(rel.getSrcAccount())){
                map.get(rel.getDestAccount()).add(rel.getSrcAccount());
            }

        }
        return map;
    }

    @Override
    public List<Long> selectBySrcAccountIds(List<Long> srcAccountIds) {
        if (CollectionUtils.isEmpty(srcAccountIds)) {
            return Collections.emptyList();
        }
        List<AccountRelation> list = accountRelationMapper.selectBySrcAccountIds(srcAccountIds, Arrays.asList(BD_MANAGER.getType(), OPERATION_MANAGER.getType()));
        return ListUtils.mapToList(list, AccountRelation::getDestAccount);
    }

    @Override
    public Map<Long, Map<Integer, List<Long>>> selectMapByDestAccountIds(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<AccountRelation> list = accountRelationMapper.selectByDestAccountIds(accountIds, Arrays.asList(BD_MANAGER.getType(), OPERATION_MANAGER.getType()));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        // 过滤禁用的账号
        List<Long> srcAccountIds = ListUtils.mapToList(list, AccountRelation::getSrcAccount);
        srcAccountIds = accountMapper.filterNormalAccountByIds(srcAccountIds);

        Map<Long, Map<Integer, List<Long>>> map = new HashMap<>();
        for (AccountRelation relation : list) {
            if (!map.containsKey(relation.getDestAccount())) {
                map.put(relation.getDestAccount(), new HashMap<>());
            }
            if (!map.get(relation.getDestAccount()).containsKey(relation.getRelationType())) {
                map.get(relation.getDestAccount()).put(relation.getRelationType(), new ArrayList<>());
            }
            if(srcAccountIds.contains(relation.getSrcAccount())){
                map.get(relation.getDestAccount()).get(relation.getRelationType()).add(relation.getSrcAccount());
            }
        }
        return map;
    }

    @Override
    public List<AccountRelation> selectListByDestAccountAndRelationType(List<Long> destAccountIds, Integer relationType) {
        if (CollectionUtils.isEmpty(destAccountIds) || null == relationType) {
            return Collections.emptyList();
        }
        return accountRelationMapper.selectListByDestAccountAndRelationType(destAccountIds, relationType);
    }

    @Override
    public Set<Long> selectDestAccountIdByRelationType(Integer relationType) {
        if (null == relationType) {
            return Collections.emptySet();
        }
        List<AccountRelation> list = accountRelationMapper.selectListByDestAccountAndRelationType(null, relationType);
        return  list.stream().map(AccountRelation::getDestAccount).collect(Collectors.toSet());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRelation(Long srcAccountId, Long destAccountId, Integer relationType) {
        if (null == srcAccountId || null == destAccountId || null == relationType) {
            return;
        }
        accountRelationMapper.deleteAccountRelation(destAccountId, relationType);
        AccountRelation relation = new AccountRelation();
        relation.setSrcAccount(srcAccountId);
        relation.setDestAccount(destAccountId);
        relation.setRelationType(relationType);
        accountRelationMapper.insertAccountRelation(relation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateRelation(List<Long> srcAccountIds, Long destAccountId, Integer relationType) {
        if (null == destAccountId || null == relationType || CollectionUtils.isEmpty(srcAccountIds)) {
            return;
        }
        accountRelationMapper.deleteAccountRelation(destAccountId, relationType);
        srcAccountIds.forEach(srcAccountId -> {
            AccountRelation relation = new AccountRelation();
            relation.setSrcAccount(srcAccountId);
            relation.setDestAccount(destAccountId);
            relation.setRelationType(relationType);
            accountRelationMapper.insertAccountRelation(relation);
        });
    }
}
