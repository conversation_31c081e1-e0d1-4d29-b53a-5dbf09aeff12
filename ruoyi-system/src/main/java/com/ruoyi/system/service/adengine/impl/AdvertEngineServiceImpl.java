package com.ruoyi.system.service.adengine.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.local.RequestThreadLocal;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.advert.AdvertTypeEnum;
import com.ruoyi.common.enums.advert.ChargeTypeEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.*;
import com.ruoyi.system.bo.data.OrderDataBo;
import com.ruoyi.system.domain.adengine.*;
import com.ruoyi.system.domain.engine.MaterialCacheDto;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.AdStat;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.datashow.MobileHapDataEntity;
import com.ruoyi.system.manager.advert.OcpcManager;
import com.ruoyi.system.req.engine.NhAdvertReq;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.service.adengine.AdvertEngineService;
import com.ruoyi.system.service.adengine.AdvertFilterHandler;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.engine.cache.MobileCacheService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.domain.DomainReplaceService;
import com.ruoyi.system.service.engine.cache.SlotDataCacheService;
import com.ruoyi.system.service.engine.cache.WxIfrUrlCacheService;
import com.ruoyi.system.service.miniapp.MiniAppService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.util.LandpageUtil;
import com.ruoyi.system.vo.engine.NhAdvertResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATE_FORMAT;
import static com.ruoyi.common.constant.BizConstants.ARTICLE_RET_PAGE_DOMAIN;
import static com.ruoyi.common.enums.InnerLogType.*;
import static com.ruoyi.common.enums.advert.AdvertFlagEnum.*;
import static com.ruoyi.common.enums.advert.ChargeTypeEnum.isOCPC;

/**
 * 广告引擎接口实现
 *
 * <AUTHOR>
 * @date 2021/8/11
 */
@Service
public class AdvertEngineServiceImpl implements AdvertEngineService {

    private static final Logger log = LoggerFactory.getLogger(AdvertEngineServiceImpl.class);
    private static final Logger advertFilterLog = LoggerFactory.getLogger("advert-filter");

    /**
     * API落地页使用的本地缓存
     */
    private static final Cache<Long, String> API_LANDPAGE_CACHE = CacheUtil.newFIFOCache(100);

    /**
     * 广告重复过滤的曝光次数阈值
     */
    private static final int ADVERT_EXPOSURE_LIMIT = 2;

    /**
     * 广告重复过滤的点击次数阈值
     */
    private static final int ADVERT_CLICK_LIMIT = 1;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private AdvertFilterHandler advertFilterHandler;

    @Autowired
    private StatService statService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private DomainReplaceService domainReplaceService;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private MiniAppService miniAppService;

    @Autowired
    private MobileCacheService mobileCacheService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SlotDataCacheService slotDataCacheService;

    @Autowired
    private WxIfrUrlCacheService wxIfrUrlCacheService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private OcpcManager ocpcManager;

    @Autowired
    private WhitelistService whitelistService;

    @Override
    public NhAdvertResp getNhAdvert(NhAdvertReq req, JSONObject logJson) {
        RequestThreadLocal threadLocal = RequestThreadLocal.get();
        Long appId = threadLocal.getAppId();
        Long slotId = threadLocal.getSlotId();
        Long activityId = threadLocal.getActivityId();
        Long consumerId = threadLocal.getConsumerId();
        Date today = DateUtil.beginOfDay(new Date());
        String dateStr = DateUtil.formatDate(today);
        Integer hour = DateUtil.thisHour(true);
        String orderId = threadLocal.getOrderId();
        String ip = threadLocal.getIp();
        String userAgent = threadLocal.getUserAgent();
        threadLocal.setAdvertFlag(NH_ADVERT.getType());

        // 获取所有可投广告缓存
        List<AdvertCacheDto> validAdverts = advertCacheService.queryTotalAdvertCache();
        if (CollectionUtils.isEmpty(validAdverts)) {
            threadLocal.setAdvertFlag(DEGRADE.getType());
            return null;
        }

        // 解析手机型号
        String model = StringUtils.defaultString(UserAgentUtils.getModel(userAgent));
        String brand = mobileCacheService.getBrandByModel(model);
        MobileHapDataEntity mobileHapData = mobileCacheService.getDataByModel(model);

        // IP地址解析
        IpAreaDto ipArea = areaService.ipAnalysis(ip);
        String province = StringUtils.defaultString(ipArea.getProvince());
        // 发券次序
        Integer launchSeq = getLaunchSeq(today, consumerId) + 1;
        // 排序
        boolean isSortedByArpu = Objects.equals(req.getSortType(), 1);

        // 重复曝光的广告ID集合
//        Set<Long> repeatAdvertIds = getRepeatAdvertIds(consumerId, today);
        // 重复曝光的配置ID集合
        Set<Long> repeatOrientIds = getRepeatOrientIds(consumerId, today);

        // 过滤原因
        AdvertFilterReason filterReason = AdvertFilterReason.build(RequestThreadLocal.get());
        filterReason.setTotal(validAdverts.size());

        // 获取计费单价最高的可投广告
        AdvertCacheDto advert = validAdverts.stream()
                // 广告过滤
                .filter(ad -> {
                    AdvertFilterContext filterContext = new AdvertFilterContext();
                    filterContext.setAdvertCacheDto(ad);
                    filterContext.setAppId(appId);
                    filterContext.setSlotId(slotId);
                    filterContext.setActivityId(activityId);
                    filterContext.setToday(today);
                    filterContext.setDateStr(dateStr);
                    filterContext.setHour(hour);
                    filterContext.setIpArea(ipArea);
                    filterContext.setUserAgent(userAgent);
                    filterContext.setMobileBrand(brand);
                    filterContext.setIsp(req.getIsp());
                    filterContext.setMobileHapData(mobileHapData);
//                    filterContext.setRepeatAdvertIds(repeatAdvertIds);
                    filterContext.setRepeatOrientIds(repeatOrientIds);
                    filterContext.setFilterReason(filterReason);
                    return advertFilterHandler.doFilter(filterContext);
                })
                .peek(ad -> {
                    OrderDataBo ctrCvr = null;

                    // OCPC出价
                    if (isOCPC(ad.getChargeType())) {
                        ctrCvr = slotDataCacheService.getSlotAdvertCtrCvrCache(slotId, ad.getAdvertId(), launchSeq, brand, province);
                        Double pCtr = null != ctrCvr ? ctrCvr.getCtr() : 0;
                        Double pCvr = null != ctrCvr ? ctrCvr.getCvr() : 0;

                        Integer ocpcPrice = ocpcManager.calculateOcpcPrice(ad.getOrientId(), pCtr, pCvr);
                        if (null != ocpcPrice && ocpcPrice > 0) {
                            ad.setChargeType(ChargeTypeEnum.OCPC.getType());
                            ad.setUnitPrice(ocpcPrice);
                            ad.setOrderFactor(ocpcPrice * ad.getWeight());
                        }
                    }

                    // ARPU排序, 计算排序因子:CPC*统计CTR
                    if (isSortedByArpu) {
                        // 预估arpu = 预估广告出价 × CTR
                        // 预估广告出价 = max(CVR * 考核成本, 广告出价)
                        if (null == ctrCvr) {
                            ctrCvr = slotDataCacheService.getSlotAdvertCtrCvrCache(slotId, ad.getAdvertId(), launchSeq, brand, province);
                        }
                        ad.setPCtr(null != ctrCvr ? ctrCvr.getCtr() : 0);
                        ad.setPCvr(null != ctrCvr ? ctrCvr.getCvr() : 0);
                        double pCpc = ad.getPCvr() * NumberUtils.defaultInt(ad.getAssessCost()) / 100;
                        ad.setOrderFactor(ad.getPCtr() * Math.max(ad.getUnitPrice() / 100.0, pCpc));
                    }
                })
                // 获取计费单价最高的广告
                .max(Comparator.comparing(AdvertCacheDto::getOrderFactor))
                .orElse(null);

        // 打印过滤原因日志
        advertFilterLog.info(JSON.toJSONString(filterReason));

        // 获取不到可投广告，降级逻辑
        if (null == advert) {
            RequestThreadLocal.get().setAdvertFlag(DEGRADE.getType());
            return null;
        }

        // 权重随机出素材
        MaterialCacheDto material = advert.randomMaterial(threadLocal.getShuntHash());

        // 落地页域名替换
        String landpageUrl = advert.getLandpageUrl();
        if (null != threadLocal.getDomainConfig()) {
            landpageUrl = domainReplaceService.doReplaceLandpageDomain(landpageUrl, threadLocal.getDomainConfig(), userAgent);
        }
        // 落地页参数宏替换
        logJson.put("orderId", orderId);
        logJson.put("advertId", advert.getAdvertId());
        logJson.put("orientId", advert.getOrientId());
        // 落地页处理
        landpageUrl = handleLandpageUrl(landpageUrl, orderId, logJson);

        // 发券埋点
        logJson.put("materialId", material.getMaterialId());
        logJson.put("chargeType", advert.getChargeType());
        logJson.put("unitPrice", advert.getUnitPrice());
        logJson.put("assessType", advert.getAssessType());
        logJson.put("assessCost", advert.getAssessCost());
        logJson.put("landpageUrl", landpageUrl);
        logJson.put("originLandpageUrl", advert.getLandpageUrl());
        logJson.put("advertFlag", threadLocal.getAdvertFlag());
        logJson.put("launchSeq", getAndIncrLaunchSeq(today, consumerId));
        logJson.put("province", ipArea.getProvince());
        logJson.put("city", ipArea.getCity());
        logJson.put("areaNum", ipArea.getCityAreaNum());
        logJson.put("isp", ipArea.getIsp());
        // 出于性能考虑，暂时先去掉
//        if (null == advert.getPCtr() && null == advert.getPCvr()) {
//            OrderDataBo ctrCvr = slotDataCacheService.getSlotAdvertCtrCvrCache(slotId, advert.getAdvertId(), launchSeq, brand, province);
//            if (null != ctrCvr) {
//                logJson.put("pCtr", ctrCvr.getCtr());
//                logJson.put("pCvr", ctrCvr.getCvr());
//            }
//        } else {
//            logJson.put("pCtr", advert.getPCtr());
//            logJson.put("pCvr", advert.getPCvr());
//        }
        statService.advertLaunch(logJson);

        // 创建订单
        createOrder(orderId, req.getPluginId(), advert, material.getMaterialId(), landpageUrl, brand, model);

        // 返回广告和素材信息
        NhAdvertResp nhAdvertResp = BeanUtil.copyProperties(advert, NhAdvertResp.class);
        BeanUtil.copyProperties(material, nhAdvertResp);
        nhAdvertResp.setOrderId(orderId);
        nhAdvertResp.setLandpageUrl(landpageUrl);
        return nhAdvertResp;
    }

    @Override
    public Pair<Order, String> getNhDirectAdvert(String orderId, Long consumerId, Long accountId, Long appId, Long slotId,
                                                 String deviceId, String srid, Long advertId, String ip, String userAgent,
                                                 IpAreaDto ipArea, JSONObject extParam, boolean isDegraded) {
        if (null == appId || null == slotId || null == accountId || null == advertId || StringUtils.isBlank(deviceId)) {
            log.error("获取广告失败, 参数异常, accountId={}, appId={}, slotId={}, deviceId={}, srid={}, advertId={}",
                    accountId, appId, slotId, deviceId, srid, advertId);
            return null;
        }

        try {
            JSONObject logJson = new JSONObject();
            logJson.put("slotId", slotId);
            logJson.put("appId", appId);
            logJson.put("srid", srid);
            logJson.put("deviceId", deviceId);
            logJson.put("consumerId", consumerId);
            logJson.put("ip", ip);
            logJson.put("userAgent", userAgent);
            logJson.put("activityId", 0);
            logJson.put("accountId", accountId);

            RequestThreadLocal.get().setAccountId(accountId);
            RequestThreadLocal.get().setAppId(appId);
            RequestThreadLocal.get().setSlotId(slotId);
            RequestThreadLocal.get().setActivityId(0L);
            RequestThreadLocal.get().setConsumerId(consumerId);
            RequestThreadLocal.get().setSrid(srid);
            RequestThreadLocal.get().setIp(ip);

            // 券请求埋点
            if (!isDegraded) {
                statService.advertRequest(logJson);
            }

            // 获取诺禾广告
            Date today = DateUtil.beginOfDay(new Date());
            String dateStr = DateUtil.formatDate(today);
            Integer hour = DateUtil.thisHour(true);

            // 获取可投广告缓存
            List<AdvertCacheDto> validAdverts = advertCacheService.queryAdvertCache(advertId);
            if (CollectionUtils.isEmpty(validAdverts)) {
                return null;
            }

            // 解析手机型号
            String model = UserAgentUtils.getModel(userAgent);
            String brand = mobileCacheService.getBrandByModel(model);

            // 过滤原因
            AdvertFilterReason filterReason = AdvertFilterReason.build(RequestThreadLocal.get());
            filterReason.setTotal(validAdverts.size());

            // 获取计费单价最高的可投广告
            AdvertCacheDto advert = validAdverts.stream()
                    // 广告过滤
                    .filter(ad -> {
                        AdvertFilterContext filterContext = new AdvertFilterContext();
                        filterContext.setAdvertCacheDto(ad);
                        filterContext.setAppId(appId);
                        filterContext.setSlotId(slotId);
                        filterContext.setActivityId(0L);
                        filterContext.setConsumerId(consumerId);
                        filterContext.setToday(today);
                        filterContext.setDateStr(dateStr);
                        filterContext.setHour(hour);
                        filterContext.setIpArea(ipArea);
                        filterContext.setUserAgent(userAgent);
                        filterContext.setMobileBrand(brand);
                        filterContext.setExtParam(extParam);
                        filterContext.setFilterReason(filterReason);
                        return advertFilterHandler.doFilter(filterContext);
                    })
                    // 获取计费单价最高的广告
                    .max(Comparator.comparing(AdvertCacheDto::getOrderFactor))
                    .orElse(null);

            // 打印过滤原因日志
            advertFilterLog.info(JSON.toJSONString(filterReason));

            // 获取不到可投广告，降级逻辑
            if (null == advert) {
                return null;
            }

            // 落地页域名替换
            String landpageUrl = advert.getLandpageUrl();
            String originLandpageUrl = advert.getLandpageUrl();
            SlotCacheDto slot = slotCacheService.getSlotCache(slotId);
            if (null != slot) {
                landpageUrl = domainReplaceService.doReplaceLandpageDomain(landpageUrl, slot.getDomainConfig(), userAgent);
            }
            // 落地页参数宏替换
            logJson.put("orderId", orderId);
            logJson.put("advertId", advert.getAdvertId());
            logJson.put("orientId", advert.getOrientId());
            // 落地页处理
            landpageUrl = handleLandpageUrl(landpageUrl, orderId, logJson);

            // 发券埋点
            logJson.put("chargeType", advert.getChargeType());
            logJson.put("unitPrice", advert.getUnitPrice());
            logJson.put("materialId", 0);
            logJson.put("landpageUrl", landpageUrl);
            logJson.put("originLandpageUrl", originLandpageUrl);
            logJson.put("launchSeq", getAndIncrLaunchSeq(today, consumerId));
            if (null != ipArea) {
                logJson.put("province", ipArea.getProvince());
                logJson.put("city", ipArea.getCity());
                logJson.put("areaNum", ipArea.getCityAreaNum());
                logJson.put("isp", ipArea.getIsp());
            }
            statService.advertLaunch(logJson);

            // 创建订单
            Order order = createOrder(orderId, null, advert, 0L, landpageUrl, brand, model);

            // 返回广告落地页
            return Pair.of(order, landpageUrl);
        } catch (Exception e) {
            log.error("获取直投广告异常, accountId={}, appId={}, slotId={}, deviceId={}, srid={}, advertId={}",
                    accountId, appId, slotId, deviceId, srid, advertId, e);
        }
        return null;
    }

    @Override
    public Pair<Order, String> getNhDirectAdvert(String orderId, Long consumerId, Long accountId, Long appId, Long slotId,
                                                 String deviceId, String srid, List<Long> advertIds, String ip, String userAgent,
                                                 IpAreaDto ipArea, JSONObject extParam) {
        if (null == appId || null == slotId || null == accountId || CollectionUtils.isEmpty(advertIds) || StringUtils.isBlank(deviceId)) {
            log.error("获取广告失败, 参数异常, accountId={}, appId={}, slotId={}, deviceId={}, srid={}, advertIds={}",
                    accountId, appId, slotId, deviceId, srid, JSON.toJSONString(advertIds));
            return null;
        }

        try {
            JSONObject logJson = new JSONObject();
            logJson.put("slotId", slotId);
            logJson.put("appId", appId);
            logJson.put("srid", srid);
            logJson.put("deviceId", deviceId);
            logJson.put("consumerId", consumerId);
            logJson.put("ip", ip);
            logJson.put("userAgent", userAgent);
            logJson.put("activityId", 0);
            logJson.put("accountId", accountId);

            RequestThreadLocal.get().setAccountId(accountId);
            RequestThreadLocal.get().setAppId(appId);
            RequestThreadLocal.get().setSlotId(slotId);
            RequestThreadLocal.get().setActivityId(0L);
            RequestThreadLocal.get().setConsumerId(consumerId);
            RequestThreadLocal.get().setSrid(srid);
            RequestThreadLocal.get().setIp(ip);

            statService.advertRequest(logJson);

            // 获取诺禾广告
            Date today = DateUtil.beginOfDay(new Date());
            String dateStr = DateUtil.formatDate(today);
            Integer hour = DateUtil.thisHour(true);

            // 获取可投广告缓存
            List<AdvertCacheDto> validAdverts = advertCacheService.queryAdvertCache(advertIds);
            if (CollectionUtils.isEmpty(validAdverts)) {
                return null;
            }

            // 解析手机型号
            String model = UserAgentUtils.getModel(userAgent);
            String brand = mobileCacheService.getBrandByModel(model);

            // 过滤原因
            AdvertFilterReason filterReason = AdvertFilterReason.build(RequestThreadLocal.get());
            filterReason.setTotal(validAdverts.size());

            // 获取计费单价最高的可投广告
            Map<Long, Integer> positionMap = getAdvertPositionMap(advertIds);
            List<AdvertCacheDto> adverts = validAdverts.stream()
                    // 广告过滤
                    .filter(ad -> {
                        AdvertFilterContext filterContext = new AdvertFilterContext();
                        filterContext.setAdvertCacheDto(ad);
                        filterContext.setAppId(appId);
                        filterContext.setSlotId(slotId);
                        filterContext.setActivityId(0L);
                        filterContext.setConsumerId(consumerId);
                        filterContext.setToday(today);
                        filterContext.setDateStr(dateStr);
                        filterContext.setHour(hour);
                        filterContext.setIpArea(ipArea);
                        filterContext.setUserAgent(userAgent);
                        filterContext.setMobileBrand(brand);
                        filterContext.setExtParam(extParam);
                        filterContext.setFilterReason(filterReason);
                        return advertFilterHandler.doFilter(filterContext);
                    })
                    .peek(ad -> {
                        // 计算排序因子:CPC*统计CVR
                        Double cvr = slotDataCacheService.getSlotAdvertCvrCache(slotId, ad.getAdvertId());
                        ad.setCvrOrderFactor(ad.getUnitPrice() * cvr);
                    })
                    // CPC*统计CVR排序
                    .sorted((o1, o2) -> {
                        int cmp = o2.getCvrOrderFactor().compareTo(o1.getCvrOrderFactor());
                        return Objects.equals(cmp, 0) ? positionMap.get(o1.getAdvertId()).compareTo(positionMap.get(o2.getAdvertId())) : cmp;
                    })
                    .collect(Collectors.toList());

            // 打印过滤原因日志
            advertFilterLog.info(JSON.toJSONString(filterReason));

            // 获取不到可投广告，降级逻辑
            if (CollectionUtils.isEmpty(adverts)) {
                return null;
            }

            // 获取广告
            AdvertCacheDto advert;
            boolean isNewSlot = slotDataCacheService.isNewSlotCache(slotId);
            if (isNewSlot) {
                // 新广告位上线，广告位发券次数<=1000，平均分配所有流量(冷启动)
                advert = getAdvertByAverage(adverts);
            } else {
                // 按特定比例获取广告
                advert = getAdvertByProportion(adverts);
            }

            // 落地页域名替换
            String landpageUrl = advert.getLandpageUrl();
            String originLandpageUrl = advert.getLandpageUrl();
            SlotCacheDto slot = slotCacheService.getSlotCache(slotId);
            if (null != slot) {
                landpageUrl = domainReplaceService.doReplaceLandpageDomain(landpageUrl, slot.getDomainConfig(), userAgent);
            }

            // 落地页参数宏替换
            logJson.put("orderId", orderId);
            logJson.put("advertId", advert.getAdvertId());
            logJson.put("orientId", advert.getOrientId());
            // 落地页处理
            landpageUrl = handleLandpageUrl(landpageUrl, orderId, logJson);

            // 发券埋点
            logJson.put("chargeType", advert.getChargeType());
            logJson.put("unitPrice", advert.getUnitPrice());
            logJson.put("materialId", 0);
            logJson.put("landpageUrl", landpageUrl);
            logJson.put("originLandpageUrl", originLandpageUrl);
            logJson.put("launchSeq", getAndIncrLaunchSeq(today, consumerId));
            if (null != ipArea) {
                logJson.put("province", ipArea.getProvince());
                logJson.put("city", ipArea.getCity());
                logJson.put("areaNum", ipArea.getCityAreaNum());
                logJson.put("isp", ipArea.getIsp());
            }
            statService.advertLaunch(logJson);

            // 创建订单
            Order order = createOrder(orderId, null, advert, 0L, landpageUrl, brand, model);

            // 返回广告落地页
            return Pair.of(order, landpageUrl);
        } catch (Exception e) {
            log.error("获取直投广告异常, accountId={}, appId={}, slotId={}, deviceId={}, srid={}, advertIds={}",
                    accountId, appId, slotId, deviceId, srid, JSON.toJSONString(advertIds), e);
        }
        return null;
    }

    @Override
    public Pair<Order, String> getNhDirectAdvertOrient(String orderId, Long consumerId, Long accountId, Long appId, Long slotId,
                                                       String deviceId, String srid, List<Long> orientIds, String ip, String userAgent,
                                                       IpAreaDto ipArea, JSONObject extParam) {
        if (null == appId || null == slotId || null == accountId || CollectionUtils.isEmpty(orientIds) || StringUtils.isBlank(deviceId)) {
            log.error("获取广告失败, 参数异常, accountId={}, appId={}, slotId={}, deviceId={}, srid={}, orientIds={}",
                    accountId, appId, slotId, deviceId, srid, JSON.toJSONString(orientIds));
            return null;
        }

        try {
            JSONObject logJson = new JSONObject();
            logJson.put("slotId", slotId);
            logJson.put("appId", appId);
            logJson.put("srid", srid);
            logJson.put("deviceId", deviceId);
            logJson.put("consumerId", consumerId);
            logJson.put("ip", ip);
            logJson.put("userAgent", userAgent);
            logJson.put("activityId", 0);
            logJson.put("accountId", accountId);

            RequestThreadLocal.get().setAccountId(accountId);
            RequestThreadLocal.get().setAppId(appId);
            RequestThreadLocal.get().setSlotId(slotId);
            RequestThreadLocal.get().setActivityId(0L);
            RequestThreadLocal.get().setConsumerId(consumerId);
            RequestThreadLocal.get().setSrid(srid);
            RequestThreadLocal.get().setIp(ip);

            statService.advertRequest(logJson);

            // 获取诺禾广告
            Date today = DateUtil.beginOfDay(new Date());
            String dateStr = DateUtil.formatDate(today);
            Integer hour = DateUtil.thisHour(true);

            // 获取可投广告缓存
            List<AdvertCacheDto> validAdverts = advertCacheService.batchQueryAdvertCache(orientIds);
            if (CollectionUtils.isEmpty(validAdverts)) {
                return null;
            }

            // 重复曝光的配置ID集合
            Set<Long> repeatOrientIds = whitelistService.contains(WhitelistType.ADVERT_EXPOSURE_ONCE_SLOT, slotId)
                    ? getRepeatOrientIdsForDirectAdvert(consumerId, today) : null;

            // 解析手机型号
            String model = UserAgentUtils.getModel(userAgent);
            String brand = mobileCacheService.getBrandByModel(model);

            // 过滤原因
            AdvertFilterReason filterReason = AdvertFilterReason.build(RequestThreadLocal.get());
            filterReason.setTotal(validAdverts.size());

            // 获取计费单价最高的可投广告
            Map<Long, Integer> positionMap = getAdvertPositionMap(orientIds);
            List<AdvertCacheDto> adverts = validAdverts.stream()
                    // 广告过滤
                    .filter(ad -> {
                        AdvertFilterContext filterContext = new AdvertFilterContext();
                        filterContext.setAdvertCacheDto(ad);
                        filterContext.setAppId(appId);
                        filterContext.setSlotId(slotId);
                        filterContext.setActivityId(0L);
                        filterContext.setConsumerId(consumerId);
                        filterContext.setToday(today);
                        filterContext.setDateStr(dateStr);
                        filterContext.setHour(hour);
                        filterContext.setIpArea(ipArea);
                        filterContext.setUserAgent(userAgent);
                        filterContext.setMobileBrand(brand);
                        filterContext.setRepeatOrientIds(repeatOrientIds);
                        filterContext.setExtParam(extParam);
                        filterContext.setFilterReason(filterReason);
                        return advertFilterHandler.doFilter(filterContext);
                    })
                    .peek(ad -> {
                        Double cvr = slotDataCacheService.getSlotAdvertCvrCache(slotId, ad.getAdvertId());

                        // OCPC出价
                        if (isOCPC(ad.getChargeType())) {
                            Integer ocpcPrice = ocpcManager.calculateOcpcPrice(ad.getOrientId(), 1.0, cvr);
                            if (null != ocpcPrice && ocpcPrice > 0) {
                                ad.setChargeType(ChargeTypeEnum.OCPC.getType());
                                ad.setUnitPrice(ocpcPrice);
                                ad.setOrderFactor(ocpcPrice * ad.getWeight());
                            }
                        }
                        // 计算排序因子:CPC*统计CVR
                        ad.setCvrOrderFactor(ad.getUnitPrice() * cvr);
                    })
                    // CPC*统计CVR排序
                    .sorted((o1, o2) -> {
                        int cmp = o2.getCvrOrderFactor().compareTo(o1.getCvrOrderFactor());
                        return Objects.equals(cmp, 0) ? positionMap.get(o1.getOrientId()).compareTo(positionMap.get(o2.getOrientId())) : cmp;
                    })
                    .collect(Collectors.toList());

            // 打印过滤原因日志
            advertFilterLog.info(JSON.toJSONString(filterReason));

            // 获取不到可投广告，降级逻辑
            if (CollectionUtils.isEmpty(adverts)) {
                return null;
            }

            // 获取广告
            AdvertCacheDto advert;
            boolean isNewSlot = slotDataCacheService.isNewSlotCache(slotId);
            if (isNewSlot) {
                // 新广告位上线，广告位发券次数<=1000，平均分配所有流量(冷启动)
                advert = getAdvertByAverage(adverts);
            } else {
                // 按特定比例获取广告
                advert = getAdvertByProportion(adverts);
            }

            // 落地页域名替换
            String landpageUrl = advert.getLandpageUrl();
            String originLandpageUrl = advert.getLandpageUrl();
            SlotCacheDto slot = slotCacheService.getSlotCache(slotId);
            if (null != slot) {
                landpageUrl = domainReplaceService.doReplaceLandpageDomain(landpageUrl, slot.getDomainConfig(), userAgent);
            }
            // 落地页参数宏替换
            logJson.put("orderId", orderId);
            logJson.put("advertId", advert.getAdvertId());
            logJson.put("orientId", advert.getOrientId());
            // 落地页处理
            landpageUrl = handleLandpageUrl(landpageUrl, orderId, logJson);

            // 发券埋点
            logJson.put("chargeType", advert.getChargeType());
            logJson.put("unitPrice", advert.getUnitPrice());
            logJson.put("materialId", 0);
            logJson.put("landpageUrl", landpageUrl);
            logJson.put("originLandpageUrl", originLandpageUrl);
            logJson.put("launchSeq", getAndIncrLaunchSeq(today, consumerId));
            if (null != ipArea) {
                logJson.put("province", ipArea.getProvince());
                logJson.put("city", ipArea.getCity());
                logJson.put("areaNum", ipArea.getCityAreaNum());
                logJson.put("isp", ipArea.getIsp());
            }
            statService.advertLaunch(logJson);

            // 创建订单
            Order order = createOrder(orderId, null, advert, 0L, landpageUrl, brand, model);

            // 返回广告落地页
            return Pair.of(order, landpageUrl);
        } catch (Exception e) {
            log.error("获取直投广告异常, accountId={}, appId={}, slotId={}, deviceId={}, srid={}, orientIds={}",
                    accountId, appId, slotId, deviceId, srid, JSON.toJSONString(orientIds), e);
        }
        return null;
    }

    @Override
    public Pair<Order, String> getNhDirectAdvertOrientByDegraded(String orderId, Long consumerId, Long accountId, Long appId, Long slotId,
                                                                 String deviceId, String srid, List<Long> orientIds,
                                                                 String ip, String userAgent, IpAreaDto ipArea, JSONObject extParam) {
        if (null == appId || null == slotId || null == accountId || CollectionUtils.isEmpty(orientIds) || StringUtils.isBlank(deviceId)) {
            log.info("获取兜底直投广告失败, 参数异常, accountId={}, appId={}, slotId={}, deviceId={}, srid={}, orientIds={}",
                    accountId, appId, slotId, deviceId, srid, JSON.toJSONString(orientIds));
            return null;
        }

        try {
            JSONObject logJson = new JSONObject();
            logJson.put("slotId", slotId);
            logJson.put("appId", appId);
            logJson.put("srid", srid);
            logJson.put("deviceId", deviceId);
            logJson.put("consumerId", consumerId);
            logJson.put("ip", ip);
            logJson.put("userAgent", userAgent);
            logJson.put("activityId", 0);
            logJson.put("accountId", accountId);

            RequestThreadLocal.get().setAccountId(accountId);
            RequestThreadLocal.get().setAppId(appId);
            RequestThreadLocal.get().setSlotId(slotId);
            RequestThreadLocal.get().setActivityId(0L);
            RequestThreadLocal.get().setConsumerId(consumerId);
            RequestThreadLocal.get().setSrid(srid);
            RequestThreadLocal.get().setIp(ip);

            // 获取诺禾广告
            Date today = DateUtil.beginOfDay(new Date());
            String dateStr = DateUtil.formatDate(today);
            Integer hour = DateUtil.thisHour(true);

            // 获取可投广告缓存
            List<AdvertCacheDto> validAdverts = advertCacheService.batchQueryAdvertCache(orientIds);
            if (CollectionUtils.isEmpty(validAdverts)) {
                return null;
            }

            // 解析手机型号
            String model = UserAgentUtils.getModel(userAgent);
            String brand = mobileCacheService.getBrandByModel(model);

            // 过滤原因
            AdvertFilterReason filterReason = AdvertFilterReason.build(RequestThreadLocal.get());
            filterReason.setTotal(validAdverts.size());

            // 按照默认顺序获取可投广告
            List<AdvertCacheDto> adverts = validAdverts.stream()
                    // 广告过滤
                    .filter(ad -> {
                        AdvertFilterContext filterContext = new AdvertFilterContext();
                        filterContext.setAdvertCacheDto(ad);
                        filterContext.setAppId(appId);
                        filterContext.setSlotId(slotId);
                        filterContext.setActivityId(0L);
                        filterContext.setConsumerId(consumerId);
                        filterContext.setToday(today);
                        filterContext.setDateStr(dateStr);
                        filterContext.setHour(hour);
                        filterContext.setIpArea(ipArea);
                        filterContext.setUserAgent(userAgent);
                        filterContext.setMobileBrand(brand);
                        filterContext.setExtParam(extParam);
                        filterContext.setFilterReason(filterReason);
                        return advertFilterHandler.doFilter(filterContext);
                    })
                    .collect(Collectors.toList());

            // 打印过滤原因日志
            advertFilterLog.info(JSON.toJSONString(filterReason));

            // 获取不到可投广告，降级逻辑
            if (CollectionUtils.isEmpty(adverts)) {
                return null;
            }

            // 获取广告
            AdvertCacheDto advert = adverts.get(0);

            // 落地页域名替换
            String landpageUrl = advert.getLandpageUrl();
            String originLandpageUrl = advert.getLandpageUrl();
            SlotCacheDto slot = slotCacheService.getSlotCache(slotId);
            if (null != slot) {
                landpageUrl = domainReplaceService.doReplaceLandpageDomain(landpageUrl, slot.getDomainConfig(), userAgent);
            }
            // 落地页参数宏替换
            logJson.put("orderId", orderId);
            logJson.put("advertId", advert.getAdvertId());
            logJson.put("orientId", advert.getOrientId());
            // 落地页处理
            landpageUrl = handleLandpageUrl(landpageUrl, orderId, logJson);

            // 发券埋点
            logJson.put("chargeType", advert.getChargeType());
            logJson.put("unitPrice", advert.getUnitPrice());
            logJson.put("materialId", 0);
            logJson.put("landpageUrl", landpageUrl);
            logJson.put("originLandpageUrl", originLandpageUrl);
            logJson.put("launchSeq", getAndIncrLaunchSeq(today, consumerId));
            if (null != ipArea) {
                logJson.put("province", ipArea.getProvince());
                logJson.put("city", ipArea.getCity());
                logJson.put("areaNum", ipArea.getCityAreaNum());
                logJson.put("isp", ipArea.getIsp());
            }
            statService.advertLaunch(logJson);

            // 创建订单
            Order order = createOrder(orderId, null, advert, 0L, landpageUrl, brand, model);

            // 返回广告落地页
            return Pair.of(order, landpageUrl);
        } catch (Exception e) {
            log.error("获取兜底直投广告异常, accountId={}, appId={}, slotId={}, deviceId={}, srid={}, orientIds={}",
                    accountId, appId, slotId, deviceId, srid, JSON.toJSONString(orientIds), e);
        }
        return null;
    }

    /**
     * 创建订单
     */
    private Order createOrder(String orderId, Long pluginId, AdvertCacheDto advert, Long materialId, String landpageUrl,
                              String brand, String model) {
        Order order = new Order();
        order.setOrderId(orderId);
        order.setConsumerId(RequestThreadLocal.get().getConsumerId());
        order.setAppId(RequestThreadLocal.get().getAppId());
        order.setSlotId(RequestThreadLocal.get().getSlotId());
        order.setActivityId(RequestThreadLocal.get().getActivityId());
        order.setAdvertId(advert.getAdvertId());
        order.setOrientId(advert.getOrientId());
        order.setMaterialId(materialId);

        // 广告额外信息
        AdSnapshot snapshot = new AdSnapshot();
        snapshot.setAdvertName(advert.getAdvertName());
        snapshot.setThumbnailImg(advert.getThumbnailImg());
        snapshot.setAdvertType(AdvertTypeEnum.NH.getType());
        snapshot.setAdvertCategory(advert.getAdvertCategory());
        snapshot.setAdvertFlag(RequestThreadLocal.get().getAdvertFlag());
        snapshot.setChargeType(advert.getChargeType());
        snapshot.setUnitPrice(advert.getUnitPrice());
        snapshot.setMilliUnitPrice(advert.getMilliUnitPrice());
        snapshot.setAssessType(advert.getAssessType());
        snapshot.setAssessCost(advert.getAssessCost());
        snapshot.setSrid(RequestThreadLocal.get().getSrid());
        snapshot.setIp(RequestThreadLocal.get().getIp());
        snapshot.setLandpageUrl(landpageUrl);
        snapshot.setOriginLandpageUrl(advert.getOriginLandpageUrl());
        snapshot.setPluginId(pluginId);
        snapshot.setModel(model);
        snapshot.setBrand(brand);
        snapshot.setExtInfo(advert.getExtInfo());
        order.setAdSnapshot(JSON.toJSONString(snapshot));

        // 订单埋点，直投广告直接记录券点击时间
        if (Objects.equals(order.getActivityId(), 0L)) {
            AdStat adStat = new AdStat();
            adStat.setClicks(Collections.singletonList(System.currentTimeMillis()));
            order.setAdStat(JSON.toJSONString(adStat));
        }

        // 直投广告临时缓存
        orderService.cacheOrder(order);

        // 订单落库
        GlobalThreadPool.insertExecutorService.execute(() -> orderService.createOrder(order));

        return order;
    }

    /**
     * 获取重复曝光的广告ID
     * 条件:当日2次曝光或者1次点击的广告
     *
     * @param consumerId 用户ID
     * @param date       日期
     * @return 重复曝光的广告ID集合
     */
    private Set<Long> getRepeatAdvertIds(Long consumerId, Date date) {
        String dateStr = DateUtil.formatDate(date);
        Set<Long> repeatAdvertIds = new HashSet<>();
        try {
            // 查询券曝光
            Map exposureMap = redisCache.getCacheMap(EngineRedisKeyFactory.K044.join(consumerId, dateStr, ADVERT_EXPOSURE.getType()));
            if (MapUtils.isNotEmpty(exposureMap)) {
                exposureMap.forEach((advertId, times) -> {
                    if (Convert.toInt(times) >= ADVERT_EXPOSURE_LIMIT) {
                        repeatAdvertIds.add(Convert.toLong(advertId));
                    }
                });
            } else {
                return repeatAdvertIds;
            }
            // 查询券点击
            Map clickMap = redisCache.getCacheMap(EngineRedisKeyFactory.K044.join(consumerId, dateStr, ADVERT_CLICK.getType()));
            if (MapUtils.isNotEmpty(clickMap)) {
                clickMap.forEach((advertId, times) -> {
                    if (Convert.toInt(times) >= ADVERT_CLICK_LIMIT) {
                        repeatAdvertIds.add(Convert.toLong(advertId));
                    }
                });
            }
        } catch (Exception e) {
            log.error("获取重复曝光的广告ID异常, consumerId={}, date={}", consumerId, dateStr, e);
        }
        return repeatAdvertIds;
    }

    /**
     * 获取重复曝光的配置ID
     * 条件:当日2次曝光或者1次点击的配置
     *
     * @param consumerId 用户ID
     * @param date       日期
     * @return 重复曝光的配置ID集合
     */
    private Set<Long> getRepeatOrientIds(Long consumerId, Date date) {
        String dateStr = DateUtil.formatDate(date);
        Set<Long> repeatOrientIds = new HashSet<>();
        try {
            // 查询券曝光
            Map exposureMap = redisCache.getCacheMap(EngineRedisKeyFactory.K084.join(consumerId, dateStr, ADVERT_EXPOSURE.getType()));
            if (MapUtils.isNotEmpty(exposureMap)) {
                exposureMap.forEach((orientId, times) -> {
                    if (Convert.toInt(times) >= ADVERT_EXPOSURE_LIMIT) {
                        repeatOrientIds.add(Convert.toLong(orientId));
                    }
                });
            } else {
                return repeatOrientIds;
            }
            // 查询券点击
            Map clickMap = redisCache.getCacheMap(EngineRedisKeyFactory.K084.join(consumerId, dateStr, ADVERT_CLICK.getType()));
            if (MapUtils.isNotEmpty(clickMap)) {
                clickMap.forEach((orientId, times) -> {
                    if (Convert.toInt(times) >= ADVERT_CLICK_LIMIT) {
                        repeatOrientIds.add(Convert.toLong(orientId));
                    }
                });
            }
        } catch (Exception e) {
            log.error("获取重复曝光的配置ID异常, consumerId={}, date={}", consumerId, dateStr, e);
        }
        return repeatOrientIds;
    }

    /**
     * 获取重复点击的配置ID(直投广告使用,直投广告没有曝光！)
     * 条件:当日1次点击的配置
     *
     * @param consumerId 用户ID
     * @param date       日期
     * @return 重复点击的配置ID集合
     */
    private Set<Long> getRepeatOrientIdsForDirectAdvert(Long consumerId, Date date) {
        String dateStr = DateUtil.formatDate(date);
        Set<Long> repeatOrientIds = new HashSet<>();
        try {
            // 查询券点击
            Map clickMap = redisCache.getCacheMap(EngineRedisKeyFactory.K084.join(consumerId, dateStr, ADVERT_CLICK.getType()));
            if (MapUtils.isNotEmpty(clickMap)) {
                clickMap.forEach((orientId, times) -> {
                    if (Convert.toInt(times) >= ADVERT_CLICK_LIMIT) {
                        repeatOrientIds.add(Convert.toLong(orientId));
                    }
                });
            }
        } catch (Exception e) {
            log.error("获取重复点击的配置ID异常, consumerId={}, date={}", consumerId, dateStr, e);
        }
        return repeatOrientIds;
    }

    /**
     * 落地页处理
     *
     * @param originUrl 落地页原链接
     * @param data      数据
     * @return 处理后的落地页链接
     */
    private String handleLandpageUrl(String originUrl, String orderId, JSONObject data) {
        try {
            // 支付宝deeplink不处理
            if (StringUtils.isBlank(originUrl) || originUrl.startsWith("alipays://")) {
                return originUrl;
            }

            // 通过接口获取落地页链接
            if (StrUtil.startWithAny(originUrl, "api://")) {
                boolean nocache = originUrl.contains("nocache");
                originUrl = getLandpageByApiSlot(originUrl, data);
//                originUrl = getLandpageByApi(originUrl, data);
                if (!nocache) {
                    if (StringUtils.isNotBlank(originUrl)) {
                        // 缓存API链接
                        cacheLandpageApiUrl(originUrl, data.getLong("orientId"));
                    } else {
                        // 获取缓存的API链接
                        originUrl = getLandpageByApiByCache(data.getLong("orientId"));
                    }
                }
                if (StringUtils.isBlank(originUrl)) {
                    return originUrl;
                }
            }

            // 落地页添加订单号
            Map<String, String> urlParam = new HashMap<>(1);
            if (!originUrl.contains("__N_OID__") && !originUrl.contains("mp.weixin.qq.com") && !originUrl.startsWith("weixin://dl/business/")) {
                urlParam.put("nadkey", orderId);
            }
            // 特殊场景透传广告位参数
            HttpServletRequest request = ServletUtils.getRequest();
            if (null != request) {
                Optional.ofNullable(request.getParameter("qz_gdt")).ifPresent(s -> urlParam.put("qz_gdt", s));
                Optional.ofNullable(request.getParameter("gdt_vid")).ifPresent(s -> urlParam.put("gdt_vid", s));
            }

            String landpageUrl = UrlUtils.appendParams(originUrl, urlParam);

            // 宏替换
            landpageUrl = MacroUtils.lpMacroReplace(landpageUrl, data);

            // 跳转小程序的落地页处理
            landpageUrl = handleWeixinLandpageUrl(landpageUrl);
            // 微信防封落地页处理
            return handleIfrLandpageUrl(landpageUrl);
        } catch (Exception ignore) {
        }
        return originUrl;
    }

    /**
     * 处理跳转小程序的落地页(weixin://http)
     *
     * @param originUrl 落地页原链接
     * @return 小程序Scheme
     */
    private String handleWeixinLandpageUrl(String originUrl) {
        // 仅处理weixin://http的落地页
        if (!StrUtil.startWithIgnoreCase(originUrl, "weixin://http")) {
            return originUrl;
        }
        String url = StrUtil.removePrefixIgnoreCase(originUrl, "weixin://");
        String wxScheme = miniAppService.getUrlScheme("pages/direct/direct", "lp=" + UrlUtils.urlEncode(url), 30);
        // 如果微信scheme生成失败，返回去掉weixin://的原落地页链接
        return StrUtil.blankToDefault(wxScheme, url);
    }

    /**
     * 处理跳转微信防封的落地页(ifr://http)
     *
     * @param originUrl 落地页原链接
     * @return 微信防封处理后的落地页
     */
    private String handleIfrLandpageUrl(String originUrl) {
        // 仅处理ifr://http的落地页
        if (!StrUtil.startWithIgnoreCase(originUrl, "ifr://http")) {
            return originUrl;
        }

        // 非微信环境下不处理
        if (!StrUtil.containsIgnoreCase(ServletUtils.getUserAgent(), "MicroMessenger")) {
            return StrUtil.removePrefixIgnoreCase(originUrl, "ifr://");
        }

        // 获取微信防封链接
        String ifrUrl = wxIfrUrlCacheService.getWxIfrUrl(LandpageUtil.extractLpk(originUrl));
        if (StringUtils.isBlank(ifrUrl)) {
            return StrUtil.removePrefixIgnoreCase(originUrl, "ifr://");
        }

        // 落地页链接处理
        String url = StrUtil.removePrefixIgnoreCase(originUrl, "ifr://");
        return (url.startsWith("http://") ? "http://" : "https://") + ifrUrl + UrlUtils.urlEncode(url);
    }

    /**
     * 按平均占比获取广告
     * 公式: 下标 = 随机值 / ceil(100 / 池子数量)
     *
     * @param adverts 广告列表
     * @return 命中的广告
     */
    private AdvertCacheDto getAdvertByAverage(List<AdvertCacheDto> adverts) {
        AdvertCacheDto advert = adverts.get(0);
        if (adverts.size() > 1) {
            int rand = RandomUtil.randomInt(100);
            int per = (int) Math.ceil(100.0 / adverts.size());
            advert = adverts.get(rand / per);
        }
        return advert;
    }

    /**
     * 按特定占比获取广告
     * 规则:
     * 2个广告计划：大池子80%，小池子20%
     * 3~5个广告：大池子50%，小池子50%平分到4个计划
     * 小池子公式: 下标 = (随机值 - 大池子比例) / ceil(小池子比例 / 小池子数量) + 1
     * 随机值分布如下:
     * 0-79, 80-99
     * 0-49, 50-74, 75-99
     * 0-49, 50-66, 67-83, 84-99
     * 0-49, 50-62, 63-75, 76-88, 87-99
     *
     * @param adverts 广告列表
     * @return 命中的广告
     */
    private AdvertCacheDto getAdvertByProportion(List<AdvertCacheDto> adverts) {
        AdvertCacheDto advert = adverts.get(0);
        if (adverts.size() == 2) {
            int rand = RandomUtil.randomInt(100);
            if (rand >= 80) {
                advert = adverts.get(1);
            }
        } else if (adverts.size() > 2) {
            int rand = RandomUtil.randomInt(100);
            if (rand >= 50) {
                int per = (int) Math.ceil(50.0 / (adverts.size() - 1));
                advert = adverts.get((rand - 50) / per + 1);
            }
        }
        return advert;
    }

    /**
     * 广告ID列表转成广告ID-下标映射(用于排序)
     *
     * @param advertIds 广告ID列表
     * @return 广告ID-下标映射
     */
    private Map<Long, Integer> getAdvertPositionMap(List<Long> advertIds) {
        Map<Long, Integer> positionMap = new HashMap<>(advertIds.size());
        for (int i = 0; i < advertIds.size(); i++) {
            positionMap.put(advertIds.get(i), i);
        }
        return positionMap;
    }

    /**
     * 获取用户今日发券次序
     *
     * @param date       日期
     * @param consumerId 用户ID
     * @return 用户今日第几次发券
     */
    private int getLaunchSeq(Date date, Long consumerId) {
        try {
            Long seq = redisAtomicClient.getLong(EngineRedisKeyFactory.K095.join(DateUtil.formatDate(date), consumerId));
            return null != seq ? seq.intValue() : 0;
        } catch (Exception e) {
            log.error("getLaunchSeq error, consumerId={}", consumerId, e);
        }
        return 0;
    }

    /**
     * 获取并自增用户今日发券次序
     *
     * @param date       日期
     * @param consumerId 用户ID
     * @return 用户今日第几次发券
     */
    private int getAndIncrLaunchSeq(Date date, Long consumerId) {
        try {
            Long seq = redisAtomicClient.incrBy(EngineRedisKeyFactory.K095.join(DateUtil.formatDate(date), consumerId), 1, 1, TimeUnit.DAYS);
            return seq.intValue();
        } catch (Exception e) {
            log.error("getAndIncrLaunchSeq error, consumerId={}", consumerId, e);
        }
        return 1;
    }

    /**
     * 通过API获取落地页地址
     *
     * @param apiUrl API接口地址
     * @return 落地页地址
     */
    private static String WX_JUPM = "/web-static/static-html/jump.html?";

    private String getLandpageByApiSlot(String apiUrl, JSONObject data) throws Exception {
        String landpageByApi = getLandpageByApi(apiUrl, data);
        if (StringUtils.isEmpty(landpageByApi) || !landpageByApi.contains(WX_JUPM)) {
            return landpageByApi;
        }
        if (!apiUrl.contains("ret") && Objects.equals(ServletUtils.getParameter("ret"), "1")) {
            return landpageByApi;
        }
        // 文章链接处理：广告位
        String slotId = data.getString("slotId");
        if (!NumberUtil.isNumber(slotId)) {
            return landpageByApi;
        }
        // 原始链接：http://20240328112xxxx.r8i22.cn/web-static/static-html/jump.html?url=http%3
        String endFix = slotId;

        // 正常域名拼接广告位，其他其他域名要拼接【分钟】
        boolean fc2Flag = false;
        boolean fc3Flag = false;
        if (slotCacheService.isTagExistBySlotId(Long.valueOf(slotId), "文章静态2级跳")) {
            fc2Flag = true;
            endFix = endFix + DateUtils.dateMin();
        }
        if (slotCacheService.isTagExistBySlotId(Long.valueOf(slotId), "文章静态3级跳")) {
            fc3Flag = true;
            endFix = endFix + DateUtils.dateMin();
        }

        // 1.空白页：正常链接返回
        String[] split = landpageByApi.split("\\.");
        // 切割后：http://20240328112xxxx
        String firstDomain = split[0];
        firstDomain = firstDomain + endFix;
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(firstDomain);
        // 广告位域名拼接回去
        for (int i = 1; i < split.length; i++) {
            stringBuilder.append(".");
            stringBuilder.append(split[i]);
        }
        landpageByApi = stringBuilder.toString();
        if (!fc2Flag && !fc3Flag) {
            // 正常链接直接返回：非2级跳，非3级跳
            return landpageByApi;
        }
        // 丰巢的嵌套逻辑：http://20240328112xxxx.r8i22.cn/web-static/static-html/jump.html?url=http%3
        String[] newAddUrl = landpageByApi.split(WX_JUPM);
        // 裁剪后：http://20240328112slotId18.r8i22.cn/
        String firstDomainUrl = newAddUrl[0];
        String encode = URLEncoder.encode(landpageByApi, "utf-8");
        // 返回：url拼接自己,endFix：20240328112slotId18
        String firstDomainUrl2 = firstDomainUrl.replaceAll(endFix, "ft" + endFix);
        firstDomainUrl2 = firstDomainUrl2 + WX_JUPM + "url=" + encode;
        // 2.空白页2级跳转
        if (fc2Flag) {
            return firstDomainUrl2;
        }
        // 3.空白页3级跳转
        String firstDomainUrl3 = firstDomainUrl.replaceAll(endFix, "sd" + endFix);
        firstDomainUrl2 = URLEncoder.encode(firstDomainUrl2, "utf-8");
        firstDomainUrl3 = firstDomainUrl3 + WX_JUPM + "url=" + firstDomainUrl2;
        return firstDomainUrl3;
    }

    private String getLandpageByApi(String apiUrl, JSONObject data) {
        try {
            if (apiUrl.startsWith("api://")) {
                apiUrl = StrUtil.removePrefix(apiUrl, "api://");
            }
            if (apiUrl.contains("__N_DEVICE__")) {
                if (apiUrl.contains("/api/wz/")) {
                    apiUrl += "&sid=" + data.getString("slotId");
                    if (!apiUrl.contains("ret") && Objects.equals(ServletUtils.getParameter("ret"), "1")) {
                        apiUrl += "&ret=1";
                    }
                }
                apiUrl = apiUrl.replace("__N_DEVICE__", data.getString("deviceId"));
            }
            // 域内域外判断处理
            if (apiUrl.contains("dp=1") && StrUtil.containsIgnoreCase(ServletUtils.getUserAgent(), "micromessenger")) {
                apiUrl = apiUrl.replace("dp=1", "dp=0");
            }
            String resp = HttpUtil.get(apiUrl);
            JSONObject result = JSON.parseObject(resp);
            if (null != result) {
                if (apiUrl.contains("miniu.bypanghu.xyz")) {
                    JSONArray arr = result.getJSONArray("data");
                    if (null != arr && arr.size() > 0) {
                        return arr.get(0).toString();
                    }
                }
                if (result.containsKey("url")) {
                    return result.getString("url");
                }
                if (result.containsKey("jump")) {
                    return result.getString("jump");
                }
            }
        } catch (Exception e) {
            log.error("getLandpageByApi error, apiUrl={}", apiUrl, e);
        }
        return "";
    }

    /**
     * 缓存API落地页获取的链接
     */
    private void cacheLandpageApiUrl(String url, Long orientId) {
        try {
            if (StringUtils.isNotBlank(url) && null != orientId) {
                API_LANDPAGE_CACHE.put(orientId, url);
            }
        } catch (Exception e) {
            log.error("cacheLandpageApiUrl error, orientId={}", orientId, e);
        }
    }

    /**
     * 通过缓存获取API落地页地址
     *
     * @param orientId 配置ID
     * @return 落地页地址
     */
    private String getLandpageByApiByCache(Long orientId) {
        try {
            if (null != orientId) {
                return API_LANDPAGE_CACHE.get(orientId);
            }
        } catch (Exception e) {
            log.error("getLandpageByApiByCache error, orientId={}", orientId, e);
        }
        return "";
    }

    @Override
    public String cdnRedirect(String url) {
        String index = DateUtil.format(new Date(), PURE_DATE_FORMAT) + DateUtil.thisHour(true) + DateUtil.thisMinute() / 20;
        return StrUtil.format("http://{}." + ARTICLE_RET_PAGE_DOMAIN + "/web-static/static-html/jump.html?url={}", index, UrlUtils.urlEncode(url));
    }
}
