package com.ruoyi.system.service.oa.department;

import com.ruoyi.system.entity.oa.department.DepartmentEntity;

import java.util.List;
import java.util.Map;

/**
 * oa部门表 Service
 *
 * <AUTHOR>
 * @date 2021-10-9 14:20:23
 */
public interface DepartmentService {

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    DepartmentEntity selectById(Long id);

    /**
     * 根据部门id列表获取部门名称
     *
     * @param departmentIds 部门id列表
     * @return <部门id，部门名称>
     */
    Map<Long, String> selectDepartmentNameMap(List<Long> departmentIds);

    /**
     * 根据部门id列表获取部门Key
     *
     * @param departmentIds 部门id列表
     * @return 部门id-部门Key映射
     */
    Map<Long, String> selectDepartmentKeyMap(List<Long> departmentIds);

    /**
     * 查询所有部门
     *
     * @return 结果
     */
    List<DepartmentEntity> selectAllDepartment();

    /**
     * 根据公司id列表查询部门列表
     *
     * @param companyIds 公司id列表
     * @return 部门列表
     */
    List<DepartmentEntity> selectByCompanyIds(List<Long> companyIds);

    /**
     * 查询部门Id列表
     *
     * @param departmentKeys 部门Key列表
     * @return 部门ID列表
     */
    List<Long> selectDepartmentIds(List<String> departmentKeys);
}
