package com.ruoyi.system.service.kefu.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.bo.kefu.IntendedFormBo;
import com.ruoyi.system.bo.kefu.IntendedFormImportErrorBo;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.landpage.ExternalLandpageFormSendRecordEntity;
import com.ruoyi.system.entity.landpage.ExternalLandpageRecord;
import com.ruoyi.system.entity.landpage.ExternalLandpageRecordHistoryEntity;
import com.ruoyi.system.service.common.IdCardService;
import com.ruoyi.system.service.kefu.IntendedFormService;
import com.ruoyi.system.service.landpage.ExternalLandpageFormSendRecordService;
import com.ruoyi.system.service.landpage.ExternalLandpageRecordHistoryService;
import com.ruoyi.system.service.landpage.ExternalLandpageRecordService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 意向用户服务实现
 *
 * <AUTHOR>
 * @date 2023/04/10
 */
@Slf4j
@Service
public class IntendedFormServiceImpl implements IntendedFormService {

    @Autowired
    private ExternalLandpageRecordService externalLandpageRecordService;

    @Autowired
    private ExternalLandpageRecordHistoryService externalLandpageRecordHistoryService;

    @Autowired
    private ExternalLandpageFormSendRecordService externalLandpageFormSendRecordService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private IdCardService idCardService;

    @Override
    public List<IntendedFormBo> analysisExcel(MultipartFile file) {
        byte[] bytes;
        try {
            bytes = file.getBytes();
        } catch (IOException e) {
            log.error("客服意向客户Excel解析异常", e);
            throw new CustomException("Excel解析异常");
        }

        try {
            List<IntendedFormBo> list = new LinkedList<>();
            ExcelUtil.readBySax(new ByteArrayInputStream(bytes), 0, (sheetIndex, rowIndex, rows) -> {
                if (CollectionUtils.isEmpty(rows) || rowIndex < 1) {
                    return;
                }
                List<String> columns = new ArrayList<>(7);
                rows.forEach(row -> columns.add(StrUtil.trim(Convert.toStr(row))));

                if (null != CollUtil.findOne(columns, StringUtils::isNotBlank)) {
                    IntendedFormBo form = new IntendedFormBo();
                    form.setRowId(rowIndex);
                    form.setExternalNo(columns.get(0));
                    form.setName(columns.get(1));
                    form.setPhone(columns.get(2));
                    form.setProvince(columns.get(3));
                    form.setCity(columns.get(4));
                    form.setDistrict(columns.get(5));
                    form.setAddress(columns.get(6));
                    list.add(form);
                }
            });
            return list;
        } catch (Exception e) {
            log.error("意向用户Excel解析异常", e);
            throw new CustomException("Excel解析异常");
        }
    }

    @Override
    public List<IntendedFormImportErrorBo> dataVerify(List<IntendedFormBo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Set<String> existExternalNoList = externalLandpageRecordService.selectExternalNoSet(ListUtils.mapToList(list, IntendedFormBo::getExternalNo));
        Set<String> sentExternalNoList = externalLandpageFormSendRecordService.selectExternalNoSet(new ArrayList<>(existExternalNoList));

        List<IntendedFormImportErrorBo> errList = new ArrayList<>();
        Set<String> errExternalNoSet = new HashSet<>();
        for (IntendedFormBo form : list) {
            String reason = null;
            if (StringUtils.isBlank(form.getExternalNo())) {
                reason = "客户单号为空";
            } else if (StringUtils.isBlank(form.getName())) {
                reason = "姓名为空";
            } else if (!StrUtil.isNumeric(form.getPhone()) || StrUtil.length(form.getPhone()) != 11) {
                reason = "号码格式错误";
            } else if (!existExternalNoList.contains(form.getExternalNo())) {
                reason = "客户单号不存在";
            } else if (sentExternalNoList.contains(form.getExternalNo())) {
                reason = "老客户，已回传过";
            }
            if (null != reason) {
                IntendedFormImportErrorBo error = new IntendedFormImportErrorBo();
                error.setRowId(form.getRowId());
                error.setExternalNo(form.getExternalNo());
                error.setName(form.getName());
                error.setPhone(form.getPhone());
                error.setProvince(form.getProvince());
                error.setCity(form.getCity());
                error.setDistrict(form.getDistrict());
                error.setAddress(form.getAddress());
                error.setReason(reason);
                errList.add(error);
                errExternalNoSet.add(form.getExternalNo());
            }
        }
        if (CollectionUtils.isNotEmpty(errExternalNoSet)) {
            list.removeIf(form -> errExternalNoSet.contains(form.getExternalNo()));
        }
        return errList;
    }

    @Override
    public void handleForm(Long operAccountId, List<IntendedFormBo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            List<String> externalNoList = ListUtils.mapToList(list, IntendedFormBo::getExternalNo);
            Map<String, ExternalLandpageRecord> recordMap = externalLandpageRecordService.selectMapByExternalNo(externalNoList);
            Map<Long, AccountExtInfo> accountExtInfoMap = accountService.selectExtInfoMapByIds(recordMap.values().stream().map(ExternalLandpageRecord::getAdvertiserId).distinct().collect(Collectors.toList()));

            list.forEach(form -> {
                try {
                    ExternalLandpageRecord record = recordMap.get(form.getExternalNo());
                    if (null == record) {
                        return;
                    }
                    // 检查是否需要更新
                    if (!Objects.equals(record.getName(), form.getName())
                            || !Objects.equals(record.getPhone(), form.getPhone())
                            || !Objects.equals(record.getProvince(), form.getProvince())
                            || !Objects.equals(record.getCity(), form.getCity())
                            || !Objects.equals(record.getDistrict(), form.getDistrict())
                            || !Objects.equals(record.getAddress(), form.getAddress())) {
                        // 记录变更
                        ExternalLandpageRecordHistoryEntity history = BeanUtil.copyProperties(record, ExternalLandpageRecordHistoryEntity.class);
                        history.setOriginRecordId(record.getId());
                        history.setOperAccountId(operAccountId);
                        externalLandpageRecordHistoryService.insert(history);
                        // 保存变更
                        record.setName(form.getName());
                        record.setPhone(form.getPhone());
                        record.setProvince(form.getProvince());
                        record.setCity(form.getCity());
                        record.setDistrict(form.getDistrict());
                        record.setAddress(form.getAddress());
                        externalLandpageRecordService.updateExternalLandpageRecord(record);
                    }
                    // 回传表单
                    AccountExtInfo extInfo = accountExtInfoMap.get(record.getAdvertiserId());
                    if (null == extInfo || StringUtils.isBlank(extInfo.getAccessKey()) || StringUtils.isBlank(extInfo.getSecretKey())) {
                        return;
                    }
                    // 调用接口回传
                    String resp = callback(extInfo.getLpCallbackUrl(), extInfo.getAccessKey(), extInfo.getSecretKey(), record);
                    int isSuccess = LandpageUtil.isCallbackSuccess(resp);
                    // 回传记录
                    ExternalLandpageFormSendRecordEntity sendRecord = new ExternalLandpageFormSendRecordEntity();
                    sendRecord.setCurDate(DateUtil.beginOfDay(new Date()));
                    sendRecord.setRecordId(record.getId());
                    sendRecord.setExternalNo(record.getExternalNo());
                    sendRecord.setAdvertiserId(record.getAdvertiserId());
                    sendRecord.setOperAccountId(operAccountId);
                    sendRecord.setUrl(extInfo.getLpCallbackUrl());
                    sendRecord.setResp(StrUtil.subPre(resp, 500));
                    sendRecord.setIsSuccess(isSuccess);
                    externalLandpageFormSendRecordService.insert(sendRecord);
                } catch (Exception e) {
                    log.error("表单处理异常, operAccountId={}, record={}", operAccountId, JSON.toJSONString(form), e);
                }
            });
        });
    }

    /**
     * 调用接口回传表单
     */
    public String callback(String url, String accessKey, String secretKey, ExternalLandpageRecord record) {
        try {
            // 未对接API特殊处理
            if (StringUtils.isNotBlank(url) && url.contains("localhost")) {
                return "{\"code\":200, \"msg\":\"未对接API手动分配\"}";
            }

            JSONObject content = new JSONObject();
            content.put("idCard", idCardService.decrypt(record.getIdCard()));
            content.put("province", record.getProvince());
            content.put("city", record.getCity());
            content.put("district", record.getDistrict());
            content.put("address", record.getAddress());
            content.put("phone", record.getPhone());
            content.put("name", record.getName());

            JSONObject body = new JSONObject();
            body.put("accessKey", accessKey);
            body.put("orderId", record.getExternalNo());
            body.put("timestamp", System.currentTimeMillis());
            body.put("content", Base64.encode(content.toString()));
            body.put("signature", Md5Utils.hash(accessKey + secretKey + body.getString("orderId")
                    + body.getString("timestamp") + body.getString("content")));

            String resp = HttpUtil.post(url, body.toString(), 30000);
            log.info("外部表单回传, orderId={}, rep={}, resp={}", record.getExternalNo(), body.toString(), resp);
            return resp;
        } catch (Exception e) {
            log.error("外部表单回传异常, orderId={}", record.getExternalNo(), e);
        }
        return null;
    }
}
