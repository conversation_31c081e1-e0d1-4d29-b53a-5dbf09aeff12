package com.ruoyi.system.service.common.impl;

import com.ruoyi.common.utils.BlowfishUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.common.IdCardService;
import org.springframework.stereotype.Service;

/**
 * 身份证服务实现
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
@Service
public class IdCardServiceImpl implements IdCardService {

    // 身份证加密密钥
    private static final String ID_CARD_ENCRYPT_KEY = "nh-encrypt";

    @Override
    public String encrypt(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return idCard;
        }
        return BlowfishUtils.encryptBlowfish(idCard, ID_CARD_ENCRYPT_KEY);
    }

    @Override
    public String decrypt(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return idCard;
        }
        return BlowfishUtils.decryptBlowfish(idCard, ID_CARD_ENCRYPT_KEY);
    }

    public static void main(String[] args) {
        String list = "39oGj3ibpSkxn9VERqxf4chdK,3BsVi58kgqz3h8UuM5SapVqit";
        String[] split = list.split(",");
        for (String idCard : split) {
            String ps = BlowfishUtils.decryptBlowfish(idCard, ID_CARD_ENCRYPT_KEY);
            System.out.println(ps);
        }
    }

}
