package com.ruoyi.system.service.appdata.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.constant.SspRedisKeyFactory;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.ConfirmStatusEnum;
import com.ruoyi.common.enums.slot.SlotChargeTypeEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.entity.slotcharge.SlotChargeEntity;
import com.ruoyi.system.entity.slotcharge.SlotChargeOperLogEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.publisher.PublisherPrepayManager;
import com.ruoyi.system.mapper.appdata.AppMonthDataMapper;
import com.ruoyi.system.req.datashow.AppMonthDataReq;
import com.ruoyi.system.req.datashow.UpdateMonthDataListReq;
import com.ruoyi.system.req.slot.SlotMonthDataUpdateReq;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.datasource.SlotAppDataService;
import com.ruoyi.system.service.finance.AccountRevenueService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.slotcharge.SlotChargeOperLogService;
import com.ruoyi.system.service.slotcharge.SlotChargeService;
import com.ruoyi.system.vo.datashow.CrmAppMonthDataStatisticsVO;
import com.ruoyi.system.vo.datashow.CrmAppMonthDataVO;
import com.ruoyi.system.vo.withdraw.WithdrawAppDataListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.isCrmUser;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * 媒体月账单数据表 Service
 *
 * <AUTHOR>
 * @date 2021-9-9 16:55:56
 */
@Slf4j
@Service
public class AppMonthDataServiceImpl implements AppMonthDataService{

    @Autowired
    private AppMonthDataMapper appMonthDataMapper;
    @Autowired
    private AccountService accountService;
    @Autowired
    private AppService appService;
    @Autowired
    private AccountRevenueService accountRevenueService;
    @Autowired
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private SlotAppDataService slotAppDataService;
    @Autowired
    private SlotChargeService slotChargeService;
    @Autowired
    private SlotChargeOperLogService slotChargeOperLogService;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private PublisherPrepayManager publisherPrepayManager;

    @Override
    public AppMonthDataEntity selectById(Long id){
        if(Objects.isNull(id)){
            return null;
        }
        return appMonthDataMapper.selectById(id);
    }

    @Override
    public AppMonthDataEntity selectByAppIdAndMonth(Long appId, Integer month) {
        if (null == appId || null == month) {
            return null;
        }
        return  appMonthDataMapper.selectByAppIdAndMonth(appId, month);
    }

    @Override
    public int batchInsertOrUpdate(List<AppMonthDataEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return 0;
        }
        return appMonthDataMapper.batchInsertOrUpdate(entities);
    }

    @Override
    public int batchUpdateCost(List<AppMonthDataEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return 0;
        }
        return appMonthDataMapper.batchUpdateCost(entities);
    }

    @Override
    public PageInfo<CrmAppMonthDataVO> selectCrmAppMonthList(AppMonthDataReq req, boolean isExport) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        //非crm账号不能查询
        if(!isCrmUser(user.getMainType())){
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        //账号模糊搜索
        if(StringUtils.isNotBlank(req.getAccountSearch())){
            List<Long> accountIds = accountService.selectIdsByIdOrEmail(req.getAccountSearch());
            if(CollectionUtils.isEmpty(accountIds)){
                return PageInfoUtils.buildReturnList(Collections.emptyList());
            }
            req.setAccountIds(accountIds);
        }

        // 媒体模糊查询
        if (StringUtils.isNotBlank(req.getAppSearch())) {
            List<Long> appIds = appService.selectAppIdsBySearchValue(req.getAppSearch());
            if (CollectionUtils.isEmpty(appIds)) {
                return PageInfoUtils.buildReturnList(Collections.emptyList());
            }
            req.setAppIds(appIds);
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectApp();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return PageInfoUtils.buildReturnList(Collections.emptyList());
            }
            if (null == req.getAppIds()) {
                req.setAppIds(permission.getValues());
            } else {
                req.getAppIds().retainAll(permission.getValues());
            }
            if (CollectionUtils.isEmpty(req.getAppIds())) {
                return PageInfoUtils.buildReturnList(Collections.emptyList());
            }
        }

        if (!isExport) {
            TableSupport.startPage();
        }

        List<AppMonthDataEntity> entities = appMonthDataMapper.selectAppMonthList(req);
        if(CollectionUtils.isEmpty(entities)){
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }
        List<Long> appIds = entities.stream().map(AppMonthDataEntity::getAppId).collect(Collectors.toList());
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);
        List<Long> accountIds = entities.stream().map(AppMonthDataEntity::getAccountId).collect(Collectors.toList());
        Map<Long, String> emailMap = accountService.selectEmailByIds(accountIds);

        return PageInfoUtils.dto2Vo(entities,entity ->{
            CrmAppMonthDataVO vo = BeanUtil.copyProperties(entity,CrmAppMonthDataVO.class);
            vo.setTotalCost(entity.getNhCost()+ entity.getOuterCost());
            vo.setAppName(appNameMap.get(entity.getAppId()));
            vo.setEmail(emailMap.get(entity.getAccountId()));
            return vo;
        });
    }

    @Override
    public CrmAppMonthDataStatisticsVO statisticsCrmAppMonthData(AppMonthDataReq req) {

        CrmAppMonthDataStatisticsVO vo = new CrmAppMonthDataStatisticsVO();
        PageInfo<CrmAppMonthDataVO> pageInfo = selectCrmAppMonthList(req, false);
        pageInfo.getList().forEach(data ->{
            vo.setSlotRequestPv(NumberUtils.defaultInt(vo.getSlotRequestPv()) + data.getSlotRequestPv());
            vo.setSlotRequestUv(NumberUtils.defaultInt(vo.getSlotRequestUv()) + data.getSlotRequestUv());
            vo.setJoinUv(NumberUtils.defaultInt(vo.getJoinUv()) + data.getJoinUv());
            vo.setJoinPv(NumberUtils.defaultInt(vo.getJoinPv()) + data.getJoinPv());
            vo.setTotalConsume(NumberUtils.defaultInt(vo.getTotalConsume()) + data.getTotalConsume());
            vo.setNhConsume(NumberUtils.defaultInt(vo.getNhConsume()) + data.getNhConsume());
            vo.setOuterConsume(NumberUtils.defaultInt(vo.getOuterConsume()) + data.getOuterConsume());
            vo.setAppRevenue(NumberUtils.defaultInt(vo.getAppRevenue()) + data.getAppRevenue());
            vo.setNhCost(NumberUtils.defaultLong(vo.getNhCost(),0L) + data.getNhCost());
            vo.setOuterCost(NumberUtils.defaultLong(vo.getOuterCost(),0L) + data.getOuterCost());
            vo.setTotalCost(NumberUtils.defaultLong(vo.getTotalCost(),0L) + data.getNhCost() + data.getOuterCost());
        });

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmStatement(Long id) {
        if(NumberUtils.isNullOrLteZero(id)){
            throw new CustomException(ErrorCode.ARGS);
        }
        AppMonthDataEntity appMonthDataEntity = appMonthDataMapper.selectById(id);
        if(Objects.isNull(appMonthDataEntity)){
            throw new CustomException(ErrorCode.E106002);
        }
        RedisLock lock = redisAtomicClient.getLock(SspRedisKeyFactory.K001.join(id), 3);
        if (lock == null) {
            throw new CustomException(ErrorCode.RESUBMIT);
        }
        boolean result = appMonthDataMapper.confirmStatement(id, ConfirmStatusEnum.CONFIRM.getStatus()) > 0;
        // 审核成功
        if(result){
            // 预付款结算
            Integer withdrawableAmount = publisherPrepayManager.prepayStatement(appMonthDataEntity.getAccountId(), id, appMonthDataEntity.getAppRevenue());
            // 媒体增加收益
            accountRevenueService.addRevenue(appMonthDataEntity.getAccountId(),appMonthDataEntity.getAppRevenue(), withdrawableAmount);
        }
        return result;
    }

    @Override
    public List<WithdrawAppDataListVO> selectByWithdrawId(Long withdrawId) {
        if(NumberUtils.isNullOrLteZero(withdrawId)){
            return Collections.emptyList();
        }
        List<AppMonthDataEntity> appMonthDataEntities = appMonthDataMapper.selectByWithdrawId(withdrawId);
        if(CollectionUtils.isEmpty(appMonthDataEntities)){
            return Collections.emptyList();
        }

        List<Long> appIds = appMonthDataEntities.stream().map(AppMonthDataEntity::getAppId).collect(Collectors.toList());
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);

        return appMonthDataEntities.stream().map(entity ->{
            WithdrawAppDataListVO vo = BeanUtil.copyProperties(entity,WithdrawAppDataListVO.class);
            vo.setAppName(appNameMap.get(entity.getAppId()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<WithdrawAppDataListVO> selectNoWithdrawList(Long accountId,Long withdrawId) {
        if(NumberUtils.isNullOrLteZero(accountId)){
            return Collections.emptyList();
        }

        List<AppMonthDataEntity> appMonthDataEntities = selectNoWithdrawByAccountId(accountId,null,withdrawId);
        if(CollectionUtils.isEmpty(appMonthDataEntities)){
            return Collections.emptyList();
        }
        List<Long> appIds = appMonthDataEntities.stream().map(AppMonthDataEntity::getAppId).collect(Collectors.toList());
        Map<Long, String> appNameMap = appService.selectAppNameMap(appIds);

        return appMonthDataEntities.stream().map(entity ->{
            WithdrawAppDataListVO vo = BeanUtil.copyProperties(entity,WithdrawAppDataListVO.class);
            vo.setAppName(appNameMap.get(entity.getAppId()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AppMonthDataEntity> selectNoWithdrawByAccountId(Long accountId, List<Long> ids,Long withdrawId) {
        if(NumberUtils.isNullOrLteZero(accountId)){
            return Collections.emptyList();
        }
        List<Long> withdrawIds = Lists.newArrayList(0L);
        if (NumberUtils.isNonNullAndGtZero(withdrawId)) {
            withdrawIds.add(withdrawId);
        }
        return appMonthDataMapper.selectNoWithdrawByAccountId(accountId,ids,withdrawIds,Lists.newArrayList(ConfirmStatusEnum.CONFIRM.getStatus(),ConfirmStatusEnum.NO_WITHDRAW.getStatus()));
    }

    @Override
    public Integer countNoConfirmByAccountId(Long accountId) {
        if (null == accountId) {
            return 0;
        }
        return appMonthDataMapper.countNoConfirmByAccountId(accountId);
    }

    @Override
    public int relevanceWithdrawRecord(List<Long> ids, Long withdrawId,Integer confirmStatus) {
        if(CollectionUtils.isEmpty(ids) || Objects.isNull(withdrawId) || NumberUtils.isNullOrLteZero(confirmStatus)){
            return 0;
        }
        return appMonthDataMapper.relevanceWithdrawRecord(ids, withdrawId,confirmStatus);
    }

    @Override
    public int updateConfirmStatus(Long withdrawId, Integer confirmStatus) {
        if(NumberUtils.isNullOrLteZero(withdrawId) || NumberUtils.isNullOrLteZero(confirmStatus)){
            return 0;
        }
        return appMonthDataMapper.updateConfirmStatus(withdrawId, confirmStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMonthData(UpdateMonthDataListReq req) {
        Long appId = req.getAppId();
        App app = appService.selectAppById(appId);
        if(Objects.isNull(app)){
            throw new CustomException(ErrorCode.E107001);
        }

        req.getUpdateMonthDataReqs().forEach(data -> {
            //如果当天是CPT类型，则更新当天的媒体收入金额
            //新增更新广告位计费方式
            SlotChargeEntity chargeEntity = BeanUtil.copyProperties(data,SlotChargeEntity.class);
            if(Objects.equals(data.getChargeType(),SlotChargeTypeEnum.CPT.getType())){
                chargeEntity.setChargePrice(data.getAppRevenue());
            }
            slotChargeService.batchInsertOrUpdate(Lists.newArrayList(chargeEntity));
            //新增更新记录
            SlotChargeOperLogEntity entity = BeanUtil.copyProperties(chargeEntity,SlotChargeOperLogEntity.class);
            entity.setOperName(SecurityUtils.getUsername());
            slotChargeOperLogService.insert(entity);
            slotAppDataService.updateSlotAppMonthData(BeanUtil.copyProperties(data, SlotMonthDataUpdateReq.class));
        });
        return true;
    }


    @Override
    public List<AppMonthDataEntity> selectListByPage(Long id, Integer pageSize, Integer monthDay) {
        return appMonthDataMapper.selectListByPage(id, pageSize, monthDay);
    }

    @Override
    public int insert(AppMonthDataEntity entity) {
        if (null == entity) {
            return 0;
        }
        return appMonthDataMapper.insert(entity);
    }

    @Override
    public int update(AppMonthDataEntity entity) {
        if (null == entity) {
            return 0;
        }
        return appMonthDataMapper.update(entity);
    }
}
