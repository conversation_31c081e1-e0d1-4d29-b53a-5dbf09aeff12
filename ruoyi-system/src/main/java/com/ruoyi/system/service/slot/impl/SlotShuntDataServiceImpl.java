package com.ruoyi.system.service.slot.impl;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.system.entity.slot.SlotShuntData;
import com.ruoyi.system.mapper.slot.SlotShuntDataMapper;
import com.ruoyi.system.req.slot.shunt.SlotShuntDataParam;
import com.ruoyi.system.req.slot.shunt.SlotShuntDataUpdateParam;
import com.ruoyi.system.service.slot.SlotShuntDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.slot.SlotShuntType.isPvShunt;
import static com.ruoyi.common.enums.slot.SlotShuntType.isUvShunt;

/**
 * 广告位切量数据Service接口实现
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
@Slf4j
@Service
public class SlotShuntDataServiceImpl implements SlotShuntDataService {

    @Autowired
    private SlotShuntDataMapper slotShuntDataMapper;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Override
    public List<SlotShuntData> selectList(SlotShuntDataParam param) {
        return slotShuntDataMapper.selectList(param);
    }

    @Override
    public Map<Long, SlotShuntData> selectMap(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyMap();
        }
        SlotShuntDataParam param = new SlotShuntDataParam();
        param.setTaskIds(taskIds);
        List<SlotShuntData> list = slotShuntDataMapper.selectList(param);
        return list.stream().collect(Collectors.toMap(SlotShuntData::getTaskId, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public SlotShuntData selectByTaskIdAndDate(Long taskId, Date curDate) {
        return slotShuntDataMapper.selectByTaskId(taskId, curDate);
    }

    @Override
    public SlotShuntData selectById(Long id) {
        return slotShuntDataMapper.selectById(id);
    }

    @Override
    public int insert(SlotShuntData record) {
        return slotShuntDataMapper.insert(record);
    }

    @Override
    public int update(SlotShuntDataUpdateParam param) {
        return slotShuntDataMapper.update(param);
    }

    @Override
    public void statistics(Long slotId, String deviceId, Long taskId, Integer shuntType, Date date) {
        int shuntUv = BizUtils.countUv(EngineRedisKeyFactory.K032.join(taskId), deviceId);
        if (isPvShunt(shuntType)) {
            redisAtomicClient.incrBy(EngineRedisKeyFactory.K031.join(taskId), 1, 30, TimeUnit.HOURS);
        } else if (isUvShunt(shuntType)) {
            redisAtomicClient.incrBy(EngineRedisKeyFactory.K031.join(taskId), shuntUv, 30, TimeUnit.HOURS);
        }
        GlobalThreadPool.statExecutorService.submit(() -> {
            update(slotId, taskId, date, shuntUv);
        });
    }

    /**
     * 更新广告位切量数据
     *
     * @param slotId 广告位ID
     * @param taskId 切量计划ID
     * @param curDate 日期
     * @param uv UV增量
     * @return 影响行数
     */
    private int update(Long slotId, Long taskId, Date curDate, int uv) {
        SlotShuntData shuntData = selectByTaskIdAndDate(taskId, curDate);
        if (null == shuntData) {
            shuntData = new SlotShuntData();
            shuntData.setCurDate(curDate);
            shuntData.setSlotId(slotId);
            shuntData.setTaskId(taskId);
            insert(shuntData);
            shuntData = selectByTaskIdAndDate(taskId, curDate);
        }
        SlotShuntDataUpdateParam updateShuntData = new SlotShuntDataUpdateParam();
        updateShuntData.setId(shuntData.getId());
        updateShuntData.setSlotRequestPvAdd(1);
        updateShuntData.setSlotRequestUvAdd(uv);
        return update(updateShuntData);
    }
}
