package com.ruoyi.system.service.manager.impl;

import com.alibaba.fastjson.JSON;
import com.ruoyi.system.bo.slot.SlotDomainConfigBo;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.mapper.manager.SlotConfigMapper;
import com.ruoyi.system.service.manager.SlotConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 广告位配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-15
 */
@Service
public class SlotConfigServiceImpl implements SlotConfigService {

    @Autowired
    private SlotConfigMapper slotConfigMapper;

    /**
     * 查询广告位配置
     *
     * @param id 广告位配置ID
     * @return 广告位配置
     */
    @Override
    public SlotConfig selectSlotConfigById(Long id) {
        if (null == id) {
            return null;
        }
        return slotConfigMapper.selectSlotConfigById(id);
    }

    @Override
    public SlotConfig selectBySlotId(Long slotId) {
        if (null == slotId) {
            return null;
        }
        return slotConfigMapper.selectBySlotId(slotId);
    }

    @Override
    public SlotDomainConfigBo selectDomainConfigBySlotId(Long slotId) {
        SlotConfig config = selectBySlotId(slotId);
        if (null == config) {
            return new SlotDomainConfigBo();
        }
        SlotDomainConfigBo domainConfig = JSON.parseObject(config.getDomainConfig(), SlotDomainConfigBo.class);
        return null != domainConfig ? domainConfig : new SlotDomainConfigBo();
    }

    @Override
    public SlotSwitchConfig selectSwitchConfigBySlotId(Long slotId) {
        SlotConfig config = selectBySlotId(slotId);
        if (null == config) {
            return new SlotSwitchConfig();
        }
        SlotSwitchConfig switchConfig = JSON.parseObject(config.getSwitchConfig(), SlotSwitchConfig.class);
        return null != switchConfig ? switchConfig : new SlotSwitchConfig();
    }

    /**
     * 查询广告位配置列表
     *
     * @param slotConfig 广告位配置
     * @return 广告位配置
     */
    @Override
    public List<SlotConfig> selectSlotConfigList(SlotConfig slotConfig) {
        return slotConfigMapper.selectSlotConfigList(slotConfig);
    }

    @Override
    public int save(SlotConfig config) {
        if (null == config.getId()) {
            return slotConfigMapper.insertSlotConfig(config);
        }
        return slotConfigMapper.updateById(config);
    }

    /**
     * 新增广告位配置
     *
     * @param slotConfig 广告位配置
     * @return 结果
     */
    @Override
    public int insertSlotConfig(SlotConfig slotConfig) {
        return slotConfigMapper.insertSlotConfig(slotConfig);
    }

    /**
     * 修改广告位配置
     *
     * @param slotConfig 广告位配置
     * @return 结果
     */
    @Override
    public int updateSlotConfig(SlotConfig slotConfig) {
        return slotConfigMapper.updateSlotConfig(slotConfig);
    }

    @Override
    public int updateById(SlotConfig config) {
        return slotConfigMapper.updateById(config);
    }

    @Override
    public Map<Long, SlotConfig> selectSlotConfigMap(List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }

        List<SlotConfig> list = slotConfigMapper.selectListBySlotIds(slotIds);
        return list.stream().collect(Collectors.toMap(SlotConfig::getSlotId, Function.identity(), (oldVal, newVal) -> newVal));
    }
}
