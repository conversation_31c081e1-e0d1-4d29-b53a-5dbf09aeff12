package com.ruoyi.system.service.fc;

import com.ruoyi.common.core.domain.Result;
import com.ruoyi.system.entity.fc.FcLinkArticleAggrRelEntity;
import com.ruoyi.system.req.fc.AddArticleAggrLinkReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

/**
 * 丰巢链接-文章聚合链接关联表(FcLinkArticleAggrRel)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-05 16:37:16
 */
public interface FcLinkArticleAggrRelService {


    /**
     * 添加文章聚合链接
     * @param addArticleAggrLinkReq
     * @return
     */
    Result addArticleAggrLink(AddArticleAggrLinkReq addArticleAggrLinkReq);

    /**
     * 移除文章聚合链接
     * @param fcLinkKey
     * @param articleAggrLinkId
     * @return
     */
    Result removeArticleAggrLink(String fcLinkKey, Long articleAggrLinkId);


    /**
     * 根据丰巢链接key获取文章聚合链接id
     * @param fcLinkKey
     * @return
     */
    Set<Long> selectAggrIdsByFcLinkKey(String fcLinkKey);

    /**
     * 根据丰巢链接key列表获取文章聚合链接id
     * @param fcLinkKeys
     * @return
     */
    List<Long> selectAggrIdsByFcLinkKeys(List<String> fcLinkKeys);

    /**
     * 根据文章聚合链接id判断是否存在
     * @param articleAggrLinkId 文章聚合链接ID
     * @return 结果
     */
    boolean existsByArticleAggrLinkId(Long articleAggrLinkId);

    /**
     * 根据文章id获取关联的丰巢链接key
     * @param articleId 文章ID
     * @return 丰巢链接key集合
     * <AUTHOR>
     */
    Set<String> selectFcLinkKeysByArticleId(Long articleId);

    /**
     * 根据文章聚合链接ID获取关联的丰巢链接名称列表
     * @param articleAggrLinkId 文章聚合链接ID
     * @return 丰巢链接名称列表
     * <AUTHOR>
     */
    List<String> selectFcLinkNamesByArticleAggrLinkId(Long articleAggrLinkId);

}
