package com.ruoyi.system.service.appdata;

import com.github.pagehelper.PageInfo;
import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.req.datashow.AppMonthDataReq;
import com.ruoyi.system.req.datashow.UpdateMonthDataListReq;
import com.ruoyi.system.vo.datashow.CrmAppMonthDataStatisticsVO;
import com.ruoyi.system.vo.datashow.CrmAppMonthDataVO;
import com.ruoyi.system.vo.withdraw.WithdrawAppDataListVO;

import java.util.List;

/**
 * 媒体月账单数据表 Service
 *
 * <AUTHOR>
 * @date 2021-9-9 16:55:56
 */
public interface AppMonthDataService {

    /**
     * 根据id获取
     */
    AppMonthDataEntity selectById(Long id);

    /**
     * 根据媒体ID和月份查询媒体月账单
     *
     * @param appId 媒体ID
     * @param month 年月
     * @return 媒体月账单
     */
    AppMonthDataEntity selectByAppIdAndMonth(Long appId, Integer month);

    /**
     * 批量新增
     *
     * @param entities 广告位月数据
     * @return 结果
     */
    int batchInsertOrUpdate(List<AppMonthDataEntity> entities);

    /**
     * 批量更新
     *
     * @param entities 广告位月数据
     * @return 结果
     */
    int batchUpdateCost(List<AppMonthDataEntity> entities);

    /**
     * 查询媒体月数据
     *
     * @param req 参数
     * @param isExport 是否导出
     * @return 媒体月数据
     */
    PageInfo<CrmAppMonthDataVO> selectCrmAppMonthList(AppMonthDataReq req, boolean isExport);

    /**
     * 统计媒体月数据 累计
     *
     * @param req 请求参数
     * @return 结果
     */
    CrmAppMonthDataStatisticsVO statisticsCrmAppMonthData(AppMonthDataReq req);

    /**
     * 确认媒体月账单
     *
     * @param id 账单id
     * @return 结果
     */
    boolean confirmStatement(Long id);

    /**
     * 根据提现id查询媒体月账单
     *
     * @param withdrawId 媒体月账单id
     * @return 媒体月账单
     */
    List<WithdrawAppDataListVO> selectByWithdrawId(Long withdrawId);

    /**
     * 查询未绑定提现记录的媒体月账单
     *
     * @param accountId  账号id
     * @param withdrawId 关联的提现记录id ,不为空则是重新提交
     * @return 媒体月账单
     */
    List<WithdrawAppDataListVO> selectNoWithdrawList(Long accountId, Long withdrawId);

    /**
     * 根据账号id查询未绑定提现的媒体月账单
     *
     * @param accountId  账号id
     * @param ids        月账单id列表
     * @param withdrawId 关联的提现记录id
     * @return 月账单列表
     */
    List<AppMonthDataEntity> selectNoWithdrawByAccountId(Long accountId, List<Long> ids, Long withdrawId);

    /**
     * 根据账号id查询未确认的媒体月账单的数量
     *
     * @param accountId  账号id
     * @return 未确认的媒体月账单的数量
     */
    Integer countNoConfirmByAccountId(Long accountId);

    /**
     * 媒体月账单关联提现记录
     *
     * @param ids           账单id列表
     * @param withdrawId    提现记录id 当id为0时，则表示取消关联
     * @param confirmStatus 确认提现状态
     * @return 结果
     */
    int relevanceWithdrawRecord(List<Long> ids, Long withdrawId, Integer confirmStatus);

    /**
     * 批量修改媒体月账单状态
     *
     * @param withdrawId    提现记录id
     * @param confirmStatus 状态
     * @return 结果
     */
    int updateConfirmStatus(Long withdrawId, Integer confirmStatus);

    /**
     * 更新媒体月账单
     *
     * @param req 请求参数
     * @return 结果
     */
    boolean updateMonthData(UpdateMonthDataListReq req);

    /**
     * 分页查询媒体月账单列表
     *
     * @param id       上一次最大id
     * @param pageSize 每页大小
     * @param monthDay 月份
     * @return 媒体月账单列表
     */
    List<AppMonthDataEntity> selectListByPage(Long id, Integer pageSize, Integer monthDay);

    /**
     * 新增媒体月账单
     *
     * @param entity 广告位月账单
     * @return 结果
     */
    int insert(AppMonthDataEntity entity);

    /**
     * 更新媒体月账单
     *
     * @param entity 广告位月账单
     * @return 结果
     */
    int update(AppMonthDataEntity entity);
}
