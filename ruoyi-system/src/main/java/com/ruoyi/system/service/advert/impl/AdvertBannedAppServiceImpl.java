package com.ruoyi.system.service.advert.impl;

import com.ruoyi.system.bo.advert.AdvertBannedAppBo;
import com.ruoyi.system.domain.advert.AdvertBannedApp;
import com.ruoyi.system.mapper.advert.AdvertBannedAppMapper;
import com.ruoyi.system.service.advert.AdvertBannedAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 广告屏蔽媒体Service实现
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Service
public class AdvertBannedAppServiceImpl implements AdvertBannedAppService {

    @Autowired
    private AdvertBannedAppMapper advertBannedAppMapper;

    @Override
    public List<AdvertBannedAppBo> selectListByOrientId(Long orientId) {
        return advertBannedAppMapper.selectListByOrientId(orientId);
    }

    @Override
    public List<AdvertBannedApp> selectByOrientId(Long orientId) {
        return advertBannedAppMapper.selectByOrientId(orientId);
    }

    @Override
    public Map<Long, List<AdvertBannedAppBo>> selectMapByAdvertId(Long advertId) {
        List<AdvertBannedAppBo> list = advertBannedAppMapper.selectListByAdvertId(advertId);
        return list.stream().collect(Collectors.groupingBy(AdvertBannedAppBo::getOrientId));
    }
}
