package com.ruoyi.system.service.adengine.filter;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.common.enums.advert.DeviceTargetType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.service.adengine.AdvertFilter;
import org.springframework.stereotype.Component;

/**
 * 设备定向过滤
 */
@Component
public class DeviceTargetFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();

        // 不限机型
        if (null == ad.getDeviceTarget() || 0 == ad.getDeviceTarget()) {
            return true;
        }

        // 机型品牌判断
        String userAgent = StringUtils.defaultString(context.getUserAgent());
        String brand = context.getMobileBrand();
        return (DeviceTargetType.contains(ad.getDeviceTarget(), DeviceTargetType.HUAWEI.getType()) && (StrUtil.equals(brand, "华为") || StrUtil.containsIgnoreCase(userAgent, "HUAWEI")))
                || (DeviceTargetType.contains(ad.getDeviceTarget(), DeviceTargetType.VIVO.getType()) && (StrUtil.equalsIgnoreCase(brand, "VIVO") || StrUtil.containsIgnoreCase(userAgent, "vivo")))
                || (DeviceTargetType.contains(ad.getDeviceTarget(), DeviceTargetType.OPPO.getType()) && (StrUtil.equalsIgnoreCase(brand, "OPPO") || StrUtil.containsIgnoreCase(userAgent, "OPPO")))
                || (DeviceTargetType.contains(ad.getDeviceTarget(), DeviceTargetType.MI.getType()) && (StrUtil.equals(brand, "小米") || StrUtil.containsAnyIgnoreCase(userAgent, "Xiaomi", "Redmi")));
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.DEVICE_TARGET;
    }
}
