package com.ruoyi.system.service.engine.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.InnerLogUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.message.rocketmq.producer.LogMqProducer;
import com.ruoyi.system.req.engine.BaijiuLandpageStatReq;
import com.ruoyi.system.service.engine.BaijiuLandpageStatService;
import com.ruoyi.system.service.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.InnerLogType.*;

/**
 * 埋点服务实现
 *
 * <AUTHOR>
 * @date 2021/8/19
 */
@Slf4j
@Service
public class BaijiuLandpageStatServiceImpl implements BaijiuLandpageStatService {

    @Autowired
    private OrderService orderService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private LogMqProducer logMqProducer;

    @Override
    public void layerExposure(BaijiuLandpageStatReq req) {
        if (StringUtils.isBlank(req.getOrderId())) {
            return;
        }

        // 查询订单
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            throw new CustomException("无效的订单");
        }
        AdSnapshot adSnapshot = JSON.parseObject(StringUtils.defaultString(order.getAdSnapshot(), "{}"), AdSnapshot.class);

        // 打印日志
        JSONObject logJson = new JSONObject();
        logJson.put("action", req.getAction());
        logJson.put("orderId", req.getOrderId());
        logJson.put("activityId", order.getActivityId());
        logJson.put("slotId", order.getSlotId());
        logJson.put("appId", order.getAppId());
        logJson.put("consumerId", order.getConsumerId());
        logJson.put("ip", IpUtils.getIpAddr(ServletUtils.getRequest()));
        logJson.put("userAgent", ServletUtils.getRequest().getHeader("User-Agent"));
        logJson.put("orientId", order.getOrientId());
        logJson.put("advertId", order.getAdvertId());
        logJson.put("materialId", order.getMaterialId());
        logJson.put("referer", ServletUtils.getRequest().getHeader("referer"));
        if (null != adSnapshot) {
            logJson.put("chargeType", adSnapshot.getChargeType());
            logJson.put("landpageUrl", adSnapshot.getLandpageUrl());
            logJson.put("originLandpageUrl", adSnapshot.getOriginLandpageUrl());
            logJson.put("srid", adSnapshot.getSrid());
            logJson.put("pluginId", adSnapshot.getPluginId());
        }
        InnerLogUtils.log(LANDPAGE_LAYER_EXPOSURE, logJson);
        logMqProducer.sendMsg(LANDPAGE_LAYER_EXPOSURE, logJson);

        // 落地页数据
//        dataStatService.handleAsync(LANDPAGE_LAYER_EXPOSURE, logJson);
    }

    @Override
    public void retExposure(BaijiuLandpageStatReq req) {
        if (StringUtils.isBlank(req.getOrderId())) {
            return;
        }

        // 查询订单
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            throw new CustomException("无效的订单");
        }
        AdSnapshot adSnapshot = JSON.parseObject(StringUtils.defaultString(order.getAdSnapshot(), "{}"), AdSnapshot.class);

        // 打印日志
        JSONObject logJson = new JSONObject();
        logJson.put("action", req.getAction());
        logJson.put("orderId", req.getOrderId());
        logJson.put("activityId", order.getActivityId());
        logJson.put("slotId", order.getSlotId());
        logJson.put("appId", order.getAppId());
        logJson.put("consumerId", order.getConsumerId());
        logJson.put("ip", IpUtils.getIpAddr(ServletUtils.getRequest()));
        logJson.put("orientId", order.getOrientId());
        logJson.put("advertId", order.getAdvertId());
        logJson.put("materialId", order.getMaterialId());
        if (null != adSnapshot) {
            logJson.put("chargeType", adSnapshot.getChargeType());
            logJson.put("landpageUrl", adSnapshot.getLandpageUrl());
            logJson.put("originLandpageUrl", adSnapshot.getOriginLandpageUrl());
            logJson.put("srid", adSnapshot.getSrid());
            logJson.put("pluginId", adSnapshot.getPluginId());
        }
        InnerLogUtils.log(LANDPAGE_RET_EXPOSURE, logJson);
        logMqProducer.sendMsg(LANDPAGE_RET_EXPOSURE, logJson);

        // 记录数据
//        dataStatService.handleAsync(LANDPAGE_RET_EXPOSURE, logJson);

        // 设置用户触发返回挽留
        String redisKey = EngineRedisKeyFactory.K020.join(DateUtil.today());
        redisCache.addCacheSet(redisKey, String.valueOf(order.getConsumerId()));
        redisCache.expire(redisKey, 1, TimeUnit.DAYS);
    }
}
