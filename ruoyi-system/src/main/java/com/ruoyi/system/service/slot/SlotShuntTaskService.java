package com.ruoyi.system.service.slot;

import com.ruoyi.system.bo.slot.SlotShuntTaskBO;
import com.ruoyi.system.entity.slot.SlotShuntTask;
import com.ruoyi.system.req.slot.shunt.SlotShuntTaskParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告位切量计划Service接口
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
public interface SlotShuntTaskService {

    /**
     * 广告位切量分流
     *
     * @param slotId 广告位Id
     * @return 广告位切量信息
     */
    SlotShuntTaskBO shunt(Long appId, Long slotId, String deviceId);

    /**
     * 查询广告位切量计划列表
     *
     * @param param 请求参数
     * @return 广告位切量计划列表
     */
    List<SlotShuntTask> selectList(SlotShuntTaskParam param);

    /**
     * 统计广告位切量计划数量
     *
     * @param param 请求参数
     * @return 广告位切量计划数量
     */
    int countByParam(SlotShuntTaskParam param);

    /**
     * 根据ID查询广告位切量计划
     *
     * @param id 计划ID
     * @return 广告位切量计划
     */
    SlotShuntTask selectById(Long id);

    /**
     * 批量查询广告位是否正在执行切量计划
     *
     * @param slotIds 广告位ID列表
     * @return 广告位-是否正在切量映射
     */
    Map<Long, Integer> selectSlotShuntMap(List<Long> slotIds);

    /**
     * 新增计划
     *
     * @param param 参数
     * @return 结果
     */
    int addTask(SlotShuntTask param);

    /**
     * 更新计划
     * 实现:先删除，再新增
     *
     * @param param 参数
     * @return 结果
     */
    int updateTask(SlotShuntTask param);

    /**
     * 执行计划
     *
     * @param id 计划ID
     * @return 结果
     */
    int executeTask(Long id);

    /**
     * 完成计划
     *
     * @param id 计划ID
     * @return 结果
     */
    int finishTask(Long id);

    /**
     * 停止计划
     *
     * @param id 计划ID
     * @return 结果
     */
    int cancelTask(Long id);

    /**
     * 删除计划
     *
     * @param id 计划ID
     * @return 结果
     */
    int deleteTask(Long id);

    /**
     * 查询广告位当日已使用的切量类型
     *
     * @param slotId 广告位ID
     * @param date 日期
     * @return 广告位当日已使用的切量类型
     */
    Integer selectUsedShuntType(Long slotId, Date date);

    /**
     * 查询广告位当日已使用的切量比例
     *
     * @param slotId 广告位ID
     * @param date 日期
     * @return 广告位当日已使用的切量比例
     */
    Integer selectUsedShuntRatio(Long slotId, Date date);

    /**
     * 查询待执行/执行中的广告位切量计划列表
     *
     * @return 广告位切量计划列表
     */
    List<SlotShuntTask> selectTotalValidList();

    /**
     * 查询执行中的广告位切量计划列表
     *
     * @param slotId 广告位ID
     * @return 广告位切量计划列表
     */
    List<SlotShuntTask> selectExecuteList(Long slotId);
}
