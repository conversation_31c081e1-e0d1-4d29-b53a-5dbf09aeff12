package com.ruoyi.system.service.callback;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.SlotBiddingConfigDto;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.req.callback.ConvCallbackParam;
import com.ruoyi.system.service.callback.processor.CallbackProcessor;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import com.ruoyi.system.service.slot.BiddingDayDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 转化上报服务
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
@Slf4j
@Service
public class ConvCallbackService {

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private BiddingDayDataService biddingDayDataService;

    /**
     * 上报处理器映射
     */
    private static final Map<CallbackProcessorTypeEnum, CallbackProcessor> processorMap = new HashMap<>();

    /**
     * 转化上报(异步)
     *
     * @param type 业务类型
     * @param convType 转化类型
     * @param order 订单
     */
    public void handleAsync(final InnerLogType type, final ConvType convType, Order order) {
        if (null != order) {
            handle(type, convType, order, true);
        }
    }

    /**
     * 转化上报处理(指定处理器，非投流上报，异步)
     *
     * @param type 业务类型
     * @param convType 转化类型
     * @param order 订单
     */
    public void directCallback(final InnerLogType type, final ConvType convType, Order order,
                               CallbackProcessorTypeEnum processorType, Map<String, String> ext) {
        if (null == order || null == processorType) {
            return;
        }
        // 构造参数
        // 注:因为参数里有时间，所以构造参数是同步执行
        ConvCallbackContext context = new ConvCallbackContext();
        context.setType(type);
        context.setConvType(convType);
        context.setOrder(order);
        context.setParam(buildDataRequest(order));
        context.setExt(ext);
        // 转化上报
        GlobalThreadPool.statExecutorService.execute(() -> {
            CallbackProcessor processor = processorMap.get(processorType);
            if (null != processor && processor.validate(context) && processor.process(context)) {
                processor.process(context);
            }
        });
    }

    /**
     * 上报处理器注册
     *
     * @param processor 上报处理器
     */
    public static void register(CallbackProcessor processor) {
        if (null != processor) {
            processorMap.put(processor.getType(), processor);
        }
    }

    /**
     * 上报处理(异步)
     *
     * @param type 业务类型
     * @param convType 转化类型
     * @param order 订单
     * @param isAsync 是否异步处理
     * @return true.上报成功,false.上报失败/未上报
     */
    private boolean handle(final InnerLogType type, final ConvType convType, Order order, boolean isAsync) {
        // 构造参数
        // 注:因为参数里有时间，所以构造参数是同步执行
        ConvCallbackContext context = new ConvCallbackContext();
        context.setType(type);
        context.setConvType(convType);
        context.setOrder(order);
        context.setParam(buildDataRequest(order));
        // 处理数据
        if (isAsync) {
            GlobalThreadPool.statExecutorService.execute(() -> {
                // 投流上报
                biddingAndCallback(context, this::doHandle);
            });
            return true;
        } else {
            return biddingAndCallback(context, this::doHandle);
        }
    }

    /**
     * 遍历 CallbackProcessor 上报
     */
    private boolean doHandle(final ConvCallbackContext context) {
        try {
            CallbackProcessor processor = getProcessor(context.getType(), context);
            return null != processor && processor.process(context);
        } catch (Exception e) {
            log.error("转化上报异常, type={}, context={}", context.getType(), JSON.toJSONString(context), e);
        }
        return false;
    }

    /**
     * 构造转化上报参数
     *
     * @param order 订单
     * @return 转化上报参数
     */
    private ConvCallbackParam buildDataRequest(Order order) {
        ConvCallbackParam req = new ConvCallbackParam();
        req.setDate(DateUtil.beginOfDay(new Date()));
        req.setSlotParam(callbackService.getParameterFromCache(order.getOrderId()));
        req.setBiddingConfig(slotCacheService.getSlotBiddingConfigCache(order.getSlotId()));
        return req;
    }

    /**
     * 获取转化上报处理器
     *
     * @return 转化上报处理器
     */
    private CallbackProcessor getProcessor(final InnerLogType type, final ConvCallbackContext context) {
        // TODO 上报节点/上报对象/上报事件
        // TODO 如果广告位指定了，用指定的上报对象

        // 广告位未指定，根据参数自动匹配处理器
        for (CallbackProcessor processor : processorMap.values()) {
            if (processor.validate(context)) {
                return processor;
            }
        }
        return null;
    }

    /**
     * 投流上报
     *
     * @param context 参数
     * @param callback 上报
     * @return 是否上报成功
     */
    private boolean biddingAndCallback(final ConvCallbackContext context, Function<ConvCallbackContext, Boolean> callback) {
        // 投流配置
        SlotBiddingConfigDto biddingConfig = context.getParam().getBiddingConfig();
        // 投流账户标识
        String hu = StringUtils.defaultString(context.getParam().getSlotParam().getString("hu"));
        // 订单
        Order order = context.getOrder();

        // 1.广告位设置的固定收益价格【广告位当前固定收益元】
        if (biddingConfig.isConfigEnable()) {
            // 模式：进入【固定收益上报】模式；需要跳过，就返回不上报
            if (order.isSkipCoolFlag() && callbackService.jumpCallback(order, biddingConfig, hu)) {
                return false;
            }
        }

        // 2.上报媒体
        boolean isSuccess = callback.apply(context);

        // 3.固定收益上报模式：广告位设置的【固定收益上报价格】:累加上报之后的价格
        if (isSuccess) {
            if (order.isSkipCoolFlag() && biddingConfig.isConfigEnable()) {
                callbackService.callBackCost(order.getSlotId(), biddingConfig, hu);
            }
            biddingDayDataService.incrConv(context.getParam().getDate(), order.getSlotId(), hu);
        }
        return isSuccess;
    }
}
