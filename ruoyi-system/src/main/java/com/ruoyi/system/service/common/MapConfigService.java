package com.ruoyi.system.service.common;

import com.ruoyi.common.enums.common.MapConfigEnum;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 映射配置服务接口
 *
 * <AUTHOR>
 * @date 2022/9/7
 */
public interface MapConfigService {

    /**
     * 获取映射
     *
     * @param config 配置类型
     * @param kClass key类型
     * @param vClass value类型
     * @return 白名单列表
     */
    <K, V> Map<K, V> getMap(MapConfigEnum config, Class<K> kClass, Class<V> vClass);

    /**
     * 根据Key获取映射Value
     *
     * @param config 配置类型
     * @param key key
     * @param vClass value类型
     * @return 映射Value
     */
    <K, V> V getValue(MapConfigEnum config, K key, Class<V> vClass);

    /**
     * 获取映射Key集合
     *
     * @param config 配置类型
     * @param kClass key类型
     * @return 映射Key集合
     */
    <K> Set<K> getKeySet(MapConfigEnum config, Class<K> kClass);

    /**
     * 获取映射Value集合
     *
     * @param config 配置类型
     * @param vClass value类型
     * @return 映射Value集合
     */
    <V> Set<V> getValueSet(MapConfigEnum config, Class<V> vClass);

    /**
     * 获取配置并转成列表
     *
     * @param config 配置类型
     * @return 列表
     */
    List<String> getList(MapConfigEnum config);

    /**
     * 移除配置
     *
     * @param config 配置类型
     * @param key key
     */
     void remove(MapConfigEnum config, String key);
}
