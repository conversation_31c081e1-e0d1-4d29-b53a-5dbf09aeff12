package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.util.RandomUtil;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.mapper.landpage.LandpageMapper;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.landpage.LandpageLibraryService;
import com.ruoyi.system.util.LandpageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 落地页表Service接口实现
 *
 * <AUTHOR>
 * @date 2022-02-10
 */
@Service
public class LandpageLibraryServiceImpl implements LandpageLibraryService {

    @Autowired
    private LandpageMapper landpageMapper;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Override
    public Landpage selectById(Long id) {
        if (null == id) {
            return null;
        }
        return landpageMapper.selectById(id);
    }

    @Override
    public Landpage selectByUrl(String url) {
        String key = LandpageUtil.extractLpk(url);
        if (StringUtils.isNotBlank(key)) {
            return landpageMapper.selectByKey(key);
        }
        return null;
    }

    @Override
    public Landpage selectByKey(String key) {
        return landpageMapper.selectByKey(key);
    }

    @Override
    public List<Landpage> selectByKeys(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyList();
        }
        return landpageMapper.selectByKeys(keys);
    }

    @Override
    public List<Landpage> selectByTargetLandpage(String targetLandpage) {
        if (StringUtils.isBlank(targetLandpage)) {
            return Collections.emptyList();
        }
        Landpage param = new Landpage();
        param.setPageConfig(targetLandpage);
        return landpageMapper.selectSimpleList(param);
    }

    @Override
    public List<Landpage> selectList(Landpage param) {
        return landpageMapper.selectList(param);
    }

    @Override
    public Map<String, Landpage> selectKeyMap() {
        List<Landpage> list = landpageMapper.selectSimpleList(new Landpage());
        return list.stream().collect(Collectors.toMap(Landpage::getKey, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public int insertLandpage(Landpage param) {
        return landpageMapper.insertLandpage(param);
    }

    @Override
    public int insertLandpageBySkin(Landpage param) {
        //随机一个key
        String key = RandomUtil.randomStringUpper(8);

        if(StringUtils.isBlank(param.getUrl())){
            //url为空则是新增，则需要根据域名拼接url
            String url = param.getDomain() +"/land/render/"+key;
            if(!StringUtils.startsWith(url,"http")){
                if(SpringEnvironmentUtils.isProd()){
                    url = "https://"+url;
                }else{
                    url = "http://"+url;
                }
            }
            param.setUrl(url);
        }else{
            //复制的则替换最后的key
            param.setUrl(param.getUrl().replace(param.getKey(), key));
        }
        param.setKey(key);

        int result = landpageMapper.insertLandpage(param);
        if(result > 0){
            refreshCacheService.sendRefreshLandpageCacheMsg(key);
        }
        return result;
    }

    @Override
    public int updateLandpage(Landpage param) {
        return landpageMapper.updateLandpage(param);
    }

    @Override
    public int updateLandpageUrl(Long id, String key, String url) {
        Landpage param = new Landpage();
        param.setId(id);
        param.setUrl(url);
        return landpageMapper.updateLandpage(param);
    }

    @Override
    public int updateLandpageBySkin(Landpage param) {
        Landpage landpage = landpageMapper.selectById(param.getId());
        if(Objects.isNull(landpage)){
            throw new CustomException(ErrorCode.E110002);
        }
        //拼接url
        String url = param.getDomain() +"/land/render/"+landpage.getKey();
        if(!StringUtils.startsWith(url,"http")){
            if(SpringEnvironmentUtils.isProd()){
                url = "https://"+url;
            }else{
                url = "http://"+url;
            }
        }
        param.setUrl(url);
        int result = landpageMapper.updateLandpage(param);
        if (result > 0) {
            refreshCacheService.sendRefreshLandpageCacheMsg(landpage.getKey());
        }
        return result;
    }

    @Override
    public boolean isLandpageExist(String url) {
        String key = LandpageUtil.extractLpk(url);
        return StringUtils.isNotBlank(key) && null != landpageMapper.existByKey(key);
    }

    @Override
    public String getLandpageTag(String key) {
        if (StringUtils.isBlank(key)) {
            return "";
        }
        return Optional.ofNullable(landpageMapper.selectByKey(key)).map(Landpage::getTag).orElse("");
    }

    @Override
    public Map<String, String> getLandpageTagMap() {
        List<Landpage> list = landpageMapper.selectSimpleList(new Landpage());
        return list.stream().collect(Collectors.toMap(Landpage::getKey, Landpage::getTag, (v1, v2) -> v2));
    }

    @Override
    public int invalidateLandpage(Long landpageId) {
        if (null == landpageId) {
            return 0;
        }
        return landpageMapper.invalidateLandpage(landpageId);
    }

    @Override
    public List<String> getLandpageTags() {
        return landpageMapper.getLandpageTags();
    }
}
