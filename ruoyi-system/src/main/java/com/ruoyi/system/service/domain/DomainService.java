package com.ruoyi.system.service.domain;

import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.req.manager.DomainReq;
import com.ruoyi.system.req.manager.DomainUpdateStatusReq;
import com.ruoyi.system.vo.manager.DomainReplaceVO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 域名Service接口
 *
 * <AUTHOR>
 * @date 2021-09-16
 */
public interface DomainService {

    /**
     * 查询域名
     *
     * @param id 域名ID
     * @return 域名
     */
    Domain selectDomainById(Long id);

    /**
     * 查询域名
     *
     * @param domain 域名
     * @return 域名信息
     */
    Domain selectDomain(String domain);

    /**
     * 查询域名列表
     *
     * @param domain 域名
     * @return 域名集合
     */
    List<Domain> selectDomainList(Domain domain);

    /**
     * 查询支付宝可用域名列表(用于域名自动替换)
     *
     * @param domainType 域名类型
     * @return 支付宝可用域名集合
     */
    Set<String> selectAlipayValidDomainList(Integer domainType);

    /**
     * 查询微信可用域名列表(用于域名自动替换)
     *
     * @param domainType 域名类型
     * @return 微信可用域名集合
     */
    Set<String> selectWechatValidDomainList(Integer domainType);

    /**
     * 查询有效域名分组列表
     *
     * @return 域名类型-域名列表映射
     */
    Map<Integer, List<DomainReplaceVO>> listValidByGroup();

    /**
     * 新增域名
     *
     * @param req 请求参数
     * @return 结果
     */
    int insertDomain(DomainReq req);

    /**
     * 修改域名
     *
     * @param req 请求参数
     * @return 结果
     */
    int updateDomain(DomainReq req);

    /**
     * 更新支付宝/微信状态
     *
     * @param req 参数
     * @return 是否更新成功
     */
    boolean updateWxAlipayStatus(DomainUpdateStatusReq req);

    /**
     * 更新微信状态不可用
     *
     * @param domain 域名
     * @return 是否更新成功
     */
    boolean updateWxDisable(String domain);

    /**
     * 更新支付宝状态不可用
     *
     * @param domain 域名
     * @return 是否更新成功
     */
    boolean updateAlipayDisable(String domain);

    /**
     * 删除域名信息
     *
     * @param id 域名ID
     * @return 结果
     */
    int deleteDomainById(Long id);

    /**
     * 更新域名状态
     *
     * @param domain 域名
     * @param domainStatus 域名状态
     */
    void updateDomainStatus(String domain, Integer domainStatus);

    /**
     * 检查域名状态
     *
     * @param domain 域名
     * @param domainType 域名类型
     * @param httpsEnable 是否开启https证书
     * @return 域名状态
     */
    int checkDomainStatus(String domain, Integer domainType, Integer httpsEnable);

    /**
     * 获取域名的证书到期时间
     *
     * @param domain 域名
     * @return 证书到期时间
     */
    Date getCertificateExpireTime(String domain);
}
