package com.ruoyi.system.service.manager;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.req.slot.SlotModifyReq;
import com.ruoyi.system.req.slot.SlotReq;
import com.ruoyi.system.vo.slot.SlotVO;

/**
 * 广告位Service接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface SlotService {

    /**
     * 查询广告位
     *
     * @param id 广告位ID
     * @return 广告位
     */
    SlotVO selectSlotById(Long id);

    /**
     * 查询广告位
     *
     * @param id 广告位ID
     * @return 广告位
     */
    Slot selectSimpleSlotById(Long id);

    /**
     * 查询广告位列表
     *
     * @param param 参数
     * @return 广告位列表
     */
    List<Slot> selectList(Slot param);

    /**
     * 查询广告位列表
     *
     * @param slot 广告位
     * @return 广告位集合
     */
    List<Slot> selectSlotList(Slot slot);

    /**
     * 查询广告位ID列表
     *
     * @param searchValue 广告位ID/广告位名称
     * @return 广告位ID列表
     */
    List<Long> selectSlotIdsBySearchValue(String searchValue);
    /**
     * 查询所有广告位列表
     *
     * @return 广告位集合
     */
    List<Slot> selectTotalSlotList();

    /**
     * 新增广告位
     *
     * @param slot 广告位
     * @return 结果
     */
    int insertSlot(Slot slot);

    /**
     * 修改广告位
     *
     * @param req 请求参数
     * @return 结果
     */
    int updateSlot(SlotModifyReq req);

    /**
     * 更新广告位状态
     *
     * @param req 请求参数
     * @return 结果
     */
    int updateStatus(SlotReq req);

    /**
     * 获取媒体账号对应的广告位数量
     *
     * @param accountIds 媒体账号ID列表
     * @return 媒体账号ID-广告位数量映射
     */
    Map<Long, Integer> groupByAccountId(List<Long> accountIds);

    /**
     * 获取媒体对应的广告位数量
     *
     * @param appIds 媒体ID列表
     * @return 媒体ID-广告位数量映射
     */
    Map<Long, Integer> groupByAppId(List<Long> appIds);

    /**
     * 查询广告位域名配置
     *
     * @param slotId 广告位ID
     * @return 广告位域名配置
     */
    JSONObject querySlotDomainConfig(Long slotId);

    /**
     * 更新广告位域名配置
     *
     * @param slotId 广告位ID
     * @param domainType 域名类型
     * @param domain 替换域名
     * @return 是否更新成功
     */
    boolean updateSlotDomainConfig(Long slotId, Integer domainType, String domain);

    /**
     * 根据广告位id列表查询
     *
     * @param ids 广告位id列表
     * @return 结果
     */
    List<Slot> selectSimpleSlotByIds(List<Long> ids);

    /**
     * 根据id列表查询广告位名称
     *
     * @param ids 广告位id列表
     * @return <slotId,slotName>
     */
    Map<Long,String> selectSlotNameMap(List<Long> ids);

    /**
     * 根据媒体ID列表查询广告位名称
     *
     * @param appIds 媒体ID列表
     * @return 广告位ID-广告位名称映射
     */
    Map<Long, String> selectSlotNameMapByAppIds(List<Long> appIds);

    /**
     * 查询广告位对应的媒体ID
     *
     * @param slotIds 广告位ID列表
     * @return 广告位ID-媒体ID映射
     */
    Map<Long, Long> selectAppIdMap(List<Long> slotIds);

    /**
     * 查询广告位ID-广告位名称映射
     *
     * @return 广告位ID-广告位名称映射
     */
    Map<Long, String> selectSlotNameMap();

    /**
     * 查询广告位ID-链接映射
     *
     * @return 广告位ID-链接映射
     */
    Map<Long, String> selectSlotUrlMap();
    Map<String, Set<Long>> getDomainSlotMapS(List<String> domains);

    /**
     * 根据媒体ID列表查询广告位ID列表
     *
     * @param appIds 媒体ID列表
     * @return 广告位ID列表
     */
    List<Long> selectSlotIdsByAppIds(List<Long> appIds);

    /**
     * 查询广告位映射(包含媒体信息)
     *
     * @param ids 广告位ID列表
     * @return 广告ID-广告位映射
     */
    Map<Long, Slot> selectSlotAppMapByIds(List<Long> ids);

    /**
     * 广告位对应渠道配置
     *
     * @param slotId 广告位ID
     * @param channel 渠道
     */
    void updateSlotChannel(Long slotId, Integer channel);

    /**
     * 获取广告位对应渠道
     *
     * @param slotId 广告位ID
     * @return 渠道
     */
    Integer getSlotChannel(Long slotId);

    /**
     * 根据媒体id查询广告位列表，不分页
     *
     * @param appId 媒体ID
     * @return 广告位列表
     */
    List<Slot> selectByAppId(Long appId);

    /**
     * 根据媒体id列表查询广告位列表，不分页，不做数据隔离
     *
     * @param appIds 媒体Id列表
     * @return 广告位列表
     */
    List<Slot> selectByAppIds(List<Long> appIds);

    /**
     * 设置数据校准开关
     *
     * @param slotId 广告位ID
     * @param status 开关状态
     */
    void setAdjustSwitch(Long slotId, Integer status);

    /**
     * 设置自动结算开关
     *
     * @param slotId 广告位ID
     * @param status 开关状态
     */
    void setAutoChargeSwitch(Long slotId, Integer status);
}
