package com.ruoyi.system.service.common.impl;

import com.ruoyi.system.bo.data.OrderDataBo;
import com.ruoyi.system.entity.common.AdvertOrderLogEntity;
import com.ruoyi.system.mapper.common.AdvertOrderLogMapper;
import com.ruoyi.system.service.common.AdvertOrderLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 业务订单明细表 Service
 *
 * <AUTHOR>
 * @date 2023-10-16
 */
@Service
public class AdvertOrderLogServiceImpl implements AdvertOrderLogService {

    @Autowired
    private AdvertOrderLogMapper orderLogMapper;

    @Override
    public Boolean insert(AdvertOrderLogEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return orderLogMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(AdvertOrderLogEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return orderLogMapper.updateById(entity) > 0;
    }

    @Override
    public AdvertOrderLogEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return orderLogMapper.selectById(id);
    }

    @Override
    public Long selectIdByOrderId(String orderId) {
        if (Objects.isNull(orderId)) {
            return null;
        }
        return orderLogMapper.selectIdByOrderId(orderId);
    }

    @Override
    public OrderDataBo selectCtrCvrBySlotIdAndAdvertId(Long slotId, Long advertId, Integer launchSeq, String mobileBrand, String province, Integer times) {
        if (null == slotId && null == advertId) {
            return null;
        }
        return orderLogMapper.selectCtrCvrBySlotIdAndAdvertIdAndMobileBrand(slotId, advertId, launchSeq, mobileBrand, province, times, null);
    }

    @Override
    public OrderDataBo selectCtrCvrBySlotIdAndAdvertId(Long slotId, Long advertId, Integer launchSeq, String mobileBrand, String province, Integer times, Long minId) {
        if (null == slotId && null == advertId) {
            return null;
        }
        return orderLogMapper.selectCtrCvrBySlotIdAndAdvertIdAndMobileBrand(slotId, advertId, launchSeq, mobileBrand, province, times, minId);
    }

    @Override
    public OrderDataBo selectCvrBySlotIdAndAdvertId(Long slotId, Long advertId, Integer times) {
        if (null == slotId && null == advertId) {
            return null;
        }
        return orderLogMapper.selectCvrBySlotIdAndAdvertId(slotId, advertId, times, null);
    }

    @Override
    public OrderDataBo selectCvrBySlotIdAndAdvertId(Long slotId, Long advertId, Integer times, Long minId) {
        if (null == slotId && null == advertId) {
            return null;
        }
        return orderLogMapper.selectCvrBySlotIdAndAdvertId(slotId, advertId, times, minId);
    }

    @Override
    public OrderDataBo selectCvrByAdvertId(Long advertId, Integer times) {
        if (null == advertId) {
            return null;
        }
        return orderLogMapper.selectCtrCvrByAdvertId(advertId, times);
    }
}
