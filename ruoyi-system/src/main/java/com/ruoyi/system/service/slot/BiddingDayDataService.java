package com.ruoyi.system.service.slot;

import com.ruoyi.system.entity.slot.BiddingDayDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 投流日数据 Service
 *
 * <AUTHOR>
 * @date 2023-7-31 11:55:54
 */
public interface BiddingDayDataService {

    /**
     * 新增记录
     */
    Boolean insert(BiddingDayDataEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(BiddingDayDataEntity entity);

    /**
     * 根据id获取
     */
    BiddingDayDataEntity selectById(Long id);

    /**
     * 统计广告位_日期维度的投流数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param slotIds 广告位ID列表
     * @return 广告位ID_日期-投流数据
     */
    Map<String, BiddingDayDataEntity> countByDateAndSlotIds(Date startDate, Date endDate, List<Long> slotIds);

    /**
     * 统计投流数据汇总
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param slotIds 广告位ID列表
     * @return 投流数据汇总
     */
    BiddingDayDataEntity sumByDateAndSlotIds(Date startDate, Date endDate, List<Long> slotIds);

    /**
     * 累加投流上报数
     *
     * @param curDate 日期
     * @param slotId 广告位ID
     * @param advertiserId 账户ID
     */
    void incrConv(Date curDate, Long slotId, String advertiserId);

    /**
     * 更新消耗
     *
     * @param param 数据
     */
    boolean updateConsume(BiddingDayDataEntity param);
}
