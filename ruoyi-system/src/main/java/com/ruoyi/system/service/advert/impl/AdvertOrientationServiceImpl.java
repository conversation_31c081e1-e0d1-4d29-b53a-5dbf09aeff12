package com.ruoyi.system.service.advert.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.advert.DeviceTargetType;
import com.ruoyi.common.enums.advert.FlowTargetType;
import com.ruoyi.common.enums.advert.IspTargetType;
import com.ruoyi.common.enums.advert.OsTargetType;
import com.ruoyi.common.enums.advert.ServingHourEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.advert.AdvertOrientBO;
import com.ruoyi.system.bo.advert.AdvertOrientBatchUpdateParam;
import com.ruoyi.system.bo.advert.AdvertOrientCountBo;
import com.ruoyi.system.bo.advert.AdvertOrientLandpageBo;
import com.ruoyi.system.domain.advert.AdvertBannedApp;
import com.ruoyi.system.domain.advert.AdvertOrienteApp;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.mapper.advert.AdvertOrienteAppMapper;
import com.ruoyi.system.req.advert.AdvertOrientationAddReq;
import com.ruoyi.system.req.advert.AdvertOrientationModifyReq;
import com.ruoyi.system.mapper.advert.AdvertBannedAppMapper;
import com.ruoyi.system.mapper.manager.AdvertOrientationMapper;
import com.ruoyi.system.req.slot.AppSlotReq;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.traffic.AdvertOrientTrafficService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.IsDefaultEnum.IS_DEFAULT;
import static com.ruoyi.common.enums.IsDefaultEnum.NOT_DEFAULT;
import static com.ruoyi.common.enums.common.SwitchStatusEnum.OFF;

/**
 * 广告定向配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@Slf4j
@Service
public class AdvertOrientationServiceImpl implements AdvertOrientationService {

    @Autowired
    private AdvertOrientationMapper advertOrientationMapper;

    @Autowired
    private AdvertBannedAppMapper advertBannedAppMapper;

    @Autowired
    private AdvertOrienteAppMapper advertOrienteAppMapper;

    @Autowired
    private AdvertOrientTrafficService advertOrientTrafficService;

    @Override
    public AdvertOrientation selectDefaultOrientationByAdvertId(Long advertId) {
        if (null == advertId) {
            return null;
        }
        return advertOrientationMapper.selectDefaultOrientationByAdvertId(advertId);
    }

    @Override
    public List<Long> selectIdsByAdvertId(Long advertId) {
        if (null == advertId) {
            return Collections.emptyList();
        }
        return advertOrientationMapper.selectIdsByAdvertId(advertId);
    }

    @Override
    public List<Long> selectIdsByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyList();
        }
        AdvertOrientation param = new AdvertOrientation();
        param.setAdvertIds(advertIds);
        return ListUtils.mapToList(selectAdvertOrientationList(param), AdvertOrientation::getId);
    }

    @Override
    public List<Long> selectIdsWithAreaTargetByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyList();
        }
        return advertOrientationMapper.selectIdsWithAreaTargetByAdvertIds(advertIds);
    }

    @Override
    public AdvertOrientation selectAdvertOrientationById(Long id) {
        if (null == id) {
            return null;
        }
        return advertOrientationMapper.selectAdvertOrientationById(id);
    }

    @Override
    public List<AdvertOrientation> selectAdvertOrientationList(AdvertOrientation param) {
        return advertOrientationMapper.selectAdvertOrientationList(param);
    }

    @Override
    public List<AdvertOrientation> selectListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        AdvertOrientation param = new AdvertOrientation();
        param.setIds(ids);
        return advertOrientationMapper.selectAdvertOrientationList(param);
    }

    @Override
    public Map<Long, AdvertOrientation> selectMapByIds(List<Long> ids) {
        List<AdvertOrientation> list = selectListByIds(ids);
        return list.stream().collect(Collectors.toMap(AdvertOrientation::getId, Function.identity()));
    }

    @Override
    public Map<Long, String> selectOrientNameMap(List<Long> ids) {
        List<AdvertOrientation> list = selectListByIds(ids);
        return list.stream().collect(Collectors.toMap(AdvertOrientation::getId, AdvertOrientation::getOrientName));
    }

    @Override
    public List<AdvertOrientation> selectListByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyList();
        }
        AdvertOrientation param = new AdvertOrientation();
        param.setAdvertIds(advertIds);
        return advertOrientationMapper.selectAdvertOrientationList(param);
    }

    @Override
    public List<AdvertOrientation> selectListByAdvertId(Long advertId) {
        if (null == advertId) {
            return Collections.emptyList();
        }
        AdvertOrientation param = new AdvertOrientation();
        param.setAdvertId(advertId);
        return selectAdvertOrientationList(param);
    }

    @Override
    public Integer selectMaxMilliUnitPriceByAdvertId(Long advertId) {
        if (null == advertId) {
            return 0;
        }
        return advertOrientationMapper.selectMaxMilliUnitPriceByAdvertId(advertId);
    }

    @Override
    public List<AdvertOrientation> selectDefaultByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyList();
        }
        AdvertOrientation param = new AdvertOrientation();
        param.setAdvertIds(advertIds);
        param.setIsDefault(IS_DEFAULT.getType());
        return selectAdvertOrientationList(param);
    }

    @Override
    public Map<Long, Set<Long>> selectOrientBannedApp(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }

        List<AdvertBannedApp> bannedApps = advertBannedAppMapper.selectByAdvertIds(advertIds);
        if (CollectionUtils.isEmpty(bannedApps)) {
            return Collections.emptyMap();
        }

        Map<Long, Set<Long>> map = new HashMap<>();
        for (AdvertBannedApp bannedApp : bannedApps) {
            Long orientId = bannedApp.getOrientId();
            if (!map.containsKey(orientId)) {
                map.put(orientId, new HashSet<>());
            }
            map.get(orientId).add(bannedApp.getAppId());
        }
        return map;
    }

    @Override
    public Map<Long, Set<Long>> selectOrientApp(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }

        List<AdvertOrienteApp> orienteApps = advertOrienteAppMapper.selectByAdvertIds(advertIds);
        if (CollectionUtils.isEmpty(orienteApps)) {
            return Collections.emptyMap();
        }

        Map<Long, Set<Long>> map = new HashMap<>();
        for (AdvertOrienteApp orienteApp : orienteApps) {
            Long orientId = orienteApp.getOrientId();
            if (!map.containsKey(orientId)) {
                map.put(orientId, new HashSet<>());
            }
            map.get(orientId).add(orienteApp.getAppId());
        }
        return map;
    }

    @Override
    public Map<Long, AdvertOrientBO> selectOrientInfo(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }

        List<AdvertOrienteApp> orienteApps = advertOrienteAppMapper.selectByAdvertIds(advertIds);
        if (CollectionUtils.isEmpty(orienteApps)) {
            return Collections.emptyMap();
        }
        Map<Long, AdvertOrientBO> map = new HashMap<>();

        Map<Long, List<AdvertOrienteApp>> advertOrientMap = orienteApps.stream().collect(Collectors.groupingBy(AdvertOrienteApp::getOrientId));
        advertOrientMap.forEach((orientId, orienteList) -> {

            Set<Long> appIds = orienteList.stream().map(AdvertOrienteApp::getAppId).collect(Collectors.toSet());
            Map<Long, Set<Long>> appSlotMap = orienteList.stream().collect(HashMap::new,
                    (m, v) -> m.put(v.getAppId(), CollectionUtils.isEmpty(JSONArray.parseArray(v.getOrienteSlotIds(), Long.class)) ? new HashSet<>() : new HashSet<>(JSONArray.parseArray(v.getOrienteSlotIds(), Long.class))), HashMap::putAll);

            AdvertOrientBO bo = new AdvertOrientBO();
            bo.setAppSlotMap(appSlotMap);
            bo.setAppIds(appIds);
            map.put(orientId, bo);
        });

        return map;
    }

    @Override
    public Map<Long, Set<Long>> selectAdvertOrientSlotMap(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }

        List<AdvertOrienteApp> orienteApps = advertOrienteAppMapper.selectByAdvertIds(advertIds);
        if (CollectionUtils.isEmpty(orienteApps)) {
            return Collections.emptyMap();
        }
        Map<Long, Set<Long>> map = new HashMap<>();
        for (AdvertOrienteApp orientApp : orienteApps) {
            Long advertId = orientApp.getAdvertId();
            if (!map.containsKey(advertId)) {
                map.put(advertId, new HashSet<>());
            }
            List<Long> slotIds = JSONArray.parseArray(orientApp.getOrienteSlotIds(), Long.class);
            if (CollectionUtils.isNotEmpty(slotIds)) {
                map.get(advertId).addAll(slotIds);
            }
        }
        return map;
    }

    @Override
    public Map<Long, Set<Long>> selectAdvertOrientSlotMapByOrientIds(List<Long> orientIds) {
        if (CollectionUtils.isEmpty(orientIds)) {
            return Collections.emptyMap();
        }

        List<AdvertOrienteApp> orientApps = advertOrienteAppMapper.selectByOrientIds(orientIds);
        if (CollectionUtils.isEmpty(orientApps)) {
            return Collections.emptyMap();
        }
        Map<Long, Set<Long>> map = new HashMap<>();
        for (AdvertOrienteApp orientApp : orientApps) {
            Long orientId = orientApp.getOrientId();
            if (!map.containsKey(orientId)) {
                map.put(orientId, new HashSet<>());
            }
            List<Long> slotIds = JSONArray.parseArray(orientApp.getOrienteSlotIds(), Long.class);
            if (CollectionUtils.isNotEmpty(slotIds)) {
                map.get(orientId).addAll(slotIds);
            }
        }
        return map;
    }

    @Override
    public Map<Long, Set<Long>> selectAdvertOrientAppMap(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }

        List<AdvertOrienteApp> orientApps = advertOrienteAppMapper.selectByAdvertIds(advertIds);
        if (CollectionUtils.isEmpty(orientApps)) {
            return Collections.emptyMap();
        }

        Map<Long, Set<Long>> map = new HashMap<>();
        for (AdvertOrienteApp orientApp : orientApps) {
            Long advertId = orientApp.getAdvertId();
            if (!map.containsKey(advertId)) {
                map.put(advertId, new HashSet<>());
            }
            map.get(advertId).add(orientApp.getAppId());
        }
        return map;
    }

    @Override
    public Map<Long, Set<Long>> selectAdvertOrientAppMapByOrientIds(List<Long> orientIds) {
        if (CollectionUtils.isEmpty(orientIds)) {
            return Collections.emptyMap();
        }

        List<AdvertOrienteApp> orientApps = advertOrienteAppMapper.selectByOrientIds(orientIds);
        if (CollectionUtils.isEmpty(orientApps)) {
            return Collections.emptyMap();
        }

        Map<Long, Set<Long>> map = new HashMap<>();
        for (AdvertOrienteApp orientApp : orientApps) {
            Long orientId = orientApp.getOrientId();
            if (!map.containsKey(orientId)) {
                map.put(orientId, new HashSet<>());
            }
            map.get(orientId).add(orientApp.getAppId());
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertAdvertOrientation(AdvertOrientationAddReq req) {
        LoginUser user = SecurityUtils.getLoginUser();

        // 新增定向配置
        AdvertOrientation orient = new AdvertOrientation();
        orient.setOrientName(req.getOrientName());
        orient.setAdvertId(req.getAdvertId());
        orient.setLandpageType(req.getLandpageType());
        orient.setLandpageUrl(StringUtils.defaultString(req.getLandpageUrl()).trim());
        orient.setWeight(req.getWeight());
        orient.setChargeType(req.getChargeType());
        orient.setMilliUnitPrice(NumberUtils.defaultInt(req.getMilliUnitPrice(), NumberUtils.defaultInt(req.getUnitPrice()) * 100));
        orient.setUnitPrice(orient.getMilliUnitPrice() / 100);
        orient.setOcpcConvType(req.getOcpcConvType());
        orient.setOcpcConvCost(req.getOcpcConvCost());
        orient.setDailyBudget(req.getDailyBudget());
        orient.setDeviceTarget(DeviceTargetType.convertToInteger(req.getDeviceTargets()));
        orient.setFlowTarget(FlowTargetType.convertToInteger(req.getFlowTargets()));
        orient.setOsTarget(OsTargetType.convertToInteger(req.getOsTargets()));
        orient.setIspTarget(IspTargetType.convertToInteger(req.getIspTargets()));
        orient.setServingHour(ServingHourEnum.convertToInteger(req.getServingHours()));
        orient.setAreaTarget(JSON.toJSONString(CollUtil.defaultIfEmpty(req.getAreaTarget(), Collections.emptySet())));
        orient.setIsDefault(NOT_DEFAULT.getType());
        orient.setServingSwitch(OFF.getStatus());
        orient.setOperatorId(user.getCrmAccountId());
        orient.setOperatorName(user.getUserName());
        advertOrientationMapper.insertAdvertOrientation(orient);

        // 屏蔽媒体
        if (CollectionUtils.isNotEmpty(req.getBannedApps())) {
            // 添加屏蔽媒体
            advertBannedAppMapper.batchInsert(
                    req.getBannedApps().stream()
                            .filter(Objects::nonNull)
                            .map(bannedApp -> {
                                AdvertBannedApp advertBannedApp = new AdvertBannedApp();
                                advertBannedApp.setAdvertId(orient.getAdvertId());
                                advertBannedApp.setOrientId(orient.getId());
                                advertBannedApp.setAppId(bannedApp.getAppId());
                                return advertBannedApp;
                            }).collect(Collectors.toList()));
        }
        // 定向媒体
        if (CollectionUtils.isNotEmpty(req.getOrientApps())) {
            // 按照媒体分组
            Map<Long, List<Long>> appSlotMap = req.getOrientApps().stream().filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(AppSlotReq::getAppId, Collectors.mapping(AppSlotReq::getSlotId, Collectors.toList())));
            // 添加定向媒体
            advertOrienteAppMapper.batchInsert(
                    appSlotMap.entrySet().stream()
                            .map(entry -> {
                                AdvertOrienteApp advertOrientApp = new AdvertOrienteApp();
                                advertOrientApp.setAdvertId(orient.getAdvertId());
                                advertOrientApp.setOrientId(orient.getId());
                                advertOrientApp.setAppId(entry.getKey());
                                advertOrientApp.setOrienteSlotIds(JSONArray.toJSONString(entry.getValue()));
                                return advertOrientApp;
                            }).collect(Collectors.toList()));
        }
        // 流量包定向
        if (CollectionUtils.isNotEmpty(req.getOrientTrafficIds())) {
            advertOrientTrafficService.update(orient.getAdvertId(), orient.getId(), req.getOrientTrafficIds());
        }
        return orient.getAdvertId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateAdvertOrientation(AdvertOrientationModifyReq req) {
        AdvertOrientation orient = advertOrientationMapper.selectAdvertOrientationById(req.getId());
        if (null == orient) {
            throw new CustomException("无效的配置ID");
        }

        AdvertOrientation updateOrient = new AdvertOrientation();
        updateOrient.setId(req.getId());
        updateOrient.setOrientName(req.getOrientName());
        updateOrient.setLandpageType(req.getLandpageType());
        updateOrient.setLandpageUrl(StringUtils.defaultString(req.getLandpageUrl()).trim());
        updateOrient.setWeight(req.getWeight());
        updateOrient.setChargeType(req.getChargeType());
        updateOrient.setMilliUnitPrice(NumberUtils.defaultInt(req.getMilliUnitPrice(), NumberUtils.defaultInt(req.getUnitPrice()) * 100));
        updateOrient.setUnitPrice(updateOrient.getMilliUnitPrice() / 100);
        updateOrient.setOcpcConvType(req.getOcpcConvType());
        updateOrient.setOcpcConvCost(req.getOcpcConvCost());
        updateOrient.setDailyBudget(req.getDailyBudget());
        updateOrient.setDeviceTarget(DeviceTargetType.convertToInteger(req.getDeviceTargets()));
        updateOrient.setFlowTarget(FlowTargetType.convertToInteger(req.getFlowTargets()));
        updateOrient.setOsTarget(OsTargetType.convertToInteger(req.getOsTargets()));
        updateOrient.setIspTarget(IspTargetType.convertToInteger(req.getIspTargets()));
        updateOrient.setServingHour(ServingHourEnum.convertToInteger(req.getServingHours()));
        updateOrient.setAreaTarget(JSON.toJSONString(CollUtil.defaultIfEmpty(req.getAreaTarget(), Collections.emptySet())));

        // 屏蔽媒体
        if (null != req.getBannedApps()) {
            // 删除原来的屏蔽媒体
            advertBannedAppMapper.deleteByOrientId(orient.getId());
            if (CollectionUtils.isNotEmpty(req.getBannedApps())) {
                // 添加屏蔽媒体
                advertBannedAppMapper.batchInsert(
                        req.getBannedApps().stream()
                                .filter(Objects::nonNull)
                                .map(bannedApp -> {
                                    AdvertBannedApp advertBannedApp = new AdvertBannedApp();
                                    advertBannedApp.setAdvertId(orient.getAdvertId());
                                    advertBannedApp.setOrientId(orient.getId());
                                    advertBannedApp.setAppId(bannedApp.getAppId());
                                    return advertBannedApp;
                                }).collect(Collectors.toList()));
            }
        }
        // 定向媒体
        if (null != req.getOrientApps()) {
            // 删除原来的定向媒体
            advertOrienteAppMapper.deleteByOrientId(orient.getId());
            if (CollectionUtils.isNotEmpty(req.getOrientApps())) {
                // 按照媒体分组
                Map<Long, List<Long>> appSlotMap = req.getOrientApps().stream().filter(Objects::nonNull)
                        .collect(Collectors.groupingBy(AppSlotReq::getAppId, Collectors.mapping(AppSlotReq::getSlotId, Collectors.toList())));
                // 添加定向媒体
                advertOrienteAppMapper.batchInsert(
                        appSlotMap.entrySet().stream()
                                .map(entry -> {
                                    AdvertOrienteApp advertOrientApp = new AdvertOrienteApp();
                                    advertOrientApp.setAdvertId(orient.getAdvertId());
                                    advertOrientApp.setOrientId(orient.getId());
                                    advertOrientApp.setAppId(entry.getKey());
                                    advertOrientApp.setOrienteSlotIds(JSONArray.toJSONString(entry.getValue()));
                                    return advertOrientApp;
                                }).collect(Collectors.toList()));
            }
        }

        // 更新流量包定向
        if (null != req.getOrientTrafficIds()) {
            advertOrientTrafficService.update(orient.getAdvertId(), orient.getId(), req.getOrientTrafficIds());
        }

        // 更新广告定向配置
        advertOrientationMapper.updateWithDailyBudget(updateOrient);
        return orient.getAdvertId();
    }

    @Override
    public int updateAdvertOrientation(AdvertOrientation param) {
        return advertOrientationMapper.updateAdvertOrientation(param);
    }

    @Override
    public boolean deleteAdvertOrientation(Long id) {
        return advertOrientationMapper.deleteAdvertOrientation(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyAdvertOrientation(Long originOrientId, Long advertId) {
        AdvertOrientation originOrient = advertOrientationMapper.selectAdvertOrientationById(originOrientId);
        if (null == originOrient) {
            throw new CustomException("无效的配置ID");
        }

        // 新增配置
        AdvertOrientation orient = new AdvertOrientation();
        orient.setOrientName(originOrient.getOrientName() + (null == advertId ? "-复制" : ""));
        orient.setAdvertId(NumberUtils.defaultLong(advertId, originOrient.getAdvertId()));
        orient.setLandpageType(originOrient.getLandpageType());
        orient.setLandpageUrl(originOrient.getLandpageUrl());
        orient.setWeight(originOrient.getWeight());
        orient.setChargeType(originOrient.getChargeType());
        orient.setUnitPrice(originOrient.getUnitPrice());
        orient.setMilliUnitPrice(originOrient.getMilliUnitPrice());
        orient.setOcpcConvType(originOrient.getOcpcConvType());
        orient.setOcpcConvCost(originOrient.getOcpcConvCost());
        orient.setDailyBudget(originOrient.getDailyBudget());
        orient.setDeviceTarget(originOrient.getDeviceTarget());
        orient.setFlowTarget(originOrient.getFlowTarget());
        orient.setOsTarget(originOrient.getOsTarget());
        orient.setIspTarget(originOrient.getIspTarget());
        orient.setServingHour(originOrient.getServingHour());
        orient.setAreaTarget(originOrient.getAreaTarget());
        orient.setIsDefault(null != advertId ? originOrient.getIsDefault() : NOT_DEFAULT.getType());
        orient.setServingSwitch(OFF.getStatus());
        Optional.ofNullable(SecurityUtils.getLoginUser()).ifPresent(user -> {
            orient.setOperatorId(user.getCrmAccountId());
            orient.setOperatorName(user.getUserName());
        });
        advertOrientationMapper.insertAdvertOrientation(orient);

        // 屏蔽媒体
        List<AdvertBannedApp> bannedApps = advertBannedAppMapper.selectByOrientId(originOrientId);
        if (CollectionUtils.isNotEmpty(bannedApps)) {
            advertBannedAppMapper.batchInsert(bannedApps.stream().filter(Objects::nonNull).map(bannedApp -> {
                AdvertBannedApp advertBannedApp = new AdvertBannedApp();
                advertBannedApp.setAdvertId(orient.getAdvertId());
                advertBannedApp.setOrientId(orient.getId());
                advertBannedApp.setAppId(bannedApp.getAppId());
                return advertBannedApp;
            }).collect(Collectors.toList()));
        }
        // 定向媒体
        List<AdvertOrienteApp> orientApps = advertOrienteAppMapper.selectByOrientId(originOrientId);
        if (CollectionUtils.isNotEmpty(orientApps)) {
            advertOrienteAppMapper.batchInsert(orientApps.stream().filter(Objects::nonNull).map(orientApp -> {
                AdvertOrienteApp advertOrientApp = new AdvertOrienteApp();
                advertOrientApp.setAdvertId(orient.getAdvertId());
                advertOrientApp.setOrientId(orient.getId());
                advertOrientApp.setAppId(orientApp.getAppId());
                advertOrientApp.setOrienteSlotIds(orientApp.getOrienteSlotIds());
                return advertOrientApp;
            }).collect(Collectors.toList()));
        }
        // 流量包定向
        List<Long> trafficPackageIds = advertOrientTrafficService.selectTrafficPackageIdByOrientId(originOrientId);
        advertOrientTrafficService.update(orient.getAdvertId(), orient.getId(), trafficPackageIds);

        return orient.getAdvertId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateAreaTarget(List<Long> orientIds, Set<String> areaTarget) {
        if (CollectionUtils.isEmpty(orientIds)) {
            return 0;
        }
        return advertOrientationMapper.batchUpdateAreaTarget(orientIds, JSON.toJSONString(CollUtil.defaultIfEmpty(areaTarget, Collections.emptySet())));
    }

    @Override
    public int updateLandpageUrl(Long orientId, String landpageUrl) {
        AdvertOrientation param = new AdvertOrientation();
        param.setId(orientId);
        param.setLandpageUrl(landpageUrl);
        return advertOrientationMapper.updateAdvertOrientation(param);
    }

    @Override
    public int updateDailyBudget(AdvertOrientation advertOrientation) {
        return advertOrientationMapper.updateWithDailyBudget(advertOrientation);
    }

    @Override
    public Map<Long, Integer> selectCountMapByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }
        List<AdvertOrientCountBo> list = advertOrientationMapper.selectOrientCountByAdvertIds(advertIds);
        return list.stream().collect(Collectors.toMap(AdvertOrientCountBo::getAdvertId, AdvertOrientCountBo::getCount, (o, n) -> n));
    }

    @Override
    public List<Long> selectAdvertIdsByOrientIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return advertOrientationMapper.selectAdvertIdsByOrientIds(ids);
    }

    @Override
    public Set<Long> selectOrientBannedOrientIds(List<Long> advertIds) {
        List<Long> list = advertOrientationMapper.selectOrientBannedOrientIds(advertIds);
        return new HashSet<>(list);
    }

    @Override
    public List<AdvertOrientLandpageBo> selectLandpageByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyList();
        }
        return advertOrientationMapper.selectLandpageByAdvertIds(advertIds);
    }

    @Override
    public Boolean batchUpdateAdvertOrientation(AdvertOrientBatchUpdateParam param) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getIds())) {
            return false;
        }
        return advertOrientationMapper.batchUpdateAdvertOrientation(param);
    }

    @Override
    public List<AdvertOrientLandpageBo> selectByLandpage(String landpageUrl) {
        if (StringUtils.isBlank(landpageUrl)) {
            return Collections.emptyList();
        }
        return advertOrientationMapper.selectByLandpage(landpageUrl);
    }
}
