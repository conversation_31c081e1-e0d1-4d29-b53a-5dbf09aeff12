package com.ruoyi.system.service.oa.department.impl;

import com.ruoyi.system.entity.oa.department.DepartmentEntity;
import com.ruoyi.system.mapper.oa.department.DepartmentMapper;
import com.ruoyi.system.service.oa.department.DepartmentService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * oa部门表 Service
 *
 * <AUTHOR>
 * @date 2021-10-9 14:20:23
 */
@Service
public class DepartmentServiceImpl implements DepartmentService {

    @Autowired
    private DepartmentMapper departmentMapper;

    @Override
    public DepartmentEntity selectById(Long id){
        if(Objects.isNull(id)){
            return null;
        }
        return departmentMapper.selectById(id);
    }
    @Override
    public Map<Long, String> selectDepartmentNameMap(List<Long> departmentIds) {
        if(CollectionUtils.isEmpty(departmentIds)){
            return Collections.emptyMap();
        }
        List<DepartmentEntity> oaDepartmentEntities = departmentMapper.selectByIds(departmentIds);
        return oaDepartmentEntities.stream().collect(Collectors.toMap(DepartmentEntity::getId, DepartmentEntity::getDepartmentName));
    }

    @Override
    public Map<Long, String> selectDepartmentKeyMap(List<Long> departmentIds) {
        if(CollectionUtils.isEmpty(departmentIds)){
            return Collections.emptyMap();
        }
        List<DepartmentEntity> oaDepartmentEntities = departmentMapper.selectByIds(departmentIds);
        return oaDepartmentEntities.stream().collect(Collectors.toMap(DepartmentEntity::getId, DepartmentEntity::getDepartmentKey));
    }

    @Override
    public List<DepartmentEntity> selectAllDepartment() {
        return departmentMapper.selectAllDepartment();
    }

    @Override
    public List<DepartmentEntity> selectByCompanyIds(List<Long> companyIds) {
        if(CollectionUtils.isEmpty(companyIds)){
            return Collections.emptyList();
        }
        return departmentMapper.selectByCompanyIds(companyIds);
    }

    @Override
    public List<Long> selectDepartmentIds(List<String> departmentKeys) {
        if (CollectionUtils.isEmpty(departmentKeys)) {
            return Collections.emptyList();
        }
        return departmentMapper.selectDepartmentIds(departmentKeys);
    }
}
