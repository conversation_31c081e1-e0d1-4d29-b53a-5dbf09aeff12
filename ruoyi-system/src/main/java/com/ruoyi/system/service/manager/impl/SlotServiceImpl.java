package com.ruoyi.system.service.manager.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.SlotUrlConstants;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.enums.domain.DomainType;
import com.ruoyi.common.enums.plugin.PluginSwitchEnum;
import com.ruoyi.common.enums.plugin.PluginTypeEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.slot.SlotDomainConfigBo;
import com.ruoyi.system.domain.slot.AreaTargetRedirectItem;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.plugin.Plugin;
import com.ruoyi.system.entity.plugin.PluginInfo;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.entity.slot.SlotRetConfig;
import com.ruoyi.system.entity.slot.SlotSpec;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.mapper.manager.AppMapper;
import com.ruoyi.system.mapper.manager.SlotConfigMapper;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.mapper.manager.SlotSpecMapper;
import com.ruoyi.system.message.rocketmq.message.RefreshCacheMessage;
import com.ruoyi.system.message.rocketmq.producer.RefreshCacheMqProducer;
import com.ruoyi.system.req.slot.RetConfigReq;
import com.ruoyi.system.req.slot.SlotModifyReq;
import com.ruoyi.system.req.slot.SlotReq;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.domain.DomainReplaceService;
import com.ruoyi.system.service.engine.cache.AppCacheService;
import com.ruoyi.system.service.engine.cache.DomainCacheService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.PluginService;
import com.ruoyi.system.service.manager.SlotConfigService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.slot.SlotShuntTaskService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.service.validate.SlotRedirectValidateService;
import com.ruoyi.system.vo.advert.DegradedAdvertVO;
import com.ruoyi.system.vo.slot.RetConfigVO;
import com.ruoyi.system.vo.slot.SlotVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.isCrmUser;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;
import static com.ruoyi.common.enums.common.SysConfigKeyEnum.DEFAULT_ACT_URL;
import static com.ruoyi.common.enums.common.SysConfigKeyEnum.DEFAULT_DOMAIN_LANDPAGE;
import static com.ruoyi.common.enums.domain.DomainType.ACTIVITY_DOMAIN;
import static com.ruoyi.common.enums.domain.DomainType.LANDPAGE_DOMAIN;
import static com.ruoyi.common.enums.domain.DomainType.SLOT_DOMAIN;
import static com.ruoyi.common.enums.slot.SlotDataVisibleEnum.INVISIBLE;
import static com.ruoyi.common.enums.slot.SlotDataVisibleEnum.VISIBLE;

/**
 * 广告位Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Slf4j
@Service
public class SlotServiceImpl implements SlotService {

    // 广告位投放链接配置Key
    private static final String SLOT_URL = "slot.st.url";

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private SlotSpecMapper slotSpecMapper;

    @Autowired
    private AppMapper appMapper;

    @Autowired
    private SlotConfigMapper slotConfigMapper;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RefreshCacheMqProducer refreshCacheMqProducer;

    @Autowired
    private DomainReplaceService domainReplaceService;

    @Autowired
    private PluginService pluginService;

    @Autowired
    private SlotShuntTaskService slotShuntTaskService;

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private SlotConfigService slotConfigService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private AccountRelationService accountRelationService;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private DomainCacheService domainCacheService;

    @Autowired
    private SlotRedirectValidateService slotRedirectValidateService;

    @Autowired
    private AppCacheService appCacheService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    /**
     * 查询广告位
     *
     * @param id 广告位ID
     * @return 广告位
     */
    @Override
    public SlotVO selectSlotById(Long id) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        Slot slot = slotMapper.selectSlotById(id);
        if (null == slot) {
            return null;
        }

        // 非CRM用户限制查询本账号的数据
        if (!isCrmUser(user.getMainType()) && !Objects.equals(user.getCrmAccountId(), slot.getAccountId())) {
            return null;
        }

        SlotVO slotVO = new SlotVO();
        slotVO.setSlotSpecId(slot.getSlotSpecId());
        slotVO.setSlotName(slot.getSlotName());
        slotVO.setSckType(slot.getSckType());
        slotVO.setShowServingUrl(0);

        // CRM用户补充更多信息
        if (!Objects.equals(user.getMainType(), AccountMainType.CRM.getType())) {
            return slotVO;
        }

        slotVO.setAppId(slot.getAppId());
        slotVO.setAppName(selectAppName(slot.getAppId()));
        slotVO.setSlotUrl(slot.getSlotUrl());

        // 广告位配置
        SlotConfig slotConfig = slotConfigMapper.selectBySlotId(id);
        if (null != slotConfig) {
            slotVO.setRedirectType(slotConfig.getRedirectType());
            slotVO.setRedirectValue(slotConfig.getRedirectValue());

            // 广告位域名替换
            if (StringUtils.isNotBlank(slotConfig.getDomainConfig())) {
                Optional.ofNullable(JSON.parseObject(slotConfig.getDomainConfig(), SlotDomainConfigBo.class)).ifPresent(domainConfig -> {
                    slotVO.setSlotUrl(domainReplaceService.doReplaceDomain(slot.getSlotUrl(), domainConfig.getSlotDomain()));
                    slotVO.setAutoReplace(domainConfig.getAutoReplace());
                });
            }

            // 如果存在指定标签，则域名选择微信可用域名
            if (slotCacheService.isTagExistBySlotId(slot.getId(), "微信自动替换")) {
                Set<String> domainPool = domainCacheService.selectWechatValidDomainListCache(DomainType.SLOT_DOMAIN.getType());
                if (CollectionUtils.isNotEmpty(domainPool)) {
                    String originDomain = UrlUtils.extractDomain(slotVO.getSlotUrl());
                    if (!domainPool.contains(originDomain)) {
                        slotVO.setSlotUrl(domainReplaceService.doReplaceDomain(slotVO.getSlotUrl(), domainPool.iterator().next()));
                    }
                }
                slotVO.setServingUrl(slot.getSlotUrl().replace("/open", "/serving"));
                slotVO.setShowServingUrl(1);
            }

            // 返回挽留配置
            if (StringUtils.isNotBlank(slotConfig.getRetConfig())) {
                RetConfigVO retConfig = JSON.parseObject(slotConfig.getRetConfig(), RetConfigVO.class);
                if (PluginSwitchEnum.isOn(retConfig.getIsOpen())) {
                    Long pluginId = retConfig.getPluginId();
                    Plugin plugin = pluginService.selectById(pluginId);
                    if (null != plugin) {
                        retConfig.setPluginType(plugin.getPluginType());
                        PluginInfo pluginInfo = JSON.parseObject(plugin.getPluginInfo(), PluginInfo.class);
                        if (null != pluginInfo) {
                            retConfig.setActivityId(pluginInfo.getActivityId());
                            retConfig.setUrl(pluginInfo.getUrl());
                        }
                    }
                }
                slotVO.setRetConfig(retConfig);
            }
            // 兜底直投广告
            if (StringUtils.isNotBlank(slotConfig.getDegradedOrientIds())) {
                List<Long> degradedOrientIds = JSON.parseArray(slotConfig.getDegradedOrientIds(), Long.class);
                if (CollectionUtils.isNotEmpty(degradedOrientIds)) {
                    slotVO.setDegradedDirectAdverts(advertOrientationService.selectListByIds(degradedOrientIds).stream()
                            .map(orient -> new DegradedAdvertVO(orient.getAdvertId(), orient.getId()))
                            .collect(Collectors.toList()));
                }
            }
        }

        // 广告位切量计划状态
        Map<Long, Integer> slotShuntMap = slotShuntTaskService.selectSlotShuntMap(Collections.singletonList(slot.getId()));
        slotVO.setIsShunting(slotShuntMap.getOrDefault(slot.getId(), 0));

        // 广告位昨日数据
        SlotData slotData = slotDataService.selectBySlotIdAndDate(id, DateUtil.beginOfDay(DateUtil.yesterday()));
        if (null != slotData) {
            slotVO.setYdaySlotReqPv(slotData.getSlotRequestPv());
            slotVO.setYdaySlotReqUv(slotData.getSlotRequestUv());
        } else {
            slotVO.setYdaySlotReqPv(0);
            slotVO.setYdaySlotReqUv(0);
        }

        // 信息流投放
        Set<String> appTags = appCacheService.getAppTagSetByAppId(slot.getAppId());
        if (CollectionUtils.isNotEmpty(appTags)) {
            if (appTags.contains( "巨量")) {
                // 巨量投放链接拼接 &hu=__ADVERTISER_ID__
                slotVO.setSlotUrl(slotVO.getSlotUrl() + "&hu=__ADVERTISER_ID__");
                // 百度 &hu=__USER_ID__
                // 快手 &hu=__ACCOUNTID__
            } else if (appTags.contains( "灯火")) {
                slotVO.setSlotUrl(slotVO.getSlotUrl() + "&request=__REQUEST_ID__&hu=__PRINCIPAL_ID__");
            } else if (appTags.contains("Sigmob")) {
                slotVO.setSlotUrl(slotVO.getSlotUrl() + "&get_callback=_CALLBACK_");
            } else if (appTags.contains("VIVO")) {
                slotVO.setSlotUrl(slotVO.getSlotUrl() + "&adid=__ADID__&requestid=__REQUESTID__&hu=__ADVERTISERID__");
            }
        }
        return slotVO;
    }

    @Override
    public Slot selectSimpleSlotById(Long id) {
        if (null == id) {
            return null;
        }
        return slotMapper.selectSlotById(id);
    }

    @Override
    public List<Slot> selectList(Slot param) {
        return slotMapper.selectSlotList(param);
    }

    /**
     * 查询广告位列表
     *
     * @param slot 广告位
     * @return 广告位
     */
    @Override
    public List<Slot> selectSlotList(Slot slot) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();
        Long accountId = slot.getAccountId();

        // 非CRM用户限制查询账号
        if (!isCrmUser(user.getMainType())) {
            accountId = user.getCrmAccountId();
        }
        slot.setAccountId(accountId);

        // 媒体模糊查询
        if (StringUtils.isNotBlank(slot.getSearchValue()) || StringUtils.isNotBlank(slot.getAppName())) {
            App app = new App();
            app.setAccountId(accountId);
            app.setSearchValue(slot.getSearchValue());
            app.setAppName(slot.getAppName());
            List<Long> appIds = appMapper.selectAppIdList(app);
            if (CollectionUtils.isEmpty(appIds)) {
                return Collections.emptyList();
            }
            slot.setAppIds(appIds);
        }
        // 负责人查询
        if (CollectionUtils.isNotEmpty(slot.getManagerIds())) {
            List<Long> accountIds = accountRelationService.selectBySrcAccountIds(slot.getManagerIds());
            if (CollectionUtils.isEmpty(accountIds)) {
                return Collections.emptyList();
            }
            slot.setAccountIds(accountIds);
        }

        List<Slot> slots;
        // 非CRM用户根据id排序
        if (!isCrmUser(user.getMainType())) {
            TableSupport.startPage();
            slots = slotMapper.selectSlotList(slot);
        } else {
            // 数据权限控制
            DataPermissionBo permission = dataPermissionManager.selectSlot();
            if (hasPartialPermission(permission.getType())) {
                if (CollectionUtils.isEmpty(permission.getValues())) {
                    return Collections.emptyList();
                }
                if (null == slot.getSlotIds()) {
                    slot.setSlotIds(permission.getValues());
                } else {
                    slot.getSlotIds().retainAll(permission.getValues());
                }
                if (CollectionUtils.isEmpty(slot.getSlotIds())) {
                    return Collections.emptyList();
                }
            }
            slot.setCurDate(DateUtil.beginOfDay(DateUtils.addDays(new Date(),-1)));
            TableSupport.startPage();
            slots = slotMapper.selectSlotListBySlotDataSort(slot);
        }

        // 补充信息
        if (CollectionUtils.isNotEmpty(slots)) {
            App app = new App();
            app.setAccountId(accountId);
            List<App> apps = appMapper.selectSimpleAppList(app);
            Map<Long, String> appNameMap = new HashMap<>(apps.size());
            if (CollectionUtils.isNotEmpty(apps)) {
                apps.forEach(ele -> appNameMap.put(ele.getId(), ele.getAppName()));
            }
            slots.forEach(ele -> ele.setAppName(appNameMap.get(ele.getAppId())));
        }
        //todo
        List<Long> slotList = Lists.newArrayList(854556L, 854706L);
        slots = slots.stream().filter(slotentity -> !slotList.contains(slotentity.getId())).collect(Collectors.toList());
        return slots;
    }

    @Override
    public List<Long> selectSlotIdsBySearchValue(String searchValue) {
        if (StringUtils.isBlank(searchValue)) {
            return Collections.emptyList();
        }

        Slot param = new Slot();
        param.setSearchValue(searchValue);
        return slotMapper.selectSlotIdList(param);
    }

    @Override
    public List<Slot> selectTotalSlotList() {
        Slot slot = new Slot();

        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();
        slot.setAccountId(user.getCrmAccountId());

        return slotMapper.selectSimpleSlotList(slot);
    }

    /**
     * 新增广告位
     *
     * @param slot 广告位
     * @return 结果
     */
    @Override
    public int insertSlot(Slot slot) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();
        slot.setAccountId(user.getCrmAccountId());

        App app = appMapper.selectAppById(slot.getAppId());
        if (null == app || !Objects.equals(user.getCrmAccountId(), app.getAccountId())) {
            throw new CustomException("新增广告位异常，请刷新页面后操作");
        }

        // 新增广告位规格
        if (null == slot.getSlotSpecId() && null != slot.getSlotSpec()) {
            SlotSpec spec = slot.getSlotSpec();
            spec.setSpecName(spec.getLength() + "*" + spec.getWidth());
            slotSpecMapper.insertSlotSpec(spec);
            slot.setSlotSpecId(spec.getId());
        }

        // 新增广告位
        int result = slotMapper.insertSlot(slot);
        if (result < 1) {
            return result;
        }

        // 生成广告位链接并更新
        String slotUrl = genSlotUrl(slot.getAppId(), slot.getId());
        updateSlotUrl(slot.getId(), slotUrl);
        slot.setSlotUrl(slotUrl);
        return result;
    }

    /**
     * 修改广告位
     *
     * @param req 广告位修改参数
     * @return 结果
     */
    @Override
    public int updateSlot(SlotModifyReq req) {
        Slot updateSlot = new Slot();
        updateSlot.setId(req.getId());

        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 查询广告位
        Slot slot = slotMapper.selectSlotById(req.getId());

        // 非CRM用户只能修改本账号的广告位的广告位名称
        if (!Objects.equals(user.getMainType(), AccountMainType.CRM.getType())) {
            if (!Objects.equals(user.getCrmAccountId(), slot.getAccountId())) {
                return 0;
            }
            updateSlot.setSlotName(req.getSlotName());
            return slotMapper.updateSlot(updateSlot);
        }

        // 投放配置
        List<AreaTargetRedirectItem> items = null;
        if (StringUtils.isNotBlank(req.getRedirectValue())) {
            items = JSON.parseArray(req.getRedirectValue(), AreaTargetRedirectItem.class);
            if (CollectionUtils.isNotEmpty(items)) {
                // 投放地域校验
                slotRedirectValidateService.checkSlotAreaTargetRedirect(items);
                // 投放设置增加操作人
                items.forEach(item -> item.getRedirectValue().forEach(value -> {
                    if (Objects.equals(value.getIsModified(), 1)) {
                        value.setOperatorName(user.getUserName());
                        value.setModifyTime(DateUtil.now());
                    }
                }));
            }
        }

        // 更新投放配置
        SlotConfig config = new SlotConfig();
        config.setSlotId(req.getId());
        // 投放信息
        if (CollectionUtils.isNotEmpty(items)) {
            config.setRedirectType(req.getRedirectType());
            config.setRedirectValue(JSON.toJSONString(items));
        }
        // 域名自动替换开关
        if (null != req.getAutoReplace()) {
            SlotDomainConfigBo domainConfig = JSON.parseObject(config.getDomainConfig(), SlotDomainConfigBo.class);
            if (null == domainConfig) {
                domainConfig = new SlotDomainConfigBo();
            }
            domainConfig.setAutoReplace(req.getAutoReplace());
            config.setDomainConfig(JSON.toJSONString(domainConfig));
        }
        // 返回挽留设置
        if (null != req.getRetConfig()) {
            // URL补全协议头
            if (StringUtils.isNotBlank(req.getRetConfig().getUrl()) && !req.getRetConfig().getUrl().startsWith("http")) {
                req.getRetConfig().setUrl("https://" + req.getRetConfig().getUrl());
            }

            SlotRetConfig retConfig = new SlotRetConfig();
            retConfig.setIsOpen(NumberUtils.defaultInt(req.getRetConfig().getIsOpen()));
            retConfig.setPluginId(getPluginId(req.getRetConfig()));
            config.setRetConfig(JSON.toJSONString(retConfig));
        }

        int result;
        SlotConfig oldConfig = slotConfigMapper.selectBySlotId(req.getId());
        if (null == oldConfig) {
            result = slotConfigMapper.insertSlotConfig(config);
        } else {
            result = slotConfigMapper.updateSlotConfig(config);
        }

        // 刷新缓存
        sendRefreshCacheMsg(slot.getId());
        return result;
    }

    @Override
    public int updateStatus(SlotReq req) {
        Slot slot = new Slot();
        slot.setId(req.getId());
        slot.setStatus(req.getStatus());
        int result = slotMapper.updateSlot(slot);
        // 刷新缓存
        sendRefreshCacheMsg(slot.getId());
        return result;
    }

    @Override
    public Map<Long, Integer> groupByAccountId(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<Map<String, Long>> list = slotMapper.groupByAccountId(accountIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> result = new HashMap<>(list.size());
        for (Map<String, Long> map : list) {
            result.put(map.get("accountId"), map.getOrDefault("count", 0L).intValue());
        }
        return result;
    }

    @Override
    public Map<Long, Integer> groupByAppId(List<Long> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return Collections.emptyMap();
        }
        List<Map<String, Long>> list = slotMapper.groupByAppId(appIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> result = new HashMap<>(list.size());
        for (Map<String, Long> map : list) {
            result.put(map.get("appId"), map.getOrDefault("count", 0L).intValue());
        }
        return result;
    }

    @Override
    public JSONObject querySlotDomainConfig(Long slotId) {
        Slot slot = slotMapper.selectSlotById(slotId);
        if (null == slot ) {
            throw new CustomException("无效的广告位ID");
        }
        SlotConfig slotConfig = slotConfigMapper.selectBySlotId(slotId);
        JSONObject config = null;
        if (null != slotConfig) {
            config = JSON.parseObject(slotConfig.getDomainConfig());
        }
        if (null == config) {
            config = new JSONObject();
        }
        config.putIfAbsent(SLOT_DOMAIN.getKey(), UrlUtils.extractDomain(slot.getSlotUrl()));
        config.putIfAbsent(ACTIVITY_DOMAIN.getKey(), UrlUtils.extractDomain(sysConfigService.selectConfigCacheByKey(DEFAULT_ACT_URL.getKey())));
        config.putIfAbsent(LANDPAGE_DOMAIN.getKey(), sysConfigService.selectConfigCacheByKey(DEFAULT_DOMAIN_LANDPAGE.getKey()));
        return config;
    }

    @Override
    public boolean updateSlotDomainConfig(Long slotId, Integer domainType, String domain) {
        Slot slot = slotMapper.selectSlotById(slotId);
        if (null == slot ) {
            return false;
        }
        SlotConfig slotConfig = slotConfigMapper.selectBySlotId(slotId);
        JSONObject config;
        if (null == slotConfig || StringUtils.isBlank(slotConfig.getDomainConfig())) {
            config = new JSONObject();
        } else {
            config = JSON.parseObject(slotConfig.getDomainConfig());
        }
        config.put(DomainType.getKeyByType(domainType), domain);

        SlotConfig updateConfig = new SlotConfig();
        updateConfig.setSlotId(slotId);
        updateConfig.setDomainConfig(config.toString());
        boolean result;
        if (null == slotConfig) {
            result = slotConfigMapper.insertSlotConfig(updateConfig) > 0;
        } else {
            result = slotConfigMapper.updateSlotConfig(updateConfig) > 0;
        }

        // 刷新缓存
        sendRefreshCacheMsg(slot.getId());
        return result;
    }

    @Override
    public List<Long> selectSlotIdsByAppIds(List<Long> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return Collections.emptyList();
        }

        Slot param = new Slot();
        param.setAppIds(appIds);
        List<Slot> slots = slotMapper.selectSlotList(param);
        if (CollectionUtils.isEmpty(slots)) {
            return Collections.emptyList();
        }
        return slots.stream().map(Slot::getId).collect(Collectors.toList());
    }

    @Override
    public Map<Long, Slot> selectSlotAppMapByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        // 广告位列表
        List<Slot> slots = slotMapper.selectSimpleSlotByIds(ids);
        if (CollectionUtils.isEmpty(slots)) {
            return Collections.emptyMap();
        }
        // 媒体列表
        List<Long> appIds = slots.stream().map(Slot::getAppId).collect(Collectors.toList());
        List<App> apps = appMapper.selectSimpleInfoByIds(appIds);
        Map<Long, App> appMap = apps.stream().collect(Collectors.toMap(App::getId, Function.identity(), (v1, v2) -> v1));
        // 构造广告位映射
        return slots.stream().collect(Collectors.toMap(Slot::getId, slot -> {
            App app = appMap.get(slot.getAppId());
            if (null != app) {
                slot.setAppId(app.getId());
                slot.setAppName(app.getAppName());
            }
            return slot;
        }, (v1, v2) -> v1));
    }

    @Override
    public void updateSlotChannel(Long slotId, Integer channel) {
        if (null == slotId || null == channel) {
            return;
        }

        Slot slot = slotMapper.selectSlotById(slotId);
        if (null == slot) {
            return;
        }

        SlotConfig config = slotConfigMapper.selectBySlotId(slotId);
        if (null == config) {
            config = new SlotConfig();
        }
        config.setSlotId(slotId);
        config.setChannel(channel);

        if (null == config.getId()) {
            slotConfigMapper.insertSlotConfig(config);
        } else {
            slotConfigMapper.updateSlotConfig(config);
        }

        // 刷新缓存
        sendRefreshCacheMsg(slotId);
    }

    @Override
    public Integer getSlotChannel(Long slotId) {
        int channel = 0;

        if (null == slotId) {
            return channel;
        }

        try {
            SlotConfig config = slotConfigMapper.selectBySlotId(slotId);
            if (null != config && null != config.getChannel()) {
                return config.getChannel();
            }
        } catch (Exception ignored) {}
        return channel;
    }

    @Override
    public List<Slot> selectByAppId(Long appId) {
        if (null == appId) {
            return Collections.emptyList();
        }
        return slotMapper.selectSlotByAppId(appId);
    }

    @Override
    public List<Slot> selectSimpleSlotByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        return slotMapper.selectSimpleSlotByIds(ids);
    }

    @Override
    public Map<Long, String> selectSlotNameMap(List<Long> ids) {
        List<Slot> slots = selectSimpleSlotByIds(ids);
        return slots.stream().collect(Collectors.toMap(Slot::getId,Slot::getSlotName,(v1,v2) -> v1));
    }

    @Override
    public Map<Long, String> selectSlotNameMapByAppIds(List<Long> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return Collections.emptyMap();
        }
        List<Slot> slots = slotMapper.selectByAppIds(appIds);
        return slots.stream().collect(Collectors.toMap(Slot::getId,Slot::getSlotName,(v1,v2) -> v1));
    }

    @Override
    public Map<Long, Long> selectAppIdMap(List<Long> slotIds) {
        List<Slot> slots = selectSimpleSlotByIds(slotIds);
        return slots.stream().collect(Collectors.toMap(Slot::getId, Slot::getAppId, (v1, v2) -> v2));
    }

    @Override
    public Map<Long, String> selectSlotNameMap() {
        List<Slot> slots = slotMapper.selectSlotIdAndName();
        return slots.stream().collect(Collectors.toMap(Slot::getId, Slot::getSlotName, (v1, v2) -> v1));
    }

    @Override
    public Map<Long, String> selectSlotUrlMap() {
        List<Slot> slots = slotMapper.selectSlotIdAndSlotUrl();
        return slots.stream().collect(Collectors.toMap(Slot::getId, Slot::getSlotUrl, (v1, v2) -> v2));
    }

    @Override
    public Map<String, Set<Long>> getDomainSlotMapS(List<String> domains) {
        Map<String, Set<Long>> map = domains.stream().collect(Collectors.toMap(Function.identity(), data -> new HashSet<>()));

        Map<Long, String> slotUrlMap = selectSlotUrlMap();
        List<SlotConfig> slotConfigs = slotConfigService.selectSlotConfigList(new SlotConfig());
        slotConfigs.forEach(config -> {
            Optional.ofNullable(JSON.parseObject(config.getDomainConfig(), SlotDomainConfigBo.class)).ifPresent(domainConfig -> {
                Long slotId = config.getSlotId();
                if (StringUtils.isNotBlank(domainConfig.getSlotDomain()) && map.containsKey(domainConfig.getSlotDomain())) {
                    map.get(domainConfig.getSlotDomain()).add(slotId);
                    slotUrlMap.remove(slotId);
                }
                if (StringUtils.isNotBlank(domainConfig.getActivityDomain()) && map.containsKey(domainConfig.getActivityDomain())) {
                    map.get(domainConfig.getActivityDomain()).add(slotId);
                }
                if (StringUtils.isNotBlank(domainConfig.getLandpageDomain()) && map.containsKey(domainConfig.getLandpageDomain())) {
                    map.get(domainConfig.getLandpageDomain()).add(slotId);
                }
            });
        });
        slotUrlMap.forEach((slotId, url) -> {
            String domain = UrlUtils.extractDomain(url);
            if (StringUtils.isNotBlank(domain) && map.containsKey(domain)) {
                map.get(domain).add(slotId);
            }
        });
        return map;
    }

    @Override
    public List<Slot> selectByAppIds(List<Long> appIds) {
        if(CollectionUtils.isEmpty(appIds)){
            return Collections.emptyList();
        }
        return slotMapper.selectByAppIds(appIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setAdjustSwitch(Long slotId, Integer status) {
        Slot slot = slotMapper.selectSlotById(slotId);
        if (null == slot) {
            throw new CustomException("无效的广告ID");
        }

        SlotConfig config = slotConfigService.selectBySlotId(slotId);
        if (null == config) {
            config = new SlotConfig();
        }
        SlotSwitchConfig switchConfig = JSON.parseObject(config.getSwitchConfig(), SlotSwitchConfig.class);
        if (null == switchConfig) {
            switchConfig = new SlotSwitchConfig();
        }
        switchConfig.setAdjust(status);

        // 保存配置
        SlotConfig updateConfig = new SlotConfig();
        updateConfig.setId(config.getId());
        updateConfig.setSlotId(slotId);
        updateConfig.setSwitchConfig(JSON.toJSONString(switchConfig));
        int result = slotConfigService.save(updateConfig);
        if (result < 1) {
            throw new CustomException("设置失败");
        }

        // 移除白名单(兼容)
        whitelistService.remove(WhitelistType.ADJUST_DATA_SLOT, slotId);

        // 更新当日数据的可见可编辑状态
        SlotData slotData = slotDataService.selectBySlotIdAndDate(slotId, DateUtil.beginOfDay(new Date()));
        if (null != slotData) {
            SlotData updateSlotDate = new SlotData();
            updateSlotDate.setId(slotData.getId());
            if (SwitchStatusEnum.isSwitchOn(status)) {
                updateSlotDate.setIsVisible(INVISIBLE.getStatus());
                updateSlotDate.setIsEditable(1);
            } else {
                updateSlotDate.setIsVisible(VISIBLE.getStatus());
                updateSlotDate.setIsEditable(0);
            }
            slotDataService.updateSlotData(updateSlotDate);
        }
    }

    @Override
    public void setAutoChargeSwitch(Long slotId, Integer status) {
        Slot slot = slotMapper.selectSlotById(slotId);
        if (null == slot) {
            throw new CustomException("无效的广告ID");
        }

        SlotConfig config = slotConfigService.selectBySlotId(slotId);
        if (null == config) {
            config = new SlotConfig();
        }
        SlotSwitchConfig switchConfig = JSON.parseObject(config.getSwitchConfig(), SlotSwitchConfig.class);
        if (null == switchConfig) {
            switchConfig = new SlotSwitchConfig();
        }
        switchConfig.setAutoCharge(status);

        // 保存配置
        SlotConfig updateConfig = new SlotConfig();
        updateConfig.setId(config.getId());
        updateConfig.setSlotId(slotId);
        updateConfig.setSwitchConfig(JSON.toJSONString(switchConfig));
        int result = slotConfigService.save(updateConfig);
        if (result < 1) {
            throw new CustomException("设置失败");
        }
    }

    /**
     * 查询媒体名称
     *
     * @param appId 媒体Id
     * @return 媒体名称
     */
    private String selectAppName(Long appId) {
        App app = appMapper.selectAppById(appId);
        return null == app ? null : app.getAppName();
    }

    /**
     * 更新广告位投放链接
     *
     * @param slotId 广告位ID
     * @param slotUrl 广告位投放链接
     */
    private void updateSlotUrl(Long slotId, String slotUrl) {
        if (null != slotId && null != slotUrl) {
            Slot updateSlot = new Slot();
            updateSlot.setId(slotId);
            updateSlot.setSlotUrl(slotUrl);
            slotMapper.updateSlot(updateSlot);
        }
    }

    /**
     * 生成广告位投放链接
     *
     * @param slotId 广告位ID
     * @return 广告位投放链接
     */
    private String genSlotUrl(Long appId, Long slotId) {
        if (null == appId || null == slotId) {
            return null;
        }
        App app = appMapper.selectAppById(appId);
        if (null == app) {
            return null;
        }
        HashMap<String, String> map = new HashMap<>();
        map.put(SlotUrlConstants.APP_KEY, app.getAppKey());
        map.put(SlotUrlConstants.SLOT_ID, String.valueOf(slotId));

        String slotUrl = sysConfigService.selectConfigByKey(SLOT_URL);
        if (StringUtils.isEmpty(slotUrl)) {
            return null;
        }

        return UrlUtils.appendParams(slotUrl, map);
    }

    /**
     * 发送消息刷新广告位缓存
     */
    private void sendRefreshCacheMsg(Long slotId) {
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setSlotId(slotId);
        refreshCacheMqProducer.sendMsg(message);
    }

    /**
     * 获取否则生成插件ID
     *
     * @param retConfig 返回挽留设置
     * @return 插件ID
     */
    private Long getPluginId(RetConfigReq retConfig) {
        if (!PluginSwitchEnum.isOn(retConfig.getIsOpen())) {
            return null;
        }

        Plugin param = new Plugin();
        param.setPluginType(retConfig.getPluginType());
        List<Plugin> plugins = pluginService.selectList(param);

        if (CollectionUtils.isNotEmpty(plugins)) {
            for (Plugin plugin : plugins) {
                if (Objects.equals(plugin.getPluginType(), PluginTypeEnum.RED_PACKETS.getType())) {
                    return plugin.getId();
                }
                PluginInfo pluginInfo = JSON.parseObject(plugin.getPluginInfo(), PluginInfo.class);
                if (null == pluginInfo) {
                    continue;
                }
                if ((Objects.equals(plugin.getPluginType(), PluginTypeEnum.URL.getType())
                        && Objects.equals(retConfig.getUrl(), pluginInfo.getUrl()))
                        || (Objects.equals(plugin.getPluginType(), PluginTypeEnum.ACTIVITY.getType())
                                && Objects.equals(retConfig.getActivityId(), pluginInfo.getActivityId()))) {
                    return plugin.getId();
                }
            }
        }

        PluginInfo pluginInfo = new PluginInfo();
        pluginInfo.setActivityId(retConfig.getActivityId());
        pluginInfo.setUrl(retConfig.getUrl());

        Plugin plugin = new Plugin();
        plugin.setPluginType(retConfig.getPluginType());
        plugin.setPluginInfo(JSON.toJSONString(pluginInfo));
        pluginService.insertPlugin(plugin);
        return plugin.getId();
    }
}
