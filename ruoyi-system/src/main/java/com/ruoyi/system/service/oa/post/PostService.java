package com.ruoyi.system.service.oa.post;

import com.ruoyi.system.entity.oa.post.PostEntity;

import java.util.List;
import java.util.Map;

/**
 * oa职位表 Service
 *
 * <AUTHOR>
 * @date 2021-10-9 14:20:23
 */
public interface PostService {

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    PostEntity selectById(Long id);

    /**
     * 根据职位id列表获取职位名称
     *
     * @param postIds 职位id列表
     * @return <职位id，职位名称>
     */
    Map<Long, String> selectPostNameMap(List<Long> postIds);

    /**
     * 根据部门id列表查询职位列表
     *
     * @param departmentIds 部门id列表
     * @return 职位列表
     */
    List<PostEntity> selectListByDepartmentIds(List<Long> departmentIds);

    /**
     * 查询所有职位
     *
     * @return 职位列表
     */
    List<PostEntity> selectAllPost();

    /**
     * 根据职位id列表查询职位列表
     *
     * @param postIds 职位id列表
     * @return 职位列表
     */
    List<PostEntity> selectPostListByIds(List<Long> postIds);

    /**
     * 根据职位名称模糊查询职位列表
     *
     * @param postName 职位名称
     * @return 职位列表
     */
    List<PostEntity> selectByPostName(String postName);
}
