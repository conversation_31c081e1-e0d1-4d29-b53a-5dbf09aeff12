package com.ruoyi.system.service.open.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.open.HhOcpxRecordService;
import com.ruoyi.system.entity.open.HhOcpxRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.open.HhOcpxRecordMapper;

/**
 * 辉煌OCPX事件记录 Service
 *
 * <AUTHOR>
 * @date 2024-9-18 17:40:48
 */
@Service
public class HhOcpxRecordServiceImpl implements HhOcpxRecordService {
    @Autowired
    private HhOcpxRecordMapper hhOcpxRecordMapper;

    @Override
    public Boolean insert(HhOcpxRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return hhOcpxRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return hhOcpxRecordMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(HhOcpxRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return hhOcpxRecordMapper.updateById(entity) > 0;
    }

    @Override
    public HhOcpxRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return hhOcpxRecordMapper.selectById(id);
    }
}
