package com.ruoyi.system.service.manager;

import com.ruoyi.system.entity.account.AccountRelation;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 账户关联Service接口
 *
 * <AUTHOR>
 * @date 2021-07-15
 */
public interface AccountRelationService {

    /**
     * 查询账号对应的负责人列表映射
     *
     * @param accountIds 媒体账号Id列表
     * @return 媒体账号Id-负责人ID列表映射
     */
    Map<Long, List<Long>> selectByAccountIds(List<Long> accountIds);

    /**
     * 查询负责人管理的账号ID列表
     *
     * @param srcAccountIds 负责人ID列表
     * @return 账号Id列表
     */
    List<Long> selectBySrcAccountIds(List<Long> srcAccountIds);

    /**
     * 查询账号对应的负责人映射
     *
     * @param accountIds 媒体账号Id列表
     * @return 媒体账号ID-<关联类型-起点账户ID列表>映射
     */
    Map<Long, Map<Integer, List<Long>>> selectMapByDestAccountIds(List<Long> accountIds);

    /**
     * 查询账户关联列表
     *
     * @param destAccountIds 终点账户ID列表
     * @param relationType 关联类型
     * @return 账户关联集合
     */
    List<AccountRelation> selectListByDestAccountAndRelationType(List<Long> destAccountIds, Integer relationType);

    /**
     * 根据关联类型查询终点账户ID集合
     *
     * @param relationType 关联类型
     * @return 终点账户ID集合
     */
    Set<Long> selectDestAccountIdByRelationType(Integer relationType);

    /**
     * 更新账户关联
     *
     * @param srcAccountId 起点账户ID
     * @param destAccountId 终点账户ID
     * @param relationType 关联类型
     */
    void updateRelation(Long srcAccountId, Long destAccountId, Integer relationType);

    /**
     * 批量更新账户关联
     *
     * @param srcAccountIds 起点账户ID列表
     * @param destAccountId 终点账户ID
     * @param relationType 关联类型
     */
    void batchUpdateRelation(List<Long> srcAccountIds, Long destAccountId, Integer relationType);
}
