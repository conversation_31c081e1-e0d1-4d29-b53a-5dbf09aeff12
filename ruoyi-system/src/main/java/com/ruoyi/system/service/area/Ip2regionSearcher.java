package com.ruoyi.system.service.area;

import com.ruoyi.system.domain.adengine.Ip2regionInfo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.beans.factory.DisposableBean;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.regex.Pattern;

@Slf4j
public class Ip2regionSearcher implements DisposableBean {

    private static final Pattern SPLIT_PATTERN = Pattern.compile("\\|");

    private final Searcher searcher;

    public Ip2regionSearcher(Searcher searcher) {
        this.searcher = searcher;
    }

    @SneakyThrows
    public String searchStr(String ip) {
        return searcher.search(ip);
    }

    public Ip2regionInfo search(String ip) {
        try {
            String region = searchStr(ip);
            if (region == null) {
                return null;
            }
            Ip2regionInfo ipInfo = new Ip2regionInfo();
            String[] splitInfos = SPLIT_PATTERN.split(region);
            // 补齐5位
            if (splitInfos.length < 5) {
                splitInfos = Arrays.copyOf(splitInfos, 5);
            }
            ipInfo.setCountry(filterZero(splitInfos[0]));
            ipInfo.setRegion(filterZero(splitInfos[1]));
            ipInfo.setProvince(filterZero(splitInfos[2]));
            ipInfo.setCity(filterZero(splitInfos[3]));
            ipInfo.setIsp(filterZero(splitInfos[4]));
            return ipInfo;
        } catch (Exception e) {
            log.error("ip2region异常", e);
        }
        return null;
    }

    /**
     * 数据过滤，因为 ip2Region 采用 0 填充的没有数据的字段
     * @param info info
     * @return info
     */
    private String filterZero(String info) {
        // null 或 0 返回 null
        if (info == null || BigDecimal.ZERO.toString().equals(info)) {
            return null;
        }
        return info;
    }

    @Override
    public void destroy() throws Exception {
        if (this.searcher != null) {
            this.searcher.close();
        }
    }
}
