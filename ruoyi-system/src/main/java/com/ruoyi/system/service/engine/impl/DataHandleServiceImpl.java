package com.ruoyi.system.service.engine.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.local.RequestThreadLocal;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.req.datashow.DataUpdateReq;
import com.ruoyi.system.service.datasource.DataUpdateService;
import com.ruoyi.system.service.engine.DataHandleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 数据处理服务接口实现
 *
 * <AUTHOR>
 * @date 2021/07/21
 */
@Slf4j
@Service
public class DataHandleServiceImpl implements DataHandleService {

    @Autowired
    private DataUpdateService dataUpdateService;

    @Override
    public void addSlotRequestData(Long accountId, Long appId, Long slotId, String deviceId) {
        // 1.获取日期
        String dayStr = DateUtils.getDate();
        Date date = DateUtil.beginOfDay(new Date());
        final int hour = DateUtil.thisHour(true);

        GlobalThreadPool.statExecutorService.execute(() -> {
            // 2.计算pv/uv
            String hourSlotUvKey = Constants.SLOT_UV_SLOT_REQUEST + slotId + ":" + dayStr + ":" + hour;
            String value = Md5Utils.hash(appId + ":" + deviceId);
            final int hourSlotUv = BizUtils.countUv(hourSlotUvKey, value, 1, TimeUnit.HOURS);

            String slotUvKey = Constants.SLOT_UV_SLOT_REQUEST + slotId + ":" + dayStr;
            String slotUvValue = Md5Utils.hash(appId + ":" + deviceId);
            int uv = BizUtils.countUv(slotUvKey, slotUvValue);

            // 3.更新数据
            DataUpdateReq req = new DataUpdateReq();
            req.setDate(date);
            req.setDateStr(dayStr);
            req.setHour(hour);
            req.setAccountId(accountId);
            req.setAppId(appId);
            req.setSlotId(slotId);
            req.setPv(1);
            req.setSlotUv(uv);
            req.setAppUv(uv);
            req.setHourSlotUv(hourSlotUv);

            try {
                dataUpdateService.updateSlotRequestPvUv(req);
            } catch (Exception e) {
                log.error("广告位请求数据更新异常, req={}", JSON.toJSONString(req), e);
            }
        });
    }

    @Override
    public void addPvUvData(final InnerLogType type) {
        int pv = 1;

        String dayStr = DateUtils.getDate();
        Date date = DateUtil.beginOfDay(new Date());
        Integer hour = DateUtil.thisHour(true);

        Long accountId = RequestThreadLocal.get().getAccountId();
        Long appId = RequestThreadLocal.get().getAppId();
        Long slotId = RequestThreadLocal.get().getSlotId();
        Long activityId = RequestThreadLocal.get().getActivityId();

        // 1.判断是uv还是pv数据
        String slotUvKey;
        String appUvKey;
        String hourSlotUvKey;

        switch (type) {
            case ACTIVITY_REQUEST:
                slotUvKey = Constants.SLOT_UV_ACTIVITY_REQUEST + slotId + ":" + dayStr;
                hourSlotUvKey = Constants.SLOT_UV_ACTIVITY_REQUEST + slotId + ":" + dayStr + ":" + hour;
                appUvKey = Constants.APP_UV_ACTIVITY_REQUEST + appId + ":" + dayStr;
                break;
            case ADVERT_CLICK:
                slotUvKey = Constants.SLOT_UV_AD_CLICK + slotId + ":" + dayStr;
                hourSlotUvKey = Constants.SLOT_UV_AD_CLICK + slotId + ":" + dayStr + ":" + hour;
                appUvKey = Constants.APP_UV_AD_CLICK + appId + ":" + dayStr;
                break;
            case ADVERT_EXPOSURE:
                slotUvKey = Constants.SLOT_UV_AD_EXPOSURE + slotId + ":" + dayStr;
                hourSlotUvKey = Constants.SLOT_UV_AD_EXPOSURE + slotId + ":" + dayStr + ":" + hour;
                appUvKey = Constants.APP_UV_AD_EXPOSURE + appId + ":" + dayStr;
                break;
            case ACTIVITY_JOIN:
                slotUvKey = Constants.SLOT_UV_ACTIVITY_JOIN + slotId + ":" + dayStr;
                hourSlotUvKey = Constants.SLOT_UV_ACTIVITY_JOIN + slotId + ":" + dayStr + ":" + hour;
                appUvKey = Constants.APP_UV_ACTIVITY_JOIN + appId + ":" + dayStr;
                break;
            default:
                slotUvKey = "";
                appUvKey = "";
                hourSlotUvKey = "";
                break;
        }

        // 计算uv
        String value = String.valueOf(RequestThreadLocal.get().getConsumerId());
        final int slotUv = BizUtils.countUv(slotUvKey, value);
        final int hourSlotUv = BizUtils.countUv(hourSlotUvKey, value, 1, TimeUnit.HOURS);
        final int appUv = BizUtils.countUv(appUvKey, value);

        // 2.异步更新数据
        GlobalThreadPool.statExecutorService.execute(() -> {
            DataUpdateReq req = new DataUpdateReq();
            req.setType(type);
            req.setDate(date);
            req.setDateStr(dayStr);
            req.setHour(hour);
            req.setAccountId(accountId);
            req.setAppId(appId);
            req.setSlotId(slotId);
            req.setActivityId(activityId);
            req.setPv(pv);
            req.setSlotUv(slotUv);
            req.setAppUv(appUv);
            req.setHourSlotUv(hourSlotUv);

            try {
                dataUpdateService.update(req);
            } catch (Exception e) {
                log.error("{}数据更新异常, req={}", type.getDesc(), JSON.toJSONString(req), e);
            }
        });
    }

    @Override
    public void addPvUvData(InnerLogType type, JSONObject logJson) {
        int pv = 1;

        String dayStr = DateUtils.getDate();
        Date date = DateUtil.beginOfDay(new Date());
        int hour = DateUtil.thisHour(true);

        Long accountId = logJson.getLong("accountId");
        if (null == accountId) {
            accountId = RequestThreadLocal.get().getAccountId();
        }
        Long appId = logJson.getLong("appId");
        Long slotId = logJson.getLong("slotId");
        Long activityId = logJson.getLong("activityId");
        Long advertId = logJson.getLong("advertId");
        Long materialId = logJson.getLong("materialId");

        // 1.判断是uv还是pv数据
        String slotUvKey = "";
        String appUvKey = "";
        String hourSlotUvKey = "";

        switch (type) {
            case ADVERT_EXPOSURE:
                slotUvKey = Constants.SLOT_UV_AD_EXPOSURE + slotId + ":" + dayStr;
                hourSlotUvKey = Constants.SLOT_UV_AD_EXPOSURE + slotId + ":" + dayStr + ":" + hour;
                appUvKey = Constants.APP_UV_AD_EXPOSURE + appId + ":" + dayStr;
                break;
            case ADVERT_CLICK:
                slotUvKey = Constants.SLOT_UV_AD_CLICK + slotId + ":" + dayStr;
                hourSlotUvKey = Constants.SLOT_UV_AD_CLICK + slotId + ":" + dayStr + ":" + hour;
                appUvKey = Constants.APP_UV_AD_CLICK + appId + ":" + dayStr;
                break;
            default:
                break;
        }

        // 计算uv
        Long consumerId = logJson.getLong("consumerId");
        if (null == consumerId) {
            consumerId = RequestThreadLocal.get().getConsumerId();
        }
        String value = String.valueOf(consumerId);
        final int slotUv = BizUtils.countUv(slotUvKey, value);
        final int hourSlotUv = BizUtils.countUv(hourSlotUvKey, value, 1, TimeUnit.HOURS);
        final int appUv = BizUtils.countUv(appUvKey, value);
        final Long finalAccountId = accountId;
        final Long finalAppId = appId;
        final Long finalSlotId = slotId;
        final Long finalActivityId = activityId;

        // 2.异步更新数据
        GlobalThreadPool.statExecutorService.execute(() -> {
            DataUpdateReq req = new DataUpdateReq();
            req.setType(type);
            req.setDate(date);
            req.setDateStr(dayStr);
            req.setHour(hour);
            req.setAccountId(finalAccountId);
            req.setAppId(finalAppId);
            req.setSlotId(finalSlotId);
            req.setActivityId(finalActivityId);
            req.setMaterialId(materialId);
            req.setAdvertId(advertId);
            req.setPv(pv);
            req.setSlotUv(slotUv);
            req.setAppUv(appUv);
            req.setHourSlotUv(hourSlotUv);

            try {
                dataUpdateService.update(req);
            } catch (Exception e) {
                log.error("{}数据更新异常, req={}", type.getDesc(), JSON.toJSONString(req), e);
            }
        });
    }

    @Override
    public void addPvData(final InnerLogType type, JSONObject logJson) {
        String dayStr = DateUtils.getDate();
        Date date = DateUtil.beginOfDay(new Date());
        Integer hour = DateUtil.thisHour(true);
        Long accountId = RequestThreadLocal.get().getAccountId();
        Long appId = RequestThreadLocal.get().getAppId();
        Long slotId = RequestThreadLocal.get().getSlotId();
        Long activityId = RequestThreadLocal.get().getActivityId();
        Long advertId = logJson.getLong("advertId");
        Long materialId = logJson.getLong("materialId");

        // 2.异步更新数据
        GlobalThreadPool.statExecutorService.execute(() -> {
            DataUpdateReq req = new DataUpdateReq();
            req.setType(type);
            req.setDate(date);
            req.setDateStr(dayStr);
            req.setHour(hour);
            req.setAccountId(accountId);
            req.setAppId(appId);
            req.setSlotId(slotId);
            req.setActivityId(activityId);
            req.setAdvertId(advertId);
            req.setMaterialId(materialId);
            req.setPv(1);

            try {
                dataUpdateService.update(req);
            } catch (Exception e) {
                log.error("{}数据更新异常, req={}", type.getDesc(), JSON.toJSONString(req), e);
            }
        });
    }
}
