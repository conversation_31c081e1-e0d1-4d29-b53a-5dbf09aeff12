package com.ruoyi.system.service.engine;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.req.engine.NhStatReq;

import java.util.Date;
import java.util.Map;

/**
 * 埋点服务
 *
 * <AUTHOR>
 * @date 2021/8/19
 */
public interface StatService {

    /**
     * 只打印Inner日志
     *
     * @param type 业务类型
     */
    void innerLog(InnerLogType type);

    /**
     * 打印Inner日志和落库
     *
     * @param type 业务类型
     * @param orderId 订单号
     */
    void innerLogByOrderId(InnerLogType type, String orderId);

    /**
     * 打印Inner日志和落库
     *
     * @param type 业务类型
     * @param orderId 订单号
     * @param action 自定义行为
     */
    void innerLogByOrderId(InnerLogType type, String orderId, Integer action);

    /**
     * 打印Inner日志和落库
     *
     * @param type 业务类型
     * @param order 订单
     * @param action 自定义行为
     */
    void innerLogByOrder(InnerLogType type, Order order, Integer action);

    /**
     * 打印Inner日志、落库和统计数据
     *
     * @param type 业务类型
     * @param orderId 订单号
     */
    void innerLogStatByOrderId(InnerLogType type, String orderId);

    /**
     * 打印Inner日志、落库和统计数据
     */
    void innerLogStatTLByOrderId(NhStatReq req);

    /**
     * 活动访问
     */
    void activityRequest(NhStatReq req);

    /**
     * 活动首屏加载
     */
    void activityMainLoad(NhStatReq req);

    /**
     * 活动参与
     */
    void activityJoin(JSONObject logJson);

    /**
     * 券请求
     */
    void advertRequest(JSONObject logJson);

    /**
     * 发券
     */
    void advertLaunch(JSONObject logJson);

    /**
     * 券曝光
     */
    void advertExposure(NhStatReq req);

    /**
     * 券点击
     */
    void advertClick(String orderId);

    /**
     * 直投广告点击
     */
    @Deprecated
    void directAdvertClick(String orderId);

    /**
     * 直投广告点击
     */
    void directAdvertClick(Date time, Long accountId, Order order, JSONObject logJson);

    /**
     * 落地页曝光
     */
    void landpageExposure(String orderId);

    /**
     * 落地页转化
     */
    void landpageClick(String orderId);

    /**
     * 落地页转化
     */
    void landpageClick(String orderId, Map<String, Object> extParam);

    /**
     * 后端转化
     */
    void convertEvent(String orderId, Integer convType);

    /**
     * 后端转化
     */
    void convertEvent(String orderId, Integer convType, JSONObject convExt);

    /**
     * 后端转化
     */
    void convertEvent(Order order, Integer convType, JSONObject convExt);

    /**
     * 插件曝光
     */
    void pluginExposure(NhStatReq req);
}
