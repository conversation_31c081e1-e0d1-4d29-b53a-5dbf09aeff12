package com.ruoyi.system.service.sms;

import com.ruoyi.system.entity.sms.SmsTemplateEntity;

import java.util.List;

/**
 * 短信模版表 Service
 *
 * <AUTHOR>
 * @date 2022-12-5 10:09:01
 */
public interface SmsTemplateService {
    /**
     * 新增记录
     */
    Boolean insert(SmsTemplateEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(SmsTemplateEntity entity);

    /**
     * 根据id获取
     */
    SmsTemplateEntity selectById(Long id);

    /**
     * 查询所有短信模版
     * @return
     */
    List<SmsTemplateEntity> selectAllList();

    /**
     * 批量新增更新
     * @param entities
     * @return
     */
    int batchInsertOrUpdate(List<SmsTemplateEntity> entities);


    /**
     * 根据渠道和模版id查询短信内容
     * @param type 渠道
     * @see com.ruoyi.common.enums.SmsChannelEnum
     * @param tpId 模版id
     * @return
     */
    SmsTemplateEntity selectByTypeAndTpId(Integer type,Long tpId);

    /**
     * 根据短信内容
     * @param content
     * @return
     */
    List<SmsTemplateEntity> selectListByContent(String content);

}
