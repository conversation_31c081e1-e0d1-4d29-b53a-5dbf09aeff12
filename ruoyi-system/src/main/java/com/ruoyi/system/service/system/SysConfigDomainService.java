package com.ruoyi.system.service.system;

import com.ruoyi.system.entity.system.SysConfigDomainEntity;

/**
 * 参数配置表域名为主查询配置信息 Service
 *
 * <AUTHOR>
 * @date 2023-6-7 16:25:42
 */
public interface SysConfigDomainService {
    /**
     * 新增记录
     */
    Boolean insert(SysConfigDomainEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Integer id);

    /**
     * 根据id更新
     */
    Boolean updateById(SysConfigDomainEntity entity);

    /**
     * 根据id获取
     */
    SysConfigDomainEntity selectById(Integer id);
    SysConfigDomainEntity selectByKey(String configKey);


}
