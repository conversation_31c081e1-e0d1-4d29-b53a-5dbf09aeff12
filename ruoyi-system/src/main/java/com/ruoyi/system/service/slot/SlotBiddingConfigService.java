package com.ruoyi.system.service.slot;

import com.ruoyi.system.entity.slot.SlotBiddingConfigEntity;

/**
 * 广告位投流配置表 Service
 *
 * <AUTHOR>
 * @date 2023-9-7 11:44:30
 */
public interface SlotBiddingConfigService {

    /**
     * 新增记录
     */
    int insert(SlotBiddingConfigEntity entity);

    /**
     * 根据id更新
     */
    int updateById(SlotBiddingConfigEntity entity);

    /**
     * 根据id获取
     */
    SlotBiddingConfigEntity selectById(Long id);

    /**
     * 根据广告位ID获取
     */
    SlotBiddingConfigEntity selectBySlotId(Long slotId);
}
