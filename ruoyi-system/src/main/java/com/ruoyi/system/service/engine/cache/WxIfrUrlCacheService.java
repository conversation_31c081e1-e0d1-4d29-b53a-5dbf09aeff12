package com.ruoyi.system.service.engine.cache;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.common.WhitelistService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 微信防封链接缓存服务
 *
 * <AUTHOR>
 * @date 2023/8/22
 */
@Slf4j
@Service
public class WxIfrUrlCacheService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private WhitelistService whitelistService;

    /**
     * 短链缓存
     */
    private final LoadingCache<String, String> WX_IFR_URL_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) {
                    return StringUtils.defaultString(getIfrUrlByHttp(key));
                }

                @Override
                public ListenableFuture<String> reload(String key, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 获取微信防封链接
     */
    public String getWxIfrUrl(String lpKey) {
        String ifrUrl = null;
        lpKey = StringUtils.defaultString(lpKey, "1");
        // 优先获取本地缓存
        try {
            ifrUrl = WX_IFR_URL_CACHE.get(lpKey);
        } catch (Exception e) {
            log.error("getWxIfrUrlByCache error", e);
        }
        // 降级获取Redis缓存
        if (StringUtils.isBlank(ifrUrl)) {
            try {
                ifrUrl = redisCache.getCacheObject(EngineRedisKeyFactory.K092.join(lpKey));
            } catch (Exception e) {
                log.error("getWxIfrUrlByRedis error", e);
            }
        }
        try {
            // 兜底获取白名单列表
            if (StringUtils.isBlank(ifrUrl)) {
                List<String> ifrUrls = whitelistService.list(WhitelistType.IFR_URL);
                if (CollectionUtils.isEmpty(ifrUrls)) {
                    return ifrUrl;
                }
                // 随机获取一个链接
                ifrUrl = ifrUrls.get(RandomUtil.randomInt(ifrUrls.size())) + "?xuexienc=";
            }
            if (StringUtils.isNotBlank(ifrUrl)) {
                if (ifrUrl.startsWith("https://")) {
                    ifrUrl = ifrUrl.substring(8);
                } else if (ifrUrl.startsWith("http://")) {
                    ifrUrl = ifrUrl.substring(7);
                }
            }
        } catch (Exception e) {
            log.error("getWxIfrUrl error", e);
        }
        return ifrUrl;
    }

    /**
     * 清理缓存
     */
    public void invalidateCache(String key) {
        WX_IFR_URL_CACHE.invalidate(StringUtils.defaultString(key, "1"));
    }

    /**
     * 调用接口获取可用的微信防封链接
     */
    private String getIfrUrlByHttp(String lpKey) {
        String ifrUrl = null;
        try {
            // 调用接口获取可用的微信防封链接
            // 返回值格式 {"msg": "https://230822-1320237060.cos.ap-shanghai.myqcloud.com/ifrcbc4e3d8e-ae2c-413b-b48a-6dc432214510.html?xuexienc=","code": 200}
//            String resp = HttpUtil.get(StrUtil.format("http://qs-manager.nuohe.com.cn/qs/cdnos/noauth/getUrl?coName=%E8%AF%BA%E7%A6%BE&coType=land&coUniqueKey={}", lpKey));
            String resp = HttpUtil.get(StrUtil.format("http://qs-manager.ydns.com.cn/qs/cdnos/noauth/getUrl?coName=%E8%AF%BA%E7%A6%BE&coType=land&coUniqueKey={}", lpKey));
            if (StringUtils.isNotBlank(resp)) {
                JSONObject result = JSON.parseObject(resp);
                if (null != result && Objects.equals(result.getInteger("code"), 200)) {
                    ifrUrl = result.getString("msg");
                }
            }
        } catch (Exception e) {
            log.error("getIfrUrlByHttp error", e);
        }
        try {
            if (StringUtils.isNotBlank(ifrUrl)) {
                // 更新Redis缓存
                redisCache.setCacheObject(EngineRedisKeyFactory.K092.join(lpKey), ifrUrl, 1, TimeUnit.DAYS);
            } else {
                // 兜底获取Redis缓存
                return redisCache.getCacheObject(EngineRedisKeyFactory.K092.join(lpKey));
            }
        } catch (Exception e) {
            log.error("getIfrUrlByRedis error", e);
        }
        return ifrUrl;
    }
}
