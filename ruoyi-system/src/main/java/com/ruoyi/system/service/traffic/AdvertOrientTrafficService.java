package com.ruoyi.system.service.traffic;

import com.ruoyi.system.entity.traffic.AdvertOrientTrafficEntity;

import java.util.List;
import java.util.Map;

/**
 * 广告定向流量包表 Service
 *
 * <AUTHOR>
 * @date 2022-8-24 10:54:06
 */
public interface AdvertOrientTrafficService {

    /**
     * 更新广告定向流量包
     *
     * @param advertId 广告ID
     * @param orientId 广告定向配置ID
     * @param trafficPackageIds 流量包ID
     * @return 是否更新成功
     */
    Boolean update(Long advertId, Long orientId, List<Long> trafficPackageIds);

    /**
     * 根据id获取
     */
    AdvertOrientTrafficEntity selectById(Long id);

    /**
     * 统计使用流量包的广告数量
     *
     * @param trafficPackageIds 流量包ID列表
     * @return 流量包ID-广告数量映射
     */
    Map<Long, Integer> countByTrafficPackageId(List<Long> trafficPackageIds);

    /**
     * 根据流量包ID查询定向该流量包的广告ID列表
     *
     * @param trafficPackageId 流量包ID
     * @return 广告ID列表
     */
    List<Long> selectAdvertIdByTrafficPackageId(Long trafficPackageId);

    /**
     * 根据广告定向配置ID查询定向的流量包ID
     *
     * @param orientId 广告定向配置ID
     * @return 流量包ID列表
     */
    List<Long> selectTrafficPackageIdByOrientId(Long orientId);

    /**
     * 根据广告ID查询定向的流量包ID
     *
     * @param advertId 广告Id
     * @return 配置ID-流量包ID列表映射
     */
    Map<Long, List<Long>> selectTrafficPackageIdByAdvertId(Long advertId);

    /**
     * 查询广告定向的流量包
     *
     * @param advertIds 广告ID列表
     * @return 广告ID-流量包ID列表映射
     */
    Map<Long, List<Long>> selectTrafficByAdvertIds(List<Long> advertIds);

    /**
     * 查询广告配置定向的流量包
     *
     * @param orientIds 配置ID列表
     * @return 配置ID-流量包ID列表映射
     */
    Map<Long, List<Long>> selectTrafficByOrientIds(List<Long> orientIds);

    /**
     * 查询广告定向的流量包ID列表
     *
     * @param advertIds 广告ID列表
     * @return 流量包ID列表
     */
    List<Long> selectTrafficPackageIdsByAdvertIds(List<Long> advertIds);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();
}
