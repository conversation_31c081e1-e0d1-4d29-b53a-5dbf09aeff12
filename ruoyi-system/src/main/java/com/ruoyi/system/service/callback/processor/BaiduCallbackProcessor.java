package com.ruoyi.system.service.callback.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.enums.open.BaiduConfigEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.SlotCacheDto;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.BAIDU;

/**
 * 百度上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Slf4j
@Service
public class BaiduCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final Integer RETRY_TIMES = 3;
    private static final String BAIDU_OCPC_URL = "https://ocpc.baidu.com/ocpcapi/api/uploadConvertData";

    @Autowired
    private SlotCacheService slotCacheService;

    @Override
    public CallbackProcessorTypeEnum getType() {
        return BAIDU;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("bd_vid"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            Long slotId = context.getOrder().getSlotId();
            JSONObject param = context.getParam().getSlotParam();

            // 获取token参数，优先获取链接中的，否则用默认配置
            String token = StrUtil.blankToDefault(param.getString("bd_token"), BaiduConfigEnum.getTokenBySlotId(slotId));
            // 构造logidUrl，即还原投放中的广告位链接
            StringBuilder logidUrl = new StringBuilder(getSlotUrl(slotId));
            logidUrl.append(logidUrl.indexOf("?") != -1 ? "&" : "?");
            logidUrl.append(URLUtil.buildQuery(param.getInnerMap(), StandardCharsets.UTF_8));
            // 构造请求参数
            JSONObject conversionType = new JSONObject();
            conversionType.put("logidUrl", logidUrl.toString());
            // https://dev2.baidu.com/content?sceneType=0&pageId=101207&nodeId=655
            conversionType.put("newType", 3);
            JSONArray conversionTypes = new JSONArray();
            conversionTypes.add(conversionType);

            JSONObject data = new JSONObject();
            // 设置API接口回传Token
            data.put("token", token);
            // 设置API接口回传conversionTypes
            data.put("conversionTypes", conversionTypes);
            // 向百度发送数据
            String resp = sendWithRetry(data.toString());

            log.info("{}接口上报, req={}, resp={}", getType().getName(), data, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getJSONObject("header").getInteger("status"), 0)) {
                return true;
            }
            log.error("{}接口上报失败, req={}, resp={}", getType().getName(), data, resp);
            return false;
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }

    private String getSlotUrl(Long slotId) {
        SlotCacheDto slot = slotCacheService.getSlotCache(slotId);
        return null != slot ? slot.getSlotUrl() : "";
    }

    private static String sendWithRetry(String msg) {
        // 发送请求
        HttpRequest request = HttpUtil.createPost(BAIDU_OCPC_URL)
                .header("Content-Type", "application/json; charset=UTF-8")
                .body(msg);
        String res = null;
        // 添加失败重试
        int retry = RETRY_TIMES;
        for (int i = 0; i < retry; i++) {
            try {
                HttpResponse response = request.execute();
                // 检验状态码，如果成功接收数据
                int code = response.getStatus();
                if (code == HttpStatus.SC_OK) {
                    res = response.body();
                    JSONObject returnData = JSON.parseObject(res);
                    int status = returnData.getJSONObject("header").getInteger("status");
                    // status为4，代表服务端异常，可添加重试
                    if ( status != 4) {
                        return res;
                    }
                }
            } catch (Exception ignored) {
            }
        }
        return res;
    }
}
