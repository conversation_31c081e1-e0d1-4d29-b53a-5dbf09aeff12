package com.ruoyi.system.service.common.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.common.DocumentService;
import com.ruoyi.system.entity.common.DocumentEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.common.DocumentMapper;

/**
 * 文档表 Service
 *
 * <AUTHOR>
 * @date 2022-12-1 17:08:32
 */
@Service
public class DocumentServiceImpl implements DocumentService {

    @Autowired
    private DocumentMapper documentMapper;

    @Override
    public List<DocumentEntity> selectList(DocumentEntity param) {
        return documentMapper.selectList(param);
    }

    @Override
    public int insert(DocumentEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return documentMapper.insert(entity);
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return documentMapper.deleteById(id) > 0;
    }

    @Override
    public int updateById(DocumentEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return documentMapper.updateById(entity);
    }

    @Override
    public DocumentEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return documentMapper.selectById(id);
    }

    @Override
    public boolean existDuplication(Long id, String companyName, String documentName, String documentUrl) {
        return null != documentMapper.existDuplication(id, companyName, documentName, documentUrl);
    }
}
