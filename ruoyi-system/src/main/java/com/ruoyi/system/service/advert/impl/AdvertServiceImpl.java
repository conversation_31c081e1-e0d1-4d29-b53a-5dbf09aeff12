package com.ruoyi.system.service.advert.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.enums.LayerType;
import com.ruoyi.common.enums.advert.DeviceTargetType;
import com.ruoyi.common.enums.advert.MaterialStatusEnum;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.*;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertCost;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.advert.Material;
import com.ruoyi.system.entity.datashow.AdvertDayBudgetData;
import com.ruoyi.system.manager.slot.SlotAdvertManager;
import com.ruoyi.system.mapper.manager.AdvertMapper;
import com.ruoyi.system.mapper.manager.AdvertOrientationMapper;
import com.ruoyi.system.mapper.manager.MaterialMapper;
import com.ruoyi.system.message.rocketmq.message.RefreshCacheMessage;
import com.ruoyi.system.message.rocketmq.producer.CheckBudgetMqProducer;
import com.ruoyi.system.req.advert.AdvertCreateReq;
import com.ruoyi.system.service.datasource.AdvertDayBudgetDataService;
import com.ruoyi.system.service.datasource.OrientDayConsumeDataService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.engine.cache.SlotDataCacheService;
import com.ruoyi.system.service.manager.AdvertCostService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.util.LandpageUtil;
import com.ruoyi.system.vo.advert.AdvertProportionVO;
import com.ruoyi.system.vo.slot.SlotAdvertServeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.IsDefaultEnum.IS_DEFAULT;
import static com.ruoyi.common.enums.advert.AdvertStatusEnum.NORMAL;
import static com.ruoyi.common.enums.advert.AssessTypeEnum.CONVERSION;
import static com.ruoyi.common.enums.common.SwitchStatusEnum.OFF;

/**
 * 广告Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@Slf4j
@Service
public class AdvertServiceImpl implements AdvertService {

    @Autowired
    private AdvertMapper advertMapper;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private AdvertOrientationMapper advertOrientationMapper;

    @Autowired
    private MaterialMapper materialMapper;

    @Autowired
    private CheckBudgetMqProducer checkBudgetMqProducer;

    @Autowired
    private AdvertDayBudgetDataService advertDayBudgetDataService;

    @Autowired
    private AdvertCostService advertCostService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private SlotDataCacheService slotDataCacheService;

    @Autowired
    private SlotAdvertManager slotAdvertManager;

    @Autowired
    private OrientDayConsumeDataService orientDayConsumeDataService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    /**
     * 查询广告
     *
     * @param id 广告ID
     * @return 广告
     */
    @Override
    public Advert selectAdvertById(Long id) {
        if (null == id) {
            return null;
        }
        return advertMapper.selectAdvertById(id);
    }

    @Override
    public String selectAdvertNameById(Long id) {
        return Optional.ofNullable(selectAdvertById(id)).map(Advert::getAdvertName).orElse(null);
    }

    @Override
    public <R> R selectById(Long id, Function<Advert, R> mapper) {
        return Optional.ofNullable(selectAdvertById(id)).map(mapper).orElse(null);
    }

    @Override
    public <R> List<R> selectByIds(List<Long> ids, Function<Advert, R> mapper) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        Advert param = new Advert();
        param.setIds(ids);
        List<Advert> adverts = advertMapper.selectAdvertList(param);
        return adverts.stream().map(mapper).collect(Collectors.toList());
    }

    /**
     * 查询广告列表
     *
     * @param advert 广告
     * @return 广告
     */
    @Override
    public List<Advert> selectAdvertList(Advert advert) {
        // 临时处理，isInvalid=-1查询所有广告
        // 有些地方需要过滤失效广告，有些地方不用，后面优化
        if (null != advert) {
            if (Objects.equals(advert.getIsInvalid(), -1)) {
                advert.setIsInvalid(null);
            } else {
                advert.setIsInvalid(0);
            }
        }
        return advertMapper.selectAdvertList(advert);
    }

    @Override
    public Map<String, Set<Long>> getDomainAdvertMapS(List<String> domains) {
        Map<String, Set<Long>> map = domains.stream().collect(Collectors.toMap(Function.identity(), data -> new HashSet<>()));

        Advert advertParam = new Advert();
        advertParam.setLandpageUrl("land");
        List<Advert> adverts = selectAdvertList(advertParam);
        adverts.forEach(advert -> {
            String url = advert.getLandpageUrl();
            if (LandpageUtil.isQuickApp(url)) {
                url = UrlUtils.extractUrlParamsFromUrl(url).get("lp");
            }
            String domain = UrlUtils.extractDomain(url);
            if (map.containsKey(domain)) {
                map.get(domain).add(advert.getId());
            }
        });

        AdvertOrientation orientParam = new AdvertOrientation();
        orientParam.setLandpageUrl("land");
        List<AdvertOrientation> orients = advertOrientationService.selectAdvertOrientationList(orientParam);
        orients.forEach(orient -> {
            String url = orient.getLandpageUrl();
            if (LandpageUtil.isQuickApp(url)) {
                url = UrlUtils.extractUrlParamsFromUrl(url).get("lp");
            }
            String domain = UrlUtils.extractDomain(url);
            if (map.containsKey(domain)) {
                map.get(domain).add(orient.getAdvertId());
            }
        });
        return map;
    }

    @Override
    public List<Long> selectAdvertIds(Advert param) {
        List<Advert> adverts = selectAdvertList(param);
        return adverts.stream().map(Advert::getId).collect(Collectors.toList());
    }

    @Override
    public List<Long> selectAdvertIdsBySearchValue(String searchValue) {
        if (StringUtils.isBlank(searchValue)) {
            return Collections.emptyList();
        }
        return advertMapper.selectAdvertIdsBySearchValue(searchValue);
    }

    @Override
    public List<Long> selectAdvertIdsByAdvertiserId(Long advertiserId) {
        if (null == advertiserId) {
            return Collections.emptyList();
        }
        Advert advert = new Advert();
        advert.setAdvertiserId(advertiserId);
        advert.setIsInvalid(-1);
        return selectAdvertIds(advert);
    }

    @Override
    public List<Long> selectAdvertIdsByAdvertiserIds(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyList();
        }
        Advert advert = new Advert();
        advert.setAdvertiserIds(advertiserIds);
        advert.setIsInvalid(-1);
        return selectAdvertIds(advert);
    }

    @Override
    public Map<Long, Long> selectAdvertIdMapByAdvertiserIds(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }
        Advert advertParam = new Advert();
        advertParam.setAdvertiserIds(advertiserIds);
        advertParam.setIsInvalid(-1);
        List<Advert> adverts = selectAdvertList(advertParam);
        return adverts.stream().collect(Collectors.toMap(Advert::getId, Advert::getAdvertiserId, (v1, v2) -> v2));
    }

    @Override
    public Map<Long, Long> selectAdvertiserIdMapByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }
        Advert advertParam = new Advert();
        advertParam.setIds(advertIds);
        advertParam.setIsInvalid(-1);
        List<Advert> adverts = selectAdvertList(advertParam);
        return adverts.stream().collect(Collectors.toMap(Advert::getId, Advert::getAdvertiserId, (v1, v2) -> v2));
    }

    @Override
    public List<Long> selectValidAdvertIds() {
        try {
            return advertMapper.selectValidAdvertIdList();
        } catch (Exception e) {
            log.error("selectValidAdvertIds error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public Map<Long, Advert> selectAdvertMapByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        Advert param = new Advert();
        param.setIds(ids);
        List<Advert> adverts = advertMapper.selectAdvertList(param);
        return adverts.stream().collect(Collectors.toMap(Advert::getId, Function.identity(), (o, n) -> n));
    }

    @Override
    public Map<Long, String> selectAdvertNameMap(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }
        List<Advert> adverts = advertMapper.selectAdvertIdNameList(advertIds);
        return adverts.stream().collect(Collectors.toMap(Advert::getId, Advert::getAdvertName, (oldVal, newVal) -> newVal));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertAdvert(AdvertCreateReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 新增广告
        Advert advert = new Advert();
        advert.setAdvertName(req.getAdvertName());
        advert.setAdvertCategory(req.getAdvertCategory());
        advert.setThumbnailImg(req.getThumbnailImg());
        advert.setAdvertiserId(req.getAdvertiserId());
        advert.setAssessType(CONVERSION.getType());
        advert.setAssessCost(0);
        advert.setLandpageUrl(StringUtils.defaultString(req.getLandpageUrl()).trim());
        advert.setClickCallbackUrl(req.getClickCallbackUrl());
        advert.setDailyBudget(req.getDailyBudget());
        advert.setStartServingDate(req.getStartServingDate());
        advert.setStopServingDate(req.getStopServingDate());
        advert.setServingSwitch(OFF.getStatus());
        advert.setOperatorId(user.getCrmAccountId());
        advert.setOperatorName(user.getUserName());
        if (null != req.getExtInfo()) {
            advert.setExtInfo(JSON.toJSONString(req.getExtInfo()));
        }
        advertMapper.insertAdvert(advert);
        Long advertId = advert.getId();

        // 新增广告默认定向配置
        AdvertOrientation orient = new AdvertOrientation();
        orient.setAdvertId(advertId);
        orient.setOrientName("默认");
        orient.setServingSwitch(OFF.getStatus());
        orient.setLandpageUrl(StringUtils.defaultString(req.getLandpageUrl()).trim());
        orient.setChargeType(req.getChargeType());
        orient.setMilliUnitPrice(NumberUtils.defaultInt(req.getMilliUnitPrice(), NumberUtils.defaultInt(req.getUnitPrice()) * 100));
        orient.setUnitPrice(orient.getMilliUnitPrice() / 100);
        orient.setIsDefault(IS_DEFAULT.getType());
        if(CollectionUtils.isNotEmpty(req.getDeviceTargets())){
            orient.setDeviceTarget(DeviceTargetType.convertToInteger(req.getDeviceTargets()));
        }
        if(CollectionUtils.isNotEmpty(req.getFlowTargets())){
            orient.setFlowTarget(DeviceTargetType.convertToInteger(req.getFlowTargets()));
        }
        advertOrientationMapper.insertAdvertOrientation(orient);

        // 新增广告默认素材
        Material material = new Material();
        material.setAdvertId(advertId);
        material.setAdvertTitle(req.getAdvertTitle());
        material.setMaterialImg(req.getMaterialImg());
        material.setButtonText(req.getButtonText());
        material.setLayerType(LayerType.NORMAL.getType());
        material.setIsDefault(IS_DEFAULT.getType());
        material.setStatus(MaterialStatusEnum.ENABLE.getStatus());
        materialMapper.insertMaterial(material);

        return advertId;
    }

    @Override
    public int updateLandPageUrl(Long id, String landpageUrl) {
        if (StringUtils.isBlank(landpageUrl)) {
            return 0;
        }
        return advertMapper.updateLandPageUrl(id, landpageUrl.trim());
    }

    @Override
    public int updateAdvert(Advert advert) {
        int result = advertMapper.updateAdvert(advert);
        // 修改日预算数据
        updateBudgetData(advert);
        return result;
    }

    @Override
    public int updateAdvertStatus(Advert advert) {
        int result = advertMapper.updateAdvertStatus(advert);
        if (SwitchStatusEnum.isSwitchOn(advert.getServingSwitch())) {
            // 检查预算
            sendCheckBudgetMsg(advert.getId());
        }
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertCacheMsg(advert.getId());
        return result;
    }

    @Override
    public int updateSwitchByIds(List<Long> ids, Integer servingSwitch) {
        if(CollectionUtils.isEmpty(ids)){
            return 0;
        }
        return advertMapper.updateSwitchByIds(ids, servingSwitch);
    }

    @Override
    public int updateAdvertStatus(Long advertId, Integer advertStatus) {
        if (null == advertId || null == advertStatus) {
            return 0;
        }
        Advert advert = new Advert();
        advert.setId(advertId);
        advert.setAdvertStatus(advertStatus);
        int result = advertMapper.updateAdvertStatus(advert);
        if (result > 0) {
            refreshCacheService.sendRefreshAdvertCacheMsg(advertId);
        }
        return result;
    }

    @Override
    public void resetAdvertStatus(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return ;
        }
        advertMapper.resetAdvertStatus(advertIds);
        refreshCacheService.sendRefreshAdvertCacheMsg(advertIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyAdvert(Long id,Long advertiserId) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 复制广告基础信息
        Advert advert = selectAdvertById(id);
        if(Objects.isNull(advert)){
            throw new CustomException(ErrorCode.E109001);
        }
        advert.setAdvertiserId(advertiserId);
        advert.setAdvertName(advert.getAdvertName()+"-复制");
        advert.setServingSwitch(OFF.getStatus());
        advert.setOperatorId(user.getCrmAccountId());
        advert.setOperatorName(user.getUserName());
        advert.setAdvertStatus(NORMAL.getStatus()); // 复制广告重置广告状态
        advertMapper.insertAdvert(advert);
        Long advertId = advert.getId();

        // 复制广告定向配置
        // 注:业务方说只用复制默认配置
        AdvertOrientation orient = advertOrientationService.selectDefaultOrientationByAdvertId(id);
        advertOrientationService.copyAdvertOrientation(orient.getId(), advertId);

        // 复制广告素材
        List<Material> materials = materialMapper.selectMaterialListByAdvertId(id);
        materials.forEach(material -> material.setAdvertId(advertId));
        materialMapper.batchInsertMaterial(materials);

        // 复制广告结算
        AdvertCost advertCost = advertCostService.selectById(id);
        if(Objects.nonNull(advertCost)){
            advertCost.setAdvertId(advertId);
            advertCostService.insertOrUpdateAdvertCost(advertCost);
        }

        return advertId;
    }

    @Override
    public String selectLandpageUrlByAdvertId(Long advertId) {
        if (null == advertId) {
            return null;
        }
        AdvertOrientation advertOrientation = advertOrientationMapper.selectDefaultOrientationByAdvertId(advertId);
        if (null != advertOrientation && StringUtils.isNotBlank(advertOrientation.getLandpageUrl())) {
            return advertOrientation.getLandpageUrl();
        }
        Advert advert = advertMapper.selectAdvertById(advertId);
        if (null != advert) {
            return advert.getLandpageUrl();
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int invalidateAdvert(Long advertId) {
        if (null == advertId) {
            return 0;
        }
        // 删除广告
        int result = advertMapper.invalidateAdvert(advertId);
        // 删除配置
        List<Long> orientIds = advertOrientationMapper.selectIdsByAdvertId(advertId);
        orientIds.forEach(orientId -> advertOrientationMapper.deleteAdvertOrientation(orientId));
        return result;
    }

    @Override
    public int disableAdvertByAdvertiserId(Long advertiserId) {
        if (null == advertiserId) {
            return 0;
        }
        return advertMapper.disableAdvertByAdvertiserId(advertiserId);
    }

    @Override
    public int enableAdvertByAdvertiserId(Long advertiserId) {
        if (null == advertiserId) {
            return 0;
        }
        return advertMapper.enableAdvertByAdvertiserId(advertiserId);
    }

    @Override
    public void sendCheckBudgetMsg(Long advertId) {
        if (null == advertId) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setAdvertId(advertId);
        checkBudgetMqProducer.sendMsg(message);
    }

    @Override
    public void sendCheckBudgetMsgByAdvertiserId(Long advertiserId) {
        if (null == advertiserId) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            List<Long> advertIds = advertMapper.selectAdvertIdByAdvertiserIds(Collections.singletonList(advertiserId));
            for (Long advertId : advertIds) {
                sendCheckBudgetMsg(advertId);
            }
        });
    }

    @Override
    public void sendRefreshCacheMsgByAdvertiserId(Long advertiserId) {
        if (null == advertiserId) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            List<Long> advertIds = advertMapper.selectAdvertIdByAdvertiserIds(Collections.singletonList(advertiserId));
            refreshCacheService.sendRefreshAdvertCacheMsg(advertIds);
        });
    }

    @Override
    public List<Long> selectAllOpenOrCloseAdvertNameByAdvertiserId(Long advertiserId, Integer status) {
        if(NumberUtils.isNullOrLteZero(advertiserId)){
            return Collections.emptyList();
        }
        List<Advert> adverts = advertMapper.selectAllOpenOrCloseAdvertNameByAdvertiserId(advertiserId,status);
        return adverts.stream().map(Advert::getId).collect(Collectors.toList());
    }

    @Override
    public List<String> selectAllOpenAdvertNameByAdvertiserId(Long advertiserId,Integer status) {
        if(NumberUtils.isNullOrLteZero(advertiserId)){
            return Collections.emptyList();
        }
        List<Advert> adverts = advertMapper.selectAllOpenOrCloseAdvertNameByAdvertiserId(advertiserId,status);
        return adverts.stream().map(Advert::getAdvertName).collect(Collectors.toList());
    }

    @Override
    public List<AdvertProportionVO> getDirectAdvertOrder(Long slotId, List<Long> orientIds) {
        List<AdvertOrientation> orients = advertOrientationService.selectListByIds(orientIds);
        if (null == slotId || CollectionUtils.isEmpty(orients)) {
            return Collections.emptyList();
        }

        String today = DateUtil.today();
        List<Long> advertIds = ListUtils.mapToList(orients, AdvertOrientation::getAdvertId);
        Map<Long, String> advertNameMap = selectAdvertNameMap(advertIds);
        Map<Long, SlotAdvertServeVO> serveMap = slotAdvertManager.queryAdvertOrientServeMap(slotId, orientIds);
        Map<Long, Integer> consumeMap = orientDayConsumeDataService.selectOrientConsumeMap(DateUtil.beginOfDay(new Date()), orientIds);

        Map<Long, Integer> positionMap = getAdvertPositionMap(orientIds);
        List<AdvertProportionVO> adverts = orients.stream()
                    .map(orient -> {
                        AdvertProportionVO advertBO = new AdvertProportionVO();
                        advertBO.setAdvertId(orient.getAdvertId());
                        advertBO.setAdvertName(advertNameMap.get(advertBO.getAdvertId()));
                        advertBO.setOrientId(orient.getId());
                        advertBO.setOrientName(orient.getOrientName());
                        advertBO.setProportion(0);
                        // 计算排序因子:CPC*统计CVR
                        Double cvr = slotDataCacheService.getSlotAdvertCvrCache(slotId, orient.getAdvertId());
                        advertBO.setOrderFactor(orient.getUnitPrice() * cvr);
                        // 投放状态
                        SlotAdvertServeVO serve = serveMap.get(orient.getId());
                        if (null != serve) {
                            advertBO.setCanServe(serve.getCanServe());
                            advertBO.setReason(serve.getReason());
                            Integer consume = NumberUtils.defaultInt(consumeMap.get(orient.getId()));
                            if (null != orient.getDailyBudget()) {
                                if (consume + orient.getUnitPrice() > orient.getDailyBudget()) {
                                    advertBO.setCanServe(0);
                                    advertBO.setReason("配置预算不足");
                                } else if (orient.getDailyBudget() - consume < orient.getDailyBudget() / 100) {
                                    Long launchConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K080.join(today, orient.getAdvertId(), orient.getId())));
                                    if (launchConsume + orient.getUnitPrice() > orient.getDailyBudget()) {
                                        advertBO.setCanServe(0);
                                        advertBO.setReason("配置超发控制");
                                    }
                                }
                            }
                        } else {
                            advertBO.setCanServe(0);
                            advertBO.setReason("已删除");
                        }
                        return advertBO;
                    })
                    // CPC*统计CVR排序
                    .sorted((o1, o2) -> {
                        int cmp = o2.getCanServe().compareTo(o1.getCanServe());
                        if (Objects.equals(cmp, 0)) {
                            cmp = o2.getOrderFactor().compareTo(o1.getOrderFactor());
                        }
                        return Objects.equals(cmp, 0) ? positionMap.get(o1.getOrientId()).compareTo(positionMap.get(o2.getOrientId())) : cmp;
                    })
                    .collect(Collectors.toList());

        int size = (int) adverts.stream().filter(s -> Objects.equals(s.getCanServe(), 1)).count();
        if (size == 1) {
            adverts.get(0).setProportion(100);
        } else if (size > 1) {
            boolean isNewSlot = slotDataCacheService.isNewSlotCache(slotId);
            if (isNewSlot) {
                // 新广告位上线，广告位发券次数<=1000，平均分配所有流量(冷启动)
                int proportion = 100;
                int per = (int) Math.ceil(100.0 / size);
                int i = 0;
                for (; i < size - 1 && proportion >= per; i++) {
                    adverts.get(i).setProportion(per);
                    proportion -= per;
                }
                if (i < size) {
                    adverts.get(i).setProportion(proportion);
                    for (i = i + 1; i < size; i++) {
                        adverts.get(i).setProportion(0);
                    }
                }
            } else {
                if (size == 2) {
                    adverts.get(0).setProportion(80);
                    adverts.get(1).setProportion(20);
                } else {
                    adverts.get(0).setProportion(50);
                    int proportion = 50;
                    int tmpProportion = (int) Math.ceil(50.0 / (size - 1));
                    int i = 1;
                    for (; i < size - 1 && proportion >= tmpProportion; i++) {
                        adverts.get(i).setProportion(tmpProportion);
                        proportion -= tmpProportion;
                    }
                    if (i < size) {
                        adverts.get(i).setProportion(proportion);
                        for (i = i + 1; i < size; i++) {
                            adverts.get(i).setProportion(0);
                        }
                    }
                    adverts.get(size - 1).setProportion(proportion);
                }
            }
        }
        return adverts;
    }

    /**
     * 修改每日预算数据
     */
    private void updateBudgetData(Advert advert) {
        AdvertDayBudgetData param = new AdvertDayBudgetData();
        param.setCurDate(DateUtil.beginOfDay(new Date()));
        param.setAdvertId(advert.getId());
        param.setBudget(advert.getDailyBudget());
        advertDayBudgetDataService.updateBudget(param);
    }

    /**
     * 配置ID列表转成配置ID-下标映射(用于排序)
     *
     * @param orientIds 配置ID列表
     * @return 配置ID-下标映射
     */
    private Map<Long, Integer> getAdvertPositionMap(List<Long> orientIds) {
        Map<Long, Integer> positionMap = new HashMap<>(orientIds.size());
        for (int i = 0; i < orientIds.size(); i++) {
            positionMap.put(orientIds.get(i), i);
        }
        return positionMap;
    }
}
