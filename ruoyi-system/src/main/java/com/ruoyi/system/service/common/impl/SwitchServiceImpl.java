package com.ruoyi.system.service.common.impl;

import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.enums.common.SwitchType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.system.SysConfig;
import com.ruoyi.system.service.common.SwitchService;
import com.ruoyi.system.service.system.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 开关服务实现
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Service
public class SwitchServiceImpl implements SwitchService {

    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public Integer getSwitch(SwitchType type) {
        String configValue = sysConfigService.selectConfigCacheByKey(type.getKey());
        return StringUtils.isNumeric(configValue) ? Integer.valueOf(configValue) : null;
    }

    @Override
    public boolean setSwitch(SwitchType type, Integer status) {
        if (!SwitchStatusEnum.isValidStatus(status)) {
            return false;
        }

        SysConfig config = sysConfigService.selectByKey(type.getKey());
        if (null == config) {
            config = new SysConfig();
            config.setConfigName(type.getDesc());
            config.setConfigKey(type.getKey());
            config.setConfigValue(String.valueOf(status));
            config.setConfigType("N");
            return sysConfigService.insertConfig(config) > 0;
        }

        config.setConfigValue(String.valueOf(status));
        return sysConfigService.updateConfig(config) > 0;
    }

    @Override
    public boolean toggleSwitch(SwitchType type) {
        Integer status = getSwitch(type);
        if (null == status) {
            return false;
        }

        return setSwitch(type, SwitchStatusEnum.toggle(status));
    }

    @Override
    public boolean isSwitchOn(SwitchType type) {
        Integer status = getSwitch(type);
        return SwitchStatusEnum.isSwitchOn(status);
    }

    @Override
    public boolean isSwitchOff(SwitchType type) {
        return !isSwitchOn(type);
    }
}
