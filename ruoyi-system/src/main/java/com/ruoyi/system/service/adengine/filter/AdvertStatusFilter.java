package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.system.service.adengine.AdvertFilter;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import org.springframework.stereotype.Component;

import static com.ruoyi.common.enums.advert.AdvertStatusEnum.isAdvertValid;

/**
 * 广告状态过滤
 */
@Component
public class AdvertStatusFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();
        return isAdvertValid(ad.getAdvertStatus());
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.ADVERT_STATUS;
    }
}
