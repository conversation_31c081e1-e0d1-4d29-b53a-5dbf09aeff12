package com.ruoyi.system.service.engine.cache;

import com.alibaba.excel.util.StringUtils;
import com.ruoyi.system.message.rocketmq.message.RefreshCacheMessage;
import com.ruoyi.system.message.rocketmq.producer.RefreshCacheMqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 本地缓存刷新服务(发送MQ消息)
 *
 * <AUTHOR>
 * @date 2021/8/13
 */
@Service
public class RefreshCacheService {

    @Autowired
    private RefreshCacheMqProducer refreshCacheMqProducer;

    /**
     * 发送消息刷新广告位缓存
     */
    public void sendRefreshSlotCacheMsg(Long slotId) {
        if (null == slotId) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setSlotId(slotId);
        refreshCacheMqProducer.sendMsg(message);
    }

    /**
     * 发送消息刷新活动缓存
     */
    public void sendRefreshActivityCacheMsg(Long activityId) {
        if (null == activityId) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setActivityId(activityId);
        refreshCacheMqProducer.sendMsg(message);
    }

    /**
     * 发送消息刷新广告缓存
     */
    public void sendRefreshAdvertCacheMsg(Long advertId) {
        if (null == advertId) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setAdvertId(advertId);
        refreshCacheMqProducer.sendMsg(message);
    }

    /**
     * 发送消息刷新广告缓存
     */
    public void sendRefreshAdvertCacheMsg(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setAdvertIds(advertIds);
        refreshCacheMqProducer.sendMsg(message);
    }

    /**
     * 发送消息刷新广告配置缓存
     */
    public void sendRefreshAdvertOrientationCacheMsg(Long orientId) {
        if (null == orientId) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setOrientId(orientId);
        refreshCacheMqProducer.sendMsg(message);
    }

    /**
     * 发送消息刷新广告配置缓存
     */
    public void sendRefreshAdvertOrientationCacheMsg(List<Long> orientIds) {
        if (CollectionUtils.isEmpty(orientIds)) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setOrientIds(orientIds);
        refreshCacheMqProducer.sendMsg(message);
    }

    /**
     * 发送消息刷新域名缓存
     */
    public void sendRefreshDomainCacheMsg(String domain) {
        if (StringUtils.isBlank(domain)) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setDomain(domain);
        refreshCacheMqProducer.sendMsg(message);
    }

    /**
     * 发送消息刷新落地页缓存
     */
    public void sendRefreshLandpageCacheMsg(String landpageKey) {
        if (StringUtils.isBlank(landpageKey)) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setLandpageKey(landpageKey);
        refreshCacheMqProducer.sendMsg(message);
    }

    /**
     * 发送消息刷新短链缓存
     */
    public void sendRefreshShortUrlCacheMsg(Long shortUrlId) {
        if (null == shortUrlId) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setShortUrlId(shortUrlId);
        refreshCacheMqProducer.sendMsg(message);
    }

    /**
     * 发送消息刷新广告主缓存
     */
    public void sendRefreshAdvertiserCacheMsg(Long advertiserId) {
        if (null == advertiserId) {
            return;
        }
        RefreshCacheMessage message = new RefreshCacheMessage();
        message.setAdvertiserId(advertiserId);
        refreshCacheMqProducer.sendMsg(message);
    }
}
