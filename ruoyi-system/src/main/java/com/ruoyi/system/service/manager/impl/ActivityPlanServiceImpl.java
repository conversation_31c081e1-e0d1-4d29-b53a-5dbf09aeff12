package com.ruoyi.system.service.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.activity.ActivityPlan;
import com.ruoyi.system.mapper.manager.ActivityPlanMapper;
import com.ruoyi.system.req.activity.ActivityPlanReq;
import com.ruoyi.system.req.activity.ActivityPlanStatusReq;
import com.ruoyi.system.service.manager.ActivityPlanService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 活动推广计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
@Service
public class ActivityPlanServiceImpl implements ActivityPlanService {

    @Autowired
    private ActivityPlanMapper activityPlanMapper;

    /**
     * 查询活动推广计划
     *
     * @param id 活动推广计划ID
     * @return 活动推广计划
     */
    @Override
    public ActivityPlan selectActivityPlanById(Long id) {
        return activityPlanMapper.selectActivityPlanById(id);
    }

    @Override
    public ActivityPlan selectByActivityId(Long activityId) {
        return activityPlanMapper.selectByActivityId(activityId);
    }

    /**
     * 查询活动推广计划列表
     *
     * @param activityPlan 活动推广计划
     * @return 活动推广计划
     */
    @Override
    public List<ActivityPlan> selectActivityPlanList(ActivityPlan activityPlan) {
        return activityPlanMapper.selectActivityPlanList(activityPlan);
    }

    /**
     * 新增活动推广计划
     *
     * @param req 活动推广计划
     * @return 结果
     */
    @Override
    public int insertActivityPlan(ActivityPlanReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        ActivityPlan plan = new ActivityPlan();
        plan.setActivityId(req.getActivityId());
        plan.setOperatorId(user.getCrmAccountId());
        plan.setOperatorName(user.getUserName());
        return activityPlanMapper.insertActivityPlan(plan);
    }

    /**
     * 修改活动推广计划
     *
     * @param activityPlan 活动推广计划
     * @return 结果
     */
    @Override
    public int updateActivityPlan(ActivityPlan activityPlan)
    {
        return activityPlanMapper.updateActivityPlan(activityPlan);
    }

    @Override
    public int updateStatus(ActivityPlanStatusReq req) {
        ActivityPlan plan = BeanUtil.copyProperties(req, ActivityPlan.class);
        return activityPlanMapper.updateActivityPlan(plan);
    }

    @Override
    public Map<Long, ActivityPlan> selectByActivityIds(List<Long> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return Collections.emptyMap();
        }

        List<ActivityPlan> list = activityPlanMapper.selectByActivityIds(activityIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(ActivityPlan::getActivityId, Function.identity(), (oldVal, newVal) -> newVal));
    }
}
