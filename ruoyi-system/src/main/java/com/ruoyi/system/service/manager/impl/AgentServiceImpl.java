package com.ruoyi.system.service.manager.impl;

import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountRelation;
import com.ruoyi.system.mapper.manager.AccountMapper;
import com.ruoyi.system.mapper.manager.AccountRelationMapper;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.AgentService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.AGENT;
import static com.ruoyi.common.enums.account.AccountRelationType.ADVERT_AGENT;

/**
 * 代理商Service业务层处理
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Service
public class AgentServiceImpl implements AgentService {

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private AccountRelationMapper accountRelationMapper;

    @Autowired
    private AccountService accountService;

    @Override
    public Long selectAgentIdByAdvertiserId(Long advertiserId) {
        if (null == advertiserId) {
            return null;
        }
        List<AccountRelation> relations = accountRelationMapper.selectByDestAccountIds(Collections.singletonList(advertiserId), Collections.singletonList(ADVERT_AGENT.getType()));
        return CollectionUtils.isNotEmpty(relations) ? relations.get(0).getSrcAccount() : null;
    }

    @Override
    public String selectAgentNameByAdvertiserId(Long advertiserId) {
        if (null == advertiserId) {
            return null;
        }
        List<AccountRelation> relations = accountRelationMapper.selectByDestAccountIds(Collections.singletonList(advertiserId), Collections.singletonList(ADVERT_AGENT.getType()));
        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }
        Account agent = accountMapper.selectAccountById(relations.get(0).getSrcAccount());
        return Optional.ofNullable(agent).map(Account::getCompanyName).orElse(null);
    }

    @Override
    public String selectAgentNameByAgentId(Long agentId) {
        if (null == agentId) {
            return "";
        }
        Account account = accountMapper.selectAccountById(agentId);
        return Optional.ofNullable(account).map(Account::getCompanyName).orElse("");
    }

    @Override
    public Map<Long, String> selectAgentNameMap(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }

        // 查询代理关系
        List<AccountRelation> relations = accountRelationMapper.selectByDestAccountIds(advertiserIds, Collections.singletonList(ADVERT_AGENT.getType()));
        if (CollectionUtils.isEmpty(relations)) {
            return Collections.emptyMap();
        }

        // 广告主ID-代理商Id映射
        Map<Long, Long> agentIdMap = relations.stream().collect(Collectors.toMap(AccountRelation::getDestAccount, AccountRelation::getSrcAccount, (oldVal, newVal) -> newVal));
        // 查询代理商
        List<Account> agents = accountMapper.selectByIds(new ArrayList<>(agentIdMap.values()));
        Map<Long, Account> agentMap = agents.stream().collect(Collectors.toMap(Account::getId, Function.identity(), (oldVal, newVal) -> newVal));
        // 广告主ID-代理商名称映射
        Map<Long, String> agentNameMap = new HashMap<>();
        for (Long advertiserId : advertiserIds) {
            Long agentId = agentIdMap.get(advertiserId);
            if (null == agentId) {
                continue;
            }
            Account agent = agentMap.get(agentId);
            if (null != agent) {
                agentNameMap.put(advertiserId, agent.getCompanyName());
            }
        }
        return agentNameMap;
    }

    @Override
    public Map<Long, Account> selectAgentMap(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }

        // 查询代理关系
        List<AccountRelation> relations = accountRelationMapper.selectByDestAccountIds(advertiserIds, Collections.singletonList(ADVERT_AGENT.getType()));
        if (CollectionUtils.isEmpty(relations)) {
            return Collections.emptyMap();
        }

        // 广告主ID-代理商Id映射
        Map<Long, Long> agentIdMap = relations.stream().collect(Collectors.toMap(AccountRelation::getDestAccount, AccountRelation::getSrcAccount, (oldVal, newVal) -> newVal));
        // 查询代理商
        List<Account> agents = accountMapper.selectByIds(new ArrayList<>(agentIdMap.values()));
        Map<Long, Account> agentMap = agents.stream().collect(Collectors.toMap(Account::getId, Function.identity(), (oldVal, newVal) -> newVal));
        // 广告主ID-代理商名称映射
        Map<Long, Account> agentEntityMap = new HashMap<>();
        for (Long advertiserId : advertiserIds) {
            Long agentId = agentIdMap.get(advertiserId);
            if (null == agentId) {
                continue;
            }
            Account agent = agentMap.get(agentId);
            if (null != agent) {
                agentEntityMap.put(advertiserId, agent);
            }
        }
        return agentEntityMap;
    }

    @Override
    public List<Long> selectAgentIdsByName(String agentName) {
        if (StringUtils.isEmpty(agentName)) {
            return Collections.emptyList();
        }

        return accountMapper.selectIdByCompanyName(agentName, AGENT.getType());
    }

    @Override
    public List<Long> selectAdvertiserIdsByName(String agentName) {
        if (StringUtils.isBlank(agentName)) {
            return Collections.emptyList();
        }

        List<Long> agentIds = accountMapper.selectIdByCompanyName(agentName.trim(), AGENT.getType());
        if (CollectionUtils.isEmpty(agentIds)) {
            return Collections.emptyList();
        }

        // 查询代理关系
        List<AccountRelation> relations = accountRelationMapper.selectBySrcAccountIds(agentIds, Collections.singletonList(ADVERT_AGENT.getType()));
        return relations.stream().map(AccountRelation::getDestAccount).collect(Collectors.toList());
    }

    @Override
    public List<Long> selectAdvertiserIdsByAgent(Long agentId) {
        if (null == agentId) {
            return Collections.emptyList();
        }
        return selectAdvertiserIdsByAgentIds(Collections.singletonList(agentId));
    }

    @Override
    public List<Long> selectAdvertiserIdsByAgentIds(List<Long> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            return Collections.emptyList();
        }
        List<AccountRelation> relations = accountRelationMapper.selectBySrcAccountIds(agentIds, Collections.singletonList(ADVERT_AGENT.getType()));
        return relations.stream().map(AccountRelation::getDestAccount).collect(Collectors.toList());
    }

    @Override
    public List<Long> selectAdvertiserIdsByAgent(Long agentId, String agentName) {
        if (null == agentId && StringUtils.isBlank(agentName)) {
            return Collections.emptyList();
        }
        Account param = new Account();
        param.setMainType(AGENT.getType());
        param.setId(agentId);
        param.setCompanyName(agentName);
        List<Account> agents = accountMapper.selectSimpleAccountList(param);
        if (CollectionUtils.isEmpty(agents)) {
            return Collections.emptyList();
        }
        List<Long> agentIds = agents.stream().map(Account::getId).collect(Collectors.toList());

        // 查询代理关系
        List<AccountRelation> relations = accountRelationMapper.selectBySrcAccountIds(agentIds, Collections.singletonList(ADVERT_AGENT.getType()));
        return relations.stream().map(AccountRelation::getDestAccount).collect(Collectors.toList());
    }

    @Override
    public List<Long> selectAgentIdsBySearch(String agentSearch) {
        return accountService.selectIdsBySearch(agentSearch, AGENT.getType());
    }

    @Override
    public List<Account> selectTotalAgentList() {
        Account param = new Account();
        param.setMainType(AGENT.getType());
        return accountMapper.selectSimpleAccountList(param);
    }
}
