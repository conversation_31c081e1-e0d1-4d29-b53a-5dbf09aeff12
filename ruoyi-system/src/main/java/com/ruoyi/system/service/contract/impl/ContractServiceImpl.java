package com.ruoyi.system.service.contract.impl;

import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.contract.ContractService;
import com.ruoyi.system.entity.contract.ContractEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.contract.ContractMapper;

/**
 * 合同表 Service
 *
 * <AUTHOR>
 * @date 2022-11-3 17:14:28
 */
@Service
public class ContractServiceImpl implements ContractService {
    @Autowired
    private ContractMapper contractMapper;

    @Override
    public Boolean insert(ContractEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return contractMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return contractMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(ContractEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return contractMapper.updateById(entity) > 0;
    }

    @Override
    public ContractEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return contractMapper.selectById(id);
    }

    @Override
    public List<ContractEntity> selectListByAccountId(Long accountId) {
        if(NumberUtils.isNullOrLteZero(accountId)){
            return Collections.emptyList();
        }
        return contractMapper.selectListByAccountId(accountId);
    }

    @Override
    public List<ContractEntity> selectLatestContractByAccountIds(List<Long> accountIds) {
        if(CollectionUtils.isEmpty(accountIds)){
            return Collections.emptyList();
        }
        return contractMapper.selectLatestContractByAccountIds(accountIds);
    }

    @Override
    public List<Long> selectAccountIdsByLatestContractStatus(Integer status) {
        if(NumberUtils.isNullOrLteZero(status)){
            return Collections.emptyList();
        }
        return contractMapper.selectAccountIdsByLatestContractStatus(status);
    }

    @Override
    public ContractEntity selectByContractCode(String contractCode) {
        if(StringUtils.isBlank(contractCode)){
            return null;
        }
        return contractMapper.selectByContractCode(contractCode);
    }
}
