package com.ruoyi.system.service.callback.processor;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.MI;

/**
 * 小米上报处理器
 *
 * <AUTHOR>
 * @date 2023/11/1
 */
@Slf4j
@Service
public class MiCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final String CONV_URL = "https://site.e.mi.com/conversionLog";

    @Override
    public CallbackProcessorTypeEnum getType() {
        return MI;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("conversionId"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            JSONObject param = context.getParam().getSlotParam();
            Map<String, String> map = new HashMap<>();
            map.put("eventType", "form");
            map.put("conversionId", param.getString("conversionId"));
            map.put("clientTime", String.valueOf(System.currentTimeMillis()));
            map.put("logExtra", param.getString("logExtra"));
            map.put("webConversionId", param.getString("webConversionId"));
            if (null != param.getBoolean("conversion_debug")) {
                map.put("conversion_debug", param.getString("conversion_debug"));
            }
            if (null != param.getString("convType")) {
                map.put("convType", param.getString("convType"));
            }
            String url = UrlUtils.appendParams(CONV_URL, map);
            String resp = HttpUtil.createGet(url).execute().body();
            log.info("{}接口上报, url={}, resp={}", getType().getName(), url, resp);
            return true;
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
