package com.ruoyi.system.service.callback.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.landpage.LandpageLibraryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.KUAISHOU;

/**
 * 快手上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Slf4j
@Service
public class KuaishouCallbackProcessor implements CallbackProcessor, InitializingBean {

    @Autowired
    private AdvertService advertService;

    @Autowired
    private LandpageLibraryService landpageLibraryService;

    @Override
    public CallbackProcessorTypeEnum getType() {
        return KUAISHOU;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        String ksCallback = param.getString("ksCallback");
        // 因为快手和喜马拉雅参数一样，所以根据callback是否是链接判断
        return StringUtils.isNotBlank(ksCallback) && !StrUtil.startWith(param.getString("callback"), "http", true);
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            String ksCallback = context.getParam().getSlotParam().getString("ksCallback");
            String price = "19.9";
            Landpage landpage = landpageLibraryService.selectByUrl(advertService.selectLandpageUrlByAdvertId(context.getOrder().getAdvertId()));
            if (null != landpage && null != landpage.getName()) {
                if (landpage.getName().contains("29.9")) {
                    price = "29.9";
                } else if (landpage.getName().contains("29")) {
                    price = "29";
                } else if (landpage.getName().contains("19.9")) {
                    price = "19.9";
                } else if (landpage.getName().contains("19")) {
                    price = "19";
                } else if (landpage.getName().contains("39.9")) {
                    price = "39.9";
                } else if (landpage.getName().contains("39")) {
                    price = "39";
                } else if (landpage.getName().contains("49.9")) {
                    price = "49.9";
                } else if (landpage.getName().contains("49")) {
                    price = "49";
                } else if (landpage.getName().contains("59.9")) {
                    price = "59.9";
                }else if (landpage.getName().contains("59")) {
                    price = "59";
                }
            }
            String url = StrUtil.format("http://ad.partner.gifshow.com/track/activate?event_type=9&purchase_amount={}&event_time={}&callback={}",
                    price, System.currentTimeMillis(), ksCallback);
            String resp = HttpUtil.get(url);
            log.info("{}接口上报, url={}, resp={}", getType().getName(), url, resp);
            if (null != resp) {
                return true;
            }
            log.error("{}接口上报失败, url={}, resp={}", getType().getName(), url, resp);
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }
}
