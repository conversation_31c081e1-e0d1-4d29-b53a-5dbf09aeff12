package com.ruoyi.system.service.advert;

import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.advert.AdvertMauRepeatFilterEntity;

import java.util.List;

/**
 * 广告MAU去重 Service
 *
 * <AUTHOR>
 * @date 2024-11-5 11:37:22
 */
public interface AdvertMauRepeatFilterService {

    /**
     * 支付宝小程序code换openId
     * 如果传了openId就不用换了
     */
    String codeToOpenId(String code, Long consumerId, String openId);

    /**
     * 初始化Mau用户缓存
     */
    void initMauCache();

    /**
     * 广告点击后保存用户信息
     */
    void saveMau(Order order);

    /**
     * 新增记录
     */
    Boolean insert(Order order, String openId);

    /**
     * 新增记录
     */
    Boolean insert(AdvertMauRepeatFilterEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(AdvertMauRepeatFilterEntity entity);

    /**
     * 根据id获取
     */
    AdvertMauRepeatFilterEntity selectById(Long id);

    /**
     * 根据openId获取
     */
    AdvertMauRepeatFilterEntity selectByOpenId(String openId);

    /**
     * 查询列表
     */
    List<AdvertMauRepeatFilterEntity> selectList(AdvertMauRepeatFilterEntity entity);
}
