package com.ruoyi.system.service.manager.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.manager.SlotSpecMapper;
import com.ruoyi.system.entity.slot.SlotSpec;
import com.ruoyi.system.service.manager.SlotSpecService;

/**
 * 广告位规格Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Service
public class SlotSpecServiceImpl implements SlotSpecService {

    @Autowired
    private SlotSpecMapper slotSpecMapper;

    /**
     * 查询广告位规格
     *
     * @param id 广告位规格ID
     * @return 广告位规格
     */
    @Override
    public SlotSpec selectSlotSpecById(Long id) {
        return slotSpecMapper.selectSlotSpecById(id);
    }

    /**
     * 查询广告位规格列表
     *
     * @param slotSpec 广告位规格
     * @return 广告位规格
     */
    @Override
    public List<SlotSpec> selectSlotSpecList(SlotSpec slotSpec) {
        return slotSpecMapper.selectSlotSpecList(slotSpec);
    }

    @Override
    public List<SlotSpec> selectTotalSlotSpecList(SlotSpec slotSpec) {
        return slotSpecMapper.selectSimpleSlotSpecList(slotSpec);
    }

    /**
     * 新增广告位规格
     *
     * @param slotSpec 广告位规格
     * @return 结果
     */
    @Override
    public int insertSlotSpec(SlotSpec slotSpec) {
        return slotSpecMapper.insertSlotSpec(slotSpec);
    }

    /**
     * 修改广告位规格
     *
     * @param slotSpec 广告位规格
     * @return 结果
     */
    @Override
    public int updateSlotSpec(SlotSpec slotSpec) {
        return slotSpecMapper.updateSlotSpec(slotSpec);
    }
}
