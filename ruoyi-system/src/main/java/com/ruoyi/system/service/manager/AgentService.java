package com.ruoyi.system.service.manager;

import com.ruoyi.system.entity.account.Account;

import java.util.List;
import java.util.Map;

/**
 * 代理商Service接口
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
public interface AgentService {

    /**
     * 查询广告主对应的代理商ID
     *
     * @param advertiserId 广告主ID
     * @return 代理商ID
     */
    Long selectAgentIdByAdvertiserId(Long advertiserId);

    /**
     * 查询广告主对应的代理商名称
     *
     * @param advertiserId 广告主ID
     * @return 代理商名称
     */
    String selectAgentNameByAdvertiserId(Long advertiserId);

    /**
     * 查询代理商名称
     *
     * @param agentId 代理商ID
     * @return 代理商名称
     */
    String selectAgentNameByAgentId(Long agentId);

    /**
     * 查询广告主对应的代理商名称
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告主ID-代理商名称映射
     */
    Map<Long, String> selectAgentNameMap(List<Long> advertiserIds);

    /**
     * 查询广告主对应的代理商
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告主ID-代理商映射
     */
    Map<Long, Account> selectAgentMap(List<Long> advertiserIds);

    /**
     * 根据代理商名称模糊查询代理商ID列表
     *
     * @param agentName 代理商名称
     * @return 广告主ID列表
     */
    List<Long> selectAgentIdsByName(String agentName);

    /**
     * 根据代理商名称模糊查询广告主ID列表
     *
     * @param agentName 代理商名称
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIdsByName(String agentName);

    /**
     * 根据代理商查询广告主ID列表
     *
     * @param agentId 代理商ID
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIdsByAgent(Long agentId);

    /**
     * 根据代理商查询广告主ID列表
     *
     * @param agentIds 代理商ID列表
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIdsByAgentIds(List<Long> agentIds);

    /**
     * 根据代理商查询广告主ID列表
     *
     * @param agentId 代理商ID
     * @param agentName 代理商名称
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIdsByAgent(Long agentId, String agentName);

    /**
     * 根据代理商ID/名称/邮箱模糊查询代理商ID列表
     *
     * @param agentSearch 代理商ID/名称/邮箱
     * @return 代理商ID列表
     */
    List<Long> selectAgentIdsBySearch(String agentSearch);

    /**
     * 查询所有的代理商
     *
     * @return 代理商列表
     */
    List<Account> selectTotalAgentList();
}
