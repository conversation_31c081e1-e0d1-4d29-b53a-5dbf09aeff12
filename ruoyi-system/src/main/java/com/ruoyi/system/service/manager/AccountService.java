package com.ruoyi.system.service.manager;

import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.req.account.AccountRegisterReq;
import com.ruoyi.system.vo.account.AccountMangerListVO;

import java.util.List;
import java.util.Map;

/**
 * 账号Service接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface AccountService {

    /**
     * 根据ID查询账号
     *
     * @param id 账号ID
     * @return 账号
     */
    Account selectAccountById(Long id);

    /**
     * 根据ID查询账号补充信息
     *
     * @param id 账号ID
     * @return 账号补充信息
     */
    AccountExtInfo selectAccountExtInfoById(Long id);

    /**
     * 查询账号(登录用)
     *
     * @param account 参数
     * @return 账号
     */
    Account selectAccountForLogin(Account account);

    /**
     * 查询账号列表
     *
     * @param param 账号
     * @return 账号集合
     */
    List<Account> selectAccountList(Account param);

    /**
     * 查询代理商及广告主账号列表
     *
     * @param param 账号
     * @return 账号列表
     */
    List<Account> selectAgentAdvertiserList(Account param);

    /**
     * 根据邮箱或者id搜索 账号id
     * @param accountSearch 搜索条件
     * @return 账号id列表
     */
    List<Long> selectIdsByIdOrEmail(String accountSearch);

    /**
     * 根据邮箱或者id或公司搜索 账号id
     *
     * @param accountSearch 搜索条件
     * @return 账号id列表
     */
    List<Long> selectIdsByIdOrEmailOrCompanyName(String accountSearch);

    /**
     * 根据账号ID/公司名称/邮箱搜索账号ID列表
     *
     * @param accountSearch 搜索条件
     * @param mainType 账号类型
     * @return 账号ID列表
     */
    List<Long> selectIdsBySearch(String accountSearch, Integer mainType);

    /**
     * 根据邮箱或者id搜索 账号id
     * @param accountSearch 搜索条件
     * @return 账号id列表
     */
    List<Long> selectIdsByIdOrEmailAndCompany(String accountSearch,String companyName);

    /**
     * 新增账号
     *
     * @param param 账号
     * @return 账号ID
     */
    Long insertAccount(AccountRegisterReq param);

    /**
     * 修改账号
     *
     * @param account 账号
     * @return 结果
     */
    int updateAccount(Account account);

    /**
     * 修改密码
     *
     * @param id 账号ID
     * @param password 密码
     * @return 结果
     */
    int updatePassword(Long id, String password);

    /**
     * 检查公司名称是否已存在
     *
     * @param companyName 公司名称
     * @param mainType 主体类型
     * @return 是否已存在(1.是,0.否)
     */
    int checkCompanyNameUnique(String companyName, Integer mainType);

    /**
     * 检查邮箱是否已存在
     *
     * @param email 邮箱
     * @param mainType 主体类型
     * @return 是否已存在(1.是,0.否)
     */
    int checkEmailUnique(String email,Integer mainType);

    /**
     * 检查手机号是否已存在
     *
     * @param phone 手机号
     * @param mainType 主体类型
     * @return 是否已存在(1.是,0.否)
     */
    int checkPhoneUnique(String phone, Integer mainType);

    /**
     * 检查营业执照注册号是否已存在
     *
     * @param businessLicense 营业执照注册号
     * @return 是否已存在(1.是,0.否)
     */
    int checkBusinessLicenseUnique(String businessLicense);

    /**
     * 查询媒体对应的负责人
     *
     * @param accountIds 媒体账号ID列表
     * @return 媒体ID-负责人名称映射
     */
    Map<Long, String> selectManagerMap(List<Long> accountIds);

    /**
     * 根据ID批量查询账号
     *
     * @param ids 账号ID列表
     * @return 账号列表
     */
    List<Account> selectListByIds(List<Long> ids);

    /**
     * 根据ID批量查询账号
     *
     * @param ids 账号ID列表
     * @return 账号ID-账号信息映射
     */
    Map<Long, Account> selectMapByIds(List<Long> ids);

    /**
     * 根据ID批量查询账号补充信息
     *
     * @param ids 账号ID列表
     * @return 账号ID-账号补充信息映射
     */
    Map<Long, AccountExtInfo> selectExtInfoMapByIds(List<Long> ids);

    /**
     * 根据账号id列表查询邮箱账号
     * @param ids 账号id列表
     * @return <id,email>
     */
    Map<Long,String> selectEmailByIds(List<Long> ids);

    /**
     * 查询账号联系人名称映射
     *
     * @param accountIds 账号ID列表
     * @return 账号ID-联系人名称映射
     */
    Map<Long, String> selectAccountContactMap(List<Long> accountIds);

    /**
     * 查询账号的公司名称映射
     *
     * @param accountIds 账号ID列表
     * @return 账号ID-公司名称映射
     */
    Map<Long, String> selectCompanyNameMap(List<Long> accountIds);

    /**
     * 新账号注册提醒
     *
     * @param accountId 账号ID
     * @param parentId 注册账号的ID
     */
    void registerNoticeAsync(final Long accountId, Long parentId);

    /**
     * 获取负责人列表
     *
     * @return 负责人列表
     */
    AccountMangerListVO getCrmAccountList();

    /**
     * 根据邮箱获取CRM用户
     *
     * @param email 邮箱
     * @return 用户账号
     */
    Account selectCrmUserByEmail(String email);

    /**
     * 根据邮箱获取CRM用户
     *
     * @param emails 邮箱列表
     * @return 用户账号ID列表
     */
    List<Long> selectCrmUserIdsByEmail(List<String> emails);

    /**
     * 查询私域账号列表
     *
     * @return 私域账号列表
     */
    List<Account> selectPrivateAccountList();

    /**
     * 查询私域账号列表
     *
     * @return 私域账号列表
     */
    List<Long> selectPrivateAccountIds();

    /**
     * 获取所有ssp私域账号
     * @return
     */
    List<Account> selectSspPrivateAccountList();

    /**
     * 获取所有SSP私域账号ID
     */
    List<Long> selectSspPrivateAccountIds();

    /**
     * 获取所有ssp短剧账号
     * @return
     */
    List<Account> selectSspPlayletAccountList();
}
