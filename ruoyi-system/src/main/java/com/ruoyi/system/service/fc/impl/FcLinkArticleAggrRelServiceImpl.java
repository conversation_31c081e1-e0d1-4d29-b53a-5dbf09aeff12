package com.ruoyi.system.service.fc.impl;

import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkListParamBo;
import com.ruoyi.system.entity.fc.FcLinkArticleAggrRelEntity;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity;
import com.ruoyi.system.mapper.fc.FcLinkArticleAggrRelMapper;
import com.ruoyi.system.req.fc.AddArticleAggrLinkReq;
import com.ruoyi.system.service.fc.FcLinkArticleAggrRelService;
import com.ruoyi.system.service.fc.FcLinkService;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkService;
import com.ruoyi.system.service.landpage.article.ArticleService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 丰巢链接-文章聚合链接关联表(FcLinkArticleAggrRel)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-05 16:37:16
 */
@Service("fcLinkArticleAggrRelService")
public class FcLinkArticleAggrRelServiceImpl implements FcLinkArticleAggrRelService {
    @Resource
    private FcLinkArticleAggrRelMapper fcLinkArticleAggrRelMapper;
    @Resource
    private FcLinkService fcLinkService;
    @Resource
    private ArticleService articleService;

    @Override
    public Result addArticleAggrLink(AddArticleAggrLinkReq addArticleAggrLinkReq) {

        if (Objects.isNull(addArticleAggrLinkReq) || StringUtils.isEmpty(addArticleAggrLinkReq.getFcLinkKey()) || CollectionUtils.isEmpty(addArticleAggrLinkReq.getArticleAggrLinkIdList()) ) {
            return ResultBuilder.paramFail();
        }

        Set<Long> hasAddArticleAggrLinIds = fcLinkArticleAggrRelMapper.selectAggrIdsByFcLinkKey(addArticleAggrLinkReq.getFcLinkKey());
        hasAddArticleAggrLinIds.retainAll(addArticleAggrLinkReq.getArticleAggrLinkIdList());
        if (!CollectionUtils.isEmpty(hasAddArticleAggrLinIds)) {
            return ResultBuilder.fail("存在已添加的文章聚合链接");
        }


        LoginUser operator = SecurityUtils.getLoginUser();
        List<FcLinkArticleAggrRelEntity> list = new ArrayList<>();
        for (Long id : addArticleAggrLinkReq.getArticleAggrLinkIdList()) {
            FcLinkArticleAggrRelEntity fcLinkArticleAggrRelEntity = new FcLinkArticleAggrRelEntity();
            fcLinkArticleAggrRelEntity.setFcLinkKey(addArticleAggrLinkReq.getFcLinkKey());
            fcLinkArticleAggrRelEntity.setArticleAggrLinkId(id);
            fcLinkArticleAggrRelEntity.setOperatorId(operator.getCrmAccountId());
            fcLinkArticleAggrRelEntity.setCreatorId(operator.getCrmAccountId());
            list.add(fcLinkArticleAggrRelEntity);
        }
        fcLinkArticleAggrRelMapper.insertBatch(list);
        fcLinkService.deleteFcLinkAggrLinkCaChe(addArticleAggrLinkReq.getFcLinkKey());
        // 如果聚合链接有同步成功的文章，则删除丰巢文章缓存
        if ( articleService.hasFcSyncSuccessArticleByLinkIds(addArticleAggrLinkReq.getArticleAggrLinkIdList()) ) {
            fcLinkService.deleteFcLinkArticleCache(addArticleAggrLinkReq.getFcLinkKey());
        }
        return ResultBuilder.success();
    }

    @Override
    public Result removeArticleAggrLink(String fcLinkKey, Long articleAggrLinkId) {
        if ( StringUtils.isEmpty(fcLinkKey) || Objects.isNull(articleAggrLinkId) ) {
            return ResultBuilder.paramFail();
        }
        fcLinkArticleAggrRelMapper.updateStatusByFcLinkKeyAndAggrId(fcLinkKey, articleAggrLinkId, 1);
        fcLinkService.deleteFcLinkAggrLinkCaChe(fcLinkKey);
        // 如果聚合链接有同步成功的文章，则删除丰巢文章缓存
        if ( articleService.hasFcSyncSuccessArticleByLinkId(articleAggrLinkId) ) {
            fcLinkService.deleteFcLinkArticleCache(fcLinkKey);
        }
        return ResultBuilder.success();
    }



    @Override
    public Set<Long> selectAggrIdsByFcLinkKey(String fcLinkKey) {
        if ( StringUtils.isEmpty(fcLinkKey) ) {
            return new HashSet<>();
        }
        return fcLinkArticleAggrRelMapper.selectAggrIdsByFcLinkKey(fcLinkKey);
    }

    @Override
    public List<Long> selectAggrIdsByFcLinkKeys(List<String> fcLinkKeys) {
        return fcLinkArticleAggrRelMapper.selectAggrIdsByFcLinkKeys(fcLinkKeys);
    }

    @Override
    public boolean existsByArticleAggrLinkId(Long articleAggrLinkId) {
        if (Objects.isNull(articleAggrLinkId)) {
            return false;
        }
        Integer result = fcLinkArticleAggrRelMapper.existsByArticleAggrLinkId(articleAggrLinkId);
        return result != null && result > 0;
    }

    @Override
    public Set<String> selectFcLinkKeysByArticleId(Long articleId) {
        if (Objects.isNull(articleId)) {
            return new HashSet<>();
        }
        return fcLinkArticleAggrRelMapper.selectFcLinkKeysByArticleId(articleId);
    }

    @Override
    public List<String> selectFcLinkNamesByArticleAggrLinkId(Long articleAggrLinkId) {
        if (Objects.isNull(articleAggrLinkId)) {
            return new ArrayList<>();
        }
        return fcLinkArticleAggrRelMapper.selectFcLinkNamesByArticleAggrLinkId(articleAggrLinkId);
    }
}
