package com.ruoyi.system.service.common;

import com.ruoyi.system.bo.common.MobileDataBo;
import com.ruoyi.system.entity.common.MobileEntity;
import com.ruoyi.system.req.traffic.MobileListReq;

import java.util.List;

/**
 * 设备信息表 Service
 *
 * <AUTHOR>
 * @date 2022-11-10 15:23:50
 */
public interface MobileService {

    /**
     * 查询列表
     */
    List<MobileDataBo> selectList(MobileListReq req);

    /**
     * 新增设备
     *
     * @param model 手机型号
     * @param brand 手机品牌
     */
    int insert(String model, String brand);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据model更新
     */
    int updateByModel(MobileEntity entity);

    /**
     * 根据id获取
     */
    MobileEntity selectById(Long id);
}
