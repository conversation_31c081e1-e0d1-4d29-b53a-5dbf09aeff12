package com.ruoyi.system.service.manager;

import com.ruoyi.system.entity.advert.AdvertCost;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告成本Service接口
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
public interface AdvertCostService {

    /**
     * 新增/更新广告结算指标和成本
     *
     * @param advertCost 更新对象
     * @return 结果
     */
    int insertOrUpdateAdvertCost(AdvertCost advertCost);

    /**
     * 根据广告id查询当日结算成本
     *
     * @param advertId 广告Id
     * @return 当日结算成本
     */
    AdvertCost selectById(Long advertId);

    /**
     * 查询广告结算指标成本映射
     *
     * @param curDate 日期
     * @param type 类型
     * @return 广告-结算指标成本映射
     */
    Map<Long, AdvertCost> selectMapByDateAndType(Date curDate, Integer type);

    /**
     * 分页获取昨天的所有广告成本列表
     *
     * @param id 上一个广告最大id
     * @param pageSize pagesize
     * @return 广告成本列表
     */
    List<AdvertCost> selectListByYesterday(Long id,Integer pageSize);

    /**
     * 批量新增更新
     * @param advertCosts
     * @return
     */
    int batchInsertOrUpdate(List<AdvertCost> advertCosts);
}
