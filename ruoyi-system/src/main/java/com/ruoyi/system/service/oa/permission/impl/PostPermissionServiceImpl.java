package com.ruoyi.system.service.oa.permission.impl;

import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.oa.permission.PostPermissionEntity;
import com.ruoyi.system.mapper.oa.permission.PostPermissionMapper;
import com.ruoyi.system.service.oa.permission.PostPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 职位权限关联表 Service
 *
 * <AUTHOR>
 * @date 2022-11-11 10:07:18
 */
@Service
public class PostPermissionServiceImpl implements PostPermissionService {

    @Autowired
    private PostPermissionMapper postPermissionMapper;

    @Override
    public Boolean save(PostPermissionEntity entity) {
        if (null == entity) {
            return false;
        }
        return null == entity.getId() ? insert(entity) : updateById(entity);
    }

    @Override
    public Boolean insert(PostPermissionEntity entity){
        if(Objects.isNull(entity)){
            return false;
        }
        return postPermissionMapper.insert(entity)>0;
    }

    @Override
    public Boolean deleteById(Long id){
        if(Objects.isNull(id)){
            return false;
        }
        return postPermissionMapper.deleteById(id)>0;
    }

    @Override
    public Boolean updateById(PostPermissionEntity entity){
        if(Objects.isNull(entity)){
            return false;
        }
        return postPermissionMapper.updateById(entity)>0;
    }

    @Override
    public PostPermissionEntity selectById(Long id){
        if(Objects.isNull(id)){
            return null;
        }
        return postPermissionMapper.selectById(id);
    }

    @Override
    public PostPermissionEntity selectByPostIdAndSystemId(Long postId, Long systemId) {
        if(NumberUtils.isNullOrLteZero(postId) || NumberUtils.isNullOrLteZero(systemId)){
            return null;
        }
        return postPermissionMapper.selectByPostIdAndSystemId(postId, systemId);
    }
}
