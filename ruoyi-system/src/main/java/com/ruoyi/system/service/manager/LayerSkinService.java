package com.ruoyi.system.service.manager;

import com.ruoyi.system.entity.advert.LayerSkin;

import java.util.List;
import java.util.Map;

/**
 * 弹层皮肤Service接口
 * 
 * <AUTHOR>
 * @date 2021-09-26
 */
public interface LayerSkinService {

    /**
     * 查询弹层皮肤
     * 
     * @param id 弹层皮肤ID
     * @return 弹层皮肤
     */
    LayerSkin selectLayerSkinById(Long id);

    /**
     * 查询弹层皮肤列表
     * 
     * @param layerSkin 弹层皮肤
     * @return 弹层皮肤集合
     */
    List<LayerSkin> selectLayerSkinList(LayerSkin layerSkin);

    /**
     * 查询弹层皮肤名称映射
     *
     * @return 皮肤编号-皮肤名称映射
     */
    Map<String, String> selectSkinNameMap();

    /**
     * 新增弹层皮肤
     * 
     * @param layerSkin 弹层皮肤
     * @return 结果
     */
    int insertLayerSkin(LayerSkin layerSkin);

    /**
     * 修改弹层皮肤
     * 
     * @param layerSkin 弹层皮肤
     * @return 结果
     */
    int updateLayerSkin(LayerSkin layerSkin);
}
