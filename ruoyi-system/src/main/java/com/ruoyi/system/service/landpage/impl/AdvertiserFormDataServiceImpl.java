package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.entity.landpage.AdvertiserFormDataEntity;
import com.ruoyi.system.mapper.landpage.AdvertiserFormDataMapper;
import com.ruoyi.system.service.landpage.AdvertiserFormDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 广告主日维度表单数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-15 11:35:07
 */
@Slf4j
@Service
public class AdvertiserFormDataServiceImpl implements AdvertiserFormDataService {

    @Autowired
    private AdvertiserFormDataMapper advertiserFormDataMapper;

    @Override
    public int incr(Date curDate, Long advertiserId, Integer isSuccess, Integer formPrice) {
        if (null == curDate || null == advertiserId) {
            return 0;
        }
        try {
            AdvertiserFormDataEntity data = advertiserFormDataMapper.selectBy(curDate, advertiserId);
            if (null == data) {
                data = new AdvertiserFormDataEntity();
                data.setCurDate(curDate);
                data.setAdvertiserId(advertiserId);
                advertiserFormDataMapper.insert(data);
                data = advertiserFormDataMapper.selectBy(curDate, advertiserId);
            }
            if (null != data) {
                AdvertiserFormDataEntity updateData = new AdvertiserFormDataEntity();
                updateData.setId(data.getId());
                updateData.setFormCountAdd(1);
                updateData.setSuccessFormCountAdd(Objects.equals(isSuccess, 1) ? 1 : 0);
                updateData.setConsumeAdd(formPrice);
                advertiserFormDataMapper.updateById(updateData);

            }
        } catch (Exception e) {
            log.error("广告主日维度表单数据更新异常, curDate={}, advertiserId={}, isSuccess={}, formPrice={}", curDate, advertiserId, isSuccess, formPrice, e);
        }
        return 0;
    }

    @Override
    public AdvertiserFormDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return advertiserFormDataMapper.selectById(id);
    }

    @Override
    public Map<String, AdvertiserFormDataEntity> selectMapByDateAndAdvertiserIds(Date startDate, Date endDate, List<Long> advertiserIds) {
        List<AdvertiserFormDataEntity> list = advertiserFormDataMapper.selectListByDateAndAdvertiserIds(startDate, endDate, advertiserIds);
        return list.stream().collect(Collectors.toMap(
                s -> DateUtil.formatDate(s.getCurDate()) + "_" + s.getAdvertiserId(), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public AdvertiserFormDataEntity sumFormDataByDateAndAdvertiserIds(Date startDate, Date endDate, List<Long> advertiserIds) {
        return advertiserFormDataMapper.sumFormDataByDateAndAdvertiserIds(startDate, endDate, advertiserIds);
    }
}
