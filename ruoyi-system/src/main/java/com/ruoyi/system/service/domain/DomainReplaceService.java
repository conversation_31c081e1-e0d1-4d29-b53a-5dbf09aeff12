package com.ruoyi.system.service.domain;

import com.alibaba.fastjson.JSONObject;

import javax.servlet.http.HttpServletRequest;

/**
 * 域名替换服务接口
 *
 * <AUTHOR>
 * @date 2021/9/16
 */
public interface DomainReplaceService {

    /**
     * 域名替换
     *
     * @param originUrl 原链接
     * @param targetDomain 替换的目标域名
     * @return 域名替换后的链接
     */
    String doReplaceDomain(String originUrl, String targetDomain);

    /**
     * 落地页域名替换
     *
     * @param landpageUrl 落地页链接
     * @param domainConfig 域名配置
     * @param userAgent userAgent
     * @return 域名替换后的链接
     */
    String doReplaceLandpageDomain(String landpageUrl, JSONObject domainConfig, String userAgent);

    /**
     * 活动域名替换
     *
     * @param activityUrl 活动链接
     * @param domainConfig 域名配置
     * @return 域名替换后的链接
     */
    String doReplaceActivityDomain(HttpServletRequest request, String activityUrl, JSONObject domainConfig);
}
