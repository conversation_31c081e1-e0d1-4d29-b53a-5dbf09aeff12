package com.ruoyi.system.service.app;

import com.ruoyi.system.entity.apptag.AppTagRelationEntity;

import java.util.List;

/**
 * 媒体标签关联表 Service
 *
 * <AUTHOR>
 * @date 2022-9-23 16:00:26
 */
public interface AppTagRelationService {
    /**
     * 新增记录
     */
    Boolean insert(AppTagRelationEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(AppTagRelationEntity entity);

    /**
     * 根据id获取
     */
    AppTagRelationEntity selectById(Long id);

    /**
     * 根据标签id列表查询被使用的标签id列表 ，删除标签时判断用
     * @param tagIds
     * @return
     */
    List<Long> selectTagIdsByTagIds(List<Long> tagIds);

    /**
     * 根据标签id查询使用的总数
     * @param tagId
     * @return
     */
    int countByTagId(Long tagId);

    /**
     * 根据媒体id查询标签列表
     *
     * @param appId 媒体ID
     * @return 标签ID列表
     */
    List<Long> selectTagIdsByAppId(Long appId);

    /**
     * 根据媒体id删除标签
     * @param appId
     * @return
     */
    Boolean deleteByAppId(Long appId);

    /**
     * 批量新增
     * @param entities
     * @return
     */
    Boolean batchInsert(List<AppTagRelationEntity> entities);

    /**
     * 根据父级标签名查询媒体ID列表
     *
     * @param parentTagName 父级标签名称
     * @return 媒体ID列表
     */
    List<Long> selectAppIdsByParentTagName(String parentTagName);
}
