package com.ruoyi.system.service.callback.processor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayDataDataserviceAdConversionUploadRequest;
import com.alipay.api.response.AlipayDataDataserviceAdConversionUploadResponse;
import com.ruoyi.common.config.DhProperties;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.SlotCacheDto;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.domain.DomainReplaceService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.DENGHUO;

/**
 * 灯火上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Slf4j
@Service
public class DenghuoCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final String API_URL = "https://openapi.alipay.com/gateway.do";
    private static final Map<String, AlipayClient> alipayClientMap = new HashMap<>();

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private DomainReplaceService domainReplaceService;

    @Override
    public CallbackProcessorTypeEnum getType() {
        return DENGHUO;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        String callbackParam = getCallbackParam(param);
        return StringUtils.isNotBlank(callbackParam);
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            Order order = context.getOrder();
            JSONObject param = context.getParam().getSlotParam();
            String principalId = param.getString("hu");
            String callbackParam = getCallbackParam(param);

            DhProperties.Config config = DhProperties.getConfigByPrincipalId(principalId);
            AlipayClient alipayClient = alipayClientMap.get(principalId);
            if (null == alipayClient) {
                alipayClient = new DefaultAlipayClient(API_URL, config.getAppId(), config.getPrivateKey(), "json", "UTF-8", config.getAlipayPublicKey(), "RSA2");
                alipayClientMap.put(principalId, alipayClient);
            }

            AlipayDataDataserviceAdConversionUploadRequest request = new AlipayDataDataserviceAdConversionUploadRequest();
            JSONObject property = new JSONObject();
            if (Objects.equals(config.getConversionType(), "425")) {
                // 留资表单提交
                property.put("key", "conversion_target_url");
                property.put("value", getSlotUrl(order));
            } else {
                property.put("key", "tinyapp_id");
                property.put("value", config.getAppId());
            }

            JSONArray propertyList = new JSONArray();
            propertyList.add(property);

            JSONObject conversionData = new JSONObject();
            conversionData.put("source", "COMMON_TARGET");
            conversionData.put("principal_tag", config.getPrincipalTag());
            conversionData.put("biz_no", order.getOrderId());
            if(!Objects.equals(config.getConversionType(), "425")){
                conversionData.put("conversion_id", "167390987332");
            }
            conversionData.put("conversion_type", StrUtil.blankToDefault(config.getConversionType(), "219"));
            conversionData.put("conversion_time", System.currentTimeMillis() / 1000);
            conversionData.put("uuid_type", "PID");
            conversionData.put("uuid", order.getConsumerId());
            conversionData.put("callback_ext_info", callbackParam);
            conversionData.put("property_list", propertyList);
            JSONArray conversionDataList = new JSONArray();
            conversionDataList.add(conversionData);

            JSONObject bizContent = new JSONObject();
            bizContent.put("biz_token", config.getBizToken());
            bizContent.put("conversion_data_list", conversionDataList);

            request.setBizContent(bizContent.toString());
            AlipayDataDataserviceAdConversionUploadResponse resp = alipayClient.execute(request);
            log.info("{}接口上报, req={}, resp={}", getType().getName(), bizContent, JSONObject.toJSONString(resp));
            if (null != resp && resp.isSuccess()) {
                return true;
            }
            log.error("{}接口上报失败, req={}, resp={}", getType().getName(), bizContent, JSONObject.toJSONString(resp));
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }

    private String getCallbackParam(JSONObject param) {
        String callbackParam = "";
        if (StringUtils.isNotBlank(param.getString("request"))) {
            callbackParam = redisCache.getCacheObject(EngineRedisKeyFactory.K121.join(param.getString("request")));
        }
        if (StringUtils.isBlank(callbackParam) && StringUtils.isNotBlank(param.getString("ip"))) {
            callbackParam = redisCache.getCacheObject(EngineRedisKeyFactory.K121.join(param.getString("ip")));
        }
        return callbackParam;
    }

    /**
     * 获取真实投放的广告位链接
     */
    private String getSlotUrl(Order order) {
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);
        if (null == adSnapshot) {
            return "";
        }
        String slotUrl = redisCache.getCacheObject(EngineRedisKeyFactory.K134.join(adSnapshot.getSrid()));
        slotUrl = slotUrl.substring(0,slotUrl.indexOf("&sid="))+"&sid="+order.getSlotId();
        slotUrl = slotUrl.replace("http://", "https://");
        return slotUrl;
    }
}
