package com.ruoyi.system.service.invoice.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.bo.invoice.InvoiceSumBO;
import com.ruoyi.system.bo.invoice.InvoiceSumListBO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.invoice.InvoiceService;
import com.ruoyi.system.entity.invoice.InvoiceEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.invoice.InvoiceMapper;

/**
 * 发票表 Service
 *
 * <AUTHOR>
 * @date 2022-10-20 14:33:51
 */
@Service
public class InvoiceServiceImpl implements InvoiceService {
    @Autowired
    private InvoiceMapper invoiceMapper;

    @Override
    public Boolean insert(InvoiceEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return invoiceMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return invoiceMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(InvoiceEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return invoiceMapper.updateById(entity) > 0;
    }

    @Override
    public InvoiceEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return invoiceMapper.selectById(id);
    }

    @Override
    public List<InvoiceSumBO> sumInvoice(List<Long> accountIds) {
        if(CollectionUtils.isEmpty(accountIds)){
            return Collections.emptyList();
        }
        return invoiceMapper.sumInvoice(accountIds);
    }

    @Override
    public List<InvoiceEntity> getInvoiceInfoListByAccountId(Long accountId) {
        if(NumberUtils.isNullOrLteZero(accountId)){
            return Collections.emptyList();
        }
        return invoiceMapper.getInvoiceInfoListByAccountId(accountId);
    }

    @Override
    public List<InvoiceEntity> selectListByGmtModified(Date startDate,Date endDate) {
        startDate = DateUtil.beginOfDay(startDate);
        endDate = DateUtil.endOfDay(endDate);
        return invoiceMapper.selectListByGmtModified(startDate,endDate);
    }

    @Override
    public List<InvoiceSumListBO> selectListByAccountIds(List<Long> accountIds, Integer status) {
        return invoiceMapper.selectListByAccountIds(accountIds, status);
    }
}
