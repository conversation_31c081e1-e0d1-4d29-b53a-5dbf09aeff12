package com.ruoyi.system.service.common;

import com.ruoyi.common.enums.common.BizConfigEnum;

/**
 * 业务配置服务接口
 *
 * <AUTHOR>
 * @date 2022/11/09
 */
public interface BizConfigService {

    /**
     * 获取配置Value
     *
     * @param config 配置类型
     * @return 配置Value
     */
    String getValue(BizConfigEnum config);

    /**
     * 获取指定类型的配置Value
     *
     * @param config 配置类型
     * @param vClass value类型
     * @return 配置Value
     */
    <T> T getValue(BizConfigEnum config, Class<T> vClass);
}
