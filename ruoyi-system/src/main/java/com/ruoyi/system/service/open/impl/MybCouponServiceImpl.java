package com.ruoyi.system.service.open.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.service.landpage.ExternalCouponRecordService;
import com.ruoyi.system.service.open.JtcService;
import com.ruoyi.system.service.open.MybCouponService;
import com.ruoyi.system.service.open.YtxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 蚂蚁保投放后发放优惠券Service接口
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Slf4j
@Service
public class MybCouponServiceImpl implements MybCouponService {

    @Autowired
    private JtcService jtcService;

    @Autowired
    private YtxService ytxService;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private StatService statService;

    @Autowired
    private ExternalCouponRecordService externalCouponRecordService;

    @Override
    public boolean grandCoupon(JSONObject slotParam, JSONObject pageConfig, Order order) {
        // 不同产品发放不同优惠券
        boolean isSuccess = false;
        ExternalCouponRecordEntity record = new ExternalCouponRecordEntity();
        Integer couponType = pageConfig.getInteger("productType");
        // 捷停车
        if (Objects.equals(couponType, 1)) {
            String jtcAdRequestId = slotParam.getString("jtcAdRequestId");
            String jtcCoupon = StrUtil.trim(pageConfig.getString("coupon"));
            isSuccess = jtcService.convAndGrantCoupon(order, record, jtcAdRequestId, jtcCoupon);
        }
        // 亿通行
        else if (Objects.equals(couponType, 2)) {
            String ytxCoupon = StrUtil.trim(pageConfig.getString("poolCode"));
            isSuccess = ytxService.grantCoupon(order, record, ytxCoupon);
        } else {
            log.error("蚂蚁保匹配优惠券产品失败, couponType={}, orderId={}", couponType, order.getOrderId());
        }
        // 创建优惠券发放记录
        insertCouponRecord(record, order, couponType, isSuccess);
        if (isSuccess) {
            // 优惠券发放成功后的激活埋点
            statService.convertEvent(order.getOrderId(), ConvType.REGISTER.getType());
        }
        return isSuccess;
    }

    private void insertCouponRecord(ExternalCouponRecordEntity record, Order order, Integer couponType, boolean isSuccess) {
        try {
            record.setCurDate(DateUtil.beginOfDay(new Date()));
            record.setAppId(order.getAppId());
            record.setSlotId(order.getSlotId());
            record.setAdvertiserId(advertCacheService.queryAdvertiserId(order.getAdvertId()));
            record.setAdvertId(order.getAdvertId());
            record.setOrderId(order.getOrderId());
            record.setConsumerId(order.getConsumerId());
            record.setCouponType(couponType);
            record.setCouponStatus(isSuccess ? 1 : 0);
            externalCouponRecordService.insert(record);
        } catch (Exception e) {
            log.error("insertCouponRecord error", e);
        }
    }
}
