package com.ruoyi.system.service.open.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.service.open.WeiboService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.open.WeiboTokenData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.open.WeiboConfigEnum.getClientId;

/**
 * 微博对接Service接口实现
 * 配置 {@link com.ruoyi.common.enums.open.WeiboConfigEnum}
 *
 * <AUTHOR>
 * @date 2022/05/23
 */
@Slf4j
@Service
public class WeiboServiceImpl implements WeiboService {

    private static final String REDIRECT_URI = UrlUtils.urlEncode("https://actengine.ydns.cn/open/oauth2/weibo/callback");
    private static final String TEMPLATE_URL_ACCESS_TOKEN = "https://api.biz.weibo.com/oauth/token?client_id={}&grant_type=authorization_code&redirect_uri={}&code={}";
    private static final String TEMPLATE_URL_REFRESH_TOKEN = "https://api.biz.weibo.com/oauth/token?client_id={}&grant_type=refresh_token&refresh_token={}";
    private static final String TEMPLATE_URL_AUTH = "https://api.biz.weibo.com/oauth/authorize?client_id={}&redirect_uri={}&response_type=code&state={}&scope=ads_read";
    private static final String TEMPLATE_URL_ACTIVATE = "https://api.biz.weibo.com/v3/track/activate";

    @Autowired
    private RedisCache redisCache;

    @Override
    public void getAndCacheToken(String code, String clientId) {
        if (StringUtils.isBlank(code) || StringUtils.isBlank(clientId)) {
            return;
        }
        String resp = HttpUtil.get(StrUtil.format(TEMPLATE_URL_ACCESS_TOKEN, clientId, REDIRECT_URI, code));
        log.info("微博获取token, clientId={}, code={}, resp={}", clientId, code, resp);
        WeiboTokenData result = JSON.parseObject(resp, WeiboTokenData.class);
        if (null == result || StringUtils.isBlank(result.getAccessToken())) {
            return;
        }
        // 缓存token信息
        redisCache.setCacheObject(CrmRedisKeyFactory.K021.join("access_token", clientId), result.getAccessToken(), result.getExpiresIn(), TimeUnit.SECONDS);
        redisCache.setCacheObject(CrmRedisKeyFactory.K021.join("refresh_token", clientId), result.getRefreshToken(), result.getRefreshExpiresIn(), TimeUnit.SECONDS);
    }

    @Override
    public String getAccessToken(Long slotId) {
        String accessToken = redisCache.getCacheObject(getAccessTokenKey(slotId));
        if (StringUtils.isBlank(accessToken)) {
            accessToken = refreshToken(slotId);
        }
        return accessToken;
    }

    @Override
    public String refreshToken(Long slotId) {
        checkRefreshExpires(slotId);
        String refreshToken = redisCache.getCacheObject(getRefreshTokenKey(slotId));
        if (StringUtils.isBlank(refreshToken)) {
            return null;
        }
        String resp = HttpUtil.get(StrUtil.format(TEMPLATE_URL_REFRESH_TOKEN, getClientId(slotId), refreshToken));
        log.info("微博刷新token, refreshToken={}, resp={}", refreshToken, resp);

        WeiboTokenData result = JSON.parseObject(resp, WeiboTokenData.class);
        if (null == result || StringUtils.isBlank(result.getAccessToken())) {
            return null;
        }
        redisCache.setCacheObject(getAccessTokenKey(slotId), result.getAccessToken(), result.getExpiresIn(), TimeUnit.SECONDS);
        return result.getAccessToken();
    }

    @Override
    public String behaviorUpload(String orderId, Long slotId, String markId) {
        if (StringUtils.isBlank(orderId)) {
            return "";
        }
        String accessToken = getAccessToken(slotId);
        if (StringUtils.isBlank(accessToken)) {
            log.error("微博行为数据上报失败，accessToken为空, orderId={}", orderId);
            return "";
        }

        JSONObject param = new JSONObject();
        param.put("time", System.currentTimeMillis());
        param.put("mark_id", markId);
        param.put("behavior", "1007");

        return HttpUtil.createGet(TEMPLATE_URL_ACTIVATE)
                .header("Authorization", "Bearer " + accessToken)
                .header("accept", "application/json,application/text+gw2.0")
                .form(param)
                .execute().body();
    }

    /**
     * 检查refreshToken过期时间
     */
    private void checkRefreshExpires(Long slotId) {
        GlobalThreadPool.executorService.submit(() -> {
            Long ttl = redisCache.getExpire(getRefreshTokenKey(slotId));
            if (null == ttl || ttl < 86400) {
                String clientId = getClientId(slotId);
                String sbr = "微博Token将在24小时内失效，请重新授权登录\n" +
                        "\n地址: " + StrUtil.format(TEMPLATE_URL_AUTH, clientId, REDIRECT_URI, clientId);
                DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sbr);
            }
        });
    }

    /**
     * 根据广告位ID获取accessToken的缓存Key
     */
    private String getAccessTokenKey(Long slotId) {
        return CrmRedisKeyFactory.K021.join("access_token", getClientId(slotId));
    }

    /**
     * 根据广告位ID获取refreshToken的缓存Key
     */
    private String getRefreshTokenKey(Long slotId) {
        return CrmRedisKeyFactory.K021.join("refresh_token", getClientId(slotId));
    }
}
