package com.ruoyi.system.service.manager;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.entity.activity.ActivitySkin;

/**
 * 活动皮肤Service接口
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
public interface ActivitySkinService {

    /**
     * 查询活动皮肤
     *
     * @param id 活动皮肤ID
     * @return 活动皮肤
     */
    ActivitySkin selectActivitySkinById(Long id);

    /**
     * 查询活动皮肤列表
     *
     * @param activitySkin 活动皮肤
     * @return 活动皮肤集合
     */
    List<ActivitySkin> selectActivitySkinList(ActivitySkin activitySkin);

    /**
     * 新增活动皮肤
     *
     * @param activitySkin 活动皮肤
     * @return 结果
     */
    int insertActivitySkin(ActivitySkin activitySkin);

    /**
     * 修改活动皮肤
     *
     * @param activitySkin 活动皮肤
     * @return 结果
     */
    int updateActivitySkin(ActivitySkin activitySkin);

    /**
     * 查询活动皮肤列表
     *
     * @return 活动皮肤列表
     */
    List<ActivitySkin> selectTotalActivitySkinList();

    /**
     * 查询活动皮肤名称映射
     *
     * @return 皮肤标识-皮肤名称映射
     */
    Map<String, String> selectSkinNameMap();

    /**
     * 查询活动皮肤映射
     *
     * @return 皮肤标识-皮肤映射
     */
    Map<String, ActivitySkin> selectSkinMap();

    /**
     * 查询活动皮肤
     *
     * @param skinCode 皮肤标识
     * @return 活动皮肤
     */
    ActivitySkin selectBySkinCode(String skinCode);

    /**
     * 查询活动皮肤名称
     *
     * @param skinCode 皮肤标识
     * @return 皮肤名称
     */
    String selectSkinNameBySkinCode(String skinCode);
}
