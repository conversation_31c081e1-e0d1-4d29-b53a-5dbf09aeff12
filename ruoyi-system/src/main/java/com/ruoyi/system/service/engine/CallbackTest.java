package com.ruoyi.system.service.engine;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class CallbackTest {

    private static final String ACCESS_KEY = "6177ff4eca774573b29e575ab11ad72d";
    private static final String SECRET_KEY = "64b80f3bcb8b4031968db31d73135f72";

    public static void main(String[] args) {
        // 测试参数
        String orderNo = "1060040004";
        Integer status = 2;
        Long submitTime = System.currentTimeMillis();

//        {"orderNo":"","submitTime":,"secretKey":"","accessKey":"","sign":"f5f7197199166e4cae2909b28db446fb","status":1,"timestamp":}
//        {"orderNo":"686833321975","submitTime":1669967406019,"secretKey":"a8b8761649324508bc1300297e1adcb5","accessKey":"2ad0799db278488e89a578d5b94c3140","sign":"f5f7197199166e4cae2909b28db446fb","status":1,"timestamp":1669967406019}
        callback(orderNo, status, submitTime, null);
    }

    private static void callback(String orderNo, Integer status, Long submitTime, JSONObject ext) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderNo", orderNo);
        param.put("accessKey", ACCESS_KEY);
        param.put("secretKey", SECRET_KEY);
        param.put("status", String.valueOf(status));
        param.put("submitTime", submitTime);
        param.put("timestamp", submitTime);

        // 生成签名
        List<String> arr = param.values().stream().map(Object::toString).sorted(String::compareTo).collect(Collectors.toList());
        String sign = SecureUtil.md5(String.join("", arr));

        System.out.println(Joiner.on("").join(arr));
        System.out.println(sign);
        param.put("sign", sign);

        param.remove("secretKey");

        // 业务信息
//        JSONObject ext2 = new JSONObject();
//        ext2.put("phone", "1575781111");
//        ext2.put("price", "222");
//        param.put("ext", ext2);

        System.out.println(JSON.toJSONString(param));

        String resp = HttpUtil.post("http://127.0.0.1:8778/open/advertiser/callback", JSON.toJSONString(param));
        System.out.println(resp);
    }
}