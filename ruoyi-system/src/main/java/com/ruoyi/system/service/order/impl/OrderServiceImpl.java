package com.ruoyi.system.service.order.impl;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.mapper.order.OrderMapper;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.service.common.IdWorkerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.common.IdWorkerType.ORDER_ID;

/**
 * 订单服务实现
 *
 * <AUTHOR>
 * @date 2021/8/18
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Value("${tb.sharding.count.order}")
    private Integer tbCount;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private IdWorkerService idWorkerService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public String generateOrderId(Long consumerId) {
        if (null == consumerId) {
            return StringUtils.EMPTY;
        }

        // orderId生成规则: 分布式ID自增 + 表后缀
        return String.valueOf(idWorkerService.getNextID(ORDER_ID) * 10000 + getTbSuffix(consumerId));
    }

    @Override
    public List<Order> selectByConsumerIdAndDate(Long consumerId, Date date) {
        if (null == consumerId) {
            return Collections.emptyList();
        }

        Order param = new Order();
        param.setConsumerId(consumerId);
        param.setGmtCreate(date);
        param.setTbSuffix(String.format("%04d", getTbSuffix(consumerId)));
        return orderMapper.selectByConsumerIdAndDate(param);
    }

    @Override
    public Order selectByOrderId(String orderId) {
        if (!isOrderIdValid(orderId)) {
            return null;
        }

        // 查询缓存
        Order order = getOrderCache(orderId);
        if (null != order) {
            return order;
        }

        // 查询数据库
        order = selectByOrderIdNoCache(orderId);
        if (null != order) {
            // 更新缓存
            cacheOrder(order);
        }
        return order;
    }

    @Override
    public boolean createOrder(Order param) {
        if (!isOrderIdValid(param.getOrderId())) {
            return false;
        }

        param.setTbSuffix(getTbSuffix(param.getOrderId()));
        int result = orderMapper.insertOrder(param);
        refreshOrderCache(param.getOrderId());
        return result > 0;
    }

    @Override
    public boolean updateOrder(Order param) {
        if (!isOrderIdValid(param.getOrderId())) {
            return false;
        }

        param.setTbSuffix(getTbSuffix(param.getOrderId()));
        int result = orderMapper.updateOrder(param);
        refreshOrderCache(param.getOrderId());
        return result > 0;
    }

    /**
     * 查询订单缓存
     *
     * @param orderId 订单号
     * @return 订单缓存
     */
    private Order getOrderCache(String orderId) {
        if (null == orderId) {
            return null;
        }

        try {
            String value = redisCache.getCacheObject(EngineRedisKeyFactory.K008.join(orderId));
            if (StringUtils.isNotBlank(value)) {
                return JSON.parseObject(value, Order.class);
            }
        } catch (Exception e) {
            log.error("查询订单缓存异常, orderId={}", orderId, e);
        }
        return null;
    }

    /**
     * 缓存订单
     *
     * @param order 订单
     */
    @Override
    public void cacheOrder(Order order) {
        if (null == order || null == order.getOrderId()) {
            return;
        }
        try {
            redisCache.setCacheObject(EngineRedisKeyFactory.K008.join(order.getOrderId()), JSON.toJSONString(order), 3, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("缓存订单异常, orderId={}", order.getOrderId());
        }
    }

    @Override
    public boolean isFirstOrderIdByConsumerIdAndDate(Long consumerId, Date date, String orderId) {
        if (null == consumerId || null == date || StringUtils.isBlank(orderId)) {
            return false;
        }

        Order param = new Order();
        param.setConsumerId(consumerId);
        param.setGmtCreate(date);
        param.setTbSuffix(String.format("%04d", getTbSuffix(consumerId)));
        String firstOrderId = orderMapper.selectFirstOrderIdByConsumerIdAndDate(param);
        return Objects.equals(firstOrderId, orderId);
    }

    /**
     * 刷新订单缓存
     *
     * @param orderId 订单号
     */
    private void refreshOrderCache(String orderId) {
        try {
            Order order = selectByOrderIdNoCache(orderId);
            if (null != order) {
                cacheOrder(order);
            }
        } catch (Exception e) {
            log.error("刷新订单缓存异常, orderId={}", orderId);
        }
    }

    /**
     * 查询订单信息(非缓存)
     *
     * @param orderId 订单号
     * @return 订单信息
     */
    private Order selectByOrderIdNoCache(String orderId) {
        if (!isOrderIdValid(orderId)) {
            return null;
        }

        Order param = new Order();
        param.setOrderId(orderId);
        param.setTbSuffix(getTbSuffix(orderId));
        return orderMapper.selectByOrderId(param);
    }

    /**
     * 获取用户对应的表后缀
     * 规则: consumerId % tbCount
     *
     * @param consumerId 用户ID
     * @return 表后缀
     */
    private int getTbSuffix(Long consumerId) {
        return (int) (consumerId % tbCount);
    }

    /**
     * 获取订单号对应的表后缀
     * 规则: 订单后四位
     *
     * @param orderId 订单号
     * @return 表后缀
     */
    private String getTbSuffix(String orderId) {
        if (orderId.length() < 4) {
            return "0000";
        }
        return orderId.substring(orderId.length() - 4);
    }

    /**
     * 判断是否是有效的订单号
     *
     * @param orderId 订单号
     * @return true.是,false.否
     */
    private boolean isOrderIdValid(String orderId) {
        return StringUtils.isNumeric(orderId) && orderId.length() >= 4 && Integer.parseInt(getTbSuffix(orderId)) < tbCount;
    }
}
