package com.ruoyi.system.service.wx;

import com.ruoyi.system.entity.pay.WxPayOrderEntity;

import java.util.Map;

/**
 * 微信支付订单表 Service
 *
 * <AUTHOR>
 * @date 2023-1-29 17:38:06
 */
public interface WxPayOrderService {

    /**
     * 新增记录
     */
    Boolean insert(WxPayOrderEntity entity);

    /**
     * 支付完成
     *
     * @param param 参数
     */
    void finishPay(Map<String, String> param);

    /**
     * 根据支付订单号获取
     */
    WxPayOrderEntity selectByOutTradeNo(String outTradeNo);
}
