package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.common.enums.advert.ServingHourEnum;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.service.adengine.AdvertFilter;
import org.springframework.stereotype.Component;

/**
 * 投放时段过滤
 */
@Component
public class ServingHourFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();
        // 投放时段判断
        return ServingHourEnum.contains(ad.getServingHour(), context.getHour());
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.SERVING_HOUR;
    }
}
