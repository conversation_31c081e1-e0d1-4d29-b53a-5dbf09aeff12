package com.ruoyi.system.service.common.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.common.MobileDataBo;
import com.ruoyi.system.req.traffic.MobileListReq;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.common.MobileService;
import com.ruoyi.system.entity.common.MobileEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.common.MobileMapper;

/**
 * 设备信息表 Service
 *
 * <AUTHOR>
 * @date 2022-11-10 15:23:50
 */
@Service
public class MobileServiceImpl implements MobileService {

    @Autowired
    private MobileMapper mobileMapper;

    @Override
    public List<MobileDataBo> selectList(MobileListReq req) {
        req.setModel(StrUtil.trim(req.getModel()));
        return mobileMapper.selectList(req);
    }

    @Override
    public int insert(String model, String brand) {
        if (StringUtils.isBlank(model)) {
            return 0;
        }
        MobileEntity mobile = new MobileEntity();
        mobile.setModel(model);
        mobile.setBrand(brand);
        return mobileMapper.insert(mobile);
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return mobileMapper.deleteById(id) > 0;
    }

    @Override
    public int updateByModel(MobileEntity entity) {
        if (StringUtils.isBlank(entity.getModel()) || StringUtils.isBlank(entity.getBrand())) {
            return 0;
        }
        return mobileMapper.updateByModel(entity);
    }

    @Override
    public MobileEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return mobileMapper.selectById(id);
    }
}
