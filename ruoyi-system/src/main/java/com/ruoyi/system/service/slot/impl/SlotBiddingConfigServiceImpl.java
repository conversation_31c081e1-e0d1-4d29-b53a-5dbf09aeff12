package com.ruoyi.system.service.slot.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.slot.SlotBiddingConfigService;
import com.ruoyi.system.entity.slot.SlotBiddingConfigEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.slot.SlotBiddingConfigMapper;

/**
 * 广告位投流配置表 Service
 *
 * <AUTHOR>
 * @date 2023-9-7 11:44:30
 */
@Service
public class SlotBiddingConfigServiceImpl implements SlotBiddingConfigService {

    @Autowired
    private SlotBiddingConfigMapper slotBiddingConfigMapper;

    @Override
    public int insert(SlotBiddingConfigEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return slotBiddingConfigMapper.insert(entity);
    }

    @Override
    public int updateById(SlotBiddingConfigEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return slotBiddingConfigMapper.updateById(entity);
    }

    @Override
    public SlotBiddingConfigEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return slotBiddingConfigMapper.selectById(id);
    }

    @Override
    public SlotBiddingConfigEntity selectBySlotId(Long slotId) {
        if (Objects.isNull(slotId)) {
            return null;
        }
        return slotBiddingConfigMapper.selectBySlotId(slotId);
    }
}
