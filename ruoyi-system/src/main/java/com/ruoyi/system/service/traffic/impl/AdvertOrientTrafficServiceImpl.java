package com.ruoyi.system.service.traffic.impl;

import com.ruoyi.system.bo.traffic.TrafficPackageAdvertCountBo;
import com.ruoyi.system.entity.traffic.AdvertOrientTrafficEntity;
import com.ruoyi.system.mapper.traffic.AdvertOrientTrafficMapper;
import com.ruoyi.system.service.traffic.AdvertOrientTrafficService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 广告定向流量包表 Service
 *
 * <AUTHOR>
 * @date 2022-8-24 10:54:06
 */
@Service
public class AdvertOrientTrafficServiceImpl implements AdvertOrientTrafficService {

    @Autowired
    private AdvertOrientTrafficMapper advertOrientTrafficMapper;

    @Override
    public Boolean update(Long advertId, Long orientId, List<Long> trafficPackageIds) {
        if (null == advertId || null == orientId) {
            return false;
        }
        advertOrientTrafficMapper.deleteByOrientId(orientId);
        if (CollectionUtils.isNotEmpty(trafficPackageIds)) {
            List<AdvertOrientTrafficEntity> entities = new ArrayList<>();
            trafficPackageIds.forEach(trafficPackageId -> {
                AdvertOrientTrafficEntity entity = new AdvertOrientTrafficEntity();
                entity.setAdvertId(advertId);
                entity.setOrientId(orientId);
                entity.setTrafficPackageId(trafficPackageId);
                entities.add(entity);
            });
            advertOrientTrafficMapper.batchInsert(entities);
        }
        return true;
    }

    @Override
    public AdvertOrientTrafficEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return advertOrientTrafficMapper.selectById(id);
    }

    @Override
    public Map<Long, Integer> countByTrafficPackageId(List<Long> trafficPackageIds) {
        if (CollectionUtils.isEmpty(trafficPackageIds)) {
            return Collections.emptyMap();
        }
        List<TrafficPackageAdvertCountBo> list = advertOrientTrafficMapper.countByTrafficPackageId(trafficPackageIds);
        return list.stream().collect(Collectors.toMap(TrafficPackageAdvertCountBo::getTrafficPackageId, TrafficPackageAdvertCountBo::getAdvertCount, (v1, v2) -> v2));
    }

    @Override
    public List<Long> selectAdvertIdByTrafficPackageId(Long trafficPackageId) {
        if (null == trafficPackageId) {
            return Collections.emptyList();
        }
        return advertOrientTrafficMapper.selectAdvertIdByTrafficPackageId(trafficPackageId);
    }

    @Override
    public List<Long> selectTrafficPackageIdByOrientId(Long orientId) {
        if (null == orientId) {
            return Collections.emptyList();
        }
        return advertOrientTrafficMapper.selectTrafficPackageIdByOrientId(orientId);
    }

    @Override
    public Map<Long, List<Long>> selectTrafficPackageIdByAdvertId(Long advertId) {
        if (null == advertId) {
            return Collections.emptyMap();
        }
        List<AdvertOrientTrafficEntity> list = advertOrientTrafficMapper.selectByAdvertIds(Collections.singletonList(advertId));
        return list.stream().collect(Collectors.groupingBy(AdvertOrientTrafficEntity::getOrientId, Collectors.mapping(AdvertOrientTrafficEntity::getTrafficPackageId, Collectors.toList())));
    }

    @Override
    public Map<Long, List<Long>> selectTrafficByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }
        List<AdvertOrientTrafficEntity> list = advertOrientTrafficMapper.selectByAdvertIds(advertIds);
        return list.stream().collect(Collectors.groupingBy(AdvertOrientTrafficEntity::getAdvertId,Collectors.mapping(AdvertOrientTrafficEntity::getTrafficPackageId, Collectors.toList())));
    }

    @Override
    public Map<Long, List<Long>> selectTrafficByOrientIds(List<Long> orientIds) {
        if (CollectionUtils.isEmpty(orientIds)) {
            return Collections.emptyMap();
        }
        List<AdvertOrientTrafficEntity> list = advertOrientTrafficMapper.selectByOrientIds(orientIds);
        return list.stream().collect(Collectors.groupingBy(AdvertOrientTrafficEntity::getOrientId, Collectors.mapping(AdvertOrientTrafficEntity::getTrafficPackageId, Collectors.toList())));
    }

    @Override
    public List<Long> selectTrafficPackageIdsByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyList();
        }
        List<AdvertOrientTrafficEntity> list = advertOrientTrafficMapper.selectByAdvertIds(advertIds);
        return list.stream().map(AdvertOrientTrafficEntity::getTrafficPackageId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<Long> selectTotalAdvertIds() {
        return advertOrientTrafficMapper.selectTotalAdvertIds();
    }
}
