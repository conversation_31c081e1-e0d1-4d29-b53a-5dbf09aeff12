package com.ruoyi.system.service.oa.staff;

import com.ruoyi.system.entity.oa.staff.StaffInfoEntity;

import java.util.List;
import java.util.Map;

/**
 * oa员工信息表 Service
 *
 * <AUTHOR>
 * @date 2021-10-9 14:20:23
 */
public interface StaffInfoService {

    /**
     * 根据账号id获取
     *
     * @param userId 账号id
     * @return 结果
     */
    StaffInfoEntity selectByUserId(Long userId);

    /**
     * 获取员工的职位ID
     *
     * @param userId 账号id
     * @return 职位ID
     */
    Long selectPostIdByUserId(Long userId);

    /**
     * 根据部门id获取员工列表
     *
     * @param departmentId 部门id
     * @param companyId    公司id
     * @return 员工列表
     */
    List<StaffInfoEntity> selectByDepartmentId(Long companyId, Long departmentId);

    /**
     * 根据用户id列表查询员工信息
     *
     * @param userIds 用户ID列表
     * @return 员工列表
     */
    List<StaffInfoEntity> selectByUserIds(List<Long> userIds);

    /**
     * 查询职位对应的员工数量映射
     *
     * @param postIds 职位Id列表
     * @return 职位ID-员工数量映射
     */
    Map<Long, Integer> selectPostStaffNumMap(List<Long> postIds);

    /**
     * 根据用户id列表查询员工部门
     *
     * @param userIds 用户ID列表
     * @return 员工ID-部门ID映射
     */
    Map<Long, Long> selectDepartmentMapByUserIds(List<Long> userIds);

    /**
     * 根据用户id列表查询员工职位
     *
     * @param userIds 用户ID列表
     * @return 员工ID-部门ID映射
     */
    Map<Long, Long> selectPostMapByUserIds(List<Long> userIds);
    /**
     * 根据手机号查询商务信息
     * @param phone
     * @return
     */
    StaffInfoEntity selectByPhone(String phone);

    /**
     * 查询部门下的员工邮箱列表
     *
     * @param departmentIds 部门ID列表
     * @return 员工邮箱列表
     */
    List<String> selectEmailByDepartmentIds(List<Long> departmentIds);
}
