package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.advertiser.AdvertiserConsumeType;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.enums.common.MapConfigEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.*;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeBo;
import com.ruoyi.system.bo.thirdparty.IdCardAuditBo;
import com.ruoyi.system.domain.advertiser.LpCallbackAdvertiser;
import com.ruoyi.system.domain.manager.Area;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.datashow.LandpageFormRecord;
import com.ruoyi.system.entity.datashow.LandpageFormSendRecordEntity;
import com.ruoyi.system.entity.datashow.LandpageFormSendRuleEntity;
import com.ruoyi.system.manager.advertiser.AdvertiserConsumeManager;
import com.ruoyi.system.mapper.landpage.LandpageFormRecordMapper;
import com.ruoyi.system.message.rocketmq.producer.LogMqProducer;
import com.ruoyi.system.req.engine.LandPageFormReq;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.common.IdCardService;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.SlotUpService;
import com.ruoyi.system.service.engine.cache.AdvertiserCacheService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.landpage.*;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.order.OrderService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.service.thirdparty.IdCardAuditService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.BizConstants.*;
import static com.ruoyi.common.enums.InnerLogType.LANDPAGE_CLICK;
import static com.ruoyi.common.enums.advertiser.FormAdvertiserPriceType.isConsume;
import static com.ruoyi.system.util.LandpageUtil.canRetryCallback;
import static com.ruoyi.system.util.LandpageUtil.isCallbackSuccess;

/**
 * 落地页服务实现
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
@Slf4j
@Service
public class LandpageServiceImpl implements LandpageService {

    @Autowired
    private AreaService areaService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private IdCardService idCardService;

    @Autowired
    private LandpageFormRecordMapper landpageFormRecordMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DataStatService dataStatService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private LandpageFormSendRecordService landpageFormSendRecordService;

    @Autowired
    private LandpageFormSendRuleService landpageFormSendRuleService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    @Autowired
    private AdvertiserConsumeManager advertiserConsumeManager;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private LogMqProducer logMqProducer;

    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private IdCardAuditService idCardAuditService;

    @Autowired
    private AdvertiserCacheService advertiserCacheService;

    @Autowired
    private AdvertiserFormDataService advertiserFormDataService;

    @Autowired
    private SlotLandpageFormDataService slotLandpageFormDataService;

    @Autowired
    private ConvCallbackService convCallbackService;

    @Autowired
    private SlotUpService slotUpService;

    @Override
    public void formSubmit(LandPageFormReq req) {
        // 查询行政区划数据
        String lpTag = landpageCacheService.getLandpageTag(req.getLpk());
        Area area = queryArea(lpTag, req.getAreaNum());
        if (null == area) {
            throw new CustomException("无效的行政区划代码");
        }

        // 地址校验
        if (StrUtil.removeAny(req.getAddress(), area.getProvince(), area.getCity(), area.getDistrict()).length() <= 4) {
            log.info("落地页表单提交失败, 地址校验不通过, req={}", JSON.toJSONString(req));
            throw new CustomException("请输入正确的详细地址信息");
        }

        // 查询订单
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            throw new CustomException("链接已失效");
        }
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);

        // 查询身份证实名认证缓存
        IdCardAuditBo auditResult = idCardAuditService.idCardAuditCache(req.getName(), req.getIdCard());
        if (IdCardAuditBo.isNotMatch(auditResult)) {
            throw new CustomException("身份证号与姓名不匹配，请检查");
        }

        // 防刷限制
        String today = DateUtil.today();
        final String idCardMd5 = Md5Utils.hash(req.getIdCard());
        Long idCardCount = redisAtomicClient.incrBy(EngineRedisKeyFactory.K018.join(today, idCardMd5), 1, 1, TimeUnit.DAYS);
        Long phoneCount = redisAtomicClient.incrBy(EngineRedisKeyFactory.K019.join(today, req.getPhone()), 1, 1, TimeUnit.DAYS);

        if (idCardCount > 3L || phoneCount > 3L) {
            log.info("落地页表单提交失败, 防刷限制, req={}", JSON.toJSONString(req));
            return;
        }

        // 身份证实名校验
        if (null == auditResult) {
            auditResult = idCardAuditService.idCardAudit(req.getName(), req.getIdCard());
        }
        if (IdCardAuditBo.isNotMatch(auditResult)) {
            throw new CustomException("身份证号与姓名不匹配，请检查");
        }
        // 是否匹配:0.未校验,1.匹配,2.不匹配
        final int idCardMath = IdCardAuditBo.isMatch(auditResult) ? 1 : 0;
        // 实名认证接口:1.聚合,2.毫秒科技
        final int auditApiType = null != auditResult ? auditResult.getApiType() : 0;

        // 添加转化记录
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        String referer = ServletUtils.getRequest().getHeader("referer");
        GlobalThreadPool.executorService.execute(() -> {
            if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K009.join(idCardMd5), 60)) {
                log.info("落地页数据回传重复(分布式锁限制), advertId={}, orderId={}, phone={}, idCardMd5={}",
                        order.getAdvertId(), order.getOrderId(), req.getPhone(), idCardMd5);
                return;
            }

            // 是否重复表单
            Integer isRepeated = NumberUtils.defaultInt(landpageFormRecordMapper.isFormRepeated(idCardMd5, lpTag));
            boolean isRepeatedInHalfMonth = Objects.equals(landpageFormRecordMapper.isFormRepeatedInHalfMonth(idCardMd5, lpTag), 1);

            // 落地页转化记录
            LandpageFormRecord record = new LandpageFormRecord();
            record.setIdCard(idCardService.encrypt(req.getIdCard()));
            record.setIdCardMd5(idCardMd5);
            record.setAreaNum(req.getAreaNum());
            record.setProvince(area.getProvince());
            record.setCity(area.getCity());
            record.setDistrict(area.getDistrict());
            record.setAddress(req.getAddress());
            record.setPhone(req.getPhone());
            record.setName(req.getName());
            record.setIdCardAudit(idCardMath);
            record.setAuditApiType(auditApiType);
            record.setAdvertId(order.getAdvertId());
            record.setOrderId(order.getOrderId());
            record.setConsumerId(order.getConsumerId());
            record.setAppId(order.getAppId());
            record.setSlotId(order.getSlotId());
            record.setIsRepeated(isRepeated);
            record.setLandpageTag(lpTag);
            record.setBirth(IdcardUtil.getBirthDate(req.getIdCard()));
            record.setIp(StrUtil.sub(ip, 0, 32));
            record.setReferer(StrUtil.sub(referer, 0, 255));
            record.setLandpageUrl(Optional.ofNullable(adSnapshot).map(AdSnapshot::getLandpageUrl).orElse(""));
            int result = landpageFormRecordMapper.insertLandpageFormRecord(record);

            // 更新广告位表单数据
            Date now = new Date();
            if (result > 0) {
                slotLandpageFormDataService.incr(DateUtil.beginOfDay(now), record.getSlotId());
            }

            // 年龄超过65的不回传
            int age = IdcardUtil.getAgeByIdCard(req.getIdCard(), now);
            if (age > 65) {
                return;
            }
            // 未校验身份证/不匹配的不回传
            if (!Objects.equals(idCardMath, 1)) {
                return;
            }
            // 15天内身份证重复不传
            if (isRepeatedInHalfMonth) {
                log.info("落地页数据回传实时，过滤重复信息(身份证)的发送，recordId={}, orderId={}", record.getId(), record.getOrderId());
                return;
            }

            // 落地页数据回传
            callback(lpTag, record, age);
        });

        // 打印inner日志
        JSONObject logJson = new JSONObject();
        logJson.put("activityId", order.getActivityId());
        logJson.put("slotId", order.getSlotId());
        logJson.put("appId", order.getAppId());
        logJson.put("consumerId", order.getConsumerId());
        logJson.put("orientId", order.getOrientId());
        logJson.put("advertId", order.getAdvertId());
        logJson.put("materialId", order.getMaterialId());
        if (null != adSnapshot) {
            logJson.put("chargeType", adSnapshot.getChargeType());
            logJson.put("landpageUrl", adSnapshot.getLandpageUrl());
            logJson.put("originLandpageUrl", adSnapshot.getOriginLandpageUrl());
            logJson.put("srid", adSnapshot.getSrid());
            logJson.put("pluginId", adSnapshot.getPluginId());
        }
        logJson.put("ip", IpUtils.getIpAddr(ServletUtils.getRequest()));
        logJson.put("userAgent", ServletUtils.getRequest().getHeader("User-Agent"));
        logJson.put("referer", referer);
        logJson.put("orderId", order.getOrderId());
        logJson.put("idCardMd5", idCardMd5);
        logJson.put("phone", req.getPhone());
        logJson.put("name", req.getName());
        logJson.put("areaNum", req.getAreaNum());
        logJson.put("address", req.getAddress());
        logJson.put("idCard", idCardService.encrypt(req.getIdCard()));
        InnerLogUtils.log(LANDPAGE_CLICK, logJson);
        logMqProducer.sendMsg(LANDPAGE_CLICK, logJson);

        // 落地页数据
        dataStatService.handleAsync(LANDPAGE_CLICK, logJson);

        // [固定收益上报]缓存转化个数和理论消耗
        JSONObject param = callbackService.getParameterFromCache(req.getOrderId());
        slotUpService.slotUpAddCostMulti(order, param.getString("hu"));

        // 行为数据上报
        convCallbackService.handleAsync(LANDPAGE_CLICK, null, order);
    }

    @Override
    public void callback(String lpTag, LandpageFormRecord record, Integer age) {
        // 查询符合标签和地域规则的广告主列表
        List<LpCallbackAdvertiser> advertiserList = landpageFormSendRuleService.selectAdvertiserList(lpTag, record.getCity(),  record.getProvince());

        // 查询广告主余额
        List<Long> advertiserIds = advertiserList.stream().map(LpCallbackAdvertiser::getAdvertiserId).collect(Collectors.toList());
        Map<Long, Integer> advertiserBalanceMap = advertiserBalanceService.selectAdvertiserBalanceMap(advertiserIds);
        // 查询地域表单分配上限
        Long areaLimit = queryFormAreaLimit();
        // 查询地域表单每日分配上限
        Map<Long, Long> areaDayLimitMap = queryFormAreaDayLimitMap();

        // 过滤无法分单的广告主
        String today = DateUtil.today();
        Integer curHour = DateUtil.thisHour(true);
        Map<Long, String> filterReasonMap = new HashMap<>();
        Long formAdvertiserId = advertService.selectById(record.getAdvertId(), Advert::getAdvertiserId);

        // CPC结算广告主定向分单
        boolean specifyFlag = false;
        LandpageFormSendRuleEntity cpcAdvertiserRule = landpageFormSendRuleService.selectByAdvertiserId(formAdvertiserId);
        if (advertiserCacheService.isCpcAdvertiser(formAdvertiserId)) {
            LpCallbackAdvertiser lpCallbackAdvertiser = new LpCallbackAdvertiser();
            lpCallbackAdvertiser.setAdvertiserId(formAdvertiserId);
            lpCallbackAdvertiser.setOrderFactor(1);
            lpCallbackAdvertiser.setFormPrice(0);
            lpCallbackAdvertiser.setGmtCreate(new Date());

            // 查询限制条件
            if (null != cpcAdvertiserRule) {
                lpCallbackAdvertiser.setAgeMin(cpcAdvertiserRule.getAgeMin());
                lpCallbackAdvertiser.setAgeMax(cpcAdvertiserRule.getAgeMax());
                lpCallbackAdvertiser.setDailyLimit(cpcAdvertiserRule.getDailyLimit());
            }

            advertiserList = Collections.singletonList(lpCallbackAdvertiser);
            specifyFlag = true;
        }

        boolean finalSpecifyFlag = specifyFlag;
        advertiserList = advertiserList.stream().filter(advertiser -> {
            // 校验年龄
            if (null != advertiser.getAgeMin() && age < advertiser.getAgeMin()
                    || null != advertiser.getAgeMax() && age > advertiser.getAgeMax()) {
                filterReasonMap.put(advertiser.getAdvertiserId(), "年龄不符合要求");
                return false;
            }
            // 指定广告主
            if (finalSpecifyFlag) {
                return true;
            } else {
                // 不分配表单给CPC结算广告主
                if (AdvertiserConsumeType.isCPC(advertiser.getConsumeType())) {
                    return false;
                }
            }
            // 校验表单每日自动回传数量是否到上限
            Long formCount = redisAtomicClient.getLong(EngineRedisKeyFactory.K027.join(advertiser.getAdvertiserId(), today));
            if (null != advertiser.getDailyLimit() && NumberUtils.defaultLong(formCount, 0L) >= advertiser.getDailyLimit()) {
                filterReasonMap.put(advertiser.getAdvertiserId(), "每日自动回传数量达到上限");
                return false;
            }
            // 校验地域表单分配上限
            Long areaCount = redisAtomicClient.getLong(EngineRedisKeyFactory.K040.join(record.getCity(), advertiser.getAdvertiserId(), today, curHour));
            if (null != areaLimit && NumberUtils.defaultLong(areaCount) >= areaLimit) {
                filterReasonMap.put(advertiser.getAdvertiserId(), "地域时段数量达到上限");
                return false;
            }
            // 校验地域表单每日分配上限
            Long areaDayCount = redisAtomicClient.getLong(EngineRedisKeyFactory.K040.join(record.getCity(), advertiser.getAdvertiserId(), today));
            if (null != areaDayLimitMap.get(advertiser.getAdvertiserId()) && NumberUtils.defaultLong(areaDayCount) >= areaDayLimitMap.get(advertiser.getAdvertiserId())) {
                filterReasonMap.put(advertiser.getAdvertiserId(), "地域每日数量达到上限");
                return false;
            }
            // 校验广告主与落地页标签(保险措施)
            if (!advertiserTagService.isExist(advertiser.getAdvertiserId(), lpTag)) {
                filterReasonMap.put(advertiser.getAdvertiserId(), "广告主与落地页标签不匹配");
                return false;
            }
            // 广告主余额校验
            Integer balance = advertiserBalanceMap.get(advertiser.getAdvertiserId());
            if (null == balance || balance <= 0 || balance < NumberUtils.defaultInt(advertiser.getFormPrice())) {
                filterReasonMap.put(advertiser.getAdvertiserId(), "广告主余额不足");
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        if (MapUtils.isNotEmpty(filterReasonMap)) {
            log.info("落地页数据回传,过滤原因,recordId={},orderId={},reason={}", record.getId(), record.getOrderId(), JSON.toJSONString(filterReasonMap));
        }

        if (CollectionUtils.isEmpty(advertiserList)) {
            log.info("落地页数据回传,无剩余可分单广告主,recordId={}", record.getId());
            return;
        }

        // 筛选出排序因子最高的广告主，然后打乱
        Integer orderFactor = advertiserList.get(0).getOrderFactor();
        advertiserList = advertiserList.stream().filter(advertiser -> advertiser.getOrderFactor() >= orderFactor).collect(Collectors.toList());
        Collections.shuffle(advertiserList);

        // 选出一个广告主
        Long advertiserId = advertiserList.get(0).getAdvertiserId();
        Integer formCountLimit = advertiserList.get(0).getDailyLimit();

        // 查询广告主信息
        Account account = accountService.selectAccountById(advertiserId);
        if (null == account || StringUtils.isBlank(account.getExtInfo())) {
            return;
        }
        AccountExtInfo extInfo = JSON.parseObject(account.getExtInfo(), AccountExtInfo.class);
        if (null == extInfo || StringUtils.isBlank(extInfo.getAccessKey()) || StringUtils.isBlank(extInfo.getSecretKey())) {
            return;
        }
        String url = extInfo.getLpCallbackUrl();
        Integer formPrice = extInfo.getFormPrice();

        // CPC结算广告主定向分单
        if (specifyFlag) {
            formPrice = 0;
        }

        // 广告位对应渠道
        Integer channel = slotService.getSlotChannel(record.getSlotId());
        channel = Objects.equals(extInfo.getAppRet(), 1) ? NumberUtils.defaultInt(channel) : 0;

        // 调用接口回传
        String resp = callback(url, extInfo.getAccessKey(), extInfo.getSecretKey(), record, channel, advertiserId);
        if (canRetryCallback(resp)) {
            // 失败重试
            resp = callback(url, extInfo.getAccessKey(), extInfo.getSecretKey(), record, channel, advertiserId);
        }
        int isSuccess = LandpageUtil.isCallbackSuccess(resp);

        // 表单回传数量累加
        redisAtomicClient.incrBy(EngineRedisKeyFactory.K027.join(advertiserId, today), 1, 1, TimeUnit.DAYS);
        redisAtomicClient.incrBy(EngineRedisKeyFactory.K040.join(record.getCity(), advertiserId, today, curHour), 1, 70, TimeUnit.MINUTES);
        redisAtomicClient.incrBy(EngineRedisKeyFactory.K040.join(record.getCity(), advertiserId, today), 1, 1, TimeUnit.DAYS);

        // 毛表单/有效表单计费
        if (!isConsume(extInfo.getPriceType(), isSuccess)) {
            formPrice = 0;
        }

        // 发送记录
        LandpageFormSendRecordEntity sendRecord = new LandpageFormSendRecordEntity();
        sendRecord.setOrderId(record.getOrderId());
        sendRecord.setAdvertiserId(account.getId());
        sendRecord.setRecordId(record.getId());
        sendRecord.setChannel(channel);
        sendRecord.setUrl(url);
        sendRecord.setResp(StrUtil.subPre(resp, 500));
        sendRecord.setIsSuccess(isSuccess);
        sendRecord.setFormPrice(formPrice);
        landpageFormSendRecordService.insert(sendRecord);

        // 如果回传成功且设置了表单价格，进行广告主扣费
        if (NumberUtils.defaultInt(formPrice) > 0) {
            AdvertiserConsumeBo consumeBo = new AdvertiserConsumeBo();
            consumeBo.setAccountId(advertiserId);
            consumeBo.setConsumeAmount(formPrice);
            consumeBo.setRecordId(record.getId());
            consumeBo.setConsumeType(1);
            consumeBo.setRemark("sendRecordId:" + sendRecord.getId());
            advertiserConsumeManager.consume(consumeBo);

            // 累加广告位维度当日消费
            incrSlotFormConsume(record.getSlotId(), formPrice);
        }

        // 更新广告主表单数据
        advertiserFormDataService.incr(DateUtil.beginOfDay(new Date()), advertiserId, isSuccess, formPrice);

        // 钉钉通知
        StringBuilder sb = new StringBuilder();
        sb.append("落地页实时回传\n")
                .append("\n广告主ID: ").append(advertiserId)
                .append("\n广告主名称: ").append(account.getCompanyName())
                .append("\n订单号: ").append(record.getOrderId())
                .append("\n广告ID: ").append(record.getAdvertId());
        if (channel > 0) {
            sb.append("\n渠道: ").append(channel);
        }
        if (specifyFlag) {
            sb.append("\nCPC结算: 是");
        }
        if (null != formPrice) {
            sb.append("\n表单价格: ").append(NumberUtils.fenToYuan(formPrice)).append("元");
            sb.append("\n账户余额: ").append(NumberUtils.fenToYuan(advertiserBalanceService.selectTotalAmountByAccountId(advertiserId))).append("元");
        }
        sb.append("\n落地页标签: ").append(lpTag);
        sb.append("\n姓名: ").append(DesensitizedUtil.chineseName(record.getName()))
                .append("\n手机号: ").append(DesensitizedUtil.mobilePhone(record.getPhone()))
                .append("\n省: ").append(record.getProvince())
                .append("\n市: ").append(record.getCity())
                .append("\n年龄: ").append(age)
                .append("\n上报结果: ").append(StrUtil.length(resp) > 256 && StrUtil.contains(resp, "502 Bad Gateway") ? "502 Bad Gateway": resp);
        Long formCount = redisAtomicClient.getLong(EngineRedisKeyFactory.K027.join(advertiserId, today));
        if (null != formCountLimit) {
            sb.append("\n今日已自动回传: ").append("(").append(formCount).append("/").append(formCountLimit).append(")单");
        } else {
            sb.append("\n今日已自动回传: ").append(formCount).append("单");
        }
        sendLpNoticeToDing(sb.toString());

        // CPC结算的广告主不限制表单数量
        if (finalSpecifyFlag) {
            return;
        }

        // 每日传单到达上限提醒
        if (null != formCountLimit && formCount >= formCountLimit) {
            String sb2 = "落地页表单实时回传达到上限\n" +
                    "\n广告主ID: " + advertiserId +
                    "\n广告主名称: " + account.getCompanyName() +
                    "\n每日表单上限: " + formCountLimit;
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb2);
        }
        // 地域分配表单时段上限提醒
        Long areaCount = redisAtomicClient.getLong(EngineRedisKeyFactory.K040.join(record.getCity(), advertiserId, today, curHour));
        if (null != areaLimit && areaCount >= areaLimit) {
            String sb2 = "落地页表单实时回传地域达到上限\n" +
                    "\n广告主ID: " + advertiserId +
                    "\n广告主名称: " + account.getCompanyName() +
                    "\n地域: " + record.getProvince() + "," + record.getCity() +
                    "\n时段表单上限: " + areaLimit;
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb2);
        }
        // 地域分配表单每日上限提醒
        Long areaDayCount = redisAtomicClient.getLong(EngineRedisKeyFactory.K040.join(record.getCity(), advertiserId, today));
        if (null != areaDayLimitMap.get(advertiserId) && areaDayCount >= areaDayLimitMap.get(advertiserId)) {
            String sb3 = "落地页表单实时回传地域达到每日上限\n" +
                    "\n广告主ID: " + advertiserId +
                    "\n广告主名称: " + account.getCompanyName() +
                    "\n地域: " + record.getProvince() + "," + record.getCity() +
                    "\n每日表单上限: " + areaDayLimitMap.get(advertiserId);
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb3);
        }
    }

    @Override
    public String callback(String url, String accessKey, String secretKey, LandpageFormRecord record, Integer channel, Long advertiserId) {
        // 未对接API特殊处理
        if (StringUtils.isNotBlank(url) && url.contains("localhost")) {
            return "{\"code\":200, \"msg\":\"未对接API手动分配\"}";
        }

        // 挖金客移动(195)特殊处理
        if (Objects.equals(advertiserId, 195L)) {
            accessKey = "ca7949ec7d0f49619ef443aa9f294391";
            secretKey = "5b53555a0ae64280966a3b316dc3885b";
        }

        String orderId = record.getOrderId();

        JSONObject content = new JSONObject();
        content.put("idCard", idCardService.decrypt(record.getIdCard()));
        content.put("province", record.getProvince());
        content.put("city", record.getCity());
        content.put("district", record.getDistrict());
        content.put("address", record.getAddress());
        content.put("phone", record.getPhone());
        content.put("name", record.getName());
        content.put("areaNum", record.getAreaNum());
        content.put("isp", record.getLandpageTag());
        if (channel > 0) {
            content.put("channel", channel);
        }

        JSONObject body = new JSONObject();
        body.put("accessKey", accessKey);
        body.put("orderId", orderId);
        body.put("timestamp", System.currentTimeMillis());
        body.put("content", Base64.encode(content.toString()));
        body.put("signature", Md5Utils.hash(accessKey + secretKey + body.getString("orderId")
                + body.getString("timestamp") + body.getString("content")));

        // 特殊处理(蒸蒸)
        if (Objects.equals(advertiserId, 133L) && record.getProvince().contains("河北")) {
            body.put("pcode", "zznh-2");
        }
        if (Objects.equals(advertiserId, 155L)) {
            body.put("pcode", "nuohe-lt001");

//            if (!StrUtil.containsAny(record.getProvince(), "黑龙江", "山东", "吉林", "河南", "山西",
//                    "内蒙古", "辽宁", "天津", "北京", "河北")) {
//                url = "https://icard2.qunla.cn/10010/nhGz";
//            }
        }

        String resp = HttpUtil.post(url, body.toString(), 30000);
        log.info("落地页数据回传, orderId={}, rep={}, resp={}", orderId, body.toString(), resp);
        return resp;
    }

    /**
     * 检查身份证是否已发送过
     *
     * @param idCardMd5 身份证号
     * @param lpTag 落地页标签
     * @return 是否已发送过
     */
    @Override
    public boolean isIdCardSent(String idCardMd5, String lpTag) {
        if (StringUtils.isBlank(idCardMd5)) {
            return true;
        }

        List<LandpageFormRecord> list = landpageFormRecordMapper.selectByIdCardMd5(idCardMd5, lpTag);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (LandpageFormRecord record : list) {
            if (landpageFormSendRecordService.existByRecordId(record.getId())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void sendLpNoticeToDing(String content) {
        if (StringUtils.isBlank(content)) {
            return;
        }

        // 每分钟最多传20条消息，不然会被屏蔽10分钟
        Long times = redisAtomicClient.incrBy(CrmRedisKeyFactory.K018.toString(), 1, 1, TimeUnit.MINUTES);
        if (times > 20) {
            return;
        }
        DingRobotUtil.sendText(DingWebhookConfig.getLpNotice(), content);
    }

    @Override
    public void reCallbackForm(Long recordId) {
        if (null == recordId) {
            return;
        }

        LandpageFormRecord record = landpageFormRecordMapper.selectLandpageFormRecordById(recordId);
        if (null == record) {
            return;
        }
        LandpageFormSendRecordEntity sendRecord = landpageFormSendRecordService.selectByRecordId(recordId);
        if (null == sendRecord || Objects.equals(sendRecord.getIsSuccess(), 1)) {
            return;
        }

        // 重传无用的情况
        String resp = sendRecord.getResp();
        if (!canRetryCallback(resp)) {
            return;
        }

        // 查询广告主信息
        Account advertiser = accountService.selectAccountById(sendRecord.getAdvertiserId());
        if (null == advertiser || StringUtils.isBlank(advertiser.getExtInfo())) {
            return;
        }
        AccountExtInfo extInfo = JSON.parseObject(advertiser.getExtInfo(), AccountExtInfo.class);
        if (null == extInfo || StringUtils.isBlank(extInfo.getAccessKey()) || StringUtils.isBlank(extInfo.getSecretKey())) {
            return;
        }
        String url = extInfo.getLpCallbackUrl();
        if (StringUtils.isBlank(url) || !url.startsWith("http")) {
            return;
        }

        // 重传
        resp = callback(url, extInfo.getAccessKey(), extInfo.getSecretKey(), record, sendRecord.getChannel(), advertiser.getId());
        StringBuilder sbr = new StringBuilder();
        sbr.append("落地页手动失败重传\n")
                .append("\n广告主ID: ").append(advertiser.getId())
                .append("\n广告主名称: ").append(advertiser.getCompanyName())
                .append("\n订单号: ").append(record.getOrderId())
                .append("\n提交时间: ").append(DateUtil.formatDate(record.getGmtCreate()))
                .append("\n广告ID: ").append(record.getAdvertId());
        if (sendRecord.getChannel() > 0) {
            sbr.append("\n渠道: ").append(sendRecord.getChannel());
        }
        sbr.append("\n落地页标签: ").append(record.getLandpageTag());
        sbr.append("\n姓名: ").append(DesensitizedUtil.chineseName(record.getName()))
                .append("\n手机号: ").append(DesensitizedUtil.mobilePhone(record.getPhone()))
                .append("\n省: ").append(record.getProvince())
                .append("\n市: ").append(record.getCity())
                .append("\n上报结果: ").append(StrUtil.length(resp) > 256 && StrUtil.contains(resp, "502 Bad Gateway") ? "502 Bad Gateway": resp);
        sendLpNoticeToDing(sbr.toString());


        // 发送记录
        LandpageFormSendRecordEntity updateSendRecord = new LandpageFormSendRecordEntity();
        updateSendRecord.setId(sendRecord.getId());
        updateSendRecord.setUrl(url);
        updateSendRecord.setResp(StrUtil.subPre(resp, 500));
        updateSendRecord.setIsSuccess(isCallbackSuccess(resp));
        landpageFormSendRecordService.updateById(updateSendRecord);
    }

    @Override
    public List<Long> retryIdCardAudit(Integer limit) {
        LandpageFormRecord param = new LandpageFormRecord();
        param.setStartDate(DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -3)));
        param.setIdCardAudit(0);
        List<LandpageFormRecord> list = landpageFormRecordMapper.selectList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        if (null == limit || limit < 0) {
            limit = 1;
        }
        list = list.stream().filter(record -> Objects.equals(record.getIdCardAudit(), 0)).limit(limit).collect(Collectors.toList());

        List<Long> retryIds = new ArrayList<>();
        for (LandpageFormRecord record : list) {
            retryIds.add(record.getId());

            LandpageFormRecord updateRecord = new LandpageFormRecord();
            updateRecord.setId(record.getId());

            // 身份证实名认证校验
            String idCard = idCardService.decrypt(record.getIdCard());
            IdCardAuditBo authResult = idCardAuditService.idCardAudit(record.getName(), idCard);
            updateRecord.setIdCardAudit(IdCardAuditBo.isMatch(authResult) ? 1 : (IdCardAuditBo.isNotMatch(authResult) ? 2 : 0));
            updateRecord.setAuditApiType(null != authResult ? authResult.getApiType() : 0);

            if (NumberUtils.defaultInt(updateRecord.getIdCardAudit()) > 0) {
                landpageFormRecordMapper.updateLandpageFormRecord(updateRecord);
            }
        }
        return retryIds;
    }

    @Override
    public void incrSlotFormConsume(Long slotId, Integer formPrice) {
        // 更新Redis中的数据(后面移除)
        try {
            String key = EngineRedisKeyFactory.K051.join(DateUtil.today());
            redisCache.incrCacheMapValue(key, String.valueOf(slotId), formPrice);
            redisCache.expire(key, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("累加广告位维度表单消耗异常, slotId={}, formPrice={}", slotId, formPrice, e);
        }
        // 数据落库
        slotLandpageFormDataService.incrFormConsume(DateUtil.beginOfDay(new Date()),slotId,formPrice);
    }

    /**
     * 查询行政区划数据
     *
     * @param lpTag 落地页标签
     * @param areaNum 行政区划代码
     * @return 地域信息
     */
    private Area queryArea(String lpTag, String areaNum) {
        if (!StringUtils.isNumeric(areaNum)) {
            return null;
        }

        Area area = null;
        if (Objects.equals(lpTag, AREA_TAG_LT) || Objects.equals(lpTag, AREA_TAG_YD) || Objects.equals(lpTag, AREA_TAG_DX)) {
            area = areaService.queryAreaByAreaNumLtYdCache(lpTag, areaNum);
        }
        if (null == area) {
            area = areaService.queryAreaByAreaNum(areaNum);
            if (null == area && !Objects.equals(lpTag, AREA_TAG_LT)) {
                area = areaService.queryAreaByAreaNumLtYdCache(AREA_TAG_LT, areaNum);
            }
            if (null == area && !Objects.equals(lpTag, AREA_TAG_YD)) {
                area = areaService.queryAreaByAreaNumLtYdCache(AREA_TAG_YD, areaNum);
            }
        }
        return area;
    }

    /**
     * 查询每小时地域表单上限
     *
     * @return 每小时地域表单上限
     */
    private Long queryFormAreaLimit() {
        String areaLimitStr = sysConfigService.selectConfigCacheByKey(BizConfigEnum.FORM_AREA_LIMIT.getKey());
        return StringUtils.isNumeric(areaLimitStr) ? Long.valueOf(areaLimitStr) : null;
    }

    /**
     * 查询广告主每日地域表单上限映射
     *
     * @return 广告主每日地域表单上限
     */
    private Map<Long, Long> queryFormAreaDayLimitMap() {
        return mapConfigService.getMap(MapConfigEnum.FORM_AREA_DAY_LIMIT_MAP, Long.class, Long.class);
    }
}
