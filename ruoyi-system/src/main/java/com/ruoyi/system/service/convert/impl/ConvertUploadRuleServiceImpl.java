package com.ruoyi.system.service.convert.impl;

import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.convert.ConvertUploadRuleEntity;
import com.ruoyi.system.mapper.convert.ConvertUploadRuleMapper;
import com.ruoyi.system.req.manager.ConvertUploadRuleListReq;
import com.ruoyi.system.req.manager.ConvertUploadRuleReq;
import com.ruoyi.system.service.convert.ConvertUploadRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 媒体转化上报规则 Service
 *
 * <AUTHOR>
 * @date 2022-10-19 10:30:13
 */
@Service
public class ConvertUploadRuleServiceImpl implements ConvertUploadRuleService {

    @Autowired
    private ConvertUploadRuleMapper convertUploadRuleMapper;

    @Override
    public List<ConvertUploadRuleEntity> selectList(ConvertUploadRuleListReq req) {
        return convertUploadRuleMapper.selectList(req);
    }

    @Override
    public List<ConvertUploadRuleEntity> selectHistory(Long slotId) {
        if (null == slotId) {
            return Collections.emptyList();
        }
        return convertUploadRuleMapper.selectHistory(slotId);
    }

    @Override
    public ConvertUploadRuleEntity selectBySlotId(Long slotId) {
        if (null == slotId) {
            return null;
        }
        return convertUploadRuleMapper.selectBySlotId(slotId);
    }

    @Override
    public int insert(ConvertUploadRuleEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return convertUploadRuleMapper.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(ConvertUploadRuleReq req) {
        // 查询旧规则
        ConvertUploadRuleEntity oldRule = selectBySlotId(req.getSlotId());
        if (null == oldRule) {
            throw new CustomException("该广告位未配置比例");
        }
        // 删除旧规则
        oldRule.setIsDeleted(1);
        convertUploadRuleMapper.update(oldRule);
        // 增加新规则
        ConvertUploadRuleEntity newRule = new ConvertUploadRuleEntity();
        newRule.setSlotId(req.getSlotId());
        newRule.setRatio(req.getRatio());
        newRule.setCreatorId(oldRule.getCreatorId());
        newRule.setCreateTime(oldRule.getCreateTime());
        newRule.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
        newRule.setOperateTime(new Date());
        return convertUploadRuleMapper.insert(newRule);
    }
}
