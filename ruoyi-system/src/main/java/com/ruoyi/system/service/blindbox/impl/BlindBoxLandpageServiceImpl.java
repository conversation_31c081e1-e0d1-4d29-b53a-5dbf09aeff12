package com.ruoyi.system.service.blindbox.impl;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.blindbox.BlindBoxLandpageService;
import com.ruoyi.system.entity.blindbox.BlindBoxLandpageEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.blindbox.BlindBoxLandpageMapper;

/**
 * 盲盒落地页表 Service
 *
 * <AUTHOR>
 * @date 2022-6-13 11:10:01
 */
@Service
public class BlindBoxLandpageServiceImpl implements BlindBoxLandpageService {

    @Autowired
    private BlindBoxLandpageMapper blindBoxLandpageMapper;

    @Override
    public Map<String, BlindBoxLandpageEntity> selectMap() {
        List<BlindBoxLandpageEntity> list = blindBoxLandpageMapper.selectList(new BlindBoxLandpageEntity());
        return list.stream().collect(Collectors.toMap(BlindBoxLandpageEntity::getLandpageKey, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Boolean insert(BlindBoxLandpageEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return blindBoxLandpageMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return blindBoxLandpageMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(BlindBoxLandpageEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return blindBoxLandpageMapper.updateById(entity) > 0;
    }

    @Override
    public BlindBoxLandpageEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return blindBoxLandpageMapper.selectById(id);
    }

    @Override
    public List<BlindBoxLandpageEntity> selectList(BlindBoxLandpageEntity param) {
        return blindBoxLandpageMapper.selectList(param);
    }

    @Override
    public BlindBoxLandpageEntity selectByLandpageKey(String landpageKey) {
        if (StringUtils.isBlank(landpageKey)) {
            return null;
        }
        return blindBoxLandpageMapper.selectByLandpageKey(landpageKey);
    }

    @Override
    public List<String> selectByTargetLandpage(String targetLandpage) {
        if (StringUtils.isBlank(targetLandpage)) {
            return Collections.emptyList();
        }
        BlindBoxLandpageEntity param = new BlindBoxLandpageEntity();
        param.setTargetLandpage(targetLandpage);
        return selectList(param).stream().map(BlindBoxLandpageEntity::getLandpageKey).collect(Collectors.toList());
    }
}
