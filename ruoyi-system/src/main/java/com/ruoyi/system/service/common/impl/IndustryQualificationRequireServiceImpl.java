package com.ruoyi.system.service.common.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.common.IndustryQualificationRequireService;
import com.ruoyi.system.entity.common.IndustryQualificationRequireEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.common.IndustryQualificationRequireMapper;
import org.springframework.util.CollectionUtils;

/**
 * 行业资质要求表 Service
 *
 * <AUTHOR>
 * @date 2023-3-6 17:34:59
 */
@Service
public class IndustryQualificationRequireServiceImpl implements IndustryQualificationRequireService {

    @Autowired
    private IndustryQualificationRequireMapper industryQualificationRequireMapper;

    @Override
    public List<IndustryQualificationRequireEntity> selectList(IndustryQualificationRequireEntity param) {
        return industryQualificationRequireMapper.selectList(param);
    }

    @Override
    public Integer insert(IndustryQualificationRequireEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return industryQualificationRequireMapper.insert(entity);
    }

    @Override
    public Integer updateById(IndustryQualificationRequireEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return industryQualificationRequireMapper.updateById(entity);
    }

    @Override
    public IndustryQualificationRequireEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return industryQualificationRequireMapper.selectById(id);
    }

    @Override
    public Map<Long, List<IndustryQualificationRequireEntity>> selectMapByIndustryIds(List<Long> industryIds) {
        if (CollectionUtils.isEmpty(industryIds)) {
            return Collections.emptyMap();
        }
        List<IndustryQualificationRequireEntity> list = industryQualificationRequireMapper.selectByIndustryIds(industryIds);
        return list.stream().collect(Collectors.groupingBy(IndustryQualificationRequireEntity::getIndustryId));
    }

    @Override
    public List<Long> selectMustRequireIdByIndustryId(Long industryId) {
        if (null == industryId) {
            return Collections.emptyList();
        }
        return industryQualificationRequireMapper.selectMustRequireIdByIndustryId(industryId);
    }

    @Override
    public Map<Long, List<Long>> selectMustRequireIdMap() {
        IndustryQualificationRequireEntity param = new IndustryQualificationRequireEntity();
        param.setIsMust(1);
        param.setEnableStatus(1);
        List<IndustryQualificationRequireEntity> list = selectList(param);
        return list.stream().collect(Collectors.groupingBy(IndustryQualificationRequireEntity::getIndustryId, Collectors.mapping(IndustryQualificationRequireEntity::getId, Collectors.toList())));
    }
}
