package com.ruoyi.system.service.callback.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.enums.open.YoukuConfigEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.youku.MapStrUtil;
import com.ruoyi.common.utils.youku.SignUtil;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.YOUKU;

/**
 * 优酷上报处理器
 * 配置: {@link YoukuConfigEnum}
 *
 * <AUTHOR>
 * @date 2023/11/2
 */
@Slf4j
@Service
public class YoukuCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final String CONV_UPLOAD_URL = "https://missile.youku.com/api/ad/conv/v2";

    @Override
    public CallbackProcessorTypeEnum getType() {
        return YOUKU;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNumeric(param.getString("creativeid")) && StringUtils.isNotBlank(param.getString("trackid"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            String eventType = "pay";
            JSONObject param = context.getParam().getSlotParam();

            Map<String, Object> ykParam = new HashMap<>();
            ykParam.put("event_type", eventType);
            ykParam.put("event_time", System.currentTimeMillis());
            ykParam.put("track_id", param.getString("trackid"));
            ykParam.put("deep_conv", 0);
            ykParam.put("creative_id", param.getString("creativeid"));
            ykParam.put("client_ip", param.getString("client_ip"));

            YoukuConfigEnum config = YoukuConfigEnum.getBySlotId(context.getOrder().getSlotId());
            String token = config.getToken();
            AdConvInfo advConvInfo = new AdConvInfo();
            advConvInfo.setAppKey(config.getAppKey());
            advConvInfo.setContent(ykParam);
            advConvInfo.setSignature(SignUtil.sign(advConvInfo.getAppKey(), token, MapStrUtil.toStr(advConvInfo.getContent())));
            String resp = doPostEntityJSON(CONV_UPLOAD_URL, advConvInfo);
            log.info("{}接口上报, param={}, resp={}", getType().getName(), JSON.toJSONString(ykParam), resp);
            return true;
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }

    public String doPostEntityJSON(String url, Object postReq) {
        RequestConfig requestCong = RequestConfig.custom().setConnectionRequestTimeout(5000)
                .setConnectTimeout(5000).setSocketTimeout(5000).build();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(requestCong);
        httpPost.setEntity(new StringEntity(JSON.toJSONString(postReq), ContentType.APPLICATION_JSON));

        CloseableHttpClient httpClient = HttpClients.custom().setMaxConnTotal(100).setMaxConnPerRoute(100).setRetryHandler(
                new DefaultHttpRequestRetryHandler(0, false)).disableAutomaticRetries().build();

        try {
            CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
            return EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            return null;
        }
    }

    @Getter
    @Setter
    static class AdConvInfo {
        /**
         * 回传事件类型
         *
         */
        private String eventType;

        /**
         * 回传时间
         */
        private Long eventTime;

        /**
         * 转化收益，单位分，人民币
         */
        private String convAmount;

        /**
         * 转化数量
         */
        private String convCount;

        /**
         * 用于追踪关联广告投放维度的id
         */
        private String trackId;

        /**
         * 广告投放单元id（排期/计划）
         */
        private String adgroupId;

        /**
         * 广告投放创意id
         */
        private String creativeId;

        /**
         * 回传侧ip
         */
        private String clientIp;

        /**
         * 设备终端idfa原值，iOS上报
         */
        private String idfa;

        /**
         * 设备终端oaid原值
         */
        private String oaid;

        /**
         * 设备终端imei，md5摘要值，安卓上报
         */
        private String imei;

        /**
         * MAC地址，去除分隔符”:”的大写MAC地址取MD5摘要值
         */
        private String mac;

        /**
         * User Agent，需要进行 URL encode之后传值
         */
        private String ua;

        /**
         * 互联网广告标识，版本号以及具体的CAID值，格式为：ver1_caid1，多个之间以逗号分割，例：20210301_xxxxxxxx,20210101_xxxxxx
         */
        private String caid;

        /**
         * 设备utdid
         */
        private String utdid;

        /**
         * 终端机型
         */
        private String model;

        /**
         * 终端品牌
         */
        private String brand;

        /**
         * 系统类型
         * 0-表示Android，1-表示iOS，2-表示Windows Phone，4-表示HarmonyOS，3-表示其他
         */
        private String os;

        /**
         * 回传应用来源标识
         */
        private String appKey;

        /**
         * 签名
         */
        private String signature;

        /**
         * 内容
         */
        private Map<String, Object> content;

        /**
         * 是否是深度转化数据
         */
        private Integer deepConv;
    }
}
