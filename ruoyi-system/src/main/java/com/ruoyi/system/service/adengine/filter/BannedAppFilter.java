package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.system.service.adengine.AdvertFilter;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 屏蔽媒体过滤
 */
@Component
public class BannedAppFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();
        Long appId = context.getAppId();
        return CollectionUtils.isEmpty(ad.getBannedAppIds()) || !ad.getBannedAppIds().contains(appId);
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.BANNED_APP;
    }
}
