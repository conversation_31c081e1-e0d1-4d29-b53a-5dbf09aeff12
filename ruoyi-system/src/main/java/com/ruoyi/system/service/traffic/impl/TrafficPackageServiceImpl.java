package com.ruoyi.system.service.traffic.impl;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.traffic.TrafficPackageListBo;
import com.ruoyi.system.entity.traffic.TrafficPackageEntity;
import com.ruoyi.system.mapper.traffic.TrafficPackageMapper;
import com.ruoyi.system.req.traffic.TrafficPackageListReq;
import com.ruoyi.system.req.traffic.TrafficPackageReq;
import com.ruoyi.system.service.traffic.AdvertOrientTrafficService;
import com.ruoyi.system.service.traffic.TrafficPackageItemService;
import com.ruoyi.system.service.traffic.TrafficPackageService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 流量包 Service
 *
 * <AUTHOR>
 * @date 2022-8-23 10:31:21
 */
@Service
public class TrafficPackageServiceImpl implements TrafficPackageService {

    @Autowired
    private TrafficPackageMapper trafficPackageMapper;

    @Autowired
    private TrafficPackageItemService trafficPackageItemService;

    @Autowired
    private AdvertOrientTrafficService advertOrientTrafficService;

    @Override
    public List<TrafficPackageListBo> selectList(TrafficPackageListReq req) {
        return trafficPackageMapper.selectList(req);
    }

    @Override
    public List<TrafficPackageListBo> selectByOrientId(Long orientId) {
        List<Long> trafficPackageIds = advertOrientTrafficService.selectTrafficPackageIdByOrientId(orientId);
        return selectListByTrafficPackageIds(trafficPackageIds);
    }

    @Override
    public Map<Long, List<TrafficPackageListBo>> selectByAdvertId(Long advertId) {
        Map<Long, List<Long>> trafficPackageIdMap = advertOrientTrafficService.selectTrafficPackageIdByAdvertId(advertId);
        List<TrafficPackageListBo> trafficPackageList = selectListByTrafficPackageIds(trafficPackageIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        Map<Long, TrafficPackageListBo> trafficPackageMap = trafficPackageList.stream().collect(Collectors.toMap(TrafficPackageListBo::getId, Function.identity(), (v1, v2) -> v2));

        Map<Long, List<TrafficPackageListBo>> map = new HashMap<>();
        trafficPackageIdMap.forEach((orientId, trafficPackageIds) -> map.put(orientId, trafficPackageIds.stream().map(trafficPackageMap::get).collect(Collectors.toList())));
        return map;
    }

    @Override
    public Map<Long, Set<Long>> selectTrafficSlotByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }
        Map<Long, List<Long>> trafficMap = advertOrientTrafficService.selectTrafficByAdvertIds(advertIds);
        if (MapUtils.isEmpty(trafficMap)) {
            return Collections.emptyMap();
        }
        Map<Long, List<Long>> trafficSlotMap = trafficPackageItemService.selectTrafficSlotMap(trafficMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));

        Map<Long, Set<Long>> map = new HashMap<>();
        trafficMap.forEach((advertId, trafficPackageIds) -> {
            Set<Long> slots = new HashSet<>();
            trafficPackageIds.forEach(trafficPackageId -> slots.addAll(trafficSlotMap.getOrDefault(trafficPackageId, new ArrayList<>())));
            map.put(advertId, slots);
        });
        return map;
    }

    @Override
    public Map<Long, Set<Long>> selectTrafficSlotByOrientIds(List<Long> orientIds) {
        if (CollectionUtils.isEmpty(orientIds)) {
            return Collections.emptyMap();
        }
        Map<Long, List<Long>> trafficMap = advertOrientTrafficService.selectTrafficByOrientIds(orientIds);
        if (MapUtils.isEmpty(trafficMap)) {
            return Collections.emptyMap();
        }
        Map<Long, List<Long>> trafficSlotMap = trafficPackageItemService.selectTrafficSlotMap(trafficMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));

        Map<Long, Set<Long>> map = new HashMap<>();
        trafficMap.forEach((orientId, trafficPackageIds) -> {
            Set<Long> slots = new HashSet<>();
            trafficPackageIds.forEach(trafficPackageId -> slots.addAll(trafficSlotMap.getOrDefault(trafficPackageId, new ArrayList<>())));
            map.put(orientId, slots);
        });
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(TrafficPackageReq req) {
        if (StringUtils.isBlank(req.getName()) || CollectionUtils.isEmpty(req.getSlotIds())) {
            return false;
        }
        TrafficPackageEntity pkg = new TrafficPackageEntity();
        pkg.setName(req.getName());
        pkg.setDesc(req.getDesc());
        pkg.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
        trafficPackageMapper.insert(pkg);
        trafficPackageItemService.updateOrInsert(pkg.getId(), req.getSlotIds());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(TrafficPackageReq req) {
        if (null == req.getId() || CollectionUtils.isEmpty(req.getSlotIds())) {
            return false;
        }

        TrafficPackageEntity updatePkg = new TrafficPackageEntity();
        updatePkg.setId(req.getId());
        updatePkg.setDesc(req.getDesc());
        if (trafficPackageMapper.updateById(updatePkg) < 1) {
            return false;
        }
        trafficPackageItemService.updateOrInsert(req.getId(), req.getSlotIds());
        return true;
    }

    @Override
    public TrafficPackageEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return trafficPackageMapper.selectById(id);
    }

    @Override
    public boolean isNameDuplicate(String name) {
        return null != trafficPackageMapper.existByName(name);
    }

    private List<TrafficPackageListBo> selectListByTrafficPackageIds(List<Long> trafficPackageIds)  {
        if (CollectionUtils.isEmpty(trafficPackageIds)) {
            return Collections.emptyList();
        }
        TrafficPackageListReq req = new TrafficPackageListReq();
        req.setIds(trafficPackageIds);
        return trafficPackageMapper.selectList(req);
    }
}
