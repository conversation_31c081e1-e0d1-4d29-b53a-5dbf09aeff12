package com.ruoyi.system.service.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.enums.LayerType;
import com.ruoyi.common.enums.advert.MaterialStatusEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.advert.InnovateLayer;
import com.ruoyi.system.entity.advert.Material;
import com.ruoyi.system.mapper.manager.MaterialMapper;
import com.ruoyi.system.req.advert.MaterialReq;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.manager.InnovateLayerService;
import com.ruoyi.system.service.manager.MaterialService;
import com.ruoyi.system.vo.advert.InnovateLayerVO;
import com.ruoyi.system.vo.advert.MaterialVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.ruoyi.common.enums.IsDefaultEnum.IS_DEFAULT;
import static com.ruoyi.common.enums.IsDefaultEnum.NOT_DEFAULT;

/**
 * 广告素材Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@Slf4j
@Service
public class MaterialServiceImpl implements MaterialService {

    @Autowired
    private MaterialMapper materialMapper;

    @Autowired
    private InnovateLayerService innovateLayerService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    /**
     * 查询广告素材
     *
     * @param id 广告素材ID
     * @return 广告素材
     */
    @Override
    public Material selectMaterialById(Long id) {
        return materialMapper.selectMaterialById(id);
    }

    /**
     * 查询广告素材列表
     *
     * @param material 广告素材
     * @return 广告素材
     */
    @Override
    public List<Material> selectMaterialList(Material material) {
        return materialMapper.selectMaterialList(material);
    }

    @Override
    public Map<Long, List<MaterialVO>> selectValidByAdvertIds(List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }

        try {
            Material param = new Material();
            param.setAdvertIds(advertIds);
            param.setStatus(MaterialStatusEnum.ENABLE.getStatus());
            List<Material> materials = materialMapper.selectMaterialList(param);
            if (CollectionUtils.isEmpty(materials)) {
                return Collections.emptyMap();
            }
            Map<Long, InnovateLayerVO> layerMap = innovateLayerService.selectInnovateLayerMap();
            Map<Long, List<MaterialVO>> map = new HashMap<>();
            for (Material material : materials) {
                Long advertId = material.getAdvertId();
                if (!map.containsKey(advertId)) {
                    map.put(advertId, new ArrayList<>());
                }
                MaterialVO materialVO = BeanUtil.copyProperties(material, MaterialVO.class);
                InnovateLayerVO layer = layerMap.get(material.getLayerId());
                if (null != layer) {
                    materialVO.setGifImg(layer.getGifImg());
                    materialVO.setLayerInfo(layer.getLayerInfo());
                    materialVO.setSkinType(layer.getSkinType());
                }
                map.get(advertId).add(materialVO);
            }
            return map;
        } catch (Exception e) {
            log.error("selectValidByAdvertIds error", e);
        }
        return Collections.emptyMap();
    }

    /**
     * 新增广告素材
     *
     * @param req 广告素材
     * @return 结果
     */
    @Override
    public int insertMaterial(MaterialReq req) {
        Material material = new Material();
        material.setAdvertId(req.getAdvertId());
        material.setAdvertTitle(req.getAdvertTitle());
        material.setMaterialImg(req.getMaterialImg());
        material.setButtonText(req.getButtonText());
        material.setLayerType(req.getLayerType());
        material.setLayerId(req.getLayerId());
        material.setIsDefault(NOT_DEFAULT.getType());
        material.setStatus(MaterialStatusEnum.DISABLE.getStatus());

        // 创新弹层
        if (LayerType.isInnovateLayer(req.getLayerType())) {
            InnovateLayer layer = innovateLayerService.selectInnovateLayerById(req.getLayerId());
            if (null == layer) {
                throw new CustomException("无效的弹层ID");
            }
            material.setMaterialImg(layer.getBgImg());
        }

        int result = materialMapper.insertMaterial(material);
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertCacheMsg(material.getAdvertId());
        return result;

    }

    /**
     * 修改广告素材
     *
     * @param req 广告素材
     * @return 结果
     */
    @Override
    public int updateMaterial(MaterialReq req) {
        Material material = materialMapper.selectMaterialById(req.getId());
        if (null == material) {
            throw new CustomException("无效的素材ID");
        }

        // 创新弹层
        InnovateLayer layer = null;
        if (LayerType.isInnovateLayer(req.getLayerType())) {
            layer = innovateLayerService.selectInnovateLayerById(req.getLayerId());
            if (null == layer) {
                throw new CustomException("无效的弹层ID");
            }
        }

        Material updateMaterial = new Material();
        updateMaterial.setId(req.getId());
        updateMaterial.setAdvertTitle(req.getAdvertTitle());
        updateMaterial.setMaterialImg(req.getMaterialImg());
        updateMaterial.setButtonText(req.getButtonText());
        updateMaterial.setLayerType(req.getLayerType());
        updateMaterial.setLayerId(req.getLayerId());
        if (null != layer) {
            updateMaterial.setMaterialImg(layer.getBgImg());
        }
        int result = materialMapper.updateMaterial(updateMaterial);
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertCacheMsg(material.getAdvertId());
        return result;

    }

    @Override
    public int updateMaterialStatus(MaterialReq req) {
        Material material = materialMapper.selectMaterialById(req.getId());
        if (null == material) {
            throw new CustomException("无效的素材ID");
        }
        if (Objects.equals(material.getIsDefault(), IS_DEFAULT.getType())) {
            throw new CustomException("默认素材不能激活/屏蔽");
        }
        if (Objects.equals(req.getStatus(), MaterialStatusEnum.DELETED.getStatus())
                && !Objects.equals(material.getStatus(), MaterialStatusEnum.DISABLE.getStatus())) {
            throw new CustomException("素材屏蔽后才可删除");
        }

        Material updateMaterial = new Material();
        updateMaterial.setId(req.getId());
        updateMaterial.setStatus(req.getStatus());
        int result = materialMapper.updateMaterial(updateMaterial);
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertCacheMsg(material.getAdvertId());
        return result;
    }

    @Override
    public int updateMaterialWeight(MaterialReq req) {
        Material material = materialMapper.selectMaterialById(req.getId());
        if (null == material) {
            throw new CustomException("无效的素材ID");
        }

        Material updateMaterial = new Material();
        updateMaterial.setId(req.getId());
        updateMaterial.setWeight(req.getWeight());
        int result = materialMapper.updateMaterial(updateMaterial);
        // 刷新缓存
        refreshCacheService.sendRefreshAdvertCacheMsg(material.getAdvertId());
        return result;
    }

    @Override
    public int updateMaterialByLayer(Long layerId, String materialImg) {
        if (null == layerId || StringUtils.isBlank(materialImg)) {
            return 0;
        }

        Material param = new Material();
        param.setLayerType(LayerType.INNOVATE.getType());
        param.setLayerId(layerId);
        List<Material> materials = selectMaterialList(param);
        if (CollectionUtils.isEmpty(materials)) {
            return 0;
        }

        int result = 0;
        for (Material material : materials) {
            if (!Objects.equals(layerId, material.getLayerId())
                    || !Objects.equals(material.getLayerType(), LayerType.INNOVATE.getType())) {
                continue;
            }

            Material updateMaterial = new Material();
            updateMaterial.setId(material.getId());
            updateMaterial.setMaterialImg(materialImg);
            result += materialMapper.updateMaterial(updateMaterial);
            refreshCacheService.sendRefreshAdvertCacheMsg(material.getAdvertId());
        }
        return result;
    }

    @Override
    public List<Long> selectUsedLayerId(List<Long> layerIds) {
        if (CollectionUtils.isEmpty(layerIds)) {
            return Collections.emptyList();
        }
        return materialMapper.selectUsedLayerId(layerIds);
    }
}
