package com.ruoyi.system.service.slot.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.entity.slot.SlotRedirectHourData;
import com.ruoyi.system.service.slot.SlotRedirectHourDataService;
import com.ruoyi.system.service.slot.SlotRedirectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToActivity;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToAdvert;

/**
 * 广告位投放Service接口实现
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
@Slf4j
@Service
public class SlotRedirectServiceImpl implements SlotRedirectService {

    @Autowired
    private SlotRedirectHourDataService slotRedirectHourDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public void statistics(Long slotId, Date date, Integer hour, String deviceId, Integer redirectType, String redirectValue) {
        GlobalThreadPool.statExecutorService.submit(() -> {
            String dateStr = DateUtil.formatDate(date);
            String redirectValueMd5 = Md5Utils.hash(redirectValue);
            String rValue = redirectToActivity(redirectType) || redirectToAdvert(redirectType) ? redirectValue : redirectValueMd5;
            int redirectUv = BizUtils.countUv(EngineRedisKeyFactory.K034.join(slotId, dateStr, hour, redirectType, rValue), deviceId, 1, TimeUnit.HOURS);

            Long dataId = getSlotRedirectHourDataId(dateStr, date, hour, slotId, redirectType, redirectValue, redirectValueMd5);
            slotRedirectHourDataService.update(dataId, 1, redirectUv);
        });
    }

    @Override
    public void clickCallback(HttpServletRequest request) {
        String callbackUrl = request.getParameter("callbackUrlRemote");
        if (!StrUtil.startWith(callbackUrl, "http")) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            HttpUtil.createGet(callbackUrl).timeout(5000).execute(true);
        });
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getSlotRedirectHourDataId(String dateStr, Date date, Integer hour, Long slotId, Integer redirectType, String redirectValue, String redirectValueMd5) {
        String key = EngineRedisKeyFactory.K022.join("SlotRedirectHourData", dateStr, hour, slotId, redirectType, redirectValueMd5);
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        SlotRedirectHourData param = new SlotRedirectHourData();
        param.setSlotId(slotId);
        param.setCurDate(date);
        param.setCurHour(hour);
        param.setRedirectType(redirectType);
        param.setRedirectValue(redirectValue);
        param.setRedirectValueMd5(redirectValueMd5);
        SlotRedirectHourData data = slotRedirectHourDataService.selectBy(param);
        if (null == data) {
            slotRedirectHourDataService.insert(param);
            data = slotRedirectHourDataService.selectBy(param);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.HOURS);
        return data.getId();
    }
}
