package com.ruoyi.system.service.invoice;

import com.ruoyi.system.bo.invoice.InvoiceSumBO;
import com.ruoyi.system.bo.invoice.InvoiceSumListBO;
import com.ruoyi.system.entity.invoice.InvoiceEntity;

import java.util.Date;
import java.util.List;

/**
 * 发票表 Service
 *
 * <AUTHOR>
 * @date 2022-10-20 14:33:51
 */
public interface InvoiceService {
    /**
     * 新增记录
     */
    Boolean insert(InvoiceEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(InvoiceEntity entity);

    /**
     * 根据id获取
     */
    InvoiceEntity selectById(Long id);

    /**
     * 根据账号id列表查询发票总金额
     * @param accountIds 账号id列表
     * @return
     */
    List<InvoiceSumBO> sumInvoice(List<Long> accountIds);

    /**
     * 根据账号id查询发票信息
     * @param accountId 账号id
     * @return
     */
    List<InvoiceEntity> getInvoiceInfoListByAccountId(Long accountId);

    /**
     * 根据更新时间查询发票列表
     * @param startDate
     * @param endDate
     * @return
     */
    List<InvoiceEntity> selectListByGmtModified(Date startDate,Date endDate);

    /**
     * 根据账号id列表和状态查询
     * @param accountIds
     * @param status
     * @return
     */
    List<InvoiceSumListBO> selectListByAccountIds(List<Long> accountIds, Integer status);
}
