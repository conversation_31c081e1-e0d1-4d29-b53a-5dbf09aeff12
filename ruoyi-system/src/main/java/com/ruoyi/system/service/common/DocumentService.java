package com.ruoyi.system.service.common;

import com.ruoyi.system.entity.common.DocumentEntity;

import java.util.List;

/**
 * 文档表 Service
 *
 * <AUTHOR>
 * @date 2022-12-1 17:08:32
 */
public interface DocumentService {

    /**
     * 查询列表
     */
    List<DocumentEntity> selectList(DocumentEntity param);

    /**
     * 新增记录
     */
    int insert(DocumentEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(DocumentEntity entity);

    /**
     * 根据id获取
     */
    DocumentEntity selectById(Long id);

    /**
     * 同公司下是否存在同名文档或者相同链接
     *
     * @param id 文档ID
     * @param companyName 公司名称
     * @param documentName 文档链接
     * @param documentUrl 文档链接
     * @return true.存在,false.不存在
     */
    boolean existDuplication(Long id, String companyName, String documentName, String documentUrl);
}
