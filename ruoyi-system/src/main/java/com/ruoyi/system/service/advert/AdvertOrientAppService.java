package com.ruoyi.system.service.advert;

import com.ruoyi.system.bo.advert.AdvertOrientAppBo;
import com.ruoyi.system.domain.advert.AdvertOrienteApp;

import java.util.List;
import java.util.Map;

/**
 * 广告定向媒体Service
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
public interface AdvertOrientAppService {

    /**
     * 查询配置的定向媒体列表
     *
     * @param orientId 广告配置ID
     * @return 定向媒体列表
     */
    List<AdvertOrientAppBo> selectListByOrientId(Long orientId);

    /**
     * 查询配置的定向媒体列表
     *
     * @param orientId 广告配置ID
     * @return 定向媒体列表
     */
    List<AdvertOrienteApp> selectByOrientId(Long orientId);

    /**
     * 查询配置的定向媒体列表
     *
     * @param advertId 广告ID
     * @return 配置ID-定向媒体列表映射
     */
    Map<Long, List<AdvertOrientAppBo>> selectMapByAdvertId(Long advertId);
}
