package com.ruoyi.system.service.finance;

import com.ruoyi.system.entity.checkrecord.CheckRecordEntity;

import java.util.List;

/**
 * 提现记录 Service
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:02
 */
public interface CheckRecordService {

    /**
     * 保存记录
     */
    Boolean save(CheckRecordEntity entity);

    /**
     * 根据提现记录id查询最新的审核记录
     *
     * @param withdrawId 提现记录ID
     * @return 审核记录
     */
    CheckRecordEntity selectByWithdrawId(Long withdrawId);

    /**
     * 根据提现记录id列表查询审核记录列表
     *
     * @param withdrawIds 提现记录id列表
     * @return 审核记录
     */
    List<CheckRecordEntity> selectListByWithdrawIds(List<Long> withdrawIds);
}
