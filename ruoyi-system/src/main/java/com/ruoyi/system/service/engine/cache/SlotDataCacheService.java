package com.ruoyi.system.service.engine.cache;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.data.OrderDataBo;
import com.ruoyi.system.service.common.AdvertOrderLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 广告位维度数据缓存服务
 *
 * <AUTHOR>
 * @date 2023/05/17
 */
@Slf4j
@Service
public class SlotDataCacheService {

    @Autowired
    private AdvertOrderLogService advertOrderLogService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 是否新广告位缓存(发券<=1000)
     */
    private final LoadingCache<Long, Boolean> IS_NEW_SLOT_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build(new CacheLoader<Long, Boolean>() {
                @Override
                public Boolean load(Long slotId) {
                    return selectAdLaunchCacheBySlotIdAndAdvertId(slotId, null) <= 1000;
                }

                @Override
                public ListenableFuture<Boolean> reload(Long slotId, Boolean oldValue) {
                    ListenableFutureTask<Boolean> task = ListenableFutureTask.create(() -> load(slotId));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告位广告CVR缓存(直投广告使用)
     */
    private final LoadingCache<Pair<Long, Long>, Double> SLOT_AD_CVR_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build(new CacheLoader<Pair<Long, Long>, Double>() {
                @Override
                public Double load(Pair<Long, Long> key) {
                    Long slotId = key.getKey();
                    Long advertId = key.getValue();

                    // 1.当前广告位发券A次数>=500次；统计CVR=近1000次CVR*50%+近200次CVR*50%
                    Long minId = redisCache.getCacheObject(EngineRedisKeyFactory.K112.join("direct", slotId, advertId, 1000));
                    OrderDataBo advertCvrBo = advertOrderLogService.selectCvrBySlotIdAndAdvertId(slotId, advertId, 1000, minId);
                    if (null != advertCvrBo && null != advertCvrBo.getMinId()) {
                        redisCache.setCacheObject(EngineRedisKeyFactory.K112.join("direct", slotId, advertId, 1000), advertCvrBo.getMinId(), 3, TimeUnit.DAYS);
                    }
                    if (null != advertCvrBo && advertCvrBo.getAdLaunchSum() >= 500) {
                        minId = redisCache.getCacheObject(EngineRedisKeyFactory.K112.join("direct", slotId, advertId, 200));
                        OrderDataBo cvr200 = advertOrderLogService.selectCvrBySlotIdAndAdvertId(slotId, advertId, 200, minId);
                        redisCache.setCacheObject(EngineRedisKeyFactory.K112.join("direct", slotId, advertId, 200), cvr200.getMinId(), 3, TimeUnit.DAYS);
                        return advertCvrBo.getCvr() * 0.5 + cvr200.getCvr() * 0.5;
                    }
                    // 2.新广告：发券次数>200次：统计CVR=真实CVR
                    minId = redisCache.getCacheObject(EngineRedisKeyFactory.K112.join("direct", 0, advertId, 1000));
                    advertCvrBo = advertOrderLogService.selectCvrBySlotIdAndAdvertId(null, advertId, 1000, minId);
                    if (null != advertCvrBo && null != advertCvrBo.getMinId()) {
                        redisCache.setCacheObject(EngineRedisKeyFactory.K112.join("direct", 0, advertId, 1000), advertCvrBo.getMinId(), 3, TimeUnit.DAYS);
                    }
                    if (null != advertCvrBo && advertCvrBo.getAdLaunchSum() > 200) {
                        return advertCvrBo.getCvr();
                    }
                    // 3.新广告刚上线：统计CVR=广告位近1000次发券的CVR%
                    minId = redisCache.getCacheObject(EngineRedisKeyFactory.K112.join("direct", slotId, 0, 1000));
                    advertCvrBo = advertOrderLogService.selectCvrBySlotIdAndAdvertId(slotId, null, 1000, minId);
                    redisCache.setCacheObject(EngineRedisKeyFactory.K112.join("direct", slotId, 0, 1000), advertCvrBo.getMinId(), 3, TimeUnit.DAYS);
                    return advertCvrBo.getCvr();
                }

                @Override
                public ListenableFuture<Double> reload(Pair<Long, Long> key, Double oldValue) {
                    ListenableFutureTask<Double> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告位广告CTR*CVR缓存(活动使用)
     */
    private final LoadingCache<String, OrderDataBo> SLOT_AD_RATE_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build(new CacheLoader<String, OrderDataBo>() {
                @Override
                public OrderDataBo load(String key) {
                    String[] params = key.split("::");
                    Long slotId = Long.valueOf(params[0]);
                    Long advertId = Long.valueOf(params[1]);
                    Integer launchSeq = Integer.valueOf(params[2]);
                    String mobileBrand = params.length > 3 ? params[3] : "";
                    String province = params.length > 4 ? params[4] : "";
                    int timesMax = 1500;
                    int timesThreshold = 500;
                    int convThreshold = 100;

                    OrderDataBo advertCvrBo;

                    // 广告位ID, 广告ID, 发券次序, 发券 >= 400
                    Long minId = redisCache.getCacheObject(EngineRedisKeyFactory.K112.join(slotId, advertId, launchSeq, timesMax));
                    advertCvrBo = advertOrderLogService.selectCtrCvrBySlotIdAndAdvertId(slotId, advertId, launchSeq, null, null, timesMax, minId);
                    if (null != advertCvrBo && null != advertCvrBo.getMinId()) {
                        redisCache.setCacheObject(EngineRedisKeyFactory.K112.join(slotId, advertId, launchSeq, timesMax), advertCvrBo.getMinId(), 3, TimeUnit.DAYS);
                    }
                    if (null != advertCvrBo && advertCvrBo.getAdLaunchSum() >= timesThreshold) {

                        // 广告位ID, 广告ID, 发券次序, 手机品牌, 省份, (发券 >= 100 and 转化 >= 1 or 转化 >= 2 or 发券 >= 400)
                        if (StringUtils.isNotBlank(mobileBrand) && StringUtils.isNotBlank(province)) {
                            advertCvrBo = advertOrderLogService.selectCtrCvrBySlotIdAndAdvertId(slotId, advertId, launchSeq, mobileBrand, province, timesMax);
                            if (null != advertCvrBo) {
                                if (advertCvrBo.getAdLaunchSum() >= convThreshold && advertCvrBo.getLpClick() > 0
                                        || advertCvrBo.getLpClick() >= 2
                                        || advertCvrBo.getAdLaunchSum() >= timesThreshold) {
                                    return advertCvrBo;
                                }
                            }
                        }
                        // 广告位ID, 广告ID, 发券次序, 省份
                        if (StringUtils.isNotBlank(province)) {
                            advertCvrBo = advertOrderLogService.selectCtrCvrBySlotIdAndAdvertId(slotId, advertId, launchSeq, null, province, timesMax);
                            if (null != advertCvrBo) {
                                if (advertCvrBo.getAdLaunchSum() >= convThreshold && advertCvrBo.getLpClick() > 0
                                        || advertCvrBo.getLpClick() >= 2
                                        || advertCvrBo.getAdLaunchSum() >= timesThreshold) {
                                    return advertCvrBo;
                                }
                            }
                        }
                        // 广告位ID, 广告ID, 发券次序, 手机品牌
                        if (StringUtils.isNotBlank(mobileBrand)) {
                            advertCvrBo = advertOrderLogService.selectCtrCvrBySlotIdAndAdvertId(slotId, advertId, launchSeq, mobileBrand, null,  timesMax);
                            if (null != advertCvrBo) {
                                if (advertCvrBo.getAdLaunchSum() >= convThreshold && advertCvrBo.getLpClick() > 0
                                        || advertCvrBo.getLpClick() >= 2
                                        || advertCvrBo.getAdLaunchSum() >= timesThreshold) {
                                    return advertCvrBo;
                                }
                            }
                        }

                        return advertCvrBo;
                    }
                    // 广告ID, 发券次序
                    minId = redisCache.getCacheObject(EngineRedisKeyFactory.K112.join(0, advertId, launchSeq, timesMax));
                    advertCvrBo = advertOrderLogService.selectCtrCvrBySlotIdAndAdvertId(null, advertId, launchSeq, null, null, timesMax, minId);
                    if (null != advertCvrBo && null != advertCvrBo.getMinId()) {
                        redisCache.setCacheObject(EngineRedisKeyFactory.K112.join(0, advertId, launchSeq, timesMax), advertCvrBo.getMinId(), 3, TimeUnit.DAYS);
                    }
                    if (null != advertCvrBo && advertCvrBo.getAdLaunchSum() >= timesThreshold) {
                        return advertCvrBo;
                    }
                    // 广告位ID, 发券次序
                    minId = redisCache.getCacheObject(EngineRedisKeyFactory.K112.join(slotId, 0, launchSeq, timesMax));
                    advertCvrBo = advertOrderLogService.selectCtrCvrBySlotIdAndAdvertId(slotId, null, launchSeq, null, null, timesMax, minId);
                    if (null != advertCvrBo && null != advertCvrBo.getMinId()) {
                        redisCache.setCacheObject(EngineRedisKeyFactory.K112.join(slotId, 0, launchSeq, timesMax), advertCvrBo.getMinId(), 3, TimeUnit.DAYS);
                    }
                    if (null != advertCvrBo && advertCvrBo.getAdLaunchSum() >= timesThreshold) {
                        return advertCvrBo;
                    }
                    // 广告位ID, 广告ID
                    advertCvrBo = advertOrderLogService.selectCtrCvrBySlotIdAndAdvertId(slotId, advertId, null, null, null, timesMax);
                    if (null != advertCvrBo) {
                        if (advertCvrBo.getAdLaunchSum() >= convThreshold && advertCvrBo.getLpClick() > 0
                                || advertCvrBo.getLpClick() >= 2
                                || advertCvrBo.getAdLaunchSum() >= timesThreshold) {
                            return advertCvrBo;
                        }
                    }
                    // 广告ID
                    advertCvrBo = advertOrderLogService.selectCtrCvrBySlotIdAndAdvertId(null, advertId, null, null, null, timesMax);
                    if (null != advertCvrBo && advertCvrBo.getAdLaunchSum() >= timesThreshold) {
                        return advertCvrBo;
                    }
                    // 广告位ID, 发券
                    advertCvrBo = advertOrderLogService.selectCtrCvrBySlotIdAndAdvertId(slotId, null, null, null, null, timesMax);
                    return advertCvrBo;
                }

                @Override
                public ListenableFuture<OrderDataBo> reload(String key, OrderDataBo oldValue) {
                    ListenableFutureTask<OrderDataBo> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告CVR缓存(OCPC使用)
     */
    private final LoadingCache<Long, OrderDataBo> AD_CVR_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build(new CacheLoader<Long, OrderDataBo>() {
                @Override
                public OrderDataBo load(Long advertId) {
                    OrderDataBo advertCvrBo = advertOrderLogService.selectCvrByAdvertId(advertId, 1000);
                    return advertCvrBo;
                }

                @Override
                public ListenableFuture<OrderDataBo> reload(Long key, OrderDataBo oldValue) {
                    ListenableFutureTask<OrderDataBo> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 查询是否新广告位缓存(发券<=1000)
     *
     * @param slotId 广告位ID
     * @return 是否新广告位
     */
    public Boolean isNewSlotCache(Long slotId) {
        if (null != slotId) {
            try {
                return IS_NEW_SLOT_CACHE.get(slotId);
            } catch (ExecutionException e) {
                log.error("查询是否新广告位缓存, slotId={}", slotId, e);
            }
        }
        return true;
    }

    /**
     * 查询广告位广告CVR缓存(直投广告使用)
     *
     * @param slotId 广告位ID
     * @param advertId 广告ID
     * @return 广告位广告CVR
     */
    public Double getSlotAdvertCvrCache(Long slotId, Long advertId) {
        // TODO 慢SQL问题，先去掉
//        if (null != slotId && null != advertId) {
//            try {
//                return SLOT_AD_CVR_CACHE.get(Pair.of(slotId, advertId));
//            } catch (ExecutionException e) {
//                log.error("查询广告位广告CVR缓存异常, slotId={}", slotId, e);
//            }
//        }
        return 0d;
    }

    /**
     * 查询广告位广告CTR*CVR缓存(活动使用)
     *
     * @param slotId 广告位ID
     * @param advertId 广告ID
     * @param launchSeq 发券次序
     * @param mobileBrand 手机品牌
     * @param province 省份
     * @return 广告位广告CVR
     */
    public OrderDataBo getSlotAdvertCtrCvrCache(Long slotId, Long advertId, Integer launchSeq, String mobileBrand, String province) {
        if (null != slotId && null != advertId) {
            try {
                int tmpLaunchSeq = Math.min(NumberUtils.defaultInt(launchSeq, 1), 3);
                String key = StrUtil.format("{}::{}::{}::{}::{}", slotId, advertId, tmpLaunchSeq, mobileBrand, province);
                return SLOT_AD_RATE_CACHE.get(key);
            } catch (ExecutionException e) {
                log.error("查询活动的广告位广告CVR缓存异常, slotId={}, advertId={}", slotId, advertId, e);
            }
        }
        return null;
    }

    /**
     * 查询广告位广告的发券次数缓存
     * 注:直投广告使用，超过1000不更新
     *
     * @param slotId 广告位ID
     * @param advertId 广告ID
     * @return 发券次数
     */
    public Integer selectAdLaunchCacheBySlotIdAndAdvertId(Long slotId, Long advertId) {
        String key = EngineRedisKeyFactory.K078.join(slotId, advertId);
        Integer adLaunch = redisCache.getCacheObject(key);
        if (null != adLaunch && adLaunch > 1000) {
            return adLaunch;
        }
        adLaunch = advertOrderLogService.selectCvrBySlotIdAndAdvertId(slotId, advertId, 1100).getAdLaunchSum();
        redisCache.setCacheObject(key, adLaunch, 90, TimeUnit.DAYS);
        return adLaunch;
    }

    /**
     * 查询广告CTR*CVR缓存(OCPC使用)
     *
     * @param advertId 广告ID
     * @return 广告CVR
     */
    public OrderDataBo getAdvertCvrCache(Long advertId) {
        if (null != advertId) {
            try {
                return AD_CVR_CACHE.get(advertId);
            } catch (ExecutionException e) {
                log.error("查询广告CVR缓存异常, advertId={}", advertId, e);
            }
        }
        return null;
    }
}
