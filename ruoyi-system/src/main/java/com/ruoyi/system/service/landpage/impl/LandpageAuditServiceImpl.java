package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.entity.manager.LandpageAuditRecord;
import com.ruoyi.system.mapper.manager.LandpageAuditRecordMapper;
import com.ruoyi.system.req.manager.LandpageAuditRecordReq;
import com.ruoyi.system.service.landpage.LandpageAuditService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.IqiyiAuditStatus.*;

/**
 * 落地页审核服务实现
 *
 * <AUTHOR>
 * @date 2021-09-10
 */
@Deprecated
@Service
public class LandpageAuditServiceImpl implements LandpageAuditService {

    private static final Logger log = LoggerFactory.getLogger(LandpageAuditServiceImpl.class);

    @Autowired
    private LandpageAuditRecordMapper landpageAuditRecordMapper;

    // 爱奇艺落地页送审-上传接口地址
    @Value("${iqiyi.lp.uploadUrl}")
    private String iqiyiLpUploadUrl;

    // 爱奇艺落地页送审-更新接口地址
    @Value("${iqiyi.lp.updateUrl}")
    private String iqiyiLpUpdateUrl;

    // 爱奇艺落地页送审-回调地址
    @Value("${iqiyi.lp.cb}")
    private String iqiyiLpCb;

    // 爱奇艺落地页送审-业务方编号
    @Value("${iqiyi.lp.biz}")
    private String iqiyiLpBiz;

    // 爱奇艺落地页送审-业务方秘钥
    @Value("${iqiyi.lp.secretKey}")
    private String iqiyiLpSecretKey;

    @Override
    public Map<String, String> queryIqiyiUrlMap() {
        LandpageAuditRecord param = new LandpageAuditRecord();
        param.setAuditStatus(PASSED.getStatus());
        List<LandpageAuditRecord> list = landpageAuditRecordMapper.selectLandpageAuditRecordList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(LandpageAuditRecord::getOriginUrl, LandpageAuditRecord::getIqiyiUrl, (oldVal, newVal) -> newVal));
    }

    /**
     * 查询落地页送审记录列表
     *
     * @param req 请求参数
     * @return 落地页送审记录
     */
    @Override
    public List<LandpageAuditRecord> selectLandpageAuditRecordList(LandpageAuditRecordReq req) {
        LandpageAuditRecord param = BeanUtil.copyProperties(req, LandpageAuditRecord.class);
        if (null != req.getEndDate()) {
            param.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        return landpageAuditRecordMapper.selectLandpageAuditRecordList(param);
    }

    @Override
    public int landpageAudit(List<LandpageAuditRecord> urlList) {
        // 成功送审的链接数量
        int count = 0;

        // 获取链接内容，并上传到爱奇艺审核（异步处理）
        for (LandpageAuditRecord url : urlList) {
            if (StringUtils.isBlank(url.getOriginUrl())) {
                continue;
            }

            try {
                String originUrl = url.getOriginUrl();
                String originUrlMd5 = Md5Utils.hash(originUrl);

                // 判断链接是否已存在
                if (isOriginUrlExist(originUrlMd5)) {
                    continue;
                }

                // 调用爱奇艺落地页上传接口
                JSONObject resp = iqiyiLpAudit(iqiyiLpUploadUrl, originUrl);

                // 处理送审返回结果
                String reason = null;
                String fileId = null;
                if (null == resp) {
                    reason = "接口请求无返回值";
                } else if (Objects.equals(resp.getString("status"), "SUCCESS")) {
                    fileId = resp.getString("fileId");
                } else if (Objects.equals(resp.getString("status"), "ERROR")) {
                    if (null != resp.getJSONObject("msg")) {
                        reason = resp.getJSONObject("msg").getString("reason");
                    } else {
                        reason = "接口请求失败";
                    }
                }

                // 新增落地页送审记录
                LandpageAuditRecord record = new LandpageAuditRecord();
                record.setOriginUrl(originUrl);
                record.setOriginUrlMd5(originUrlMd5);
                record.setAuditApplyTime(new Date());
                record.setAuditStatus(null == reason ? IN_AUDIT.getStatus() : REFUSED.getStatus());
                record.setAuditReason(reason);
                if (null != fileId) {
                    JSONObject extInfo = new JSONObject();
                    extInfo.put("fileId", fileId);
                    record.setExtInfo(extInfo.toString());
                }
                landpageAuditRecordMapper.insertLandpageAuditRecord(record);
                count++;
            } catch (Exception e) {
                log.info("爱奇艺落地页链接送审异常: {}", url, e);
            }
        }
        return count;
    }

    @Override
    public void reAudit(Long recordId) {
        LandpageAuditRecord record = landpageAuditRecordMapper.selectLandpageAuditRecordById(recordId);
        if (null == record) {
            throw new CustomException("未查询到落地页送审记录");
        }

        try {
            String originUrl = record.getOriginUrl();
            String apiUrl = Objects.equals(record.getAuditStatus(), 1) ? iqiyiLpUpdateUrl : iqiyiLpUploadUrl;

            // 调用爱奇艺落地页上传接口
            JSONObject resp = iqiyiLpAudit(apiUrl, originUrl);

            // 处理送审返回结果
            String reason = null;
            String fileId = null;
            if (null == resp) {
                reason = "接口请求无返回值";
            } else if (Objects.equals(resp.getString("status"), "SUCCESS")) {
                fileId = resp.getString("fileId");
            } else if (Objects.equals(resp.getString("status"), "ERROR")) {
                if (null != resp.getJSONObject("msg")) {
                    reason = resp.getJSONObject("msg").getString("reason");
                } else {
                    reason = "接口请求失败";
                }
            }

            // 更新落地页送审记录
            LandpageAuditRecord updateRecord = new LandpageAuditRecord();
            updateRecord.setId(recordId);
            updateRecord.setAuditApplyTime(new Date());
            updateRecord.setAuditStatus(null == reason ? IN_AUDIT.getStatus() : REFUSED.getStatus());
            updateRecord.setAuditReason(reason);
            if (null != fileId) {
                JSONObject extInfo = JSON.parseObject(record.getExtInfo());
                if (null == extInfo) {
                    extInfo = new JSONObject();
                }
                extInfo.put("fileId", fileId);
                updateRecord.setExtInfo(extInfo.toString());
            }
            landpageAuditRecordMapper.updateLandpageAuditRecord(updateRecord);
        } catch (Exception e) {
            log.info("爱奇艺落地页链接重新送审异常: recordId={}", recordId, e);
        }
    }

    @Override
    public void callback(String content) {
        JSONObject result = JSON.parseObject(StringUtils.defaultString(content));
        if (null == result || !result.containsKey("data")) {
            return;
        }

        JSONObject data = result.getJSONObject("data");
        String status = result.getString("status");
        String iqiyiUrl = data.getString("iqiyiUrl");
        String originUrl = data.getString("originUrl");
        String reason = result.getString("reason");
        String previewUrl = result.getString("previewUrl");

        // 更新落地页送审记录
        LandpageAuditRecord record = landpageAuditRecordMapper.selectByOriginUrl(Md5Utils.hash(originUrl));
        if (null == record) {
            return;
        }
        LandpageAuditRecord updateRecord = new LandpageAuditRecord();
        updateRecord.setId(record.getId());
        updateRecord.setAuditFinishTime(new Date());
        updateRecord.setAuditStatus(Objects.equals("passed", status) ? PASSED.getStatus() : REFUSED.getStatus());
        updateRecord.setIqiyiUrl(iqiyiUrl);
        updateRecord.setAuditReason(reason);
        if (StringUtils.isNotBlank(previewUrl)) {
            JSONObject extInfo = JSON.parseObject(record.getExtInfo());
            if (null == extInfo) {
                extInfo = new JSONObject();
            }
            extInfo.put("previewUrl", previewUrl);
            updateRecord.setExtInfo(extInfo.toString());
        }
        landpageAuditRecordMapper.updateLandpageAuditRecord(updateRecord);
    }

    /**
     * 判断原落地页链接是否已存在
     *
     * @param originUrlMd5 落地页链接MD5
     * @return true.存在,false.不存在
     */
    private boolean isOriginUrlExist(String originUrlMd5) {
        return landpageAuditRecordMapper.checkOriginUrlExist(originUrlMd5) == 1;
    }

    /**
     * 爱奇艺落地页托管-上传/更新
     *
     * @param originUrl 原落地页链接
     * @return 接口返回
     */
    private JSONObject iqiyiLpAudit(String apiUrl, String originUrl) {
        long timestamp = System.currentTimeMillis();
        Map<String, Object> param = new HashMap<>();
        List<String> arr = Arrays.asList(iqiyiLpBiz, iqiyiLpSecretKey, String.valueOf(timestamp));
        arr.sort(String::compareTo);
        param.put("timestamp", timestamp);
        param.put("url", originUrl);
        param.put("file", HttpUtil.get(originUrl));
        // 简单判断是否是活动页面(1.一跳,2.二跳)
        if (originUrl.contains("/act")) {
            param.put("type", 1);
        } else {
            param.put("type", 2);
        }
        param.put("cb", iqiyiLpCb);
        param.put("biz", iqiyiLpBiz);
        param.put("sign", SecureUtil.sha1(Joiner.on("").join(arr)));
        String resp = HttpUtil.post(apiUrl, param);
        log.info("爱奇艺落地页链接送审: apiUrl={}, param={}, resp={}", apiUrl, JSON.toJSONString(param), resp);
        return JSON.parseObject(resp);
    }
}
