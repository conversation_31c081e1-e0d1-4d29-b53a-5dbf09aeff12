package com.ruoyi.system.service.common;

import com.ruoyi.common.enums.common.SwitchType;

/**
 * 开关服务接口
 * TODO 还未测试
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
public interface SwitchService {

    /**
     * 获取开关
     *
     * @param type 开关类型
     * @return 开关状态
     */
    Integer getSwitch(SwitchType type);

    /**
     * 设置开关
     *
     * @param type 开关类型
     * @param status 开关状态
     * @return 是否设置成功
     */
    boolean setSwitch(SwitchType type, Integer status);

    /**
     * 切换开关状态
     *
     * @param type 开关
     * @return 是否切换成功
     */
    boolean toggleSwitch(SwitchType type);

    /**
     * 开关是否开启
     *
     * @param type 开关类型
     * @return true.开启,false.关闭
     */
    boolean isSwitchOn(SwitchType type);

    /**
     * 开关是否关闭
     *
     * @param type 开关类型
     * @return true.关闭,false.开启
     */
    boolean isSwitchOff(SwitchType type);
}
