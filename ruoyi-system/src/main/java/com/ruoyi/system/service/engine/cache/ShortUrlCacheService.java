package com.ruoyi.system.service.engine.cache;

import cn.hutool.core.lang.Pair;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.enums.ShortUrlTypeEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.shorturl.ShortUrlEntity;
import com.ruoyi.system.service.shorturl.ShortUrlService;
import com.ruoyi.system.util.ShortUrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.ShortUrlStatus.ENABLE;
import static com.ruoyi.common.enums.ShortUrlStatus.isEnable;

/**
 * 短链缓存服务
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
@Slf4j
@Service
public class ShortUrlCacheService implements ApplicationListener<ContextRefreshedEvent> {

    /**
     * 短链布隆过滤器
     */
    private BloomFilter<String> bloomFilter = null;

    /**
     * 默认空链接
     */
    private static final Pair<Integer, String> EMPTY_URL = Pair.of(ShortUrlTypeEnum.DEFAULT.getType(), "");

    @Autowired
    private ShortUrlService shortUrlService;

    /**
     * 短链缓存
     */
    private final LoadingCache<String, Pair<Integer, String>> SHORT_URL_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(1, TimeUnit.HOURS)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build(new CacheLoader<String, Pair<Integer, String>>() {
                @Override
                public Pair<Integer, String> load(String idStr) {
                    long dStr = ShortUrlUtils.shortUrlUrlToId(idStr);
                    if (StringUtils.isNumeric(String.valueOf(dStr))) {
                        Long id = Long.valueOf(dStr);
                        ShortUrlEntity shortUrl = shortUrlService.selectById(id);
                        if (null != shortUrl && isEnable(shortUrl.getUrlStatus())) {
                            return Pair.of(shortUrl.getUrlType(), shortUrl.getOriginUrl());
                        }
                    }
                    return EMPTY_URL;
                }

                @Override
                public ListenableFuture<Pair<Integer, String>> reload(String idStr, Pair<Integer, String> oldValue) {
                    ListenableFutureTask<Pair<Integer, String>> task = ListenableFutureTask.create(() -> load(idStr));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 查询短链缓存
     *
     * @param idStr 短链编码后的ID
     * @return 原链接
     */
    public Pair<Integer, String> selectShortUrlCache(String idStr) {
        if (StringUtils.isNotBlank(idStr)) {
            try {
                return SHORT_URL_CACHE.get(idStr);
            } catch (Exception e) {
                log.error("查询短链缓存异常, idStr={}", idStr, e);
            }
        }
        return EMPTY_URL;
    }

    /**
     * 检查短链是否存在
     *
     * @param idStr 短链编码后的ID
     * @return 是否存在
     */
    public boolean isShortUrlExist(String idStr) {
        return StringUtils.isNotBlank(idStr) && bloomFilter.mightContain(idStr);
    }

    /**
     * 刷新短链缓存
     *
     * @param shortUrlId 短链ID
     */
    public void refreshShortUrlCache(Long shortUrlId) {
        if (null != shortUrlId) {
            String idStr = ShortUrlUtils.shortUrlIdToUrl(shortUrlId);
            SHORT_URL_CACHE.refresh(idStr);
            bloomFilter.put(idStr);
        }
    }

    /**
     * 初始化短链缓存
     */
    private void initShortUrlCache() {
        // 查询有效短链
        ShortUrlEntity param = new ShortUrlEntity();
        param.setUrlStatus(ENABLE.getStatus());
        List<ShortUrlEntity> shortUrlList = shortUrlService.selectList(param);

        // 初始化布隆过滤器
        bloomFilter = BloomFilter.create(Funnels.stringFunnel(Charset.defaultCharset()), shortUrlList.size());

        // 初始化短链缓存
        shortUrlList.forEach(shortUrl -> {
            String idStr = ShortUrlUtils.shortUrlIdToUrl(shortUrl.getId());
            SHORT_URL_CACHE.put(idStr, Pair.of(shortUrl.getUrlType(), shortUrl.getOriginUrl()));
            bloomFilter.put(idStr);
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        if (contextRefreshedEvent.getApplicationContext().getParent() == null) {
            log.info("ShortUrlCacheService.initCacheCache started.");
            initShortUrlCache();
            log.info("ShortUrlCacheService.initCacheCache finished.");
        }
    }
}
