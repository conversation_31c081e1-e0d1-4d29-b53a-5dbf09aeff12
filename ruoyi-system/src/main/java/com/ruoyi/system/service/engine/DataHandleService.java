package com.ruoyi.system.service.engine;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.InnerLogType;

/**
 * 数据处理服务接口
 *
 * <AUTHOR>
 * @date 2021/07/21
 */
public interface DataHandleService {

    /**
     * 统计并累加广告位请求数据
     */
    void addSlotRequestData(Long accountId, Long appId, Long slotId, String deviceId);

    /**
     * 统计并累加pv/uv数据
     */
    void addPvUvData(InnerLogType type);

    /**
     * 统计并累加pv/uv数据
     */
    void addPvUvData(InnerLogType type, JSONObject logJson);

    /**
     * 统计并累加pv数据
     */
    void addPvData(InnerLogType type, JSONObject logJson);
}
