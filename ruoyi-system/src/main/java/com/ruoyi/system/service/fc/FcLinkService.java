package com.ruoyi.system.service.fc;

import com.ruoyi.common.core.domain.Result;
import com.ruoyi.system.bo.landpage.article.ArticleCacheBo;
import com.ruoyi.system.entity.fc.FcLinkEntity;
import com.ruoyi.system.req.fc.FcCheckArticleReq;
import com.ruoyi.system.req.fc.GetArticleListReq;
import com.ruoyi.system.vo.fc.FcArticleVo;
import com.ruoyi.system.vo.fc.FcLinkVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * 丰巢聚合链接表(FcLink)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-05 15:38:00
 */
public interface FcLinkService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    FcLinkEntity queryById(Long id);

    /**
     * 分页查询
     *
     * @param fcLinkEntity      筛选条件
     * @param pageRequest 分页对象
     * @return 查询结果
     */
    Page<FcLinkEntity> queryByPage(FcLinkEntity fcLinkEntity, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param fcLinkEntity 实例对象
     * @return 实例对象
     */
    FcLinkEntity insert(FcLinkEntity fcLinkEntity);

    /**
     * 修改数据
     *
     * @param fcLinkEntity 实例对象
     * @return 实例对象
     */
    FcLinkEntity update(FcLinkEntity fcLinkEntity);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 添加丰巢链接
     * @param name
     * @return
     */
    Result addFcLink(String name);

    /**
     * 生成唯一key
     * @return
     */
    String generateKey();

    /**
     * 删除丰巢链接
     * @param id
     * @return
     */
    Result removeFcLink(Long id);

    /**
     * 根据丰巢链接key获取文章
     * @param userId
     * @param fcLinkKey
     * @return
     */
    ArticleCacheBo getArticleByfcLinkKey(String userId, String fcLinkKey);

    /**
     * 删除丰巢链接的文章缓存
     * @param fcLinkKey
     */
    void deleteFcLinkArticleCache(String fcLinkKey);

    /**
     * 删除丰巢链接的文章聚合链接缓存
     * @param fcLinkKey
     */
    void deleteFcLinkAggrLinkCaChe(String fcLinkKey);

    /**
     * 通过文章id删除丰巢文章缓存
     * @param articleId
     */
    void deleteFcLinkArticleCacheByArticleId(Long articleId);

    /**
     * 丰巢链接文章审核
     * @param fcCheckArticleReq
     * @return
     */
    Result fcCheckArticle(FcCheckArticleReq fcCheckArticleReq);

    /**
     * 根据状态获取所有丰巢链接key
     * @param status
     * @return
     */
    List<String> getFcLinkKeysByStatus(Integer status);

    /**
     * 获取文章列表
     * @return
     */
    List<FcArticleVo> getArticleByStatusAndTime(GetArticleListReq req);

    /**
     * 查询丰巢链接列表（id、key、url、name），按id降序排列
     * @return 丰巢链接列表
     */
    List<FcLinkVo> getFcLinkListOrderByIdDesc(String searchKey);

    /**
     * 通过聚合链接id获取丰巢链接详细信息
     * @param articleAggrLinkIdList
     * @return
     */
    List<FcLinkVo> getFcLinkInfoByArticleAggrLinkIdList(List<Long> articleAggrLinkIdList, List<Long> fcLinkIdList);

    /**
     * 丰巢链接统计
     */
    void fcLinkStatistic(String fcLinkKey, String userId);

    /**
     * 根据key查询丰巢链接
     * @param key 丰巢链接key
     * @return 丰巢链接实体
     */
    FcLinkEntity queryByKey(String key);

}
