package com.ruoyi.system.service.advert.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.advert.AdvertPreConsumeService;
import com.ruoyi.system.entity.advert.AdvertPreConsumeEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Objects;

import com.ruoyi.system.mapper.advert.AdvertPreConsumeMapper;

/**
 * 广告消耗预扣表 Service
 *
 * <AUTHOR>
 * @date 2023-12-25 19:54:55
 */
@Slf4j
@Service
public class AdvertPreConsumeServiceImpl implements AdvertPreConsumeService {

    @Autowired
    private AdvertPreConsumeMapper advertPreConsumeMapper;

    @Override
    public Boolean insert(AdvertPreConsumeEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return advertPreConsumeMapper.insert(entity) > 0;
    }

    @Override
    public AdvertPreConsumeEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return advertPreConsumeMapper.selectById(id);
    }

    @Override
    public AdvertPreConsumeEntity selectBy(Date date, Long orientId) {
        if (null == date || null == orientId) {
            return null;
        }
        return advertPreConsumeMapper.selectBy(date, orientId);
    }

    @Override
    public boolean preConsume(Long id, Integer milliConsumeAdd) {
        if (null == id || null == milliConsumeAdd || 0 == milliConsumeAdd) {
            return false;
        }
        try {
            return advertPreConsumeMapper.preConsume(id, milliConsumeAdd) > 0;
        } catch (Exception e) {
            log.error("preConsume error, id={}, milliConsumeAdd={}", id, milliConsumeAdd, e);
        }
        return false;
    }
}
