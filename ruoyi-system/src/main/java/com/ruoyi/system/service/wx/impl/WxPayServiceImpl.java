package com.ruoyi.system.service.wx.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ijpay.core.enums.SignType;
import com.ijpay.core.kit.WxPayKit;
import com.ruoyi.common.config.WxPayConfig;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.service.wx.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 微信支付Service实现
 *
 * <AUTHOR>
 * @date 2023-3-21
 */
@Slf4j
@Service
public class WxPayServiceImpl implements WxPayService {

    @Autowired
    private RedisCache redisCache;

    @Override
    public Map<String, String> getJSParams(String payAppId, String prepayId) {
        // JSAPI加密参数
        Map<String, String> jsParams = MapUtil.builder(new HashMap<String, String>())
                .put("appId", payAppId)
                .put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000))
                .put("nonceStr", WxPayKit.generateStr())
                .put("package", "prepay_id=" + prepayId)
                .put("signType", SignType.HMACSHA256.getType())
                .build();
        String paySign = WxPayKit.createSign(jsParams, WxPayConfig.getPartnerKey(), SignType.HMACSHA256, null);
        jsParams.put("paySign", paySign);
        return jsParams;
    }

    @Override
    public Map<String, String> getJSConfig(String url) {
        if (StringUtils.isBlank(url)) {
            return Collections.emptyMap();
        }
        String nonceStr = WxPayKit.generateStr();
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);

        String signature = SecureUtil.sha1(
                MapUtil.builder(new HashMap<String, String>())
                        .put("noncestr", nonceStr)
                        .put("jsapi_ticket", getJsapiTicket())
                        .put("timestamp", timestamp)
                        .put("url", url)
                        .build().entrySet()
                        .stream().sorted(Map.Entry.comparingByKey()).map(s -> s.getKey() + "=" + s.getValue()).collect(Collectors.joining("&"))
        );

        return MapUtil.builder(new HashMap<String, String>())
                .put("appId", WxPayConfig.getGzhAppId())
                .put("timestamp", timestamp)
                .put("nonceStr", nonceStr)
                .put("signature", signature)
                .build();
    }

    @Override
    public String getOpenid(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        String key = EngineRedisKeyFactory.K058.join(code);
        String value = redisCache.getCacheObject(key);
        if (StringUtils.isNotBlank(value)) {
            JSONObject result = JSON.parseObject(value);
            if (null != result && StringUtils.isNotBlank(result.getString("openid"))) {
                return result.getString("openid");
            }
        }

        String resp = HttpUtil.get(StrUtil.format("https://api.weixin.qq.com/sns/oauth2/access_token?appid={}&secret={}&code={}&grant_type=authorization_code", WxPayConfig.getGzhAppId(), WxPayConfig.getGzhSecret(), code));
        log.info("微信支付公众号获取用户openid: {}", resp);
        JSONObject result = JSON.parseObject(resp);
        if (null != result) {
            redisCache.setCacheObject(key, resp, result.getIntValue("expires_in"), TimeUnit.SECONDS);
            return result.getString("openid");
        }
        return "";
    }

    @Override
    public String getAccessToken() {
        String key = EngineRedisKeyFactory.K056.join(WxPayConfig.getGzhAppId());
        String accessToken = redisCache.getCacheObject(key);
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }

        String resp = HttpUtil.get(StrUtil.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={}&secret={}", WxPayConfig.getGzhAppId(), WxPayConfig.getGzhSecret()));
        log.info("微信支付JSAPI获取accessToken: {}", resp);
        if (StringUtils.isNotBlank(resp)) {
            JSONObject result = JSON.parseObject(resp);
            accessToken = result.getString("access_token");
            if (StringUtils.isNotBlank(accessToken)) {
                redisCache.setCacheObject(key, accessToken, result.getIntValue("expires_in"), TimeUnit.SECONDS);
                return accessToken;
            } else {
                log.error("微信支付JSAPI获取accessToken失败, {}", resp);
            }
        }
        return "";
    }

    @Override
    public String getJsapiTicket() {
        String key = EngineRedisKeyFactory.K057.join(WxPayConfig.getGzhAppId());
        String accessToken = redisCache.getCacheObject(key);
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }

        String resp = HttpUtil.get(StrUtil.format("https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token={}&type=jsapi", getAccessToken()));
        log.info("微信支付JSAPI获取jsapi_ticket: {}", resp);
        if (StringUtils.isNotBlank(resp)) {
            JSONObject result = JSON.parseObject(resp);
            accessToken = result.getString("ticket");
            if (StringUtils.isNotBlank(accessToken)) {
                redisCache.setCacheObject(key, accessToken, result.getIntValue("expires_in"), TimeUnit.SECONDS);
                return accessToken;
            } else {
                log.error("微信支付JSAPI获取jsapi_ticket, {}", accessToken);
            }
        }
        return "";
    }
}
