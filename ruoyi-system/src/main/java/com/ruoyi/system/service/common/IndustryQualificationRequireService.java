package com.ruoyi.system.service.common;

import com.ruoyi.system.entity.common.IndustryQualificationRequireEntity;

import java.util.List;
import java.util.Map;

/**
 * 行业资质要求表 Service
 *
 * <AUTHOR>
 * @date 2023-3-6 17:34:59
 */
public interface IndustryQualificationRequireService {

    /**
     * 查询列表
     */
    List<IndustryQualificationRequireEntity> selectList(IndustryQualificationRequireEntity param);

    /**
     * 新增记录
     */
    Integer insert(IndustryQualificationRequireEntity entity);

    /**
     * 根据id更新
     */
    Integer updateById(IndustryQualificationRequireEntity entity);

    /**
     * 根据id获取
     */
    IndustryQualificationRequireEntity selectById(Long id);

    /**
     * 批量查询行业的资质要求
     *
     * @param industryIds 行业ID列表
     * @return 行业ID-资质要求映射
     */
    Map<Long, List<IndustryQualificationRequireEntity>> selectMapByIndustryIds(List<Long> industryIds);

    /**
     * 获取行业必填的资质要求
     *
     * @param industryId 行业ID
     * @return 资质要求ID列表
     */
    List<Long> selectMustRequireIdByIndustryId(Long industryId);

    /**
     * 批量获取行业必填的资质要求
     *
     * @return 资质要求ID列表
     */
    Map<Long, List<Long>> selectMustRequireIdMap();
}
