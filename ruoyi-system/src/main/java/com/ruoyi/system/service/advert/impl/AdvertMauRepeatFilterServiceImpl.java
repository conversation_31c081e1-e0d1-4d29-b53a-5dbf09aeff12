package com.ruoyi.system.service.advert.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alipay.api.AlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.service.common.WhitelistService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.advert.AdvertMauRepeatFilterService;
import com.ruoyi.system.entity.advert.AdvertMauRepeatFilterEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.ruoyi.system.mapper.advert.AdvertMauRepeatFilterMapper;

/**
 * 广告MAU去重 Service 实现
 *
 * <AUTHOR>
 * @date 2024-11-5 11:37:22
 */
@Slf4j
@Service
public class AdvertMauRepeatFilterServiceImpl implements AdvertMauRepeatFilterService {

    @Autowired
    private AlipayClient alipayClient;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertMauRepeatFilterMapper advertMauRepeatFilterMapper;

    @Override
    public String codeToOpenId(String code, Long consumerId, String openId) {
        if (null == consumerId || (StringUtils.isBlank(code) && StringUtils.isBlank(openId))) {
            return "";
        }
        try {
            if (StringUtils.isBlank(openId)) {
                openId = getAlipayOpenId(code);
            }
            if (StringUtils.isNotBlank(openId)) {
                redisCache.setCacheObject(EngineRedisKeyFactory.K147.join(consumerId), openId, 60, TimeUnit.DAYS);
            }
            return openId;
        } catch (Exception e) {
            log.error("AdvertMauRepeatFilterServiceImpl.codeToOpenId error, code={}, consumerId={}", code, consumerId);
        }
        return "";
    }

    @Override
    public void initMauCache() {
        GlobalThreadPool.executorService.submit(() -> {
            RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K067.join("AdvertRepeatFilter"), 600);
            if (null == lock) {
                return;
            }
            try {
                // 数据库的数据刷入缓存
                AdvertMauRepeatFilterEntity param = new AdvertMauRepeatFilterEntity();
                param.setLastClickTime(DateUtil.offsetDay(new Date(), -31));
                param.setId(0L);
                List<AdvertMauRepeatFilterEntity> list;
                do {
                    list = selectList(param);
                    for (AdvertMauRepeatFilterEntity entity : list) {
                        param.setId(Math.max(param.getId(), entity.getId()));
                        redisCache.setCacheObject(EngineRedisKeyFactory.K145.join(entity.getAdvertId(), entity.getOpenId()), entity.getLastClickTime().getTime(), 60, TimeUnit.DAYS);
                        redisCache.setCacheObject(EngineRedisKeyFactory.K145.join(entity.getAdvertId(), entity.getConsumerId()), entity.getLastClickTime().getTime(), 60, TimeUnit.DAYS);
                    }
                } while (list.size() >= 900);
                redisCache.setCacheObject(EngineRedisKeyFactory.K146.toString(), System.currentTimeMillis());
            } catch (Exception e) {
                log.error("RepeatClickFilter initCache error", e);
            }
        });
    }

    @Override
    public void saveMau(Order order) {
        // 广告白名单-MAU去重缓存
        GlobalThreadPool.executorService.execute(() -> {
            if (whitelistService.contains(WhitelistType.MAU_REPEAT_FILTER_ADVERT, order.getAdvertId())) {
                redisCache.setCacheObject(EngineRedisKeyFactory.K145.join(order.getAdvertId(), order.getConsumerId()), System.currentTimeMillis());
                String openId = redisCache.getCacheObject(EngineRedisKeyFactory.K147.join(order.getConsumerId()));
                if (StringUtils.isNotBlank(openId)) {
                    redisCache.setCacheObject(EngineRedisKeyFactory.K145.join(order.getAdvertId(), openId), System.currentTimeMillis());
                    insert(order, openId);
                }
            }
        });
    }

    @Override
    public Boolean insert(Order order, String openId) {
        AdvertMauRepeatFilterEntity entity = BeanUtil.copyProperties(order, AdvertMauRepeatFilterEntity.class);
        entity.setOpenId(openId);
        entity.setLastClickTime(new Date());
        return advertMauRepeatFilterMapper.insert(entity) > 0;
    }

    @Override
    public Boolean insert(AdvertMauRepeatFilterEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return advertMauRepeatFilterMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(AdvertMauRepeatFilterEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return advertMauRepeatFilterMapper.updateById(entity) > 0;
    }

    @Override
    public AdvertMauRepeatFilterEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return advertMauRepeatFilterMapper.selectById(id);
    }

    @Override
    public AdvertMauRepeatFilterEntity selectByOpenId(String openId) {
        if (StringUtils.isBlank(openId)) {
            return null;
        }
        return advertMauRepeatFilterMapper.selectByOpenId(openId);
    }

    @Override
    public List<AdvertMauRepeatFilterEntity> selectList(AdvertMauRepeatFilterEntity entity) {
        return advertMauRepeatFilterMapper.selectList(entity);
    }

    /**
     * 获取支付宝小程序openId
     */
    private String getAlipayOpenId(String code) {
        try {
            AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
            request.setCode(code);
            request.setGrantType("authorization_code");
            AlipaySystemOauthTokenResponse response = alipayClient.execute(request);

            if (response.isSuccess()) {
                return response.getOpenId();
            } else {
                log.error("获取支付宝小程序openId失败, code={}, resp={}", code, response.getBody());
                // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
                // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                // System.out.println(diagnosisUrl);
            }
        } catch (Exception e) {
            log.error("获取支付宝小程序openId异常, code={}", code, e);
        }
        return "";
    }
}
