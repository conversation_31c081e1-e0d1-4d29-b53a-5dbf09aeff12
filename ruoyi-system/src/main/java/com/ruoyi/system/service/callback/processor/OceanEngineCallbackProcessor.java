package com.ruoyi.system.service.callback.processor;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.OCEANENGINE;

/**
 * 巨量上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/17
 */
@Slf4j
@Service
public class OceanEngineCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final String ACTIVATE_URL = "https://analytics.oceanengine.com/api/v2/conversion";

    @Override
    public CallbackProcessorTypeEnum getType() {
        return OCEANENGINE;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("clickid"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            String clickId = context.getParam().getSlotParam().getString("clickid");
            String eventType = "form";
            JSONObject body = buildBody(clickId, eventType);

            String resp = HttpUtil.createPost(ACTIVATE_URL)
                    .header("Content-Type", "application/json; charset=utf-8")
                    .body(body.toString())
                    .execute().body();
            log.info("{}接口上报, req={}, resp={}", getType().getName(), body, resp);
            if (null != resp) {
                return true;
            }
            log.error("{}接口上报失败, req={}, resp={}", getType().getName(), body, resp);
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }

    private JSONObject buildBody(String clickId, String eventType) {
        JSONObject ad = new JSONObject();
        ad.put("callback", clickId);

        JSONObject context = new JSONObject();
        context.put("ad", ad);

        JSONObject body = new JSONObject();
        body.put("event_type", eventType);
        body.put("context", context);

        return body;
    }
}
