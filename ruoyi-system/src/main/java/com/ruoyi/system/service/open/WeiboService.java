package com.ruoyi.system.service.open;

/**
 * 微博对接Service接口
 *
 * <AUTHOR>
 * @date 2022/07/19
 */
public interface WeiboService {

    /**
     * 获取并缓存令牌
     *
     * @param code 授权码
     * @param clientId 应用ID
     */
    void getAndCacheToken(String code, String clientId);

    /**
     * 获取访问令牌
     *
     * @param slotId 广告位ID
     * @return 访问令牌
     */
    String getAccessToken(Long slotId);

    /**
     * 刷新令牌
     *
     * @param slotId 广告位ID
     * @return 新的令牌
     */
    String refreshToken(Long slotId);

    /**
     * 用户行为上传
     */
    String behaviorUpload(String orderId, Long slotId, String markId);
}
