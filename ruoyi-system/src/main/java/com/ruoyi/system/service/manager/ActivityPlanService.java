package com.ruoyi.system.service.manager;

import com.ruoyi.system.entity.activity.ActivityPlan;
import com.ruoyi.system.req.activity.ActivityPlanReq;
import com.ruoyi.system.req.activity.ActivityPlanStatusReq;

import java.util.List;
import java.util.Map;

/**
 * 活动推广计划Service接口
 *
 * <AUTHOR>
 * @date 2021-07-16
 */
public interface ActivityPlanService {
    /**
     * 查询活动推广计划
     *
     * @param id 活动推广计划ID
     * @return 活动推广计划
     */
    ActivityPlan selectActivityPlanById(Long id);

    /**
     * 查询活动推广计划
     *
     * @param activityId 活动工具ID
     * @return 活动推广计划
     */
    ActivityPlan selectByActivityId(Long activityId);

    /**
     * 查询活动推广计划列表
     *
     * @param activityPlan 活动推广计划
     * @return 活动推广计划集合
     */
    List<ActivityPlan> selectActivityPlanList(ActivityPlan activityPlan);

    /**
     * 新增活动推广计划
     *
     * @param req 活动推广计划
     * @return 结果
     */
    int insertActivityPlan(ActivityPlanReq req);

    /**
     * 修改活动推广计划
     *
     * @param activityPlan 活动推广计划
     * @return 结果
     */
    int updateActivityPlan(ActivityPlan activityPlan);

    /**
     * 更新活动推广计划状态
     *
     * @param req 请求参数
     * @return 结果
     */
    int updateStatus(ActivityPlanStatusReq req);

    /**
     * 根据活动ID列表批量查询活动推广计划
     *
     * @param activityIds 活动ID列表
     * @return 活动ID-活动推广计划映射
     */
    Map<Long, ActivityPlan> selectByActivityIds(List<Long> activityIds);
}
