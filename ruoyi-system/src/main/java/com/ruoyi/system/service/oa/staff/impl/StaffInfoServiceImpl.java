package com.ruoyi.system.service.oa.staff.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.PostStaffNumBo;
import com.ruoyi.system.entity.oa.staff.StaffInfoEntity;
import com.ruoyi.system.mapper.oa.staff.StaffInfoMapper;
import com.ruoyi.system.service.oa.staff.StaffInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * oa员工信息表 Service
 *
 * <AUTHOR>
 * @date 2021-10-9 14:20:23
 */
@Service
public class StaffInfoServiceImpl implements StaffInfoService {

    @Autowired
    private StaffInfoMapper staffInfoMapper;

    @Override
    public StaffInfoEntity selectByUserId(Long userId){
        if(Objects.isNull(userId)){
            return null;
        }
        return staffInfoMapper.selectByUserId(userId);
    }

    @Override
    public Long selectPostIdByUserId(Long userId) {
        return Optional.ofNullable(selectByUserId(userId)).map(StaffInfoEntity::getPostId).orElse(null);
    }

    @Override
    public List<StaffInfoEntity> selectByDepartmentId(Long companyId,Long departmentId) {
        return staffInfoMapper.selectByDepartmentId(companyId,departmentId);
    }

    @Override
    public List<StaffInfoEntity> selectByUserIds(List<Long> userIds) {
        if(CollectionUtils.isEmpty(userIds)){
            return Collections.emptyList();
        }
        return staffInfoMapper.selectByUserIds(userIds);
    }

    @Override
    public Map<Long, Integer> selectPostStaffNumMap(List<Long> postIds) {
        if (CollectionUtils.isEmpty(postIds)) {
            return Collections.emptyMap();
        }

        List<PostStaffNumBo> list = staffInfoMapper.selectPostStaffNumByPostIds(postIds);
        return list.stream().collect(Collectors.toMap(PostStaffNumBo::getPostId, PostStaffNumBo::getNum, (v1, v2) -> v1));
    }

    @Override
    public Map<Long, Long> selectDepartmentMapByUserIds(List<Long> userIds) {
        List<StaffInfoEntity> list = selectByUserIds(userIds);
        return list.stream().collect(Collectors.toMap(StaffInfoEntity::getUserId, StaffInfoEntity::getDepartmentId, (v1, v2) -> v1));
    }

    @Override
    public Map<Long, Long> selectPostMapByUserIds(List<Long> userIds) {
        List<StaffInfoEntity> list = selectByUserIds(userIds);
        return list.stream().collect(Collectors.toMap(StaffInfoEntity::getUserId, StaffInfoEntity::getPostId, (v1, v2) -> v1));
    }

    @Override
    public StaffInfoEntity selectByPhone(String phone) {
        if(StringUtils.isBlank(phone)){
            return null;
        }
        return staffInfoMapper.selectByPhone(phone);
    }

    @Override
    public List<String> selectEmailByDepartmentIds(List<Long> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Collections.emptyList();
        }
        return staffInfoMapper.selectEmailByDepartmentIds(departmentIds);
    }
}
