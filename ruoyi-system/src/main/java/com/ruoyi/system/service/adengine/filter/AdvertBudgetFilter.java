package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.service.adengine.AdvertFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 广告每日预算过滤
 */
@Component
public class AdvertBudgetFilter implements AdvertFilter {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();
        String dateStr = context.getDateStr();

        if (null == ad.getAdvertBudget()) {
            return true;
        }
        if (ad.getAdvertBudget() <= 0) {
            return false;
        }

        // 直投发券预扣: 发券消耗 + 单价 <= 广告预算 时可以正常发券
        if (Objects.equals(context.getActivityId(), 0L)) {
            Long launchConsume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K080.join(dateStr, ad.getAdvertId())));
            if (launchConsume + ad.getUnitPrice() > ad.getAdvertBudget()) {
                return false;
            }
        }

        // 预算控制
        Long consume = redisAtomicClient.getLong(EngineRedisKeyFactory.K086.join(dateStr, ad.getAdvertId()));
        if (null == consume) {
            return true;
        }
        if (null != ad.getMilliUnitPrice() && ad.getMilliUnitPrice() % 100 > 0) {
            return consume + ad.getUnitPrice() <= ad.getAdvertBudget() &&
                    consume * 100 + ad.getMilliUnitPrice() <= ad.getAdvertBudget() * 100L;
        }
        return consume + ad.getUnitPrice() <= ad.getAdvertBudget();
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.ADVERT_BUDGET;
    }
}
