package com.ruoyi.system.service.ai.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.entity.ai.AiLiuziEntity;
import com.ruoyi.system.mapper.ai.AiLiuziMapper;
import com.ruoyi.system.req.ai.AiLiuziListReq;
import com.ruoyi.system.service.ai.AiLiuziService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * ai 留资service
 * <AUTHOR>
 * @date 2024/1/2 15:15
 */
@Service
public class AiLiuziServiceImpl implements AiLiuziService {
    @Autowired
    private AiLiuziMapper aiLiuziMapper;
    @Override
    public boolean insert(AiLiuziEntity entity) {
        return aiLiuziMapper.insert(entity) > 0;
    }

    @Override
    public boolean updateById(AiLiuziEntity entity) {
        return aiLiuziMapper.updateById(entity) > 0;
    }

    @Override
    public List<AiLiuziEntity> selectList(AiLiuziListReq req) {
        if(Objects.nonNull(req.getEndDate())){
            req.setEndDate(DateUtil.endOfDay(req.getEndDate()));
        }
        return aiLiuziMapper.selectList(req);
    }
}
