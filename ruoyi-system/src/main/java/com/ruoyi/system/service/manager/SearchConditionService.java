package com.ruoyi.system.service.manager;

import java.util.List;

/**
 * 查询条件过滤接口
 *
 * <AUTHOR>
 * @date 2023-09-20
 */
public interface SearchConditionService {

    /**
     * 通过数据权限过滤广告ID列表
     *
     * @param originAdvertIds 原广告ID列表
     * @return 过滤后的广告ID列表
     */
    List<Long> filterAdvertIdsByDatePermission(List<Long> originAdvertIds);

    /**
     * 通过负责人过滤广告ID列表
     *
     * @param originAdvertIds 原广告ID列表
     * @param managerIds 负责人ID列表
     * @return 过滤后的广告ID列表
     */
    List<Long> filterAdvertIdsByManagerIds(List<Long> originAdvertIds, List<Long> managerIds);

    /**
     * 通过广告ID/名称关键词过滤广告ID列表
     *
     * @param originAdvertIds 原广告ID列表
     * @param advertSearch 广告ID/名称关键词
     * @return 过滤后的广告ID列表
     */
    List<Long> filterAdvertIdsByAdvertSearch(List<Long> originAdvertIds, String advertSearch);

    /**
     * 通过广告主/代理商过滤广告ID列表
     *
     * @param originAdvertIds 原广告ID列表
     * @param advertiserIds 广告主ID列表
     * @param agentIds 代理商ID列表
     * @return 过滤后的广告ID列表
     */
    List<Long> filterAdvertIdsByAdvertiserOrAgent(List<Long> originAdvertIds, List<Long> advertiserIds, List<Long> agentIds);

    /**
     * 通过配置ID/名称关键词过滤配置ID列表
     *
     * @param originOrientIds 原配置ID列表
     * @param orientSearch 配置ID/名称关键词
     * @return 过滤后的配置ID列表
     */
    List<Long> filterOrientIdsByOrientSearch(List<Long> originOrientIds, String orientSearch);

    /**
     * 通过媒体名称过滤媒体ID列表
     *
     * @param originAppIds 原媒体ID列表
     * @param appName 媒体名称
     * @return 过滤后的媒体ID列表
     */
    List<Long> filterAppIdsByAppName(List<Long> originAppIds, String appName);

    /**
     * 通过媒体ID/名称关键词过滤广告位ID列表
     *
     * @param originSlotIds 原广告位ID列表
     * @param appSearch 媒体ID/名称关键词
     * @return 过滤后的广告位ID列表
     */
    List<Long> filterSlotIdsByAppSearch(List<Long> originSlotIds, String appSearch);

    /**
     * 通过广告位ID/名称关键词过滤广告位ID列表
     *
     * @param originSlotIds 原广告位ID列表
     * @param slotSearch 广告位ID/名称关键词
     * @return 过滤后的广告位ID列表
     */
    List<Long> filterSlotIdsBySlotSearch(List<Long> originSlotIds, String slotSearch);
}
