package com.ruoyi.system.service.finance.impl;

import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.account.finance.AccountRevenueEntity;
import com.ruoyi.system.mapper.finance.AccountRevenueMapper;
import com.ruoyi.system.service.finance.AccountRevenueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 账户收益表 Service
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:30
 */
@Service
public class AccountRevenueServiceImpl implements AccountRevenueService{

    @Autowired
    private AccountRevenueMapper accountRevenueMapper;

    @Override
    public AccountRevenueEntity selectByAccountId(Long accountId) {
        if(NumberUtils.isNullOrLteZero(accountId)){
            throw new CustomException(ErrorCode.ARGS);
        }

        return accountRevenueMapper.selectByAccountId(accountId);
    }

    @Override
    public boolean addRevenue(Long accountId, Integer revenue, Integer withdrawableAmount) {
        if(NumberUtils.isNullOrLteZero(accountId) || NumberUtils.isNullOrLteZero(revenue)){
            return false;
        }
        return accountRevenueMapper.addRevenue(accountId, revenue, NumberUtils.defaultInt(withdrawableAmount)) > 0;
    }

    @Override
    public boolean addPrepayAmount(Long accountId, Integer prepayAmount) {
        if (null == accountId || null == prepayAmount) {
            return false;
        }
        return accountRevenueMapper.addPrepayAmount(accountId, prepayAmount) > 0;
    }

    @Override
    public boolean deductionWithdrawAmount(Long accountId, Integer amount) {
        if(NumberUtils.isNullOrLteZero(accountId) || Objects.isNull(amount)){
            return false;
        }
        return accountRevenueMapper.deductionWithdrawAmount(accountId, amount) > 0;
    }

    @Override
    public int updatePayType(Long accountId, Integer payType) {
        if (null == accountId || null == payType) {
            return 0;
        }
        return accountRevenueMapper.updatePayType(accountId, payType);
    }

    @Override
    public List<AccountRevenueEntity> selectPrepayAccountRevenue() {
        return accountRevenueMapper.selectPrepayAccountRevenue();
    }

    @Override
    public List<AccountRevenueEntity> selectPrepayAccountRevenue(List<Long> accountIds) {
        return accountRevenueMapper.selectPrepayAccountRevenueByAccountIds(accountIds);
    }
}
