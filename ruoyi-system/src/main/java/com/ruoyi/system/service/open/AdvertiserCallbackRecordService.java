package com.ruoyi.system.service.open;

import com.ruoyi.system.entity.open.AdvertiserCallbackRecord;

import java.util.List;
import java.util.Map;

/**
 * 广告主回调记录Service接口
 *
 * <AUTHOR>
 * @date 2022-04-15
 */
public interface AdvertiserCallbackRecordService {

    /**
     * 查询记录
     *
     * @param param 参数
     * @return 记录
     */
    AdvertiserCallbackRecord selectBy(AdvertiserCallbackRecord param);

    /**
     * 新增记录
     *
     * @param record 参数
     * @return 结果
     */
    int insert(AdvertiserCallbackRecord record);

    /**
     * 根据订单查询广告主回调记录
     *
     * @param orderNoList 订单号列表
     * @return 订单号-回传状态映射
     */
    Map<String, String> selectMapByOrderNo(List<String> orderNoList);
}
