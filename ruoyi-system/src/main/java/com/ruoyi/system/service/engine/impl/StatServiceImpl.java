package com.ruoyi.system.service.engine.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.DeviceUidEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.system.domain.adengine.AppCacheDto;
import com.ruoyi.system.message.rocketmq.producer.LogMqProducer;
import com.ruoyi.system.service.advert.AdvertMauRepeatFilterService;
import com.ruoyi.system.service.billing.BillingService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.domain.WxDomainService;
import com.ruoyi.system.service.engine.DeviceUidService;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.domain.billing.BillingMessage;
import com.ruoyi.common.core.local.RequestThreadLocal;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.advert.AdvertTypeEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.*;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.req.engine.NhStatReq;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.engine.DataHandleService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.message.rocketmq.producer.BillingMqProducer;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.AdStat;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.service.engine.cache.AppCacheService;
import com.ruoyi.system.service.order.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.InnerLogType.*;

/**
 * 埋点服务实现
 *
 * <AUTHOR>
 * @date 2021/8/19
 */
@Service
public class StatServiceImpl implements StatService {

    private static final Logger log = LoggerFactory.getLogger(StatServiceImpl.class);

    @Autowired
    private DataHandleService dataHandleService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private BillingMqProducer billingMqProducer;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DataStatService dataStatService;

    @Autowired
    private AppCacheService appCacheService;

    @Autowired
    private LogMqProducer logMqProducer;

    @Autowired
    private WxDomainService wxDomainService;

    @Autowired
    private DeviceUidService deviceUidService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertMauRepeatFilterService advertMauRepeatFilterService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private BillingService billingService;

    @Override
    public void innerLog(InnerLogType type) {
        // 生成日志
        JSONObject logJson = InnerLogUtils.buildJSON();
        // 打印日志
        InnerLogUtils.log(type, logJson);
        // 日志落库
        logMqProducer.sendMsg(type, logJson);
    }

    @Override
    public void innerLogByOrderId(InnerLogType type, String orderId) {
        innerLogByOrderId(type, orderId, null);
    }

    @Override
    public void innerLogByOrderId(InnerLogType type, String orderId, Integer action) {
        // 查询订单
        Order order = orderService.selectByOrderId(orderId);
        if (null == order) {
            return;
        }
        innerLogByOrder(type, order, action);
    }

    @Override
    public void innerLogByOrder(InnerLogType type, Order order, Integer action) {
        // 生成日志
        JSONObject logJson = new JSONObject();
        buildLogJsonByRequest(logJson, ServletUtils.getRequest());
        if (null != order) {
            buildLogJsonByOrder(logJson, order);
            buildLogJsonByAdSnapshot(logJson, JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class));
        }
        if (null != action) {
            logJson.put("action", action);
        }
        // 打印日志
        InnerLogUtils.log(type, logJson);
        // 日志落库
        logMqProducer.sendMsg(type, logJson);
    }

    @Override
    public void innerLogStatByOrderId(InnerLogType type, String orderId) {
        // 查询订单
        Order order = orderService.selectByOrderId(orderId);
        if (null == order) {
            return;
        }
        // 生成日志
        JSONObject logJson = new JSONObject();
        buildLogJsonByOrder(logJson, order);
        buildLogJsonByRequest(logJson, ServletUtils.getRequest());
        buildLogJsonByAdSnapshot(logJson, JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class));
        // 打印日志
        InnerLogUtils.log(type, logJson);
        // 日志落库
        logMqProducer.sendMsg(type, logJson);
        // 统计数据
        dataStatService.handleAsync(type, logJson);
    }

    @Override
    public void innerLogStatTLByOrderId(NhStatReq req) {
        InnerLogType logType = InnerLogType.getByType(req.getType());
        if (null == logType) {
            return;
        }

        // 查询订单
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            return;
        }
        RequestThreadLocal.get().setOrderId(req.getOrderId());

        JSONObject logJson = new JSONObject();
        buildLogJsonByOrder(logJson, order);
        buildLogJsonByRequest(logJson, ServletUtils.getRequest());
        buildLogJsonByAdSnapshot(logJson, JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class));
        InnerLogUtils.log(logType, logJson);
        logMqProducer.sendMsg(logType, logJson);
        dataStatService.handleAsync(logType, logJson);

        // 补充一下ThreadLocal，避免后续券曝光/券点击被校验拦截
        RequestThreadLocal.get().setConsumerId(order.getConsumerId());
        RequestThreadLocal.get().setAppId(order.getAppId());
        RequestThreadLocal.get().setSlotId(order.getSlotId());
        RequestThreadLocal.get().setActivityId(order.getActivityId());
        RequestThreadLocal.get().setDeviceId("");
    }

    @Override
    public void activityRequest(NhStatReq req) {
        // 活动访问日志
        JSONObject logJson = InnerLogUtils.buildJSON();
        InnerLogUtils.log(ACTIVITY_REQUEST, logJson);
        logMqProducer.sendMsg(ACTIVITY_REQUEST, logJson);

        // 活动访问数据
        dataHandleService.addPvUvData(ACTIVITY_REQUEST);
    }

    @Override
    public void activityMainLoad(NhStatReq req) {
        // 活动首屏加载日志
        JSONObject logJson = InnerLogUtils.buildJSON();
        InnerLogUtils.log(ACTIVITY_MAIN_LOAD, logJson);
    }

    @Override
    public void activityJoin(JSONObject logJson) {
        // 活动参与日志
        InnerLogUtils.log(ACTIVITY_JOIN, logJson);
        logMqProducer.sendMsg(ACTIVITY_JOIN, logJson);

        // 活动参与数据
        dataHandleService.addPvUvData(ACTIVITY_JOIN);
    }

    @Override
    public void advertRequest(JSONObject logJson) {
        // 券请求日志
        InnerLogUtils.log(ADVERT_REQUEST, logJson);
        logMqProducer.sendMsg(ADVERT_REQUEST, logJson);

        // 券请求数据
        dataHandleService.addPvData(ADVERT_REQUEST, logJson);
    }

    @Override
    public void advertLaunch(JSONObject logJson) {
        // 发券日志
        InnerLogUtils.log(ADVERT_LAUNCH, logJson);
        logMqProducer.sendMsg(ADVERT_LAUNCH, logJson);

        // 发券数据
        dataHandleService.addPvData(ADVERT_LAUNCH, logJson);
        dataStatService.handleAsync(ADVERT_LAUNCH, logJson);

        // 落地页链接加入微信巡查
        wxDomainService.addUrlToPatrolAsync(logJson.getString("advertId"), logJson.getString("originLandpageUrl"), logJson.getString("userAgent"));

        try {
            String today = DateUtil.today();
            redisAtomicClient.incrBy(EngineRedisKeyFactory.K080.join(today, logJson.getLong("advertId"), logJson.getLong("orientId")), logJson.getInteger("unitPrice"), 1, TimeUnit.DAYS);
            redisAtomicClient.incrBy(EngineRedisKeyFactory.K080.join(today, logJson.getLong("advertId")), logJson.getInteger("unitPrice"), 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("广告发券消耗累计异常", e);
        }
    }

    @Override
    public void advertExposure(NhStatReq req) {
        if (StringUtils.isEmpty(req.getOrderId())) {
            return;
        }

        // 查询订单
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            return;
        }
        RequestThreadLocal.get().setOrderId(req.getOrderId());
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);

        // 埋点参数校验，避免构造参数作弊
        if (!statVerify(order)) {
            return;
        }

        // 券曝光日志
        JSONObject logJson = InnerLogUtils.buildJSON();
        logJson.put("orientId", order.getOrientId());
        logJson.put("advertId", order.getAdvertId());
        logJson.put("materialId", order.getMaterialId());
        logJson.putIfAbsent("activityId", order.getActivityId());
        logJson.putIfAbsent("slotId", order.getSlotId());
        logJson.putIfAbsent("appId", order.getAppId());
        logJson.putIfAbsent("consumerId", order.getConsumerId());
        if (null != adSnapshot) {
            logJson.put("chargeType", adSnapshot.getChargeType());
            logJson.put("landpageUrl", adSnapshot.getLandpageUrl());
            logJson.put("originLandpageUrl", adSnapshot.getOriginLandpageUrl());
            logJson.put("srid", adSnapshot.getSrid());
            if (null != adSnapshot.getPluginId()) {
                logJson.putIfAbsent("pluginId", adSnapshot.getPluginId());
            }
        }
        InnerLogUtils.log(ADVERT_EXPOSURE, logJson);
        logMqProducer.sendMsg(ADVERT_EXPOSURE, logJson);

        // 券请求数据
        dataHandleService.addPvUvData(ADVERT_EXPOSURE, logJson);
        dataStatService.handleAsync(ADVERT_EXPOSURE, logJson);

        // 记录曝光时间
        Date time = new Date();
        GlobalThreadPool.executorService.execute(() -> recordExposureTime(order.getConsumerId(), req.getOrderId(), order.getAdvertId(), order.getOrientId(), order.getAdStat(), time));
    }

    @Override
    public void advertClick(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }

        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K023.join(ADVERT_CLICK.getType(), orderId), 1);
        if (lock == null) {
            log.info("广告点击去重, orderId={}", orderId);
            return;
        }

        // 查询订单
        Order order = orderService.selectByOrderId(orderId);
        if (null == order) {
            log.error("互动广告查询订单失败, orderId={}", orderId);
            return;
        }
        RequestThreadLocal.get().setOrderId(orderId);
        AdSnapshot adSnapshot = JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class);

        JSONObject logJson = InnerLogUtils.buildJSON();
        logJson.put("orientId", order.getOrientId());
        logJson.put("advertId", order.getAdvertId());
        logJson.put("materialId", order.getMaterialId());
        logJson.putIfAbsent("activityId", order.getActivityId());
        logJson.putIfAbsent("slotId", order.getSlotId());
        logJson.putIfAbsent("appId", order.getAppId());
        logJson.putIfAbsent("consumerId", order.getConsumerId());
        if (null != adSnapshot) {
            logJson.put("chargeType", adSnapshot.getChargeType());
            logJson.put("landpageUrl", adSnapshot.getLandpageUrl());
            logJson.put("originLandpageUrl", adSnapshot.getOriginLandpageUrl());
            logJson.put("srid", adSnapshot.getSrid());
            if (null != adSnapshot.getPluginId()) {
                logJson.putIfAbsent("pluginId", adSnapshot.getPluginId());
            }
        }

        // 防作弊-埋点参数校验
        if (!statVerify(order)) {
            return;
        }
        // 防作弊-超时IP校验
        if (null != order.getGmtCreate() && null != adSnapshot
                && new Date().after(DateUtil.offsetMinute(order.getGmtCreate(), 30))
                && !StrUtil.equals(adSnapshot.getIp(), IpUtils.getIpAddr(ServletUtils.getRequest()))) {
            return;
        }

        // 券点击日志
        InnerLogUtils.log(ADVERT_CLICK, logJson);
        logMqProducer.sendMsg(ADVERT_CLICK, logJson);

        // 券点击数据
        dataHandleService.addPvUvData(ADVERT_CLICK, logJson);
        dataStatService.handleAsync(ADVERT_CLICK, logJson);

        // 记录点击时间
        Date time = new Date();
        GlobalThreadPool.executorService.execute(() -> recordClickTime(order.getConsumerId(), orderId, order.getAdvertId(), order.getOrientId(), order.getAdStat(), time));

        // 发送计费消息
        if (null != adSnapshot && Objects.equals(adSnapshot.getAdvertType(), AdvertTypeEnum.NH.getType())) {
            billingMqProducer.sendMsg(new BillingMessage(orderId));
        }

        // 广告白名单-MAU去重缓存
        advertMauRepeatFilterService.saveMau(order);

        // 广告点击后调用
        doAfterAdvertClick(order, logJson);
    }

    @Deprecated
    @Override
    public void directAdvertClick(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }

        HttpServletRequest request = ServletUtils.getRequest();
        String appKey = request.getParameter("appKey");
        AppCacheDto app = appCacheService.getAppCache(appKey);
        if (null == app) {
            return;
        }

        // 查询订单
        Order order = orderService.selectByOrderId(orderId);
        if (null == order || !Objects.equals(order.getAppId(), app.getId())) {
            log.error("直投广告查询订单失败, orderId={}", orderId);
            return;
        }
        AdSnapshot adSnapshot = JSON.parseObject(StringUtils.defaultString(order.getAdSnapshot(), "{}"), AdSnapshot.class);

        // 打印日志
        JSONObject logJson = new JSONObject();
        logJson.put("accountId", app.getAccountId());
        buildLogJsonByOrder(logJson, order);
        buildLogJsonByRequest(logJson, request);
        buildLogJsonByAdSnapshot(logJson, adSnapshot);

        // 券点击日志
        InnerLogUtils.log(ADVERT_CLICK, logJson);
        logMqProducer.sendMsg(ADVERT_CLICK, logJson);

        // 券点击数据
        dataHandleService.addPvUvData(ADVERT_CLICK, logJson);
        dataStatService.handleAsync(ADVERT_CLICK, logJson);

        // 发送计费消息
        if (null != adSnapshot && Objects.equals(adSnapshot.getAdvertType(), AdvertTypeEnum.NH.getType())) {
            if (StrUtil.containsAny(adSnapshot.getAdvertName(), "公众号", "文章")
                    || whitelistService.contains(WhitelistType.ADVERT_NOT_MQ, order.getAdvertId())) {
                GlobalThreadPool.statExecutorService.submit(() -> {
                    log.info("非消息队列计费, advertId={}, orderId={}", order.getAdvertId(), orderId);
                    billingService.billing(new BillingMessage(orderId));
                });
            } else {
                billingMqProducer.sendMsg(new BillingMessage(orderId));
            }
        }

        // 记录点击时间
        recordClickTimeCache(order.getConsumerId(), order.getAdvertId(), order.getOrientId(), new Date());

        // 广告点击后调用
        doAfterAdvertClick(order, logJson);
    }

    @Override
    public void directAdvertClick(Date time, Long accountId, Order order, JSONObject logJson) {
        if (null == order) {
            return;
        }
        String orderId = order.getOrderId();
        AdSnapshot adSnapshot = JSON.parseObject(StringUtils.defaultString(order.getAdSnapshot(), "{}"), AdSnapshot.class);

        // 打印日志
        logJson.put("accountId", accountId);
        buildLogJsonByOrder(logJson, order);
        buildLogJsonByAdSnapshot(logJson, adSnapshot);

        // 券点击日志
        InnerLogUtils.log(ADVERT_CLICK, logJson);
        logMqProducer.sendMsg(ADVERT_CLICK, logJson);

        // 券点击数据
        dataHandleService.addPvUvData(ADVERT_CLICK, logJson);
        dataStatService.handleAsync(ADVERT_CLICK, logJson);

        // 发送计费消息
        if (null != adSnapshot && Objects.equals(adSnapshot.getAdvertType(), AdvertTypeEnum.NH.getType())) {
            if (StrUtil.containsAny(adSnapshot.getAdvertName(), "公众号", "文章")
                    || whitelistService.contains(WhitelistType.ADVERT_NOT_MQ, order.getAdvertId())) {
                GlobalThreadPool.statExecutorService.submit(() -> {
                    log.info("非消息队列计费, advertId={}, orderId={}", order.getAdvertId(), orderId);
                    billingService.billing(new BillingMessage(orderId));
                });
            } else {
                billingMqProducer.sendMsg(new BillingMessage(orderId));
            }
        }

        // 更新Redis中的券点击时间
        recordClickTimeCache(order.getConsumerId(), order.getAdvertId(), order.getOrientId(), time);

        // 广告白名单-MAU去重缓存
        advertMauRepeatFilterService.saveMau(order);

        // 广告点击后调用
        doAfterAdvertClick(order, logJson);
    }

    @Override
    public void landpageExposure(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }

        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K023.join(LANDPAGE_EXPOSURE.getType(), orderId), 1);
        if (lock == null) {
            log.info("落地页曝光去重, orderId={}", orderId);
            return;
        }

        // 打印日志
        JSONObject logJson = buildLogJsonByOrderId(orderId);
        InnerLogUtils.log(LANDPAGE_EXPOSURE, logJson);
        logMqProducer.sendMsg(LANDPAGE_EXPOSURE, logJson);

        // 落地页数据
        dataStatService.handleAsync(LANDPAGE_EXPOSURE, logJson);
    }

    @Override
    public void landpageClick(String orderId) {
        landpageClick(orderId, null);
    }

    @Override
    public void landpageClick(String orderId, Map<String, Object> extParam) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }

        // 打印日志
        JSONObject logJson = buildLogJsonByOrderId(orderId);
        if (MapUtil.isNotEmpty(extParam)) {
            extParam.forEach(logJson::put);
        }
        InnerLogUtils.log(LANDPAGE_CLICK, logJson);
        logMqProducer.sendMsg(LANDPAGE_CLICK, logJson);

        // 落地页数据
        dataStatService.handleAsync(LANDPAGE_CLICK, logJson);
    }

    @Override
    public void convertEvent(String orderId, Integer convType) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }
        convertEvent(orderId, convType, null);
    }

    @Override
    public void convertEvent(String orderId, Integer convType, JSONObject convExt) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }

        JSONObject logJson = buildLogJsonByOrderId(orderId);
        logJson.put("convType", convType);
        logJson.put("convExt", convExt);
        InnerLogUtils.log(CONVERT_EVENT, logJson);
        logMqProducer.sendMsg(CONVERT_EVENT, logJson);
        dataStatService.handleAsync(CONVERT_EVENT, logJson);
    }

    @Override
    public void convertEvent(Order order, Integer convType, JSONObject convExt) {
        JSONObject logJson = buildLogJsonByOrder(order);
        logJson.put("convType", convType);
        logJson.put("convExt", convExt);
        InnerLogUtils.log(CONVERT_EVENT, logJson);
        logMqProducer.sendMsg(CONVERT_EVENT, logJson);
        dataStatService.handleAsync(CONVERT_EVENT, logJson);
    }

    @Override
    public void pluginExposure(NhStatReq req) {
        // 记录日志
        JSONObject logJson = InnerLogUtils.buildJSON();
        InnerLogUtils.log(PLUGIN_EXPOSURE, logJson);
        logMqProducer.sendMsg(PLUGIN_EXPOSURE, logJson);

        // 记录数据
        dataStatService.handleAsync(PLUGIN_EXPOSURE, logJson);

        // 设置用户触发返回挽留
        String redisKey = EngineRedisKeyFactory.K011.join(DateUtil.today());
        redisCache.addCacheSet(redisKey, String.valueOf(RequestThreadLocal.get().getConsumerId()));
        redisCache.expire(redisKey, 1, TimeUnit.DAYS);
    }

    /**
     * 埋点校验(回传的数据和订单数据)
     *
     * @param order 订单信息
     * @return 是否校验通过
     */
    private boolean statVerify(Order order) {
        RequestThreadLocal local = RequestThreadLocal.get();
        return Objects.equals(local.getConsumerId(), order.getConsumerId())
                && Objects.equals(local.getAppId(), order.getAppId())
                && Objects.equals(local.getSlotId(), order.getSlotId())
                && Objects.equals(local.getActivityId(), order.getActivityId());
    }

    /**
     * 广告点击后调用
     *
     * @param order   订单信息
     * @param logJson 日志数据
     */
    private void doAfterAdvertClick(Order order, JSONObject logJson) {
        // 根据consumerId设置设备号信息uid_o5等
        setDeviceIdSFromRedis(logJson);

        GlobalThreadPool.executorService.execute(() -> {
            // 点击监测上报
            clickCallback(order.getAdvertId(), logJson);
            // 缓存设备唯一标识
            deviceUidService.cacheAdvertClickUid(order, logJson);
        });
    }

    /**
     * 根据consumerId设置设备号信息uid_o5等
     * uid_o5=__OAID_MD5__&uid_m5=__IMEI__&uid_f5=__IDFA__
     *
     * @param logJson
     */
    private void setDeviceIdSFromRedis(JSONObject logJson) {
        String consumerId = logJson.getString("consumerId");
        if (StringUtils.isEmpty(consumerId)) {
            return;
        }
        // 1.redis获取数据源
        String value = redisCache.getCacheObject(EngineRedisKeyFactory.K072.join("cid", consumerId));
        JSONObject deviceUid = null;
        try {
            deviceUid = JSON.parseObject(value);
        } catch (Exception e) {
            log.error("setDeviceIdSFromRedis,e", e);
        }
        if (null == deviceUid) {
            return;
        }

        // 2.数据存储：k:uid_o5, value=32位具体的用户标志
        for (DeviceUidEnum deviceUidEnum : DeviceUidEnum.values()) {
            String key = deviceUidEnum.getKey();
            String did = deviceUid.getString(key);
            if (StringUtils.isEmpty(did)) {
                continue;
            }
            logJson.put(key, did);
        }
    }

    /**
     * 点击监测链接回调
     *
     * @param advertId 广告ID
     * @param data     宏替换数据
     */
    private void clickCallback(Long advertId, JSONObject data) {
        // 获取点击监测链接
        String clickUrl = advertCacheService.queryClickCallbackUrl(advertId);
        if (StringUtils.isBlank(clickUrl)) {
            return;
        }

        // 链接参数宏替换
        String url = MacroUtils.ccMacroReplace(clickUrl, data);
        try {
            int status = HttpUtil.createGet(url).execute().getStatus();
            log.info("点击监测链接回调，url={}, status={}", url, status);
        } catch (Exception e) {
            log.error("点击监测链接回调异常，url={}", url, e);
        }
    }

    /**
     * 订单里记录曝光时间
     *
     * @param consumerId 用户ID
     * @param orderId    订单号
     * @param advertId   广告ID
     * @param orientId   配置ID
     * @param adStatStr  订单埋点信息
     */
    private void recordExposureTime(Long consumerId, String orderId, Long advertId, Long orientId, String adStatStr, Date time) {
        // Redis中记录，用于发券过滤
        String key = EngineRedisKeyFactory.K044.join(consumerId, DateUtil.formatDate(time), ADVERT_EXPOSURE.getType());
        redisCache.incrCacheMapValue(key, String.valueOf(advertId), 1);
        redisCache.expire(key, 1, TimeUnit.DAYS);

        String orientKey = EngineRedisKeyFactory.K084.join(consumerId, DateUtil.formatDate(time), ADVERT_EXPOSURE.getType());
        redisCache.incrCacheMapValue(orientKey, String.valueOf(orientId), 1);
        redisCache.expire(orientKey, 1, TimeUnit.DAYS);

        // 更新到数据库
        AdStat adStat = JSON.parseObject(adStatStr, AdStat.class);
        if (null == adStat) {
            adStat = new AdStat();
        }
        if (null == adStat.getExposures()) {
            adStat.setExposures(new ArrayList<>(1));
        }
        adStat.getExposures().add(0, time.getTime());
        Order updateOrder = new Order();
        updateOrder.setOrderId(orderId);
        updateOrder.setAdStat(JSON.toJSONString(adStat));
        orderService.updateOrder(updateOrder);
    }

    /**
     * 订单里记录点击时间(Redis)
     *
     * @param consumerId 用户ID
     * @param advertId   广告ID
     * @param orientId   配置ID
     */
    private void recordClickTimeCache(Long consumerId, Long advertId, Long orientId, Date time) {
        String dateStr = DateUtil.formatDate(time);

        String key = EngineRedisKeyFactory.K044.join(consumerId, dateStr, ADVERT_CLICK.getType());
        redisCache.incrCacheMapValue(key, String.valueOf(advertId), 1);
        redisCache.expire(key, 1, TimeUnit.DAYS);

        String orientKey = EngineRedisKeyFactory.K084.join(consumerId, dateStr, ADVERT_CLICK.getType());
        redisCache.incrCacheMapValue(orientKey, String.valueOf(orientId), 1);
        redisCache.expire(orientKey, 1, TimeUnit.DAYS);
    }

    /**
     * 订单里记录点击时间
     *
     * @param consumerId 用户ID
     * @param orderId    订单号
     * @param advertId   广告ID
     * @param orientId   配置ID
     * @param adStatStr  订单埋点信息
     */
    private void recordClickTime(Long consumerId, String orderId, Long advertId, Long orientId, String adStatStr, Date time) {
        // Redis中记录，用于发券过滤
        recordClickTimeCache(consumerId, advertId, orientId, time);

        // 数据库落库
        AdStat adStat = JSON.parseObject(adStatStr, AdStat.class);
        if (null == adStat) {
            adStat = new AdStat();
        }
        if (null == adStat.getClicks()) {
            adStat.setClicks(new ArrayList<>(1));
        }
        adStat.getClicks().add(0, time.getTime());
        Order updateOrder = new Order();
        updateOrder.setOrderId(orderId);
        updateOrder.setAdStat(JSON.toJSONString(adStat));
        orderService.updateOrder(updateOrder);
    }

    /**
     * 查询订单构造日志信息
     *
     * @param orderId 订单号
     */
    private JSONObject buildLogJsonByOrderId(String orderId) {
        // 查询订单
        Order order = orderService.selectByOrderId(orderId);
        if (null == order) {
            throw new CustomException("无效的订单");
        }

        // 构造日志
        return buildLogJsonByOrder(order);
    }

    /**
     * 根据订单构造日志信息
     *
     * @param order 订单
     */
    private JSONObject buildLogJsonByOrder(Order order) {
        JSONObject logJson = new JSONObject();
        buildLogJsonByRequest(logJson, ServletUtils.getRequest());
        if (null != order) {
            buildLogJsonByOrder(logJson, order);
            buildLogJsonByAdSnapshot(logJson, JSON.parseObject(order.getAdSnapshot(), AdSnapshot.class));
        }
        return logJson;
    }

    /**
     * 用请求信息完善日志
     *
     * @param logJson 日志
     * @param request 请求信息
     */
    private void buildLogJsonByRequest(JSONObject logJson, HttpServletRequest request) {
        if (null == logJson || null == request) {
            return;
        }
        logJson.put("ip", IpUtils.getIpAddr(request));
        logJson.put("userAgent", request.getHeader("User-Agent"));
        logJson.put("referer", request.getHeader("referer"));
    }

    /**
     * 用订单信息完善日志
     *
     * @param logJson 日志
     * @param order   订单
     */
    private void buildLogJsonByOrder(JSONObject logJson, Order order) {
        if (null == logJson || null == order) {
            return;
        }
        logJson.put("orderId", order.getOrderId());
        logJson.put("activityId", order.getActivityId());
        logJson.put("slotId", order.getSlotId());
        logJson.put("appId", order.getAppId());
        logJson.put("consumerId", order.getConsumerId());
        logJson.put("orientId", order.getOrientId());
        logJson.put("advertId", order.getAdvertId());
        logJson.put("materialId", order.getMaterialId());
    }

    /**
     * 用订单快照信息完善日志
     *
     * @param logJson    日志
     * @param adSnapshot 订单快照信息
     */
    private void buildLogJsonByAdSnapshot(JSONObject logJson, AdSnapshot adSnapshot) {
        if (null == logJson || null == adSnapshot) {
            return;
        }
        logJson.put("srid", adSnapshot.getSrid());
        logJson.put("pluginId", adSnapshot.getPluginId());
        logJson.put("chargeType", adSnapshot.getChargeType());
        logJson.put("landpageUrl", adSnapshot.getLandpageUrl());
        logJson.put("originLandpageUrl", adSnapshot.getOriginLandpageUrl());
        logJson.put("brand", adSnapshot.getBrand());
        logJson.put("model", adSnapshot.getModel());
        logJson.put("assessType", adSnapshot.getAssessType());
        logJson.put("assessCost", adSnapshot.getAssessCost());
    }
}
