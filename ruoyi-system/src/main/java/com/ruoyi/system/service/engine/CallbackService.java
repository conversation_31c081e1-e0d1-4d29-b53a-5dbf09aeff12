package com.ruoyi.system.service.engine;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.domain.adengine.SlotBiddingConfigDto;
import com.ruoyi.system.domain.order.Order;

import javax.servlet.http.HttpServletRequest;
import java.util.function.Function;

/**
 * 外部回调接口
 *
 * <AUTHOR>
 * @date 2022/01/05
 */
public interface CallbackService {

    /**
     * 缓存参数
     *
     * @param request 请求
     * @param orderId 订单号
     */
    void cacheParameter(HttpServletRequest request, String orderId);

    /**
     * 缓存真实投放的广告位链接
     *
     * @param request 请求
     * @param srid 广告位请求唯一标识
     */
    void cacheActualSlotUrl(HttpServletRequest request, String srid);

    /**
     * 获取缓存的参数
     *
     * @param orderId 订单号
     * @return 缓存的参数
     */
    JSONObject getParameterFromCache(String orderId);

    /**
     * 累计统计广告位已经上报产生的成本：上报个数*浮动当前上报成本
     *
     * @param slotId
     */
    void callBackCost(Long slotId, SlotBiddingConfigDto biddingConfig, String hu);

    /**
     * 回调返利接口
     *
     * @param order 订单
     * @param param 广告位参数
     * @return 回调状态-接口信息
     */
    Pair<Integer, String> callbackFanli(Order order, JSONObject param);

    /**
     * 异步回调极准接口
     *
     * @param order 订单
     * @return 回调状态-接口信息
     */
     Pair<Integer, String> callbackJizhun(Order order, JSONObject param);

    /**
     * 广告位转化数据自增
     *
     * @param order 订单
     * @param callback 回调
     */
    void callbackAndIncrData(Order order, Function<Pair<Order, JSONObject>, Pair<Integer, String>> callback);

    /**
     * 判断是否要跳过上报
     *
     * @param order 订单
     * @param biddingConfig 投流配置
     * @param hu 投流账户标识
     * @return true.不上报,false.上报
     */
    boolean jumpCallback(Order order, SlotBiddingConfigDto biddingConfig, String hu);
}
