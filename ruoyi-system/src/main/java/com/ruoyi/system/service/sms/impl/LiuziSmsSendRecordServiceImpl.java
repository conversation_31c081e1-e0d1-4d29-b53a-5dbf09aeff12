package com.ruoyi.system.service.sms.impl;

import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.sms.LiuziSmsSendRecordCountBO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.sms.LiuziSmsSendRecordService;
import com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.sms.LiuziSmsSendRecordMapper;

/**
 * 留资短信发送记录表 Service
 *
 * <AUTHOR>
 * @date 2022-12-5 10:09:17
 */
@Service
public class LiuziSmsSendRecordServiceImpl implements LiuziSmsSendRecordService {
    @Autowired
    private LiuziSmsSendRecordMapper liuziSmsSendRecordMapper;

    @Override
    public Boolean insert(LiuziSmsSendRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return liuziSmsSendRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean batchInsert(List<LiuziSmsSendRecordEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return false;
        }
        return liuziSmsSendRecordMapper.batchInsert(entities) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return liuziSmsSendRecordMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(LiuziSmsSendRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return liuziSmsSendRecordMapper.updateById(entity) > 0;
    }

    @Override
    public LiuziSmsSendRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return liuziSmsSendRecordMapper.selectById(id);
    }


    @Override
    public List<LiuziSmsSendRecordEntity> selectListByLiuziRecordId(Long liuziRecordId) {
        if (NumberUtils.isNullOrLteZero(liuziRecordId)) {
            return Collections.emptyList();
        }
        return liuziSmsSendRecordMapper.selectListByLiuziRecordId(liuziRecordId);
    }

    @Override
    public List<LiuziSmsSendRecordCountBO> countByLiuziRecordIds(List<Long> liuziRecordIds) {
        if (CollectionUtils.isEmpty(liuziRecordIds)) {
            return Collections.emptyList();
        }
        return liuziSmsSendRecordMapper.countByLiuziRecordIds(liuziRecordIds);
    }

    @Override
    public Boolean updateSmsStatusById(Long id ,Integer status) {
        if(NumberUtils.isNullOrLteZero(id)){
            return false;
        }
        return liuziSmsSendRecordMapper.updateSmsStatusById(id, status);
    }

    @Override
    public Boolean updateSmsStatusByMsgIdAndType(Integer type, String msgId, Integer status) {
        if(NumberUtils.isNullOrLteZero(type) || StringUtils.isBlank(msgId)){
            return false;
        }
        return liuziSmsSendRecordMapper.updateSmsStatusByMsgIdAndType(type, msgId, status);
    }

    @Override
    public LiuziSmsSendRecordEntity selectByTypeAndMsgId(Integer type, String msgId) {
        if(NumberUtils.isNullOrLteZero(type) || StringUtils.isBlank(msgId)){
            return null;
        }
        return liuziSmsSendRecordMapper.selectByTypeAndMsgId(type, msgId);
    }

    @Override
    public Map<Long, List<LiuziSmsSendRecordEntity>> selectMapByLiuziRecordIds(List<Long> liuziRecordIds) {
        if (CollectionUtils.isEmpty(liuziRecordIds)) {
            return Collections.emptyMap();
        }
        List<LiuziSmsSendRecordEntity> list = liuziSmsSendRecordMapper.selectListByLiuziRecordIds(liuziRecordIds);
        return list.stream().collect(Collectors.groupingBy(LiuziSmsSendRecordEntity::getLiuziRecordId));
    }
}
