package com.ruoyi.system.service.wx.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.enums.WxPayOrderStatus;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.wx.WxPayOrderService;
import com.ruoyi.system.entity.pay.WxPayOrderEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Objects;

import com.ruoyi.system.mapper.pay.WxPayOrderMapper;

/**
 * 微信支付订单表 Service
 *
 * <AUTHOR>
 * @date 2023-1-29 17:38:06
 */
@Service
public class WxPayOrderServiceImpl implements WxPayOrderService {

    @Autowired
    private WxPayOrderMapper wxPayOrderMapper;

    @Override
    public Boolean insert(WxPayOrderEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return wxPayOrderMapper.insert(entity) > 0;
    }

    @Override
    public void finishPay(Map<String, String> param) {
        String outTradeNo = param.get("out_trade_no");
        if (StringUtils.isBlank(outTradeNo) || !StrUtil.equalsAnyIgnoreCase(param.get("result_code"), "SUCCESS")) {
            return;
        }
        WxPayOrderEntity order = selectByOutTradeNo(outTradeNo);
        if (null == order || WxPayOrderStatus.isPaid(order.getStatus())) {
            return;
        }
        WxPayOrderEntity updateOrder = new WxPayOrderEntity();
        updateOrder.setId(order.getId());
        updateOrder.setStatus(WxPayOrderStatus.PAID.getStatus());
        updateOrder.setTransactionId(param.get("transaction_id"));
        updateOrder.setOpenid(param.get("openid"));
        updateOrder.setBankType(param.get("bank_type"));
        updateOrder.setTimeEnd(param.get("time_end"));
        wxPayOrderMapper.updateById(updateOrder);
    }

    @Override
    public WxPayOrderEntity selectByOutTradeNo(String outTradeNo) {
        if (Objects.isNull(outTradeNo)) {
            return null;
        }
        return wxPayOrderMapper.selectByOutTradeNo(outTradeNo);
    }
}
