package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.bo.landpage.ExternalLandpageFormInfoBo;
import com.ruoyi.system.entity.landpage.ExternalLandpageRecord;
import com.ruoyi.system.service.common.IdCardService;
import com.ruoyi.system.service.landpage.ExternalLandpageRecordService;
import com.ruoyi.system.service.landpage.ExternalLandpageService;
import com.ruoyi.system.service.manager.AgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.landpage.ImportTypeEnum.API;

/**
 * 外部落地页表单接口实现
 *
 * <AUTHOR>
 * @date 2023/4/6
 */
@Slf4j
@Service
public class ExternalLandpageServiceImpl implements ExternalLandpageService {

    @Autowired
    private ExternalLandpageRecordService externalLandpageRecordService;

    @Autowired
    private IdCardService idCardService;

    @Autowired
    private AgentService agentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submit(Long advertiserId, List<ExternalLandpageFormInfoBo> list) {
        Date today = DateUtil.beginOfDay(new Date());
        Long agentId = NumberUtils.defaultLong(agentService.selectAgentIdByAdvertiserId(advertiserId));

        List<ExternalLandpageRecord> records = list.stream().map(req -> {
            ExternalLandpageRecord record = new ExternalLandpageRecord();
            record.setSubmitDate(today);
            record.setAdvertiserId(advertiserId);
            record.setAgentId(agentId);
            record.setExternalNo(req.getExternalNo());
            record.setName(req.getName());
            record.setPhone(req.getPhone());
            record.setIdCard(idCardService.encrypt(req.getIdCard()));
            record.setIdCardMd5(Md5Utils.hash(req.getIdCard()));
            record.setProvince(req.getProvince());
            record.setCity(req.getCity());
            record.setDistrict(req.getDistrict());
            record.setAddress(req.getAddress());
            record.setExtInfo("");
            record.setImportType(API.getType());
            return record;
        }).collect(Collectors.toList());
        return externalLandpageRecordService.batchInsert(records);
    }
}
