package com.ruoyi.system.service.common.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.system.SysConfig;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.system.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 白名单服务实现
 *
 * <AUTHOR>
 * @date 2021/9/9
 */
@Slf4j
@Service
public class WhitelistServiceImpl implements WhitelistService {

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 白名单缓存
     */
    private final LoadingCache<String, List<?>> WHITELIST_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<?>>() {
                @Override
                public List<?> load(String key) {
                    WhitelistType type = WhitelistType.getByKey(key);
                    return null == type ? Collections.emptyList() : list(type, type.getClazz());
                }

                @Override
                public ListenableFuture<List<?>> reload(String key, List<?> oldValue) {
                    ListenableFutureTask<List<?>> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    @Override
    public List<?> list(WhitelistType type) {
        try {
            return WHITELIST_CACHE.get(type.getKey());
        } catch (Exception e) {
            log.error("查询白名单列表缓存异常, type={}", type.getKey(), e);
        }
        return Collections.emptyList();
    }

    @Override
    public <E> List<E> list(WhitelistType type, Class<E> clazz) {
        String configValue = sysConfigService.selectConfigCacheByKey(type.getKey());
        if (StringUtils.isBlank(configValue)) {
            return Collections.emptyList();
        }

        List<E> list = JSON.parseArray(configValue, clazz);
        return null == list ? Collections.emptyList() : list;
    }

    @Override
    public <E> boolean contains(WhitelistType type, E e) {
        if (null == e) {
            return false;
        }

        List<E> whitelist = list(type, (Class<E>) e.getClass());
        return CollectionUtils.isNotEmpty(whitelist) && whitelist.contains(e);
    }

    @Override
    public <E> boolean add(WhitelistType type, E e) {
        if (null == e) {
            return false;
        }
        SysConfig config = sysConfigService.selectByKey(type.getKey());
        if (null == config) {
            config = new SysConfig();
            config.setConfigName(type.getDesc());
            config.setConfigKey(type.getKey());
            config.setConfigValue(JSON.toJSONString(Collections.singletonList(e)));
            config.setConfigType("N");
            return sysConfigService.insertConfig(config) > 0;
        }

        List<E> list = JSON.parseArray(StringUtils.defaultString(config.getConfigValue()), (Class<E>) e.getClass());
        if (null == list) {
            list = new ArrayList<>(1);
        } else if (list.contains(e)) {
            return true;
        }
        list.add(e);
        config.setConfigValue(JSON.toJSONString(list));
        return sysConfigService.updateConfig(config) > 0;
    }

    @Override
    public <E> boolean remove(WhitelistType type, E e) {
        if (null == e) {
            return true;
        }
        SysConfig config = sysConfigService.selectByKey(type.getKey());
        if (null == config || StringUtils.isBlank(config.getConfigValue())) {
            return true;
        }

        List<E> list = JSON.parseArray(config.getConfigValue(), (Class<E>) e.getClass());
        list.remove(e);
        config.setConfigValue(JSON.toJSONString(list));
        return sysConfigService.updateConfig(config) > 0;
    }

    @Override
    public <E> boolean update(WhitelistType type, List<E> list) {
        SysConfig config = sysConfigService.selectByKey(type.getKey());
        if (null == config) {
            config = new SysConfig();
            config.setConfigName(type.getDesc());
            config.setConfigKey(type.getKey());
            config.setConfigValue(JSON.toJSONString(list));
            config.setConfigType("N");
            return sysConfigService.insertConfig(config) > 0;
        }

        config.setConfigValue(JSON.toJSONString(list));
        return sysConfigService.updateConfig(config) > 0;
    }

    @Override
    public boolean clear(WhitelistType type) {
        SysConfig config = sysConfigService.selectByKey(type.getKey());
        if (null == config || StringUtils.isBlank(config.getConfigValue())) {
            return true;
        }

        config.setConfigValue("");
        return sysConfigService.updateConfig(config) > 0;
    }

    @Override
    public void refreshCache(String key) {
        if (StringUtils.isNotBlank(key)) {
            WHITELIST_CACHE.refresh(key);
        }
    }
}
