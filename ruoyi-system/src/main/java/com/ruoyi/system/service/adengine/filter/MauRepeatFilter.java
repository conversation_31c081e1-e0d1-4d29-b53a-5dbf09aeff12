package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.entity.advert.AdvertMauRepeatFilterEntity;
import com.ruoyi.system.service.adengine.AdvertFilter;
import com.ruoyi.system.service.advert.AdvertMauRepeatFilterService;
import com.ruoyi.system.service.common.WhitelistService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 广告MAU去重过滤
 */
@Slf4j
@Component
public class MauRepeatFilter implements AdvertFilter {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private AdvertMauRepeatFilterService advertMauRepeatFilterService;

    @Override
    public boolean filter(AdvertFilterContext context) {
        Long advertId = context.getAdvertCacheDto().getAdvertId();
        if (!whitelistService.contains(WhitelistType.MAU_REPEAT_FILTER_ADVERT, advertId)) {
            return true;
        }

        long expireTime = System.currentTimeMillis() - 2678400000L;
        String openId = null != context.getExtParam() ? context.getExtParam().getString("openId") : "";

        // 广告白名单，过滤一个月内重复点击的用户
        if (!redisCache.hasKey(EngineRedisKeyFactory.K146.toString())) {
            // 异步初始化缓存
            advertMauRepeatFilterService.initMauCache();
            // 降级查询数据库
            AdvertMauRepeatFilterEntity entity = advertMauRepeatFilterService.selectByOpenId(openId);
            return null == entity || entity.getLastClickTime().getTime() < expireTime;
        }

        // 查询openId缓存
        if (StringUtils.isNotBlank(openId)) {
            Long lastClickTime = redisCache.getCacheObject(EngineRedisKeyFactory.K145.join(advertId, openId));
            if (null != lastClickTime && lastClickTime >= expireTime) {
                return false;
            }
        }
        // 查询consumerId缓存
        Long lastClickTime = redisCache.getCacheObject(EngineRedisKeyFactory.K145.join(advertId, context.getConsumerId()));
        return null == lastClickTime || lastClickTime < expireTime;
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.MAU_REPEAT;
    }
}
