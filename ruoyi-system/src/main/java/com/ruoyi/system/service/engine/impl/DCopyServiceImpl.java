package com.ruoyi.system.service.engine.impl;

import com.ruoyi.system.service.engine.DCopyService;
import org.springframework.stereotype.Service;

import java.util.Random;

@Service
public class DCopyServiceImpl implements DCopyService {

    /**
     * 后期打埋点监控使用，勿动
     *
     * @param text
     * @return
     */
    @Override
    public String printLog(String slotId, String text) {
        int i = new Random().nextInt(1) + 1;
        if (i == 1) {
            return "";
        }
        return "orderId=123&slotId=" + slotId;
    }
}
