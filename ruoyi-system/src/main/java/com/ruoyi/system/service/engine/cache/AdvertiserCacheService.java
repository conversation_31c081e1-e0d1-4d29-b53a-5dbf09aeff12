package com.ruoyi.system.service.engine.cache;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.account.AdvertiserAccess;
import com.ruoyi.system.mapper.manager.AccountMapper;
import com.ruoyi.system.service.advertiser.AdvertiserBudgetService;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 广告主缓存服务
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
@Slf4j
@Service
public class AdvertiserCacheService {

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AdvertiserBudgetService advertiserBudgetService;

    /**
     * 广告主日预算缓存
     */
    private final LoadingCache<Long, Optional<Long>> ADVERTISER_BUDGET_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Long, Optional<Long>>() {
                @Override
                public Optional<Long> load(Long advertiserId) {
                    Long budget = advertiserBudgetService.selectBudgetByAccountId(advertiserId);
                    return Optional.ofNullable(budget);
                }

                @Override
                public ListenableFuture<Optional<Long>> reload(Long key, Optional<Long> oldValue) {
                    ListenableFutureTask<Optional<Long>> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告主名称缓存
     */
    private final LoadingCache<Long, String> ADVERTISER_NAME_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long advertiserId) {
                    return advertiserService.selectAdvertiserName(advertiserId);
                }

                @Override
                public ListenableFuture<String> reload(Long key, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });


    /**
     * 判断CPC结算广告主缓存
     */
    private final LoadingCache<Long, Boolean> IS_CPC_ADVERTISER_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Long, Boolean>() {
                @Override
                public Boolean load(Long advertiserId) {
                    return advertiserService.isCpcAdvertiser(advertiserId);
                }

                @Override
                public ListenableFuture<Boolean> reload(Long key, Boolean oldValue) {
                    ListenableFutureTask<Boolean> task = ListenableFutureTask.create(() -> load(key));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 广告主秘钥缓存
     */
    private final LoadingCache<String, AdvertiserAccess> ACCESS_KEY_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(200)
            .expireAfterAccess(1, TimeUnit.HOURS)
            .build(new CacheLoader<String, AdvertiserAccess>() {

                @Override
                public AdvertiserAccess load(String accessKey) {
                    Account param = new Account();
                    param.setMainType(AccountMainType.ADVERTISER.getType());
                    param.setExtInfo(accessKey);
                    List<Account> accounts = accountMapper.selectAccountExtInfo(param);
                    if (CollectionUtils.isNotEmpty(accounts)) {
                        for (Account account : accounts) {
                            if (StringUtils.isBlank(account.getExtInfo())) {
                                continue;
                            }
                            AccountExtInfo extInfo = JSON.parseObject(account.getExtInfo(), AccountExtInfo.class);
                            if (Objects.equals(extInfo.getAccessKey(), accessKey)) {
                                AdvertiserAccess access = new AdvertiserAccess();
                                access.setAccessKey(accessKey);
                                access.setSecretKey(extInfo.getSecretKey());
                                access.setAdvertiserId(account.getId());
                                return access;
                            }
                        }
                    }
                    return new AdvertiserAccess();
                }

                @Override
                public ListenableFuture<AdvertiserAccess> reload(String accessKey, AdvertiserAccess oldValue) {
                    ListenableFutureTask<AdvertiserAccess> task = ListenableFutureTask.create(() -> load(accessKey));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 获取广告主预算
     */
    public Long getAdvertiserBudget(Long advertiserId) {
        if (null != advertiserId) {
            try {
                Optional<Long> budget = ADVERTISER_BUDGET_CACHE.get(advertiserId);
                return budget.orElse(null);
            } catch (ExecutionException e) {
                log.error("获取广告主预算缓存异常, advertiserId={}", advertiserId, e);
            }
        }
        return null;
    }

    /**
     * 获取广告主名称
     */
    public String getAdvertiserName(Long advertiserId) {
        if (null != advertiserId) {
            try {
                return ADVERTISER_NAME_CACHE.get(advertiserId);
            } catch (ExecutionException e) {
                log.error("获取广告主名称缓存异常, advertiserId={}", advertiserId, e);
            }
        }
        return "";
    }

    /**
     * 查询广告主秘钥
     */
    public AdvertiserAccess queryAdvertiserAccess(String accessKey) {
        if (StringUtils.isNotBlank(accessKey)) {
            try {
                return ACCESS_KEY_CACHE.get(accessKey);
            } catch (ExecutionException e) {
                log.error("查询广告主秘钥缓存异常, accessKey={}", accessKey, e);
            }
        }
        return new AdvertiserAccess();
    }

    /**
     * 判断是不是CPC结算广告主
     *
     * @param advertiserId 广告主ID
     * @return true.是,false.不是
     */
    public boolean isCpcAdvertiser(Long advertiserId) {
        try {
            return IS_CPC_ADVERTISER_CACHE.get(advertiserId);
        } catch (ExecutionException e) {
            log.error("查询判断是不是CPC结算广告主缓存异常, advertiserId={}", advertiserId, e);
        }
        return false;
    }

    /**
     * 刷新广告主缓存
     */
    public void refreshAdvertiserCache(Long advertiserId) {
        if (null == advertiserId) {
            return;
        }
        ADVERTISER_BUDGET_CACHE.refresh(advertiserId);
        IS_CPC_ADVERTISER_CACHE.refresh(advertiserId);
    }
}
