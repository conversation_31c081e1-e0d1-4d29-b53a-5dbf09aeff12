package com.ruoyi.system.service.finance;

import com.github.pagehelper.PageInfo;
import com.ruoyi.system.entity.withdraw.WithdrawRecordEntity;
import com.ruoyi.system.req.withdraw.WithdrawApplyReq;
import com.ruoyi.system.req.withdraw.WithdrawFileReq;
import com.ruoyi.system.req.withdraw.WithdrawListReq;
import com.ruoyi.system.req.withdraw.WithdrawStatusReq;
import com.ruoyi.system.vo.withdraw.SspWithdrawListVO;
import com.ruoyi.system.vo.withdraw.WithdrawInfoVO;
import com.ruoyi.system.vo.withdraw.WithdrawListVO;

import java.util.List;
import java.util.Map;

/**
* 提现记录 Service
* <AUTHOR>
* @date 2021-9-9 16:57:24
*/
public interface WithdrawRecordService {

    /**
    * 新增记录
    */
	Boolean insert(WithdrawRecordEntity entity);

    /**
    * 根据id更新
    */
    Boolean updateById(WithdrawRecordEntity entity);

    /**
     * 查询提现记录列表(SSP使用)
     *
     * @param accountId 媒体账号ID
     * @return 结果列表
     */
    PageInfo<SspWithdrawListVO> selectWithdrawListForSsp(Long accountId);

    /**
     * 查询提现记录列表
     *
     * @param req 请求参数
     * @param isExport 是否是导出
     * @return 结果列表
     */
    PageInfo<WithdrawListVO> selectWithdrawList(WithdrawListReq req,boolean isExport);

    /**
     * 提现记录审核
     *
     * @param req 请求参数
     * @return 结果
     */
    boolean checkWithdraw(WithdrawStatusReq req);

    /**
     * 提现申请
     *
     * @param req 请求参数
     * @return 结果 id
     */
    Long withdrawApply(WithdrawApplyReq req);

    /**
     * 提现单详情
     *
     * @param withdrawId 提现单ID
     * @return 提现单详情
     */
    WithdrawInfoVO withdrawInfo(Long withdrawId);

    /**
     * 更新提现单文件
     *
     * @param req 请求参数
     * @return 结果
     */
    boolean updateWithdrawFile(WithdrawFileReq req);

    /**
     * 查询账户的已提现和待审核的提现金额总和
     *
     * @param accountId 账户ID
     * @return 提现金额总和
     */
    Integer sumWithdrawAmountByAccountId(Long accountId);

    /**
     * 查询账户的已提现和待审核的提现金额总和
     *
     * @param accountIds 账户ID列表
     * @return 账户-提现金额总和映射
     */
    Map<Long, Integer> sumWithdrawAmountByAccountId(List<Long> accountIds);
}
