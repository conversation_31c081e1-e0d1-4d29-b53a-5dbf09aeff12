package com.ruoyi.system.service.domain.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.google.common.base.Joiner;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.common.EnableStatusEnum;
import com.ruoyi.common.enums.domain.DomainStatus;
import com.ruoyi.common.enums.domain.DomainType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.mapper.manager.DomainMapper;
import com.ruoyi.system.mapper.manager.SlotConfigMapper;
import com.ruoyi.system.req.manager.DomainReq;
import com.ruoyi.system.req.manager.DomainUpdateStatusReq;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.manager.DomainReplaceVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.net.ssl.HttpsURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.domain.DomainStatus.DISABLE;
import static com.ruoyi.common.enums.domain.DomainStatus.NORMAL;
import static com.ruoyi.common.enums.domain.DomainStatus.isNormal;

/**
 * 域名Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-16
 */
@Slf4j
@Service
public class DomainServiceImpl implements DomainService {

    @Autowired
    private DomainMapper domainMapper;

    @Autowired
    private SlotConfigMapper slotConfigMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RefreshCacheService refreshCacheService;

    /**
     * 查询域名
     *
     * @param id 域名ID
     * @return 域名
     */
    @Override
    public Domain selectDomainById(Long id) {
        if (null == id) {
            return null;
        }
        return domainMapper.selectDomainById(id);
    }

    @Override
    public Domain selectDomain(String domain) {
        if (StringUtils.isBlank(domain)) {
            return null;
        }
        return domainMapper.selectByDomain(domain);
    }

    /**
     * 查询域名列表
     *
     * @param domain 域名
     * @return 域名
     */
    @Override
    public List<Domain> selectDomainList(Domain domain) {
        return domainMapper.selectDomainList(domain);
    }

    @Override
    public Set<String> selectAlipayValidDomainList(Integer domainType) {
        Domain param = new Domain();
        param.setDomainType(domainType);
        param.setDomainStatus(DomainStatus.NORMAL.getStatus());
        param.setAlipayStatus(DomainStatus.NORMAL.getStatus());
        if (SpringEnvironmentUtils.isProd()) {
            param.setHttpsEnable(EnableStatusEnum.ENABLE.getStatus());
        }
        List<Domain> list = selectDomainList(param);
        return list.stream()
                .filter(domain -> !StrUtil.contains(domain.getRemark(), "专用") && !StrUtil.equals(domain.getDomain(), "lego.zidg.com"))
                .map(Domain::getDomain).collect(Collectors.toSet());
    }

    @Override
    public Set<String> selectWechatValidDomainList(Integer domainType) {
        Domain param = new Domain();
        param.setDomainType(domainType);
        param.setDomainStatus(DomainStatus.NORMAL.getStatus());
        param.setWxStatus(DomainStatus.NORMAL.getStatus());
        if (SpringEnvironmentUtils.isProd()) {
            param.setHttpsEnable(EnableStatusEnum.ENABLE.getStatus());
        }
        List<Domain> list = selectDomainList(param);
        return list.stream()
                .filter(domain -> !StrUtil.contains(domain.getRemark(), "专用") && !StrUtil.equals(domain.getDomain(), "lego.zidg.com"))
                .map(Domain::getDomain).collect(Collectors.toSet());
    }

    @Override
    public Map<Integer, List<DomainReplaceVO>> listValidByGroup() {
        Domain param = new Domain();
        param.setDomainStatus(DomainStatus.NORMAL.getStatus());
        List<Domain> domains = domainMapper.selectDomainList(param);
        if (CollectionUtils.isEmpty(domains)) {
            return Collections.emptyMap();
        }

        Map<Integer, List<DomainReplaceVO>> map = new HashMap<>();
        DomainType.types().forEach(e -> map.put(e, new ArrayList<>()));
        domains.forEach(domain -> map.getOrDefault(domain.getDomainType(), new ArrayList<>()).add(BeanUtil.copyProperties(domain, DomainReplaceVO.class)));
        return map;
    }

    /**
     * 新增域名
     *
     * @param req 请求参数
     * @return 结果
     */
    @Override
    public int insertDomain(DomainReq req) {
        Domain domain = domainMapper.selectByDomain(req.getDomain());
        if (null != domain) {
            throw new CustomException("域名已存在");
        }

        Domain param = BeanUtil.copyProperties(req, Domain.class);
        // 默认正常，通过定时任务刷新域名状态
        param.setDomainStatus(DomainStatus.NORMAL.getStatus());
        int result = domainMapper.insertDomain(param);
        if (result > 0) {
            refreshCacheService.sendRefreshDomainCacheMsg(req.getDomain());
        }
        return result;
    }

    /**
     * 修改域名
     *
     * @param req 请求参数
     * @return 结果
     */
    @Override
    public int updateDomain(DomainReq req) {
        Domain domain = selectDomainById(req.getId());
        if (null == domain) {
            throw new CustomException("域名不存在");
        }

        Domain updateDomain = BeanUtil.copyProperties(req, Domain.class);
        int result = domainMapper.updateDomain(updateDomain);
        if (result > 0) {
            refreshCacheService.sendRefreshDomainCacheMsg(req.getDomain());
        }
        return result;
    }

    @Override
    public boolean updateWxAlipayStatus(DomainUpdateStatusReq req) {
        Domain domain = selectDomainById(req.getId());
        if (null == domain || (null == req.getAlipayStatus() && null == req.getWxStatus())) {
            return false;
        }
        int result = domainMapper.updateWxAlipayStatus(req);
        if (result > 0) {
            refreshCacheService.sendRefreshDomainCacheMsg(domain.getDomain());
            if (null != req.getAlipayStatus()) {
                checkAlipayValidDomainCount(domain.getDomainType());
            }
            if (null != req.getWxStatus()) {
                checkWechatValidDomainCount(domain.getDomainType());
            }
        }
        return result > 0;
    }

    @Override
    public boolean updateWxDisable(String domain) {
        Domain domainDO = selectDomain(domain);
        if (null == domainDO || !isNormal(domainDO.getWxStatus())) {
            return false;
        }
        DomainUpdateStatusReq req = new DomainUpdateStatusReq();
        req.setId(domainDO.getId());
        req.setWxStatus(DISABLE.getStatus());
        boolean result = updateWxAlipayStatus(req);
        if (result) {
            DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), StrUtil.format("域名 {} 已自动更新为微信不可用。", domain));
        }
        return result;
    }

    @Override
    public boolean updateAlipayDisable(String domain) {
        Domain domainDO = selectDomain(domain);
        if (null == domainDO || !isNormal(domainDO.getAlipayStatus())) {
            return false;
        }
        DomainUpdateStatusReq req = new DomainUpdateStatusReq();
        req.setId(domainDO.getId());
        req.setAlipayStatus(DISABLE.getStatus());
        boolean result = updateWxAlipayStatus(req);
        if (result) {
            DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), StrUtil.format("域名 {} 已自动更新为支付宝不可用。", domain));
        }
        return result;
    }

    /**
     * 删除域名信息
     *
     * @param id 域名ID
     * @return 结果
     */
    @Override
    public int deleteDomainById(Long id) {
        Domain domain = selectDomainById(id);
        if (null == domain) {
            throw new CustomException("无效的域名ID");
        }

        // 判断域名是否在被使用
        SlotConfig param = new SlotConfig();
        param.setDomainConfig(domain.getDomain());
        List<SlotConfig> slots = slotConfigMapper.selectSlotConfigList(param);
        if (CollectionUtils.isNotEmpty(slots)) {
            throw new CustomException("域名正在被广告位("
                    + Joiner.on(",").join( slots.stream().map(SlotConfig::getSlotId).collect(Collectors.toList()))
                    + ")使用，暂时无法删除");
        }

        int result = domainMapper.deleteDomainById(id);
        if (result > 0) {
            refreshCacheService.sendRefreshDomainCacheMsg(domain.getDomain());
        }
        return result;
    }

    @Override
    public void updateDomainStatus(String domain, Integer domainStatus) {
        Domain domainDO = selectDomain(domain);
        if (null == domainDO || null == domainStatus) {
            return;
        }
        Domain updateDomain = new Domain();
        updateDomain.setId(domainDO.getId());
        updateDomain.setDomainStatus(domainStatus);
        domainMapper.updateDomain(updateDomain);
        refreshCacheService.sendRefreshDomainCacheMsg(domain);
    }

    @Override
    public int checkDomainStatus(String domain, Integer domainType, Integer httpsEnable) {
        if (StringUtils.isBlank(domain) || null == domainType) {
            return DISABLE.getStatus();
        }

        StringBuilder url = new StringBuilder();
        if (Objects.equals(httpsEnable, 1)) {
            url.append("https://");
        } else {
            url.append("http://");
        }
        url.append(domain);
        if (Objects.equals(domainType, 1)) {
            url.append("/st/open?appKey=7da724dcdc0c4314a21df66a759dd265&sid=852981");
        } else if (Objects.equals(domainType, 2)) {
            url.append("/act/turntable/tqdFY?appKey=7da724dcdc0c4314a21df66a759dd265&slotId=852989&deviceId=c9c99a78-fd23-4323-9ebe-8231760e7a5a&srid=b84e11f3b8d84e03a0ad9f32717bdaa9");
        } else if (Objects.equals(domainType, 3)) {
            url.append("/land/28RU0WWJ");
        } else {
            return DISABLE.getStatus();
        }

        try {
            int status = HttpUtil.createGet(url.toString()).timeout(5000).execute().getStatus();
            return status >= 200 && status <= 302 ? NORMAL.getStatus() : DISABLE.getStatus();
        } catch (Exception ignored) {}
        return DISABLE.getStatus();
    }

    @Override
    public Date getCertificateExpireTime(String domain) {
        if (StringUtils.isBlank(domain)) {
            return null;
        }

        String redisKey = CrmRedisKeyFactory.K011.join(domain);
        String value = redisCache.getCacheObject(redisKey);
        if (null != value) {
            return StringUtils.isBlank(value) ? null : DateUtil.parseDate(value);
        }

        Date expireTime = null;
        try {
            URL url = new URL("https://" + domain);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.connect();
            if (null != connection.getServerCertificates() && connection.getServerCertificates().length > 0) {
                // 第一个就是服务器本身证书，后续的是证书链上的其他证书
                X509Certificate x509Certificate = (X509Certificate) connection.getServerCertificates()[0];
                expireTime = x509Certificate.getNotAfter();
            }
            connection.disconnect();
        } catch (Exception ignore) {}

        if (null != expireTime) {
            redisCache.setCacheObject(redisKey, DateUtil.formatDate(expireTime), 5, TimeUnit.DAYS);
        } else {
            redisCache.setCacheObject(redisKey, "", 1, TimeUnit.DAYS);
        }
        return expireTime;
    }

    /**
     * 检查支付宝可用域名剩余数量
     */
    private void checkAlipayValidDomainCount(Integer domainType) {
        Set<String> list = selectAlipayValidDomainList(domainType);
        if (list.size() < 10 && domainType > 0) {
            DingRobotUtil.sendText(DingWebhookConfig.getBizAlert(),
                    StrUtil.format("支付宝{}域名只剩{}个可用，请及时补充。", DomainType.getDescByType(domainType), list.size()));
        }
    }

    /**
     * 检查微信落地页可用域名剩余数量
     */
    private void checkWechatValidDomainCount(Integer domainType) {
        Set<String> list = selectWechatValidDomainList(domainType);
        if (list.size() < 10 && domainType > 0) {
            DingRobotUtil.sendText(DingWebhookConfig.getBizAlert(),
                    StrUtil.format("微信{}域名只剩{}个可用，请及时补充。", DomainType.getDescByType(domainType), list.size()));
        }
    }
}
