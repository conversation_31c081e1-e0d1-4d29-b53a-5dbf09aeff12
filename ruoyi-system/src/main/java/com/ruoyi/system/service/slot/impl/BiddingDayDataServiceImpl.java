package com.ruoyi.system.service.slot.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.slot.BiddingDayDataService;
import com.ruoyi.system.entity.slot.BiddingDayDataEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.datashow.BiddingDayDataMapper;

/**
 * 投流日数据 Service
 *
 * <AUTHOR>
 * @date 2023-7-31 11:55:54
 */
@Service
public class BiddingDayDataServiceImpl implements BiddingDayDataService {

    @Autowired
    private BiddingDayDataMapper biddingDayDataMapper;

    @Autowired
    private RedisCache redisCache;

    @Override
    public Boolean insert(BiddingDayDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return biddingDayDataMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return biddingDayDataMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(BiddingDayDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return biddingDayDataMapper.updateById(entity) > 0;
    }

    @Override
    public BiddingDayDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return biddingDayDataMapper.selectById(id);
    }

    @Override
    public Map<String, BiddingDayDataEntity> countByDateAndSlotIds(Date startDate, Date endDate, List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }
        List<BiddingDayDataEntity> list = biddingDayDataMapper.countByDateAndSlotIds(startDate, endDate, slotIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(s ->  s.getSlotId() + "_" + DateUtil.formatDate(s.getCurDate()), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public BiddingDayDataEntity sumByDateAndSlotIds(Date startDate, Date endDate, List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return new BiddingDayDataEntity();
        }
        return biddingDayDataMapper.sumByDateAndSlotIds(startDate, endDate, slotIds);
    }

    @Override
    public void incrConv(Date curDate, Long slotId, String advertiserId) {
        if (null == curDate || null == slotId) {
            return;
        }
        String key = EngineRedisKeyFactory.K022.join("BiddingDayDataEntity", DateUtil.formatDate(curDate), slotId, advertiserId);
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            biddingDayDataMapper.incrConv(dataId);
            return;
        }

        BiddingDayDataEntity data = biddingDayDataMapper.selectBy(curDate, slotId, advertiserId);
        if (null == data) {
            data = new BiddingDayDataEntity();
            data.setCurDate(curDate);
            data.setSlotId(slotId);
            data.setAdvertiserId(advertiserId);
            biddingDayDataMapper.insert(data);
            data = biddingDayDataMapper.selectBy(curDate, slotId, advertiserId);
        }
        biddingDayDataMapper.incrConv(data.getId());
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
    }

    @Override
    public boolean updateConsume(BiddingDayDataEntity param) {
        BiddingDayDataEntity data = biddingDayDataMapper.selectBy(param.getCurDate(), null, param.getAdvertiserId());
        if (null == data) {
            data = new BiddingDayDataEntity();
            data.setCurDate(param.getCurDate());
            data.setSlotId(param.getSlotId());
            data.setAdvertiserId(param.getAdvertiserId());
            biddingDayDataMapper.insert(data);
            data = biddingDayDataMapper.selectBy(param.getCurDate(), null, param.getAdvertiserId());
        }

        BiddingDayDataEntity updateData = new BiddingDayDataEntity();
        updateData.setId(data.getId());
        updateData.setSlotId(param.getSlotId());
        updateData.setConsume(param.getConsume());
        updateData.setOperatorId(param.getOperatorId());
        updateData.setOperatorName(param.getOperatorName());
        return biddingDayDataMapper.updateById(updateData) > 0;
    }
}
