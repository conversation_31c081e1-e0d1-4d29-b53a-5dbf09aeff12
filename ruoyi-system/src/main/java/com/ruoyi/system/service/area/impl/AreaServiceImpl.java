package com.ruoyi.system.service.area.impl;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.Ip2regionInfo;
import com.ruoyi.system.domain.adengine.IpAreaDto;
import com.ruoyi.system.mapper.area.AreaDatasetLtMapper;
import com.ruoyi.system.mapper.area.AreaDatasetYdMapper;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.domain.manager.Area;
import com.ruoyi.system.entity.area.AreaDataset;
import com.ruoyi.system.domain.manager.AreaVO;
import com.ruoyi.system.mapper.area.AreaDatasetMapper;
import com.ruoyi.system.service.area.Ip2regionSearcher;
import com.ruoyi.system.service.thirdparty.market.HaomiaoApiService;
import com.ruoyi.system.service.thirdparty.market.JuheApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.BizConstants.*;
import static com.ruoyi.common.enums.AreaTypeEnum.*;
import static com.ruoyi.system.service.thirdparty.market.HaomiaoApiService.ISP_MAP;

/**
 * 地域服务实现
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
@Slf4j
@Service
public class AreaServiceImpl implements AreaService {

    @Autowired
    private AreaDatasetMapper areaDatasetMapper;

    @Autowired
    private AreaDatasetLtMapper areaDatasetLtMapper;

    @Autowired
    private AreaDatasetYdMapper areaDatasetYdMapper;

    @Autowired
    private JuheApiService juheApiService;

    @Autowired
    private Ip2regionSearcher ip2regionSearcher;

    @Autowired
    private HaomiaoApiService haomiaoApiService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 地域缓存
     */
    private final LoadingCache<Integer, List<AreaVO>> TOTAL_AREA_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(1)
            .refreshAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Integer, List<AreaVO>>() {

                @Override
                public List<AreaVO> load(Integer param) {
                    return queryTotalArea();
                }

                @Override
                public ListenableFuture<List<AreaVO>> reload(Integer param, List<AreaVO> oldValue) {
                    ListenableFutureTask<List<AreaVO>> task = ListenableFutureTask.create(() -> load(param));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 地域缓存
     */
    private final LoadingCache<Pair<String, String>, List<AreaVO>> AREA_LIST_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(1000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build(new CacheLoader<Pair<String, String>, List<AreaVO>>() {

                @Override
                public List<AreaVO> load(Pair<String, String> param) {
                    return queryAreaList(param.getKey(), param.getValue());
                }

                @Override
                public ListenableFuture<List<AreaVO>> reload(Pair<String, String> param, List<AreaVO> oldValue) {
                    ListenableFutureTask<List<AreaVO>> task = ListenableFutureTask.create(() -> load(param));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 地域缓存
     */
    private final LoadingCache<String, String> AREA_NAME_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(2000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, String>() {

                @Override
                public String load(String areaName) {
                    AreaDataset area = areaDatasetMapper.selectByAreaName(areaName);
                    if (null == area) {
                        area = areaDatasetMapper.selectByAreaNameLike(areaName);
                    }
                    return null != area ? area.getAreaNum() : StringUtils.EMPTY;
                }

                @Override
                public ListenableFuture<String> reload(String areaName, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(areaName));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    private final LoadingCache<String, String> AREA_NUM_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(2000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, String>() {

                @Override
                public String load(String areaNum) {
                    AreaDataset area = areaDatasetMapper.selectByAreaNum(areaNum);
                    return null != area ? area.getAreaName() : StringUtils.EMPTY;
                }

                @Override
                public ListenableFuture<String> reload(String areaNum, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(areaNum));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    private final LoadingCache<String, Optional<AreaDataset>> AREA_DATASET_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(500)
            .expireAfterAccess(20, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<AreaDataset>>() {

                @Override
                public Optional<AreaDataset> load(String areaNum) {
                    AreaDataset areaData = areaDatasetMapper.selectByAreaNum(areaNum);
                    return Optional.ofNullable(areaData);
                }

                @Override
                public ListenableFuture<Optional<AreaDataset>> reload(String areaNum, Optional<AreaDataset> oldValue) {
                    ListenableFutureTask<Optional<AreaDataset>> task = ListenableFutureTask.create(() -> load(areaNum));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 地域缓存
     */
    private final LoadingCache<Pair<String, String>, Optional<Area>> AREA_LYTD_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(2000)
            .expireAfterAccess(20, TimeUnit.MINUTES)
            .build(new CacheLoader<Pair<String, String>, Optional<Area>>() {

                @Override
                public Optional<Area> load(Pair<String, String> param) {
                    Area area = queryAreaByAreaNumLtYd(param.getKey(), param.getValue());
                    return Optional.ofNullable(area);
                }

                @Override
                public ListenableFuture<Optional<Area>> reload(Pair<String, String> param, Optional<Area> oldValue) {
                    ListenableFutureTask<Optional<Area>> task = ListenableFutureTask.create(() -> load(param));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    @Override
    public List<AreaVO> searchProvinceAndCity(String searchKey) {
        List<AreaDataset> areaList;
        AreaDataset param = new AreaDataset();
        Map<String, AreaDataset> provinceMap = new HashMap<>();
        Map<String, AreaDataset> areaMap = new HashMap<>();
        if (StringUtils.isNumeric(searchKey)) {
            // 查询行政区划代码
            param.setAreaNum(searchKey);
            areaList = areaDatasetMapper.selectAreaDatasetList(param);
            if (CollectionUtils.isNotEmpty(areaList)) {
                for (AreaDataset areaData : areaList) {
                    provinceMap.put(areaData.getAreaNum(), areaData);
                }
            }
        } else {
            // 查询市
            param.setAreaName(searchKey);
            param.setAreaType(CITY.getType());
            areaList = areaDatasetMapper.selectAreaDatasetList(param);
            if (CollectionUtils.isNotEmpty(areaList)) {
                for (AreaDataset areaData : areaList) {
                    areaMap.put(areaData.getAreaNum(), areaData);
                }
            }
            // 查询省
            param.setAreaName(searchKey);
            param.setAreaType(PROVINCE.getType());
            areaList = areaDatasetMapper.selectAreaDatasetList(param);
            if (CollectionUtils.isNotEmpty(areaList)) {
                for (AreaDataset areaData : areaList) {
                    provinceMap.put(areaData.getAreaNum(), areaData);
                }
            }
            // 查询省下辖的市
            if (MapUtils.isNotEmpty(provinceMap)) {
                param = new AreaDataset();
                param.setAreaType(CITY.getType());
                param.setParentNums(new ArrayList<>(provinceMap.keySet()));
                areaList = areaDatasetMapper.selectAreaDatasetList(param);
                if (CollectionUtils.isNotEmpty(areaList)) {
                    for (AreaDataset areaData : areaList) {
                        areaMap.put(areaData.getAreaNum(), areaData);
                    }
                }
                areaMap.putAll(provinceMap);
            }
        }
        if (MapUtils.isEmpty(areaMap)) {
            return Collections.emptyList();
        }
        return areaMap.values().stream().map(area -> {
            AreaVO areaVO = convertTo(area);
            // 市补充省名称
            if (Objects.equals(area.getAreaType(), CITY.getType())) {
                if (provinceMap.containsKey(area.getParentNum())) {
                    areaVO.setAreaName(provinceMap.get(area.getParentNum()).getAreaName() + "，" + areaVO.getAreaName());
                } else {
                    areaVO.setAreaName(queryAreaNameByNum(area.getParentNum()) + "，" + areaVO.getAreaName());
                }
            }
            areaVO.setAreaName("中国，" + areaVO.getAreaName());
            return areaVO;
        }).sorted(Comparator.comparing(AreaVO::getAreaNum)).collect(Collectors.toList());
    }

    @Override
    public String queryAreaNumByName(String areaName) {
        if (StringUtils.isBlank(areaName)) {
            return StringUtils.EMPTY;
        }

        try {
            return AREA_NAME_CACHE.get(areaName);
        } catch (ExecutionException e) {
            return StringUtils.EMPTY;
        }
    }

    @Override
    public List<String> queryAreaNumByName(List<String> areaNames) {
        if (CollectionUtils.isEmpty(areaNames)) {
            return Collections.emptyList();
        }

        AreaDataset param = new AreaDataset();
        param.setAreaTypes(Arrays.asList(PROVINCE.getType(), CITY.getType()));
        param.setAreaNames(areaNames);
        List<AreaDataset> areaList = areaDatasetMapper.selectAreaDatasetList(param);
        return areaList.stream().map(AreaDataset::getAreaNum).collect(Collectors.toList());
    }

    @Override
    public String queryAreaNameByNum(String areaNum) {
        if (StringUtils.isBlank(areaNum)) {
            return StringUtils.EMPTY;
        }

        try {
            return AREA_NUM_CACHE.get(areaNum);
        } catch (ExecutionException e) {
            return StringUtils.EMPTY;
        }
    }

    @Override
    public Area queryAreaByAreaNum(String areaNum) {
        if (StringUtils.isBlank(areaNum)) {
            return null;
        }

        // TODO 默认查询的是区，后续优化
        AreaDataset areaData = selectByAreaNumCache(areaNum);
        if (null == areaData) {
            return null;
        }

        Area area = new Area();
        area.setAreaNum(areaNum);
        area.setDistrict(areaData.getAreaName());
        areaData = selectByAreaNumCache(areaData.getParentNum());
        if (null == areaData) {
            return area;
        }
        area.setCity(areaData.getAreaName());
        if (Objects.equals(areaData.getAreaNum(), areaData.getParentNum())) {
            area.setProvince(areaData.getAreaName());
            return area;
        }
        areaData = selectByAreaNumCache(areaData.getParentNum());
        if (null == areaData) {
            return area;
        }
        area.setProvince(areaData.getAreaName());
        return area;
    }

    @Override
    public Area queryAreaByAreaNumLtYdCache(String tag, String areaNum) {
        if (StringUtils.isBlank(areaNum)) {
            return null;
        }

        try {
            return AREA_LYTD_CACHE.get(Pair.of(tag, areaNum)).orElse(null);
        } catch (ExecutionException e) {
            log.error("queryAreaByAreaNumLtYdCache ERROR, tag={}, areaNum={}", tag, areaNum);
        }
        return null;
    }

    private Area queryAreaByAreaNumLtYd(String tag, String areaNum) {
        if (StringUtils.isBlank(areaNum)) {
            return null;
        }

        AreaDataset areaData = selectByAreaNumAndType(tag, areaNum, DISTRICT.getType());
        if (null == areaData) {
            return null;
        }

        Area area = new Area();
        area.setAreaNum(areaNum);
        area.setDistrict(areaData.getAreaName());
        areaData = selectByAreaNumAndType(tag, areaData.getParentNum(), CITY.getType());
        if (null == areaData) {
            return area;
        }
        area.setCity(areaData.getAreaName());
        areaData = selectByAreaNumAndType(tag, areaData.getParentNum(), PROVINCE.getType());
        if (null == areaData) {
            return area;
        }
        area.setProvince(areaData.getAreaName());
        return area;
    }

    @Override
    public List<AreaVO> queryAreaListCache(String tag, String areaNum) {
        try {
            return AREA_LIST_CACHE.get(Pair.of(tag, areaNum));
        } catch (ExecutionException e) {
            log.error("落地页查询地域异常, tag={}, areaNum={}", tag, areaNum, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AreaVO> queryTotalAreaCache() {
        try {
            return TOTAL_AREA_CACHE.get(1);
        } catch (ExecutionException e) {
            log.error("落地页查询地域异常", e);
        }
        return Collections.emptyList();
    }

    private List<AreaVO> queryTotalArea() {
        // 查询所有的省市区
        List<AreaDataset> areaList = selectAreaDatasetList(AREA_TAG_LT, new AreaDataset());
        if (CollectionUtils.isEmpty(areaList)) {
            return Collections.emptyList();
        }
        List<AreaDataset> provinces = new ArrayList<>();
        Map<String, List<AreaDataset>> cityMap = new HashMap<>();
        Map<String, List<AreaDataset>> districtMap = new HashMap<>();

        for (AreaDataset area : areaList) {
            if (Objects.equals(PROVINCE.getType(), area.getAreaType())) {
                provinces.add(area);
            } else if (Objects.equals(CITY.getType(), area.getAreaType())) {
                if (!cityMap.containsKey(area.getParentNum())) {
                    cityMap.put(area.getParentNum(), new ArrayList<>());
                }
                cityMap.get(area.getParentNum()).add(area);
            } else if (Objects.equals(DISTRICT.getType(), area.getAreaType())) {
                if (!districtMap.containsKey(area.getParentNum())) {
                    districtMap.put(area.getParentNum(), new ArrayList<>());
                }
                districtMap.get(area.getParentNum()).add(area);
            }
        }
        return provinces.stream().map(province -> {
            AreaVO area = convertTo(province);
            area.setChildren(
                    cityMap.getOrDefault(area.getAreaNum(), Collections.emptyList())
                            .stream().map(city -> {
                                AreaVO cityArea = convertTo(city);
                                cityArea.setChildren(
                                        districtMap.getOrDefault(cityArea.getAreaNum(), Collections.emptyList()).stream()
                                                .map(this::convertTo).collect(Collectors.toList())
                                );
                                return cityArea;
                            }).collect(Collectors.toList())
            );
            return area;
        }).collect(Collectors.toList());
    }

    private List<AreaVO> queryAreaList(String tag, String areaNum) {
        // 查询所有的省
        if (StringUtils.isBlank(areaNum)) {
            AreaDataset param = new AreaDataset();
            param.setAreaType(PROVINCE.getType());
            List<AreaDataset> areaList = selectAreaDatasetList(tag, param);
            if (CollectionUtils.isEmpty(areaList)) {
                return Collections.emptyList();
            }
            return areaList.stream().map(this::convertTo).collect(Collectors.toList());
        }

        // 查询省下面的市
        AreaDataset cityParam = new AreaDataset();
        cityParam.setParentNum(areaNum);
        cityParam.setAreaType(CITY.getType());
        List<AreaDataset> cityList = selectAreaDatasetList(tag, cityParam);
        if (CollectionUtils.isEmpty(cityList)) {
            return Collections.emptyList();
        }
        // 查询市下面的区
        AreaDataset districtParam = new AreaDataset();
        districtParam.setParentNums(cityList.stream().map(AreaDataset::getAreaNum).collect(Collectors.toList()));
        districtParam.setAreaType(DISTRICT.getType());
        List<AreaDataset> districtList = selectAreaDatasetList(tag, districtParam);

        // 构造市-区的映射
        Map<String, List<AreaVO>> cityMap = new HashMap<>();
        for (AreaDataset area : districtList) {
            if (!cityMap.containsKey(area.getParentNum())) {
                cityMap.put(area.getParentNum(), new ArrayList<>());
            }
            cityMap.get(area.getParentNum()).add(convertTo(area));
        }

        // 构造返回结果
        return cityList.stream().map(city -> {
            AreaVO area = convertTo(city);
            area.setChildren(cityMap.get(area.getAreaNum()));
            if (CollectionUtils.isEmpty(area.getChildren()) && Integer.parseInt(city.getAreaNum()) % 100 > 0) {
                AreaVO district = new AreaVO();
                district.setAreaName(city.getAreaName());
                district.setAreaType(DISTRICT.getType());
                district.setAreaNum(city.getAreaNum());
                area.setChildren(Collections.singletonList(district));
            }
            return area;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AreaVO> queryTotalProvinceAndCity() {
        AreaVO top = new AreaVO();
        top.setAreaName("中国");
        top.setAreaType(COUNTRY.getType());
        top.setAreaNum(CHINA_AREA_NUM);
        top.setChildren(new ArrayList<>());

        // 查询所有的整型
        AreaDataset param = new AreaDataset();
        param.setAreaTypes(Arrays.asList(PROVINCE.getType(), CITY.getType()));
        List<AreaDataset> areaList = areaDatasetMapper.selectAreaDatasetList(param);
        if (CollectionUtils.isEmpty(areaList)) {
            return Collections.singletonList(top);
        }

        Map<String, AreaDataset> provinceMap = new HashMap<>();
        Map<String, List<AreaDataset>> provinceCityMap = new HashMap<>();

        // 遍历省
        for (AreaDataset areaData : areaList) {
            String areaNum = areaData.getAreaNum();
            if (Objects.equals(areaData.getAreaType(), PROVINCE.getType())) {
                provinceMap.put(areaNum, areaData);
                provinceCityMap.put(areaNum, new ArrayList<>());
            }
        }
        // 遍历市
        for (AreaDataset areaData : areaList) {
            if (Objects.equals(areaData.getAreaType(), CITY.getType())
                    && provinceMap.containsKey(areaData.getParentNum())) {
                provinceCityMap.get(areaData.getParentNum()).add(areaData);
            }
        }

        provinceMap.values().forEach(province -> {
            String areaNum = province.getAreaNum();
            AreaVO areaVO = convertTo(province);
            areaVO.setChildren(provinceCityMap.getOrDefault(areaNum, new ArrayList<>())
                    .stream().map(this::convertTo).collect(Collectors.toList()));
            top.getChildren().add(areaVO);
        });

        return Collections.singletonList(top);
    }

    @Override
    public List<String> queryTotalProvince() {
        AreaDataset param = new AreaDataset();
        param.setAreaTypes(Collections.singletonList(PROVINCE.getType()));
        List<AreaDataset> areaList = areaDatasetMapper.selectAreaDatasetList(param);
        return areaList.stream().map(AreaDataset::getAreaName).collect(Collectors.toList());
    }

    @Override
    public IpAreaDto ipAnalysis(String ip) {
        if (StringUtils.isBlank(ip)) {
            return new IpAreaDto();
        }

        // 查询Redis缓存
        String redisKey = EngineRedisKeyFactory.K093.join(ip);
        IpAreaDto ipAreaDto = getIpAreaCache(redisKey);
        if (null != ipAreaDto) {
            return ipAreaDto;
        }

        // 使用聚合IP接口
        JSONObject ipResult = juheApiService.ipAnalysis(ip);
        if (null != ipResult && null != ipResult.getJSONObject("result")) {
            JSONObject result = ipResult.getJSONObject("result");
            IpAreaDto ipArea = new IpAreaDto();
            ipArea.setCountry(result.getString("Country"));
            ipArea.setProvince(result.getString("Province"));
            ipArea.setCity(result.getString("City"));
            ipArea.setIsp(result.getString("Isp"));
            ipArea.setProvinceAreaNum(queryAreaNumByName(ipArea.getProvince()));
            ipArea.setCityAreaNum(queryAreaNumByName(ipArea.getCity()));
            cacheIpAreaCache(redisKey, ipArea);
            return ipArea;
        }

        // 使用毫秒科技IP接口
//        ipResult = haomiaoApiService.ipAnalysis(ip);
//        if (null != ipResult && null != ipResult.getJSONObject("data")) {
//            JSONObject result = ipResult.getJSONObject("data");
//            IpAreaDto ipArea = new IpAreaDto();
//            ipArea.setCountry(result.getString("country_name"));
//            ipArea.setProvince(result.getString("region_name"));
//            ipArea.setCity(result.getString("city_name"));
//            ipArea.setIsp(ISP_MAP.get(result.getString("isp_domain")));
//            ipArea.setProvinceAreaNum(queryAreaNumByName(ipArea.getProvince()));
//            ipArea.setCityAreaNum(queryAreaNumByName(ipArea.getCity()));
//            cacheIpAreaCache(redisKey, ipArea);
//            return ipArea;
//        }

        // 使用ip2region
        Ip2regionInfo ipInfo= ip2regionSearcher.search(ip);
        if (null != ipInfo) {
            IpAreaDto ipArea = new IpAreaDto();
            ipArea.setCountry(ipInfo.getCountry());
            ipArea.setProvince(ipInfo.getProvince());
            ipArea.setCity(ipInfo.getCity());
            ipArea.setIsp(ipInfo.getIsp());
            ipArea.setProvinceAreaNum(queryAreaNumByName(ipArea.getProvince()));
            ipArea.setCityAreaNum(queryAreaNumByName(ipArea.getCity()));
            return ipArea;
        }

        return new IpAreaDto();
    }

    @Override
    public IpAreaDto ipAnalysisLocal(String ip) {
        if (StringUtils.isBlank(ip)) {
            return new IpAreaDto();
        }

        // 查询Redis缓存
        String redisKey = EngineRedisKeyFactory.K093.join(ip);
        IpAreaDto ipAreaDto = getIpAreaCache(redisKey);
        if (null != ipAreaDto) {
            return ipAreaDto;
        }

        // 使用ip2region
        Ip2regionInfo ipInfo= ip2regionSearcher.search(ip);
        if (null != ipInfo) {
            IpAreaDto ipArea = new IpAreaDto();
            ipArea.setCountry(ipInfo.getCountry());
            ipArea.setProvince(ipInfo.getProvince());
            ipArea.setCity(ipInfo.getCity());
            ipArea.setIsp(ipInfo.getIsp());
            ipArea.setProvinceAreaNum(queryAreaNumByName(ipArea.getProvince()));
            ipArea.setCityAreaNum(queryAreaNumByName(ipArea.getCity()));
            return ipArea;
        }

        return new IpAreaDto();
    }

    /**
     * convert AreaDataset to AreaVO
     */
    private AreaVO convertTo(AreaDataset areaData) {
        AreaVO areaVO = new AreaVO();
        areaVO.setAreaName(areaData.getAreaName());
        areaVO.setAreaType(areaData.getAreaType());
        areaVO.setAreaNum(areaData.getAreaNum());
        return areaVO;
    }

    private List<AreaDataset> selectAreaDatasetList(String tag, AreaDataset param) {
        if (Objects.equals(AREA_TAG_LT, tag) || Objects.equals(AREA_TAG_DX, tag)) {
            return areaDatasetLtMapper.selectAreaDatasetList(param);
        } else if (Objects.equals(AREA_TAG_YD, tag)) {
            return areaDatasetYdMapper.selectAreaDatasetList(param);
        } else {
            // 直辖市特殊处理(以后再优化吧)
            if (Objects.equals(param.getAreaType(), CITY.getType())) {
                if (Objects.equals(param.getParentNum(), "110000")) {
                    AreaDataset area = new AreaDataset();
                    area.setAreaName("北京市");
                    area.setAreaType(2);
                    area.setAreaNum("110000");
                    return Collections.singletonList(area);
                } else if (Objects.equals(param.getParentNum(), "120000")) {
                    AreaDataset area = new AreaDataset();
                    area.setAreaName("天津市");
                    area.setAreaType(2);
                    area.setAreaNum("120000");
                    return Collections.singletonList(area);
                } else if (Objects.equals(param.getParentNum(), "310000")) {
                    AreaDataset area = new AreaDataset();
                    area.setAreaName("上海市");
                    area.setAreaType(2);
                    area.setAreaNum("310000");
                    return Collections.singletonList(area);
                } else if (Objects.equals(param.getParentNum(), "500000")) {
                    AreaDataset area = new AreaDataset();
                    area.setAreaName("重庆市");
                    area.setAreaType(2);
                    area.setAreaNum("500000");
                    return Collections.singletonList(area);
                }
            }
            return areaDatasetMapper.selectAreaDatasetList(param);
        }
    }

    private AreaDataset selectByAreaNumAndType(String tag, String areaNum, Integer areaType) {
        if (Objects.equals(AREA_TAG_LT, tag) || Objects.equals(AREA_TAG_DX, tag)) {
            return areaDatasetLtMapper.selectByAreaNumAndType(areaNum, areaType);
        } else if (Objects.equals(AREA_TAG_YD, tag)) {
            return areaDatasetYdMapper.selectByAreaNumAndType(areaNum, areaType);
        } else {
            return areaDatasetMapper.selectByAreaNumAndType(areaNum, areaType);
        }
    }

    private AreaDataset selectByAreaNumCache(String areaNum) {
        if (StringUtils.isBlank(areaNum)) {
            return null;
        }
        try {
            return AREA_DATASET_CACHE.get(areaNum).orElse(null);
        } catch (ExecutionException e) {
            log.error("selectByAreaNumCache ERROR, areaNum={}", areaNum, e);
        }
        return null;
    }

    private IpAreaDto getIpAreaCache(String redisKey) {
        try {
            String value = redisCache.getCacheObject(redisKey);
            if (StringUtils.isNotBlank(value)) {
                redisCache.expire(redisKey, 2, TimeUnit.MINUTES);
                return JSON.parseObject(value, IpAreaDto.class);
            }
        } catch (Exception e) {
            log.error("getIpAreaCache error, redisKey={}", redisKey, e);
        }
        return null;
    }

    private void cacheIpAreaCache(String redisKey, IpAreaDto ipArea) {
        try {
            redisCache.setCacheObject(redisKey, JSON.toJSONString(ipArea), 2, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("cacheIpAreaCache error, redisKey={}", redisKey, e);
        }
    }
}
