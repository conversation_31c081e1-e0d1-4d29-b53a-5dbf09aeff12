package com.ruoyi.system.service.common.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.enums.common.MapConfigEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.system.SysConfig;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.system.ISysConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 映射配置服务接口实现
 *
 * <AUTHOR>
 * @date 2022/9/7
 */
@Service
public class MapConfigServiceImpl implements MapConfigService {

    /**
     * 映射KV分隔符
     */
    private static final char SEPARATOR = '-';

    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public <K, V> Map<K, V> getMap(MapConfigEnum config, Class<K> kClass, Class<V> vClass) {
        List<String> list = getList(config);
        Map<K, V> map = new HashMap<>(list.size());
        list.forEach(entry -> {
            List<String> kv = StrUtil.split(entry, SEPARATOR, 2, true, false);
            map.put(Convert.convert(kClass, kv.get(0)), Convert.convert(vClass, kv.get(1)));
        });
        return map;
    }

    @Override
    public <K, V> V getValue(MapConfigEnum config, K key, Class<V> vClass) {
        if (null == key) {
            return null;
        }
        List<String> list = getList(config);
        for (String entry : list) {
            List<String> kv = StrUtil.split(entry, SEPARATOR, 2, true, false);
            if (Objects.equals(Convert.convert(key.getClass(), kv.get(0)), key)) {
                return Convert.convert(vClass, kv.get(1));
            }
        }
        return null;
    }

    @Override
    public <K> Set<K> getKeySet(MapConfigEnum config, Class<K> kClass) {
        return getList(config).stream().map(ele -> {
            List<String> eles = StrUtil.split(ele, SEPARATOR, 2, true, false);
            return Convert.convert(kClass, eles.get(0));
        }).collect(Collectors.toSet());
    }

    @Override
    public <V> Set<V> getValueSet(MapConfigEnum config, Class<V> vClass) {
        return getList(config).stream().map(ele -> {
            List<String> eles = StrUtil.split(ele, SEPARATOR, 2, true, false);
            return Convert.convert(vClass, eles.get(1));
        }).collect(Collectors.toSet());
    }

    @Override
    public List<String> getList(MapConfigEnum config) {
        String configValue = sysConfigService.selectConfigCacheByKey(config.getKey());
        if (StringUtils.isBlank(configValue)) {
            return Collections.emptyList();
        }
        List<String> list = JSON.parseArray(configValue, String.class);
        return null == list ? Collections.emptyList() : list;
    }

    @Override
    public void remove(MapConfigEnum config, String key) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        SysConfig configValue = sysConfigService.selectByKey(config.getKey());
        if (null == configValue || StringUtils.isBlank(configValue.getConfigValue())) {
            return;
        }
        List<String> list = JSON.parseArray(configValue.getConfigValue(), String.class);
        if (CollectionUtils.isNotEmpty(list)) {
            String prefix = key + SEPARATOR;
            configValue.setConfigValue(JSON.toJSONString(list.stream().filter(e -> !e.startsWith(prefix)).collect(Collectors.toList())));
            sysConfigService.updateConfig(configValue);
        }
    }
}
