package com.ruoyi.system.service.adengine.filter;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.service.adengine.AdvertFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

/**
 * 定向媒体广告位过滤
 */
@Slf4j
@Component
public class OrientAppFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();
        Long appId = context.getAppId();
        Long slotId = context.getSlotId();

        // 无定向广告位(定向流量包)且无定向媒体，可以出
        if (CollectionUtils.isEmpty(ad.getOrientSlotIds()) && CollectionUtils.isEmpty(ad.getOrientAppIds())) {
            return true;
        }
        // 无定向广告位(定向流量包)且无广告位，可以出
        if (CollectionUtils.isEmpty(ad.getOrientSlotIds()) && MapUtils.isEmpty(ad.getOrientSlotMap())) {
            return true;
        }
        // 定向广告位(定向流量包)包含当前广告，可以出
        // 有定向媒体无定向广告位，都可以出(老数据兼容)
        // 定向广告位包含当前广告位，则可以出
        return CollUtil.contains(ad.getOrientSlotIds(), slotId)
                || (ad.getOrientAppIds().contains(appId)
                    && (CollectionUtils.isEmpty(ad.getOrientSlotMap().get(appId)) || ad.getOrientSlotMap().get(appId).contains(slotId)));
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.ORIENTED_APP;
    }
}
