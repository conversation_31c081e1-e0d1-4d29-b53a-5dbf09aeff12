package com.ruoyi.system.service.manager.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.enums.TagManagerTypeEnum;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.enums.advertiser.AdvertiserConsumeType;
import com.ruoyi.common.enums.advertiser.QualificationAuditStatus;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.accounttag.AccountTagRelationEntity;
import com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity;
import com.ruoyi.system.manager.account.OaStaffManger;
import com.ruoyi.system.mapper.manager.AccountMapper;
import com.ruoyi.system.req.account.AccountRegisterReq;
import com.ruoyi.system.service.accounttag.AccountTagRelationService;
import com.ruoyi.system.service.advertiser.AdvertiserQualificationService;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.vo.account.AccountMangerListVO;
import com.ruoyi.system.vo.account.CrmAccountVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.BizConstants.DEFAULT_LP_CALLBACK_URL;
import static com.ruoyi.common.constant.OaConstants.GUANG_GAO_SHANG_WU;
import static com.ruoyi.common.constant.OaConstants.GUANG_GAO_YUN_YING;
import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.account.AccountMainType.isPublisher;
import static com.ruoyi.common.enums.account.AccountStatusEnum.NORMAL;

/**
 * CRM业务通知接口实现
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Service
public class AccountServiceImpl implements AccountService {

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private AccountRelationService accountRelationService;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    @Autowired
    private AdvertiserQualificationService advertiserQualificationService;

    @Autowired
    private OaStaffManger oaStaffManger;

    @Autowired
    private AccountTagRelationService accountTagRelationService;

    @Autowired
    private TagManagerService tagManagerService;

    @Override
    public Account selectAccountById(Long id) {
        if (null == id) {
            return null;
        }
        return accountMapper.selectAccountById(id);
    }

    @Override
    public AccountExtInfo selectAccountExtInfoById(Long id) {
        Account account = selectAccountById(id);
        return null != account ? JSON.parseObject(account.getExtInfo(), AccountExtInfo.class) : null;
    }

    @Override
    public Account selectAccountForLogin(Account account) {
        return accountMapper.selectAccountForLogin(account);
    }

    @Override
    public List<Account> selectAccountList(Account param) {
        return accountMapper.selectAccountList(param);
    }

    @Override
    public List<Account> selectAgentAdvertiserList(Account param) {
        return accountMapper.selectAgentAdvertiserList(param);
    }

    @Override
    public List<Long> selectIdsByIdOrEmail(String accountSearch) {
        if(StringUtils.isEmpty(accountSearch)){
            return Collections.emptyList();
        }
        return accountMapper.selectIdsByIdOrEmail(accountSearch);
    }

    @Override
    public List<Long> selectIdsByIdOrEmailOrCompanyName(String accountSearch) {
        if (StringUtils.isEmpty(accountSearch)) {
            return Collections.emptyList();
        }
        return accountMapper.selectIdsByIdOrEmailOrCompanyName(accountSearch);
    }

    @Override
    public List<Long> selectIdsBySearch(String accountSearch, Integer mainType) {
        if (StringUtils.isBlank(accountSearch)) {
            return Collections.emptyList();
        }
        if (StringUtils.isNumeric(accountSearch)) {
            Account account = accountMapper.selectAccountById(Long.valueOf(accountSearch));
            if (null != account && Objects.equals(account.getMainType(), mainType)) {
                return Collections.singletonList(account.getId());
            }
        }
        return accountMapper.selectIdsByEmailOrCompanyName(accountSearch, mainType);
    }

    @Override
    public List<Long> selectIdsByIdOrEmailAndCompany(String accountSearch, String companyName) {
        return accountMapper.selectIdsByIdOrEmailAndCompany(accountSearch, companyName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertAccount(AccountRegisterReq param) {
        Account account = new Account();
        account.setMainType(param.getMainType());
        account.setEmail(param.getEmail());
        account.setPasswd(param.getPasswd());
        account.setCompanyName(param.getCompanyName());
        account.setContact(StringUtils.defaultString(param.getContact()).trim());
        account.setPhone(StringUtils.defaultString(param.getPhone()));
        String accessKey = null;

        if (isAdvertiser(param.getMainType()) || isAgent(param.getMainType())) {
            AccountExtInfo extInfo = new AccountExtInfo();
            extInfo.setBusinessLicense(param.getBusinessLicense());
            extInfo.setBusinessLicenseImg(param.getBusinessLicenseImg());
            extInfo.setAccessKey(IdUtils.fastSimpleUUID());
            extInfo.setSecretKey(IdUtils.fastSimpleUUID());
            extInfo.setAddress(param.getAddress());
            extInfo.setConsumeType(param.getConsumeType());
            if (isAdvertiser(param.getMainType())) {
                extInfo.setLpCallbackUrl(DEFAULT_LP_CALLBACK_URL);
                if (null == extInfo.getConsumeType()) {
                    extInfo.setConsumeType(AdvertiserConsumeType.CPC.getType());
                }
            }
            account.setExtInfo(JSON.toJSONString(extInfo));
        }
        accountMapper.insertAccount(account);
        if (null != account.getId()) {
            //同步资质信息
            if (isAdvertiser(param.getMainType()) || isAgent(param.getMainType())) {
                AdvertiserQualificationEntity entity = new AdvertiserQualificationEntity();
                entity.setAccountId(account.getId());
                entity.setQualificationName("营业执照");
                entity.setQualificationImg(JSONArray.toJSONString(Lists.newArrayList(param.getBusinessLicenseImg())));
                entity.setExpireTime(null == param.getBusinessExpireTime() ? DateUtils.parseDate("9999-12-31") : param.getBusinessExpireTime()); //默认永久
                entity.setAuditStatus(QualificationAuditStatus.READY.getStatus());
                entity.setApplicationTime(new Date());
                entity.setQualificationType(0);
                entity.setIndustryId(0L);
                entity.setQualificationRequireId(0L);
                advertiserQualificationService.batchInsert(Lists.newArrayList(entity));
            }
        }
        return account.getId();
    }

    /**
     * 修改账号
     *
     * @param account 账号
     * @return 结果
     */
    @Override
    public int updateAccount(Account account) {
        return accountMapper.updateAccount(account);
    }

    @Override
    public int updatePassword(Long id, String password) {
        Account account = new Account();
        account.setId(id);
        account.setPasswd(password);
        return updateAccount(account);
    }

    @Override
    public int checkCompanyNameUnique(String companyName, Integer mainType) {
        // CRM用户不校验公司重名
        if (Objects.equals(mainType, AccountMainType.CRM.getType())) {
            return 0;
        }
        return accountMapper.checkCompanyNameUnique(companyName, mainType);
    }

    @Override
    public int checkEmailUnique(String email, Integer mainType) {
        return accountMapper.checkEmailUnique(email, mainType);
    }

    @Override
    public int checkPhoneUnique(String phone, Integer mainType) {
        if (StringUtils.isBlank(phone)) {
            return 0;
        }
        return accountMapper.checkPhoneUnique(phone, mainType);
    }

    @Override
    public int checkBusinessLicenseUnique(String businessLicense) {
        if (StringUtils.isBlank(businessLicense)) {
            return 0;
        }

        Account param = new Account();
        param.setMainType(AccountMainType.ADVERTISER.getType());
        param.setExtInfo(businessLicense);
        List<Account> accounts = accountMapper.selectAccountExtInfo(param);
        if (CollectionUtils.isEmpty(accounts)) {
            return 0;
        }
        for (Account account : accounts) {
            AccountExtInfo extInfo = JSON.parseObject(account.getExtInfo(), AccountExtInfo.class);
            if (null != extInfo && Objects.equals(extInfo.getBusinessLicense(), businessLicense)) {
                return 1;
            }
        }
        return 0;
    }

    @Override
    public Map<Long, String> selectManagerMap(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }

        // 查询媒体账号对应的负责人ID列表
        Map<Long, List<Long>> relMap = accountRelationService.selectByAccountIds(accountIds);
        if (MapUtils.isEmpty(relMap)) {
            return Collections.emptyMap();
        }

        // 查询负责人名称映射
        Set<Long> accountIdSet = new HashSet<>();
        relMap.values().forEach(accountIdSet::addAll);
        Map<Long, String> accountNameMap = selectAccountContactMap(new ArrayList<>(accountIdSet));

        Map<Long, String> map = new HashMap<>();
        relMap.forEach((key, value) -> {
            List<String> names = new ArrayList<>();
            value.forEach(id -> names.add(accountNameMap.getOrDefault(id, "")));
            map.put(key, Joiner.on(",").join(names));
        });

        return map;
    }

    @Override
    public List<Account> selectListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return accountMapper.selectByIds(ids);
    }

    @Override
    public Map<Long, Account> selectMapByIds(List<Long> ids) {
        return selectMapByIds(ids, Function.identity());
    }

    @Override
    public Map<Long, AccountExtInfo> selectExtInfoMapByIds(List<Long> ids) {
        return selectMapByIds(ids, s -> JSON.parseObject(s.getExtInfo(), AccountExtInfo.class));
    }

    @Override
    public Map<Long, String> selectEmailByIds(List<Long> ids) {
        return selectMapByIds(ids, Account::getEmail);
    }

    @Override
    public Map<Long, String> selectAccountContactMap(List<Long> accountIds) {
        return selectMapByIds(accountIds, Account::getContact);
    }

    @Override
    public Map<Long, String> selectCompanyNameMap(List<Long> accountIds) {
        return selectMapByIds(accountIds, Account::getCompanyName);
    }

    /**
     * 查询账号的指定属性的映射
     *
     * @param accountIds 账号ID列表
     * @param valueMapper 指定属性
     * @return 账号ID-指定属性映射
     */
    private <T> Map<Long, T> selectMapByIds(List<Long> accountIds, Function<Account, T> valueMapper) {
        List<Account> list = selectListByIds(accountIds);
        return list.stream().collect(Collectors.toMap(Account::getId, valueMapper, (oldVal, newVal) -> newVal));
    }

    /**
     * 新账号注册提醒
     *
     * @param accountId 账号ID
     */
    @Override
    public void registerNoticeAsync(final Long accountId, Long parentId) {
        GlobalThreadPool.executorService.submit(() -> {
            Account account = selectAccountById(accountId);
            if (null == account) {
                return;
            }
            AccountExtInfo extInfo = Optional.ofNullable(JSON.parseObject(account.getExtInfo(), AccountExtInfo.class)).orElse(new AccountExtInfo());

            String sb = "";
            if (isPublisher(account.getMainType())) {
                sb += "流量主注册\n" +
                        "\n账号ID: " + accountId +
                        "\n邮箱: " + account.getEmail() +
                        "\n公司名称: " + account.getCompanyName() +
                        "\n联系人: " + account.getContact() +
                        "\n手机: " + account.getPhone();
            } else if (isAdvertiser(account.getMainType())) {
                sb += (null != parentId ? "代理商新增客户\n" : "直客广告主注册\n") +
                        "\n账号ID: " + accountId +
                        "\n邮箱: " + account.getEmail() +
                        "\n公司名称: " + account.getCompanyName();
                if (StringUtils.isNotBlank(extInfo.getAddress())) {
                    sb +=  "\n公司地址: " + extInfo.getAddress();
                }
                if (null != parentId) {
                    sb += "\n代理商: " + Optional.ofNullable(accountMapper.selectAccountById(parentId)).map(Account::getCompanyName).orElse("");
                }
                sb += "\n营业执照注册号: " + StringUtils.defaultString(extInfo.getBusinessLicense()) +
                        "\n联系人: " + account.getContact() +
                        "\n手机: " + account.getPhone() +
                        "\naccessKey: " + StringUtils.defaultString(extInfo.getAccessKey()) +
                        "\nsecretKey: " + StringUtils.defaultString(extInfo.getSecretKey());
            } else if (isAgent(account.getMainType())) {
                sb += "代理商注册\n" +
                        "\n账号ID: " + accountId +
                        "\n邮箱: " + account.getEmail() +
                        "\n公司名称: " + account.getCompanyName();
                if (StringUtils.isNotBlank(extInfo.getAddress())) {
                    sb += "\n公司地址: " + extInfo.getAddress();
                }
                sb += "\n营业执照注册号: " + StringUtils.defaultString(extInfo.getBusinessLicense()) +
                        "\n联系人: " + account.getContact() +
                        "\n手机: " + account.getPhone();
            }
            if (StringUtils.isNotBlank(sb)) {
                DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb);
            }
        });
    }

    @Override
    public AccountMangerListVO getCrmAccountList() {
        Account param = new Account();
        param.setMainType(AccountMainType.CRM.getType());
        param.setStatus(NORMAL.getStatus());
        List<Account> accounts = selectAccountList(param);
        Map<String, String> postMap = oaStaffManger.selectIdPostMapByEmails(ListUtils.mapToList(accounts, Account::getEmail));

        AccountMangerListVO managerVO = new AccountMangerListVO();
        managerVO.setBdManagers(new LinkedList<>());
        managerVO.setOperationManagers(new LinkedList<>());
        managerVO.setAllManagers(new ArrayList<>());
        accounts.forEach(account -> {
            String postKey = postMap.getOrDefault(account.getEmail(),"");
            CrmAccountVO accountVO = new CrmAccountVO();
            accountVO.setId(account.getId());
            accountVO.setName(account.getContact());
            if (postKey.contains(GUANG_GAO_YUN_YING)) {
                managerVO.getOperationManagers().addFirst(accountVO);
            } else if (postKey.contains(GUANG_GAO_SHANG_WU)) {
                managerVO.getBdManagers().addLast(accountVO);
            }
            managerVO.getAllManagers().add(accountVO);
        });
        return managerVO;
    }

    @Override
    public Account selectCrmUserByEmail(String email) {
        if(StringUtils.isBlank(email)){
            return null;
        }
        return accountMapper.selectCrmUserByEmail(email);
    }

    @Override
    public List<Long> selectCrmUserIdsByEmail(List<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return Collections.emptyList();
        }
        return accountMapper.selectCrmUserIdsByEmail(emails);
    }

    @Override
    public List<Account> selectPrivateAccountList() {
        Map<Long, List<String>> map = advertiserTagService.getMap();
        List<Long> ids = new ArrayList<>();
        map.forEach((id, values) -> {
            if (values.contains("私域")) {
                ids.add(id);
            }
        });
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        Account account = new Account();
        account.setIds(ids);
        return selectAccountList(account);
    }

    @Override
    public List<Long> selectPrivateAccountIds() {
        Map<Long, List<String>> map = advertiserTagService.getMap();
        List<Long> ids = new ArrayList<>();
        map.forEach((id, values) -> {
            if (values.contains("私域")) {
                ids.add(id);
            }
        });
        return ids;
    }

    @Override
    public List<Account> selectSspPrivateAccountList() {
        return getAccounts(TagManagerTypeEnum.APP_TAG.getType(),"私域");
    }

    @Override
    public List<Long> selectSspPrivateAccountIds() {
        return getAccountIds(TagManagerTypeEnum.APP_TAG.getType(), "私域");
    }

    @Override
    public List<Account> selectSspPlayletAccountList() {
        return getAccounts(TagManagerTypeEnum.APP_TAG.getType(),"短剧");
    }

    private List<Account> getAccounts(Integer firstTagType,String firstTagName) {
        List<Long> parentIds = tagManagerService.selectTagIdsByFirstTagName(firstTagType, firstTagName);
        List<Long> tagIds = tagManagerService.selectIdsByParentIds(parentIds);
        List<AccountTagRelationEntity> entities = accountTagRelationService.selectListByTagIds(tagIds);
        List<Long> accountIds = entities.stream().map(AccountTagRelationEntity::getAccountId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }
        Account account = new Account();
        account.setIds(accountIds);
        return selectAccountList(account);
    }

    private List<Long> getAccountIds(Integer firstTagType, String firstTagName) {
        List<Long> parentIds = tagManagerService.selectTagIdsByFirstTagName(firstTagType, firstTagName);
        List<Long> tagIds = tagManagerService.selectIdsByParentIds(parentIds);
        List<AccountTagRelationEntity> entities = accountTagRelationService.selectListByTagIds(tagIds);
        return entities.stream().map(AccountTagRelationEntity::getAccountId).distinct().collect(Collectors.toList());
    }
}
