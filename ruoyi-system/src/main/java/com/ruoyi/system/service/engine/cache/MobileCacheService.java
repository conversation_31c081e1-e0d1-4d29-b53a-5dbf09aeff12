package com.ruoyi.system.service.engine.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.datashow.MobileHapDataEntity;
import com.ruoyi.system.mapper.common.MobileMapper;
import com.ruoyi.system.mapper.datashow.MobileHapDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 设备缓存服务
 *
 * <AUTHOR>
 * @date 2022/11/10
 */
@Slf4j
@Service
public class MobileCacheService implements ApplicationListener<ContextRefreshedEvent> {

    /**
     * 手机型号布隆过滤器
     */
    private BloomFilter<String> bloomFilter = null;

    @Autowired
    private MobileMapper mobileMapper;

    @Autowired
    private MobileHapDataMapper mobileHapDataMapper;

    /**
     * 手机品牌缓存
     */
    private final LoadingCache<String, String> MOBILE_BRAND_CACHE = CacheBuilder
            .newBuilder()
            .expireAfterAccess(30, TimeUnit.MINUTES)
            .maximumSize(3000)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String model) {
                    return StringUtils.defaultString(mobileMapper.selectBrandByModel(model));
                }

                @Override
                public ListenableFuture<String> reload(String model, String oldValue) {
                    ListenableFutureTask<String> task = ListenableFutureTask.create(() -> load(model));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 手机数据缓存
     */
    private final LoadingCache<String, Optional<MobileHapDataEntity>> MOBILE_DATA_CACHE = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(10, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(1000)
            .build(new CacheLoader<String, Optional<MobileHapDataEntity>>() {
                @Override
                public Optional<MobileHapDataEntity> load(String model) {
                    MobileHapDataEntity param = new MobileHapDataEntity();
                    param.setModel(model);
                    return Optional.ofNullable(mobileHapDataMapper.selectBy(param));
                }

                @Override
                public ListenableFuture<Optional<MobileHapDataEntity>> reload(String model, Optional<MobileHapDataEntity> oldValue) {
                    ListenableFutureTask<Optional<MobileHapDataEntity>> task = ListenableFutureTask.create(() -> load(model));
                    GlobalThreadPool.executorService.submit(task);
                    return task;
                }
            });

    /**
     * 查询手机品牌
     *
     * @param model 手机型号
     * @return 手机品牌
     */
    public String getBrandByModel(String model) {
        if (StringUtils.isNotBlank(model)) {
            try {
                return MOBILE_BRAND_CACHE.get(model);
            } catch (ExecutionException e) {
                log.error("查询手机品牌缓存异常, model={}", model, e);
            }
        }
        return "";
    }

    /**
     * 查询手机数据
     *
     * @param model 手机型号
     * @return 手机数据
     */
    public MobileHapDataEntity getDataByModel(String model) {
        if (StringUtils.isNotBlank(model)) {
            try {
                return MOBILE_DATA_CACHE.get(model).orElse(null);
            } catch (ExecutionException e) {
                log.error("查询手机数据缓存异常, model={}", model, e);
            }
        }
        return null;
    }

    /**
     * 查询手机型号是否在布隆过滤器中存在
     *
     * @param model 手机型号
     * @return 可能存在
     */
    public boolean isMightContain(String model) {
        return null != bloomFilter && StringUtils.isNotBlank(model) && bloomFilter.mightContain(model);
    }

    /**
     * 将手机型号加入布隆过滤器
     *
     * @param model 手机型号
     */
    public void putBoolFilter(String model) {
        if (null != bloomFilter) {
            bloomFilter.put(model);
        }
    }

    /**
     * 初始化设备缓存
     */
    private void initMobileCache() {
        // 初始化布隆过滤器
        bloomFilter = BloomFilter.create(Funnels.stringFunnel(Charset.defaultCharset()), 1000);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        if (contextRefreshedEvent.getApplicationContext().getParent() == null) {
            log.info("MobileCacheService.initCacheCache started.");
            initMobileCache();
            log.info("MobileCacheService.initCacheCache finished.");
        }
    }
}
