package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.system.service.adengine.AdvertFilter;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import org.springframework.stereotype.Component;

import static com.ruoyi.common.enums.common.SwitchStatusEnum.isSwitchOn;

/**
 * 广告状态过滤
 */
@Component
public class ServingSwitchFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        AdvertCacheDto ad = context.getAdvertCacheDto();
        return isSwitchOn(ad.getServingSwitch());
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.SERVING_SWITCH;
    }
}
