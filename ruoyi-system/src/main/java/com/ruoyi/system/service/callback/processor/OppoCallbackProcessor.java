package com.ruoyi.system.service.callback.processor;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.enums.open.OppoConfigEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.callback.ConvCallbackContext;
import com.ruoyi.system.service.callback.ConvCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum.OPPO;
import static com.ruoyi.common.enums.open.OppoConfigEnum.getByOwnId;
import static com.ruoyi.common.enums.open.OppoConfigEnum.getBySlotId;
import static com.ruoyi.common.enums.open.OppoConfigEnum.getOwnerIdBySlotId;

/**
 * OPPO上报处理器
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Slf4j
@Service
public class OppoCallbackProcessor implements CallbackProcessor, InitializingBean {

    private static final String SEND_DATA_URL = "https://sapi.ads.oppomobile.com/v1/clue/sendData";

    @Override
    public CallbackProcessorTypeEnum getType() {
        return OPPO;
    }

    @Override
    public boolean validate(ConvCallbackContext context) {
        JSONObject param = context.getParam().getSlotParam();
        return StringUtils.isNotBlank(param.getString("tid"));
    }

    @Override
    public boolean process(ConvCallbackContext context) {
        try {
            Long slotId = context.getOrder().getSlotId();
            JSONObject param = context.getParam().getSlotParam();
            String ownerId = param.getString("ownerId");
            if (StrUtil.isBlank(ownerId)) {
                ownerId = getOwnerIdBySlotId(slotId);
            }

            JSONObject body = new JSONObject();
            body.put("pageId", param.getString("pageId"));
            body.put("ownerId", ownerId);
            body.put("ip", param.getString("ip"));
            body.put("tid", param.getString("tid"));
            body.put("lbid", param.getString("lbid"));
            body.put("transformType", 101);

            String resp = HttpUtil.createPost(SEND_DATA_URL)
                    .header("Authorization", "Bearer " + getToken(slotId, ownerId))
                    .header("Content-Type", "application/json")
                    .body(body.toString())
                    .execute().body();
            log.info("{}接口上报, req={}, resp={}", getType().getName(), body, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getInteger("code"), 0)) {
                return true;
            }
            log.error("{}接口上报失败, req={}, resp={}", getType().getName(), body, resp);
        } catch (Exception e) {
            log.error("{}接口上报异常, context={}", getType().getName(), JSON.toJSONString(context), e);
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConvCallbackService.register(this);
    }

    /**
     * 获取访问令牌
     *
     * @param slotId 广告位ID
     * @param ownerId 账号广告主ID
     * @return 访问令牌
     */
    private String getToken(Long slotId, String ownerId) {
        OppoConfigEnum config;
        if (StrUtil.isNotBlank(ownerId)) {
            config = getByOwnId(ownerId);
        } else {
            config = getBySlotId(slotId);
        }
        long timestamp = System.currentTimeMillis() / 1000;
        String sign = SecureUtil.sha1(config.getApiId() + config.getApiKey() + timestamp);
        return Base64.encode(config.getOwnerId() + "," + config.getApiId() + "," + timestamp + "," + sign);
    }
}
