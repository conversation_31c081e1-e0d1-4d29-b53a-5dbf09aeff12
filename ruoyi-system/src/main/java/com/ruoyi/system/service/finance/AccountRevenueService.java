package com.ruoyi.system.service.finance;

import com.ruoyi.system.entity.account.finance.AccountRevenueEntity;

import java.util.List;

/**
 * 账户收益表 Service
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:30
 */
public interface AccountRevenueService {

    /**
     * 根据账号id查询账号收益信息
     *
     * @param accountId 账号id
     * @return 收益详情
     */
    AccountRevenueEntity selectByAccountId(Long accountId);

    /**
     * 增加收益
     *
     * @param accountId 账号id
     * @param revenue 收益金额
     * @param withdrawableAmount 可提现金额
     * @return 结果
     */
    boolean addRevenue(Long accountId, Integer revenue, Integer withdrawableAmount);

    /**
     * 增加预付款
     *
     * @param accountId 账号id
     * @param prepayAmount 预付款
     * @return 结果
     */
    boolean addPrepayAmount(Long accountId, Integer prepayAmount);

    /**
     * 扣减可提现金额
     *
     * @param accountId 账号id
     * @param amount 提现金额
     * @return 结果
     */
    boolean deductionWithdrawAmount(Long accountId,Integer amount);

    /**
     * 更新媒体付款类型
     *
     * @param accountId 账号ID
     * @param payType 付款类型
     * @return 结果
     */
    int updatePayType(Long accountId, Integer payType);

    /**
     * 查询预付款媒体账号ID列表
     *
     * @return 预付款媒体收益列表
     */
    List<AccountRevenueEntity> selectPrepayAccountRevenue();

    /**
     * 查询预付款媒体账号ID列表
     *
     * @param accountIds 账号ID列表
     * @return 预付款媒体收益列表
     */
    List<AccountRevenueEntity> selectPrepayAccountRevenue(List<Long> accountIds);
}
