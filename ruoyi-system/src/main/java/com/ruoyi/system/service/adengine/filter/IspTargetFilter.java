package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.common.enums.advert.IspTargetType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.domain.adengine.IpAreaDto;
import com.ruoyi.system.service.adengine.AdvertFilter;
import org.springframework.stereotype.Component;

import static com.ruoyi.common.utils.NetTypeUtil.getNetTypeByUserAgent;
import static com.ruoyi.common.utils.NetTypeUtil.isCellularNetwork;

/**
 * 运营商定向过滤
 */
@Component
public class IspTargetFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        IpAreaDto ipArea = context.getIpArea();
        AdvertCacheDto ad = context.getAdvertCacheDto();

        // 不限运营商
        if (null == ad.getIspTarget() || 0 == ad.getIspTarget() || null == ipArea) {
            return true;
        }
        final String isp = StringUtils.defaultString(ipArea.getIsp());
        final String netType = getNetTypeByUserAgent(context.getUserAgent());

        // 蜂窝网络时才匹配运营商
        if (isCellularNetwork(netType)) {
            switch (isp) {
                case "电信":
                    return IspTargetType.contains(ad.getIspTarget(), IspTargetType.CTCC.getType());
                case "移动":
                    return IspTargetType.contains(ad.getIspTarget(), IspTargetType.CMCC.getType());
                case "联通":
                    return IspTargetType.contains(ad.getIspTarget(), IspTargetType.CUCC.getType());
                default:
                    break;
            }
        }
        // 判断历史是否存在蜂窝网络的运营商
        if (StringUtils.isNotBlank(context.getIsp())) {
            switch (context.getIsp()) {
                case "电信":
                    return IspTargetType.contains(ad.getIspTarget(), IspTargetType.CTCC.getType());
                case "移动":
                    return IspTargetType.contains(ad.getIspTarget(), IspTargetType.CMCC.getType());
                case "联通":
                    return IspTargetType.contains(ad.getIspTarget(), IspTargetType.CUCC.getType());
                default:
                    break;
            }
        }
        return IspTargetType.contains(ad.getIspTarget(), IspTargetType.OTHER.getType());
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.ISP_TARGET;
    }
}
