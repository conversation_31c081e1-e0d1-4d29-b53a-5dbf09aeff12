package com.ruoyi.system.service.blindbox;

import com.ruoyi.system.entity.blindbox.BlindBoxLandpageEntity;

import java.util.List;
import java.util.Map;

/**
 * 盲盒落地页表 Service
 *
 * <AUTHOR>
 * @date 2022-6-13 11:10:01
 */
public interface BlindBoxLandpageService {

    /**
     * 查询落地页标识-落地页链接映射
     *
     * @return 落地页标识-落地页映射
     */
    Map<String, BlindBoxLandpageEntity> selectMap();

    /**
     * 新增记录
     */
    Boolean insert(BlindBoxLandpageEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(BlindBoxLandpageEntity entity);

    /**
     * 根据id获取
     */
    BlindBoxLandpageEntity selectById(Long id);

    /**
     * 查询列表
     */
    List<BlindBoxLandpageEntity> selectList(BlindBoxLandpageEntity param);

    /**
     * 根据landpageKey获取
     */
    BlindBoxLandpageEntity selectByLandpageKey(String landpageKey);

    /**
     * 根据广告主落地页查询落地页标识
     */
    List<String> selectByTargetLandpage(String targetLandpage);
}
