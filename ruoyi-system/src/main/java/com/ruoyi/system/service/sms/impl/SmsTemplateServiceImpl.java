package com.ruoyi.system.service.sms.impl;

import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.sms.SmsTemplateService;
import com.ruoyi.system.entity.sms.SmsTemplateEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.sms.SmsTemplateMapper;

/**
 * 短信模版表 Service
 *
 * <AUTHOR>
 * @date 2022-12-5 10:09:01
 */
@Service
public class SmsTemplateServiceImpl implements SmsTemplateService {
    @Autowired
    private SmsTemplateMapper smsTemplateMapper;

    @Override
    public Boolean insert(SmsTemplateEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return smsTemplateMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return smsTemplateMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(SmsTemplateEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return smsTemplateMapper.updateById(entity) > 0;
    }

    @Override
    public SmsTemplateEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return smsTemplateMapper.selectById(id);
    }

    @Override
    public List<SmsTemplateEntity> selectAllList() {
        return smsTemplateMapper.selectAllList();
    }

    @Override
    public int batchInsertOrUpdate(List<SmsTemplateEntity> entities) {
        return smsTemplateMapper.batchInsertOrUpdate(entities);
    }


    @Override
    public SmsTemplateEntity selectByTypeAndTpId(Integer type, Long tpId) {
        if(NumberUtils.isNullOrLteZero(type) || NumberUtils.isNullOrLteZero(tpId)){
            return null;
        }
        return smsTemplateMapper.selectByTypeAndTpId(type, tpId);
    }

    @Override
    public List<SmsTemplateEntity> selectListByContent(String content) {
        if(StringUtils.isBlank(content)){
            return Collections.emptyList();
        }
        return smsTemplateMapper.selectListByContent(content);
    }
}
