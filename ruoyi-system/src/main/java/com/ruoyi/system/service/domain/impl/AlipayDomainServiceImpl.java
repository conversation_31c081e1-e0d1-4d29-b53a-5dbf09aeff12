package com.ruoyi.system.service.domain.impl;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.domain.AlipayDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 支付宝域名接口实现
 *
 * <AUTHOR>
 * @date 2022-03-18
 */
@Service
public class AlipayDomainServiceImpl implements AlipayDomainService {

    @Autowired
    private RedisCache redisCache;

    @Override
    public boolean isAlipayEnvironment(String userAgent) {
        if (StringUtils.isBlank(userAgent)) {
            return false;
        }

        String ua = userAgent.toLowerCase();
        return ua.contains("aliapp") || ua.contains("alipay");
    }

    @Override
    public void updateDomainRecentlyRequestTime(String domain) {
        if (StringUtils.isBlank(domain)) {
            return;
        }
        String key = EngineRedisKeyFactory.K017.join(domain);
        redisCache.setCacheObject(key, System.currentTimeMillis(), 1, TimeUnit.DAYS);
    }
}
