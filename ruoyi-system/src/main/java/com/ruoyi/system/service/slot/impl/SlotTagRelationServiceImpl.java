package com.ruoyi.system.service.slot.impl;

import com.ruoyi.common.enums.TagManagerTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.slot.SlotTagCountBo;
import com.ruoyi.system.entity.slot.SlotTagRelationEntity;
import com.ruoyi.system.mapper.slot.SlotTagRelationMapper;
import com.ruoyi.system.service.slot.SlotTagRelationService;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 广告位标签关联表 Service
 *
 * <AUTHOR>
 * @date 2023-5-9 17:00:13
 */
@Service
public class SlotTagRelationServiceImpl implements SlotTagRelationService {

    @Autowired
    private SlotTagRelationMapper slotTagRelationMapper;

    @Autowired
    private TagManagerService tagManagerService;

    @Override
    public Map<Long, Integer> selectTagCountMapBySlotIds(List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }
        List<SlotTagCountBo> list = slotTagRelationMapper.selectTagCountListBySlotIds(slotIds);
        return list.stream().collect(Collectors.toMap(SlotTagCountBo::getSlotId, SlotTagCountBo::getCount, (v1, v2) -> v2));
    }

    @Override
    public List<Long> selectTagIdsBySlotId(Long slotId) {
        if (null == slotId) {
            return Collections.emptyList();
        }
        return slotTagRelationMapper.selectTagIdsBySlotId(slotId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTags(Long slotId, List<Long> tagIds) {
        if (null == slotId) {
            return false;
        }
        // 删除标签
        slotTagRelationMapper.deleteBySlotId(slotId);

        // 批量插入
        if (CollectionUtils.isNotEmpty(tagIds)) {
            slotTagRelationMapper.batchInsert(tagIds.stream().map(tagId -> {
                SlotTagRelationEntity entity = new SlotTagRelationEntity();
                entity.setSlotId(slotId);
                entity.setTagId(tagId);
                return entity;
            }).collect(Collectors.toList()));
        }
        return true;
    }

    @Override
    public List<Long> selectSlotIdsByTagName(String tagName) {
        if (StringUtils.isEmpty(tagName)) {
            return Collections.emptyList();
        }
        List<Long> tagIds = tagManagerService.selectTagIdsByFirstTagName(TagManagerTypeEnum.SLOT_TAG.getType(), tagName);
        if (CollectionUtils.isEmpty(tagIds)) {
            return Collections.emptyList();
        }
        return slotTagRelationMapper.selectSlotIdsByTagId(tagIds.get(0));
    }
}
