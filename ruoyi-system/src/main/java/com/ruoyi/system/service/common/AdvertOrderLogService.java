package com.ruoyi.system.service.common;

import com.ruoyi.system.bo.data.OrderDataBo;
import com.ruoyi.system.entity.common.AdvertOrderLogEntity;

/**
 * 业务订单明细表 Service
 *
 * <AUTHOR>
 * @date 2023-10-16
 */
public interface AdvertOrderLogService {

    /**
     * 新增记录
     */
    Boolean insert(AdvertOrderLogEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(AdvertOrderLogEntity entity);

    /**
     * 根据id获取
     */
    AdvertOrderLogEntity selectById(Long id);

    /**
     * 根据订单号获取ID
     */
    Long selectIdByOrderId(String orderId);

    /**
     * 查询广告位广告的CTR和CVR(活动使用)
     *
     * @param slotId 广告位ID
     * @param advertId 广告ID
     * @param launchSeq 发券次序
     * @param mobileBrand 手机品牌
     * @param province 省份
     * @param times 最近发券次数
     * @return CVR
     */
    OrderDataBo selectCtrCvrBySlotIdAndAdvertId(Long slotId, Long advertId, Integer launchSeq, String mobileBrand, String province, Integer times);

    /**
     * 查询广告位广告的CTR和CVR(活动使用)
     *
     * @param slotId 广告位ID
     * @param advertId 广告ID
     * @param launchSeq 发券次序
     * @param mobileBrand 手机品牌
     * @param province 省份
     * @param times 最近发券次数
     * @param minId 最小ID
     * @return CVR
     */
    OrderDataBo selectCtrCvrBySlotIdAndAdvertId(Long slotId, Long advertId, Integer launchSeq, String mobileBrand, String province, Integer times, Long minId);

    /**
     * 查询广告位广告的CVR(直投使用)
     *
     * @param slotId 广告位ID
     * @param advertId 广告ID
     * @param times 最近发券次数
     * @return CVR
     */
    OrderDataBo selectCvrBySlotIdAndAdvertId(Long slotId, Long advertId, Integer times);

    /**
     * 查询广告位广告的CVR(直投使用)
     *
     * @param slotId 广告位ID
     * @param advertId 广告ID
     * @param times 最近发券次数
     * @param minId 最小ID
     * @return CVR
     */
    OrderDataBo selectCvrBySlotIdAndAdvertId(Long slotId, Long advertId, Integer times, Long minId);

    /**
     * 查询广告的CVR(OCPC使用)
     *
     * @param advertId 广告ID
     * @param times 最近发券次数
     * @return CVR
     */
    OrderDataBo selectCvrByAdvertId(Long advertId, Integer times);
}
