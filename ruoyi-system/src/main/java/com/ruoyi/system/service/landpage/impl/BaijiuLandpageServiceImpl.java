package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.InnerLogUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.adengine.SlotCacheDto;
import com.ruoyi.system.domain.manager.Area;
import com.ruoyi.system.domain.order.AdSnapshot;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.mapper.landpage.BaijiuLandpageFormRecordMapper;
import com.ruoyi.system.message.rocketmq.producer.LogMqProducer;
import com.ruoyi.system.req.engine.BaijiuLandPageFormReq;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.area.AreaService;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.engine.CallbackService;
import com.ruoyi.system.service.engine.SlotUpService;
import com.ruoyi.system.service.landpage.BaijiuLandpageService;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import com.ruoyi.system.service.manager.SlotConfigService;
import com.ruoyi.system.service.open.JizhunService;
import com.ruoyi.system.service.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

import java.util.Objects;

import static com.ruoyi.common.enums.InnerLogType.LANDPAGE_CLICK;
import static com.ruoyi.common.enums.common.SwitchStatusEnum.OFF;
import static com.ruoyi.common.enums.common.SwitchStatusEnum.ON;

/**
 * 白酒落地页服务实现
 *
 * <AUTHOR>
 * @date 2022/01/24
 */
@Slf4j
@Service
public class BaijiuLandpageServiceImpl implements BaijiuLandpageService {

    @Autowired
    private AreaService areaService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private BaijiuLandpageFormRecordMapper baijiuLandpageFormRecordMapper;

    @Autowired
    private DataStatService dataStatService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SlotConfigService slotConfigService;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private LogMqProducer logMqProducer;

    @Autowired
    private JizhunService jizhunService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private ConvCallbackService convCallbackService;

    @Autowired
    private SlotUpService slotUpService;

    @Override
    public Long formSubmit(BaijiuLandPageFormReq req) {
        // 查询行政区划数据
        Area area = queryArea(req.getAreaNum());
        if (null == area) {
            throw new CustomException("无效的行政区划代码");
        }

        // 查询订单
        Order order = orderService.selectByOrderId(req.getOrderId());
        if (null == order) {
            throw new CustomException("链接已失效");
        }
        AdSnapshot adSnapshot = JSON.parseObject(StringUtils.defaultString(order.getAdSnapshot(), "{}"), AdSnapshot.class);

        // 添加转化记录
        HttpServletRequest request = ServletUtils.getRequest();
        String ip = IpUtils.getIpAddr(request);
        String referer = null != request ? request.getHeader("referer") : "";
        String userAgent = null != request ? request.getHeader("User-Agent") : "";
        // 去重
        if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K021.join(req.getName(), req.getPhone(), req.getAddress(), req.getAreaNum()), 300)) {
            log.info("白酒落地页表单提交重复(分布式锁限制), advertId={}, orderId={}, name={}, phone={}, address={}, areaNum={}",
                    order.getAdvertId(), order.getOrderId(), req.getName(), req.getPhone(), req.getAddress(), req.getAreaNum());
            throw new CustomException("表单已提交，请勿重复提交~");
        }
        // 特殊广告主特殊处理
        if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K021.join(req.getPhone()), 86400)) {
            Advert advert = advertService.selectAdvertById(order.getAdvertId());
            if (null != advert && whitelistService.contains(WhitelistType.BAIJIU_PHONE_ADVERTISER, advert.getAdvertiserId())) {
                throw new CustomException("表单已提交，请勿重复提交~");
            }
        }

        // 落地页转化记录
        BaijiuLandpageFormRecord record = new BaijiuLandpageFormRecord();
        record.setAreaNum(req.getAreaNum());
        record.setProvince(area.getProvince());
        record.setCity(area.getCity());
        record.setDistrict(area.getDistrict());
        record.setAddress(req.getAddress());
        record.setPhone(req.getPhone());
        record.setName(req.getName());
        record.setAdvertId(order.getAdvertId());
        record.setOrderId(order.getOrderId());
        record.setConsumerId(order.getConsumerId());
        record.setAppId(order.getAppId());
        record.setSlotId(order.getSlotId());
        record.setIp(StrUtil.subPre(ip, 32));
        record.setReferer(StrUtil.subPre(referer, 255));
        if (null != adSnapshot) {
            record.setLandpageUrl(adSnapshot.getLandpageUrl());
        }
        baijiuLandpageFormRecordMapper.insertLandpageFormRecord(record);


        // 打印inner日志
        JSONObject logJson = new JSONObject();
        logJson.put("activityId", order.getActivityId());
        logJson.put("slotId", order.getSlotId());
        logJson.put("appId", order.getAppId());
        logJson.put("consumerId", order.getConsumerId());
        logJson.put("orientId", order.getOrientId());
        logJson.put("advertId", order.getAdvertId());
        logJson.put("materialId", order.getMaterialId());
        if (null != adSnapshot) {
            logJson.put("chargeType", adSnapshot.getChargeType());
            logJson.put("landpageUrl", adSnapshot.getLandpageUrl());
            logJson.put("originLandpageUrl", adSnapshot.getOriginLandpageUrl());
            logJson.put("srid", adSnapshot.getSrid());
            logJson.put("pluginId", adSnapshot.getPluginId());
        }
        logJson.put("ip", ip);
        logJson.put("userAgent", userAgent);
        logJson.put("referer", referer);
        logJson.put("orderId", order.getOrderId());
        logJson.put("phone", req.getPhone());
        logJson.put("name", req.getName());
        logJson.put("areaNum", req.getAreaNum());
        logJson.put("address", req.getAddress());
        InnerLogUtils.log(LANDPAGE_CLICK, logJson);
        logMqProducer.sendMsg(LANDPAGE_CLICK, logJson);

        // 落地页数据
        dataStatService.handleAsync(LANDPAGE_CLICK, logJson);

        // [固定收益上报]缓存转化个数和理论消耗
        JSONObject param = callbackService.getParameterFromCache(req.getOrderId());
        slotUpService.slotUpAddCostMulti(order, param.getString("hu"));

        // 上报媒体
        if (whitelistService.contains(WhitelistType.BAIJIU_JIZHUN, order.getAdvertId())) {
            String orderId = req.getOrderId();
            GlobalThreadPool.executorService.submit(() -> {
                JSONObject slotParam = callbackService.getParameterFromCache(orderId);
                if (!slotParam.containsKey("jcid")) {
                    return;
                }
                String resp = jizhunService.baijiuFormReport(orderId, slotParam);
                log.info("白酒上报极准, orderId={}, jcid={}, resp={}", orderId, slotParam.getString("jcid"), resp);
            });
        }
        convCallbackService.handleAsync(LANDPAGE_CLICK, null, order);

        return record.getId();
    }

    @Override
    public Integer getRetSwitch(String orderId) {
        // 查询订单
        Order order = orderService.selectByOrderId(orderId);
        if (null == order) {
            return OFF.getStatus();
        }

        // 查询广告位配置的落地页返回挽留开关（默认是开）
        SlotCacheDto slot = slotCacheService.getSlotCache(order.getSlotId());
        if (null != slot && null != slot.getSwitchConfig() && SwitchStatusEnum.isSwitchOff(slot.getSwitchConfig().getLpRet())) {
            return OFF.getStatus();
        }

        // 设置用户触发返回挽留
        String redisKey = EngineRedisKeyFactory.K020.join(DateUtil.today());
        boolean isExposed = redisCache.cacheSetContains(redisKey, String.valueOf(order.getConsumerId()));
        return isExposed ? OFF.getStatus() : ON.getStatus();
    }

    @Override
    public void setRetSwitch(Long slotId, Integer status) {
        SlotConfig config = slotConfigService.selectBySlotId(slotId);
        if (null == config) {
            return;
        }
        SlotSwitchConfig switchConfig = JSON.parseObject(config.getSwitchConfig(), SlotSwitchConfig.class);
        if (null == switchConfig) {
            switchConfig = new SlotSwitchConfig();
        }
        switchConfig.setLpRet(status);

        SlotConfig updateConfig = new SlotConfig();
        updateConfig.setSlotId(slotId);
        updateConfig.setSwitchConfig(JSON.toJSONString(switchConfig));
        slotConfigService.updateSlotConfig(updateConfig);

        // 刷新缓存
        refreshCacheService.sendRefreshSlotCacheMsg(slotId);
    }

    /**
     * 查询行政区划数据
     *
     * @param areaNum 行政区划代码
     * @return 地域信息
     */
    private Area queryArea(String areaNum) {
        if (!StringUtils.isNumeric(areaNum)) {
            return null;
        }
        return areaService.queryAreaByAreaNum(areaNum);
    }
}
