package com.ruoyi.system.service.landpage.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.LandpageSkinService;
import com.ruoyi.system.entity.landpage.LandpageSkinEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.landpage.LandpageSkinMapper;

/**
 * 落地页皮肤表 Service
 *
 * <AUTHOR>
 * @date 2023-4-3 16:56:23
 */
@Service
public class LandpageSkinServiceImpl implements LandpageSkinService {

    @Autowired
    private LandpageSkinMapper landpageSkinMapper;

    @Override
    public int insert(LandpageSkinEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return landpageSkinMapper.insert(entity);
    }

    @Override
    public int updateById(LandpageSkinEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return landpageSkinMapper.updateById(entity);
    }

    @Override
    public LandpageSkinEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return landpageSkinMapper.selectById(id);
    }

    @Override
    public List<LandpageSkinEntity> selectList(LandpageSkinEntity param) {
        return landpageSkinMapper.selectList(param);
    }
}
