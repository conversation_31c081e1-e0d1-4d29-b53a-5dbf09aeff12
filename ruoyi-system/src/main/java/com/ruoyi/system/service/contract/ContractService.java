package com.ruoyi.system.service.contract;

import com.ruoyi.system.entity.contract.ContractEntity;
import org.checkerframework.checker.units.qual.C;

import java.util.List;

/**
 * 合同表 Service
 *
 * <AUTHOR>
 * @date 2022-11-3 17:14:28
 */
public interface ContractService {
    /**
     * 新增记录
     */
    Boolean insert(ContractEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(ContractEntity entity);

    /**
     * 根据id获取
     */
    ContractEntity selectById(Long id);

    /**
     * 根据乙方账户id获取合同列表
     * @param accountId
     * @return
     */
    List<ContractEntity> selectListByAccountId(Long accountId);

    /**
     * 根据账号id列表查询最新的合同
     * @param accountIds
     * @return
     */
    List<ContractEntity> selectLatestContractByAccountIds(List<Long> accountIds);

    /**
     * 根据最新的发票状态查询账号id列表
     * @param status
     * @return
     */
    List<Long> selectAccountIdsByLatestContractStatus(Integer status);

    /**
     * 根据合同编号查询合同
     * @return
     */
    ContractEntity selectByContractCode(String contractCode);

}
