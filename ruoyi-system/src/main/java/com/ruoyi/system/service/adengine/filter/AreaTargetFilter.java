package com.ruoyi.system.service.adengine.filter;

import com.ruoyi.system.service.adengine.AdvertFilter;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.domain.adengine.AdvertFilterContext;
import com.ruoyi.system.domain.adengine.IpAreaDto;
import com.ruoyi.common.enums.advert.AdvertFilterType;
import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import static com.ruoyi.common.constant.BizConstants.CHINA_AREA_NUM;

/**
 * 地域定向过滤
 */
@Component
public class AreaTargetFilter implements AdvertFilter {

    @Override
    public boolean filter(AdvertFilterContext context) {
        IpAreaDto ipArea = context.getIpArea();
        AdvertCacheDto ad = context.getAdvertCacheDto();
        if (null == ipArea || CollectionUtils.isEmpty(ad.getAreaTargetSet())) {
            return true;
        }
        // 000000当做地域定向全选处理
        return ad.getAreaTargetSet().contains(CHINA_AREA_NUM)
                || ad.getAreaTargetSet().contains(StringUtils.defaultString(ipArea.getProvinceAreaNum()))
                || ad.getAreaTargetSet().contains(StringUtils.defaultString(ipArea.getCityAreaNum()));
    }

    @Override
    public AdvertFilterType getType() {
        return AdvertFilterType.AREA_TARGET;
    }
}
