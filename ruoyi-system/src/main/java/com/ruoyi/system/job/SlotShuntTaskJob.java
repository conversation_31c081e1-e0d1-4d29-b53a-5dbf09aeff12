package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.system.entity.slot.SlotShuntData;
import com.ruoyi.system.entity.slot.SlotShuntTask;
import com.ruoyi.system.service.slot.SlotShuntDataService;
import com.ruoyi.system.service.slot.SlotShuntTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import static com.ruoyi.common.enums.slot.SlotShuntStatusEnum.canExecute;
import static com.ruoyi.common.enums.slot.SlotShuntStatusEnum.canFinish;

/**
 * 广告位切量计划定时任务
 *
 * <AUTHOR>
 * @date 2022/04/24
 */
@Slf4j
@Component
public class SlotShuntTaskJob {

    @Autowired
    private SlotShuntTaskService slotShuntTaskService;

    @Autowired
    private SlotShuntDataService slotShuntDataService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Scheduled(cron = "0 * * * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("SlotShuntTaskJob"), 10);
        if (lock == null) {
            return;
        }

        // 查询任务列表
        List<SlotShuntTask> tasks = slotShuntTaskService.selectTotalValidList();
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }

        Date now = new Date();
        for (SlotShuntTask task : tasks) {
            try {
                // 开始执行计划
                if (canExecute(task.getTaskStatus()) && !now.before(task.getStartTime())) {
                    int result = slotShuntTaskService.executeTask(task.getId());
                    if (result > 0) {
                        SlotShuntData shuntData = new SlotShuntData();
                        shuntData.setCurDate(DateUtil.beginOfDay(now));
                        shuntData.setSlotId(task.getSlotId());
                        shuntData.setTaskId(task.getId());
                        slotShuntDataService.insert(shuntData);
                    }
                }
                // 判断计划是否完成
                else if (canFinish(task.getTaskStatus())) {
                    // 达到计划结束时间
                    if (!now.before(task.getEndTime())) {
                        slotShuntTaskService.finishTask(task.getId());
                        continue;
                    }
                    // 达到切量上限
                    Long count = redisAtomicClient.getLong(EngineRedisKeyFactory.K031.join(task.getId()));
                    if (null != count && count >= task.getThreshold()) {
                        slotShuntTaskService.finishTask(task.getId());
                    }
                }
            } catch (Exception e) {
                log.error("广告位切量计划定时任务执行异常, task={}", JSON.toJSONString(task), e);
            }
        }
    }
}
