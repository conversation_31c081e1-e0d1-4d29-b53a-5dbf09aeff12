package com.ruoyi.system.job;

import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.system.manager.liuzi.AsyncAlipayLandpageFormDataManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 支付宝推广页留资表单同步定时任务 同步昨一天的数据，防止昨天的定时任务没有拉全数据
 *
 * <AUTHOR>
 * @date 2022/12/5 17:02
 */
@Slf4j
@Component
public class AlipayLandpageFormSyncYesterdayJob {


    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AsyncAlipayLandpageFormDataManager asyncAlipayLandpageFormDataManager;

    @Scheduled(cron = "0 1 0 * * ?")
    public void execute() {
        if (!SpringEnvironmentUtils.isProd()) {
            return;
        }
        try (RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K027.toString(), 60)) {
            if (lock == null) {
                return;
            }
            log.info("支付宝推广页昨日留资数据同步定时任务开始");
            asyncAlipayLandpageFormDataManager.asyncAlipayData(DateUtils.addDays(new Date(),-1));

        } catch (Exception e) {
            log.error("支付宝推广页昨日留资数据同步定时任务异常,e:", e);
        }
        log.info("支付宝推广页昨日留资数据同步定时任务结束");

    }


}
