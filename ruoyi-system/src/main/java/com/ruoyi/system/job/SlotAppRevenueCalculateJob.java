package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.entity.slotcharge.SlotChargeEntity;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.manager.SlotConfigService;
import com.ruoyi.system.service.slotcharge.SlotChargeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 广告位每日媒体应得收益统计定时任务
 *
 * <AUTHOR>
 * @date 2022/2/17 3:02 下午
 */
@Slf4j
@Component
public class SlotAppRevenueCalculateJob {

    @Autowired
    private SlotChargeService slotChargeService;

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private SlotConfigService slotConfigService;

    @Scheduled(cron = "0 20 0 * * ?")
    public void execute(){
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("SlotAppRevenueCalculateJob"), 10);
        if (lock == null) {
            return;
        }
        log.info("广告位每日媒体应得收益统计定时任务开始");

        // 查询开启了自动结算的广告位
        List<Long> slotIds = selectAutoChargeSlot();
        if (CollectionUtils.isEmpty(slotIds)) {
            log.info("广告位每日媒体应得收益统计定时任务结束");
            return;
        }

        // 查询昨日广告位数据
        Date yesterday = DateUtil.beginOfDay(DateUtils.addDays(new Date(),-1));
        SlotData param = new SlotData();
        param.setCurDate(yesterday);
        param.setSlotIds(slotIds);
        List<SlotData> dataList = slotDataService.selectSlotDataList(param);

        // 查询所有广告位昨日计费方式和价格
        List<SlotChargeEntity> chargeList = slotChargeService.selectListBySlotIdsAndDateList(ListUtils.mapToList(dataList, SlotData::getSlotId), Lists.newArrayList(yesterday));
        Map<Long, SlotChargeEntity> chargeMap = chargeList.stream().collect(Collectors.toMap(SlotChargeEntity::getSlotId, Function.identity()));

        // 计算媒体应得收入并更新
        dataList.forEach(data -> {
            SlotChargeEntity slotCharge = chargeMap.get(data.getSlotId());
            long appRevenue = slotChargeService.calculateAppRevenue(slotCharge, data);
            data.setAppRevenue(appRevenue);
            log.info("广告位每日媒体应得收益统计, slotId={}, appRevenue={}", data.getSlotId(), appRevenue);
        });
        int result = slotDataService.batchUpdateSlotAppRevenue(dataList);

        log.info("广告位每日媒体应得收益统计定时任务结束, slotIds={}, 开启的广告位数量={}, 有数据的广告位数量={}, 成功更新数量={}",
                JSON.toJSONString(slotIds), slotIds.size(), dataList.size(), result);
    }

    /**
     * 查询开启自动结算的广告位ID列表
     */
    private List<Long> selectAutoChargeSlot() {
        SlotConfig param = new SlotConfig();
        param.setSwitchConfig("autoCharge");
        List<SlotConfig> slotConfigs = slotConfigService.selectSlotConfigList(param);
        return slotConfigs.stream().filter(config -> {
            SlotSwitchConfig switchConfig = JSON.parseObject(config.getSwitchConfig(), SlotSwitchConfig.class);
            return null != switchConfig && SwitchStatusEnum.isSwitchOn(switchConfig.getAutoCharge());
        }).map(SlotConfig::getSlotId).collect(Collectors.toList());
    }
}
