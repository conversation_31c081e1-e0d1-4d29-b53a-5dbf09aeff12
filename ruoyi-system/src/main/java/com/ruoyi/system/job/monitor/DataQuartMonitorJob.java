package com.ruoyi.system.job.monitor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.domain.DomainType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.slot.AreaTargetRedirectItem;
import com.ruoyi.system.domain.slot.ShuntRedirectItem;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.datashow.AdvertSlotDayData;
import com.ruoyi.system.entity.datashow.SlotActivityData;
import com.ruoyi.system.entity.datashow.SlotActivityHourData;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.mapper.datashow.AdvertSlotDayDataMapper;
import com.ruoyi.system.mapper.datashow.SlotActivityDataMapper;
import com.ruoyi.system.mapper.datashow.SlotActivityHourDataMapper;
import com.ruoyi.system.mapper.datashow.SlotDataMapper;
import com.ruoyi.system.mapper.manager.AppMapper;
import com.ruoyi.system.mapper.manager.SlotConfigMapper;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.service.domain.DomainReplaceService;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.service.domain.WxDomainService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.slot.SlotRedirectType.isAreaTargetRedirect;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToActivity;

/**
 * 数据监控定时任务
 *
 * <AUTHOR>
 * @date 2022/01/19
 */
@Slf4j
@Component
public class DataQuartMonitorJob {

    private static final Integer SLOT_WARNING_INTERVAL = 3;

    @Autowired
    private SlotActivityHourDataMapper slotActivityHourDataMapper;

    @Autowired
    private SlotActivityDataMapper slotActivityDataMapper;

    @Autowired
    private AdvertSlotDayDataMapper advertSlotDayDataMapper;

    @Autowired
    private SlotDataMapper slotDataMapper;

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private SlotConfigMapper slotConfigMapper;

    @Autowired
    private AppMapper appMapper;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DomainReplaceService domainReplaceService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private DomainService domainService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private WxDomainService wxDomainService;

    @Value("${ding.webhook.dataAlert}")
    private String webhook;


    @Scheduled(cron = "0 0/15 * * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("DataQuartMonitorJob"), 10);
        if (lock == null) {
            return;
        }

        // 时间计算
        Date today = DateUtil.beginOfDay(new Date());
        Date yesterday = DateUtil.offsetDay(today, -1);
        int hour = DateUtil.thisHour(true);
        int minute = DateUtil.thisMinute();

        if (hour < 1 && minute < 30) {
            return;
        }

        int lastHour = minute < 15 ? hour - 1 : hour;
        int lastMinute = minute < 15 ? minute + 45 : minute - 15;

        int lastLastHour = lastMinute < 15 ? lastHour - 1 : lastHour;
        int lastLastMinute = lastMinute < 15 ? lastMinute + 45 : lastMinute - 15;

        // 数据收集

        // 广告位访问pv
        Map<String, Integer> slotRequestPvMap = querySlotRequestPvMap(today);
        // 广告位访问uv
        Map<String, Integer> slotRequestUvMap = querySlotRequestUvMap(today);
        // 活动访问pv
        Map<String, Integer> activityRequestPvMap = queryActivityRequestPvMap(today);
        // 活动访问Uv
        Map<String, Integer> activityRequestUvMap = queryActivityRequestUvMap(today);
        // 广告点击pv
        Map<String, Integer> advertClickPvMap = queryAdvertClickPvMap(today);
        // 落地页曝光uv
        Map<String, Integer> lpExposurePvMap = queryLpExposurePvMap(today);


        // 缓存数据
        saveMapCache(CrmRedisKeyFactory.K005, today, hour, minute, slotRequestPvMap);
        saveMapCache(CrmRedisKeyFactory.K006, today, hour, minute, slotRequestUvMap);
        saveMapCache(CrmRedisKeyFactory.K007, today, hour, minute, activityRequestPvMap);
        saveMapCache(CrmRedisKeyFactory.K019, today, hour, minute, activityRequestUvMap);
        saveMapCache(CrmRedisKeyFactory.K008, today, hour, minute, advertClickPvMap);
        saveMapCache(CrmRedisKeyFactory.K009, today, hour, minute, lpExposurePvMap);

        // 监控执行
        if (hour < 1) {
            return;
        }

        Map<String, Integer> slotRequestUvHourMap = querySlotRequestUvHourMap(today, hour);
        Map<String, Integer> ydaySlotRequestUvHourMap = querySlotRequestUvHourMap(yesterday, hour);

        Map<String, Integer> lastSlotRequestPvMap = querySlotRequestPvMapCache(today, lastHour, lastMinute);
        Map<String, Integer> lastLastSlotRequestPvMap = querySlotRequestPvMapCache(today, lastLastHour, lastLastMinute);

        Map<String, Integer> lastSlotRequestUvMap = querySlotRequestUvMapCache(today, lastHour, lastMinute);
        Map<String, Integer> lastLastSlotRequestUvMap = querySlotRequestUvMapCache(today, lastLastHour, lastLastMinute);

        Map<String, Integer> lastActivityRequestPvMap = queryActivityRequestPvMapCache(today, lastHour, lastMinute);
        Map<String, Integer> lastLastActivityRequestPvMap = queryActivityRequestPvMapCache(today, lastLastHour, lastLastMinute);

        Map<String, Integer> lastActivityRequestUvMap = queryActivityRequestUvMapCache(today, lastHour, lastMinute);
        Map<String, Integer> lastLastActivityRequestUvMap = queryActivityRequestUvMapCache(today, lastLastHour, lastLastMinute);

        Map<String, Integer> lastAdvertClickPvMap = queryAdvertClickPvMapCache(today, lastHour, lastMinute);
        Map<String, Integer> lastLastAdvertClickPvMap = queryAdvertClickPvMapCache(today, lastLastHour, lastLastMinute);

        Map<String, Integer> lastLpExposurePvMap = queryLpExposurePvMapCache(today, lastHour, lastMinute);
        Map<String, Integer> lastLastLpExposurePvMap = queryLpExposurePvMapCache(today, lastLastHour, lastLastMinute);

        // 广告位访问uv监控
        lastLastSlotRequestUvMap.forEach((slotId, lastLastUv) -> {
            if (isSlotWarned(slotId)) {
                return;
            }

            Integer lastUv = lastSlotRequestUvMap.getOrDefault(slotId, 0);
            Integer uv = slotRequestUvMap.getOrDefault(slotId, 0);

            if (uv <= 100) {
                return;
            }
            Integer diffUv = uv - lastUv;
            Integer lastDiffUv = lastUv - lastLastUv;

            if (lastDiffUv < 100) {
                return;
            }

            if ((lastDiffUv - diffUv) / Double.valueOf(lastDiffUv) > 0.5) {
                Integer todayUv = slotRequestUvHourMap.getOrDefault(slotId, 0);
                Integer ydayUv = ydaySlotRequestUvHourMap.getOrDefault(slotId, 0);

                if (ydayUv != 0 && (ydayUv - todayUv) / Double.valueOf(ydayUv) >= 0.5) {
                    // 告警
                    Slot slot = slotMapper.selectSlotById(Long.valueOf(slotId));
                    App app = appMapper.selectAppById(slot.getAppId());

                    StringBuilder sb = new StringBuilder();
                    sb.append("广告位：").append(slotId).append("\n")
                            .append("所属媒体：").append(app.getAppName()).append("\n")
                            .append("广告位链接：").append(querySlotUrl(Long.valueOf(slotId), slot.getSlotUrl())).append("\n")
                            .append("\n数据\n")
                            .append(String.format("%02d:%02d-%02d:%02d", lastLastHour, lastLastMinute, lastHour, lastMinute)).append(" 广告位访问uv：").append(lastDiffUv).append("\n")
                            .append(String.format("%02d:%02d-%02d:%02d", lastHour, lastMinute, hour, minute)).append(" 广告位访问uv：").append(diffUv).append("\n");
                    DingRobotUtil.sendText(webhook, sb.toString(), null, true);

                    // 设置告警沉默
                    setSlotWarned(slotId);
                }
            }
        });

        // 活动曝光成功率监控
        lastLastSlotRequestPvMap.forEach((slotId, lastLastSlotRequestPv) -> {
//            if (isSlotWarned(slotId)) {
//                return;
//            }

            Integer lastSlotRequestPv = lastSlotRequestPvMap.getOrDefault(slotId, 0);
            Integer slotRequestPv = slotRequestPvMap.getOrDefault(slotId, 0);

            if (slotRequestPv <= 100) {
                return;
            }

            if (slotRequestPv - lastSlotRequestPv == 0 || lastSlotRequestPv - lastLastSlotRequestPv < 100) {
                return;
            }

            Integer lastLastActivityRequestPv = lastLastActivityRequestPvMap.getOrDefault(slotId, 0);
            Integer lastActivityRequestPv = lastActivityRequestPvMap.getOrDefault(slotId, 0);
            Integer activityRequestPv = activityRequestPvMap.getOrDefault(slotId, 0);

            double expRate = (activityRequestPv - lastActivityRequestPv) / (double) (slotRequestPv - lastSlotRequestPv);
            double lastExpRate = (lastActivityRequestPv - lastLastActivityRequestPv) / (double) (lastSlotRequestPv - lastLastSlotRequestPv);

            if (lastExpRate - expRate > 0.5 && expRate < 1) {
                // 告警
                Slot slot = slotMapper.selectSlotById(Long.valueOf(slotId));
                App app = appMapper.selectAppById(slot.getAppId());
                SlotConfig slotConfig = slotConfigMapper.selectBySlotId(Long.valueOf(slotId));
                if (null != slotConfig && !isRedirectActivity(slotConfig)) {
                    return;
                }

                StringBuilder sb = new StringBuilder();
                sb.append("广告位：").append(slotId).append("\n")
                        .append("所属媒体：").append(app.getAppName()).append("\n")
                        .append("广告位链接：").append(querySlotUrl(Long.valueOf(slotId), slot.getSlotUrl())).append("\n");
                if (null != slotConfig) {
                    sb.append("活动：").append(getRedirectActivity(slotConfig)).append("\n");
                    sb.append("活动域名：").append(queryActivityDomain(Long.valueOf(slotId))).append("\n");
                }
                sb.append("\n数据\n")
                        .append(String.format("%02d:%02d-%02d:%02d", lastLastHour, lastLastMinute, lastHour, lastMinute))
                        .append(" 广告位访问pv:").append(lastSlotRequestPv - lastLastSlotRequestPv)
                        .append(" 活动访问pv:").append(lastActivityRequestPv - lastLastActivityRequestPv)
                        .append(String.format(" 活动曝光成功率:%d%%", (int)(lastExpRate * 100))).append("\n")
                        .append(String.format("%02d:%02d-%02d:%02d", lastHour, lastMinute, hour, minute))
                        .append(" 广告位访问pv:").append(slotRequestPv - lastSlotRequestPv)
                        .append(" 活动访问pv:").append(activityRequestPv - lastActivityRequestPv)
                        .append(String.format(" 活动曝光成功率:%d%%", (int)(expRate * 100))).append("\n")
                        .append("异常值：").append(String.format("由 %d%% 下降到 %d%%，下降了 %d%%", (int)(lastExpRate * 100), (int)(expRate * 100), (int)((lastExpRate - expRate) * 100))).append("\n");
                DingRobotUtil.sendText(webhook, sb.toString(), null, true);

                // 设置告警沉默
//                setSlotWarned(slotId);
            }
        });

        // 活动UV流失率监控
        lastSlotRequestUvMap.forEach((slotId, lastSlotRequestUv) -> {
//            if (isSlotWarned(slotId)) {
//                return;
//            }

            Integer slotRequestUv = slotRequestUvMap.getOrDefault(slotId, 0);

            if (slotRequestUv <= 100 || slotRequestUv - lastSlotRequestUv < 100) {
                return;
            }

            Integer lastActivityRequestUv = lastActivityRequestUvMap.getOrDefault(slotId, 0);
            Integer activityRequestUv = activityRequestUvMap.getOrDefault(slotId, 0);

            double expRate = (activityRequestUv - lastActivityRequestUv) / (double) (slotRequestUv - lastSlotRequestUv);

            if (expRate < 0.7) {
                // 告警
                Slot slot = slotMapper.selectSlotById(Long.valueOf(slotId));
                App app = appMapper.selectAppById(slot.getAppId());
                SlotConfig slotConfig = slotConfigMapper.selectBySlotId(Long.valueOf(slotId));
                if (null != slotConfig && !isRedirectActivity(slotConfig)) {
                    return;
                }

                StringBuilder sb = new StringBuilder();
                sb.append("广告位：").append(slotId).append("\n")
                        .append("所属媒体：").append(app.getAppName()).append("\n")
                        .append("广告位链接：").append(querySlotUrl(Long.valueOf(slotId), slot.getSlotUrl())).append("\n");
                if (null != slotConfig) {
                    sb.append("活动：").append(getRedirectActivity(slotConfig)).append("\n");
                    sb.append("活动域名：").append(queryActivityDomain(Long.valueOf(slotId))).append("\n");
                }
                sb.append("\n数据\n")
                        .append(String.format("%02d:%02d-%02d:%02d", lastHour, lastMinute, hour, minute))
                        .append(" 广告位访问uv:").append(slotRequestUv - lastSlotRequestUv)
                        .append(" 活动访问uv:").append(activityRequestUv - lastActivityRequestUv)
                        .append(String.format(" 活动UV流失:%d%%", (int)((expRate - 1) * 100))).append("\n")
                        .append("异常值：活动UV流失大于30%\n");
                DingRobotUtil.sendText(webhook, sb.toString(), null, true);

                // 设置告警沉默
//                setSlotWarned(slotId);
            } else {
                double todayExpRate = activityRequestUv / (double) slotRequestUv;
                if ((todayExpRate - expRate) / todayExpRate >= 0.5 && expRate < 0.85) {
                    // 告警
                    Slot slot = slotMapper.selectSlotById(Long.valueOf(slotId));
                    App app = appMapper.selectAppById(slot.getAppId());
                    SlotConfig slotConfig = slotConfigMapper.selectBySlotId(Long.valueOf(slotId));
                    if (null != slotConfig && !isRedirectActivity(slotConfig)) {
                        return;
                    }

                    StringBuilder sb = new StringBuilder();
                    sb.append("广告位：").append(slotId).append("\n")
                            .append("所属媒体：").append(app.getAppName()).append("\n")
                            .append("广告位链接：").append(querySlotUrl(Long.valueOf(slotId), slot.getSlotUrl())).append("\n");
                    if (null != slotConfig) {
                        sb.append("活动：").append(getRedirectActivity(slotConfig)).append("\n");
                        sb.append("活动域名：").append(queryActivityDomain(Long.valueOf(slotId))).append("\n");
                    }
                    sb.append("\n数据\n")
                            .append(String.format("00:00-%02d:%02d", hour, minute))
                            .append(" 广告位访问uv:").append(slotRequestUv)
                            .append(" 活动访问uv:").append(activityRequestUv)
                            .append(String.format(" 活动UV流失:%d%%", (int)((todayExpRate - 1) * 100))).append("\n")
                            .append(String.format("%02d:%02d-%02d:%02d", lastHour, lastMinute, hour, minute))
                            .append(" 广告位访问uv:").append(slotRequestUv - lastSlotRequestUv)
                            .append(" 活动访问uv:").append(activityRequestUv - lastActivityRequestUv)
                            .append(String.format(" 活动UV流失:%d%%", (int)((expRate - 1) * 100))).append("\n")
                            .append("异常值：活动UV流失增幅超过50%\n");
                    DingRobotUtil.sendText(webhook, sb.toString(), null, true);

                    // 设置告警沉默
//                setSlotWarned(slotId);
                }
            }
        });

        // 落地页曝光成功率监控
        lastLastSlotRequestPvMap.forEach((slotId, lastLastSlotRequestPv) -> {
//            if (isSlotWarned(slotId)) {
//                return;
//            }

            Integer lastSlotRequestPv = lastSlotRequestPvMap.getOrDefault(slotId, 0);
            Integer slotRequestPv = slotRequestPvMap.getOrDefault(slotId, 0);

            if (slotRequestPv <= 100 || lastSlotRequestPv - lastLastSlotRequestPv < 100) {
                return;
            }

            Integer lastLastAdvertClickPv = lastLastAdvertClickPvMap.getOrDefault(slotId, 0);
            Integer lastAdvertClickPv = lastAdvertClickPvMap.getOrDefault(slotId, 0);
            Integer advertClickPv = advertClickPvMap.getOrDefault(slotId, 0);

            Integer lastLastLpExposurePv = lastLastLpExposurePvMap.getOrDefault(slotId, 0);
            Integer lastLpExposurePv = lastLpExposurePvMap.getOrDefault(slotId, 0);
            Integer lpExposurePv = lpExposurePvMap.getOrDefault(slotId, 0);

            if (lpExposurePv - lastLpExposurePv == 0 || lastLpExposurePv - lastLastLpExposurePv == 0) {
                return;
            }
            if (lastAdvertClickPv - lastLastAdvertClickPv < 10 && advertClickPv - lastAdvertClickPv < 10) {
                return;
            }

            double expRate = (lpExposurePv - lastLpExposurePv) / (double) (advertClickPv - lastAdvertClickPv);
            double lastExpRate = (lastLpExposurePv - lastLastLpExposurePv) / (double) (lastAdvertClickPv - lastLastAdvertClickPv);

            if (lastExpRate - expRate > 0.5 && expRate < 1) {
                // 告警
                Slot slot = slotMapper.selectSlotById(Long.valueOf(slotId));
                App app = appMapper.selectAppById(slot.getAppId());

                String landDomain = queryLandpageDomain(Long.valueOf(slotId));

                StringBuilder sb = new StringBuilder();
                sb.append("广告位：").append(slotId).append("\n")
                        .append("所属媒体：").append(app.getAppName()).append("\n")
                        .append("广告位链接：").append(querySlotUrl(Long.valueOf(slotId), slot.getSlotUrl())).append("\n")
                        .append("活动域名：").append(queryActivityDomain(Long.valueOf(slotId))).append("\n");
                if (StringUtils.isNotBlank(landDomain)) {
                    sb.append("落地页域名：").append(landDomain).append("\n");
                }
                sb.append("\n数据\n")
                        .append(String.format("%02d:%02d-%02d:%02d", lastLastHour, lastLastMinute, lastHour, lastMinute))
                        .append(" 计费点击pv:").append(lastAdvertClickPv - lastLastAdvertClickPv)
                        .append(" 落地页曝光pv:").append(lastLpExposurePv - lastLastLpExposurePv)
                        .append(String.format(" 落地页曝光成功率:%d%%", (int)(lastExpRate * 100))).append("\n")
                        .append(String.format("%02d:%02d-%02d:%02d", lastHour, lastMinute, hour, minute))
                        .append(" 计费点击pv:").append(advertClickPv - lastAdvertClickPv)
                        .append(" 落地页曝光pv:").append(lpExposurePv - lastLpExposurePv)
                        .append(String.format(" 落地页曝光成功率:%d%%", (int)(expRate * 100))).append("\n")
                        .append("异常值：").append(String.format("由 %d%% 下降到 %d%%，下降了 %d%%", (int)(lastExpRate * 100), (int)(expRate * 100), (int)((lastExpRate - expRate) * 100))).append("\n");
                DingRobotUtil.sendText(webhook, sb.toString(), null, true);

                // 设置告警沉默
//                setSlotWarned(slotId);
            }
        });
    }

    /**
     * 查询广告位的广告位访问uv时段数据
     */
    private Map<String, Integer> querySlotRequestUvHourMap(Date date, int hour) {
        SlotActivityHourData param = new SlotActivityHourData();
        param.setCurDate(date);
        param.setCurHour(hour);
        List<SlotActivityHourData> list = slotActivityHourDataMapper.selectSlotActivityHourDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> map = new HashMap<>();
        for (SlotActivityHourData data : list) {
            String slotId = String.valueOf(data.getSlotId());
            Integer uv = map.getOrDefault(slotId, 0) + data.getActivityRequestUv();
            map.put(slotId, uv);
        }
        return map;
    }

    /**
     * 查询广告位的广告位访问pv数据
     */
    private Map<String, Integer> querySlotRequestPvMap(Date date) {
        SlotData param = new SlotData();
        param.setCurDate(date);
        List<SlotData> list = slotDataMapper.selectSlotDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> map = new HashMap<>();
        for (SlotData data : list) {
            String slotId = String.valueOf(data.getSlotId());
            Integer pv = map.getOrDefault(slotId, 0) + data.getSlotRequestPv();
            map.put(slotId, pv);
        }
        return map;
    }

    /**
     * 查询广告位的广告位访问uv数据
     */
    private Map<String, Integer> querySlotRequestUvMap(Date date) {
        SlotData param = new SlotData();
        param.setCurDate(date);
        List<SlotData> list = slotDataMapper.selectSlotDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> map = new HashMap<>();
        for (SlotData data : list) {
            String slotId = String.valueOf(data.getSlotId());
            Integer uv = map.getOrDefault(slotId, 0) + data.getSlotRequestUv();
            map.put(slotId, uv);
        }
        return map;
    }

    /**
     * 查询广告位的活动访问pv数据
     */
    private Map<String, Integer> queryActivityRequestPvMap(Date date) {
        SlotActivityData param = new SlotActivityData();
        param.setCurDate(date);
        List<SlotActivityData> list = slotActivityDataMapper.selectSlotActivityDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> map = new HashMap<>();
        for (SlotActivityData data : list) {
            String slotId = String.valueOf(data.getSlotId());
            Integer pv = map.getOrDefault(slotId, 0) + data.getActivityRequestPv();
            map.put(slotId, pv);
        }
        return map;
    }

    /**
     * 查询广告位的活动访问uv数据
     */
    private Map<String, Integer> queryActivityRequestUvMap(Date date) {
        SlotActivityData param = new SlotActivityData();
        param.setCurDate(date);
        List<SlotActivityData> list = slotActivityDataMapper.selectSlotActivityDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> map = new HashMap<>();
        for (SlotActivityData data : list) {
            String slotId = String.valueOf(data.getSlotId());
            Integer uv = map.getOrDefault(slotId, 0) + data.getActivityRequestUv();
            map.put(slotId, uv);
        }
        return map;
    }

    /**
     * 查询广告位的广告点击pv数据
     */
    private Map<String, Integer> queryAdvertClickPvMap(Date date) {
        AdvertSlotDayData param = new AdvertSlotDayData();
        param.setCurDate(date);
        List<AdvertSlotDayData> list = advertSlotDayDataMapper.selectAdvertSlotDayDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> map = new HashMap<>();
        for (AdvertSlotDayData data : list) {
            String slotId = String.valueOf(data.getSlotId());
            Integer pv = map.getOrDefault(slotId, 0) + data.getClickPv();
            map.put(slotId, pv);
        }
        return map;
    }

    /**
     * 查询广告位的落地页曝光pv数据
     */
    private Map<String, Integer> queryLpExposurePvMap(Date date) {
        AdvertSlotDayData param = new AdvertSlotDayData();
        param.setCurDate(date);
        List<AdvertSlotDayData> list = advertSlotDayDataMapper.selectAdvertSlotDayDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> map = new HashMap<>();
        for (AdvertSlotDayData data : list) {
            String slotId = String.valueOf(data.getSlotId());
            Integer pv = map.getOrDefault(slotId, 0) + data.getLpExposurePv();
            map.put(slotId, pv);
        }
        return map;
    }

    /**
     * 查询广告位的广告位访问pv数据缓存
     */
    private Map<String, Integer> querySlotRequestPvMapCache(Date date, Integer hour, Integer minute) {
        return MapUtils.emptyIfNull(redisCache.getCacheMap(CrmRedisKeyFactory.K005.join(date, hour, minute)));
    }

    /**
     * 查询广告位的广告位访问uv数据缓存
     */
    private Map<String, Integer> querySlotRequestUvMapCache(Date date, Integer hour, Integer minute) {
        return MapUtils.emptyIfNull(redisCache.getCacheMap(CrmRedisKeyFactory.K006.join(date, hour, minute)));
    }

    /**
     * 查询广告位的活动访问pv数据缓存
     */
    private Map<String, Integer> queryActivityRequestPvMapCache(Date date, Integer hour, Integer minute) {
        return MapUtils.emptyIfNull(redisCache.getCacheMap(CrmRedisKeyFactory.K007.join(date, hour, minute)));
    }

    /**
     * 查询广告位的活动访问uv数据缓存
     */
    private Map<String, Integer> queryActivityRequestUvMapCache(Date date, Integer hour, Integer minute) {
        return MapUtils.emptyIfNull(redisCache.getCacheMap(CrmRedisKeyFactory.K019.join(date, hour, minute)));
    }


    /**
     * 查询广告位的广告点击pv数据缓存
     */
    private Map<String, Integer> queryAdvertClickPvMapCache(Date date, Integer hour, Integer minute) {
        return MapUtils.emptyIfNull(redisCache.getCacheMap(CrmRedisKeyFactory.K008.join(date, hour, minute)));
    }

    /**
     * 查询广告位的落地页曝光pv数据缓存
     */
    private Map<String, Integer> queryLpExposurePvMapCache(Date date, Integer hour, Integer minute) {
        return MapUtils.emptyIfNull(redisCache.getCacheMap(CrmRedisKeyFactory.K009.join(date, hour, minute)));
    }

    /**
     * 缓存数据
     */
    private void saveMapCache(CrmRedisKeyFactory keyFactory, Date date, Integer hour, Integer minute, Map<String, Integer> map) {
        String key = keyFactory.join(date, hour, minute);
        redisCache.setCacheMap(key, map);
        redisCache.expire(key, 1, TimeUnit.HOURS);
    }

    /**
     * 广告位是否已告警过
     *
     * @param slotId 广告位
     * @return true.告警过;false.未告警过
     */
    public boolean isSlotWarned(String slotId) {
        String key = CrmRedisKeyFactory.K004.join(slotId);
        return null != redisCache.getCacheObject(key);
    }

    /**
     * 设置广告位告警标识
     *
     * @param slotId 广告位ID
     */
    public void setSlotWarned(String slotId) {
        String key = CrmRedisKeyFactory.K004.join(slotId);
        redisCache.setCacheObject(key, "1", SLOT_WARNING_INTERVAL, TimeUnit.HOURS);
    }

    /**
     * 查询当前广告位链接
     */
    private String querySlotUrl(Long slotId, String slotUrl) {
        SlotConfig slotConfig = slotConfigMapper.selectBySlotId(slotId);
        if (null != slotConfig) {
            JSONObject domainConfig = JSON.parseObject(slotConfig.getDomainConfig());
            if (null != domainConfig) {
                return domainReplaceService.doReplaceDomain(slotUrl, domainConfig.getString(DomainType.SLOT_DOMAIN.getKey()));
            }
        }
        return slotUrl;
    }

    /**
     * 查询活动域名
     */
    private String queryActivityDomain(Long slotId) {
        SlotConfig slotConfig = slotConfigMapper.selectBySlotId(slotId);
        if (null != slotConfig && StringUtils.isNotBlank(slotConfig.getDomainConfig())) {
            JSONObject domainConfig = JSON.parseObject(slotConfig.getDomainConfig());
            if (null != domainConfig && domainConfig.containsKey(DomainType.ACTIVITY_DOMAIN.getKey())) {
                return domainConfig.getString(DomainType.ACTIVITY_DOMAIN.getKey());
            }
        }
        String activityUrl = sysConfigService.selectConfigByKey("activity.act.url");
        return ReUtil.get("[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\\.?", activityUrl, 0);
    }

    /**
     * 查询落地页域名
     */
    private String queryLandpageDomain(Long slotId) {
        SlotConfig slotConfig = slotConfigMapper.selectBySlotId(slotId);
        if (null != slotConfig && StringUtils.isNotBlank(slotConfig.getDomainConfig())) {
            JSONObject domainConfig = JSON.parseObject(slotConfig.getDomainConfig());
            if (null != domainConfig) {
                return domainConfig.getString(DomainType.LANDPAGE_DOMAIN.getKey());
            }
        }
        return null;
    }

    /**
     * 查询落地页在用的广告ID列表
     *
     * @param lpk 落地页标识
     * @return 广告ID列表
     */
    private Set<Long> queryAdvertIdsByLpk(String lpk) {
        Set<Long> advertIds = new HashSet<>();

        Advert advert = new Advert();
        advert.setLandpageUrl(lpk);
        List<Advert> adverts = advertService.selectAdvertList(advert);
        if (CollectionUtils.isNotEmpty(adverts)) {
            advertIds.addAll(adverts.stream().filter(ad -> StrUtil.equalsIgnoreCase(LandpageUtil.extractLpk(ad.getLandpageUrl()), lpk))
                    .map(Advert::getId).collect(Collectors.toList()));
        }
        AdvertOrientation orient = new AdvertOrientation();
        orient.setLandpageUrl(lpk);
        List<AdvertOrientation> orients = advertOrientationService.selectAdvertOrientationList(orient);
        if (CollectionUtils.isNotEmpty(orients)) {
            advertIds.addAll(orients.stream().filter(o -> StrUtil.equalsIgnoreCase(LandpageUtil.extractLpk(o.getLandpageUrl()), lpk))
                    .map(AdvertOrientation::getAdvertId).collect(Collectors.toList()));
        }
        return advertIds;
    }

    /**
     * 是否重定向到活动
     */
    private boolean isRedirectActivity(SlotConfig slotConfig) {
        if (!isAreaTargetRedirect(slotConfig.getRedirectType())) {
            return false;
        }
        List<AreaTargetRedirectItem> list = JSON.parseArray(slotConfig.getRedirectValue(), AreaTargetRedirectItem.class);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (AreaTargetRedirectItem areaItem : list) {
            if (CollectionUtils.isNotEmpty(areaItem.getRedirectValue())) {
                for (ShuntRedirectItem shuntItem : areaItem.getRedirectValue()) {
                    if (!redirectToActivity(shuntItem.getRedirectType())) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取重定向的活动
     */
    private String getRedirectActivity(SlotConfig slotConfig) {
        if (!isAreaTargetRedirect(slotConfig.getRedirectType())) {
            return slotConfig.getRedirectValue();
        }
        List<AreaTargetRedirectItem> list = JSON.parseArray(slotConfig.getRedirectValue(), AreaTargetRedirectItem.class);
        if (CollectionUtils.isEmpty(list)) {
            return slotConfig.getRedirectValue();
        }
        Set<String> activityIds = new HashSet<>();
        for (AreaTargetRedirectItem areaItem : list) {
            if (CollectionUtils.isNotEmpty(areaItem.getRedirectValue())) {
                for (ShuntRedirectItem shuntItem : areaItem.getRedirectValue()) {
                    if (redirectToActivity(shuntItem.getRedirectType())) {
                        activityIds.add(shuntItem.getRedirectValue());
                    }
                }
            }
        }
        return Joiner.on(",").join(activityIds);
    }
}
