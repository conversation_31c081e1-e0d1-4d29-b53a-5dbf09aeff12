package com.ruoyi.system.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.SspRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.entity.datashow.AppActivityData;
import com.ruoyi.system.entity.datashow.AppData;
import com.ruoyi.system.manager.publisher.PublisherManager;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.datasource.AppActivityDataService;
import com.ruoyi.system.service.datasource.AppDataService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.vo.publisher.prepay.PublisherPrepayVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.publisher.PayType.POSTPAID;
import static com.ruoyi.common.enums.publisher.PayType.PREPAY;

/**
 * 媒体月数据同步定时任务
 */
@Slf4j
@Component
public class AppMonthDataSyncJob {

    @Autowired
    private AppDataService appDataService;
    @Autowired
    private AppActivityDataService appActivityDataService;
    @Autowired
    private AppService appService;
    @Autowired
    private AppMonthDataService appMonthDataService;
    @Autowired
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private PublisherManager publisherManager;

    @Scheduled(cron = "0 1 0 2 * ?")
    public void advertStatusReset() {
        try (RedisLock lock = redisAtomicClient.getLock(SspRedisKeyFactory.K004.toString(), 60)){
            if(lock == null){
                return;
            }
            log.info("媒体月数据同步开始");
            DateTime lastMonth = DateUtil.lastMonth();
            //查询上个月所有去重后的媒体列表
            DateTime startDate = DateUtil.beginOfMonth(lastMonth);
            DateTime endDate = DateUtil.endOfMonth(lastMonth);
            List<Long> distinctAppIds = appDataService.selectDistinctAppIdByDate(startDate, endDate);
            if(CollectionUtils.isEmpty(distinctAppIds)){
                log.info("媒体月数据同步结束，无媒体数据");
                return;
            }
            //10个一组批量查询。每次最多查出310条
            List<List<Long>> lists = Lists.partition(distinctAppIds, 10);
            //遍历每个媒体，统计月数据
            lists.forEach(appIds -> statisticsAppMonthData(startDate, endDate, appIds));

            log.info("媒体月数据同步成功");
        }catch (Exception e){
            log.error("媒体月账单同步失败,e:",e);
        }
    }

    /**
     * 统计媒体月数据
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param appIds 媒体id列表
     */
    public void statisticsAppMonthData(DateTime startDate, DateTime endDate, List<Long> appIds) {
        try{
            AppData data = new AppData();
            data.setAppIds(appIds);
            data.setStartDate(startDate);
            data.setEndDate(endDate);
            List<AppData> appDataList = appDataService.selectAppDataList(data);
            //<媒体id，媒体每日数据>
            Map<Long, List<AppData>> appDataMap = appDataList.stream().collect(Collectors.groupingBy(AppData::getAppId));

            List<AppActivityData> appActivityData = appActivityDataService.selectAppActivityData(appIds, startDate, endDate);
            //<媒体id,媒体活动统计数据>
            Map<String, AppActivityData> activityDataMap = appActivityData.stream().collect(Collectors.toMap(activityData -> activityData.getAppId() + "_" + DateUtils.dateTime(activityData.getCurDate()), Function.identity(),(v1,v2) -> v2));

            List<App> appInfoList = appService.selectSimpleInfoByIds(appIds);
            //<媒体id,媒体信息>
            Map<Long, App> appMap = appInfoList.stream().collect(Collectors.toMap(App::getId, Function.identity(),(v1,v2) -> v2));
            // 预付款媒体列表
            List<PublisherPrepayVO> prepayPublisherList = publisherManager.getPrepayPublisher();
            Map<Long, PublisherPrepayVO> prepayPublisherMap = prepayPublisherList.stream().collect(Collectors.toMap(PublisherPrepayVO::getAccountId, Function.identity(), (v1, v2) -> v2));

            List<AppMonthDataEntity> insertList = new ArrayList<>();
            appDataMap.forEach((appId,appDatas) ->{
                AppMonthDataEntity entity = buildAppMonthDataEntity(startDate, activityDataMap, appMap, appId, appDatas, prepayPublisherMap);
                if(Objects.nonNull(entity)){
                    insertList.add(entity);
                }
            });
            //存入媒体月数据表
            appMonthDataService.batchInsertOrUpdate(insertList);
        }catch (Exception e){
            log.error("statisticsAppMonthData error,appIds:{},exception:",appIds,e);
        }
    }

    /**
     * 构建媒体月统计数据
     * @param startDate 开始时间
     * @param activityDataMap 活动统计数据
     * @param appMap 媒体信息
     * @param appId 媒体id列表
     * @param appDatas 媒体日数据列表
     * @param prepayPublisherMap 预付款媒体映射
     * @return 媒体月统计数据
     */
    private AppMonthDataEntity buildAppMonthDataEntity(DateTime startDate, Map<String, AppActivityData> activityDataMap,
                                                       Map<Long, App> appMap, Long appId, List<AppData> appDatas,
                                                       Map<Long, PublisherPrepayVO> prepayPublisherMap) {
        App app = appMap.get(appId);
        if(Objects.isNull(app)){
            log.error("媒体月数据同步失败,媒体不存在,appId:{}", appId);
            return null;
        }

        AppMonthDataEntity entity = new AppMonthDataEntity();
        entity.setAccountId(app.getAccountId());
        entity.setAppId(appId);
        entity.setMonthDate(DateUtils.dateTimeMonth(startDate));

        appDatas.forEach(appData -> {
            entity.setOuterConsume(appData.getOuterConsume().intValue()+ NumberUtils.defaultInt(entity.getOuterConsume()));
            entity.setAppRevenue(appData.getAppRevenue().intValue() +  NumberUtils.defaultInt(entity.getAppRevenue()));
            entity.setTotalConsume(appData.getTotalConsume().intValue() + NumberUtils.defaultInt(entity.getTotalConsume()));
            entity.setSlotRequestUv(appData.getSlotRequestUvOriginal() + NumberUtils.defaultInt(entity.getSlotRequestUv()));
            entity.setSlotRequestPv(appData.getSlotRequestPv() + NumberUtils.defaultInt(entity.getSlotRequestPv()));
            entity.setAppSlotClickPv(appData.getAppSlotClickPv() + NumberUtils.defaultInt(entity.getAppSlotClickPv()));
            entity.setAppSlotClickUv(appData.getAppSlotClickUv() + NumberUtils.defaultInt(entity.getAppSlotClickUv()));
            entity.setAppSlotExposurePv(appData.getAppSlotExposurePv() + NumberUtils.defaultInt(entity.getAppSlotExposurePv()));
            entity.setAppSlotExposureUv(appData.getAppSlotExposureUv() + NumberUtils.defaultInt(entity.getAppSlotExposureUv()));
            entity.setSlotRequestUvCalculate(appData.getSlotRequestUv() + NumberUtils.defaultInt(entity.getSlotRequestUvCalculate()));
            entity.setNhConsume(appData.getNhConsume().intValue() + NumberUtils.defaultInt(entity.getNhConsume()));
            entity.setNhCost(appData.getNhCost() + NumberUtils.defaultLong(entity.getNhCost()));
            entity.setOuterCost(appData.getOuterCost() + NumberUtils.defaultLong(entity.getOuterCost()));
            AppActivityData appActivityData = activityDataMap.get(appData.getAppId()+"_"+DateUtils.dateTime(appData.getCurDate()));
            if(Objects.nonNull(appActivityData)){
                entity.setJoinUv(appActivityData.getJoinUv() + NumberUtils.defaultInt(entity.getJoinUv()));
                entity.setJoinPv(appActivityData.getJoinPv() + NumberUtils.defaultInt(entity.getJoinPv()));
            }else{
                entity.setJoinUv(NumberUtils.defaultInt(entity.getJoinUv()));
                entity.setJoinPv(NumberUtils.defaultInt(entity.getJoinPv()));
            }
        });
        // 预付款
        if (prepayPublisherMap.containsKey(app.getAccountId())) {
            entity.setPayType(PREPAY.getType());
            // 计算预付款欠款金额=max(历史结算金额扣除后还欠的预付款金额-当前结算金额,0)
            entity.setPrepayAmount(Math.max(prepayPublisherMap.get(app.getAccountId()).getPrepayAmount() - entity.getAppRevenue(), 0));
        } else {
            entity.setPayType(POSTPAID.getType());
            entity.setPrepayAmount(0L);
        }
        return entity;
    }
}
