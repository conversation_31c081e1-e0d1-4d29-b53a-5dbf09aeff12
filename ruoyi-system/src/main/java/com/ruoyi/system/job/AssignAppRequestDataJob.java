package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.entity.datashow.AppData;
import com.ruoyi.system.entity.datashow.SlotActivityData;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.service.datasource.AppDataService;
import com.ruoyi.system.service.datasource.SlotActivityDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 每天00:00计算特定媒体广告位访问pv uv
 *
 * <AUTHOR>
 * @date 2022/02/23
 */
@Slf4j
@Component
public class AssignAppRequestDataJob {

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private AppDataService appDataService;

    @Autowired
    private SlotActivityDataService slotActivityDataService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Scheduled(cron = "0 5 0 * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K017.toString(), 60);
        if (lock == null) {
            return;
        }
        log.info("1189媒体广告位访问数据重新计算定时任务开始");
        Long appId = 1189L;
        Date yesterday = DateUtil.beginOfDay(DateUtils.addDays(new Date(),-1));

        AppData appData = appDataService.selectByAppIdAndDate(appId, yesterday);
        if(Objects.isNull(appData)){
            return;
        }
        //查询指定媒体的所有昨日广告位数据
        List<SlotData> slotData = slotDataService.selectDataByAppIdAndDate(appId, yesterday);
        List<SlotActivityData> slotActivityData = slotActivityDataService.selectDataByAppIdAndDate(appId, yesterday);

        Map<Long, SlotData> slotDataMap = slotData.stream().collect(Collectors.toMap(SlotData::getSlotId, Function.identity(), (v1, v2) -> v1));

        Map<Long, SlotActivityData> activityDataMap = slotActivityData.stream().collect(Collectors.toMap(SlotActivityData::getSlotId, Function.identity(), (v1, v2) -> v1));

        AtomicInteger requestPv = new AtomicInteger(0);
        AtomicInteger requestUv = new AtomicInteger(0);
        //批量把广告位访问pv和uv重新修改为活动访问pv和活动访问uv
        slotDataMap.forEach((slotId,data) ->{
            //如果当天没有活动数据，则pv uv 默认0，产品跟运营定的
            Integer requestPvInt = 0;
            Integer requestUvInt = 0;

            SlotActivityData activityData = activityDataMap.get(slotId);
            if(Objects.nonNull(activityData)){
                requestPvInt = activityData.getActivityRequestPv();
                requestUvInt = activityData.getActivityRequestUv();
            }
            requestPv.addAndGet(requestPvInt);
            requestUv.addAndGet(requestUvInt);

            data.setSlotRequestPv(requestPvInt);
            data.setSlotRequestUv(requestUvInt);
        });
        slotDataService.batchUpdateSlotRequestPvAndUv(Lists.newArrayList(slotDataMap.values()));
        //重新计算媒体访问pv和uv
        AppData updateData = new AppData();
        updateData.setId(appData.getId());
        updateData.setSlotRequestPv(requestPv.get());
        updateData.setSlotRequestUv(requestUv.get());
        appDataService.updateAppData(updateData);
        log.info("1189媒体广告位访问数据重新计算定时任务结束, appData:{}", JSON.toJSONString(updateData));
    }
}
