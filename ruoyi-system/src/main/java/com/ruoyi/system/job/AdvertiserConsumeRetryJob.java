package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeDetailRecordEntity;
import com.ruoyi.system.manager.advertiser.AdvertiserConsumeManager;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 广告主消费重试任务
 *
 * <AUTHOR>
 * @date 2022/03/25
 */
@Slf4j
@Component
public class AdvertiserConsumeRetryJob {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertiserConsumeManager advertiserConsumeManager;

    @Autowired
    private AdvertiserConsumeRecordDetailService advertiserConsumeRecordDetailService;

    @Scheduled(cron = "0 */10 * * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("AdvertiserConsumeRetryJob"), 120);
        if (lock == null) {
            return;
        }

        AdvertiserConsumeDetailRecordEntity param = new AdvertiserConsumeDetailRecordEntity();
        param.setIsDone(0);
        param.setRetryTimes(5);
        // 只查十分钟之前的数据。因为出了一个bug，消费逻辑在xx:09:59的时候新增记录，但是还没来得及扣费，就在xx:10:00被重试逻辑扣费，导致一笔订单多次扣费
        param.setGmtCreate(DateUtil.offsetMinute(new Date(), -10));
        List<AdvertiserConsumeDetailRecordEntity> list = advertiserConsumeRecordDetailService.selectList(param);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (AdvertiserConsumeDetailRecordEntity detailRecord : list) {
            try {
                advertiserConsumeManager.consumeRetry(detailRecord);
            } catch (Exception e) {
                AdvertiserConsumeDetailRecordEntity updateDetail = new AdvertiserConsumeDetailRecordEntity();
                updateDetail.setId(detailRecord.getId());
                updateDetail.setRetryTimes(1);
                advertiserConsumeRecordDetailService.update(updateDetail);
            }
        }
    }
}
