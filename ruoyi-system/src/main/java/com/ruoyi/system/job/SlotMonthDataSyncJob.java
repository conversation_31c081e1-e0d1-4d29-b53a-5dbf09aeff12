package com.ruoyi.system.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.SspRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.datashow.SlotActivityData;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slotdata.SlotMonthDataEntity;
import com.ruoyi.system.service.datasource.SlotActivityDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.slotdata.SlotMonthDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 广告位月数据同步定时任务
 *
 * <AUTHOR>
 * @date 2021-09-22 10:00:00
 */
@Slf4j
@Component
public class SlotMonthDataSyncJob {

    @Autowired
    private SlotDataService slotDataService;
    @Autowired
    private SlotActivityDataService slotActivityDataService;
    @Autowired
    private SlotService slotService;
    @Autowired
    private SlotMonthDataService slotMonthDataService;
    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Scheduled(cron = "0 1 0 2 * ?")
    public void advertStatusReset() {
        try (RedisLock lock = redisAtomicClient.getLock(SspRedisKeyFactory.K005.toString(), 60)) {
            if (lock == null) {
                return;
            }
            log.info("广告位月数据同步开始");
            DateTime lastMonth = DateUtil.lastMonth();
            //查询上个月所有去重后的广告位列表
            DateTime startDate = DateUtil.beginOfMonth(lastMonth);
            DateTime endDate = DateUtil.endOfMonth(lastMonth);
            List<Long> distinctSlotIds = slotDataService.selectDistinctSlotIdByDate(startDate, endDate);
            if (CollectionUtils.isEmpty(distinctSlotIds)) {
                log.info("广告位月数据同步结束，无广告位数据");
                return;
            }
            //10个一组批量查询。每次最多查出310条
            List<List<Long>> lists = Lists.partition(distinctSlotIds, 10);
            //遍历每个广告位，统计月数据
            lists.forEach(slotIds -> statisticsSlotMonthData(startDate, endDate, slotIds));

            log.info("广告位月数据同步成功");
        }catch (Exception e){
            log.error("广告位月账单数据同步失败,e:",e);
        }
    }

    /**
     * 统计广告位月数据
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param slotIds 广告位id列表
     */
    public void statisticsSlotMonthData(DateTime startDate, DateTime endDate, List<Long> slotIds) {
        SlotData data = new SlotData();
        data.setSlotIds(slotIds);
        data.setStartDate(startDate);
        data.setEndDate(endDate);
        List<SlotData> slotDataList = slotDataService.selectSlotDataList(data);
        //<广告位id，广告位每日数据>
        Map<Long, List<SlotData>> slotDataMap = slotDataList.stream().collect(Collectors.groupingBy(SlotData::getSlotId));
        List<SlotActivityData> slotActivityData = slotActivityDataService.selectSlotActivityData(slotIds, startDate, endDate);
        //<广告位id,广告位活动统计数据>
        Map<String, SlotActivityData> activityDataMap = slotActivityData.stream().collect(Collectors.toMap(activityData -> activityData.getSlotId() + "_" + DateUtils.dateTime(activityData.getCurDate()), Function.identity(),(v1,v2) -> v2));

        List<Slot> slotInfoList = slotService.selectSimpleSlotByIds(slotIds);
        //<广告位id,广告位信息>
        Map<Long, Slot> slotMap = slotInfoList.stream().collect(Collectors.toMap(Slot::getId, Function.identity(),(v1,v2) -> v2));

        List<SlotMonthDataEntity> insertList = new ArrayList<>();
        slotDataMap.forEach((slotId,slotDatas) ->{
            SlotMonthDataEntity entity = buildSlotMonthDataEntity(startDate, activityDataMap, slotMap, slotId, slotDatas);
            if(Objects.nonNull(entity)){
                insertList.add(entity);
            }
        });
        //存入广告位月数据表
        slotMonthDataService.batchInsertOrUpdate(insertList);
    }

    /**
     * 构建广告位月统计数据
     * @param startDate 开始时间
     * @param activityDataMap 活动统计数据
     * @param slotMap 广告位信息
     * @param slotId 广告位id列表
     * @param slotDatas 广告位日数据列表
     * @return 广告位月统计数据
     */
    private SlotMonthDataEntity buildSlotMonthDataEntity(DateTime startDate, Map<String, SlotActivityData> activityDataMap, Map<Long, Slot> slotMap, Long slotId, List<SlotData> slotDatas) {
        Slot slot = slotMap.get(slotId);
        if(Objects.isNull(slot)){
            log.error("广告位月数据同步失败,广告位不存在,slotId:{}", slotId);
            return null;
        }

        SlotMonthDataEntity entity = new SlotMonthDataEntity();
        entity.setAccountId(slot.getAccountId());
        entity.setAppId(slot.getAppId());
        entity.setSlotId(slotId);
        entity.setMonthDate(DateUtils.dateTimeMonth(startDate));

        slotDatas.forEach(slotData -> {
            entity.setOuterConsume(slotData.getOuterConsume().intValue()+ NumberUtils.defaultInt(entity.getOuterConsume()));
            entity.setAppRevenue(slotData.getAppRevenue().intValue() +  NumberUtils.defaultInt(entity.getAppRevenue()));
            entity.setTotalConsume(slotData.getTotalConsume().intValue() + NumberUtils.defaultInt(entity.getTotalConsume()));
            entity.setSlotRequestUv(slotData.getSlotRequestUvOriginal() +NumberUtils.defaultInt( entity.getSlotRequestUv()));
            entity.setSlotRequestPv(slotData.getSlotRequestPv() + NumberUtils.defaultInt(entity.getSlotRequestPv()));
            entity.setAppSlotClickPv(slotData.getAppSlotClickPv() + NumberUtils.defaultInt(entity.getAppSlotClickPv()));
            entity.setAppSlotClickUv(slotData.getAppSlotClickUv() + NumberUtils.defaultInt(entity.getAppSlotClickUv()));
            entity.setAppSlotExposurePv(slotData.getAppSlotExposurePv() + NumberUtils.defaultInt(entity.getAppSlotExposurePv()));
            entity.setAppSlotExposureUv(slotData.getAppSlotExposureUv() + NumberUtils.defaultInt(entity.getAppSlotExposureUv()));
            entity.setSlotRequestUvCalculate(slotData.getSlotRequestUv() + NumberUtils.defaultInt(entity.getSlotRequestUvCalculate()));
            entity.setNhConsume(slotData.getNhConsume().intValue() + NumberUtils.defaultInt(entity.getNhConsume()));
            SlotActivityData activityData = activityDataMap.get(slotData.getSlotId() + "_" + DateUtils.dateTime(slotData.getCurDate()));
            if(Objects.nonNull(activityData)){
                entity.setJoinUv(activityData.getJoinUv() + NumberUtils.defaultInt(entity.getJoinUv()));
                entity.setJoinPv(activityData.getJoinPv() + NumberUtils.defaultInt(entity.getJoinPv()));
            }else{
                entity.setJoinUv(NumberUtils.defaultInt(entity.getJoinUv()));
                entity.setJoinPv(NumberUtils.defaultInt(entity.getJoinPv()));
            }
        });
        return entity;
    }
}
