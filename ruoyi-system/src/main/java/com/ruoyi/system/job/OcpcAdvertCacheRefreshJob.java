package com.ruoyi.system.job;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.advert.ChargeTypeEnum;
import com.ruoyi.system.domain.adengine.AdvertCacheDto;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * OCPC广告缓存刷新定时任务
 *
 * <AUTHOR>
 * @date 2023/07/14
 */
@Slf4j
@Component
public class OcpcAdvertCacheRefreshJob {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Scheduled(cron = "0 0/5 * * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("AdvertCacheRefreshJob"), 120);
        if (lock == null) {
            return;
        }
        List<AdvertCacheDto> adverts = advertCacheService.queryTotalAdvertCache();
        if (CollectionUtils.isEmpty(adverts)) {
            return;
        }
        List<Long> advertIds = adverts.stream().filter(s -> ChargeTypeEnum.isOCPC(s.getChargeType())).map(AdvertCacheDto::getAdvertId).collect(toList());
        if (CollectionUtils.isEmpty(advertIds)) {
            return;
        }
        refreshCacheService.sendRefreshAdvertCacheMsg(advertIds);
        log.info("OCPC广告缓存定时刷新, advertIds={}", JSON.toJSONString(advertIds));
    }
}
