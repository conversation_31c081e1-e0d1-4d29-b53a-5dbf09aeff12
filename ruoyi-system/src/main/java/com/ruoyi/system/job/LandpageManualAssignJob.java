package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeBo;
import com.ruoyi.system.domain.landpage.AssignTaskInfo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.datashow.LandpageFormRecord;
import com.ruoyi.system.entity.datashow.LandpageFormSendRecordEntity;
import com.ruoyi.system.manager.advertiser.AdvertiserConsumeManager;
import com.ruoyi.system.mapper.landpage.LandpageFormRecordMapper;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.landpage.AdvertiserFormDataService;
import com.ruoyi.system.service.landpage.LandpageFormSendRecordService;
import com.ruoyi.system.service.landpage.LandpageLibraryService;
import com.ruoyi.system.service.landpage.LandpageService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;
import java.util.Set;

import static com.ruoyi.common.enums.advertiser.FormAdvertiserPriceType.isConsume;
import static com.ruoyi.system.util.LandpageUtil.canRetryCallback;

/**
 * 表单手动分配任务
 */
@Slf4j
@Component
public class LandpageManualAssignJob {

    @Autowired
    public RedisCache redisCache;

    @Autowired
    private LandpageFormRecordMapper landpageFormRecordMapper;

    @Autowired
    private LandpageFormSendRecordService landpageFormSendRecordService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AccountService accountService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private LandpageService landpageService;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    @Autowired
    private AdvertiserConsumeManager advertiserConsumeManager;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private LandpageLibraryService landpageLibraryService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AdvertiserFormDataService advertiserFormDataService;


    private static final String REDIS_KEY = CrmRedisKeyFactory.K013.toString();

    @Scheduled(cron = "*/6 * * * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("LandpageManualAssignJob"), 3);
        if (lock == null) {
            return;
        }

        Set<String> taskSet = redisCache.getZSetRangeByScore(REDIS_KEY, 0, System.currentTimeMillis() / 1000.0);
        if (CollectionUtils.isEmpty(taskSet)) {
            return;
        }
        for (String task : taskSet) {
            int result = 0;
            try {
                AssignTaskInfo taskInfo = JSON.parseObject(task, AssignTaskInfo.class);
                result = callback(taskInfo);
                redisCache.deleteCacheZSet(REDIS_KEY, task);
                redisCache.deleteCacheSet(CrmRedisKeyFactory.K023.join(taskInfo.getAdvertiserId()), String.valueOf(taskInfo.getRecordId()));
            } catch (Exception e) {
                log.error("手动分配表单异常, task={}", task, e);
            }
            if (result > 0) {
                try {
                    Thread.sleep(500);
                } catch (Exception ignored) {
                }
            }
        }
    }

    private int callback(AssignTaskInfo taskInfo) {
        if (null == taskInfo) {
            return 0;
        }
        // 查询表单
        LandpageFormRecord record = landpageFormRecordMapper.selectLandpageFormRecordById(taskInfo.getRecordId());
        if (null == record) {
            return 0;
        }
        // 身份证校验失败的不传
        if (record.getIdCardAudit() > 1) {
            return 0;
        }

        // CPC结算广告主定向分单
        Long formAdvertiserId = advertService.selectById(record.getAdvertId(), Advert::getAdvertiserId);
//        if (advertiserService.isCpcAdvertiser(formAdvertiserId) && !Objects.equals(taskInfo.getAdvertiserId(), formAdvertiserId)) {
//            String sbr = "落地页手动分配失败\n" +
//                    "\n订单号: " + record.getOrderId() +
//                    "\n广告ID: " + record.getAdvertId() +
//                    "\n落地页: " + record.getLandpageUrl() +
//                    "\n广告主ID: " + formAdvertiserId +
//                    "\nCPC结算广告主的表单不能分配给其他广告主";
//            landpageService.sendLpNoticeToDing(sbr);
//            return 0;
//        }

        // 查询广告主信息
        Long advertiserId = taskInfo.getAdvertiserId();
        Account account = accountService.selectAccountById(advertiserId);
        if (null == account || StringUtils.isBlank(account.getExtInfo())) {
            return 0;
        }
        AccountExtInfo extInfo = JSON.parseObject(account.getExtInfo(), AccountExtInfo.class);
        if (null == extInfo || StringUtils.isBlank(extInfo.getAccessKey()) || StringUtils.isBlank(extInfo.getSecretKey())) {
            return 0;
        }
        String url = extInfo.getLpCallbackUrl();
        if (StringUtils.isBlank(url) || !url.startsWith("http")) {
            return 0;
        }
        // 广告主余额校验
        if (NumberUtils.defaultInt(taskInfo.getFormPrice()) > 0) {
            Integer advertiserBalance = advertiserBalanceService.selectTotalAmountByAccountId(advertiserId);
            if (advertiserBalance < taskInfo.getFormPrice()) {
                if (null != redisAtomicClient.getLock(CrmRedisKeyFactory.K022.join(advertiserId), 7200)) {
                    GlobalThreadPool.executorService.submit(() -> {
                        String sb = "广告主余额不足，手动分配失败\n" +
                                "\n广告主ID: " + advertiserId +
                                "\n广告主名称: " + account.getCompanyName() +
                                "\n广告主余额: " + NumberUtils.fenToYuan(advertiserBalance) + "元" +
                                "\n手动分配剩余表单: " + redisCache.countCacheZSet(REDIS_KEY).intValue();
                        DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb);
                    });
                }
                return 0;
            }
        }

        // 上报表单
        try (RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K002.join(record.getId()), 120)) {
            if (null == lock) {
                return 0;
            }

            // 落地页标签
            String lpTag = record.getLandpageTag();
            if (StringUtils.isBlank(lpTag)) {
                String lpk = LandpageUtil.extractLpk(record.getLandpageUrl());
                lpTag = landpageLibraryService.getLandpageTag(lpk);
            }

            // 过滤重复信息的发送
            if (landpageService.isIdCardSent(record.getIdCardMd5(), lpTag)) {
                log.info("手动分配表单，过滤重复信息(身份证)的发送，advertiserId={}, recordId={}", advertiserId, taskInfo.getRecordId());
                String sbr = "落地页手动分配失败\n" +
                        "\n广告主ID: " + advertiserId +
                        "\n广告主名称: " + account.getCompanyName() +
                        "\n订单号: " + record.getOrderId() +
                        "\n广告ID: " + record.getAdvertId() +
                        "\n落地页标签: " + lpTag +
                        "\n身份证重复";
                landpageService.sendLpNoticeToDing(sbr);
                return 0;
            }

            // 检查广告主标签和落地页标签
            // 排除CPC结算广告主定向分单
            if (!advertiserService.isCpcAdvertiser(formAdvertiserId) && !advertiserTagService.isExist(advertiserId, lpTag)) {
                String sbr = "落地页手动分配失败\n" +
                        "\n广告主ID: " + advertiserId +
                        "\n广告主名称: " + account.getCompanyName() +
                        "\n订单号: " + record.getOrderId() +
                        "\n广告ID: " + record.getAdvertId() +
                        "\n落地页标签: " + lpTag +
                        "\n广告主与落地页标签不匹配";
                landpageService.sendLpNoticeToDing(sbr);
                return 0;
            }

            // 生产环境表单身份证校验不通过的不传
            if (!Objects.equals(record.getIdCardAudit(), 1) && SpringEnvironmentUtils.isProd()) {
                String sbr = "落地页手动分配失败\n" +
                        "\n广告主ID: " + advertiserId +
                        "\n广告主名称: " + account.getCompanyName() +
                        "\n订单号: " + record.getOrderId() +
                        "\n广告ID: " + record.getAdvertId() +
                        (Objects.equals(record.getIdCardAudit(), 2) ? "\n身份证校验未通过" : "\n未校验身份证");
                landpageService.sendLpNoticeToDing(sbr);
                return 0;
            }

            // 广告位对应渠道
            Integer channel = slotService.getSlotChannel(record.getSlotId());
            channel = Objects.equals(extInfo.getAppRet(), 1) ? NumberUtils.defaultInt(channel) : 0;

            // 调用接口
            String resp = landpageService.callback(url, extInfo.getAccessKey(), extInfo.getSecretKey(), record, channel, advertiserId);
            if (canRetryCallback(resp)) {
                // 失败重试
                resp = landpageService.callback(url, extInfo.getAccessKey(), extInfo.getSecretKey(), record, channel, advertiserId);
            }
            int isSuccess = LandpageUtil.isCallbackSuccess(resp);

            // 毛表单/有效表单计费
            Integer formPrice = taskInfo.getFormPrice();
            if (!isConsume(extInfo.getPriceType(), isSuccess)) {
                formPrice = 0;
            }

            // 发送记录
            LandpageFormSendRecordEntity sendRecord = new LandpageFormSendRecordEntity();
            sendRecord.setOrderId(record.getOrderId());
            sendRecord.setAdvertiserId(advertiserId);
            sendRecord.setRecordId(record.getId());
            sendRecord.setChannel(channel);
            sendRecord.setUrl(url);
            sendRecord.setResp(resp);
            sendRecord.setIsSuccess(isSuccess);
            sendRecord.setRemark(taskInfo.getRemark());
            sendRecord.setFormPrice(formPrice);
            sendRecord.setOperatorId(taskInfo.getOperatorId());
            int result = landpageFormSendRecordService.insert(sendRecord);

            // 如果回传成功且设置了表单价格，进行广告主扣费
            if (NumberUtils.defaultInt(formPrice) > 0) {
                AdvertiserConsumeBo consumeBo = new AdvertiserConsumeBo();
                consumeBo.setAccountId(advertiserId);
                consumeBo.setConsumeAmount(formPrice);
                consumeBo.setRecordId(record.getId());
                consumeBo.setConsumeType(2);
                consumeBo.setRemark("sendRecordId:" + sendRecord.getId());
                advertiserConsumeManager.consume(consumeBo);

                // 累加广告位维度当日消费
                landpageService.incrSlotFormConsume(record.getSlotId(), formPrice);
            }

            // 更新广告主表单数据
            advertiserFormDataService.incr(DateUtil.beginOfDay(new Date()), advertiserId, isSuccess, formPrice);

            StringBuilder sbr = new StringBuilder();
            sbr.append("落地页手动分配\n")
                    .append("\n广告主ID: ").append(advertiserId)
                    .append("\n广告主名称: ").append(account.getCompanyName())
                    .append("\n订单号: ").append(record.getOrderId())
                    .append("\n广告ID: ").append(record.getAdvertId());
            if (channel > 0) {
                sbr.append("\n渠道: ").append(channel);
            }
            sbr.append("\n落地页标签: ").append(lpTag);
            if (NumberUtils.defaultInt(formPrice) > 0) {
                sbr.append("\n表单价格: ").append(NumberUtils.fenToYuan(formPrice)).append("元");
                sbr.append("\n账户余额: ").append(NumberUtils.fenToYuan(advertiserBalanceService.selectTotalAmountByAccountId(advertiserId))).append("元");
            }
            if (StringUtils.isNotBlank(taskInfo.getRemark())) {
                sbr.append("\n备注: ").append(taskInfo.getRemark());
            }
            sbr.append("\n姓名: ").append(DesensitizedUtil.chineseName(record.getName()))
                    .append("\n手机号: ").append(DesensitizedUtil.mobilePhone(record.getPhone()))
                    .append("\n省: ").append(record.getProvince())
                    .append("\n市: ").append(record.getCity())
                    .append("\n上报结果: ").append(StrUtil.length(resp) > 256 && StrUtil.contains(resp, "502 Bad Gateway") ? "502 Bad Gateway": resp)
                    .append("\n广告主剩余待分配数量: ").append(Math.max(redisCache.countCacheSet(CrmRedisKeyFactory.K023.join(advertiserId)).intValue() - 1, 0))
                    .append("\n剩余待分配表单总数量: ").append(Math.max(redisCache.countCacheZSet(REDIS_KEY).intValue() - 1, 0));
            landpageService.sendLpNoticeToDing(sbr.toString());

            return result;
        } catch (Exception e) {
            log.error("落地页数据回传异常, recordId={}, orderId={}", record.getId(), record.getOrderId(), e);
        }
        return 0;
    }
}
