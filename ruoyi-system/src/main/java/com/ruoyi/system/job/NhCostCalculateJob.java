package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.advert.AdvertCostBillingTypeEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.system.bo.landpage.LandPageSumDataBo;
import com.ruoyi.system.entity.advert.AdvertCost;
import com.ruoyi.system.entity.datashow.AdvertSlotDayData;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.landpage.LandpageFormFullRecord;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.mapper.landpage.LandpageFormFullRecordMapper;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.service.datasource.AdvertSlotDayDataService;
import com.ruoyi.system.service.datasource.SlotAppDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.manager.AdvertCostService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 每天00:10计算诺禾结算款
 *
 * <AUTHOR>
 * @date 2022/02/17
 */
@Slf4j
@Component
public class NhCostCalculateJob {

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private AdvertSlotDayDataService advertSlotDayDataService;

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private SlotAppDataService slotAppDataService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertCostService advertCostService;

    @Autowired
    private LandpageFormFullRecordMapper landpageFormFullRecordMapper;

    @Scheduled(cron = "0 10 0 * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K015.toString(), 60);
        if (lock == null) {
            return;
        }
        log.info("诺禾日成本结算统计定时任务开始");

        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());

        // 查询所有的广告位
        List<Slot> slots = slotMapper.selectSlotList(new Slot());

        // 结算指标成本列表
        Map<Long, AdvertCost> advertCostMap = advertCostService.selectMapByDateAndType(yesterday, AdvertCostBillingTypeEnum.LANDPAGE_CONVERT.getType());

        // 查询广告位-广告维度消耗数据
        Map<Long, Map<Long, Integer>> slotAdvertConsumeMap = querySlotAdvertConsumeMap(yesterday);

        LandpageFormFullRecord record = new LandpageFormFullRecord();
        record.setCbStartDate(DateUtil.beginOfDay(yesterday));
        record.setCbEndDate(DateUtil.endOfDay(yesterday));
        record.setSlotIds(ListUtils.mapToList(slots, Slot::getId));
        List<LandPageSumDataBo> landPageSumDataBos = landpageFormFullRecordMapper.selectSumPriceGroupBySlot(record);
        Map<Long, Long> priceSumMap = landPageSumDataBos.stream().filter(data -> Objects.nonNull(data.getFormPriceSum())).collect(Collectors.toMap(LandPageSumDataBo::getSlotId, LandPageSumDataBo::getFormPriceSum));

        // 遍历广告位计算结算款
        for (Slot slot : slots) {
            try {
                Long slotId = slot.getId();
                long nhCost = priceSumMap.getOrDefault(slotId,0L);
                // 所有结算指标为落地页转化的广告
                Set<Long> advertIds = advertCostMap.keySet();
                // 计算结算指标为CPC的广告
                Map<Long, Integer> advertConsumeMap = slotAdvertConsumeMap.getOrDefault(slotId, Collections.emptyMap());
                for (Map.Entry<Long, Integer> entry : advertConsumeMap.entrySet()) {
                    if (advertIds.contains(entry.getKey())) {
                        continue;
                    }
                    nhCost = nhCost + entry.getValue();
                }
                if (nhCost <= 0L) {
                    continue;
                }

                SlotData updateSlotData = new SlotData();
                updateSlotData.setSlotId(slotId);
                updateSlotData.setNhCost(nhCost);
                updateSlotData.setCurDate(yesterday);
                slotDataService.updateNhCost(updateSlotData);

                // 媒体数据由广告位数据累加计算
                slotAppDataService.updateAppData(slot.getAppId(), yesterday);
            } catch (Exception e) {
                log.error("计算结算款异常, slotId={}", slot.getId(), e);
            }
        }
        log.info("诺禾日成本结算统计定时任务结束");
    }

    private Map<Long, Map<Long, Integer>> querySlotAdvertConsumeMap(Date date) {
        AdvertSlotDayData param = new AdvertSlotDayData();
        param.setCurDate(date);
        List<AdvertSlotDayData> list = advertSlotDayDataService.selectAdvertSlotDayDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<Long, Map<Long, Integer>> map = new HashMap<>();
        for (AdvertSlotDayData data : list) {
            if (!map.containsKey(data.getSlotId())) {
                map.put(data.getSlotId(), new HashMap<>());
            }
            map.get(data.getSlotId()).put(data.getAdvertId(), data.getConsume());
        }

        return map;
    }
}
