package com.ruoyi.system.job;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import static com.ruoyi.common.constant.BizConstants.IP_BLACKLIST;

/**
 * IP黑名单刷新任务
 *
 * <AUTHOR>
 * @date 2022/09/22
 */
@Slf4j
@Component
public class IpBlacklistRefreshJob {

    @Autowired
    private RedisCache redisCache;

    @Scheduled(cron = "0 0/5 * * * ?")
    public void execute(){
        IP_BLACKLIST.clear();
        String key = EngineRedisKeyFactory.K039.toString();
        CollUtil.addAll(IP_BLACKLIST, redisCache.getCacheSet(key));
    }
}
