package com.ruoyi.system.job;

import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.callback.CallbackProcessorTypeEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.entity.landpage.QwtfLandpageFormRecordEntity;
import com.ruoyi.system.service.callback.ConvCallbackService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.engine.StatService;
import com.ruoyi.system.service.landpage.QwtfLandpageFormRecordService;
import com.ruoyi.system.service.landpage.QwtfWxService;
import com.ruoyi.system.service.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.InnerLogType.LANDPAGE_CLICK;

/**
 * 企微囤粉数据归因任务
 *
 * <AUTHOR>
 * @date 2023/09/26
 */
@Slf4j
@Component
public class QwtfDataTrackJob {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private QwtfLandpageFormRecordService qwtfLandpageFormRecordService;

    @Autowired
    private QwtfWxService qwtfWxService;

    @Autowired
    private StatService statService;

    @Autowired
    private ConvCallbackService convCallbackService;

    @Autowired
    private OrderService orderService;

    @Scheduled(cron = "0 */5 * * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("QwtfDataTrackJob"), 120);
        if (lock == null) {
            return;
        }

        // 企业成员用户列表
        List<String> useridList = whitelistService.list(WhitelistType.QWTF_USERID);
        if (CollectionUtils.isEmpty(useridList)) {
            return;
        }

        // 过滤出新增的unionId
        List<String> unionIds = new ArrayList<>();
        // 过滤出新增的orderId
        List<String> orderIds = new ArrayList<>();
        List<String> newExternalUserIds = new ArrayList<>();
        for (String userid : useridList) {
            try {
                // 查询外部联系人列表
                List<String> externalUserIds = qwtfWxService.getExternalContactList(userid);
                for (String externalUserId : externalUserIds) {
                    if (redisCache.hasKey(EngineRedisKeyFactory.K099.join(externalUserId))) {
                        continue;
                    }
                    newExternalUserIds.add(externalUserId);
                    // 获取外部联系人详情
                    JSONObject detail = qwtfWxService.getExternalContact(externalUserId);
                    if (null == detail) {
                        continue;
                    }
                    // 根据state归因
                    String orderId = getOrderIdByFollowUser(userid, detail.getJSONArray("follow_user"));
                    if (StringUtils.isNotBlank(orderId)) {
                        if (orderId.contains("?bizParams")) {
                            orderId = orderId.substring(0, orderId.indexOf("?bizParams"));
                        } else if (!StrUtil.isNumeric(orderId)) {
                            orderId = ReUtil.get(PatternPool.NUMBERS, orderId, 0);
                        }
                        orderIds.add(orderId);
                    }
                    // 根据unionId归因
                    if (null != detail.getJSONObject("external_contact")) {
                        String unionId = detail.getJSONObject("external_contact").getString("unionid");
                        if (StringUtils.isNotBlank(unionId)) {
                            unionIds.add(unionId);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("企微囤粉数据归因任务异常, userid={}", userid, e);
            }
        }

        // OrderId订单归因
        List<QwtfLandpageFormRecordEntity> records = qwtfLandpageFormRecordService.selectNotFriendByOrderIds(orderIds);
        if (CollectionUtils.isNotEmpty(records)) {
            Set<String> handledOrderIdSet = new HashSet<>();
            // 数据上报
            records.forEach(record -> {
                if (handledOrderIdSet.contains(record.getOrderId())) {
                    return;
                }
                // 转化数据
                convertUpload(record);
                handledOrderIdSet.add(record.getOrderId());
            });
            // 批量更新好友状态
            qwtfLandpageFormRecordService.batchUpdateFriendStatusByOrderIds(new ArrayList<>(handledOrderIdSet));
            log.info("企微囤粉数据归因，新增订单orderIds={}", JSON.toJSONString(orderIds));
        }

        // UnionId订单归因
        records = qwtfLandpageFormRecordService.selectNotFriendByUnionIds(unionIds);
        if (CollectionUtils.isNotEmpty(records)) {
            Set<String> handledUnionIdSet = new HashSet<>();
            // 数据上报
            records.forEach(record -> {
                if (handledUnionIdSet.contains(record.getUnionId())) {
                    return;
                }
                // 转化数据
                convertUpload(record);
                handledUnionIdSet.add(record.getUnionId());
            });
            // 批量更新好友状态
            qwtfLandpageFormRecordService.batchUpdateFriendStatus(new ArrayList<>(handledUnionIdSet));
            log.info("企微囤粉数据归因，新增用户unionIds={}", JSON.toJSONString(unionIds));
        }
        // 更新外部联系人缓存
        Long timestamp = System.currentTimeMillis();
        newExternalUserIds.forEach(externalUserId -> redisCache.setCacheObject(EngineRedisKeyFactory.K099.join(externalUserId), timestamp, 30, TimeUnit.DAYS));
    }

    /**
     * 媒体上报
     *
     * @param record 表单
     */
    private void convertUpload(QwtfLandpageFormRecordEntity record) {
        // 转化数据
        statService.innerLogStatByOrderId(LANDPAGE_CLICK, record.getOrderId());
        // 上报广点通
        Order order = orderService.selectByOrderId(record.getOrderId());
        convCallbackService.directCallback(LANDPAGE_CLICK, null, order, CallbackProcessorTypeEnum.DENGHUO, null);
        convCallbackService.directCallback(LANDPAGE_CLICK, null, order, CallbackProcessorTypeEnum.QQ, MapUtil.of("actionType", "SCANCODE"));
    }

    /**
     * 根据state获取订单号
     *
     * @param userid 企业成员userid
     * @param followUsers 外部联系人的跟进人列表
     * @return 订单号
     */
    private String getOrderIdByFollowUser(String userid, JSONArray followUsers) {
        if (CollectionUtils.isEmpty(followUsers)) {
            return null;
        }
        for (int i = 0; i < followUsers.size(); i++) {
            JSONObject followUser = followUsers.getJSONObject(i);
            if (Objects.equals(userid, followUser.getString("userid"))) {
                return followUser.getString("state");
            }
        }
        return null;
    }
}
