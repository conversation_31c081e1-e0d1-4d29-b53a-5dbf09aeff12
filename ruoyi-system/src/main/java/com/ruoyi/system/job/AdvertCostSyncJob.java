package com.ruoyi.system.job;

import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.system.entity.advert.AdvertCost;
import com.ruoyi.system.service.manager.AdvertCostService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 广告每日结算成本同步定时任务
 *
 * <AUTHOR>
 * @date 2022/2/17 3:02 下午
 */
@Slf4j
@Component
public class AdvertCostSyncJob {

    @Autowired
    private AdvertCostService advertCostService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Scheduled(cron = "0 1 0 * * ?")
    public void execute(){
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("AdvertCostSyncJob"), 10);
        if (lock == null) {
            return;
        }
        log.info("广告结算成本每日同步定时任务开始");
        Long id = 0L;
        Integer pageSize = 100;
        List<AdvertCost> advertCosts;
        while (CollectionUtils.isNotEmpty(advertCosts = advertCostService.selectListByYesterday(id,pageSize))){
            id = advertCosts.get(advertCosts.size() - 1).getId();
            advertCosts.stream().forEach(data ->{
                data.setCurDate(new Date());
            });

            advertCostService.batchInsertOrUpdate(advertCosts);
        }
        log.info("广告结算成本每日同步定时任务结束");
    }
}
