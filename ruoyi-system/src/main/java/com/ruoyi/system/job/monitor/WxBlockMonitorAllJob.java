package com.ruoyi.system.job.monitor;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.domain.DomainStatus;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.service.domain.WxDomainService;
import com.ruoyi.system.service.engine.cache.WxIfrUrlCacheService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 微信拦截监测定时任务
 *
 * <AUTHOR>
 * @date 2023/01/30
 */
@Slf4j
@Component
public class WxBlockMonitorAllJob {

    @Autowired
    private DomainService domainService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private WxDomainService wxDomainService;

    @Autowired
    private WxIfrUrlCacheService wxIfrUrlCacheService;

    /**
     * 全域名监测
     */
    @Scheduled(cron = "0 33 7,10,11,13,15,17,19,22 * * ? ")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("WxBlockMonitorAllJob"), 60);
        if (lock == null) {
            return;
        }

        // 微信域名监测
        Domain param = new Domain();
        param.setDomainStatus(DomainStatus.NORMAL.getStatus());
        param.setWxStatus(DomainStatus.NORMAL.getStatus());
        List<Domain> urlList = domainService.selectDomainList(param);
        if (CollectionUtils.isEmpty(urlList)) {
            return;
        }

        // 检查是否被微信拦截
        for (Domain dto : urlList) {
            if (dto.getDomainType() == 0) {
                // 0是其他，不巡查；域名类型:1.广告位域名,2.活动域名,3.落地页域名
                continue;
            }
            wxDomainService.addCheckQueue(dto.getDomain());
        }
    }

    /**
     * 腾讯云COS主体域名监测
     * 每半小时执行
     */
    @Scheduled(cron = "0 2,32 * * * ?")
    public void ifrUrlMonitorExecute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("WxBlockMonitorAllJob2"), 60);
        if (lock == null) {
            return;
        }

        String url = "https://" + wxIfrUrlCacheService.getWxIfrUrl(null);
        String bucket = ReUtil.getGroup0("(?<=\\/\\/)(.*?)(?=-)", url);
        if (null == bucket) {
            log.error("ifrUrlMonitorExecute getBucket error, url={}", url);
            return;
        }

        String checkUrl = StrUtil.replace(url, bucket, RandomUtil.randomString(6));
        wxDomainService.checkWxBlockAsync(checkUrl, false, isBlock -> {
            if (isBlock) {
                DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), "腾讯云COS主体域名被封\n" + checkUrl);
                wxIfrUrlCacheService.invalidateCache(null);
            }
        });

        log.info("腾讯云COS域名监测, 正常, url={}", url);
    }
}
