package com.ruoyi.system.job;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.utils.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import static com.ruoyi.common.constant.BizConstants.ARTICLE_RET_PAGE_DOMAIN;
import static com.ruoyi.common.constant.BizConstants.ARTICLE_RET_PAGE_DOMAIN_LIST;

/**
 * 文章空白页域名刷新任务
 *
 * <AUTHOR>
 * @date 2024/03/05
 */
@Slf4j
@Component
public class ArticleRetDomainJob {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Scheduled(cron = "0 */5 * * * ?")
    public void execute() {
        int value = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K135.toString())).intValue();
        ARTICLE_RET_PAGE_DOMAIN = ARTICLE_RET_PAGE_DOMAIN_LIST.get(value % ARTICLE_RET_PAGE_DOMAIN_LIST.size());
        log.info("文章空白页域名刷新:" + ARTICLE_RET_PAGE_DOMAIN);
    }
}
