package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserDaySumDataBo;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserFianceStatisticsRecordEntity;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserFianceStatisticsRecordService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.datasource.AdvertDayDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * 广告主离线数据同步定时任务
 *
 * <AUTHOR>
 * @date 2022/07/13
 */
@Slf4j
@Component
public class AdvertiserOfflineDataSyncJob {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private AdvertDayDataService advertDayDataService;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Autowired
    private AdvertiserFianceStatisticsRecordService advertiserFianceStatisticsRecordService;

    @Scheduled(cron = "0 0 0 * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("AdvertiserOfflineDataSyncJob"), 60);
        if (lock == null) {
            return;
        }

        log.info("广告主离线数据同步定时任务开始");

        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
        String dateStr = DateUtil.formatDate(yesterday);

        // 查询所有的白名单广告主
        List<Long> advertiserIds = whitelistService.list(OFFLINE_DATA_ADVERTISER, Long.class).stream().distinct().collect(Collectors.toList());
        // 广告主维度日数据
        Map<String, AdvertiserDaySumDataBo> advertiserDayDataMap = advertDayDataService.groupByDateAndAdvertiserId(yesterday, yesterday, advertiserIds);

        // 创建昨日离线数据
        advertiserIds.forEach(advertiserId -> {
            try {
                AdvertiserFianceStatisticsRecordEntity statisticsRecord = advertiserFianceStatisticsRecordService.select(advertiserId, yesterday);
                AdvertiserDaySumDataBo dayData = advertiserDayDataMap.get(dateStr + "_" + advertiserId);
                if (null != statisticsRecord || null != dayData) {
                    DspAdvertiserConsumeRecordEntity record = new DspAdvertiserConsumeRecordEntity();
                    record.setCurDate(yesterday);
                    record.setAdvertiserId(advertiserId);
                    record.setIsVisible(0);
                    record.setOperatorId(0L);
                    Optional.ofNullable(statisticsRecord).ifPresent(data -> record.setConsumeAmount(data.getConsumeAmount()));
                    Optional.ofNullable(dayData).ifPresent(data -> {
                        record.setBillingClickPv(data.getBillingClickPv());
                        record.setBillingClickUv(data.getBillingClickUv());
                    });
                    dspAdvertiserConsumeRecordService.insert(record);
                }
            } catch (Exception e) {
                log.error("创建昨日离线数据异常, advertiserId={}", advertiserId, e);
            }
        });

        log.info("广告主离线数据同步定时任务结束");
    }
}
