package com.ruoyi.system.job;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.mapper.manager.AdvertOrientationMapper;
import com.ruoyi.system.service.advertiser.AdvertiserBudgetService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.advert.AdvertService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.ruoyi.common.enums.advert.AdvertStatusEnum.INVALID_WITHOUT_ADVERTISER_BUDGET;
import static com.ruoyi.common.enums.advert.AdvertStatusEnum.INVALID_WITHOUT_BALANCE;
import static com.ruoyi.common.enums.advert.AdvertStatusEnum.INVALID_WITHOUT_BUDGET;

/**
 * 每天零点重置广告状态
 */
@Slf4j
@Component
public class AdvertStatusResetJob {

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertiserBudgetService advertiserBudgetService;

    @Scheduled(cron = "0 0 0 * * ?")
    public void advertStatusReset() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("AdvertStatusResetJob"), 10);
        if (lock == null) {
            return;
        }

        List<Long> advertIds = new ArrayList<>();
        Advert param = new Advert();

        // 预算不足重置
        param.setAdvertStatus(INVALID_WITHOUT_BUDGET.getStatus());
        List<Advert> adverts = advertService.selectAdvertList(param);
        if (CollectionUtils.isNotEmpty(adverts)) {
            for (Advert advert : adverts) {
                // 广告日预算
                if (null != advert.getDailyBudget() && advert.getDailyBudget() > 0) {
                    advertIds.add(advert.getId());
                }
            }
        }

        // 广告主余额不足重置
        param.setAdvertStatus(INVALID_WITHOUT_BALANCE.getStatus());
        adverts = advertService.selectAdvertList(param);
        if (CollectionUtils.isNotEmpty(adverts)) {
            for (Advert advert : adverts) {
                // 检查广告主余额
                Integer balance = advertiserBalanceService.selectAdjustTotalAmountByAccountId(advert.getAdvertiserId());
                if (NumberUtils.defaultInt(balance) > 0) {
                    advertIds.add(advert.getId());
                }
            }
        }

        // 广告主日预算不足重置
        param.setAdvertStatus(INVALID_WITHOUT_ADVERTISER_BUDGET.getStatus());
        adverts = advertService.selectAdvertList(param);
        if (CollectionUtils.isNotEmpty(adverts)) {
            for (Advert advert : adverts) {
                Long budget = advertiserBudgetService.selectBudgetByAccountId(advert.getAdvertiserId());
                if (null == budget || budget > 0) {
                    advertIds.add(advert.getId());
                }
            }
        }

        advertService.resetAdvertStatus(advertIds);
        log.info("广告状态重置任务完成，advertIds={}", JSON.toJSONString(advertIds));
    }
}
