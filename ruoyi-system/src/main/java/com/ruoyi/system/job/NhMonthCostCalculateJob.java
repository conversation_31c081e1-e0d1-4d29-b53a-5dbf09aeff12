package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.entity.datashow.AppData;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.datasource.AppDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 每月2号1点计算诺禾结算款
 *
 * <AUTHOR>
 * @date 2022/02/17
 */
@Slf4j
@Component
public class NhMonthCostCalculateJob {

    @Autowired
    private AppDataService appDataService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AppMonthDataService appMonthDataService;

    @Scheduled(cron = "0 0 1 2 * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K016.toString(), 60);
        if (lock == null) {
            return;
        }

        log.info("诺禾月成本结算统计定时任务开始");

        //批量查询所有已有媒体月账单的媒体列表
        Integer pageSize = 30;
        Date lastMonthDate = DateUtils.addMonths(new Date(), -2);
        Integer monthDate = DateUtils.dateTimeMonth(lastMonthDate);
        Long id = 0L;
        Date startDate = DateUtil.beginOfMonth(lastMonthDate);
        Date endDate = DateUtil.endOfMonth(lastMonthDate);
        List<AppMonthDataEntity> appMonthDataEntities;
        while (CollectionUtils.isNotEmpty(appMonthDataEntities = appMonthDataService.selectListByPage(id,pageSize,monthDate))){
            id = appMonthDataEntities.get(appMonthDataEntities.size() - 1).getId();

            List<Long> appIds = appMonthDataEntities.stream().map(AppMonthDataEntity::getAppId).collect(Collectors.toList());
            AppData appData = new AppData();
            appData.setAppIds(appIds);
            appData.setStartDate(startDate);
            appData.setEndDate(endDate);
            List<AppData> appDataList = appDataService.selectAppDataList(appData);
            Map<Long,Long> appCostMap = new HashMap<>();
            appDataList.stream().forEach(data ->{
                Long cost = appCostMap.getOrDefault(data.getAppId(),0L);
                cost += data.getNhCost();
                appCostMap.put(data.getAppId(),cost);
            });

            List<AppMonthDataEntity> appMonthDataEntityList = new ArrayList<>();
            appCostMap.forEach((key,value) ->{
                AppMonthDataEntity entity = new AppMonthDataEntity();
                entity.setNhCost(value);
                entity.setMonthDate(monthDate);
                entity.setAppId(key);
                appMonthDataEntityList.add(entity);
            });
            appMonthDataService.batchUpdateCost(appMonthDataEntityList);
        }

        log.info("诺禾月成本结算统计定时任务结束");
    }
}
