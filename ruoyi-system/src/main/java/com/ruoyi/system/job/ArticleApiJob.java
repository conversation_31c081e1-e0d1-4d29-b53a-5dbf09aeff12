package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.system.service.landpage.article.ArticleApiService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 文章阅读类接口调用统计定时任务
 *
 * <AUTHOR>
 * @date 2024/4/1
 */
@Slf4j
@Component
public class ArticleApiJob {

    @Autowired
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private ArticleApiService articleApiService;

    @Scheduled(cron = "0 0 10 * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("ArticleApiJob"), 10);
        if (lock == null) {
            return;
        }
        if (!SpringEnvironmentUtils.isProd()) {
            return;
        }
        try {
            String yesterday = DateUtil.formatDate(DateUtil.yesterday());
            Long times = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K137.join(yesterday)));
            Long remainTimes = redisAtomicClient.incrBy(EngineRedisKeyFactory.K139.toString(), -times, 365, TimeUnit.DAYS);
            // 获取了极致了API余额
            JSONObject jsonObject = articleApiService.callJzlRemainMoneyApi();
            String jzlRemainMoney = "查询失败";
            if (jsonObject != null && jsonObject.getInteger("code") != null && jsonObject.getInteger("code") == 0) {
                Double remainMoney = jsonObject.getDouble("remain_money");
                if (remainMoney != null) {
                    jzlRemainMoney = String.valueOf(remainMoney);
                }
            }
            DingRobotUtil.sendText("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fec93c9a-7217-4863-9499-f3db4f7d4e04",
                    StrUtil.format("文章阅读量接口调用统计\n\n日期: {}\n调用次数: {}\n剩余次数: {}\n充值链接: {}\n极致了余额: {}",
                            yesterday, NumberUtils.defaultLong(times), remainTimes, "http://actengine.ydns.cn/api/wz/apiRecharge?amount=0", jzlRemainMoney));
            log.info("文章阅读类接口调用统计定时任务, date={}, times={}, remainTimes={}, jzlRemainMoney={}", yesterday, times, remainTimes, jzlRemainMoney);
        } catch (Exception e) {
            log.error("文章阅读类接口调用统计定时任务异常", e);
        }
    }
}
