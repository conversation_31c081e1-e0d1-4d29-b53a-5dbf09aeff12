package com.ruoyi.system.job;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.SmsStatusEnum;
import com.ruoyi.system.bo.sms.LiuziSmsQwTaskBo;
import com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity;
import com.ruoyi.system.manager.liuzi.AsyncAlipayLandpageFormDataManager;
import com.ruoyi.system.service.sms.LiuziSmsSendRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 留资短信降级发企微定时任务
 *
 * <AUTHOR>
 * @date 2023/06/07
 */
@Slf4j
@Component
public class LiuziSmsQwTaskJob {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AsyncAlipayLandpageFormDataManager asyncAlipayLandpageFormDataManager;

    @Autowired
    private LiuziSmsSendRecordService liuziSmsSendRecordService;

    @Scheduled(cron = "0 0/10 * * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("LiuziSmsQwTaskJob"), 3);
        if (lock == null) {
            return;
        }

        // 获取任务列表
        String key = EngineRedisKeyFactory.K071.toString();
        Set<String> values = redisCache.getCacheSet(key);
        if (CollectionUtils.isEmpty(values)) {
            return;
        }

        log.info("LiuziSmsQwTaskJob execute, count={}", values.size());

        // 查询短信是否已发送，发送失败或者超过5小时则发送企微
        Date now = new Date();
        for (String value : values) {
            LiuziSmsQwTaskBo taskBo = JSON.parseObject(value, LiuziSmsQwTaskBo.class);
            if (null == taskBo) {
                continue;
            }
            List<LiuziSmsSendRecordEntity> smsRecords = liuziSmsSendRecordService.selectListByLiuziRecordId(taskBo.getRecordId());

            // 查询短信是否已发送成功
            if (isSmsSuccess(smsRecords)) {
                redisCache.deleteCacheSet(key, value);
                continue;
            }

            // 查询短信是否失败或者时间是否超过5小时
            if (!isSmsFail(smsRecords) && DateUtil.between(taskBo.getCommitTime(), now, DateUnit.MINUTE) < 300) {
                continue;
            }

            // 企微加好友
            asyncAlipayLandpageFormDataManager.addUserByQw(taskBo.getPhone());
            redisCache.deleteCacheSet(key, value);

            log.info("LiuziSmsQwTaskJob execute, addUserByQw, task={}", value);
        }
    }

    /**
     * 短信是否发送成功
     *
     * @param smsRecords 短信记录列表
     * @return true.是,false.否
     */
    private boolean isSmsSuccess(List<LiuziSmsSendRecordEntity> smsRecords) {
        if (CollectionUtils.isEmpty(smsRecords)) {
            return false;
        }
        for (LiuziSmsSendRecordEntity smsRecord : smsRecords) {
            if (SmsStatusEnum.isSuccess(smsRecord.getResult())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 短信是否发送成功
     *
     * @param smsRecords 短信记录列表
     * @return true.是,false.否
     */
    private boolean isSmsFail(List<LiuziSmsSendRecordEntity> smsRecords) {
        if (CollectionUtils.isEmpty(smsRecords)) {
            return false;
        }
        for (LiuziSmsSendRecordEntity smsRecord : smsRecords) {
            if (!SmsStatusEnum.isFail(smsRecord.getResult())) {
                return false;
            }
        }
        return true;
    }
}
