package com.ruoyi.system.job.monitor;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 域名到期时间监控定时任务
 *
 * <AUTHOR>
 * @date 2022/05/23
 */
@Slf4j
@Component
public class DomainExpireMonitorJob {

    private static final List<Long> THRESHOLD = Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 15L, 30L);

    @Autowired
    private DomainService domainService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Scheduled(cron = "0 0 10 * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("DomainExpireMonitorJob"), 60);
        if (lock == null) {
            return;
        }

        log.info("域名到期时间监控定时任务开始");

        Date today = DateUtil.beginOfDay(new Date());
        List<Domain> domainList = domainService.selectDomainList(new Domain());
        for (Domain domain : domainList) {
            try {
                // 域名到期时间
                Optional.ofNullable(domain.getDomainExpire()).ifPresent(domainExpire -> {
                    long betweenDay = DateUtil.between(domainExpire, today, DateUnit.DAY);
                    if (domainExpire.after(today) && THRESHOLD.contains(betweenDay)) {
                        String sbr = "域名到期提醒\n" +
                                "\n域名: " + domain.getDomain() +
                                "\n到期时间: " + DateUtil.formatDate(domainExpire);
                        if (StringUtils.isNotBlank(domain.getRemark())) {
                            sbr += "\n备注: " + domain.getRemark();
                        }
                        DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), sbr);
                    }
                });

                // 证书到期时间
                if (Objects.equals(domain.getHttpsEnable(), 1)) {
                    Date certExpireTime = domainService.getCertificateExpireTime(domain.getDomain());
                    Optional.ofNullable(certExpireTime).ifPresent(expireTime -> {
                        long betweenDay = DateUtil.between(expireTime, today, DateUnit.DAY);
                        if (expireTime.after(today) && THRESHOLD.contains(betweenDay)) {
                            String sbr = "域名证书到期提醒\n" +
                                    "\n域名: " + domain.getDomain() +
                                    "\n证书到期时间: " + DateUtil.formatDate(expireTime);
                            if (StringUtils.isNotBlank(domain.getRemark())) {
                                sbr += "\n备注: " + domain.getRemark();
                            }
                            DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), sbr);
                        }
                    });
                }
            } catch (Exception e) {
                log.error("域名到期时间监控定时任务异常, domain={}", domain.getDomain(), e);
            }
        }

        log.info("域名到期时间监控定时任务完成");
    }
}
