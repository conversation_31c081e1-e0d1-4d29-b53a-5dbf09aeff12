package com.ruoyi.system.job.monitor;

import cn.hutool.http.HttpUtil;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.common.EnableStatusEnum;
import com.ruoyi.common.enums.domain.DomainStatus;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.ruoyi.common.enums.domain.DomainStatus.DISABLE;
import static com.ruoyi.common.enums.domain.DomainStatus.NORMAL;
import static com.ruoyi.common.enums.domain.DomainType.isOtherDomain;

/**
 * 域名状态检查定时任务
 *
 * <AUTHOR>
 * @date 2022/01/17
 */
@Slf4j
@Component
public class DomainStatusJob {

    @Autowired
    private DomainService domainService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Scheduled(cron = "0 0 */1 * * ?")
    public void execute() {
        if (!SpringEnvironmentUtils.isProd()) {
            return;
        }

        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("DomainStatusJob"), 10);
        if (lock == null) {
            return;
        }

        log.info("域名状态检查定时任务开始");

        StringBuilder sb = new StringBuilder();
        sb.append("域名状态检查");
        int failed = 0;

        Domain param = new Domain();
        param.setDomainStatus(NORMAL.getStatus());
        List<Domain> domainList = domainService.selectDomainList(param);
        for (Domain domain : domainList) {
            // 其他域名不进行监控
            if (isOtherDomain(domain.getDomainType())) {
                continue;
            }

            StringBuilder url = new StringBuilder();
            if (EnableStatusEnum.isEnable(domain.getHttpsEnable())) {
                url.append("https://");
            } else {
                url.append("http://");
            }
            url.append(domain.getDomain());
            if (Objects.equals(domain.getDomainType(), 1)) {
                url.append("/st/open?appKey=7da724dcdc0c4314a21df66a759dd265&sid=852981");
            } else if (Objects.equals(domain.getDomainType(), 2)) {
                url.append("/act/turntable/tqdFY?appKey=7da724dcdc0c4314a21df66a759dd265&slotId=852989&deviceId=c9c99a78-fd23-4323-9ebe-8231760e7a5a&srid=b84e11f3b8d84e03a0ad9f32717bdaa9");
            } else if (Objects.equals(domain.getDomainType(), 3)) {
                url.append("/land/28RU0WWJ");
            } else {
                continue;
            }

            try {
                int status = HttpUtil.createGet(url.toString()).timeout(5000).execute().getStatus();
                if (status >= 200 && status <= 302) {
                    if (!Objects.equals(domain.getDomainStatus(), NORMAL.getStatus())) {
                        domainService.updateDomainStatus(domain.getDomain(), NORMAL.getStatus());
                    }
                } else {
                    if (!Objects.equals(domain.getDomainStatus(), DISABLE.getStatus())) {
                        domainService.updateDomainStatus(domain.getDomain(), DISABLE.getStatus());
                    }
                    failed++;
                    sb.append("\n\n异常域名: ").append(domain.getDomain())
                            .append("\n状态: ").append(status)
                            .append("\n检测链接: ").append(url.toString());
                }
            } catch (Exception e) {
                log.error("域名检测异常, domain={}", domain.getDomain(), e);
            }
        }

        if (failed > 0) {
            DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), sb.toString());
        }

        log.info("域名状态检查定时任务完成，异常域名数量:{}", failed);
    }
}
