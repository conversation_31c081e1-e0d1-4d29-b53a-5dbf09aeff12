package com.ruoyi.system.job;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.system.entity.advertiser.AdvertiserOperateTimerEntity;
import com.ruoyi.system.service.advertiser.AdvertiserOperateTimerService;
import com.ruoyi.system.service.advert.AdvertService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 广告主操作定时任务
 */
@Slf4j
@Component
public class AdvertiserOperateJob {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertiserOperateTimerService advertiserOperateTimerService;

    @Autowired
    private AdvertService advertService;

    @Scheduled(cron = "0,30 * * * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("AdvertiserOperateJob"), 3);
        if (lock == null) {
            return;
        }

        // 查询符合需要执行的定时任务
        List<AdvertiserOperateTimerEntity> timerList = advertiserOperateTimerService.selectReadyTimer();
        if (CollectionUtils.isEmpty(timerList)) {
            return;
        }
        log.info("广告主批量操作定时任务执行, size: {}, timer: {}", timerList.size(), JSON.toJSONString(timerList));
        // 执行定时任务
        for (AdvertiserOperateTimerEntity timer : timerList) {
            try {
                int result = advertiserOperateTimerService.execTimer(timer);
                advertService.sendRefreshCacheMsgByAdvertiserId(timer.getAdvertiserId());
                if (result < 1) {
                    log.error("广告主批量操作定时任务执行失败, timer={}", JSON.toJSONString(timer));
                }
            } catch (Exception e) {
                log.error("广告主批量操作定时任务执行异常, timer={}", JSON.toJSONString(timer), e);
            }
        }
    }
}
