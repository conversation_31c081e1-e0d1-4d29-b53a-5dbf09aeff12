package com.ruoyi.system.job.monitor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.domain.adengine.SlotCacheDto;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.service.domain.DomainReplaceService;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.service.domain.WxDomainService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import static com.ruoyi.common.enums.domain.DomainStatus.isNormal;
import static com.ruoyi.common.enums.domain.DomainType.SLOT_DOMAIN;

/**
 * 微信拦截监测定时任务
 *
 * <AUTHOR>
 * @date 2023/01/30
 */
@Slf4j
@Component
public class WxBlockMonitorJob {

    @Autowired
    private DomainService domainService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private WxDomainService wxDomainService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private DomainReplaceService domainReplaceService;

    @Scheduled(cron = "0 4/6 * * * ? ")
    public void activityCheck() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("WxBlockMonitorJob-activityCheck"), 60);
        if (lock == null) {
            return;
        }

        // 活动域名微信监测
        Set<String> activityUrls = new HashSet<>();
        activityUrls.addAll(SetUtils.emptyIfNull(redisCache.getCacheSet(EngineRedisKeyFactory.K050.join(DateUtil.thisHour(true)))));
        activityUrls.addAll(SetUtils.emptyIfNull(redisCache.getCacheSet(EngineRedisKeyFactory.K050.join(DateUtil.hour(DateUtil.offsetHour(new Date(), -1), true)))));
        activityUrls.forEach(activityUrl -> {
            String domain = UrlUtils.extractDomain(activityUrl);
            Domain domainDO = domainService.selectDomain(domain);
            if (null != domainDO && !isNormal(domainDO.getWxStatus())) {
                return;
            }
            // 检查是否被微信拦截
            wxDomainService.checkWxBlockAsync(activityUrl, true, null);
        });
        log.info("活动域名定时检测，size={}", activityUrls.size());
    }

    @Scheduled(cron = "0 4/15 7-23 * * ? ")
    public void slotCheck() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("WxBlockMonitorJob-slotCheck"), 60);
        if (lock == null) {
            return;
        }

        // 广告位域名监测
        Set<String> slotIds = new HashSet<>();
        slotIds.addAll(SetUtils.emptyIfNull(redisCache.getCacheSet(EngineRedisKeyFactory.K052.join(DateUtil.today()))));
        slotIds.addAll(SetUtils.emptyIfNull(redisCache.getCacheSet(EngineRedisKeyFactory.K052.join(DateUtil.formatDate(DateUtil.yesterday())))));
        slotIds.forEach(slotId -> {
            // 查询广告位使用的域名
            SlotCacheDto slot = slotCacheService.getSlotCache(Long.valueOf(slotId));
            if (null == slot) {
                return;
            }
            String slotUrl = slot.getSlotUrl();
            String domain = UrlUtils.extractDomain(slotUrl);
            if (null != slot.getDomainConfig() && StringUtils.isNotBlank(slot.getDomainConfig().getString(SLOT_DOMAIN.getKey()))) {
                domain = slot.getDomainConfig().getString(SLOT_DOMAIN.getKey());
                slotUrl = domainReplaceService.doReplaceDomain(slot.getSlotUrl(), domain);
            }
            // 检查域名状态，避免重复调用接口
            Domain domainDO = domainService.selectDomain(domain);
            if (null != domainDO && !isNormal(domainDO.getWxStatus())) {
//                    DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(), slotUrl + " 被微信拦截，请及时更换广告位域名");
                return;
            }
            // 检查是否被微信拦截
            wxDomainService.checkWxBlockAsync(slotUrl, true, null);
        });
        log.info("广告位域名定时检测，size={}, set={}", slotIds.size(), JSON.toJSONString(slotIds));
    }
}
