package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.datasource.SlotAppDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.manager.SlotConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.slot.SlotDataVisibleEnum.VISIBLE;

/**
 * 媒体数据同步定时任务
 *
 * <AUTHOR>
 * @date 2022/03/08
 */
@Slf4j
@Component
public class AppDataSyncJob {

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private SlotAppDataService slotAppDataService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private SlotConfigService slotConfigService;

    @Scheduled(cron = "0 0 1 * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("AppDataSyncJob"), 10);
        if (lock == null) {
            return;
        }

        log.info("媒体数据同步定时任务开始");

        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());

        // 初始化昨日未产生数据的白名单广告位
        Set<Long> slotIds = new HashSet<>();
        slotIds.addAll(whitelistService.list(WhitelistType.ADJUST_DATA_SLOT, Long.class));
        slotIds.addAll(selectAdjustSlot());
        slotIds.forEach(slotId -> insertSlotDataIfNotExist(slotId, yesterday));

        // 同步媒体数据
        SlotData param = new SlotData();
        param.setCurDate(yesterday);
        param.setIsVisible(VISIBLE.getStatus());
        List<SlotData> slotDataList = slotDataService.selectSlotDataList(param);
        if (CollectionUtils.isNotEmpty(slotDataList)) {
            List<Long> appIds = slotDataList.stream().map(SlotData::getAppId).collect(Collectors.toList());
            appIds.forEach(appId -> {
                try {
                    slotAppDataService.updateAppData(appId, yesterday);
                } catch (Exception e) {
                    log.error("更新媒体数据异常, appId={}", appId, e);
                }
            });
        }

        log.info("媒体数据同步定时任务结束");
    }

    /**
     * 新增广告位数据
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     */
    private void insertSlotDataIfNotExist(Long slotId, Date curDate) {
        SlotData slotData = slotDataService.selectBySlotIdAndDate(slotId, curDate);
        if (null == slotData) {
            Slot slot = slotMapper.selectSlotById(slotId);
            if (null == slot) {
                return;
            }

            slotData = new SlotData();
            slotData.setCurDate(curDate);
            slotData.setAccountId(slot.getAccountId());
            slotData.setAppId(slot.getAppId());
            slotData.setSlotId(slotId);
            slotDataService.insertSlotData(slotData);
        }
    }

    /**
     * 查询开启数据校准的广告位ID列表
     */
    private List<Long> selectAdjustSlot() {
        SlotConfig param = new SlotConfig();
        param.setSwitchConfig("adjust");
        List<SlotConfig> slotConfigs = slotConfigService.selectSlotConfigList(param);
        return slotConfigs.stream().filter(config -> {
            SlotSwitchConfig switchConfig = JSON.parseObject(config.getSwitchConfig(), SlotSwitchConfig.class);
            return null != switchConfig && SwitchStatusEnum.isSwitchOn(switchConfig.getAdjust());
        }).map(SlotConfig::getSlotId).collect(Collectors.toList());
    }
}
