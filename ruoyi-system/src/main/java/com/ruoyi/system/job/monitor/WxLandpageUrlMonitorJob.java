package com.ruoyi.system.job.monitor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.advert.LandpageTypeEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.manager.Domain;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.domain.DomainService;
import com.ruoyi.system.service.domain.WxDomainService;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.ruoyi.common.enums.domain.DomainStatus.isNormal;

/**
 * 广告落地页微信拦截监测定时任务
 *
 * <AUTHOR>
 * @date 2023/02/21
 */
@Slf4j
@Component
public class WxLandpageUrlMonitorJob {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private WxDomainService wxDomainService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DomainService domainService;

    @Scheduled(cron = "0 4/5 * * * ? ")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("WxLandpageUrlMonitorJob"), 60);
        if (lock == null) {
            return;
        }

        // 获取巡查的落地页
        String dateStr = DateUtil.formatDate(new Date());
        int curQuarter = DateUtil.thisHour(true) * 4 + DateUtil.thisMinute() / 15;
        String key = EngineRedisKeyFactory.K053.join(curQuarter);
        Map<String, String> advertUrlMap = redisCache.getCacheMap(key);
        if (MapUtil.isEmpty(advertUrlMap)) {
            return;
        }

        // 自动替换落地页的返利渠道
        List<String> channels = whitelistService.list(WhitelistType.FANLI_CHANNEL);

        // 遍历落地页
        advertUrlMap.forEach((advertId, landpageUrl) -> {
            // 检查域名状态，避免重复调用接口
            Domain domainDO = domainService.selectDomain(UrlUtils.extractDomain(landpageUrl));
            if (null != domainDO && !isNormal(domainDO.getWxStatus())) {
                redisCache.delCacheMapValue(key, advertId);
                return;
            }

            wxDomainService.checkWxBlockAsync(landpageUrl, false, isBlock -> {
                if (!isBlock) {
                    return;
                }
                redisCache.delCacheMapValue(key, advertId);

                for (String channel : channels) {
                    if (StrUtil.contains(landpageUrl, channel)) {
                        String newLandpageUrl = getNewLandpageUrl(channel);
                        if (StringUtils.isBlank(newLandpageUrl)) {
                            return;
                        }
                        // 更新广告的落地页
                        Advert advert = advertService.selectAdvertById(Long.valueOf(advertId));
                        if (StrUtil.equals(advert.getLandpageUrl(), landpageUrl) && !StrUtil.equals(landpageUrl, newLandpageUrl)) {
                            advertService.updateLandPageUrl(advert.getId(), newLandpageUrl);
                            refreshCacheService.sendRefreshAdvertCacheMsg(advert.getId());
                        }
                        // 更新配置的落地页
                        List<AdvertOrientation> orients = advertOrientationService.selectListByAdvertId(advert.getId());
                        if (CollectionUtils.isEmpty(orients)) {
                            return;
                        }
                        orients.forEach(orient -> {
                            if (!LandpageTypeEnum.isCustom(orient.getLandpageType())) {
                                return;
                            }
                            if (!StrUtil.equals(orient.getLandpageUrl(), landpageUrl)) {
                                return;
                            }
                            if (!StrUtil.equals(landpageUrl, newLandpageUrl)) {
                                // 替换落地页
                                advertOrientationService.updateLandpageUrl(orient.getId(), newLandpageUrl);
                                // 刷新缓存
                                refreshCacheService.sendRefreshAdvertCacheMsg(orient.getAdvertId());
                                // 发送钉钉提醒
                                DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(),
                                        "返利落地页替换\n" +
                                                "\n广告ID: " + orient.getAdvertId() +
                                                "\n返利渠道: " + channel +
                                                "\n原链接: " + landpageUrl +
                                                "\n新链接: " + newLandpageUrl);
                            }
                        });
                        return;
                    }
                }
                if (null == redisAtomicClient.getLock(CrmRedisKeyFactory.K029.join(dateStr, advertId, Md5Utils.hash(landpageUrl)), 86400)) {
                    return;
                }
                String advertName = advertService.selectAdvertNameById(Long.valueOf(advertId));
                DingRobotUtil.sendText(DingWebhookConfig.getDomainAlert(),
                        "广告ID: " + advertId + "\n广告名称: " +  advertName +  "\n落地页: " + landpageUrl + "\n落地页被微信拦截，请及时更换落地页域名");
            });
        });
    }

    /**
     * 获取返利最新的落地页
     *
     * @param channel 返利渠道
     * @return 落地页
     */
    private String getNewLandpageUrl(String channel) {
        try {
            String resp = HttpUtil.get("http://qilin.shnuwang.cn/qilin/mark/channelLanding?c=" + channel);
            log.info("获取返利落地页, channel={}, resp={}", channel, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null != result && Objects.equals(result.getInteger("status"), 1) && null != result.getJSONObject("data")) {
                return result.getJSONObject("data").getString("landing_url");
            }
        } catch (Exception e) {
            log.error("获取返利落地页异常, channel={}", channel, e);
        }
        return null;
    }
}
