package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.system.mapper.common.InnerLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 每天创建后面一周的业务日志表
 */
@Slf4j
@Component
public class CreateInnerLogTableJob {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private InnerLogMapper innerLogMapper;

    @Scheduled(cron = "0 0 3 * * ?")
    public void advertStatusReset() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("CreateInnerLogTableJob"), 60);
        if (lock == null) {
            return;
        }

        Date today = new Date();
        for (int i = 1; i <= 7; i++) {
            try {
                String date = DateUtil.format(DateUtil.offsetDay(today, i), "yyyyMMdd");
                innerLogMapper.createTable(date);
            } catch (Exception e) {
                log.error("新增业务日志表异常", e);
            }
        }

        try {
            innerLogMapper.dropTable(DateUtil.format(DateUtil.offsetDay(today, -32), "yyyyMMdd"));
        } catch (Exception e) {
            log.error("删除业务日志表异常", e);
        }
    }
}
