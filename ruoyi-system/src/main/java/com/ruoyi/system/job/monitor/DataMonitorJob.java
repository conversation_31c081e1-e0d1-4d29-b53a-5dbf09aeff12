package com.ruoyi.system.job.monitor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.advert.AdvertHourWarningDataBo;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.datashow.SlotActivityHourData;
import com.ruoyi.system.entity.datashow.SlotHourData;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slot.SlotConfig;
import com.ruoyi.system.mapper.datashow.AdvertHourDataMapper;
import com.ruoyi.system.mapper.datashow.SlotActivityHourDataMapper;
import com.ruoyi.system.mapper.datashow.SlotHourDataMapper;
import com.ruoyi.system.mapper.manager.AppMapper;
import com.ruoyi.system.mapper.manager.SlotConfigMapper;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.domain.DomainReplaceService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.ruoyi.common.enums.common.WhitelistType.MONITOR_HOUR_DATA_SLOT;

/**
 * 数据监控定时任务
 *
 * <AUTHOR>
 * @date 2021/9/30
 */
@Slf4j
@Component
public class DataMonitorJob {

    private static final String TEMPLATE_SLOT_UV_DECREASE = "广告位：**{}**\n\n所属媒体：**{}**\n\n广告位链接：[{}]({})\n\n数据\n\n{}:00 昨日广告位访问uv：{}\n\n{}:00 今日广告位访问uv：{}\n\n" +
            "对比昨日同时段，流量下降**{}%**\n\n[点击不再提醒](https://api.actlist.cn/manual/slotDataMonitorSilent?slotId={})";

    @Autowired
    private SlotActivityHourDataMapper slotActivityHourDataMapper;

    @Autowired
    private SlotHourDataMapper slotHourDataMapper;

    @Autowired
    private AdvertHourDataMapper advertHourDataMapper;

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private SlotConfigMapper slotConfigMapper;

    @Autowired
    private AppMapper appMapper;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private DomainReplaceService domainReplaceService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private RedisCache redisCache;


    @Scheduled(cron = "0 59 */1 * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("DataMonitorJob"), 10);
        if (lock == null) {
            return;
        }

        Date date = DateUtil.beginOfDay(new Date());
        int hour = DateUtil.thisHour(true);

        if (hour < 1) {
            return;
        }

        // 查询广告位-广告位时段数据
        Map<Long, Integer> curSlotHourData = querySlotDataMap(date, hour);
        Map<Long, Integer> ydaySlotHourData = querySlotDataMap(DateUtil.offsetDay(date, -1), hour);
        // 查询广告位-活动时段数据
        Map<Long, Integer> curActivityHourData = queryActivityDataMap(date, hour);
        Map<Long, Integer> lastActivityHourData = queryActivityDataMap(date, hour - 1);
        // 查询广告位-券请求时段数据
        Map<Long, Integer> curAdRequestHourData = queryAdRequestDataMap(date, hour);
        // 查询广告位-发券时段数据
        Map<Long, Integer> curAdLaunchHourData = queryAdLaunchDataMap(date, hour);


        // 广告位时段数据告警，对比昨日同时段
        if (MapUtils.isNotEmpty(ydaySlotHourData)) {
            for (Map.Entry<Long, Integer> entry : ydaySlotHourData.entrySet()) {
                try {
                    Long slotId = entry.getKey();
                    Integer ydayUv = entry.getValue();
                    Integer curUv = curSlotHourData.getOrDefault(slotId, 0);

                    // 广告位白名单判断
                    if (!whitelistService.contains(MONITOR_HOUR_DATA_SLOT, slotId)) {
                        continue;
                    }

                    // 沉默判断
                    if (redisCache.hasKey(CrmRedisKeyFactory.K003.join(DateUtil.today(), slotId))) {
                        continue;
                    }

                    // 告警
                    if (ydayUv >= 100 && ((ydayUv - curUv) / (double) ydayUv) >= 0.5 ) {
                        Slot slot = slotMapper.selectSlotById(slotId);
                        SlotConfig slotConfig = slotConfigMapper.selectBySlotId(slotId);
                        App app = appMapper.selectAppById(slot.getAppId());

                        // 广告位域名替换
                        String slotUrl = slot.getSlotUrl();
                        if (StringUtils.isNotBlank(slotConfig.getDomainConfig())) {
                            JSONObject domainConfig = JSON.parseObject(slotConfig.getDomainConfig());
                            if (null != domainConfig) {
                                slotUrl = domainReplaceService.doReplaceDomain(slot.getSlotUrl(), domainConfig.getString("slotDomain"));
                            }
                        }

                        Integer decrease = (ydayUv - curUv) * 100 / ydayUv;
                        String title = StrUtil.format("广告位{}流量下降{}%", slotId, decrease);
                        String content = StrUtil.format(TEMPLATE_SLOT_UV_DECREASE, slotId, app.getAppName(), slotUrl, slotUrl,
                                hour, ydayUv, hour, curUv, decrease, slotId);
                        DingRobotUtil.sendMarkdown(DingWebhookConfig.getBizAlert(), title, content, null, false);
                    }
                } catch (Exception e) {
                    log.error("数据监控定时任务异常, slotId={}", entry.getKey(), e);
                }
            }
        }

        // 活动时段数据告警，对比上一时段
        if (MapUtils.isNotEmpty(lastActivityHourData)) {
            for (Map.Entry<Long, Integer> entry : lastActivityHourData.entrySet()) {
                try {
                    Long slotId = entry.getKey();
                    Integer lastUv = entry.getValue();
                    Integer curUv = curActivityHourData.getOrDefault(slotId, 0);
                    // 告警
                    if (lastUv >= 100 && curUv == 0) {
                        Slot slot = slotMapper.selectSlotById(slotId);
                        App app = appMapper.selectAppById(slot.getAppId());
                        SlotConfig slotConfig = slotConfigMapper.selectBySlotId(slotId);

                        // 广告位域名替换
                        String slotUrl = slot.getSlotUrl();
                        if (StringUtils.isNotBlank(slotConfig.getDomainConfig())) {
                            JSONObject domainConfig = JSON.parseObject(slotConfig.getDomainConfig());
                            if (null != domainConfig) {
                                slotUrl = domainReplaceService.doReplaceDomain(slot.getSlotUrl(), domainConfig.getString("slotDomain"));
                            }
                        }

                        StringBuilder sb = new StringBuilder();
                        sb.append("广告位：").append(slotId).append("\n")
                                .append("所属媒体：").append(app.getAppName()).append("\n")
                                .append("广告位链接：").append(slotUrl).append("\n")
                                .append("\n数据\n")
                                .append(hour - 1).append(":00 活动访问uv：").append(lastUv).append("\n")
                                .append(hour).append(":00 活动访问uv：").append(curUv).append("\n");
                        DingRobotUtil.sendText(DingWebhookConfig.getDataAlert(), sb.toString(), null, true);
                    }
                } catch (Exception e) {
                    log.error("数据监控定时任务异常, slotId={}", entry.getKey(), e);
                }
            }
        }

        // 发券成功率时段数据告警
        if (MapUtils.isNotEmpty(curAdRequestHourData)) {
            for (Map.Entry<Long, Integer> entry : curAdRequestHourData.entrySet()) {
                try {
                    Long slotId = entry.getKey();
                    Integer adRequest = entry.getValue();
                    Integer adLaunch = curAdLaunchHourData.getOrDefault(slotId, 0);
                    // 告警
                    if (adRequest >= 50 && adRequest * 0.8 > adLaunch) {
                        Slot slot = slotMapper.selectSlotById(slotId);
                        App app = appMapper.selectAppById(slot.getAppId());
                        SlotConfig slotConfig = slotConfigMapper.selectBySlotId(slotId);

                        // 广告位域名替换
                        String slotUrl = slot.getSlotUrl();
                        if (StringUtils.isNotBlank(slotConfig.getDomainConfig())) {
                            JSONObject domainConfig = JSON.parseObject(slotConfig.getDomainConfig());
                            if (null != domainConfig) {
                                slotUrl = domainReplaceService.doReplaceDomain(slot.getSlotUrl(), domainConfig.getString("slotDomain"));
                            }
                        }

                        StringBuilder sb = new StringBuilder();
                        sb.append("广告位：").append(slotId).append("\n")
                                .append("所属媒体：").append(app.getAppName()).append("\n")
                                .append("广告位链接：").append(slotUrl).append("\n")
                                .append("\n数据\n")
                                .append(String.format("%02d:00 发券成功率:%d%%", hour, (int) (adLaunch * 100.0 / adRequest))).append("\n");
                        DingRobotUtil.sendText(DingWebhookConfig.getBizAlert(), sb.toString());
                    }
                } catch (Exception e) {
                    log.error("数据监控定时任务异常, slotId={}", entry.getKey(), e);
                }
            }
        }

        // 落地页到达率时段告警
        try {
            List<AdvertHourWarningDataBo> list = advertHourDataMapper.selectLpExposureWarningList();
            for (AdvertHourWarningDataBo data : list) {
                StringBuilder sb = new StringBuilder();
                sb.append("广告ID：").append(data.getAdvertId()).append("\n")
                        .append("广告名称：").append(data.getAdvertName()).append("\n")
                        .append("落地页链接：").append(data.getLandpageUrl()).append("\n")
                        .append("\n数据\n")
                        .append(String.format("%02d:00 落地页到达率:%d%%", hour, (int) (data.getLpExposurePv() * 100.0 / data.getClickPv()))).append("\n");
                DingRobotUtil.sendText(DingWebhookConfig.getBizAlert(), sb.toString());
            }
        } catch (Exception e) {
            log.error("落地页到达率时段告警异常", e);
        }

        // CVR时段告警
        try {
            List<AdvertHourWarningDataBo> list = advertHourDataMapper.selectCvrWarningList();
            for (AdvertHourWarningDataBo data : list) {
                StringBuilder sb = new StringBuilder();
                sb.append("广告ID：").append(data.getAdvertId()).append("\n")
                        .append("广告名称：").append(data.getAdvertName()).append("\n")
                        .append("落地页链接：").append(data.getLandpageUrl()).append("\n")
                        .append("\n数据\n")
                        .append(String.format("昨日%02d:00 CVR:%.2f%%", hour, data.getYCvr() * 100)).append("\n")
                        .append(String.format("今日%02d:00 CVR:%.2f%%", hour, data.getTCvr() * 100)).append("\n")
                        .append(String.format("CVR同时段下降%d%%", (int) ((data.getYCvr() - data.getTCvr()) * 100.0 / data.getYCvr())));
                DingRobotUtil.sendText(DingWebhookConfig.getBizAlert(), sb.toString());
            }
        } catch (Exception e) {
            log.error("CVR时段告警异常", e);
        }
    }

    /**
     * 查询广告位的活动访问uv时段数据
     */
    private Map<Long, Integer> queryActivityDataMap(Date date, int hour) {
        SlotActivityHourData param = new SlotActivityHourData();
        param.setCurDate(date);
        param.setCurHour(hour);
        List<SlotActivityHourData> list = slotActivityHourDataMapper.selectSlotActivityHourDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<Long, Integer> map = new HashMap<>();
        for (SlotActivityHourData data : list) {
            Integer uv = map.getOrDefault(data.getSlotId(), 0) + data.getActivityRequestUv();
            map.put(data.getSlotId(), uv);
        }
        return map;
    }

    /**
     * 查询广告位的券请求时段数据
     */
    private Map<Long, Integer> queryAdRequestDataMap(Date date, int hour) {
        SlotActivityHourData param = new SlotActivityHourData();
        param.setCurDate(date);
        param.setCurHour(hour);
        List<SlotActivityHourData> list = slotActivityHourDataMapper.selectSlotActivityHourDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<Long, Integer> map = new HashMap<>();
        for (SlotActivityHourData data : list) {
            Integer uv = map.getOrDefault(data.getSlotId(), 0) + data.getAdRequest();
            map.put(data.getSlotId(), uv);
        }
        return map;
    }

    /**
     * 查询广告位的发券时段数据
     */
    private Map<Long, Integer> queryAdLaunchDataMap(Date date, int hour) {
        SlotActivityHourData param = new SlotActivityHourData();
        param.setCurDate(date);
        param.setCurHour(hour);
        List<SlotActivityHourData> list = slotActivityHourDataMapper.selectSlotActivityHourDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<Long, Integer> map = new HashMap<>();
        for (SlotActivityHourData data : list) {
            Integer uv = map.getOrDefault(data.getSlotId(), 0) + data.getAdLaunch();
            map.put(data.getSlotId(), uv);
        }
        return map;
    }

    /**
     * 查询广告位的广告位访问uv数据
     */
    private Map<Long, Integer> querySlotDataMap(Date date, int hour) {
        SlotHourData param = new SlotHourData();
        param.setCurDate(date);
        param.setCurHour(hour);
        List<SlotHourData> list = slotHourDataMapper.selectSlotHourDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<Long, Integer> map = new HashMap<>();
        for (SlotHourData data : list) {
            map.put(data.getSlotId(), data.getSlotRequestUv());
        }
        return map;
    }
}
