package com.ruoyi.system.job;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.datasource.FcLinkDayDataService;
import com.ruoyi.system.service.fc.FcLinkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.List;

/**
 * 丰巢链接数据同步任务
 */
@Component
@Slf4j
public class FcLinkSyncJob {

    @Autowired
    private FcLinkService fcLinkService;

    @Autowired
    private FcLinkDayDataService fcLinkDayDataService;

    /**
     * 定时同步丰巢链接缓存数据到数据库
     * 每小时整点执行
     * - 每小时同步当天数据
     * - 凌晨1点时额外同步前一天的数据，并清理前一天的Redis缓存
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void syncFcLinkData() {
        // 获取当前小时
        Calendar now = Calendar.getInstance();
        int currentHour = now.get(Calendar.HOUR_OF_DAY);
        
        // 同步当天数据，不清理缓存
        syncDayData(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD), "当天", false);
        
        // 如果是凌晨1点，额外同步前一天数据，并清理前一天的缓存
        if (currentHour == 1) {
            syncDayData(DateUtils.getPreviousDayStr(), "前一天", true);
        }
    }
    
    /**
     * 同步指定日期的丰巢链接数据
     * 
     * @param dateStr 日期字符串，格式为yyyy-MM-dd
     * @param dateDesc 日期描述，用于日志输出
     * @param cleanCache 是否在同步成功后清理Redis缓存
     * @return 是否全部同步成功
     */
    private boolean syncDayData(String dateStr, String dateDesc, boolean cleanCache) {
        log.info("开始执行丰巢链接{}数据同步任务，同步后{}清理缓存", dateDesc, cleanCache ? "将" : "不");
        try {
            // 获取所有有效的丰巢链接key
            List<String> fcLinkKeys = fcLinkService.getFcLinkKeysByStatus(0);
            
            if (fcLinkKeys == null || fcLinkKeys.isEmpty()) {
                log.info("没有找到有效的丰巢链接，{}数据同步任务结束", dateDesc);
                return false;
            }
            
            log.info("开始同步{}个丰巢链接的{}({})数据", fcLinkKeys.size(), dateDesc, dateStr);
            int successCount = 0;
            int cleanedCount = 0;
            
            for (String fcLinkKey : fcLinkKeys) {
                try {
                    boolean result = fcLinkDayDataService.syncRedisMetricsToDb(dateStr, fcLinkKey);
                    if (result) {
                        successCount++;
                        
                        // 如果需要清理缓存且同步成功，则清理Redis缓存
                        if (cleanCache) {
                            try {
                                fcLinkDayDataService.cleanRedisCache(dateStr, fcLinkKey);
                                cleanedCount++;
                            } catch (Exception e) {
                                log.error("清理丰巢链接Redis缓存数据异常，链接key: {}, 日期: {}", fcLinkKey, dateStr, e);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("同步丰巢链接{}数据异常，链接key: {}", dateDesc, fcLinkKey, e);
                }
            }
            
            if (cleanCache) {
                log.info("丰巢链接{}数据同步任务完成，成功同步: {}/{}，已清理缓存: {}/{}", 
                        dateDesc, successCount, fcLinkKeys.size(), cleanedCount, successCount);
            } else {
                log.info("丰巢链接{}数据同步任务完成，成功同步: {}/{}", 
                        dateDesc, successCount, fcLinkKeys.size());
            }
            return successCount > 0;
        } catch (Exception e) {
            log.error("丰巢链接{}数据同步任务执行异常", dateDesc, e);
            return false;
        }
    }
}
