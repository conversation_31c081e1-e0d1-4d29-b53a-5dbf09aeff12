package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserFianceStatisticsRecordEntity;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserFianceStatisticsRecordService;
import com.ruoyi.system.service.common.WhitelistService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * 广告主财务汇总记录初始化定时任务
 *
 * <AUTHOR>
 * @date 2023/11/14
 */
@Slf4j
@Component
public class AdvertiserFinanceStatisticRecordJob {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private AdvertiserFianceStatisticsRecordService advertiserFianceStatisticsRecordService;

    @Scheduled(cron = "0 0 23 * * ?")
    public void execute() {
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("AdvertiserFinanceStatisticRecordJob"), 10);
        if (lock == null) {
            return;
        }

        Date curDate = DateUtil.beginOfDay(new Date());
        List<Long> advertiserIds = whitelistService.list(OFFLINE_DATA_ADVERTISER, Long.class);
        for (Long advertiserId : advertiserIds) {
            AdvertiserFianceStatisticsRecordEntity record = advertiserFianceStatisticsRecordService.selectLatest(advertiserId);
            if (null == record) {
                continue;
            }
            if (record.getCurDate().before(curDate)) {
                advertiserFianceStatisticsRecordService.insert(advertiserId, curDate, record.getCashBalance(), record.getRebateBalance());
                log.info("财务汇总记录初始化, advertiserId={}, cash={}, rebate={}", advertiserId, record.getCashBalance(), record.getRebateBalance());
            }
        }
    }
}
