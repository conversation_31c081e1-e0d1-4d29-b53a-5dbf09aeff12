package com.ruoyi.system.job;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.entity.slotcharge.SlotChargeEntity;
import com.ruoyi.system.mapper.datashow.AppDataMapper;
import com.ruoyi.system.mapper.datashow.SlotDataMapper;
import com.ruoyi.system.service.slotcharge.SlotChargeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 广告位每日计费方式同步定时任务
 *
 * <AUTHOR>
 * @date 2022/2/17 3:02 下午
 */
@Slf4j
@Component
public class SlotChargeSyncJob {

    @Autowired
    private SlotChargeService slotChargeService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private SlotDataMapper slotDataMapper;

    @Autowired
    private AppDataMapper appDataMapper;

    @Scheduled(cron = "0 1 0 * * ?")
    public void execute(){
        RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K001.join("SlotChargeSyncJob"), 10);
        if (lock == null) {
            return;
        }
        log.info("广告位每日计费方式同步定时任务开始");
        Long id =  0L;
        Integer pageSize = 100;
        List<SlotChargeEntity> slotChargeEntities;
        Date selectDate = DateUtil.beginOfDay(DateUtils.addDays(new Date(), -2));
        Date yesterday = DateUtil.beginOfDay(DateUtils.addDays(new Date(), -1));
        while (CollectionUtils.isNotEmpty(slotChargeEntities = slotChargeService.selectListByDate(id,pageSize,selectDate))){
            id = slotChargeEntities.get(slotChargeEntities.size() - 1).getId();
            slotChargeEntities.stream().forEach(data ->{
                data.setCurDate(yesterday);
            });

            slotChargeService.batchInsertOrUpdate(slotChargeEntities);
        }
        //把前一天所有广告位/媒体的媒体收益都设置为0
        slotDataMapper.initAppRevenueByDate(yesterday);
        appDataMapper.initAppRevenueByDate(yesterday);
        log.info("广告位每日计费方式同步定时任务结束");
    }
}
