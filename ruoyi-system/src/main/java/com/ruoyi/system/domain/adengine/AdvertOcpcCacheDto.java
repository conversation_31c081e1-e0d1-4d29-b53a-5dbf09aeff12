package com.ruoyi.system.domain.adengine;

import lombok.Data;

/**
 * 广告OCPC缓存DTO
 *
 * <AUTHOR>
 * @date 2023/11/16
 */
@Data
public class AdvertOcpcCacheDto {

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置
     */
    private Long orientId;

    /**
     * 计费单价(分)
     */
    private Integer unitPrice;

    /**
     * OCPC转化类型
     * @see com.ruoyi.common.enums.advert.OcpcConvTypeEnum
     */
    private Integer ocpcConvType;

    /**
     * OCPC转化成本(分)
     */
    private Integer ocpcConvCost;

    /**
     * 开启OCPC的转化阈值
     */
    private Integer convThreshold;
}
