package com.ruoyi.system.domain.adengine;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.domain.slot.AreaTargetRedirectItem;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.vo.slot.RetConfigVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 广告位缓存DTO
 *
 * <AUTHOR>
 * @date 2022-05-11
 */
@Data
public class SlotCacheDto implements Serializable {
    private static final long serialVersionUID = -7495729760865419645L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 投放链接
     */
    private String slotUrl;

    /**
     * 活动投放类型:1.活动ID,2.链接
     */
    private Integer redirectType;

    /**
     * 活动投放信息
     */
    private String redirectValue;

    /**
     * 地域定向投放配置项
     */
    private List<AreaTargetRedirectItem> redirectItems;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 域名配置
     */
    private JSONObject domainConfig;

    /**
     * 返回拦截设置
     */
    private RetConfigVO retConfig;

    /**
     * 开关设置
     */
    private SlotSwitchConfig switchConfig;
}
