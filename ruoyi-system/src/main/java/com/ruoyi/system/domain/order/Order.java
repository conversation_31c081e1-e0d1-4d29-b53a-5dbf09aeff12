package com.ruoyi.system.domain.order;

import java.util.Date;

/**
 * 订单数据对象 tb_order_xxxx
 *
 * <AUTHOR>
 * @date 2021/8/18
 */
public class Order {

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 素材ID
     */
    private Long materialId;

    /**
     * 广告信息快照
     */
    private String adSnapshot;

    /**
     * 埋点信息
     */
    private String adStat;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 表后缀
     */
    private String tbSuffix;

    /**
     * 正常期间：度过冷启动了
     */
    private boolean skipCoolFlag = true;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Long getConsumerId() {
        return consumerId;
    }

    public void setConsumerId(Long consumerId) {
        this.consumerId = consumerId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getSlotId() {
        return slotId;
    }

    public void setSlotId(Long slotId) {
        this.slotId = slotId;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getAdvertId() {
        return advertId;
    }

    public void setAdvertId(Long advertId) {
        this.advertId = advertId;
    }

    public Long getOrientId() {
        return orientId;
    }

    public void setOrientId(Long orientId) {
        this.orientId = orientId;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    public String getAdSnapshot() {
        return adSnapshot;
    }

    public void setAdSnapshot(String adSnapshot) {
        this.adSnapshot = adSnapshot;
    }

    public String getAdStat() {
        return adStat;
    }

    public void setAdStat(String adStat) {
        this.adStat = adStat;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getTbSuffix() {
        return tbSuffix;
    }

    public void setTbSuffix(String tbSuffix) {
        this.tbSuffix = tbSuffix;
    }

    public boolean isSkipCoolFlag() {
        return skipCoolFlag;
    }

    public void setSkipCoolFlag(boolean skipCoolFlag) {
        this.skipCoolFlag = skipCoolFlag;
    }
}
