package com.ruoyi.system.domain.slot;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 广告位地域定向跳转配置项
 *
 * <AUTHOR>
 * @date 2021/9/27
 */
@Data
public class AreaTargetRedirectItem implements Serializable {
    private static final long serialVersionUID = 7797345310627786125L;

    /**
     * 描述
     */
    private String desc;

    /**
     * 定向的地域
     */
    private Set<String> targetArea;

    /**
     * 跳转类型
     */
    private Integer redirectType;

    /**
     * 跳转目标
     */
    private List<ShuntRedirectItem> redirectValue;
}
