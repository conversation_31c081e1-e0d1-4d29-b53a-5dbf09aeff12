package com.ruoyi.system.domain.billing;

import lombok.Data;

import java.io.Serializable;

/**
 * 计费消息内容
 *
 * <AUTHOR>
 * @date 2021/8/23
 */
@Data
public class BillingMessage implements Serializable {
    private static final long serialVersionUID = 5109975421385792358L;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 时间戳
     */
    private Long timestamp;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BillingMessage() {
    }

    public BillingMessage(String orderId) {
        this.orderId = orderId;
        this.timestamp = System.currentTimeMillis();
    }
}
