package com.ruoyi.system.domain.engine;

import com.ruoyi.system.entity.advert.InnovateLayerInfo;
import lombok.Data;

/**
 * 素材缓存DTO
 *
 * <AUTHOR>
 * @date 2021/8/12
 */
@Data
public class MaterialCacheDto {

    /**
     * 素材ID
     */
    private Long materialId;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 广告标题
     */
    private String advertTitle;

    /**
     * 按钮文案
     */
    private String buttonText;

    /**
     * 弹层类型
     */
    private Integer layerType;

    /**
     * 弹层ID
     */
    private Long layerId;

    /**
     * 创新弹层类型(1.刮刮卡,2.翻牌子)
     */
    private Integer skinType;

    /**
     * 素材图链接
     */
    private String materialImg;

    /**
     * 轮播图
     */
    private String gifImg;

    /**
     * 弹层信息
     */
    private InnovateLayerInfo layerInfo;

    /**
     * 深拷贝
     */
    public MaterialCacheDto copy() {
        MaterialCacheDto copy = new MaterialCacheDto();
        copy.materialId = this.materialId;
        copy.weight = this.weight;
        copy.advertTitle = this.advertTitle;
        copy.buttonText = this.buttonText;
        copy.layerType = this.layerType;
        copy.layerId = this.layerId;
        copy.skinType = this.skinType;
        copy.materialImg = this.materialImg;
        copy.gifImg = this.gifImg;
        if (null != this.layerInfo) {
            copy.layerInfo = this.layerInfo.copy();
        }
        return copy;
    }
}
