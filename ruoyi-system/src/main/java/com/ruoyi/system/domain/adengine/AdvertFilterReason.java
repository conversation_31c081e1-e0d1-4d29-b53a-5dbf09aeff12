package com.ruoyi.system.domain.adengine;

import com.ruoyi.common.core.local.RequestThreadLocal;
import com.ruoyi.common.utils.DateUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 广告过滤原因
 *
 * <AUTHOR>
 * @date 2021/8/27
 */
@Data
public class AdvertFilterReason implements Serializable {
    private static final long serialVersionUID = 1038245882203677574L;

    /**
     * 时间
     */
    private String time;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 备选广告总数
     */
    private Integer total;

    /**
     * 配置ID-过滤原因映射
     */
    private Map<Long, String> reasons;

    /**
     * 构造过滤原因对象
     */
    public static AdvertFilterReason build(RequestThreadLocal local) {
        AdvertFilterReason filterReason = new AdvertFilterReason();
        filterReason.setTime(DateUtils.getTime());
        filterReason.setOrderId(local.getOrderId());
        filterReason.setConsumerId(local.getConsumerId());
        filterReason.setActivityId(local.getActivityId());
        filterReason.setAppId(local.getAppId());
        filterReason.setSlotId(local.getSlotId());
        filterReason.setReasons(new HashMap<>());
        return filterReason;
    }
}
