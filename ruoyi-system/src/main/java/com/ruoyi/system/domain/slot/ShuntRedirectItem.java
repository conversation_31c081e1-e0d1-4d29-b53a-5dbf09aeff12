package com.ruoyi.system.domain.slot;

import com.alibaba.fastjson.annotation.JSONField;
import com.ruoyi.system.domain.common.ShuntRatio;
import lombok.Data;

import java.io.Serializable;

/**
 * 广告位分流跳转配置项
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
public class ShuntRedirectItem extends ShuntRatio implements Serializable {
    private static final long serialVersionUID = -4070678713531822101L;

    /**
     * 跳转类型
     */
    private Integer redirectType;

    /**
     * 跳转目标
     */
    private String redirectValue;

    /**
     * 前置空白页，true开启，false关闭
     */
    private Boolean useEmptyPage;

    /**
     * 是否修改
     */
    @JSONField(serialize = false)
    private Integer isModified;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 修改时间
     */
    private String modifyTime;
}
