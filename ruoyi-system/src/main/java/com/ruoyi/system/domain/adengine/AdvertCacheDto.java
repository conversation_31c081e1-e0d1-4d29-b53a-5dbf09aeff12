package com.ruoyi.system.domain.adengine;

import com.ruoyi.system.bo.advert.AdvertExtInfo;
import com.ruoyi.system.domain.engine.MaterialCacheDto;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * 广告缓存DTO
 *
 * <AUTHOR>
 * @date 2021/8/12
 */
@Data
public class AdvertCacheDto {

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告分类
     * @see com.ruoyi.common.enums.advert.AdvertCategory
     */
    private Integer advertCategory;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 配置名称
     */
    private String orientName;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告缩略图
     */
    private String thumbnailImg;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 落地页原始链接
     */
    private String originLandpageUrl;

    /**
     * 计费类型
     * @see com.ruoyi.common.enums.advert.ChargeTypeEnum
     */
    private Integer chargeType;

    /**
     * 计费单价(分)
     */
    private Integer unitPrice;

    /**
     * 计费单价(毫)
     */
    private Integer milliUnitPrice;

    /**
     * 考核指标
     */
    private Integer assessType;

    /**
     * 考核成本(分)
     */
    private Integer assessCost;

    /**
     * 屏蔽媒体
     */
    private Set<Long> bannedAppIds;

    /**
     * 定向媒体
     */
    private Set<Long> orientAppIds;

    /**
     * 定向媒体-广告位列表映射
     */
    private Map<Long, Set<Long>> orientSlotMap;

    /**
     * 定向广告位
     */
    private Set<Long> orientSlotIds;

    /**
     * 设备定向
     * @see com.ruoyi.common.enums.advert.DeviceTargetType
     */
    private Integer deviceTarget;

    /**
     * 系统定向
     * @see com.ruoyi.common.enums.advert.OsTargetType
     */
    private Integer osTarget;

    /**
     * 流量定向
     * @see com.ruoyi.common.enums.advert.FlowTargetType
     */
    private Integer flowTarget;

    /**
     * 运营商定向
     * @see com.ruoyi.common.enums.advert.IspTargetType
     */
    private Integer ispTarget;

    /**
     * 投放时段
     */
    private Integer servingHour;

    /**
     * 开始投放日期
     */
    private Date startServingDate;

    /**
     * 结束投放日期
     */
    private Date stopServingDate;

    /**
     * 投放开关:0.关闭,1.开启
     */
    private Integer servingSwitch;

    /**
     * 状态
     * @see com.ruoyi.common.enums.advert.AdvertStatusEnum
     */
    private Integer advertStatus;

    /**
     * 地域定向集合
     */
    private Set<String> areaTargetSet;

    /**
     * 额外信息
     */
    private AdvertExtInfo extInfo;

    /**
     * 可投的素材列表
     */
    private TreeMap<Integer, MaterialCacheDto> materialMap;

    /**
     * 预估CTR
     */
    private Double pCtr;

    /**
     * 预估CVR
     */
    private Double pCvr;

    /**
     * 权重
     */
    private Double weight;

    /**
     * 排序因子
     */
    private Double orderFactor;

    /**
     * CVR排序因子(暂时仅用于直投广告排序)
     */
    private Double cvrOrderFactor;

    /**
     * 是否默认配置
     */
    private Integer isDefaultOrient;

    /**
     * 配置每日预算
     */
    private Integer orientBudget;

    /**
     * 广告每日预算
     */
    private Integer advertBudget;

    /**
     * 深拷贝
     */
    public AdvertCacheDto copy() {
        AdvertCacheDto copy = new AdvertCacheDto();
        copy.advertId = this.advertId;
        copy.advertCategory = this.advertCategory;
        copy.advertName = this.advertName;
        copy.orientId = this.orientId;
        copy.orientName = this.orientName;
        copy.thumbnailImg = this.thumbnailImg;
        copy.landpageUrl = this.landpageUrl;
        copy.originLandpageUrl = this.originLandpageUrl;
        copy.unitPrice = this.unitPrice;
        copy.milliUnitPrice = this.milliUnitPrice;
        copy.startServingDate = this.startServingDate;
        copy.stopServingDate = this.stopServingDate;
        copy.servingSwitch = this.servingSwitch;
        copy.advertStatus = this.advertStatus;
        copy.deviceTarget = this.deviceTarget;
        copy.flowTarget = this.flowTarget;
        copy.osTarget = this.osTarget;
        copy.ispTarget = this.ispTarget;
        copy.servingHour = this.servingHour;
        copy.pCtr = this.pCtr;
        copy.pCvr = this.pCvr;
        copy.weight = this.weight;
        copy.orderFactor = this.orderFactor;
        copy.chargeType = this.chargeType;
        copy.cvrOrderFactor = this.cvrOrderFactor;
        copy.isDefaultOrient = this.isDefaultOrient;
        copy.orientBudget = this.orientBudget;
        copy.advertBudget = this.advertBudget;
        copy.assessCost = this.assessCost;
        copy.assessType = this.assessType;
        copy.advertiserId = this.advertiserId;
        copy.materialMap = new TreeMap<>();
        if (null != this.materialMap) {
            this.materialMap.forEach((key, value) -> copy.materialMap.put(key, value.copy()));
        }
        copy.setBannedAppIds(new HashSet<>());
        if (null != this.bannedAppIds) {
            copy.getBannedAppIds().addAll(this.bannedAppIds);
        }
        copy.setOrientAppIds(new HashSet<>());
        if (null != this.orientAppIds) {
            copy.getOrientAppIds().addAll(this.orientAppIds);
        }
        copy.setOrientSlotIds(new HashSet<>());
        if (null != this.orientSlotIds) {
            copy.getOrientSlotIds().addAll(this.orientSlotIds);
        }
        copy.setAreaTargetSet(new HashSet<>());
        if (null != this.areaTargetSet) {
            copy.getAreaTargetSet().addAll(this.areaTargetSet);
        }
        copy.setOrientSlotMap(new HashMap<>());
        if (null != this.orientSlotMap) {
            this.orientSlotMap.forEach((key, value) -> copy.orientSlotMap.put(key, new HashSet<>(value)));
        }
        if (null != this.extInfo) {
            copy.extInfo = this.extInfo.copy();
        }
        return copy;
    }

    /**
     * 根据权重随机获取素材
     */
    public MaterialCacheDto randomMaterial(int shuntHash) {
        if (null == this.materialMap) {
            return null;
        }
        int randomWeight = shuntHash % this.materialMap.lastKey();
        SortedMap<Integer, MaterialCacheDto> tailMap = this.materialMap.tailMap(randomWeight, false);
        return this.materialMap.get(tailMap.firstKey());
    }
}
