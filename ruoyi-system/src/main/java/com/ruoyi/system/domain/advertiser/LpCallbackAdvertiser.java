package com.ruoyi.system.domain.advertiser;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 落地页回传广告主
 *
 * <AUTHOR>
 * @date 2022-03-14
 */
@Data
public class LpCallbackAdvertiser implements Serializable {
    private static final long serialVersionUID = -2400995000085002574L;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 年龄下限(包含)
     */
    private Integer ageMin;

    /**
     * 年龄上限(包含)
     */
    private Integer ageMax;

    /**
     * 每日表单量上限
     */
    private Integer dailyLimit;

    /**
     * 表单价格
     */
    private Integer formPrice;

    /**
     * 排序因子=int(表单价格*权重+0.5)
     */
    private Integer orderFactor;

    /**
     * 结算类型
     * @see com.ruoyi.common.enums.advertiser.AdvertiserConsumeType
     */
    private Integer consumeType;

    /**
     * 更新时间
     */
    private Date gmtCreate;
}
