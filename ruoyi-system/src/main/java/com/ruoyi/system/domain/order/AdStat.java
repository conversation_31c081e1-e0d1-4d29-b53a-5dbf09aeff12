package com.ruoyi.system.domain.order;

import java.io.Serializable;
import java.util.List;

/**
 * 埋点信息
 *
 * <AUTHOR>
 * @date 2021/8/25
 */
public class AdStat implements Serializable {
    private static final long serialVersionUID = -2295759206510498256L;

    /**
     * 曝光的毫秒时间戳列表
     */
    private List<Long> exposures;

    /**
     * 点击的毫秒时间戳列表
     */
    private List<Long> clicks;

    public List<Long> getExposures() {
        return exposures;
    }

    public void setExposures(List<Long> exposures) {
        this.exposures = exposures;
    }

    public List<Long> getClicks() {
        return clicks;
    }

    public void setClicks(List<Long> clicks) {
        this.clicks = clicks;
    }
}
