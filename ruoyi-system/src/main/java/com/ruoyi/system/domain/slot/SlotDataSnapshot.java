package com.ruoyi.system.domain.slot;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.system.entity.datashow.SlotData;
import lombok.Data;

import java.io.Serializable;

/**
 * 广告位数据快照
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
@Data
public class SlotDataSnapshot implements Serializable {
    private static final long serialVersionUID = 936102928520443097L;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;

    /**
     * 结算款(分)
     */
    private Long nhCost;

    /**
     * 外部结算金额(分)
     */
    private Long outerCost;

    /**
     * 媒体应得收益(分)
     */
    private Long appRevenue;

    /**
     * convert SlotData to SlotDataSnapshot
     */
    public static SlotDataSnapshot create(SlotData slotData) {
        return null == slotData ? null : BeanUtil.copyProperties(slotData, SlotDataSnapshot.class);
    }
}
