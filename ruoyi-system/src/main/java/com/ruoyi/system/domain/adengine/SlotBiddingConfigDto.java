package com.ruoyi.system.domain.adengine;

import com.ruoyi.common.enums.common.EnableStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 广告位投流配置缓存DTO
 *
 * <AUTHOR>
 * @date 2023-9-7
 */
@Data
public class SlotBiddingConfigDto implements Serializable {
    private static final long serialVersionUID = -3870727902661748635L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告位
     */
    private Long slotId;

    /**
     * 上报价格(分)
     */
    private Integer convPrice;

    /**
     * 冷启动个数，默认1
     */
    private Integer coldStart;

    /**
     * 消耗类型:1.真实消耗(广告CPC消耗),2.理论消耗(广告考核成本)
     */
    private Integer consumeType;

    /**
     * 是否生效:0.未生效,1.已生效
     */
    private Integer isEnable;

    /**
     * 配置是否生效
     */
    public boolean isConfigEnable() {
        return EnableStatusEnum.isEnable(isEnable) && null != convPrice && convPrice > 0;
    }

    /**
     * 配置是否未生效
     */
    public boolean isConfigDisable() {
        return !isConfigEnable();
    }

    /**
     * 是否真实消耗
     */
    public boolean isTrueConsume() {
        return Objects.equals(consumeType, 1);
    }
}

