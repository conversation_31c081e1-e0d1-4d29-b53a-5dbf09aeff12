package com.ruoyi.system.domain.advert;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 广告配置定向媒体
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Data
public class AdvertOrienteApp extends BaseEntity {
    private static final long serialVersionUID = -8518726069690359014L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 定向的广告位id列表
     */
    private String orienteSlotIds;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}
