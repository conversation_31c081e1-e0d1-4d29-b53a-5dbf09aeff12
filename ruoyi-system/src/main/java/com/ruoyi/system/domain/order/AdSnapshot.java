package com.ruoyi.system.domain.order;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.advert.AdvertTypeEnum;
import com.ruoyi.common.enums.advert.AdvertFlagEnum;
import com.ruoyi.system.bo.advert.AdvertExtInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * 订单信息快照
 *
 * <AUTHOR>
 * @date 2021/8/20
 */
@Data
public class AdSnapshot implements Serializable {
    private static final long serialVersionUID = -4242135302438634382L;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 广告缩略图
     */
    private String thumbnailImg;

    /**
     * 广告类型 {@link AdvertTypeEnum}
     */
    private Integer advertType;

    /**
     * 广告分类
     * @see com.ruoyi.common.enums.advert.AdvertCategory
     */
    private Integer advertCategory;

    /**
     * 广告标识 {@link AdvertFlagEnum}
     */
    private Integer advertFlag;

    /**
     * 插件ID
     */
    private Long pluginId;

    /**
     * 计费类型
     * @see com.ruoyi.common.enums.advert.ChargeTypeEnum
     */
    private Integer chargeType;

    /**
     * 计费单价(分)
     */
    private Integer unitPrice;

    /**
     * 计费单价(毫)
     */
    private Integer milliUnitPrice;

    /**
     * 考核指标
     */
    private Integer assessType;

    /**
     * 考核成本(分)
     */
    private Integer assessCost;

    /**
     * 广告位请求标识
     */
    private String srid;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 落地页链接
     */
    private String originLandpageUrl;

    /**
     * 手机品牌
     */
    private String brand;

    /**
     * 手机型号
     */
    private String model;

    /**
     * 额外信息
     */
    private AdvertExtInfo extInfo;

    /**
     * 外部信息
     */
    private JSONObject ext;
}
