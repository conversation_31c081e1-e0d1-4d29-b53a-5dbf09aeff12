package com.ruoyi.system.domain.callback;

import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.system.domain.order.Order;
import com.ruoyi.system.req.callback.ConvCallbackParam;
import lombok.Data;

import java.util.Map;

/**
 * 转化上报上下文
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
@Data
public class ConvCallbackContext {

    /**
     * 业务日志类型
     */
    private InnerLogType type;

    /**
     * 转化类型
     */
    private ConvType convType;

    /**
     * 订单
     */
    private Order order;

    /**
     * 转化上报参数
     */
    private ConvCallbackParam param;

    /**
     * 扩展参数，临时解决多个事件上报的类型问题
     */
    private Map<String, String> ext;
}
