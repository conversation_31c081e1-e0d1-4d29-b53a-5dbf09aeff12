package com.ruoyi.system.domain.adengine;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.entity.datashow.MobileHapDataEntity;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * 广告过滤上下文
 *
 * <AUTHOR>
 * @date 2021/8/17
 */
@Data
public class AdvertFilterContext {

    /**
     * 今日日期
     */
    private Date today;

    /**
     * 今日日期
     */
    private String dateStr;

    /**
     * 当前时段
     */
    private Integer hour;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 广告缓存
     */
    private AdvertCacheDto advertCacheDto;

    /**
     * 重复曝光的广告ID集合
     */
    private Set<Long> repeatAdvertIds;

    /**
     * 重复曝光的配置ID集合
     */
    private Set<Long> repeatOrientIds;

    /**
     * IP地域信息
     */
    private IpAreaDto ipArea;

    /**
     * 用户代理标识
     */
    private String userAgent;

    /**
     * 手机品牌
     */
    private String mobileBrand;

    /**
     * 用户在蜂窝网络下的运营商
     */
    private String isp;

    /**
     * 手机快应用数据
     */
    private MobileHapDataEntity mobileHapData;

    /**
     * 扩展参数
     */
    private JSONObject extParam;

    /**
     * 过滤原因
     */
    private AdvertFilterReason filterReason;
}
