package com.ruoyi.system.bo.landpage;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 落地页数据导出Bo
 *
 * <AUTHOR>
 * @date 2022-09-29
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class LandpageDataExcelBo {

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("日期")
    private Date curDate;

    @ExcelProperty("落地页名称")
    private String landpageName;

    @ExcelProperty("落地页链接")
    private String landpageUrl;

    @ExcelProperty("落地页曝光PV")
    private Integer lpExposurePv;

    @ExcelProperty("落地页曝光UV")
    private Integer lpExposureUv;

    @ExcelProperty("落地页转化PV")
    private Integer lpClickPv;

    @ExcelProperty("落地页转化UV")
    private Integer lpClickUv;

    @ExcelProperty("CVR(落地页转化PV/落地页曝光PV)")
    private String cvrPv;

    @ExcelProperty("CVR(落地页转化UV/落地页曝光UV)")
    private String cvrUv;

    @ExcelProperty("落地页参与PV")
    private Integer lpJoinPv;

    @ExcelProperty("落地页参与UV")
    private Integer lpJoinUv;

    @ExcelProperty("弹窗曝光PV")
    private Integer popupExposurePv;

    @ExcelProperty("弹窗曝光UV")
    private Integer popupExposureUv;

    @ExcelProperty("落地页领取PV")
    private Integer popupClickPv;

    @ExcelProperty("落地页领取UV")
    private Integer popupClickUv;

    @ExcelProperty("激活")
    private Integer register;

    @ExcelProperty("支付")
    private Integer pay;
}
