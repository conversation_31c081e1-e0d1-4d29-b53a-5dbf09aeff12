package com.ruoyi.system.bo.thirdparty;

import lombok.Data;

import java.util.Objects;

/**
 * 身份证校验结果BO
 *
 * <AUTHOR>
 * @date 2022-11-02
 */
@Data
public class IdCardAuditBo {

    /**
     * 接口提供方
     * 1.聚合
     * 2.毫秒科技
     */
    private Integer apiType;

    /**
     * 结果:0.匹配,1.不匹配
     */
    private Integer code;

    /**
     * 信息
     */
    private String msg;

    /**
     * 是否匹配
     *
     * @param result 结果
     * @return 是否匹配
     */
    public static boolean isMatch(IdCardAuditBo result) {
        return null != result && Objects.equals(result.code, 0);
    }

    /**
     * 是否不匹配
     *
     * @param result 结果
     * @return 是否不匹配
     */
    public static boolean isNotMatch(IdCardAuditBo result) {
        return null != result && Objects.equals(result.code, 1);
    }
}
