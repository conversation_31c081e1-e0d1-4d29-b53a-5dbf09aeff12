package com.ruoyi.system.bo.sms;

import lombok.Data;

import java.util.List;

/**
 * 留资短信配置vo
 *
 * <AUTHOR>
 * @date 2022/12/5 13:55
 */
@Data
public class LiuziSmsConfigBO {
    /**
     * 是否开启
     */
    private Boolean open;
    /**
     * 短信配置
     */
    private List<LiuziSmsConfigInfoBO> config;

    @Data
    public class LiuziSmsConfigInfoBO {

        /**
         * 自建站类型
         * @see com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum
         */
        private Integer landpageType;

        /**
         * 配置类型，1默认配置，2自定义配置
         */
        private Integer configType;

        /**
         * 短信模版id
         */
        @Deprecated
        private Long tpId;

        /**
         * 短信内容
         */
        private String content;

        /**
         * 推广页id,默认为0
         */
        private List<Long> advertIds;

        /**
         * 企微兜底开关:true.开启,false.关闭
         */
        private Boolean qwSwitch;
    }
}
