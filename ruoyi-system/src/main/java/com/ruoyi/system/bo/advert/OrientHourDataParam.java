package com.ruoyi.system.bo.advert;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 配置数据查询参数
 *
 * <AUTHOR>
 * @date 2023/07/12
 */
@Data
public class OrientHourDataParam {

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 配置ID列表
     */
    private List<Long> orientIds;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 要排序的字段
     */
    private String orderColumn;

    /**
     * 排序方式
     */
    private String orderType;
}
