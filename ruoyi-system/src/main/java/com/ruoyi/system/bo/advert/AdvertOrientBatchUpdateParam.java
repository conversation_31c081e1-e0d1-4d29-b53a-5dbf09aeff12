package com.ruoyi.system.bo.advert;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 批量修改广告定向配置
 *
 * <AUTHOR>
 * @Date 2023/9/25 17:52
 */
@Data
public class AdvertOrientBatchUpdateParam implements Serializable {

    private static final long serialVersionUID = 364298277673335414L;
    /**
     *广告配置id列表
     */
    private List<Long> ids;

    /**
     * 地域定向
     */
    private String areaTarget;

    /**
     * 设备定向
     * @see com.ruoyi.common.enums.advert.DeviceTargetType
     */
    private Integer deviceTarget;

    /**
     * 运营商定向
     * @see com.ruoyi.common.enums.advert.IspTargetType
     */
    private Integer ispTarget;

    /**
     * 系统定向
     * @see com.ruoyi.common.enums.advert.OsTargetType
     */
    private Integer osTarget;

    /**
     * 流量定向
     * @see com.ruoyi.common.enums.advert.FlowTargetType
     */
    private Integer flowTarget;

    /**
     * 投放时段:0.不限,非0.自定义
     */
    private Integer servingHour;

}
