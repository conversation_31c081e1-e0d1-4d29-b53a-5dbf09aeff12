package com.ruoyi.system.bo.advert;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告额外信息
 *
 * <AUTHOR>
 * @date 2022/03/29
 */
@Data
public class AdvertExtInfo implements Serializable {
    private static final long serialVersionUID = -4084794266370043566L;

    /**
     * 小程序AppID
     */
    private String appId;

    /**
     * 小程序AppPath
     */
    private String appPath;

    /**
     * 深拷贝
     */
    public AdvertExtInfo copy() {
        AdvertExtInfo copy = new AdvertExtInfo();
        copy.setAppId(this.appId);
        copy.setAppPath(this.appPath);
        return copy;
    }
}
