package com.ruoyi.system.bo.landpage.article;

import lombok.Data;

import java.io.Serializable;

/**
 * 文章到达率监测BO
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Data
public class ArticleCheckBo implements Serializable {
    private static final long serialVersionUID = 2265107977262880162L;

    /**
     * 文章ID
     */
    private Long articleId;

    /**
     * 文章链接
     */
    private String url;

    /**
     * 起始时间戳
     */
    private Long timestamp;

    /**
     * 诺禾阅读量
     */
    private Integer nhPv;

    /**
     * 实际阅读量
     */
    private Integer realPv;
}
