package com.ruoyi.system.bo.agent;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理商后台广告主数据导出
 *
 * <AUTHOR>
 * @date 2022/10/27
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class AgentClientCpcDataExcelBo implements Serializable {
    private static final long serialVersionUID = -4270519274124588253L;

    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("日期")
    private Date curDate;

    @ExcelProperty("广告主名称")
    private String advertiserName;

    @ExcelProperty("广告点击PV")
    private String billingClickPv;

    @ExcelProperty("广告点击UV")
    private String billingClickUv;

    @ExcelProperty("落地页转化PV")
    private Integer lpClickPv;

    @ExcelProperty("落地页转化UV")
    private Integer lpClickUv;

    @ExcelProperty("落地页转化成本")
    private String lpClickCost;

    @ExcelProperty("支付成本")
    private String payCost;

    @ExcelProperty("消费金额(元)")
    private String consumeAmount;
}
