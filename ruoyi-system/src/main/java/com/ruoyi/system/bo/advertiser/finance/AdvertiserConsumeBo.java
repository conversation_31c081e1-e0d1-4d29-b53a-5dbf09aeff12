package com.ruoyi.system.bo.advertiser.finance;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告主消费BO
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
@Data
public class AdvertiserConsumeBo implements Serializable {
    private static final long serialVersionUID = 901129187628846013L;

    /**
     * 广告主ID
     */
    private Long accountId;

    /**
     * 消费金额(分)
     */
    private Integer consumeAmount;

    /**
     * 消费类型:0.默认,1.表单自动分配,2.表单手动分配
     */
    private Integer consumeType;

    /**
     * 落地页表单ID
     */
    private Long recordId;

    /**
     * 备注
     */
    private String remark;
}
