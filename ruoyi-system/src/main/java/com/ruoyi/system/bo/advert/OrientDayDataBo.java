package com.ruoyi.system.bo.advert;

import lombok.Data;

import java.util.Date;

/**
 * 配置日数据BO
 *
 * <AUTHOR>
 * @date 2023-07-12
 */
@Data
public class OrientDayDataBo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 时段
     */
    private Integer curHour;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置
     */
    private Long orientId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 发券pv
     */
    private Integer adLaunchPv;

    /**
     * 发券uv
     */
    private Integer adLaunchUv;

    /**
     * 广告曝光pv
     */
    private Integer exposurePv;

    /**
     * 广告曝光uv
     */
    private Integer exposureUv;

    /**
     * 广告点击pv
     */
    private Integer clickPv;

    /**
     * 广告点击uv
     */
    private Integer clickUv;

    /**
     * 广告计费点击pv
     */
    private Integer billingClickPv;

    /**
     * 广告计费点击uv
     */
    private Integer billingClickUv;

    /**
     * 消耗(分)
     */
    private Integer consume;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页点击pv
     */
    private Integer lpClickPv;

    /**
     * 落地页点击uv
     */
    private Integer lpClickUv;

    /**
     * 领取pv
     */
    private Integer takePv;

    /**
     * 领取uv
     */
    private Integer takeUv;

    /**
     * 激活
     */
    private Integer register;

    /**
     * 支付
     */
    private Integer pay;

    /**
     * 退款
     */
    private Integer refund;

    /**
     * 投诉
     */
    private Integer complain;

    /**
     * APP激活
     */
    private Integer appActive;

    /**
     * 支付金额
     */
    private Double extPrice;
}
