package com.ruoyi.system.bo.landpage;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 企微囤粉落地页表单记录查询BO
 *
 * <AUTHOR>
 * @date 2023-9-22
 */
@Data
public class QwtfLandpageFormRecordSelectBo {

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;

    /**
     * 加好友状态:0.未加好友,1.已加好友
     */
    private Integer friendStatus;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 落地页链接
     */
    private String landpageUrl;
}
