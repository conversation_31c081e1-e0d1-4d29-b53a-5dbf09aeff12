package com.ruoyi.system.bo.landpage.article;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 文章聚合链接数据导出Bo
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Data
public class ArticleAggrLinkDataExcelBo {

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 12, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    @Excel(name = "链接名称")
    private String linkName;

    @Excel(name = "链接地址")
    private String linkUrl;

    @Excel(name = "请求PV")
    private Integer requestPv;

    @Excel(name = "请求UV")
    private Integer requestUv;
}
