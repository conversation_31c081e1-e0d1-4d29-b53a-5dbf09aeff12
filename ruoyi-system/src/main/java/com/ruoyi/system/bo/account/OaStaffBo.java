package com.ruoyi.system.bo.account;

import com.ruoyi.system.entity.oa.department.DepartmentEntity;
import com.ruoyi.system.entity.oa.post.PostEntity;
import lombok.Data;

import java.util.Set;

/**
 * OA员工BO
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
@Data
public class OaStaffBo {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门
     */
    private DepartmentEntity department;

    /**
     * 职位
     */
    private PostEntity post;

    /**
     * OA权限集合
     */
    private Set<String> oaPermissionKeys;
}
