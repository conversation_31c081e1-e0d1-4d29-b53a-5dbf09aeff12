package com.ruoyi.system.bo.advertiser.finance;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.ruoyi.common.utils.excel.FenToYuanConverter;
import lombok.Data;

import java.io.Serializable;

/**
 * 广告主余额记录Excel对象
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
@Data
@HeadRowHeight(20)
@ColumnWidth(25)
@HeadFontStyle(fontHeightInPoints = 10)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class AdvertiserBalanceExcelBo implements Serializable {
    private static final long serialVersionUID = -6319184625253260262L;

    @ExcelIgnore
    private Long id;

    @ExcelProperty("账号类型")
    private String mainTypeStr;

    @ExcelProperty("广告主ID")
    private Long advertiserId;

    @ExcelProperty("广告主名称")
    private String advertiserName;

    @ExcelProperty("代理商ID")
    private Long agentId;

    @ExcelProperty("代理商名称")
    private String agentName;

    @ExcelProperty(value = "总余额", converter = FenToYuanConverter.class)
    private Integer totalAmount;

    @ExcelProperty(value = "现金余额", converter = FenToYuanConverter.class)
    private Integer cashAmount;

    @ExcelProperty(value = "返货余额", converter = FenToYuanConverter.class)
    private Integer rebateAmount;

    @ExcelProperty(value = "运营调整", converter = FenToYuanConverter.class)
    private Integer offsetAmount;

    @ExcelProperty("标签")
    private String tag;
}
