package com.ruoyi.system.bo.landpage;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 后端转化数据BO
 *
 * <AUTHOR>
 * @date 2022-08-05
 */
@Data
public class ConvDataBo {

    /**
     * 转化日期
     */
    private Date curDate;

    /**
     * 时段
     */
    private Integer curHour;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 支付
     */
    private Integer pay;

    /**
     * 支付金额
     */
    private BigDecimal payPrice;

    /**
     * 激活
     */
    private Integer register;

    /**
     * 退款
     */
    private Integer refund;

    /**
     * 投诉
     */
    private Integer complain;

    /**
     * APP激活
     */
    private Integer appActive;
}
