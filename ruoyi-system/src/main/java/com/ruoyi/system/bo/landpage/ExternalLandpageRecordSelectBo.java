package com.ruoyi.system.bo.landpage;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 外部落地页表单查询BO
 *
 * <AUTHOR>
 * @date 2023-04-06
 */
@Data
public class ExternalLandpageRecordSelectBo {

    /**
     * 广告主ID列表
     */
    private List<Long> advertiserIds;

    /**
     * 代理商ID列表
     */
    private List<Long> agentIds;

    /**
     * 提交时间起始
     */
    private Date submitDateStart;

    /**
     * 提交时间截止
     */
    private Date submitDateEnd;

    /**
     * 回传时间起始
     */
    private Date sendDateStart;

    /**
     * 回传时间截止
     */
    private Date sendDateEnd;

    /**
     * 导出记录:0.未导出,1.已导出
     */
    private Integer isExported;

    /**
     * 回传状态:0.未回传,1.回传成功,2.回传失败
     */
    private Integer sendStatus;

    /**
     * 用户姓名/手机号
     */
    private String userSearch;

    /**
     * 回传广告主ID列表
     */
    private List<Long> sendAdvertiserIds;
}
