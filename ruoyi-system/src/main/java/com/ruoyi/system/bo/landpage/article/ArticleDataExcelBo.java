package com.ruoyi.system.bo.landpage.article;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 文章数据导出Bo
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Data
public class ArticleDataExcelBo {

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 12, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    @Excel(name = "文章名称")
    private String articleName;

    @Excel(name = "文章链接")
    private String articleUrl;

    @Excel(name = "目标阅读量")
    private Integer targetRequestPv;

    @Excel(name = "初始阅读量")
    private Integer initRequestPv;

    @Excel(name = "私域增加阅读量")
    private Integer syIncrRequestPv;

    @Excel(name = "实际增加阅读量")
    private Integer actualIncrRequestPv;

    @Excel(name = "补量")
    private Integer compensateRequestPv;

    @Excel(name = "请求PV")
    private Integer requestPv;

    @Excel(name = "请求UV")
    private Integer requestUv;
}
