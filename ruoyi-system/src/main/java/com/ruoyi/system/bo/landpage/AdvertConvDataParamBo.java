package com.ruoyi.system.bo.landpage;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 广告转化数据查询BO
 *
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
@Accessors(chain = true)
public class AdvertConvDataParamBo {

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;
}
