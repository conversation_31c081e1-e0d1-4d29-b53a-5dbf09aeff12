package com.ruoyi.system.bo.open;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 辉煌OCPX转化事件Bo
 *
 * <AUTHOR>
 * @date 2024/09/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HhOcpxEventBo implements Serializable {
    private static final long serialVersionUID = 3418584751833696852L;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 事件时间
     */
    private String eventTime;
}

