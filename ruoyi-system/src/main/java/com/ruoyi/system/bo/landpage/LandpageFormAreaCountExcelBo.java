package com.ruoyi.system.bo.landpage;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.ruoyi.common.utils.excel.FenToYuanConverter;
import lombok.Data;

import java.io.Serializable;

/**
 * 落地页表单地域分析数据导出Bo
 *
 * <AUTHOR>
 * @date 2022-08-08
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(30) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class LandpageFormAreaCountExcelBo implements Serializable {
    private static final long serialVersionUID = -4193197190171723706L;

    @ExcelProperty("省份")
    private String province;

    @ExcelProperty("城市")
    private String city;

    @ExcelProperty("订单数")
    private Integer formCount;

    @ExcelProperty(value = "消耗", converter = FenToYuanConverter.class)
    private Integer consume;

    @ExcelProperty("激活数")
    private Integer register;

    @ExcelProperty("首充数")
    private Integer pay;

    @ExcelProperty("激活率")
    private String activeRate;

    @ExcelProperty("激活成本")
    private String registerCost;

    @ExcelProperty("综转成本")
    private String payCost;
}
