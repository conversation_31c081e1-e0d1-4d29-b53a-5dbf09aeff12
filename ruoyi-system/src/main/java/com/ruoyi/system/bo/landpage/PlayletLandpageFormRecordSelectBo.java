package com.ruoyi.system.bo.landpage;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 短剧落地页表单记录查询BO
 *
 * <AUTHOR>
 * @date 2023-8-2
 */
@Data
public class PlayletLandpageFormRecordSelectBo {

    /**
     * 广告ID列表
     */
    private List<Long> advertIds;

    /**
     * 媒体ID列表
     */
    private List<Long> appIds;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 提交日期起始
     */
    private Date startDate;

    /**
     * 提交日期截止
     */
    private Date endDate;

    /**
     * 订单状态:0.未支付,1.支付成功
     */
    private Integer tradeStatus;

    /**
     * 交易单号
     */
    private String transactionId;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 支付通道:1.微信,2.支付宝
     */
    private Integer payPlatform;
}
