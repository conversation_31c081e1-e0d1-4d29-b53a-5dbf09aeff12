package com.ruoyi.system.bo.landpage;

import lombok.Data;

import java.util.Date;

/**
 * 外部落地页表单记录Bo
 *
 * <AUTHOR>
 * @date 2023-4-11
 */
@Data
public class ExternalLandpageRecordBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 提交日期
     */
    private Date submitDate;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 外部表单编号
     */
    private String externalNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号(加密)
     */
    private String idCard;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区/县
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 导入类型
     * @see com.ruoyi.common.enums.landpage.ImportTypeEnum
     */
    private Integer importType;

    /**
     * 是否导出过:0.未导出,1.已导出
     */
    private Integer isExported;

    /**
     * 额外信息
     */
    private String extInfo;

    /**
     * 接收时间
     */
    private Date gmtCreate;

    /**
     * 是否回传成功:0.失败,1.成功
     */
    private Integer isSuccess;

    /**
     * 回传结果
     */
    private String resp;

    /**
     * 回传时间
     */
    private Date sendTime;

    /**
     * 回传广告主ID
     */
    private Long sendAdvertiserId;
}
