package com.ruoyi.system.bo.landpage.article;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 文章列表查询
 *
 * <AUTHOR>
 * @date 2023-12-1
 */
@Data
@NoArgsConstructor
public class ArticleListParamBo {

    /**
     * 文章聚合链接ID
     */
    private Long linkId;

    /**
     * 名称/链接查询
     */
    private String searchKey;

    /**
     * 名称
     */
    private String name;

    /**
     * 链接
     */
    private String url;

    /**
     * 聚合链接id列表
     */
    private List<Long> ids;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    public ArticleListParamBo(Long linkId) {
        this.linkId = linkId;
    }
}

