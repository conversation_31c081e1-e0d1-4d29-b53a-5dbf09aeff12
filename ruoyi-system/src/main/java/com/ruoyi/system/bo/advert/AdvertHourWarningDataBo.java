package com.ruoyi.system.bo.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告时段告警数据BO
 *
 * <AUTHOR>
 * @date 2022-09-13
 */
@Data
public class AdvertHourWarningDataBo implements Serializable {
    private static final long serialVersionUID = -6143941438684213488L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 时段
     */
    private Integer curHour;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 广告点击pv
     */
    private Integer clickPv;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页转化pv
     */
    private Integer lpClickPv;

    /**
     * 今日CVR
     */
    private Double tCvr;

    /**
     * 昨日CVR
     */
    private Double yCvr;
}
