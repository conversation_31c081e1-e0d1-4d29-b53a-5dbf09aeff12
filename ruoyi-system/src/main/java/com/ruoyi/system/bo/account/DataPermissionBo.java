package com.ruoyi.system.bo.account;

import com.ruoyi.common.enums.account.DataPermissionType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * 账号数据权限BO
 *
 * <AUTHOR>
 * @date 2022/06/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataPermissionBo {

    public static final DataPermissionBo NONE_PERMISSION = new DataPermissionBo(DataPermissionType.NONE.getType(), Collections.emptyList());
    public static final DataPermissionBo FULL_PERMISSION = new DataPermissionBo(DataPermissionType.FULL.getType(), Collections.emptyList());

    /**
     * 数据权限类型
     * @see com.ruoyi.common.enums.account.DataPermissionType
     */
    private Integer type;

    /**
     * 数据权限列表
     */
    private List<Long> values;
}
