package com.ruoyi.system.bo.landpage.article;

import lombok.Data;

import java.util.Date;

/**
 * 文章列表BO
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@Data
public class ArticleListBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 聚合链接ID
     */
    private Long linkId;

    /**
     * 名称
     */
    private String name;

    /**
     * 链接
     */
    private String url;

    /**
     * 目标阅读量
     */
    private Integer targetRequestPv;

    /**
     * 初始阅读量
     */
    private Integer initRequestPv;

    /**
     * 实际阅读量
     */
    private Integer actualRequestPv;

    /**
     * 私域阅读量
     */
    private Integer syRequestPv;

    /**
     * 实际阅读量(广告主后台展示)
     */
    private Integer displayActualRequestPv;

    /**
     * 补量
     */
    private Integer compensateRequestPv;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 状态:0.正常,1.已删除
     */
    private Integer status;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operatorTime;

    /**
     * 今日请求PV
     */
    private Integer requestPv;

    /**
     * 今日请求PV
     */
    private Integer requestUv;

    /**
     * 是否在线:1.在线,0.不在线
     */
    private Integer online;

    /**
     * 创建时间
     */
    private Date gmtCreate;


    /**
     * 丰巢审核状态，0-待审核，1-通过，2-驳回
     */
    private Integer fcCheckStatus;

    /**
     * 丰巢同步状态，0-待同步，1-同步成功，2-同步失败
     */
    private Integer fcSyncStatus;
}

