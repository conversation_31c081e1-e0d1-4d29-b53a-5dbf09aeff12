package com.ruoyi.system.bo.slot;

import com.ruoyi.system.domain.common.ShuntRatio;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位切量计划BO
 *
 * <AUTHOR>
 * @date 2022/4/24
 */
@Data
public class SlotShuntTaskBO extends ShuntRatio implements Serializable {
    private static final long serialVersionUID = 1851114642278291989L;

    /**
     * 计划ID
     */
    private Long id;

    /**
     * 广告位
     */
    private Long slotId;

    /**
     * 切量类型:1.pv,2.uv
     */
    private Integer shuntType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 活动投放类型
     * @see com.ruoyi.common.enums.slot.SlotRedirectType
     */
    private Integer redirectType;

    /**
     * 活动ID、链接或者广告ID
     */
    private String redirectValue;

    /**
     * 切量上限
     */
    private Integer threshold;
}
