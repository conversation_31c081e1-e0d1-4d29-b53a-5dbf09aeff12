package com.ruoyi.system.bo.advertiser.finance;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.ruoyi.common.utils.excel.FenToYuanConverter;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主充值记录Excel对象
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
@Data
@HeadRowHeight(20)
@ColumnWidth(25)
@HeadFontStyle(fontHeightInPoints = 10)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class AdvertiserRechargeExcelBo implements Serializable {
    private static final long serialVersionUID = -1842432169793881864L;

    @ExcelIgnore
    private Long id;

    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("申请时间")
    private Date gmtCreate;

    @ExcelProperty("申请人")
    private String operatorName;

    @ExcelProperty("广告主ID")
    private Long advertiserId;

    @ExcelProperty("广告主名称")
    private String advertiserName;

    @ExcelProperty("代理商ID")
    private Long agentId;

    @ExcelProperty("代理商名称")
    private String agentName;

    @ExcelProperty("账号类型")
    private String mainTypeStr;

    @ExcelProperty(value = "充值金额", converter = FenToYuanConverter.class)
    private Integer rechargeAmount;

    @ExcelProperty("充值类型")
    private String rechargeTypeStr;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("审核状态")
    private String auditStatusStr;

    @ExcelProperty("审核理由")
    private String auditReason;

    @ExcelProperty("审核人")
    private String auditorName;

    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("审核时间")
    private Date auditTime;
}
