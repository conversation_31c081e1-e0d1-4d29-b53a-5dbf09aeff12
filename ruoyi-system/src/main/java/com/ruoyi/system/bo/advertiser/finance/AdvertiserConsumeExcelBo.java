package com.ruoyi.system.bo.advertiser.finance;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.utils.excel.FenToYuanConverter;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主财务汇总记录Excel对象
 *
 * <AUTHOR>
 * @date 2022-03-22
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class AdvertiserConsumeExcelBo implements Serializable {
    private static final long serialVersionUID = -6440545525651697631L;

    @ExcelIgnore
    private Long id;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("时间")
    private Date curDate;

    @ExcelProperty("广告主ID")
    private Long advertiserId;

    @ExcelProperty("广告主名称")
    private String advertiserName;

    @ExcelProperty("代理商ID")
    private Long agentId;

    @ExcelProperty("代理商名称")
    private String agentName;

    @ExcelProperty("账号类型")
    private String mainTypeStr;

    @ExcelProperty("表单量(已分配-有效)")
    private Integer formCount;

    @ExcelProperty("表单量(已分配)")
    private Integer totalFormCount;

    @ExcelProperty("计费点击PV")
    private Integer billingClickPv;

    @ExcelProperty("计费点击UV")
    private Integer billingClickUv;

    @ExcelProperty(value = "总消费", converter = FenToYuanConverter.class)
    private Integer consumeAmount;

    @ExcelProperty(value = "余额", converter = FenToYuanConverter.class)
    private Integer balanceAmount;

    @ExcelProperty("同步广告主")
    private String isVisible;

    @ExcelProperty("操作人")
    private String operatorName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("操作时间")
    private Date operatorTime;

    @ExcelProperty("负责人")
    private String managerName;
}
