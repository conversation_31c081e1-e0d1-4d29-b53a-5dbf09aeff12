package com.ruoyi.system.bo.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 广告位数据对象(CRM使用)
 *
 * <AUTHOR>
 * @date 2023-04-10
 */
@Data
public class CrmSlotDataBo {
    private static final long serialVersionUID = -6950821196586816210L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 媒体id
     */
    private Long appId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 广告位id
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;

    /**
     * 广告位点击pv(媒体反馈)
     */
    private Integer appSlotClickPv;

    /**
     * 广告位点击uv(媒体反馈)
     */
    private Integer appSlotClickUv;

    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposurePv;

    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposureUv;

    /**
     * 广告位访问uv,原始数据
     */
    private Integer slotRequestUvOriginal;

    /**
     * 诺禾广告消耗(分)
     */
    private Long nhConsume;

    /**
     * 外部广告消耗(分)
     */
    private Long outerConsume;

    /**
     * 总消耗
     */
    private Long totalConsume;

    /**
     * 媒体应得收益(分)
     */
    private Long appRevenue;

    /**
     * 总结算款(分)
     */
    private Long totalCost;

    /**
     * 结算款(分)
     */
    private Long nhCost;

    /**
     * 外部结算金额(分)
     */
    private Long outerCost;

    /**
     * 是否媒体可见(0.不可见,1.可见)
     */
    private Integer isVisible;

    /**
     * 是否可编辑(0.编辑,1.不可编辑)
     */
    private Integer isEditable;

    /**
     * 最后操作时间
     */
    private Date operateTime;

    /**
     * 是否编辑过
     */
    private Integer isEdited;
}
