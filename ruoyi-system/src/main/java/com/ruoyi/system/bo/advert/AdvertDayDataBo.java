package com.ruoyi.system.bo.advert;

import lombok.Data;

import java.util.Date;

/**
 * 广告日数据对象
 *
 * <AUTHOR>
 * @date 2023-04-10
 */
@Data
public class AdvertDayDataBo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    private Date curDate;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 发券pv
     */
    private Integer adLaunchPv;

    /**
     * 发券uv
     */
    private Integer adLaunchUv;

    /**
     * 广告曝光pv
     */
    private Integer exposurePv;

    /**
     * 广告曝光uv
     */
    private Integer exposureUv;

    /**
     * 广告点击pv
     */
    private Integer clickPv;

    /**
     * 广告点击uv
     */
    private Integer clickUv;

    /**
     * 广告计费点击pv
     */
    private Integer billingClickPv;

    /**
     * 广告计费点击uv
     */
    private Integer billingClickUv;

    /**
     * 消耗(分)
     */
    private Integer consume;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页点击pv
     */
    private Integer lpClickPv;

    /**
     * 落地页点击uv
     */
    private Integer lpClickUv;

    /**
     * 领取pv
     */
    private Integer takePv;

    /**
     * 领取uv
     */
    private Integer takeUv;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}
