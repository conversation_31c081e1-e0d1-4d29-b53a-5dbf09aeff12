package com.ruoyi.system.bo.activity.skin;

import lombok.Data;

import java.io.Serializable;

/**
 * 活动皮肤模板
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
@Data
public class JsTemplate implements Serializable {
    private static final long serialVersionUID = -1620007981933157174L;

    /**
     * [大转盘/翻牌子]背景图
     */
    private String bgImage;

    /**
     * [大转盘]大转盘底座
     */
    private String wheelPedestal;

    /**
     * [大转盘]大转盘转轮图
     */
    private String wheelPrizeBg;

    /**
     * [大转盘]大转盘子背景图
     */
    private String wheelBg;

    /**
     * [大转盘]大转盘按钮图
     */
    private String btn;

    /**
     * [大转盘]抽奖按钮指针
     */
    private String btnPedestal;

    /**
     * [通用]规则背景图
     */
    private String ruleBg;

    /**
     * 前置红包开关
     */
    private Boolean preRedSwitch;

    /**
     * 顶部文案开关
     */
    private Boolean topTextSwitch;

    /**
     * 顶部文案
     */
    private String topText;

    /**
     * [大转盘（动效版）]前置动效开关
     */
    private Boolean preAnimationSwitch;

    /**
     * [大转盘（动效版）]集现金红包开关
     */
    private Boolean turntableCashRedPacketSwitch;
}
