package com.ruoyi.system.bo.advertiser.finance;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主后台消费记录导出
 *
 * <AUTHOR>
 * @date 2023/01/05
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class WisAdvertiserConsumeDataExcelBo implements Serializable {
    private static final long serialVersionUID = -9159248941859195511L;

    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("日期")
    private Date curDate;

    @ExcelProperty("消费(元)")
    private String consumeAmount;
}
