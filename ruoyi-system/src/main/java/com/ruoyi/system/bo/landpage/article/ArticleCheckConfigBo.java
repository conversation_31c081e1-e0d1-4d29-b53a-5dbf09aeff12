package com.ruoyi.system.bo.landpage.article;

import lombok.Data;

import java.io.Serializable;

/**
 * 文章到达率监测配置BO
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Data
public class ArticleCheckConfigBo implements Serializable {
    private static final long serialVersionUID = 5216279341835287802L;

    // 监测间隔(分钟)
    private Integer timeThreshold = 10;

    // 监测增量阈值
    private Integer pvThreshold = 100;

    // 监测到达率阈值
    private Integer arriveThreshold = 80;
}
