package com.ruoyi.system.bo.shorturl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短链数据导出bo
 * <AUTHOR>
 * @date 2023/2/24 14:30
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class ShortUrlDataExcelBO implements Serializable {
    private static final long serialVersionUID = 8028850073248520159L;

    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("日期")
    private Date curDate;
    /**
     * 短链
     */
    @ExcelProperty("短链")
    private String shortUrl;

    /**
     * 原链接
     */
    @ExcelProperty("原始链接")
    private String originUrl;

    /**
     * 点击pv
     */
    @ExcelProperty("点击pv")
    private Integer requestPv;
    /**
     * 点击uv
     */
    @ExcelProperty("点击uv")
    private Integer requestUv;

}
