package com.ruoyi.system.bo.advert;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 配置数据更新参数
 *
 * <AUTHOR>
 * @date 2023-7-12
 */
@Data
public class OrientHourDataUpdateParam {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 发券pv增量
     */
    private Integer adLaunchPvAdd;

    /**
     * 发券uv增量
     */
    private Integer adLaunchUvAdd;

    /**
     * 券曝光pv增量
     */
    private Integer exposurePvAdd;

    /**
     * 券曝光uv增量
     */
    private Integer exposureUvAdd;

    /**
     * 券点击pv增量
     */
    private Integer clickPvAdd;

    /**
     * 券点击uv增量
     */
    private Integer clickUvAdd;

    /**
     * 券计费点击pv增量
     */
    private Integer billingClickPvAdd;

    /**
     * 券计费点击uv增量
     */
    private Integer billingClickUvAdd;

    /**
     * 消费增量
     */
    private Integer consumeAdd;

    /**
     * 落地页曝光pv增量
     */
    private Integer lpExposurePvAdd;

    /**
     * 落地页曝光uv增量
     */
    private Integer lpExposureUvAdd;

    /**
     * 落地页点击pv增量
     */
    private Integer lpClickPvAdd;

    /**
     * 落地页点击uv增量
     */
    private Integer lpClickUvAdd;

    /**
     * 领取pv增量
     */
    private Integer takePvAdd;

    /**
     * 领取uv增量
     */
    private Integer takeUvAdd;

    /**
     * 表单提交增量
     */
    private Integer formAdd;

    /**
     * 支付增量
     */
    private Integer payAdd;

    /**
     * 退款增量
     */
    private Integer refundAdd;

    /**
     * 注册增量
     */
    private Integer registerAdd;

    /**
     * 发货增量
     */
    private Integer deliveryAdd;

    /**
     * 投诉增量
     */
    private Integer complainAdd;

    /**
     * APP下载增量
     */
    private Integer appDownloadAdd;

    /**
     * APP激活增量
     */
    private Integer appActiveAdd;

    /**
     * APP注册增量
     */
    private Integer appRegisterAdd;

    /**
     * 支付金额增量
     */
    private BigDecimal payPriceAdd;
}

