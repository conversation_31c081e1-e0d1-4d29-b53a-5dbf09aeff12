package com.ruoyi.system.bo.landpage.article;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 文章聚合链接列表查询
 *
 * <AUTHOR>
 * @date 2023-12-1
 */
@Data
public class ArticleAggrLinkListParamBo {

    /**
     * 名称/链接查询
     */
    private String searchKey;

    /**
     * 链接唯一标识列表
     */
    private List<String> keys;

    /**
     * 名称
     */
    private String name;

    /**
     * 广告主id
     */
    private Long advertiserId;

    /**
     * 起始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 链接ID列表
     */
    private List<Long> linkIds;

    /**
     * 排除的链接ID列表
     */
    private List<Long> notInLinkIds;

    private String fcSearchKey;
}

