package com.ruoyi.system.vo.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.entity.advert.InnovateLayerInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 创新弹层VO
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
@Data
public class InnovateLayerVO implements Serializable {
    private static final long serialVersionUID = -6906217852415279715L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 弹层名称
     */
    private String layerName;

    /**
     * 弹层皮肤编号
     */
    private String skinCode;

    /**
     * 弹层皮肤名称
     */
    private String skinName;

    /**
     * 弹层皮肤类型
     */
    private Integer skinType;

    /**
     * 背景图
     */
    private String bgImg;

    /**
     * 轮播GIF图
     */
    private String gifImg;

    /**
     * 弹层信息
     */
    private InnovateLayerInfo layerInfo;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
