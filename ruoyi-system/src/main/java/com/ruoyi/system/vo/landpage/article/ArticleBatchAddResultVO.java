package com.ruoyi.system.vo.landpage.article;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文章批量导入结果VO
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ArticleBatchAddResultVO implements Serializable {
    private static final long serialVersionUID = -4810237459065959826L;

    /**
     * 成功数量
     */
    private Integer success;

    /**
     * 失败数量
     */
    private Integer error;

    /**
     * 失败条目
     */
    private List<ArticleBatchAddResultItemVO> errList;
}
