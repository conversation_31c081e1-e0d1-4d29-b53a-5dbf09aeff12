package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短剧落地页表单记录VO
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Data
public class PlayletLandpageFormRecordVO implements Serializable {
    private static final long serialVersionUID = 5344927407910027677L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告主名称
     */
    private String advertiserName;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 订单金额
     */
    private Long tradeAmount;

    /**
     * 交易单号
     */
    private String transactionId;

    /**
     * 订单状态:0.未支付,1.已支付
     */
    private Integer tradeStatus;

    /**
     * 订单OpenId
     */
    private String openid;

    /**
     * 交易时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tradeTime;

    /**
     * 支付通道:1.微信,2.支付宝
     */
    private Integer payPlatform;
}
