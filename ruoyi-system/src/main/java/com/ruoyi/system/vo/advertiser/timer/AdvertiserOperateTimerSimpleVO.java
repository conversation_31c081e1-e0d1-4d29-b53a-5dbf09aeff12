package com.ruoyi.system.vo.advertiser.timer;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主操作定时任务VO
 *
 * <AUTHOR>
 * @date 2023-4-20
 */
@Data
public class AdvertiserOperateTimerSimpleVO implements Serializable {
    private static final long serialVersionUID = -3393299445110639094L;

    /**
     * 计划时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planTime;

    /**
     * 操作类型:1.关闭计划,2.开启计划
     */
    private Integer operType;
}
