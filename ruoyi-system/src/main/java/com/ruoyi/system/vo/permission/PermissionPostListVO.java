package com.ruoyi.system.vo.permission;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 权限职位列表
 * <AUTHOR>
 * @date 2022/6/23 5:23 下午
 */
@Data
public class PermissionPostListVO implements Serializable {
    private static final long serialVersionUID = 7697261903467739372L;

    /**
     * 部门名称
     */
    private String departmentName;
    /**
     * 职位id
     */
    private Long postId;
    /**
     * 职位名称
     */
    private String postName;
    /**
     * 员工数量
     */
    private Integer staffNum;

}
