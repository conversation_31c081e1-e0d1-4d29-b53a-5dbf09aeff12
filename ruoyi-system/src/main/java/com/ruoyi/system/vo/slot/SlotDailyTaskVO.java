package com.ruoyi.system.vo.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位运营每日任务VO
 *
 * <AUTHOR>
 * @date 2022/03/04
 */
@Data
public class SlotDailyTaskVO implements Serializable {
    private static final long serialVersionUID = 63630930963965229L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 媒体id
     */
    private Long appId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 广告位id
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;

    /**
     * 是否可编辑
     */
    private Integer canEdit;

    /**
     * 最后操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;
}
