package com.ruoyi.system.vo.open;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 媒体数据VO
 *
 * <AUTHOR>
 * @date 2022/04/18
 */
@Data
public class SspRevenueVO implements Serializable {
    private static final long serialVersionUID = -7126969658445429828L;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 广告位收益列表
     */
    private List<SlotRevenueVO> slotRevenueList;

    @Data
    public static class SlotRevenueVO {

        /**
         * 广告位ID
         */
        private Long slotId;

        /**
         * 广告位访问PV
         */
        private Integer slotPv;

        /**
         * 广告位访问UV
         */
        private Integer slotUv;

        /**
         * 广告位收益(分)
         */
        private Long revenue;

        /**
         * 转化数
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer conv;
    }
}
