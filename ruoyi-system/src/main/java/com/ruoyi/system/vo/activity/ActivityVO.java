package com.ruoyi.system.vo.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.bo.activity.skin.JsTemplate;
import com.ruoyi.system.domain.manager.Prize;
import com.ruoyi.system.vo.slot.RetConfigVO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 活动工具VO
 *
 * <AUTHOR>
 * @date 2021/7/16
 */
@Data
public class ActivityVO implements Serializable, Cloneable {
    private static final long serialVersionUID = 6555338248840125466L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 皮肤标识
     */
    private String skinCode;

    /**
     * 皮肤名称
     */
    private String skinName;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 是否自动出券:0.否,1.是
     */
    private Integer autoJoin;

    /**
     * 活动参与次数
     */
    private Integer joinTimes;

    /**
     * 活动剩余参与次数
     */
    private Integer remainJoinTimes;

    /**
     * 奖品列表
     */
    private List<Prize> prizeList;

    /**
     * 规则说明
     */
    private String ruleDesc;

    /**
     * ICP备案号
     */
    private String icpNo;

    /**
     * 配置模板
     */
    private String jsTemplate;

    /**
     * 皮肤配置：全局配置，所有活动生效
     */
    private String globalConfig;

    /**
     * 配置模板
     */
    private JsTemplate jsTemplateVO;

    /**
     * 返回挽留设置
     */
    private RetConfigVO retConfig;

    /**
     * 预览链接
     */
    private String previewUrl;

    /**
     * 创建人ID
     */
    private Long operatorId;

    /**
     * 创建人姓名
     */
    private String operatorName;
    /**
     * 客服列表
     */
    private ActivityCustomerVO customerConfig;

    /**
     * 运营商
     */
    private String isp;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @Override
    public Object clone() throws CloneNotSupportedException {
        ActivityVO copy =  (ActivityVO) super.clone();
        if (null != this.prizeList) {
            copy.prizeList = new ArrayList<>(this.prizeList);
        }
        if (null != this.retConfig) {
            copy.retConfig = (RetConfigVO) this.retConfig.clone();
        }
        if( null != this.customerConfig){
            copy.customerConfig = (ActivityCustomerVO) this.customerConfig.clone();
        }
        return copy;
    }
}
