package com.ruoyi.system.vo.account;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 账号VO
 *
 * <AUTHOR>
 * @date 2021-07-13
 */
@Data
public class AccountVO implements Serializable {
    private static final long serialVersionUID = 1787399691651896102L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 负责人名称
     */
    private String managerName;

    /**
     * 媒体数量
     */
    private Integer appCount;

    /**
     * 广告位数量
     */
    private Integer slotCount;

    /**
     * 昨日广告位访问UV
     */
    private Integer ydaySlotReqUv;

    /**
     * 昨日媒体应得收益
     */
    private Long ydayAppRevenue;

    /**
     * 商务
     */
    private List<Long> bdManagerIds;

    /**
     * 运营
     */
    private List<Long> operationManagerIds;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 合同状态 0代表无无合同
     * @see com.ruoyi.common.enums.contract.ContractStatusEnum
     */
    private Integer contractStatus = 0;

    /**
     * 距离合同到期相差天数
     */
    private Integer dateDiff;

    /**
     * 是否可指派负责人
     */
    private Integer canAssign;
}
