package com.ruoyi.system.vo.manager;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体转化上报汇总数据VO
 *
 * <AUTHOR>
 * @date 2022/10/19
 */
@Data
public class ConvertUploadSummaryDataVO implements Serializable {
    private static final long serialVersionUID = -4575358807821947403L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 转化回传数
     */
    private Integer convertCount;

    /**
     * 未转化回传数
     */
    private Integer unConvertCount;

    /**
     * 回传比例
     */
    private String convertPercent;

    /**
     * 诺禾消耗(分)
     */
    private Long nhCost;

    /**
     * 媒体结算(分)
     */
    private Integer appRevenue;

    /**
     * 分成比例(分)
     */
    private String revenuePercent;
}
