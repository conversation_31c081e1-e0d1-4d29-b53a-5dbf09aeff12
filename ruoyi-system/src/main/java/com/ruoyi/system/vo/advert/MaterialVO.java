package com.ruoyi.system.vo.advert;

import com.ruoyi.system.entity.advert.InnovateLayerInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * 素材VO
 *
 * <AUTHOR>
 * @date 2021/8/10
 */
@Data
public class MaterialVO implements Serializable {
    private static final long serialVersionUID = 8286315355096629739L;

    /**
     * 素材ID
     */
    private Long id;

    /**
     * 素材图链接
     */
    private String materialImg;

    /**
     * 弹层ID
     */
    private Long layerId;

    /**
     * 弹层类型:1.普通弹层,2.创新弹
     */
    private Integer layerType;

    /**
     * 弹层皮肤类型(1.刮刮卡,2.翻牌子)
     */
    private Integer skinType;

    /**
     * 广告标题
     */
    private String advertTitle;

    /**
     * 按钮文案
     */
    private String buttonText;

    /**
     * 是否默认素材:0.否,1.是
     */
    private Integer isDefault;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 状态:0.默认,1.已屏蔽
     */
    private Integer status;

    /**
     * 轮播图（创新弹层）
     */
    private String gifImg;

    /**
     * 弹层信息
     */
    private InnovateLayerInfo layerInfo;
}
