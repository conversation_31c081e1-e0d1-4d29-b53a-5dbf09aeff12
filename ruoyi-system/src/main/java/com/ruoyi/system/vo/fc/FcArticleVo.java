package com.ruoyi.system.vo.fc;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class FcArticleVo {

    /**
     * 文章id
     */
    private Integer articleId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 文章名称
     */
    private String articleName;

    /**
     * 文章链接
     */
    private String articleUrl;

    /**
     * 丰巢审核状态
     */
    private Integer fcCheckStatus;

    /**
     * 丰巢同步状态
     */
    private Integer fcSyncStatus;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 丰巢审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fcCheckTime;

    /**
     * 丰巢同步失败原因
     */
    private String fcSyncFailReason;
}
