package com.ruoyi.system.vo.slot.shunt;

import lombok.Data;

import java.util.List;

/**
 * 广告位切量任务初始信息
 *
 * <AUTHOR>
 * @date 2022/4/22
 */
@Data
public class SlotShuntTaskInitInfoVO {

    /**
     * 可选的切量分流类型:1.pv,2.uv
     */
    private List<Integer> shuntTypeList;

    /**
     * 最大切量比例
     */
    private Integer shuntRatioLimit;

    /**
     * 昨日广告位访问PV
     */
    private Integer slotRequestPv;

    /**
     * 昨日广告位访问UV
     */
    private Integer slotRequestUv;
}
