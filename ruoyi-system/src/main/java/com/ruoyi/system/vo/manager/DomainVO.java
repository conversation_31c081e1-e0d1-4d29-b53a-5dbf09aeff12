package com.ruoyi.system.vo.manager;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 域名管理VO
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Data
public class DomainVO implements Serializable {
    private static final long serialVersionUID = 6659004273800632947L;

    /**
     * 域名ID
     */
    private Long id;

    /**
     * 域名
     */
    private String domain;

    /**
     * 域名类型
     */
    private Integer domainType;

    /**
     * 广告位列表
     */
    private Set<Long> slotIds;

    /**
     * 广告列表
     */
    private Set<Long> advertIds;

    /**
     * 域名到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date domainExpire;

    /**
     * 是否启用Https:0.不启用,1.启用
     */
    private Integer httpsEnable;

    /**
     * 证书到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certExpireTime;

    /**
     * 域名状态
     */
    private Integer domainStatus;

    /**
     * 备案号
     */
    private String icpNo;

    /**
     * 备案主体
     */
    private String icpSubject;

    /**
     * 备注
     */
    private String remark;

    /**
     * 微信状态
     */
    private Integer wxStatus;
    /**
     * 支付宝状态
     */
    private Integer alipayStatus;
}
