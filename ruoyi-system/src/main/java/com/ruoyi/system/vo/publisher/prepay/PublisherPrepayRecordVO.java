package com.ruoyi.system.vo.publisher.prepay;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.bo.invoice.InvoiceUrlBO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 媒体预付款记录VO
 *
 * <AUTHOR>
 * @date 2022-07-29
 */
@Data
public class PublisherPrepayRecordVO implements Serializable {
    private static final long serialVersionUID = 6188331099827600584L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 媒体账号ID
     */
    private Long accountId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 预付款申请金额(分)
     */
    private Integer applyPrepayAmount;

    /**
     * 预付款欠款金额(分)
     */
    private Integer prepayAmount;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行账户
     */
    private String bankAccount;

    /**
     * 开户名
     */
    private String bankAccountName;

    /**
     * 发票列表
     */
    private List<InvoiceUrlBO> invoiceList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer auditStatus;

    /**
     * 复合审核状态:0.待部门负责人审核,1.审核通过,2.审核拒绝,3.待CEO/总裁审核,4.待财务审核,5.部门负责人审核拒绝,6.CEO/总裁审核拒绝,7.财务审核拒绝
     */
    private Integer complexAuditStatus;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 申请人名称
     */
    private String applicantName;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 部门负责人审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer leaderAuditStatus;

    /**
     * 部门负责人审核人名称
     */
    private String leaderAuditorName;

    /**
     * 部门负责人审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date leaderAuditTime;

    /**
     * 部门负责人审核理由
     */
    private String leaderAuditReason;

    /**
     * 业务负责人审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer ceoAuditStatus;

    /**
     * 业务负责人审核人名称
     */
    private String ceoAuditorName;

    /**
     * 业务负责人审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ceoAuditTime;

    /**
     * 业务负责人审核理由
     */
    private String ceoAuditReason;

    /**
     * 财务审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer financeAuditStatus;

    /**
     * 财务审核人名称
     */
    private String financeAuditorName;

    /**
     * 财务审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date financeAuditTime;

    /**
     * 财务审核理由
     */
    private String financeAuditReason;

    /**
     * 是否可审核
     */
    private Integer canAudit;

    /**
     * 预付款主体，多个用逗号隔开
     */
    private String prepaySubjectNames;
}
