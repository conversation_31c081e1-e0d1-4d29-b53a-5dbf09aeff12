package com.ruoyi.system.vo.withdraw;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 提现列表
 *
 * <AUTHOR>
 * @date 2022/11/29
 */
@Data
public class SspWithdrawListVO implements Serializable {
    private static final long serialVersionUID = -818005920351189734L;

    /**
     * 提现记录ID
     */
    private Long id;

    /**
     * 账号
     */
    private String email;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 银行账户
     */
    private String bankAccount;

    /**
     * 开户名
     */
    private String bankAccountName;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 提现金额
     */
    private Integer withdrawAmount;

    /**
     * 提现状态
     */
    private Integer withdrawStatus;

    /**
     * 提现单文件
     */
    private String withdrawFile;

    /**
     * 拒绝理由
     */
    private String refuseReason;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
