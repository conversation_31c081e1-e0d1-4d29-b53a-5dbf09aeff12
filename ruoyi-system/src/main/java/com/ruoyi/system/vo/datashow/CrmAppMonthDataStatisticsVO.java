package com.ruoyi.system.vo.datashow;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位统计数据vo crm专用
 *
 * <AUTHOR>
 * @date 2021/9/1 2:42 下午
 */
@Data
public class CrmAppMonthDataStatisticsVO implements Serializable {

    private static final long serialVersionUID = -5367373875585179721L;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;

    /**
     * 活动参与pv
     */
    private Integer joinPv;

    /**
     * 活动参与uv
     */
    private Integer joinUv;

    /**
     * 总消耗(分)
     */
    private Integer totalConsume;

    /**
     * 诺禾广告消耗(分)
     */
    private Integer nhConsume;

    /**
     * 外部广告消耗(分)
     */
    private Integer outerConsume;

    /**
     * 诺禾结算款(分)
     */
    private Long nhCost;
    /**
     * 外部结算款(分)
     */
    private Long outerCost;
    /**
     * 结算总金额(分)
     */
    private Long totalCost;
    /**
     * 媒体应得收益(分)
     */
    private Integer appRevenue;

}
