package com.ruoyi.system.vo.sms;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 留资短信配置详情
 *
 * <AUTHOR>
 * @date 2022/12/5 13:58
 */
@Data
public class LiuziSmsConfigInfoVO implements Serializable {
    private static final long serialVersionUID = -4797066336429791099L;

    /**
     * 自建站类型
     * @see com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum
     */
    private Integer landpageType;

    /**
     * 配置类型，1默认配置，2自定义配置
     */
    private Integer configType;

    /**
     * 短信模版id
     */
    @Deprecated
    private Long tpId;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 推广页id,默认为0
     */
    private List<Long> advertIds;

    /**
     * 企微兜底开关:true.开启,false.关闭
     */
    private Boolean qwSwitch;
}
