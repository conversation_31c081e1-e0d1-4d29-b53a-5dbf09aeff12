package com.ruoyi.system.vo.datashow;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位日账单数据导出vo crm专用
 *
 * <AUTHOR>
 * @date 2021/9/1 2:42 下午
 */
@Data
public class CrmSlotDayDataExportVO implements Serializable {
    private static final long serialVersionUID = -8485542226567427876L;

    /**
     * 日期
     */
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 媒体名称
     */
    @Excel(name = "媒体名称/媒体ID")
    private String appName;

    /**
     * 广告位名称
     */
    @Excel(name = "广告位名称/广告位ID")
    private String slotName;

    /**
     * 广告位访问uv
     */
    @Excel(name = "广告位访问uv")
    private Integer slotRequestUv;
    /**
     * 广告位访问pv
     */
    @Excel(name = "广告位访问pv")
    private Integer slotRequestPv;
    /**
     * 活动参与pv
     */
    @Excel(name = "参与pv")
    private Integer joinPv;

    /**
     * 活动参与uv
     */
    @Excel(name = "参与uv")
    private Integer joinUv;

    @Excel(name = "参与率")
    private String joinRate;
    /**
     * 总消耗
     */
    private String totalConsume;

    /**
     * 诺禾广告消耗(分)
     */
    private String nhConsume;
    /**
     * 外部广告消耗(分)
     */
    private String outerConsume;

    /** 总收益(分) */
    @Excel(name = "媒体应得收入(元)")
    private String appRevenue;

    /**
     * 结算款(诺禾)
     */
    @Excel(name = "结算款(诺禾)")
    private String nhCost;
    /**
     * 结算款(外部)
     */
    @Excel(name = "结算款(外部)")
    private String outerCost;
    /**
     * 结算总金额
     */
    @Excel(name = "结算总金额")
    private String totalCost;


}
