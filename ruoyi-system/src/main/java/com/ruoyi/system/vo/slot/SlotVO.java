package com.ruoyi.system.vo.slot;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.vo.advert.DegradedAdvertVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告位VO
 *
 * <AUTHOR>
 * @date 2021-07-14
 */
@Data
public class SlotVO implements Serializable {
    private static final long serialVersionUID = 2731335217966673781L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 负责人名称
     */
    private String managerName;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 投放链接
     */
    private String servingUrl;

    /**
     * 广告位链接
     */
    private String slotUrl;

    /**
     * 广告位规格
     */
    private Long slotSpecId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 投放方式:1.H5投放
     */
    private Integer sckType;

    /**
     * 昨日广告位访问UV
     */
    private Integer ydaySlotReqUv;

    /**
     * 昨日外部广告消耗(分)
     */
    private Long ydayOuterConsume;


    /**
     * 昨日媒体应得收益(分)
     */
    private Long ydayAppRevenue;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 活动投放类型:1.活动ID,2.链接
     */
    private Integer redirectType;

    /**
     * 活动ID或者链接
     */
    private String redirectValue;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否设置投放(1.已设置,0.未设置)
     */
    private Integer enable;

    /**
     * 域名配置
     */
    private JSONObject domainConfig;

    /**
     * 是否爱奇艺送审广告位(1.是,0.否)
     */
    private Integer iqiyiAudit;

    /**
     * 返回拦截设置
     */
    private RetConfigVO retConfig;

    /**
     * 开关设置
     */
    private SlotSwitchConfig switchConfig;

    /**
     * 昨日结算金额(诺禾)
     */
    private Long ydayNhCost;

    /**
     * 昨日结算金额(外部)
     */
    private Long ydayOuterCost;

    /**
     * 计费类型
     */
    private Integer chargeType;

    /**
     * 计费价格
     */
    private Integer chargePrice;

    /**
     * 广告位访问pv
     */
    private Integer ydaySlotReqPv;

    /**
     * 是否正在执行切量任务
     */
    private Integer isShunting;

    /**
     * 是否正在分流投放
     */
    private Integer isRedirectShunting;

    /**
     * 是否开启域名自动替换
     * 目前仅对支付宝落地页生效
     * @see com.ruoyi.common.enums.common.SwitchStatusEnum
     */
    private Integer autoReplace;

    /**
     * 数据校准开关(0.关闭,1.开启)
     */
    private Integer adjustSwitch;

    /**
     * 自动结算开关(0.关闭,1.开启)
     */
    private Integer autoChargeSwitch;

    /**
     * 标签数量
     */
    private Integer tagCount;

    /**
     * 是否展示投放链接(1.展示,0.不展示)
     */
    private Integer showServingUrl;

    /**
     * 是否展示投流配置(1.展示,0.不展示)
     */
    private Integer showBiddingConfig;

    /**
     * 兜底广告列表
     */
    private List<DegradedAdvertVO> degradedDirectAdverts;
}
