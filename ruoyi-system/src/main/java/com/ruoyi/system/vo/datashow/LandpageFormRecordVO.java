package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 落地页单记录VO
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
@Data
public class LandpageFormRecordVO implements Serializable {
    private static final long serialVersionUID = -7461005265702569671L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 运营商
     */
    private String isp;

    /**
     * 分配的广告主
     */
    private Long targetAdvertiserId;

    /**
     * 分配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /**
     * 分配状态
     * @see com.ruoyi.common.enums.landpage.AssignStatus
     */
    private Integer assignStatus;

    /**
     * 未分配或者分配失败原因
     */
    private String reason;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 来源链接
     */
    private String referer;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区/县
     */
    private String district;

    /**
     * 行政区划代码
     */
    private String areaNum;

    /**
     * 详细信息
     */
    private String address;

    /**
     * IP
     */
    private String ip;

    /**
     * 身份证号(加密)
     */
    private String idCard;

    /**
     * 身份证校验:0.未校验,1.校验通过
     */
    private Integer idCardAudit;

    /**
     * 表单价格(分)
     */
    private Integer formPrice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 表单提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 表单修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
