package com.ruoyi.system.vo.advertiser.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主cpc数据列表
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
@Data
public class CrmAdvertiserCpcDataListVO implements Serializable {
    private static final long serialVersionUID = -4316205021827915833L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告主名称
     */
    private String advertiserName;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 广告点击PV
     */
    private Integer billingClickPv;

    /**
     * 广告点击UV
     */
    private Integer billingClickUv;

    /**
     * 是否有前置落地页
     */
    private Integer hasPreLandpage;

    /**
     * 广告主链接
     */
    private String landpageUrl;

    /**
     * 落地页曝光PV
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光UV
     */
    private Integer lpExposureUv;

    /**
     * 落地页转化PV
     */
    private Integer lpClickPv;

    /**
     * 落地页转化UV
     */
    private Integer lpClickUv;

    /**
     * 落地页转化成本
     */
    private String lpClickCost;

    /**
     * 消费金额
     */
    private Integer consumeAmount;

    /**
     * 支付
     */
    private Integer pay;

    /**
     * 支付成本
     */
    private String payCost;
}
