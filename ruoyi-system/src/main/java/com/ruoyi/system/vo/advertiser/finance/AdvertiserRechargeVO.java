package com.ruoyi.system.vo.advertiser.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主/代理商充值记录VO
 *
 * <AUTHOR>
 * @date 2022/3/21 3:10 下午
 */
@Data
public class AdvertiserRechargeVO implements Serializable {
    private static final long serialVersionUID = 3294149214256328233L;

    /**
     * 充值ID
     */
    private Long id;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 账号类型:2.广告主,4.代理商
     */
    private Integer mainType;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 子广告主的代理商ID
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 充值类型
     *
     * @see AdvertiserRechargeTypeEnum
     */
    private Integer rechargeType;

    /**
     * 充值金额(分)
     */
    private Integer rechargeAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 申请人名称
     */
    private String operatorName;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer auditStatus;

    /**
     * 审核理由
     */
    private String auditReason;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 是否能审核
     */
    private Integer canAudit;
}
