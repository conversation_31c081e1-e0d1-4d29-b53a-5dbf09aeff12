package com.ruoyi.system.vo.agent;

import lombok.Data;

import java.io.Serializable;

/**
 * 代理商的广告主余额记录VO
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@Data
public class AgentClientBalanceVO implements Serializable {
    private static final long serialVersionUID = 6989646300249056676L;

    /**
     * 广告主ID
     */
    private Long id;

    /**
     * 广告主公司名称
     */
    private String advertiserName;

    /**
     * 账号充值总金额(分)
     */
    private Integer rechargeAmount;

    /**
     * 账号消费总金额(分)
     */
    private Integer consumeAmount;

    /**
     * 账户总余额(分)
     */
    private Integer totalBalance;
}
