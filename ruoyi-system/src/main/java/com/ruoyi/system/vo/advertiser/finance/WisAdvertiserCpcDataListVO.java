package com.ruoyi.system.vo.advertiser.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主cpc数据列表
 *
 * <AUTHOR>
 * @date 2022/4/7 7:21 下午
 */
@Data
public class WisAdvertiserCpcDataListVO implements Serializable {
    private static final long serialVersionUID = 1705687513162553884L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告点击PV
     */
    private Integer billingClickPv;

    /**
     * 广告点击UV
     */
    private Integer billingClickUv;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 落地页转化PV
     */
    private Integer lpClickPv;

    /**
     * 落地页转化UV
     */
    private Integer lpClickUv;

    /**
     * 消费金额
     */
    private Integer consumeAmount;

    /**
     * 落地页转化成本
     */
    private String lpClickCost = "0";

    /**
     * 支付
     */
    private Integer pay;

    /**
     * 支付成本
     */
    private String payCost;
}
