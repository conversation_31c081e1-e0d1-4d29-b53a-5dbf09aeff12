package com.ruoyi.system.vo.advert;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 广告VO(用于下拉分页列表)
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdvertOrientSelectPageVO implements Serializable {
    private static final long serialVersionUID = -5237863923487758393L;

    /**
     * 广告ID
     */
    private Long id;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 配置名称
     */
    private String orientName;

    /**
     * 投放开关:0.关闭,1.开启
     */
    private Integer servingSwitch;
}
