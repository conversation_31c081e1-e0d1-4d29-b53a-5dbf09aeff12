package com.ruoyi.system.vo.wis;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主后台落地页记录VO
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Data
public class WisBaijiuLandpageFormRecordExcel implements Serializable {

    private static final long serialVersionUID = 1480595071175156831L;
    /**
     * 提交时间
     */
    @Excel(name = "时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date commitTime;
    /**
     * 支付金额
     */
    @Excel(name = "支付金额")
    private double amount;
    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;


    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phone;

    /**
     * 省
     */
    @Excel(name = "省份")
    private String province;

    /**
     * 市
     */
    @Excel(name = "市")
    private String city;

    /**
     * 区
     */
    @Excel(name = "区")
    private String district;

    /**
     * 详细地址
     */
    @Excel(name = "详细地址")
    private String address;


}
