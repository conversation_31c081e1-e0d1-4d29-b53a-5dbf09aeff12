package com.ruoyi.system.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理商充值记录VO
 *
 * <AUTHOR>
 * @date 2022/10/26
 */
@Data
public class AgentRechargeVO implements Serializable {
    private static final long serialVersionUID = 2598563843191937717L;

    /**
     * 充值ID
     */
    private Long id;

    /**
     * 广告主公司名称
     */
    private String advertiserName;

    /**
     * 充值类型
     *
     * @see AdvertiserRechargeTypeEnum
     */
    private Integer rechargeType;

    /**
     * 充值金额(分)
     */
    private Integer rechargeAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
