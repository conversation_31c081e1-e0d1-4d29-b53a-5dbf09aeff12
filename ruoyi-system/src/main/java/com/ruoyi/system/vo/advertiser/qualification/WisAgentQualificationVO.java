package com.ruoyi.system.vo.advertiser.qualification;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * [代理商平台]资质信息VO
 *
 * <AUTHOR>
 * @date 2023/03/30
 */
@Data
public class WisAgentQualificationVO implements Serializable {
    private static final long serialVersionUID = -7609550994995235131L;

    /**
     * 行业ID
     */
    private Long industryId;

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 行业状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer industryStatus;

    /**
     * 资质列表
     */
    private List<WisAgentQualificationVO.Qualification> qualificationList;

    @Data
    public static class Qualification {

        /**
         * 资质ID
         */
        private Long qualificationId;

        /**
         * 资质名称
         */
        private String qualificationName;

        /**
         * 资质图
         */
        private List<String> qualificationImgs;

        /**
         * 营业执照有效期 9999-12-31代表永久
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date expireTime;

        /**
         * 资质类型:0.默认资质,1.行业资质,2.自选资质
         */
        private Integer qualificationType;

        /**
         * 资质要求ID
         */
        private Long qualificationRequireId;

        /**
         * 审核状态:0.待审核,1.审核通过,2.审核拒绝
         */
        private Integer auditStatus;

        /**
         * 审核理由
         */
        private String auditReason;
    }
}
