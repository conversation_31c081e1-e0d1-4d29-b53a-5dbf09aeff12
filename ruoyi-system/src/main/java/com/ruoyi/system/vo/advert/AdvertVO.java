package com.ruoyi.system.vo.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.vo.advertiser.timer.AdvertiserOperateTimerSimpleVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告VO
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Data
public class AdvertVO implements Serializable {
    private static final long serialVersionUID = -1770760184116154670L;

    /**
     * 广告ID
     */
    private Long id;

    /**
     * 广告分类
     */
    private Integer advertCategory;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告主名称
     */
    private String advertiserName;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 投放开关
     */
    private Integer servingSwitch;

    /**
     * 创建人
     */
    private String operatorName;

    /**
     * 广告单价(分)
     */
    private Integer unitPrice;

    /**
     * 广告单价(毫)
     */
    private Integer milliUnitPrice;

    /**
     * 广告权重
     */
    private Double weight;

    /**
     * 行业列表
     */
    private List<String> industryList;

    /**
     * 行业资质齐全校验:true.齐全,false.缺失
     */
    private Boolean qualificationRequireCheck;

    /**
     * 行业资质审核校验:true.审核通过,false.审核未通过/未审核
     */
    private Boolean qualificationAuditCheck;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 定时操作列表
     */
    private List<AdvertiserOperateTimerSimpleVO> timerList;

    /**
     * 每日预算
     */
    private Integer dailyBudget;

    /**
     * 配置数量
     */
    private Integer orientCount;
}
