package com.ruoyi.system.vo.traffic;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 流量包列表VO
 *
 * <AUTHOR>
 * @date 2022-08-23
 */
@Data
public class TrafficPackageListVO implements Serializable {
    private static final long serialVersionUID = -8530771649945009252L;

    /**
     * 流量包ID
     */
    private Long id;

    /**
     * 流量包名称
     */
    private String name;

    /**
     * 流量包描述
     */
    private String desc;

    /**
     * 应用数量
     */
    private Integer appCount;

    /**
     * 广告位数量
     */
    private Integer slotCount;

    /**
     * 广告位ID列表
     */
    private List<Long> slotIds;

    /**
     * 广告数量
     */
    private Integer advertCount;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
