package com.ruoyi.system.vo.slot.shunt;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位切量计划导出VO
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class SlotShuntTaskExcelVO implements Serializable {
    private static final long serialVersionUID = -8612369811377241874L;

    @ExcelProperty("广告位ID")
    private Long slotId;

    @ExcelProperty("切量类型")
    private String shuntType;

    @ExcelProperty("计划名称")
    private String taskName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("开始时间")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("结束时间")
    private Date endTime;

    @ExcelProperty("切量类型")
    private String redirectType;

    @ExcelProperty("切量投放")
    private String redirectValue;

    @ExcelProperty("分流比例")
    private String shuntRatio;

    @ExcelProperty("状态")
    private String taskStatus;

    @ExcelProperty("切量上限")
    private Integer threshold;

    @ExcelProperty("广告位访问PV")
    private Integer slotRequestPv;

    @ExcelProperty("广告位访问UV")
    private Integer slotRequestUv;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("创建时间")
    private Date gmtCreate;
}
