package com.ruoyi.system.vo.contract;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 合同列表
 * <AUTHOR>
 * @date 2022/11/4 10:08 上午
 */
@Data
public class ContractListVO implements Serializable {
    private static final long serialVersionUID = -706572072387845232L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 甲方主体
     */
    private String planAName;

    /**
     * 乙方主体账号id
     */
    private Long accountId;
    /**
     * 乙方主体名称
     */
    private String companyName;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 合同有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 合同有效期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人id
     */
    private Long operatorId;
    /**
     * 操作人名
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 距离合同到期相差天数
     */
    private Integer dateDiff;

    /**
     * 合同状态 0代表无无合同
     * @see com.ruoyi.common.enums.contract.ContractStatusEnum
     */
    private Integer contractStatus = 0;


}
