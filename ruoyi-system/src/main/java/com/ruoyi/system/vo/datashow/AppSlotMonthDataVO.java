package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体月账单广告位数据
 * <AUTHOR>
 * @date 2023/4/7 14:10
 */
@Data
public class AppSlotMonthDataVO implements Serializable {
    private static final long serialVersionUID = 3633251273356180710L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;
    /**
     * 广告位id
     */
    private Long slotId;
    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;
    /**
     * 广告位访问pv
     */
    private Integer slotRequestUv;
    /**
     * 诺禾结算款
     */
    private Long nhCost;
    /**
     * 外部结算款
     */
    private Long outerCost;
    /**
     * 媒体应得收入
     */
    private Long appRevenue;

    /**
     * 计费类型
     * @see com.ruoyi.common.enums.slot.SlotChargeTypeEnum
     */
    private Integer chargeType;

    /**
     * 计费价格
     */
    private Integer chargePrice;
}
