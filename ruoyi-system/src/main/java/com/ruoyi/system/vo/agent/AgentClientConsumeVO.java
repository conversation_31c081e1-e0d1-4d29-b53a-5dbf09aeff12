package com.ruoyi.system.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主财务汇总记录VO
 *
 * <AUTHOR>
 * @date 2022/10/27
 */
@Data
public class AgentClientConsumeVO implements Serializable {
    private static final long serialVersionUID = 7367985164087044497L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告主名称
     */
    private String advertiserName;

    /**
     * 消费金额(分)
     */
    private Integer consumeAmount;
}
