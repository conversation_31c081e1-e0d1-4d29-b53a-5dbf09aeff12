package com.ruoyi.system.vo.activity;

import lombok.Data;

import java.io.Serializable;

/**
 * 活动皮肤模板
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
@Data
public class JsTemplateVO implements Serializable {
    private static final long serialVersionUID = -5977829270378022916L;

    /**
     * [大转盘/翻牌子]背景图
     */
    private String bgImage;

    /**
     * [大转盘]大转盘底座
     */
    private String wheelPedestal;

    /**
     * [大转盘]大转盘转轮图
     */
    private String wheelPrizeBg;

    /**
     * [大转盘]大转盘子背景图
     */
    private String wheelBg;

    /**
     * [大转盘]大转盘按钮图
     */
    private String btn;

    /**
     * [大转盘]抽奖按钮指针
     */
    private String btnPedestal;

    /**
     * 前置红包开关
     */
    private Boolean preRedSwitch;
}
