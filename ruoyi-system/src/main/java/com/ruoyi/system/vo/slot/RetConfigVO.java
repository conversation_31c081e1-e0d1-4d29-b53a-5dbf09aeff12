package com.ruoyi.system.vo.slot;

import lombok.Data;

import java.io.Serializable;

/**
 * 返回挽留设置VO
 *
 * <AUTHOR>
 * @date 2021/12/28
 */
@Data
public class RetConfigVO implements Serializable, Cloneable {
    private static final long serialVersionUID = 5680166135383058186L;

    /**
     * 插件开关
     * @see com.ruoyi.common.enums.plugin.PluginSwitchEnum
     */
    private Integer isOpen;

    /**
     * 插件ID
     */
    private Long pluginId;

    /**
     * 插件类型
     * @see com.ruoyi.common.enums.plugin.PluginTypeEnum
     */
    private Integer pluginType;

    /**
     * 链接
     */
    private String url;

    /**
     * 活动ID
     */
    private Long activityId;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
