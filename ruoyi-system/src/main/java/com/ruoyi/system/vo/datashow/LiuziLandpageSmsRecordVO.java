package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 留资落地页短信发发送录VO
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Data
public class LiuziLandpageSmsRecordVO implements Serializable {

    private static final long serialVersionUID = 2295848191971638455L;

    /**
     * 模版id
     */
    private String tpId;
    /**
     * 渠道类型
     * @see com.ruoyi.common.enums.SmsChannelEnum
     */
    private Integer type;
    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 短信内容
     */
    private String content;

    /**
     * 发送状态:0发送中,1成功,2失败,3未知
     */
    private Integer result;
    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

}
