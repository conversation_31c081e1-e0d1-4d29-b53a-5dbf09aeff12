package com.ruoyi.system.vo.datashow;

import lombok.Data;

import java.io.Serializable;

/**
 * 媒体月数据vo crm专用
 *
 * <AUTHOR>
 * @date 2021/9/1 2:42 下午
 */
@Data
public class CrmAppMonthDataVO implements Serializable {
    private static final long serialVersionUID = -6435572961561929853L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 月份 日期
     */
    private Integer monthDate;
    /**
     * 账号id
     */
    private Long accountId;
    /**
     * 邮箱账号
     */
    private String email;
    /**
     * 媒体id
     */
    private Long appId;
    /**
     * 媒体名称
     */
    private String appName;
    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;
    /**
     * 广告位访问uv 处理后的
     */
    private Integer slotRequestUvCalculate;

    /**
     * 活动参与pv
     */
    private Integer joinPv;

    /**
     * 活动参与uv
     */
    private Integer joinUv;

    /**
     * 总消耗(分)
     */
    private Integer totalConsume;

    /**
     * 诺禾广告消耗(分)
     */
    private Integer nhConsume;

    /**
     * 外部广告消耗(分)
     */
    private Integer outerConsume;

    /**
     * 媒体应得收益(分)
     */
    private Integer appRevenue;

    /**
     * 结算状态
     */
    private Integer confirmStatus;
    /**
     * 诺禾结算款(分)
     */
    private Long nhCost;
    /**
     * 外部结算款(分)
     */
    private Long outerCost;
    /**
     * 结算总金额(分)
     */
    private Long totalCost;

}
