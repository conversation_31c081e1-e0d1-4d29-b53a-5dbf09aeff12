package com.ruoyi.system.vo.wis;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主后台落地页记录VO
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Data
public class WisLandpageFormRecordVO implements Serializable {
    private static final long serialVersionUID = -6305577510779901526L;

    /**
     * 提交时间
     */
    @Excel(name = "时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date commitTime;

    /**
     * 表单类型
     */
    private String isp;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCard;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phone;

    /**
     * 省
     */
    @Excel(name = "省份")
    private String province;

    /**
     * 市
     */
    @Excel(name = "市")
    private String city;

    /**
     * 区
     */
    @Excel(name = "区")
    private String district;

    /**
     * 详细地址
     */
    @Excel(name = "详细地址")
    private String address;
}
