package com.ruoyi.system.vo.open;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 微博获取token返回结果
 *
 * <AUTHOR>
 * @date 2022/07/19
 */
@Data
public class WeiboTokenData implements Serializable {
    private static final long serialVersionUID = 5228915464584814964L;

    /**
     * accessToken值
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * accessToken剩余有效时间(秒)
     */
    @JsonProperty("expires_in")
    private Integer expiresIn;

    /**
     * refreshToken值
     */
    @JsonProperty("refresh_token")
    private String refreshToken;

    /**
     * refreshToken剩余有效时间(秒)
     */
    @JsonProperty("refresh_expires_in")
    private Integer refreshExpiresIn;

    /**
     * token类型
     */
    @JsonProperty("token_type")
    private String tokenType;
}
