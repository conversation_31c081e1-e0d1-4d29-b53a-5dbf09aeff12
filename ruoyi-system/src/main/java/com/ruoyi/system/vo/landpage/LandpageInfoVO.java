package com.ruoyi.system.vo.landpage;

import lombok.Data;

import java.io.Serializable;

/**
 * 落地页配置详情
 *
 * <AUTHOR>
 * @date 2022/9/19 4:55 下午
 */
@Data
public class LandpageInfoVO implements Serializable {
    private static final long serialVersionUID = 7458463455773739734L;

    /**
     * 落地页页面配置
     */
    private String pageConfig;

    /**
     * 皮肤类型
     * @see com.ruoyi.common.enums.landpage.LandPageSkinTypeEnum
     */
    private Integer skinType;

    /**
     * 备案号
     */
    private String icp;

    /**
     * 备案主体
     */
    private String icpSubject;

    /**
     * 手机品牌
     */
    private String mobileBrand;

    /**
     * 秘钥，用于发送验证码的签名
     */
    private String secret;
}
