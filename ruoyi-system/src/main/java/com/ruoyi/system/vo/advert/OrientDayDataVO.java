package com.ruoyi.system.vo.advert;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 配置日数据VO
 *
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
public class OrientDayDataVO extends OrientBaseDataVO implements Serializable {
    private static final long serialVersionUID = -7978023002796176561L;

    /**
     * 配置状态
     */
    @Excel(name = "配置状态", sort = 15)
    private String statusStr;

    /**
     * 配置预算
     */
    @Excel(name = "配置预算", sort = 16)
    private String budget;

    /**
     * 负责人
     */
    @Excel(name = "负责人", width = 40)
    private String managerName;
}
