package com.ruoyi.system.vo.advertiser.qualification;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 资质信息
 *
 * <AUTHOR>
 * @date 2022/4/28 5:32 下午
 */
@Data
public class AdvertiserQualificationInfoVO implements Serializable {
    private static final long serialVersionUID = 1828776834794975853L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 行业ID
     */
    private Long industryId;

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 资质名称
     */
    private String qualificationName;

    /**
     * 资质图
     */
    private List<String> qualificationImgs;

    /**
     * 营业执照有效期 9999-12-31代表永久
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireTime;

    /**
     * 备注
     */
    private String remarkText;

    /**
     * 资质类型:0.默认资质,1.行业资质,2.自选资质
     */
    private Integer qualificationType;

    /**
     * 资质要求ID
     */
    private Long qualificationRequireId;

    /**
     * 提交审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applicationTime;

    /**
     * 审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer auditStatus;

    /**
     * 审核人ID
     */
    private Long auditor;

    /**
     * 审核人姓名
     */
    private String auditorName;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 审核理由
     */
    private String auditReason;
}
