package com.ruoyi.system.vo.account;

import com.ruoyi.common.enums.account.AdminTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * 登录用户 vo
 *
 * <AUTHOR>
 * @date 2023/6/30 10:18
 */
@Data
public class LoginUserVo implements Serializable {
    private static final long serialVersionUID = 2947386225953509358L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户性别
     */
    private String sex;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 主体类型
     */
    private Integer mainType;

    /**
     * 管理员类型
     *
     * @see com.ruoyi.common.enums.account.AdminTypeEnum
     */
    private Integer adminType;

    /**
     * 是否展示广告位曝光列(SSP平台使用)
     */
    private Boolean showSlotExposure;

    /**
     * 丰巢文章审核菜单展示开关
     */
    private Boolean fcArticleCheckSwitch;

    public LoginUserVo() {

    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public Date getLoginDate() {
        return loginDate;
    }

    public void setLoginDate(Date loginDate) {
        this.loginDate = loginDate;
    }

    public Integer getMainType() {
        return mainType;
    }

    public void setMainType(Integer mainType) {
        this.mainType = mainType;
    }

    public Set<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(Set<String> permissions) {
        this.permissions = permissions;
    }

    public Integer getAdminType() {
        return adminType;
    }

    public void setAdminType(Integer adminType) {
        this.adminType = adminType;
    }

    public boolean isAdmin() {
        return Objects.equals(adminType, AdminTypeEnum.ADMIN.getType());
    }

    public Boolean getShowSlotExposure() {
        return showSlotExposure;
    }

    public void setShowSlotExposure(Boolean showSlotExposure) {
        this.showSlotExposure = showSlotExposure;
    }
}
