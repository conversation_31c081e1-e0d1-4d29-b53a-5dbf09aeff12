package com.ruoyi.system.vo.advertiser;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 落地页表单规则VO
 *
 * <AUTHOR>
 * @date 2022/03/15
 */
@Data
public class AdvertiserLpCallbackRuleVO implements Serializable {
    private static final long serialVersionUID = 1313823359427492286L;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 地域编码列表
     */
    private List<String> areaList;

    /**
     * 年龄下限(包含)
     */
    private Integer ageMin;

    /**
     * 年龄上限(包含)
     */
    private Integer ageMax;

    /**
     * 每日表单量上限
     */
    private Integer dailyLimit;
}
