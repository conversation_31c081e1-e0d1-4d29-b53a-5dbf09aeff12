package com.ruoyi.system.vo.advertiser.qualification;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * [广告主平台]客户信息VO
 *
 * <AUTHOR>
 * @date 2023/03/29
 */
@Data
public class WisAdvertiserCompanyInfoVO {

    /**
     * 公司全名
     */
    private String companyName;

    /**
     * 营业执照注册号
     */
    private String businessLicense;

    /**
     * 营业执照照片
     */
    private String businessLicenseImg;

    /**
     * 营业执照有效期 9999-12-31代表永久
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireTime;

    /**
     * 审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer auditStatus;

    /**
     * 审核理由
     */
    private String auditReason;

    /**
     * 联系人名称
     */
    private String contract;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 联系邮箱
     */
    private String email;
}
