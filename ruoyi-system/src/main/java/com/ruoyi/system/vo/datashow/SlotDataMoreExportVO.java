package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位数据对象 tb_slot_data
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Data
public class SlotDataMoreExportVO implements Serializable {
    private static final long serialVersionUID = 8563835165636589386L;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    @Excel(name = "媒体名称/媒体ID")
    private String appName;

    @Excel(name = "广告位名称/广告位ID")
    private String slotName;

    @Excel(name = "广告位曝光")
    private Integer appSlotExposurePv;

    @Excel(name = "广告位点击")
    private Integer appSlotClickPv;

    @Excel(name = "点击率")
    private String slotClickRate;

    @Excel(name = "广告位访问pv")
    private Integer slotRequestPv;

    @Excel(name = "广告位访问uv")
    private Integer slotRequestUv;

    @Excel(name = "收益")
    private String appRevenue;
}
