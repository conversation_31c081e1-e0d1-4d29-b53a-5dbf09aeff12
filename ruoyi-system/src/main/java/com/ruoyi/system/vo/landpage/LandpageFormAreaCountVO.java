package com.ruoyi.system.vo.landpage;

import lombok.Data;

import java.io.Serializable;

/**
 * 落地页表单地域分析VO
 *
 * <AUTHOR>
 * @date 2022-08-08
 */
@Data
public class LandpageFormAreaCountVO implements Serializable {
    private static final long serialVersionUID = 325038576969846241L;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 订单数
     */
    private Integer formCount;

    /**
     * 消耗(分)
     */
    private Integer consume;

    /**
     * 激活数
     */
    private Integer register;

    /**
     * 首充数
     */
    private Integer pay;

    /**
     * 激活率
     */
    private String activeRate;

    /**
     * 激活成本
     */
    private String registerCost;

    /**
     * 综转成本
     */
    private String payCost;
}
