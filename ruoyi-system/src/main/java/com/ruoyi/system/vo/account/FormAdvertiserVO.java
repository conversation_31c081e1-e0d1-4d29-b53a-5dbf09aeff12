package com.ruoyi.system.vo.account;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 表单广告主VO
 *
 * <AUTHOR>
 * @date 2022/03/11
 */
@Data
public class FormAdvertiserVO implements Serializable {
    private static final long serialVersionUID = -8808828601507967869L;

    /**
     * 账号ID
     */
    private Long id;

    /**
     * 广告主公司名称
     */
    private String advertiserName;

    /**
     * 结算类型
     */
    private Integer consumeType;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 表单价格(分)
     */
    private Integer formPrice;

    /**
     * 计费类型:0.有效表单,1.毛表单
     */
    private Integer priceType;

    /**
     * 权重
     */
    private Double weight;

    /**
     * 回传链接
     */
    private String lpCallbackUrl;

    /**
     * 账号状态:0.正常,1.冻结
     */
    private Integer status;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
