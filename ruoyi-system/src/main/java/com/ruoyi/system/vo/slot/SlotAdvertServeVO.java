package com.ruoyi.system.vo.slot;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位的的广告配置可投放状态VO
 *
 * <AUTHOR>
 * @date 2023-09-12
 */
@Data
public class SlotAdvertServeVO implements Serializable {
    private static final long serialVersionUID = -2108262217184278847L;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 是否正常投放
     */
    private Integer canServe;

    /**
     * 不可投放原因
     */
    private String reason;
}
