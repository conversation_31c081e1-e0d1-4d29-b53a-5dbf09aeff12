package com.ruoyi.system.vo.withdraw;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 提现单详情
 *
 * <AUTHOR>
 * @date 2021/9/14 5:42 下午
 */
@Data
public class WithdrawInfoVO implements Serializable {
    private static final long serialVersionUID = -4923797066063088735L;

    /**
     * 提现资质快照信息
     */
    private WithdrawQualificationVO qualificationInfo;

    /**
     * 提现金额
     */
    private Integer withdrawAmount;

    /**
     * 提现单明细
     */
    private List<WithdrawAppDataListVO> dataLists;

    /**
     * 提现文件地址
     */
    private String withdrawFile;
    /**
     * 发票快递信息
     */
    private WithdrawInvoiceAddressInfoVO addressInfoVO = new WithdrawInvoiceAddressInfoVO();
    /**
     * 开票信息
     */
    private WithdrawInvoiceInfoVO invoiceInfoVO = new WithdrawInvoiceInfoVO();
}
