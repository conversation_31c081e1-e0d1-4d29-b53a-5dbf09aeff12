package com.ruoyi.system.vo.open;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 广点通获取token返回结果
 *
 * <AUTHOR>
 * @date 2022/07/28
 */
@Data
public class QqTokenData implements Serializable {
    private static final long serialVersionUID = 8298962834644772860L;

    /**
     * accessToken值
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * accessToken剩余有效时间(秒)
     */
    @JsonProperty("access_token_expires_in")
    private Integer accessTokenExpiresIn;

    /**
     * refreshToken值
     */
    @JsonProperty("refresh_token")
    private String refreshToken;

    /**
     * refreshToken剩余有效时间(秒)
     */
    @JsonProperty("refresh_token_expires_in")
    private Integer refreshTokenExpiresIn;
}
