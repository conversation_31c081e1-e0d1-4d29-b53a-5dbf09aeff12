package com.ruoyi.system.vo.slot;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位投放的广告信息VO
 *
 * <AUTHOR>
 * @date 2022-05-10
 */
@Data
public class SlotAdvertVO implements Serializable {
    private static final long serialVersionUID = 4780664604102715359L;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告位名称
     */
    private String advertName;

    /**
     * 配置ID
     */
    private Long orientId;

    /**
     * 配置名称
     */
    private String orientName;

    /**
     * 广告出价(分)
     */
    private Integer unitPrice;

    /**
     * 广告出价(毫)
     */
    private Integer milliUnitPrice;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 排序因子=权重*计费单价
     */
    private Double orderFactor;

    /**
     * CTR
     */
    private Double ctr;

    /**
     * 预估广告出价
     */
    private Double pCpc;

    /**
     * 预估ARPU
     */
    private Double arpu;

    /**
     * 权重
     */
    private Double weight;

    /**
     * 考核成本(分)
     */
    private Integer assessCost;

    /**
     * 是否正常投放
     */
    private Integer canServe;

    /**
     * 不可投放原因
     */
    private String reason;
}
