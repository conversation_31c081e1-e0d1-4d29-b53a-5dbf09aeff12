package com.ruoyi.system.vo.datashow;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位数据导出vo crm专用
 *
 * <AUTHOR>
 * @date 2021/9/1 2:42 下午
 */
@Data
public class CrmSlotDataExportVO implements Serializable {
    private static final long serialVersionUID = 8981014871313770859L;

    /**
     * 日期
     */
    @Excel(name = "日期", width = 20, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告位名称
     */
    @Excel(name = "广告位名称/广告位ID", width = 40)
    private String slotName;

    /**
     * 媒体名称
     */
    @Excel(name = "媒体名称/媒体ID", width = 40)
    private String appName;

    /**
     * 总消耗
     */
    private String totalConsume;

    /**
     * 诺禾广告消耗(分)
     */
    private String nhConsume;

    /**
     * 外部广告消耗(分)
     */
    private String outerConsume;

    /**
     * 总收益(分)
     */
    @Excel(name = "媒体应得收入(元)", cellType = Excel.ColumnType.NUMERIC)
    private String appRevenue;


    /**
     * 表单量
     */
    @Excel(name = "表单量", cellType = Excel.ColumnType.NUMERIC)
    private Integer formCount;

    /**
     * 结算款(诺禾)
     */
    @Excel(name = "结算款(诺禾)", cellType = Excel.ColumnType.NUMERIC)
    private String nhCost;

    @Excel(name = "结算款(外部)", cellType = Excel.ColumnType.NUMERIC)
    private String outerCost;

    /**
     * 总结算款
     */
    @Excel(name = "结算总金额", cellType = Excel.ColumnType.NUMERIC)
    private String totalCost;

    /**
     * 分成比例 (媒体应得收入/诺禾总消耗)
     */
    @Excel(name = "分成比例")
    private String divideRate;

    @Excel(name = "投流消耗", cellType = Excel.ColumnType.NUMERIC)
    private String biddingConsume;

    @Excel(name = "投流上报", cellType = Excel.ColumnType.NUMERIC)
    private Integer biddingConv;

    @Excel(name = "投流成本", cellType = Excel.ColumnType.NUMERIC)
    private String biddingCost;

    /**
     * 媒体CPM (媒体应得收入/广告位曝光*1000)
     */
    @Excel(name = "媒体cpm", cellType = Excel.ColumnType.NUMERIC)
    private String appCpm;

    /**
     * 诺禾cpm (诺禾总消耗/广告位曝光*1000)
     */
    @Excel(name = "诺禾cpm", cellType = Excel.ColumnType.NUMERIC)
    private String nhCpm;

    /**
     * 入口CPC(元)
     */
    @Excel(name = "入口CPC", cellType = Excel.ColumnType.NUMERIC)
    private String entryCpc;

    /**
     * 媒体cpc (媒体应得收入/广告位点击pv)
     */
    @Excel(name = "媒体cpc", cellType = Excel.ColumnType.NUMERIC)
    private String appCpc;

    /**
     * 诺禾每uv收益 (诺禾总消耗/广告位请求uv)
     */
    @Excel(name = "诺禾每uv收益", cellType = Excel.ColumnType.NUMERIC)
    private String nhRevenuePerUv;

    /**
     * 媒体每uv收益 (媒体应得收入/广告位请求uv)
     */
    @Excel(name = "媒体每uv收益", cellType = Excel.ColumnType.NUMERIC)
    private String appRevenuePerUv;

    /**
     * 广告位访问pv
     */
    @Excel(name = "广告位访问pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    @Excel(name = "广告位访问uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer slotRequestUv;

    /**
     * 广告位点击次数（广告位请求pv/广告位请求uv）
     */
    @Excel(name = "广告位每uv点击次数", cellType = Excel.ColumnType.NUMERIC)
    private String slotRequestPerUv;

    /**
     * 广告位点击pv(媒体反馈)
     */
    @Excel(name = "广告位点击pv(媒体反馈)", cellType = Excel.ColumnType.NUMERIC)
    private Integer appSlotClickPv;

    /**
     * 广告位点击uv(媒体反馈)
     */
    @Excel(name = "广告位点击uv(媒体反馈)", cellType = Excel.ColumnType.NUMERIC)
    private Integer appSlotClickUv;

    /**
     * 广告位曝光pv(媒体反馈)
     */
    @Excel(name = "广告位曝光pv(媒体反馈)", cellType = Excel.ColumnType.NUMERIC)
    private Integer appSlotExposurePv;

    /**
     * 广告位曝光pv(媒体反馈)
     */
    @Excel(name = "广告位曝光pv(媒体反馈)", cellType = Excel.ColumnType.NUMERIC)
    private Integer appSlotExposureUv;

    /**
     * 广告位点击率 (广告位点击pv(媒体反馈)/广告位曝光pv(媒体反馈))
     */
    private String slotClickRate;

    /**
     * 活动访问pv
     */
    @Excel(name = "活动访问pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer activityRequestPv;

    /**
     * 活动访问uv
     */
    @Excel(name = "活动访问uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer activityRequestUv;

    /**
     * 活动到达率（活动请求uv/广告位请求uv）
     */
    @Excel(name = "活动到达率(活动请求uv/广告位请求uv)")
    private String actArriveRate;

    /**
     * 广告位切量，留空
     */
    @Excel(name = "广告位切量uv")
    private String slotRequestUvCalculate;

    /**
     * 活动参与pv
     */
    @Excel(name = "参与pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer joinPv;

    /**
     * 活动参与uv
     */
    @Excel(name = "参与uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer joinUv;

    /**
     * 参与率（活动参与uv/活动访问uv）
     */
    @Excel(name = "参与率(活动参与uv/活动访问uv)")
    private String joinRate;

    /**
     * 复参
     */
    @Excel(name = "复参", cellType = Excel.ColumnType.NUMERIC)
    private String rejoin;

    /**
     * 券请求
     */
    @Excel(name = "广告请求pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adRequest;

    /**
     * 发券pv
     */
    @Excel(name = "发券pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adLaunch;

    /**
     * 发券成功率(发券/券请求)
     */
    @Excel(name = "发券成功率(发券/券请求)")
    private String launchRate;

    @Excel(name = "ARPU值(消耗/发券pv)", cellType = Excel.ColumnType.NUMERIC)
    private String arpu;

    /**
     * 券曝光Pv
     */
    @Excel(name = "广告曝光Pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adExposurePv;

    /**
     * 券曝光Uv
     */
    @Excel(name = "广告曝光Uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adExposureUv;

    /**
     * 券点击Pv
     */
    @Excel(name = "广告点击Pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adClickPv;

    /**
     * 券点击Uv
     */
    @Excel(name = "广告点击Uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adClickUv;

    /**
     * CTR（券点击pv/券曝光pv）
     */
    @Excel(name = "CTR(券点击pv/券曝光pv)")
    private String ctr;

    /**
     * 每UV发券（发券pv/活动访问uv）
     */
    @Excel(name = "每UV发券(发券pv/活动访问uv)", cellType = Excel.ColumnType.NUMERIC)
    private String adLaunchPerUv;

    /**
     * 每UV券点击（券点击uv/活动访问uv）
     */
    @Excel(name = "每UV券点击(券点击uv/活动访问uv)", cellType = Excel.ColumnType.NUMERIC)
    private String adClickPerUv;

    /**
     * 落地页曝光pv
     */
    @Excel(name = "落地页曝光pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    @Excel(name = "落地页曝光uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpExposureUv;

    /**
     * 落地页转化pv
     */
    @Excel(name = "落地页转化pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpClickPv;

    /**
     * 落地页转化uv
     */
    @Excel(name = "落地页转化uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpClickUv;

    /**
     * CVR（落地页转化pv/落地页曝光pv）
     */
    @Excel(name = "CVR(落地页转化pv/落地页曝光pv)")
    private String cvrPv;

    /**
     * CVR（落地页转化uv/落地页曝光uv）
     */
    @Excel(name = "CVR(落地页转化uv/落地页曝光uv)")
    private String cvrUv;

    @Excel(name = "激活", cellType = Excel.ColumnType.NUMERIC)
    private Integer register;

    @Excel(name = "支付", cellType = Excel.ColumnType.NUMERIC)
    private Integer pay;

    @Excel(name = "退款", cellType = Excel.ColumnType.NUMERIC)
    private Integer refund;

    @Excel(name = "APP激活", cellType = Excel.ColumnType.NUMERIC)
    private Integer appActive;

    @Excel(name ="支付金额", cellType = Excel.ColumnType.NUMERIC)
    private Double extPrice;

    /**
     * 负责人名称
     */
    @Excel(name = "负责人", width = 40)
    private String managerName;
}
