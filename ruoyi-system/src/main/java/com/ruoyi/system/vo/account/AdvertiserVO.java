package com.ruoyi.system.vo.account;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.vo.advertiser.qualification.AdvertiserQualificationVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 广告主VO
 *
 * <AUTHOR>
 * @date 2021/10/8
 */
@Data
public class AdvertiserVO implements Serializable {
    private static final long serialVersionUID = 6857818606700700319L;

    /**
     * 账号ID
     */
    private Long id;

    /**
     * 类型:2.广告主,4.代理商
     */
    private Integer mainType;

    /**
     * 注册邮箱
     */
    private String email;

    /**
     * 广告主公司名称
     */
    private String advertiserName;

    /**
     * 子广告主的代理商ID
     */
    private Long agentId;

    /**
     * 代理商公司名称
     */
    private String agentName;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 公钥
     */
    private String accessKey;

    /**
     * 秘钥
     */
    private String secretKey;

    /**
     * 负责人名称
     */
    private String managerName;

    /**
     * 商务
     */
    private List<Long> bdManagerIds;

    /**
     * 运营
     */
    private List<Long> operationManagerIds;

    /**
     * 账号状态:0.正常,1.冻结
     */
    private Integer status;

    /**
     * 资质信息
     */
    private List<AdvertiserQualificationVO> qualificationList;

    /**
     * 行业列表
     */
    private List<String> industryList;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 合同状态 0代表无无合同
     * @see com.ruoyi.common.enums.contract.ContractStatusEnum
     */
    private Integer contractStatus = 0;

    /**
     * 距离合同到期相差天数
     */
    private Integer dateDiff;

    /**
     * 是否可指派负责人
     */
    private Integer canAssign;

    /**
     * 结算类型
     * @see com.ruoyi.common.enums.advertiser.AdvertiserConsumeType
     */
    private Integer consumeType;

    /**
     * 广告主今日消耗(分)
     */
    private Long consume;

    /**
     * 广告主今日预算(分)，null为不限
     */
    private Long budget;
}
