package com.ruoyi.system.vo.landpage;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 落地页标签VO(用于下拉列表)
 *
 * <AUTHOR>
 * @date 2023-11-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LandpageTagSelectVO implements Serializable {
    private static final long serialVersionUID = -1934302962137537929L;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 子标签列表
     */
    private List<LandpageTagSelectVO> childTags;

    public LandpageTagSelectVO(Long tagId, String tagName) {
        this.tagId = tagId;
        this.tagName = tagName;
    }
}
