package com.ruoyi.system.vo.wis;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * [广告主平台]信用卡表单VO
 *
 * <AUTHOR>
 * @date 2023/6/5
 */
@Data
public class WisCreditCardLandpageFormRecordVO implements Serializable {
    private static final long serialVersionUID = 48122883982843211L;

    @Excel(name = "姓名")
    private String name;

    @Excel(name = "身份证号", width = 30)
    private String idCard;

    @Excel(name = "申办额度")
    private Integer creditLimit;

    @Excel(name = "详细地址", width = 30)
    private String address;

    @Excel(name = "手机号")
    private String phone;

    @Excel(name = "时间", dateFormat = "yyyy-MM-dd HH:mm:ss", width = 20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date commitTime;
}
