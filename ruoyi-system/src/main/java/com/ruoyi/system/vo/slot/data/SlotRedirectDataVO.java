package com.ruoyi.system.vo.slot.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位投放数据VO
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
@Data
public class SlotRedirectDataVO implements Serializable {
    private static final long serialVersionUID = 2013125237146191930L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 广告位id
     */
    private Long slotId;
    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 投放类型
     * @see com.ruoyi.common.enums.slot.SlotRedirectType
     */
    private Integer redirectType;

    /**
     * 投放信息
     */
    private String redirectValue;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;
}
