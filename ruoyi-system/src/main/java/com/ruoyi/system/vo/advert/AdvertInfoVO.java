package com.ruoyi.system.vo.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.bo.advert.AdvertExtInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告基础信息VO
 *
 * <AUTHOR>
 * @date 2022-03-29
 */
@Data
public class AdvertInfoVO implements Serializable {
    private static final long serialVersionUID = 263965248472789001L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告分类
     * @see com.ruoyi.common.enums.advert.AdvertCategory
     */
    private Integer advertCategory;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 广告缩略图
     */
    private String thumbnailImg;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 考核指标
     */
    private Integer assessType;

    /**
     * 考核成本(分)
     */
    private Integer assessCost;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 点击监测链接
     */
    private String clickCallbackUrl;

    /**
     * 落地页数据回传链接
     */
    private String lpCallbackUrl;

    /**
     * 每日预算
     */
    private Integer dailyBudget;

    /**
     * 开始投放日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startServingDate;

    /**
     * 结束投放日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date stopServingDate;

    /**
     * 投放开关:0.关闭,1.开启
     */
    private Integer servingSwitch;

    /**
     * 状态:0.正常,1.无效,2.无效(预算不足)
     */
    private Integer advertStatus;

    /**
     * 额外信息
     */
    private AdvertExtInfo extInfo;

    /**
     * 创建人ID
     */
    private Long operatorId;

    /**
     * 创建人名称
     */
    private String operatorName;

    /**
     * 权重
     */
    private Double weight;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 计费类型
     */
    private Integer billingType;

    /**
     * 成本
     */
    private Integer cost;
}
