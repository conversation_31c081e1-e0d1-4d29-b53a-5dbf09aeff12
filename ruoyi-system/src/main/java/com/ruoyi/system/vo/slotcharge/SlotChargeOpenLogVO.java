package com.ruoyi.system.vo.slotcharge;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位
 * <AUTHOR>
 * @date 2022/3/8 5:04 下午
 */
@Data
public class SlotChargeOpenLogVO implements Serializable {
    private static final long serialVersionUID = 625326770819694190L;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 计费类型
     */
    private Integer chargeType;

    /**
     * 计费价格
     */
    private Integer chargePrice;

    /**
     * 操作人
     */
    private String operName;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;
}
