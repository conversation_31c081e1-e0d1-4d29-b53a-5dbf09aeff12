package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 广告位数据vo crm专用
 *
 * <AUTHOR>
 * @date 2021/9/1 2:42 下午
 */
@Data
public class CrmSlotDataVO implements Serializable {
    private static final long serialVersionUID = -6929389565494836972L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 媒体id
     */
    private Long appId;

    /**
     * 广告位id
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 负责人名称
     */
    private String managerName;

    /**
     * 总消耗(分)
     */
    private Long totalConsume;

    /**
     * 媒体应得收益(分)
     */
    private Long appRevenue;

    /**
     * 诺禾广告消耗(分)
     */
    private Long nhConsume;

    /**
     * 外部广告消耗(分)
     */
    private Long outerConsume;

    /**
     * 外部收益(分)
     */
    private Long outerRevenue;

    /**
     * 表单量
     */
    private Integer formCount;

    /**
     * 结算款(诺禾)(分)
     */
    private Long nhCost;

    /**
     * 外部结算款(分)
     */
    private Long outerCost;

    /**
     * 总结算款
     */
    private Long totalCost;

    /**
     * 入口CPC(元)
     */
    private String entryCpc;

    /**
     * 投流消耗(分)
     */
    private Long biddingConsume;

    /**
     * 投流上报
     */
    private Integer biddingConv;

    /**
     * 投流成本
     */
    private String biddingCost;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;

    /**
     * 广告位点击pv(媒体反馈)
     */
    private Integer appSlotClickPv;
    /**
     * 广告位点击uv(媒体反馈)
     */
    private Integer appSlotClickUv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposurePv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposureUv;

    /**
     * 广告位每uv点击次数
     */
    private String slotRequestPerUv;

    /**
     * 活动访问pv
     */
    private Integer activityRequestPv;

    /**
     * 活动访问uv
     */
    private Integer activityRequestUv;

    /**
     * 活动到达率（活动请求uv/广告位请求uv）
     */
    private String actArriveRate;

    /**
     * 活动参与pv
     */
    private Integer joinPv;

    /**
     * 活动参与uv
     */
    private Integer joinUv;

    /**
     * 参与率（活动参与uv/活动访问uv）
     */
    private String joinRate;

    /**
     * 复参
     */
    private String rejoin;

    /**
     * 券请求
     */
    private Integer adRequest;

    /**
     * 发券
     */
    private Integer adLaunch;

    /**
     * 发券成功率(发券/券请求)
     */
    private String launchRate;

    /**
     * ARPU值（消耗/发券pv）
     */
    private String arpu;

    /**
     * 券曝光Pv
     */
    private Integer adExposurePv;

    /**
     * 券曝光Uv
     */
    private Integer adExposureUv;

    /**
     * 券点击Pv
     */
    private Integer adClickPv;

    /**
     * 券点击Uv
     */
    private Integer adClickUv;

    /**
     * CTR（券点击pv/券曝光pv）
     */
    private String ctr;

    /**
     * 每UV发券（发券pv/活动访问uv）
     */
    private String adLaunchPerUv;

    /**
     * 每UV券点击（券点击uv/活动访问uv）
     */
    private String adClickPerUv;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页转化pv
     */
    private Integer lpClickPv;

    /**
     * 落地页转化uv
     */
    private Integer lpClickUv;

    /**
     * CVR（落地页转化pv/落地页曝光pv）
     */
    private String cvrPv;

    /**
     * CVR（落地页转化uv/落地页曝光uv）
     */
    private String cvrUv;

    /**
     * 激活
     */
    private Integer register;

    /**
     * 支付
     */
    private Integer pay;

    /**
     * 退款
     */
    private Integer refund;

    /**
     * APP激活
     */
    private Integer appActive;

    /**
     * 支付金额
     */
    private BigDecimal extPrice;

    /**
     * 是否已生成月账单
     */
    private Boolean existMonthData;

    /**
     * 分成比例 (媒体应得收入+投流消耗/结算总金额)
     */
    private String divideRate;
    /**
     * 媒体CPM (媒体应得收入/广告位曝光*1000)
     */
    private String appCpm;
    /**
     * 诺禾cpm (诺禾总消耗/广告位曝光*10000)
     */
    private String nhCpm;
    /**
     * 媒体cpc (媒体应得收入/广告位点击pv)
     */
    private String appCpc;
    /**
     * 诺禾每uv收益 (诺禾总消耗/广告位请求uv)
     */
    private String nhRevenuePerUv;
    /**
     * 媒体每uv收益 (媒体应得收入/广告位请求uv)
     */
    private String appRevenuePerUv;
    /**
     * 广告位点击率 (广告位点击pv(媒体反馈)/广告位曝光pv(媒体反馈))
     */
    private String slotClickRate;
}
