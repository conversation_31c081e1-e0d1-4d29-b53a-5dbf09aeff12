package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 外部落地页修改历史VO
 *
 * <AUTHOR>
 * @date 2023/4/11
 */
@Data
public class CrmExternalLandpageRecordHistoryVO implements Serializable {
    private static final long serialVersionUID = 7025186540875044604L;

    /**
     * 客服公司ID
     */
    private Long operAccountId;

    /**
     * 客服公司名称
     */
    private String operAccountName;

    /**
     * 原记录ID
     */
    private Long originRecordId;

    /**
     * 客户单号
     */
    private String externalNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省份
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
