package com.ruoyi.system.vo.fc;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class FcLinkVo  {
    /**
     * 丰巢链接id
     */
    private Long fcLinkId;
    /**
     * 丰巢链接名称
     */
    private String fcLinkName;
    /**
     * 丰巢链接key
     */
    private String fcLinkKey;
    /**
     * 丰巢链接url
     */
    private String fcLinkUrl;

    /**
     * 文章总数
     */
    private Integer articleCount;

    /**
     * 审核失败数
     */
    private Integer fcCheckRejectCount;

    /**
     * 审核通过数
     */
    private Integer fcCheckPassCount;

    /**
     * 文章同步数
     */
    private Integer fcSyncCount;

    /**
     * 文章目标总量（审核通过且已同步）
     */
    private Integer totalTargetRequestPv;

    /**
     * 文章在线数（审核通过且已同步）
     */
    private Integer articleOnLineCount;

    private Long pv;

    private Long uv;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    Long creatorId;

    Long operatorId;

}
