package com.ruoyi.system.vo.ai;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ai留资列表vo
 * <AUTHOR>
 * @date 2024/1/2 15:35
 */
@Data
public class AiLiuziListVo implements Serializable {
    private static final long serialVersionUID = -3152603626805402036L;
    /**
     * id
     */
    private Long id;
    /**
     * 姓名/公司名称
     */
    private String userName;
    /**
     * 用户号码
     */
    private String phone;
    /**
     * 使用场景
     */
    private String useScene;
    /**
     * 状态 0-未处理 1-有效 2-无效
     */
    private Integer status;
    /**
     * 操作人
     */
    private String operatorName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
