package com.ruoyi.system.vo.manager;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 落地页送审记录VO
 *
 * <AUTHOR>
 * @date 2021/9/10
 */
public class LandpageAuditRecordVO {

    /**
     * 记录ID
     */
    private String id;

    /**
     * 送审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditApplyTime;

    /**
     * 原落地页链接
     */
    private String originUrl;

    /**
     * 爱奇艺链接
     */
    private String iqiyiUrl;

    /**
     * 审核状态:0.审核中,1.审核通过,2.审核拒绝
     */
    private Integer auditStatus;

    /**
     * 审核结果返回时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditFinishTime;

    /**
     * 审核拒绝原因
     */
    private String auditReason;

    /**
     * 预览链接
     * 审核拒绝时，业务方可用于自查的地址，切记，预览地址不可用于投放
     */
    private String previewUrl;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getAuditApplyTime() {
        return auditApplyTime;
    }

    public void setAuditApplyTime(Date auditApplyTime) {
        this.auditApplyTime = auditApplyTime;
    }

    public String getOriginUrl() {
        return originUrl;
    }

    public void setOriginUrl(String originUrl) {
        this.originUrl = originUrl;
    }

    public String getIqiyiUrl() {
        return iqiyiUrl;
    }

    public void setIqiyiUrl(String iqiyiUrl) {
        this.iqiyiUrl = iqiyiUrl;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Date getAuditFinishTime() {
        return auditFinishTime;
    }

    public void setAuditFinishTime(Date auditFinishTime) {
        this.auditFinishTime = auditFinishTime;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }
}
