package com.ruoyi.system.vo.kefu;

import com.ruoyi.system.bo.kefu.IntendedFormImportErrorBo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 意向用户导入结果VO
 *
 * <AUTHOR>
 * @date 2023-04-10
 */
@Data
public class IntendedFormImportResultVO implements Serializable {
    private static final long serialVersionUID = 4508727905229264885L;

    /**
     * 导入总数
     */
    private Integer totalCount;

    /**
     * 错误数量
     */
    private Integer errorCount;

    /**
     * 错误列表
     */
    private List<IntendedFormImportErrorBo> errors;
}
