package com.ruoyi.system.vo.open;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * vivo获取token返回结果
 *
 * <AUTHOR>
 * @date 2022/05/23
 */
@Data
public class VivoTokenData implements Serializable {
    private static final long serialVersionUID = -242559862660845666L;

    /**
     * accessToken值
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * refreshToken值
     */
    @JsonProperty("refresh_token")
    private String refreshToken;

    /**
     * 时间戳(毫秒),accessToken截止的有效日期
     */
    @JsonProperty("token_date")
    private Long tokenDate;

    /**
     * 时间戳(毫秒),refreshToken截止的有效日期
     */
    @JsonProperty("refresh_token_date")
    private Long refreshTokenDate;
}
