package com.ruoyi.system.vo.manager;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体转化上报记录导出VO
 *
 * <AUTHOR>
 * @date 2022-10-19
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class ConvertUploadRecordExcelVO implements Serializable {
    private static final long serialVersionUID = 238995726535003875L;

    @ExcelProperty("转化时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date convertTime;

    @ExcelProperty("广告位ID")
    private Long slotId;

    @ExcelProperty("广告位名称")
    private String slotName;

    @ExcelProperty("广告ID")
    private Long advertId;

    @ExcelProperty("广告名称")
    private String advertName;

    @ExcelProperty("订单号")
    private String orderId;

    @ExcelProperty("上报状态")
    private String uploadStatusStr;

    @ExcelProperty("上报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;
}
