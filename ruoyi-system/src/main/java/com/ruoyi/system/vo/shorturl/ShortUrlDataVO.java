package com.ruoyi.system.vo.shorturl;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短链数据VO
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
@Data
public class ShortUrlDataVO implements Serializable {

    private static final long serialVersionUID = -7644048632306882930L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 短链
     */
    private String shortUrl;

    /**
     * 原链接
     */
    private String originUrl;

    /**
     * 状态:0.正常,1.禁用,2.删除
     */
    private Integer urlStatus;
    /**
     * 请求pv
     */
    private Integer requestPv;
    /**
     * 请求uv
     */
    private Integer requestUv;

    /**
     * 当前日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;
}
