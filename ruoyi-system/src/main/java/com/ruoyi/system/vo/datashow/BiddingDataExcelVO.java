package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

/**
 * 投流数据Excel VO
 *
 * <AUTHOR>
 * @date 2021/9/1 2:42 下午
 */
@Data
public class BiddingDataExcelVO implements Serializable {
    private static final long serialVersionUID = -241392285283145116L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String curDate;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 账户ID
     */
    private String advertiserId;

    /**
     * 消耗(元)
     */
    private String consume;

    /**
     * 是否导入成功
     */
    private Boolean success;
}
