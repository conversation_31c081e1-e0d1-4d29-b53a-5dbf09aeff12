package com.ruoyi.system.vo.datashow;

import com.ruoyi.system.vo.qualification.QualificationInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 结算单详情
 *
 * <AUTHOR>
 * @date 2021/9/17 2:28 下午
 */
@Data
public class StatementInfoVO implements Serializable {
    private static final long serialVersionUID = -4299805045293233705L;

    /**
     * 结算单明细
     */
    private List<CrmSlotMonthDataVO> monthDatas;

    /**
     * 客户资质信息
     */
    private QualificationInfoVO qualificationInfo;

    /**
     * 结算金额
     */
    private Integer totalRevenue;

    /**
     * 媒体应得收益
     */
    private Integer appRevenue;

    /**
     * 媒体id
     */
    private Long appId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 付款类型:1.预付款,0.后付款
     */
    private Integer payType;

    /**
     * 预付款欠款金额
     */
    private Long prepayAmount;
}
