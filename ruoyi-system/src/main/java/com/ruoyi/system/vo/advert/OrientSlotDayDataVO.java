package com.ruoyi.system.vo.advert;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 配置广告位日数据VO
 *
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
public class OrientSlotDayDataVO extends OrientBaseDataVO implements Serializable {
    private static final long serialVersionUID = -7978023002796176561L;

    /**
     * 媒体ID
     */
    @Excel(name = "媒体ID", sort = 15)
    private Long appId;

    /**
     * 媒体名称
     */
    @Excel(name = "媒体名称", width = 20, sort = 16)
    private String appName;

    /**
     * 广告位ID
     */
    @Excel(name = "广告位ID", sort = 17)
    private Long slotId;

    /**
     * 广告位名称
     */
    @Excel(name = "广告位名称", width = 30, sort = 18)
    private String slotName;
}
