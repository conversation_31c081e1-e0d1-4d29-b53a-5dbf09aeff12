package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 白酒落地页单记录VO
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Data
public class BaijiuLandpageFormRecordVO implements Serializable {
    private static final long serialVersionUID = -8876741649979411311L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告主名称
     */
    private String advertiserName;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 来源链接
     */
    private String referer;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区/县
     */
    private String district;

    /**
     * 行政区划代码
     */
    private String areaNum;

    /**
     * 详细信息
     */
    private String address;

    /**
     * IP
     */
    private String ip;

    /**
     * 支付金额
     */
    private float amount;
    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
