package com.ruoyi.system.vo.invoice;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/24 3:08 下午
 */
@Data
public class InvoiceExportVO implements Serializable {
    private static final long serialVersionUID = 6793363040189081876L;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gmtModified;

    /**
     * 账号id
     */
    @Excel(name ="账号ID")
    private Long accountId;
    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;
    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String email;

    /**
     * 预付款总金额(分)
     */
    @Excel(name = "预付款总金额(元)")
    private String prepayAmount;

    /**
     * 发票总金额
     */
    @Excel(name = "发票总金额(元)")
    private String invoiceAmountSum;

    /**
     * 欠票总金额(分)
     */
    @Excel(name = "欠票总金额(元)")
    private String debtAmount;
    /**
     * 发票状态
     */
    @Excel(name = "状态")
    private String status;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remarkText;

}
