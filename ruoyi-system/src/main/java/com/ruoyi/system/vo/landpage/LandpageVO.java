package com.ruoyi.system.vo.landpage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 落地页VO
 *
 * <AUTHOR>
 * @date 2023-09-26
 */
@Data
public class LandpageVO implements Serializable {
    private static final long serialVersionUID = 6600458638780675316L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 落地页标识
     */
    private String key;

    /**
     * 落地页名称
     */
    private String name;

    /**
     * 落地页链接
     */
    private String url;

    /**
     * 目标落地页
     */
    private String targetLandpage;

    /**
     * 落地页标签
     */
    private String tag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联广告列表
     */
    private String relateAdvertIds;

    /**
     * 落地页页面配置
     */
    private String pageConfig;

    /**
     * 皮肤类型
     * @see com.ruoyi.common.enums.landpage.LandPageSkinTypeEnum
     */
    private Integer skinType;

    /**
     * 域名
     */
    private String domain;

    /**
     * 底部文案
     */
    private String footer;

    /**
     * 是否无效:0.有效,1.无效
     */
    private Integer isInvalid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
