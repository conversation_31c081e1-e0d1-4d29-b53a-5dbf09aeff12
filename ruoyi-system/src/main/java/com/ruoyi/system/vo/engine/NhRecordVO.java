package com.ruoyi.system.vo.engine;

import com.ruoyi.system.bo.advert.AdvertExtInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * 奖品记录
 *
 * <AUTHOR>
 * @date 2022/01/05
 */
@Data
public class NhRecordVO implements Serializable {
    private static final long serialVersionUID = -3253745881293853791L;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 广告分类
     * @see com.ruoyi.common.enums.advert.AdvertCategory
     */
    private Integer advertCategory;

    /**
     * 广告缩略图
     */
    private String thumbnailImg;

    /**
     * 广告标题
     */
    private String advertTitle;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 额外信息
     */
    private AdvertExtInfo extInfo;
}
