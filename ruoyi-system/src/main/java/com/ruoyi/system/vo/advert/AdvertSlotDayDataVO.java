package com.ruoyi.system.vo.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 广告广告位维度日数据VO
 *
 * <AUTHOR>
 * @date 2021/10/18
 */
@Data
public class AdvertSlotDayDataVO implements Serializable {
    private static final long serialVersionUID = 4213972777831280808L;

    /** 主键id */
    private Long id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 12, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /** 媒体ID */
    @Excel(name = "媒体ID")
    private Long appId;

    /** 媒体名称 */
    @Excel(name = "媒体名称")
    private String appName;

    /** 广告位ID */
    @Excel(name = "广告位ID")
    private Long slotId;

    /** 广告位名称 */
    @Excel(name = "广告位名称")
    private String slotName;

    /** 消耗(元) */
    @Excel(name = "消耗", cellType = Excel.ColumnType.NUMERIC)
    private String consume;

    /** 发券pv */
    @Excel(name = "发券pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adLaunchPv;

    /** 发券uv */
    @Excel(name = "发券uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adLaunchUv;

    @Excel(name = "ARPU", cellType = Excel.ColumnType.NUMERIC)
    private String arpu;

    /** 券曝光pv */
    @Excel(name = "券曝光pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer exposurePv;

    /** 券曝光uv */
    @Excel(name = "券曝光uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer exposureUv;

    /** 券点击pv */
    @Excel(name = "券点击pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer clickPv;

    /** 券点击uv */
    @Excel(name = "券点击uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer clickUv;

    /** 计费点击pv */
    @Excel(name = "计费点击pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer billingClickPv;

    /** 计费点击uv */
    @Excel(name = "计费点击uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer billingClickUv;

    /** CTR */
    @Excel(name = "CTR")
    private String ctr;

    /** CPC */
    @Excel(name = "CPC", cellType = Excel.ColumnType.NUMERIC)
    private String cpc;

    /** 落地页曝光pv */
    @Excel(name = "落地页曝光pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpExposurePv;

    /** 落地页曝光uv */
    @Excel(name = "落地页曝光uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpExposureUv;

    /** 落地页到达率 */
    @Excel(name = "落地页到达率")
    private String lpArriveRate;

    @Excel(name = "领取pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer takePv;

    @Excel(name = "领取uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer takeUv;

    @Excel(name = "领取CVR")
    private String takeCvr;

    @Excel(name = "领取成本", cellType = Excel.ColumnType.NUMERIC)
    private String takeCost;

    /** 落地页转化pv */
    @Excel(name = "落地页转化pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpClickPv;

    /** 落地页转化uv */
    @Excel(name = "落地页转化uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpClickUv;

    /** 落地页转化成本 */
    @Excel(name = "落地页转化成本", cellType = Excel.ColumnType.NUMERIC)
    private String lpClickCost;

    /** CVR */
    @Excel(name = "CVR pv")
    private String cvr;

    /** CVR */
    @Excel(name = "CVR uv")
    private String cvrUv;

    @Excel(name = "激活", cellType = Excel.ColumnType.NUMERIC)
    private Integer register;

    @Excel(name = "支付", cellType = Excel.ColumnType.NUMERIC)
    private Integer pay;

    @Excel(name = "支付成本", cellType = Excel.ColumnType.NUMERIC)
    private String payCost;

    @Excel(name = "退款", cellType = Excel.ColumnType.NUMERIC)
    private Integer refund;

    @Excel(name ="投诉", cellType = Excel.ColumnType.NUMERIC)
    private Integer complain;

    @Excel(name ="APP激活", cellType = Excel.ColumnType.NUMERIC)
    private Integer appActive;

    @Excel(name ="支付金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal extPrice;
}
