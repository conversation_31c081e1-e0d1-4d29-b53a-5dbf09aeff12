package com.ruoyi.system.vo.activity;

import com.ruoyi.system.bo.activity.skin.ActivityCustomerBo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 活动客服号码vo
 * <AUTHOR>
 * @date 2023/5/23 10:54
 */
@Data
public class ActivityCustomerVO implements Serializable ,Cloneable{
    private static final long serialVersionUID = 7412355260424127441L;

    /**
     * 是否同步规则
     */
    private Boolean syncRule;
    /**
     * 客服号
     */
    private List<ActivityCustomerBo> customers;


    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
