package com.ruoyi.system.vo.advertiser.qualification;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告主资质校验结果VO
 *
 * <AUTHOR>
 * @date 2023-09-15
 */
@Data
public class AdvertiserCheckResultVO implements Serializable {
    private static final long serialVersionUID = 4595815300729545587L;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 行业资质是否齐全校验:true.齐全,false.缺失
     */
    private Boolean industryCheckPass;

    /**
     * 资质是否审核通过校验:true.审核通过,false.未审核通过
     */
    private Boolean qualificationAuditPass;
}
