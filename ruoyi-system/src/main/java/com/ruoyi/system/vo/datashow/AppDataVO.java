package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体数据VO
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Data
public class AppDataVO implements Serializable {
    private static final long serialVersionUID = -307937101560266377L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 媒体id
     */
    private Long appId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposurePv;

    /**
     * 广告位曝光pv(媒体反馈)
     */
    private Integer appSlotExposureUv;

    /**
     * 广告位点击pv(媒体反馈)
     */
    private Integer appSlotClickPv;

    /**
     * 广告位点击uv(媒体反馈)
     */
    private Integer appSlotClickUv;

    /**
     * 广告位点击率
     */
    private String slotClickRate;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv;

    /**
     * 媒体应得收益(分)
     */
    private Long appRevenue;
}
