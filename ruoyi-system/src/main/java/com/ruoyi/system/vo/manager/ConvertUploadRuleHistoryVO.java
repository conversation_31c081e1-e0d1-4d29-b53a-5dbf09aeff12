package com.ruoyi.system.vo.manager;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体转化上报规则修改记录VO
 *
 * <AUTHOR>
 * @date 2022/10/19
 */
@Data
public class ConvertUploadRuleHistoryVO implements Serializable {
    private static final long serialVersionUID = -5871413252992579790L;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 修改后的比例
     */
    private Integer ratio;

    /**
     * 修改人ID
     */
    private Long operatorId;

    /**
     * 修改人名称
     */
    private String operatorName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;
}
