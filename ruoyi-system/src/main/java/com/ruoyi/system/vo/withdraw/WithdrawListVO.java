package com.ruoyi.system.vo.withdraw;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 提现列表
 *
 * <AUTHOR>
 * @date 2021/9/14 5:42 下午
 */
@Data
public class WithdrawListVO implements Serializable {
    private static final long serialVersionUID = -7080582758701395466L;

    /**
     * 提现记录ID
     */
    private Long id;

    /**
     * 账号
     */
    private String email;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 银行账户
     */
    private String bankAccount;

    /**
     * 开户名
     */
    private String bankAccountName;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 提现金额
     */
    private Integer withdrawAmount;

    /**
     * 提现状态
     */
    private Integer withdrawStatus;

    /**
     * 复合审核状态:0.待部门负责人审核,1.审核通过,2.审核拒绝,3.待CEO/总裁审核,4.待财务审核,5.部门负责人审核拒绝,6.CEO/总裁审核拒绝,7.财务审核拒绝
     */
    private Integer complexAuditStatus;

    /**
     * 提现单文件
     */
    private String withdrawFile;

    /**
     * 审核人名字
     */
    private String checkAccountName;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkDate;

    /**
     * 拒绝理由
     */
    private String refuseReason;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 部门负责人审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer leaderAuditStatus;

    /**
     * 部门负责人审核人名称
     */
    private String leaderAuditorName;

    /**
     * 部门负责人审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date leaderAuditTime;

    /**
     * 部门负责人审核理由
     */
    private String leaderAuditReason;

    /**
     * 业务负责人审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer ceoAuditStatus;

    /**
     * 业务负责人审核人名称
     */
    private String ceoAuditorName;

    /**
     * 业务负责人审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ceoAuditTime;

    /**
     * 业务负责人审核理由
     */
    private String ceoAuditReason;

    /**
     * 财务审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer financeAuditStatus;

    /**
     * 财务审核人名称
     */
    private String financeAuditorName;

    /**
     * 财务审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date financeAuditTime;

    /**
     * 财务审核理由
     */
    private String financeAuditReason;

    /**
     * 是否可审核
     */
    private Integer canAudit;
}
