package com.ruoyi.system.vo.withdraw;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 提现列表
 *
 * <AUTHOR>
 * @date 2021/9/14 5:42 下午
 */
@Data
public class WithdrawListExportVO implements Serializable {
    private static final long serialVersionUID = 3088587184640362899L;

    /**
     * 提交时间
     */
    @Excel(name = "提交时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 账号
     */
    @Excel(name = "账号")
    private String email;

    /**
     * 提现单号
     */
    @Excel(name = "提现单号")
    private Long id;

    /**
     * 银行账户
     */
    @Excel(name = "银行卡号")
    private String bankAccount;

    /**
     * 开户名
     */
    @Excel(name = "开户名称")
    private String bankAccountName;

    /**
     * 开户银行
     */
    @Excel(name = "开户银行")
    private String bankName;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;

    /**
     * 提现金额
     */
    @Excel(name = "提现金额")
    private String withdrawAmount;

    /**
     * 提现状态
     */
    @Excel(name = "提现状态")
    private String status;
}
