package com.ruoyi.system.vo.cashback;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 返现列表vo
 * <AUTHOR>
 * @date 2022/5/30 5:49 下午
 */
@Data
public class CashBackListVO implements Serializable {
    private static final long serialVersionUID = -8380506725440413169L;

    private Long id;

    /**
     * 下单手机号
     */
    private String phone;

    /**
     * 表单姓名
     */
    private String name;

    /**
     * 红包金额(分)
     */
    private Integer amount;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    private String alipayName;

    /**
     * IP
     */
    private String ip;

    /**
     * 打款状态:0.未打款,1.已打款,2.无效信息
     */
    private Integer transferStatus;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operatorTime;
    /**
     * 表单提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date formSubmitTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
