package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 留资落地页单记录VO
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Data
public class LiuziLandpageFormRecordVO implements Serializable {

    private static final long serialVersionUID = 5344927407910027677L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long consumerId;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * IP
     */
    private String ip;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 落地页类型
     * @see com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum
     */
    private Integer landpageType;

    /**
     * 推广页名称
     */
    private String extendName;

    /**
     * 支付金额(分)
     */
    private Integer payAmount;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 短信条数
     */
    private Integer smsCount;
    /**
     * 加好友状态
     * @see com.ruoyi.common.enums.QwFriendStatusEnum
     */
    private Integer friendStatus;
}
