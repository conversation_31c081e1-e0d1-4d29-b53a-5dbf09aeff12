package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 外部落地页记录VO
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
public class ExternalLandpageRecordVO implements Serializable {
    private static final long serialVersionUID = 2241986403616843142L;

    /**
     * 落地页ID
     */
    @Excel(name = "落地页ID")
    private String landpageId;

    /**
     * 落地页名称
     */
    @Excel(name = "落地页名称")
    private String landpageName;

    /**
     * 提交时间
     */
    @Excel(name = "提交时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date commitTime;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCard;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phone;

    /**
     * 省
     */
    @Excel(name = "省份")
    private String province;

    /**
     * 市
     */
    @Excel(name = "市")
    private String city;

    /**
     * 区
     */
    @Excel(name = "区")
    private String district;

    /**
     * 详细地址
     */
    @Excel(name = "详细地址")
    private String address;

    public String getLandpageId() {
        return landpageId;
    }

    public void setLandpageId(String landpageId) {
        this.landpageId = landpageId;
    }

    public String getLandpageName() {
        return landpageName;
    }

    public void setLandpageName(String landpageName) {
        this.landpageName = landpageName;
    }

    public Date getCommitTime() {
        return commitTime;
    }

    public void setCommitTime(Date commitTime) {
        this.commitTime = commitTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
