package com.ruoyi.system.vo.revenue;

import lombok.Data;

import java.io.Serializable;

/**
 * 账户收益vo
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:30
 */
@Data
public class AccountRevenueVO implements Serializable {
    private static final long serialVersionUID = 6724002241171378501L;

    /**
     * 总收益（单位分，累计可提现金额之和）
     */
    private Long totalRevenue;

    /**
     * 可提现金额（分）
     */
    private Long withdrawableAmount;

    /**
     * 是否有资质信息
     */
    private Boolean hasQualification;

    /**
     * 付款类型:1.预付款,0.后付款
     */
    private Integer payType;
}

