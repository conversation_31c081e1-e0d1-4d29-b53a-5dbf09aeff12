package com.ruoyi.system.vo.advertiser.qualification;

import lombok.Data;

import java.util.List;

/**
 * 广告主资质要求VO
 *
 * <AUTHOR>
 * @date 2023/03/29
 */
@Data
public class AdvertiserQualificationRequireVO {

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 资质要求列表
     */
    private List<QualificationRequire> qualificationList;

    /**
     * 资质要求
     */
    @Data
    public static class QualificationRequire {

        /**
         * 资质ID
         */
        private Long id;

        /**
         * 资质名称
         */
        private String qualificationName;

        /**
         * 是否必填:0.非必填,1.必填
         */
        private Integer isMust;
    }
}
