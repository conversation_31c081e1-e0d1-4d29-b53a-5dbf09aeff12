package com.ruoyi.system.vo.landpage.article;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.bo.landpage.article.ArticleRetConfigBo;
import com.ruoyi.system.vo.advert.DegradedAdvertVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 文章聚合链接VO
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Data
public class ArticleAggrLinkVO implements Serializable {
    private static final long serialVersionUID = -1653138537647793395L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告主名称
     */
    private String advertiserName;

    /**
     * 链接唯一标识
     */
    private String key;

    /**
     * 链接名称
     */
    private String name;

    /**
     * 链接地址
     */
    private String url;

    /**
     * 文章数量
     */
    private String articleCount;

    /**
     * 关联广告ID
     */
    private List<String> relateAdvertIds;

    /**
     * 请求PV
     */
    private Integer requestPv;

    /**
     * 请求UV
     */
    private Integer requestUv;

    /**
     * 文章阅读量总和
     */
    private Integer articleTargetRequestPv;

    /**
     * 返回拦截配置
     */
    private ArticleRetConfigBo retConfig;

    /**
     * 私域广告位
     */
    private String sySlot;

    /**
     * 私域当日增加阅读量
     */
    private Integer todaySyIncrRequestPv;

    /**
     * 状态:0.正常,1.已删除
     */
    private Integer status;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operatorTime;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
