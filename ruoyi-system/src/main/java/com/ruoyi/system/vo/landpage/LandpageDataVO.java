package com.ruoyi.system.vo.landpage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 落地页数据VO
 *
 * <AUTHOR>
 * @date 2022/09/22
 */
@Data
public class LandpageDataVO implements Serializable {
    private static final long serialVersionUID = 2660597298443335997L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 落地页标识
     */
    private String landpageKey;

    /**
     * 落地页名称
     */
    private String landpageName;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 发券pv
     */
    private Integer adLaunchPv;

    /**
     * 发券uv
     */
    private Integer adLaunchUv;

    /**
     * 券曝光pv
     */
    private Integer adExposurePv;

    /**
     * 券曝光uv
     */
    private Integer adExposureUv;

    /**
     * 券点击pv
     */
    private Integer adClickPv;

    /**
     * 券点击uv
     */
    private Integer adClickUv;

    /**
     * 落地页曝光pv
     */
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    private Integer lpExposureUv;

    /**
     * 落地页点击pv
     */
    private Integer lpClickPv;

    /**
     * 落地页点击uv
     */
    private Integer lpClickUv;

    /**
     * CVR(落地页转化PV/落地页曝光PV)
     */
    private String cvrPv;

    /**
     * CVR(落地页转化UV/落地页曝光UV)
     */
    private String cvrUv;

    /**
     * 落地页参与pv
     */
    private Integer lpJoinPv;

    /**
     * 落地页参与uv
     */
    private Integer lpJoinUv;

    /**
     * 弹窗曝光pv
     */
    private Integer popupExposurePv;

    /**
     * 弹窗曝光uv
     */
    private Integer popupExposureUv;

    /**
     * 弹窗点击pv
     */
    private Integer popupClickPv;

    /**
     * 弹窗点击uv
     */
    private Integer popupClickUv;

    /**
     * 激活
     */
    private Integer register;

    /**
     * 支付
     */
    private Integer pay;
}
