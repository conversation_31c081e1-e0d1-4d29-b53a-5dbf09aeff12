package com.ruoyi.system.vo.wis;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文章VO
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Data
public class WisArticleVO implements Serializable {
    private static final long serialVersionUID = 1348583791143009463L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 聚合链接ID
     */
    private Long linkId;

    /**
     * 名称
     */
    private String name;

    /**
     * 链接
     */
    private String url;

    /**
     * 目标阅读量
     */
    private Integer targetRequestPv;

    /**
     * 初始阅读量
     */
    private Integer initRequestPv;

    /**
     * 实际阅读量(广告主后台展示)
     */
    private Integer displayActualRequestPv;

    /**
     * 预估PV
     */
    private Integer requestPv;

    /**
     * 预估UV
     */
    private Integer requestUv;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operatorTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 是否停止
     */
    private Integer isStop;
}
