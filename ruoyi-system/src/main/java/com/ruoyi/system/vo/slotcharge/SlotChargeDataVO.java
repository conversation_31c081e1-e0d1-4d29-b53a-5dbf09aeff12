package com.ruoyi.system.vo.slotcharge;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位日结算设置数据
 * <AUTHOR>
 * @date 2022/3/8 5:04 下午
 */
@Data
public class SlotChargeDataVO implements Serializable {

    private static final long serialVersionUID = -5115641733811312396L;

    /**
     * 计费类型
     */
    private Integer chargeType;

    /**
     * 计费价格
     */
    private Integer chargePrice;
    /**
     * 媒体收益
     */
    private Long appRevenue = 0L;
    /**
     * 诺禾结算金额
     */
    private Long nhCost = 0L;
    /**
     * 外部结算金额
     */
    private Long outerCost = 0L;
    /**
     * 广告位请求pv
     */
    private Integer slotRequestPv = 0;
    /**
     * 广告位请求uv
     */
    private Integer slotRequestUv = 0;
    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;
    /**
     * 是否可编辑 ，true可编辑，false不可编辑
     */
    private Boolean canEdit = false;

}
