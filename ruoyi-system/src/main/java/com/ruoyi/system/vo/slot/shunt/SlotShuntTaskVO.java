package com.ruoyi.system.vo.slot.shunt;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.slot.SlotShuntStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位切量计划VO
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
@Data
public class SlotShuntTaskVO implements Serializable {
    private static final long serialVersionUID = -5189599123085616347L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告位
     */
    private Long slotId;

    /**
     * 切量类型:1.pv,2.uv
     */
    private Integer shuntType;

    /**
     * 计划名称
     */
    private String taskName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 活动投放类型
     * @see com.ruoyi.common.enums.slot.SlotRedirectType
     */
    private Integer redirectType;

    /**
     * 活动ID、链接或者广告ID
     */
    private String redirectValue;

    /**
     * 分流比例[0,100]
     */
    private Integer shuntRatio;

    /**
     * 状态
     * @see SlotShuntStatusEnum
     */
    private Integer taskStatus;

    /**
     * 切量上限
     */
    private Integer threshold;

    /**
     * 广告位访问PV
     */
    private Integer slotRequestPv;

    /**
     * 广告位访问UV
     */
    private Integer slotRequestUv;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
