package com.ruoyi.system.vo.wis;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文章聚合链接VO
 * <AUTHOR>
 * @date 2023/12/15 14:43
 */
@Data
public class WisArticleAggrLinkVO implements Serializable {
    private static final long serialVersionUID = -4804508390796955342L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 链接名称
     */
    private String name;

    /**
     * 文章数量
     */
    private Integer articleCount = 0;

    /**
     * 当日文章数量
     */
    private Integer todayArticleCount = 0;

    /**
     * 在线文章数量
     */
    private Integer onlineArticleCount = 0;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
