package com.ruoyi.system.vo.activity;

import lombok.Data;

import java.io.Serializable;

/**
 * 活动推广计划VO
 *
 * <AUTHOR>
 * @date 2021/7/16
 */
@Data
public class ActivityPlanVO implements Serializable {
    private static final long serialVersionUID = -7403167089292041681L;

    /**
     * 活动推广计划ID
     */
    private Long id;

    /**
     * 活动工具ID
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动皮肤
     */
    private String skinName;

    /**
     * 状态:0.开启,1.关闭
     */
    private Integer status;
}
