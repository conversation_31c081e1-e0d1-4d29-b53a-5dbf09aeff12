package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 外部落地页记录VO
 *
 * <AUTHOR>
 * @date 2021/10/11
 */
@Data
public class CrmExternalLandpageRecordVO implements Serializable {
    private static final long serialVersionUID = 6692462076439623463L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户单号
     */
    private String externalNo;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告主名称
     */
    private String advertiserName;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

     /**
     * 导入类型:1.API回传,2.手动导入
     */
    private Integer importType;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省份
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

     /**
      * 导出记录:0.未导出,1.已导出
      */
    private Integer isExported;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 回传广告主ID
     */
    private Long sendAdvertiserId;

    /**
     * 回传广告主名称
     */
    private String sendAdvertiserName;

    /**
     * 回传状态:0.未回传,1.回传成功,2.回传失败
     */
    private Integer sendStatus;

    /**
     * 回传失败原因
     */
    private String sendMsg;

    /**
     * 回传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 修改历史
     */
    private List<CrmExternalLandpageRecordHistoryVO> history;
}
