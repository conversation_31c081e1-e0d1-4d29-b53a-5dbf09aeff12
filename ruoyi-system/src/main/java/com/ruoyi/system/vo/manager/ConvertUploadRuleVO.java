package com.ruoyi.system.vo.manager;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体转化上报规则VO
 *
 * <AUTHOR>
 * @date 2022/10/19
 */
@Data
public class ConvertUploadRuleVO implements Serializable {
    private static final long serialVersionUID = 4725948115274820924L;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 配置比例[0,100]
     */
    private Integer ratio;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
