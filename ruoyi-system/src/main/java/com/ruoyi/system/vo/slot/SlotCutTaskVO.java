package com.ruoyi.system.vo.slot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位切量任务VO
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Data
public class SlotCutTaskVO implements Serializable {
    private static final long serialVersionUID = 622275008652156606L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告位
     */
    private Long slotId;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveTime;

    /**
     * 活动投放类型
     * @see com.ruoyi.common.enums.slot.SlotRedirectType
     */
    private Integer redirectType;

    /**
     * 活动ID或者活动链接
     */
    private String redirectValue;

    /**
     * 切换的pv阈值
     */
    private Long threshold;

    /**
     * 已切量pv
     */
    private Long pv;

    /**
     * 状态:0.未执行,1.执行中,2.已完成,3.已取消
     */
    private Integer taskStatus;

    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executeTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 取消时间
     */
    private Date cancelTime;
}
