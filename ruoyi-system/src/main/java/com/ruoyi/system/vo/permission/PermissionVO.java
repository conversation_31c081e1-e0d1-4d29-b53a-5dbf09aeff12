package com.ruoyi.system.vo.permission;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 权限vo
 *
 * <AUTHOR>
 * @date 2022/6/24 11:37 上午
 */
@Data
public class PermissionVO implements Serializable {
    private static final long serialVersionUID = -696856610700251980L;

    /**
     * 权限id
     */
    private Long id;

    /**
     * 父级权限id，0表示一级
     */
    private Long parentId;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 通过前缀区分 ssp/dsp
     */
    private String permissionKey;

    /**
     * 子权限列表
     */
    private List<PermissionVO> subPermissionList;
}
