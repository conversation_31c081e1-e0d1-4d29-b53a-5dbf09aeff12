package com.ruoyi.system.vo.advertiser.finance;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告主/代理商充值余额列表VO
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
@Data
public class AdvertiserBalanceVO implements Serializable {
    private static final long serialVersionUID = 7685545648992302519L;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 账号类型:2.广告主,4.代理商
     */
    private Integer mainType;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 子广告主的代理商ID
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 总余额(分)
     */
    private Integer totalAmount;

    /**
     * 现金余额(分)
     */
    private Integer cashAmount;

    /**
     * 返货余额(分)
     */
    private Integer rebateAmount;

    /**
     * 调整金额(分)
     */
    private Integer offsetAmount;
}
