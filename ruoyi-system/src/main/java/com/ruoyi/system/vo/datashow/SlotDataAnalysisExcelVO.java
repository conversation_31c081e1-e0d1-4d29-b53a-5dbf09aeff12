package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 解析广告位数据excel vo
 *
 * <AUTHOR>
 * @date 2021/9/1 2:42 下午
 */
@Data
public class SlotDataAnalysisExcelVO implements Serializable {

    private static final long serialVersionUID = 5188515732032951949L;
    /**
     * 广告位id
     */
    private String slotId;
    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String curDate;
    /**
     * 广告位点击pv(媒体反馈)
     */
    private String appSlotClickPv;
    /**
     * 广告位点击uv(媒体反馈)
     */
    private String appSlotClickUv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private String appSlotExposurePv;
    /**
     * 广告位曝光pv(媒体反馈)
     */
    private String appSlotExposureUv;
    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 行数
     */
    private Long row;
}
