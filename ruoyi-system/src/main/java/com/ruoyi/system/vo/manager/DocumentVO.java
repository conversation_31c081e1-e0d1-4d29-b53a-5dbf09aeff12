package com.ruoyi.system.vo.manager;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 对接文档VO
 *
 * <AUTHOR>
 * @date 2022/12/1
 */
@Data
public class DocumentVO implements Serializable {
    private static final long serialVersionUID = -9220436565789885208L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 文档名称
     */
    private String documentName;

    /**
     * 文档地址
     */
    private String documentUrl;

    /**
     * 文档类型:1.内部文档,2.外部文档
     */
    private Integer documentType;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    /**
     * 操作人名称
     */
    private String operatorName;
}
