package com.ruoyi.system.vo.slot.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位投放数据导出VO
 *
 * <AUTHOR>
 * @date 2022-05-07
 */
@Data
@HeadRowHeight(20) //行高
@ColumnWidth(25) //列宽
@HeadFontStyle(fontHeightInPoints = 10) //表头字体大小
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) //居中
public class SlotRedirectDataExcelVO implements Serializable {
    private static final long serialVersionUID = -3622042724675674215L;

    @ExcelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    @ExcelProperty("广告位ID")
    private Long slotId;

    @ExcelProperty("投放类型")
    private String redirectType;

    @ExcelProperty("投放信息")
    private String redirectValue;

    @ExcelProperty("广告位访问PV")
    private Integer slotRequestPv;

    @ExcelProperty("广告位访问UV")
    private Integer slotRequestUv;
}
