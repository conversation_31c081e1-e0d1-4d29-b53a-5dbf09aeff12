package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 企微囤粉落地页单记录VO
 *
 * <AUTHOR>
 * @date 2023-09-222
 */
@Data
public class QwtfLandpageFormRecordVO implements Serializable {
    private static final long serialVersionUID = -4786500787108689022L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 手机号
     */
    private String phone;

    /**
     * IP
     */
    private String ip;

    /**
     * 加好友状态:0.未加好友,1.已加好友
     */
    private Integer friendStatus;

    /**
     * 已发短信数
     */
    private Integer smsTimes;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
