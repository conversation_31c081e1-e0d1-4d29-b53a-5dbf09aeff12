package com.ruoyi.system.vo.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 活动皮肤VO
 *
 * <AUTHOR>
 * @date 2023-05-08
 */
@Data
public class ActivitySkinVO implements Serializable {
    private static final long serialVersionUID = -2629076118686941955L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 皮肤标识
     */
    private String skinCode;

    /**
     * 皮肤名称
     */
    private String skinName;

    /**
     * 活动皮肤类型:1.套猫,2.大转盘,3.卡包
     */
    private Integer skinType;

    /**
     * 活动类型:1.轻互动活动
     */
    private Integer activityType;

    /**
     * 缩略图
     */
    private String thumbnailImage;

    /**
     * 配置模板
     */
    private String jsTemplate;

    /**
     * 皮肤配置：全局配置，所有活动生效
     */
    private String globalConfig;

    /**
     * 皮肤配置：copy到活动，活动会自己修改
     */
    private String skinConfig;

    /**
     * 跳转路径
     */
    private String redirectPath;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;
}
