package com.ruoyi.system.vo.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告定向配置列表VO
 *
 * <AUTHOR>
 * @date 2023/7/5
 */
@Data
public class AdvertOrientationListVO implements Serializable {
    private static final long serialVersionUID = 7948841939739064769L;

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 配置名称
     */
    private String orientName;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 投放开关
     */
    private Integer servingSwitch;

    /**
     * 计费单价（分）
     */
    private Integer unitPrice;

    /**
     * 计费单价（毫）
     */
    private Integer milliUnitPrice;

    /**
     * 权重
     */
    private Double weight;

    /**
     * 每日预算（分，null为不限）
     */
    private Integer dailyBudget;

    /**
     * 是否通投
     */
    private Boolean launchTotalApp;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告主名称
     */
    private String advertiserName;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 创建人
     */
    private String operatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
