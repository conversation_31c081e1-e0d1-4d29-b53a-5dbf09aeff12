package com.ruoyi.system.vo.activity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 活动VO(用于下拉列表)
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivitySelectVO implements Serializable {
    private static final long serialVersionUID = -677394865844956470L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;
}
