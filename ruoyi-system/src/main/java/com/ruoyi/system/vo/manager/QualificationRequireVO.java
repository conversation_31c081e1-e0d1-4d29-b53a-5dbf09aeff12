package com.ruoyi.system.vo.manager;

import lombok.Data;

import java.io.Serializable;

/**
 * 资质要求VO
 *
 * <AUTHOR>
 * @date 2023/03/06
 */
@Data
public class QualificationRequireVO implements Serializable {
    private static final long serialVersionUID = 3786941720866272086L;

    /**
     * 资质要求ID
     */
    private Long id;

    /**
     * 行业名称
     */
    private Long industryId;

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 资质要求
     */
    private String qualificationName;

    /**
     * 是否必填:0.非必填,1.必填
     */
    private Integer isMust;

    /**
     * 状态:0.禁用,1.启用
     */
    private Integer enableStatus;
}
