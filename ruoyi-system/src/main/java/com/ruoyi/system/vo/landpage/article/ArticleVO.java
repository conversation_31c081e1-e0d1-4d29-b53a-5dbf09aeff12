package com.ruoyi.system.vo.landpage.article;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文章VO
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Data
public class ArticleVO implements Serializable {
    private static final long serialVersionUID = -4810237459065959826L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 聚合链接ID
     */
    private Long linkId;

    /**
     * 名称
     */
    private String name;

    /**
     * 链接
     */
    private String url;

    /**
     * 目标阅读量
     */
    private Integer targetRequestPv;

    /**
     * 初始阅读量
     */
    private Integer initRequestPv;

    /**
     * 私域增加阅读量(私域阅读量-初始阅读量)
     */
    private Integer syIncrRequestPv;

    /**
     * 实际增加阅读量(实际阅读量-初始阅读量)
     */
    private Integer actualIncrRequestPv;

    /**
     * 补量
     */
    private Integer compensateRequestPv;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 权重总和
     */
    private Integer totalWeight;

    /**
     * 今日请求PV
     */
    private Integer requestPv;

    /**
     * 今日请求PV
     */
    private Integer requestUv;

    /**
     * 状态:0.正常,1.已删除
     */
    private Integer status;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operatorTime;

    /**
     * 丰巢审核状态
     */
    private Integer fcCheckStatus;

    /**
     * 丰巢同步状态
     */
    private Integer fcSyncStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
