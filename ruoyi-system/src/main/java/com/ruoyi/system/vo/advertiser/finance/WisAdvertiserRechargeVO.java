package com.ruoyi.system.vo.advertiser.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主充值记录VO
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@Data
public class WisAdvertiserRechargeVO implements Serializable {
    private static final long serialVersionUID = 3952798423493664551L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

    /**
     * 充值类型
     *
     * @see AdvertiserRechargeTypeEnum
     */
    private Integer rechargeType;

    /**
     * 充值金额(分)
     */
    private Integer rechargeAmount;

    /**
     * 备注
     */
    private String remark;
}
