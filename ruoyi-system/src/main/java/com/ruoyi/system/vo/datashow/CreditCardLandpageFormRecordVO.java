package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 信用卡落地页表单VO
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@Data
public class CreditCardLandpageFormRecordVO implements Serializable {
    private static final long serialVersionUID = 8657723377814719540L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告名称
     */
    private String advertName;

    /**
     * 广告主ID
     */
    private Long advertiserId;

    /**
     * 广告主名称
     */
    private String advertiserName;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 媒体ID
     */
    private Long appId;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 广告位ID
     */
    private Long slotId;

    /**
     * 广告位名称
     */
    private String slotName;


    /**
     * 来源链接
     */
    private String referer;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 详细信息
     */
    private String address;

    /**
     * IP
     */
    private String ip;

    /**
     * 申办额度
     */
    private Integer creditLimit;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
