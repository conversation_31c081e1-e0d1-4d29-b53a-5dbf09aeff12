package com.ruoyi.system.vo.shorturl;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短链VO
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
@Data
public class ShortUrlVO implements Serializable {
    private static final long serialVersionUID = 4952541242713392229L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 类型
     * @see com.ruoyi.common.enums.ShortUrlTypeEnum
     */
    private Integer urlType;

    /**
     * 短链
     */
    private String shortUrl;

    /**
     * 原链接
     */
    private String originUrl;

    /**
     * 状态:0.正常,1.禁用,2.删除
     */
    private Integer urlStatus;

    /**
     * 创建时间
     */
    private Date gmtCreate;
}
