package com.ruoyi.system.vo.advertiser.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主/代理商财务汇总记录VO
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
@Data
public class AdvertiserConsumeVO implements Serializable {
    private static final long serialVersionUID = -4823699989065262228L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 账号类型:2.广告主,4.代理商
     */
    private Integer mainType;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 子广告主的代理商ID
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 已分配有效表单量
     */
    private Integer formCount;

    /**
     * 已分配表单量
     */
    private Integer totalFormCount;

    /**
     * 计费点击PV
     */
    private Integer billingClickPv;

    /**
     * 计费点击UV
     */
    private Integer billingClickUv;

    /**
     * 消费金额(分)
     */
    private Integer consumeAmount;

    /**
     * 账户余额(分)
     */
    private Integer balanceAmount;

    /**
     * 是否可修改
     */
    private Integer isEditable;

    /**
     * 广告主是否可见
     */
    private Integer isVisible;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operatorTime;
}
