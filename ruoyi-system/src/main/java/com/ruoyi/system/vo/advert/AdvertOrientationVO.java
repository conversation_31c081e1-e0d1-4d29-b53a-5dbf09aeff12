package com.ruoyi.system.vo.advert;

import com.ruoyi.common.enums.advert.DeviceTargetType;
import com.ruoyi.common.enums.advert.FlowTargetType;
import com.ruoyi.common.enums.advert.IspTargetType;
import com.ruoyi.common.enums.advert.LandpageTypeEnum;
import com.ruoyi.common.enums.advert.OsTargetType;
import com.ruoyi.system.vo.slot.AppSlotGroupVO;
import com.ruoyi.system.vo.traffic.TrafficPackageListVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 广告定向配置VO
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Data
public class AdvertOrientationVO implements Serializable {
    private static final long serialVersionUID = 318391678713725191L;

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 配置名称
     */
    private String orientName;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 是否默认配置
     */
    private Integer isDefault;

    /**
     * 投放开关
     */
    private Integer servingSwitch;

    /**
     * 落地页类型
     * @see LandpageTypeEnum
     */
    private Integer landpageType;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 落地页链接(预览链接)
     */
    private String previewLandpageUrl;

    /**
     * 计费类型
     */
    private Integer chargeType;

    /**
     * 计费单价（分）
     */
    private Integer unitPrice;

    /**
     * 计费单价（毫）
     */
    private Integer milliUnitPrice;

    /**
     * OCPC转化类型
     * @see com.ruoyi.common.enums.advert.OcpcConvTypeEnum
     */
    private Integer ocpcConvType;

    /**
     * OCPC转化成本(分)
     */
    private Integer ocpcConvCost;

    /**
     * 预估OCPC出价(分)
     */
    private Integer predictOcpcPrice;

    /**
     * 每日预算（分，null为不限）
     */
    private Integer dailyBudget;

    /**
     * 屏蔽媒体（空为不限）
     */
    private List<AppSlotGroupVO> bannedApps;

    /**
     * 定向媒体（空为不限）
     */
    private List<AppSlotGroupVO> orientApps;

    /**
     * 定向流量包（空为不限）
     */
    private List<TrafficPackageListVO> orientTraffic;

    /**
     * 地域定向
     */
    private Set<String> areaTarget;

    /**
     * 设备定向列表
     * @see DeviceTargetType
     */
    private List<Integer> deviceTargets;

    /**
     * 流量定向列表
     * @see FlowTargetType
     */
    private List<Integer> flowTargets;

    /**
     * 系统定向列表
     * @see OsTargetType
     */
    private List<Integer> osTargets;

    /**
     * 运营商定向列表
     * @see IspTargetType
     */
    private List<Integer> ispTargets;

    /**
     * 投放时段
     */
    private List<Integer> servingHours;

    /**
     * 是否通投:1.是,0.否
     */
    private Integer launchTotalApp;

    /**
     * 权重
     */
    private Double weight;
}
