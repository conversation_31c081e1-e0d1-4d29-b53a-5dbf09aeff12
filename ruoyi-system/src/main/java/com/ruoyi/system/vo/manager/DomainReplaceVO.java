package com.ruoyi.system.vo.manager;

import lombok.Data;

import java.io.Serializable;

/**
 * 域名替换VO
 *
 * <AUTHOR>
 * @date 2023/02/24
 */
@Data
public class DomainReplaceVO implements Serializable {
    private static final long serialVersionUID = 3494844178643303985L;

    /**
     * 域名ID
     */
    private Long id;

    /**
     * 域名
     */
    private String domain;

    /**
     * 域名类型
     */
    private Integer domainType;

    /**
     * 微信状态
     */
    private Integer wxStatus;

    /**
     * 支付宝状态
     */
    private Integer alipayStatus;

    /**
     * 备注
     */
    private String remark;
}
