package com.ruoyi.system.vo.datashow;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位活动数据导出VO
 *
 * <AUTHOR>
 * @date 2023/10/20
 */
@Data
public class CrmSlotActivityDataExportVO implements Serializable {
    private static final long serialVersionUID = -9020286506895308589L;

    /**
     * 日期
     */
    @Excel(name = "日期", width = 20, dateFormat = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 小时
     */
    @Excel(name = "小时", width = 10)
    private Integer curHour;

    /**
     * 媒体名称
     */
    @Excel(name = "媒体名称/媒体ID", width = 40)
    private String appName;

    /**
     * 广告位名称
     */
    @Excel(name = "广告位名称/广告位ID", width = 40)
    private String slotName;

    /**
     * 活动名称
     */
    @Excel(name = "活动名称/活动ID", width = 40)
    private String activityName;

    /**
     * 活动访问pv
     */
    @Excel(name = "活动访问pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer activityRequestPv;

    /**
     * 活动访问uv
     */
    @Excel(name = "活动访问uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer activityRequestUv;

    /**
     * 活动参与pv
     */
    @Excel(name = "参与pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer joinPv;

    /**
     * 活动参与uv
     */
    @Excel(name = "参与uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer joinUv;

    /**
     * 参与率（活动参与uv/活动访问uv）
     */
    @Excel(name = "参与率(活动参与uv/活动访问uv)", width = 30)
    private String joinRate;

    /**
     * 复参
     */
    @Excel(name = "复参", cellType = Excel.ColumnType.NUMERIC)
    private String rejoin;

    /**
     * 券请求
     */
    @Excel(name = "广告请求pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adRequest;

    /**
     * 发券pv
     */
    @Excel(name = "发券pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adLaunch;

    /**
     * 发券成功率(发券/券请求)
     */
    @Excel(name = "发券成功率(发券/券请求)", width = 25)
    private String launchRate;

    /**
     * 券曝光Pv
     */
    @Excel(name = "广告曝光Pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adExposurePv;

    /**
     * 券曝光Uv
     */
    @Excel(name = "广告曝光Uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adExposureUv;

    /**
     * 券点击Pv
     */
    @Excel(name = "广告点击Pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adClickPv;

    /**
     * 券点击Uv
     */
    @Excel(name = "广告点击Uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer adClickUv;

    /**
     * CTR（券点击pv/券曝光pv）
     */
    @Excel(name = "CTR(券点击pv/券曝光pv)", width = 25)
    private String ctr;

    /**
     * 每UV发券（发券pv/活动访问uv）
     */
    @Excel(name = "每UV发券(发券pv/活动访问uv)", width = 30, cellType = Excel.ColumnType.NUMERIC)
    private String adLaunchPerUv;

    /**
     * 每UV券点击（券点击uv/活动访问uv）
     */
    @Excel(name = "每UV券点击(券点击uv/活动访问uv)", width = 30, cellType = Excel.ColumnType.NUMERIC)
    private String adClickPerUv;

    /**
     * 广告消耗(元)
     */
    @Excel(name = "广告消耗(元)")
    private String consume;

    /**
     * ARPU值(消耗/发券pv)
     */
    @Excel(name = "ARPU值(消耗/发券pv)", width = 25, cellType = Excel.ColumnType.NUMERIC)
    private String arpu;

    /**
     * 落地页曝光pv
     */
    @Excel(name = "落地页曝光pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpExposurePv;

    /**
     * 落地页曝光uv
     */
    @Excel(name = "落地页曝光uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpExposureUv;

    /**
     * 落地页转化pv
     */
    @Excel(name = "落地页转化pv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpClickPv;

    /**
     * 落地页转化uv
     */
    @Excel(name = "落地页转化uv", cellType = Excel.ColumnType.NUMERIC)
    private Integer lpClickUv;

    /**
     * CVR（落地页转化pv/落地页曝光pv）
     */
    @Excel(name = "CVR(落地页转化pv/落地页曝光pv)", width = 30)
    private String cvrPv;

    /**
     * CVR（落地页转化uv/落地页曝光uv）
     */
    @Excel(name = "CVR(落地页转化uv/落地页曝光uv)", width = 30)
    private String cvrUv;

    /**
     * 理论消耗(产生转化时的考核成本)
     */
    @Excel(name = "理论消耗(转化的考核成本)", width = 30, cellType = Excel.ColumnType.NUMERIC)
    private String theoryCost;
}
