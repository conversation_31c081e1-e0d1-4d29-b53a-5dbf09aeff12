package com.ruoyi.system.vo.advertiser.qualification;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 资质信息
 *
 * <AUTHOR>
 * @date 2022/4/28 5:32 下午
 */
@Data
public class AdvertiserQualificationVO implements Serializable {
    private static final long serialVersionUID = 1828776834794975853L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 资质名称
     */
    private String qualificationName;

    /**
     * 资质图
     */
    private List<String> qualificationImgs;

    /**
     * 审核状态:0.待审核,1.审核通过,2.审核拒绝
     */
    private Integer auditStatus;

    /**
     * 营业执照有效期 9999-12-31代表永久
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireTime;
}
