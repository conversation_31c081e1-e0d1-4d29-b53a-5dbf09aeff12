package com.ruoyi.system.vo.advert;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 配置基础数据VO
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Data
public class OrientBaseDataVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 12, dateFormat = "yyyy-MM-dd", sort = 1)
    private Date curDate;

    /**
     * 广告ID
     */
    @Excel(name = "广告ID", sort = 11)
    private Long advertId;

    /**
     * 广告名称
     */
    @Excel(name = "广告名称", width = 30, sort = 12)
    private String advertName;

    /**
     * 配置ID
     */
    @Excel(name = "配置ID", sort = 13)
    private Long orientId;

    /**
     * 配置名称
     */
    @Excel(name = "配置名称", sort = 14)
    private String orientName;

    /**
     * 消耗(元)
     */
    @Excel(name = "消耗", cellType = Excel.ColumnType.NUMERIC, sort = 101)
    private String consume;

    /**
     * 发券PV
     */
    @Excel(name = "发券PV", cellType = Excel.ColumnType.NUMERIC, sort = 102)
    private Long adLaunchPv;

    /**
     * 发券UV
     */
    @Excel(name = "发券UV", cellType = Excel.ColumnType.NUMERIC, sort = 103)
    private Long adLaunchUv;

    /**
     * ARPU
     */
    @Excel(name = "ARPU", cellType = Excel.ColumnType.NUMERIC, sort = 104)
    private String arpu;

    /**
     * 券曝光PV
     */
    @Excel(name = "券曝光PV", cellType = Excel.ColumnType.NUMERIC, sort = 105)
    private Integer exposurePv;

    /**
     * 券曝光UV
     */
    @Excel(name = "券曝光UV", cellType = Excel.ColumnType.NUMERIC, sort = 106)
    private Integer exposureUv;

    /**
     * 券点击PV
     */
    @Excel(name = "券点击PV", cellType = Excel.ColumnType.NUMERIC, sort = 107)
    private Integer clickPv;

    /**
     * 券点击UV
     */
    @Excel(name = "券点击UV", cellType = Excel.ColumnType.NUMERIC, sort = 108)
    private Integer clickUv;

    /**
     * 计费点击PV
     */
    @Excel(name = "计费点击PV", cellType = Excel.ColumnType.NUMERIC, sort = 109)
    private Integer billingClickPv;

    /**
     * 计费点击UV
     */
    @Excel(name = "计费点击UV", cellType = Excel.ColumnType.NUMERIC, sort = 110)
    private Integer billingClickUv;

    /**
     * CTR
     */
    @Excel(name = "CTR", sort = 111)
    private String ctr;

    /**
     * CPC
     */
    @Excel(name = "CPC", cellType = Excel.ColumnType.NUMERIC, sort = 112)
    private String cpc;

    /**
     * 落地页曝光PV
     */
    @Excel(name = "落地页曝光PV", cellType = Excel.ColumnType.NUMERIC, sort = 113)
    private Integer lpExposurePv;

    /**
     * 落地页曝光UV
     */
    @Excel(name = "落地页曝光UV", cellType = Excel.ColumnType.NUMERIC, sort = 114)
    private Integer lpExposureUv;

    /**
     * 落地页到达率
     */
    @Excel(name = "落地页到达率", sort = 115)
    private String lpExposurePVClickPv;

    /**
     * 领取PV
     */
    @Excel(name = "领取PV", cellType = Excel.ColumnType.NUMERIC, sort = 116)
    private Integer takePv;

    /**
     * 领取UV
     */
    @Excel(name = "领取UV", cellType = Excel.ColumnType.NUMERIC, sort = 117)
    private Integer takeUv;

    /**
     * 领取CVR
     */
    @Excel(name = "领取CVR", sort = 118)
    private String takeCvr;

    /**
     * 领取成本
     */
    @Excel(name = "领取成本", cellType = Excel.ColumnType.NUMERIC, sort = 119)
    private String takeCost;

    /**
     * 落地页转化PV
     */
    @Excel(name = "落地页转化PV", cellType = Excel.ColumnType.NUMERIC, sort = 120)
    private Integer lpClickPv;

    /**
     * 落地页转化UV
     */
    @Excel(name = "落地页转化UV", cellType = Excel.ColumnType.NUMERIC, sort = 121)
    private Integer lpClickUv;

    /**
     * CVR(PV)
     */
    @Excel(name = "CVR pv", sort = 122)
    private String cvr;

    /**
     * CVR(UV)
     */
    @Excel(name = "CVR uv", sort = 123)
    private String cvrUv;

    /**
     * 落地页转化成本(元)
     */
    @Excel(name = "落地页转化成本", cellType = Excel.ColumnType.NUMERIC, sort = 124)
    private String lpClickCost;

    /**
     * 激活
     */
    @Excel(name = "激活", cellType = Excel.ColumnType.NUMERIC, sort = 201)
    private Integer register;

    /**
     * 支付
     */
    @Excel(name = "支付", cellType = Excel.ColumnType.NUMERIC, sort = 202)
    private Integer pay;

    /**
     * 支付成本(元)
     */
    @Excel(name = "支付成本", cellType = Excel.ColumnType.NUMERIC, sort = 203)
    private String payCost;

    /**
     * 退款
     */
    @Excel(name = "退款", cellType = Excel.ColumnType.NUMERIC, sort = 204)
    private Integer refund;

    /**
     * 投诉
     */
    @Excel(name = "投诉", cellType = Excel.ColumnType.NUMERIC, sort = 205)
    private Integer complain;

    /**
     * APP激活
     */
    @Excel(name = "APP激活", cellType = Excel.ColumnType.NUMERIC, sort = 206)
    private Integer appActive;

    /**
     * 支付金额
     */
    @Excel(name = "支付金额", cellType = Excel.ColumnType.NUMERIC, sort = 207)
    private Double extPrice;
}
