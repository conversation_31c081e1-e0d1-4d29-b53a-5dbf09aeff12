package com.ruoyi.system.vo.qualification;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资质列表
 * <AUTHOR>
 * @date 2021/9/14 2:56 下午
 */
@Data
public class QualificationListVO implements Serializable {
    private static final long serialVersionUID = -6797627269783305597L;
    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    /**
     * 账户id
     */
    private Long accountId;
    /**
     * 邮箱账号
     */
    private String email;

    /**
     * 开户名
     */
    private String bankAccountName;
    /**
     * 开户银行
     */
    private String bankName;
    /**
     * 银行账户
     */
    private String bankAccount;

    /**
     * 营业执照号
     */
    private String businessLicense;
    /**
     * 营业执照图
     */
    private String businessLicenseImg;
    /**
     * 备注
     */
    private String remarkText;
}
