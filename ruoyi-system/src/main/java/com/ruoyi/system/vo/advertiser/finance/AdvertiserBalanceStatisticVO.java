package com.ruoyi.system.vo.advertiser.finance;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告主/代理商余额汇总VO
 *
 * <AUTHOR>
 * @date 2023-02-17
 */
@Data
public class AdvertiserBalanceStatisticVO implements Serializable {
    private static final long serialVersionUID = 3516190746205025109L;

    /**
     * 总余额(分)
     */
    private Long totalAmount;

    /**
     * 现金余额(分)
     */
    private Long cashAmount;

    /**
     * 返货余额(分)
     */
    private Long rebateAmount;

    /**
     * 调整金额(分)
     */
    private Long offsetAmount;
}
