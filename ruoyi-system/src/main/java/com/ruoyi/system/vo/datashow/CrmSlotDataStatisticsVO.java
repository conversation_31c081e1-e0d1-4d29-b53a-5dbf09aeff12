package com.ruoyi.system.vo.datashow;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告位统计数据vo crm专用
 *
 * <AUTHOR>
 * @date 2021/9/1 2:42 下午
 */
@Data
public class CrmSlotDataStatisticsVO implements Serializable {
    private static final long serialVersionUID = 16693589434308484L;

    /**
     * 广告位访问pv
     */
    private Integer slotRequestPv = 0;

    /**
     * 广告位访问uv
     */
    private Integer slotRequestUv = 0;

    /**
     * 活动参与pv
     */
    private Integer joinPv = 0;

    /**
     * 活动参与uv
     */
    private Integer joinUv = 0;

    /**
     * 总消耗
     */
    private Integer totalConsume = 0;

    /**
     * 诺禾广告消耗(分)
     */
    private Integer nhConsume = 0;

    /**
     * 外部广告消耗(分)
     */
    private Integer outerConsume = 0;

    /**
     * 诺禾消耗(分)
     */
    private Long nhCost = 0L;

    /**
     * 外部结算款
     */
    private Long outerCost;

    /**
     * 结算总金额
     */
    private Long totalCost;

    /**
     * 媒体应得收益(分)
      */
    private Integer appRevenue = 0;
}
