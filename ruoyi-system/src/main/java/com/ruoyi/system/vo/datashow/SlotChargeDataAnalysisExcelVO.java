package com.ruoyi.system.vo.datashow;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

/**
 * 解析广告位结算数据excel vo
 *
 * <AUTHOR>
 * @date 2021/9/1 2:42 下午
 */
@Data
public class SlotChargeDataAnalysisExcelVO implements Serializable {


    private static final long serialVersionUID = -2784793074830648053L;
    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String curDate;
    /**
     * 广告位访问pv
     */
    private String slotRequestPv;
    /**
     * 广告位访问pv
     */
    private String slotRequestUv;
    /**
     * 计费类型
     */
    private String chargeType;

    /**
     * 计费价格
     */
    private String chargePrice;

    /**
     * 媒体收益
     */
    private String appRevenue;
    /**
     * 诺禾结算金额
     */
    private String nhCost ;
    /**
     * 外部结算金额
     */
    private String outerCost ;
    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 行数
     */
    private Long row;
}
