package com.ruoyi.system.vo.slot;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 广告位VO(用于下拉列表)
 *
 * <AUTHOR>
 * @date 2023-05-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SlotSelectVO implements Serializable {
    private static final long serialVersionUID = -6652470538767655309L;

    /**
     * 广告位ID
     */
    private Long id;

    /**
     * 广告位名称
     */
    private String slotName;
}
