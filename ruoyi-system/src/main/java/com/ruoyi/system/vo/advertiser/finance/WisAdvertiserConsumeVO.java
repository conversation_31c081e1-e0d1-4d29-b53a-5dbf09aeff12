package com.ruoyi.system.vo.advertiser.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告主消费记录VO
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@Data
public class WisAdvertiserConsumeVO implements Serializable {
    private static final long serialVersionUID = 3952798423493664551L;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 消费金额(分)
     */
    private Integer consumeAmount;
}
