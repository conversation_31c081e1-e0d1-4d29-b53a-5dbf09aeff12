package com.ruoyi.system.vo.invoice;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 发票详情列表vo
 * <AUTHOR>
 * @date 2022/10/20 4:22 下午
 */
@Data
public class InvoiceInfoListVO implements Serializable {
    private static final long serialVersionUID = -5552722568319953056L;

    private Long id;
    /**
     * 账号id
     */
    private Long accountId;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 邮箱
     */
    private String email;

    /**
     * 发票单号
     */
    private String invoiceNumber;

    /**
     * 发票金额
     */
    private Integer invoiceAmount;

    /**
     * 操作人名
     */
    private String operatorName;

    /**
     * 备注
     */
    private String remarkText;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

}
