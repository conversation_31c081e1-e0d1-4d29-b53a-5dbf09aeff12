package com.ruoyi.system.vo.invoice;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 发票列表vo
 * <AUTHOR>
 * @date 2022/10/20 4:22 下午
 */
@Data
public class InvoiceListVO implements Serializable {
    private static final long serialVersionUID = 8213245729007983244L;
    /**
     * 账号id
     */
    private Long accountId;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 邮箱
     */
    private String email;

    /**
     * 预付款总金额(分)
     */
    private Integer prepayAmount;

    /**
     * 发票总金额
     */
    private Integer invoiceAmountSum;

    /**
     * 备注
     */
    private String remarkText;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

}
