package com.ruoyi.system.vo.advert;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 配置(包含广告位)日数据VO
 *
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
public class OrientSlotDayDataVO2 extends OrientBaseDataVO implements Serializable {
    private static final long serialVersionUID = -7978023002796176561L;

    /**
     * 媒体ID
     */
    @Excel(name = "媒体ID", sort = 15)
    private Long appId;

    /**
     * 媒体名称
     */
    @Excel(name = "媒体名称", width = 20, sort = 16)
    private String appName;

    /**
     * 广告位ID
     */
    @Excel(name = "广告位ID", sort = 17)
    private Long slotId;

    /**
     * 广告位名称
     */
    @Excel(name = "广告位名称", width = 30, sort = 18)
    private String slotName;

    /**
     * 配置状态
     */
    @Excel(name = "配置状态", sort = 19)
    private String statusStr;

    /**
     * 配置预算
     */
    @Excel(name = "配置预算", sort = 20)
    private String budget;

    /**
     * 负责人
     */
    @Excel(name = "负责人", width = 40)
    private String managerName;
}
