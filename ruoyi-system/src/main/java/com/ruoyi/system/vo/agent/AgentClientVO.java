package com.ruoyi.system.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.vo.advertiser.qualification.AdvertiserQualificationVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 代理商客户VO
 *
 * <AUTHOR>
 * @date 2022/10/26
 */
@Data
public class AgentClientVO implements Serializable {
    private static final long serialVersionUID = -7651705029891307413L;

    /**
     * 广告主ID
     */
    private Long id;

    /**
     * 注册邮箱
     */
    private String email;

    /**
     * 广告主公司名称
     */
    private String companyName;

    /**
     * 资质信息
     */
    private List<AdvertiserQualificationVO> qualificationList;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 账号状态:0.启用,4.禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
