package com.ruoyi.system.vo.privatesphere;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 私域账户vo
 * <AUTHOR>
 * @date 2023/2/7 11:45
 */
@Data
public class PrivateSphereChannelListVO implements Serializable {
    private static final long serialVersionUID = 5575928021556153429L;
    /**
     * id
     */
    private Long id;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公司id
     */
    private Long accountId;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 渠道名称
     */
    private String channel;
    /**
     * 渠道号列表
     */
    private List<String> channelNumbers;
    /**
     * 操作人名
     */
    private String operName;
    /**
     * 操作人id
     */
    private Long operAccountId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;
}
