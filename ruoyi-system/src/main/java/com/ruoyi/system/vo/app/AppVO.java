package com.ruoyi.system.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体VO
 *
 * <AUTHOR>
 * @date 2021-07-14
 */
@Data
public class AppVO implements Serializable {
    private static final long serialVersionUID = 8840654172094963130L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 媒体名称
     */
    private String appName;

    /**
     * 负责人名称
     */
    private String managerName;

    /**
     * 主体类型:1.Android,2.iOS,3.wap,4.小程序,5.公众号
     */
    private Integer appType;

    /**
     * 应用标识
     */
    private String appKey;

    /**
     * 应用秘钥
     */
    private String appSecret;

    /**
     * 广告位数量
     */
    private Integer slotCount;

    /**
     * 昨日广告位访问UV
     */
    private Integer ydaySlotReqUv;

    /**
     * 昨日媒体应得收益
     */
    private Long ydayAppRevenue;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;
}
