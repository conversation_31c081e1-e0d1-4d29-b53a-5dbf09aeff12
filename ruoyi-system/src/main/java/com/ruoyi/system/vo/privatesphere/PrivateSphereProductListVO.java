package com.ruoyi.system.vo.privatesphere;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 私域产品vo
 * <AUTHOR>
 * @date 2023/2/7 11:45
 */
@Data
public class PrivateSphereProductListVO implements Serializable {
    private static final long serialVersionUID = 5995858568654118893L;
    /**
     * 产品id
     */
    private Long id;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * 公司id
     */
    private String companyName;
    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date gmtCreate;

}
