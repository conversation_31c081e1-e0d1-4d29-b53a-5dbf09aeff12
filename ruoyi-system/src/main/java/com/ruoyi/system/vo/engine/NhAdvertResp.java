package com.ruoyi.system.vo.engine;

import com.ruoyi.system.bo.advert.AdvertExtInfo;
import com.ruoyi.system.entity.advert.InnovateLayerInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * 诺禾广告详情
 *
 * <AUTHOR>
 * @date 2021/8/12
 */
@Data
public class NhAdvertResp implements Serializable {
    private static final long serialVersionUID = 1541046181129237673L;

    /**
     * 广告ID
     */
    private Long advertId;

    /**
     * 广告分类
     * @see com.ruoyi.common.enums.advert.AdvertCategory
     */
    private Integer advertCategory;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 广告缩略图
     */
    private String thumbnailImg;

    /**
     * 落地页链接
     */
    private String landpageUrl;

    /**
     * 广告标题
     */
    private String advertTitle;

    /**
     * 按钮文案
     */
    private String buttonText;

    /**
     * 弹层类型
     */
    private Integer layerType;

    /**
     * 弹层皮肤类型(1.刮刮卡,2.翻牌子)
     */
    private Integer skinType;

    /**
     * 弹层ID
     */
    private Long layerId;

    /**
     * 素材图链接
     */
    private String materialImg;

    /**
     * 轮播图
     */
    private String gifImg;

    /**
     * 弹层信息
     */
    private InnovateLayerInfo layerInfo;

    /**
     * 额外信息
     */
    private AdvertExtInfo extInfo;
}
