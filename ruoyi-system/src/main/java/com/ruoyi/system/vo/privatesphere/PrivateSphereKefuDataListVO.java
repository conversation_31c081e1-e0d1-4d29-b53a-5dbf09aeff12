package com.ruoyi.system.vo.privatesphere;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 私域客服数据vo
 * <AUTHOR>
 * @date 2023/2/10 14:24
 */
@Data
public class PrivateSphereKefuDataListVO implements Serializable {

    private static final long serialVersionUID = 5970966844237967265L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date curDate;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * 渠道名称
     */
    private String channel;

    /**
     * 客服渠道id
     */
    private Long kefuChannelId;

    /**
     * 客服公司账号id
     */
    private Long sspAccountId;

    /**
     * 客服公司名称
     */
    private String sspCompanyName;

    /**
     * 外呼总数
     */
    private Integer callCount;

    /**
     * 接通数
     */
    private Integer connectCount;

    /**
     * 接通率(接通数/外呼总数)
     */
    private String connectRate;

    /**
     * 进入人工数
     */
    private Integer personCount;

    /**
     * 意向率（进入人工数/接通数）
     */
    private String intentRate;

    /**
     * 入群数
     */
    private Integer entryCount;
    /**
     * 反馈入群数
     */
    private Integer feedbackEntryCount;
    /**
     * 成单率(入群数/进入人工数)
     */
    private String entryPercent;
    /**
     * 成单比（成单/接通数）
     */
    private String entryRatio;

    /**
     * 数据成本(分),会有小数
     */
    private Float dataCost;

    /**
     * 反馈比 反馈入群数/真实入群数
     */
    private String feedbackEntryRatio;
    /**
     * 数据成本数
     */
    private Integer dataCostCount;

    /**
     * AI+线路成本
     */
    private Integer lineCost;

    /**
     * 人工成本
     */
    private Integer personCost;

    /**
     * 总成本 （数据成本+线路成本+人工成本）
     */
    private Integer sumCost;

    /**
     * 单入群成本 (总成本/入群数)
     */
    private String singleEntryCost;
    /**
     * 总转化金额
     */
    private Integer sumConverAmount;

    /**
     * 总退款金额
     */
    private Integer sumRefundAmount;
    /**
     * R值 （总转化金额-总退款）/总入群
     */
    private String rValue;

    /**
     * 渠道数据列表
     */
    private List<PrivateSphereChannelDataListVO> channelDataList;

}
