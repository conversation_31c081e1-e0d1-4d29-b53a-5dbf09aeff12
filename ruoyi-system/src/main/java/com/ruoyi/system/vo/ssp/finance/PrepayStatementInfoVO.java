package com.ruoyi.system.vo.ssp.finance;

import com.ruoyi.system.vo.qualification.QualificationInfoVO;
import com.ruoyi.system.vo.withdraw.WithdrawInvoiceAddressInfoVO;
import com.ruoyi.system.vo.withdraw.WithdrawInvoiceInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预付款结算单详情VO
 *
 * <AUTHOR>
 * @date 2022-08-03
 */
@Data
public class PrepayStatementInfoVO implements Serializable {
    private static final long serialVersionUID = 6494746302357689484L;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 付款类型:1.预付款,0.后付款
     */
    private Integer payType;

    /**
     * 结算金额
     */
    private Integer appRevenue;

    /**
     * 预付款欠款金额
     */
    private Long prepayAmount;

    /**
     * 客户资质信息
     */
    private QualificationInfoVO qualificationInfo;

    /**
     * 结算单明细
     */
    private List<PrepayStatementItemVO> items;

    /**
     * 发票快递信息
     */
    private WithdrawInvoiceAddressInfoVO addressInfoVO = new WithdrawInvoiceAddressInfoVO();
    /**
     * 开票信息
     */
    private WithdrawInvoiceInfoVO invoiceInfoVO = new WithdrawInvoiceInfoVO();
}
