package com.ruoyi.system.vo.qualification;

import lombok.Data;

import java.io.Serializable;

/**
 * 账户资质信息
 *
 * <AUTHOR>
 * @date 2021/9/14 1:46 下午
 */
@Data
public class QualificationInfoVO implements Serializable {
    private static final long serialVersionUID = 4393370809156451643L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 邮箱账号
     */
    private String email;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 营业执照号
     */
    private String businessLicense;

    /**
     * 营业执照图
     */
    private String businessLicenseImg;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行账户
     */
    private String bankAccount;

    /**
     * 开户名
     */
    private String bankAccountName;

    /**
     * 备注
     */
    private String remarkText;
}
