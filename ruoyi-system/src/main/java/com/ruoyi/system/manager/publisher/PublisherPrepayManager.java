package com.ruoyi.system.manager.publisher;

import com.ruoyi.system.req.publisher.prepay.PrepayApplyReq;
import com.ruoyi.system.req.publisher.prepay.PrepayAuditReq;

/**
 * 媒体预付款Manager接口
 *
 * <AUTHOR>
 * @date 2022/07/29
 */
public interface PublisherPrepayManager {

    /**
     * 预付款申请
     *
     * @param req 请求参数
     */
    void prepayApply(PrepayApplyReq req);

    /**
     * 预付款审核
     *
     * @param req 请求参数
     */
    void prepayAudit(PrepayAuditReq req);

    /**
     * 预付款结算
     *
     * @param accountId 媒体账号ID
     * @param statementId 结算单ID
     * @param revenue 收益
     * @return 预付款结算后剩余收益
     */
    Integer prepayStatement(Long accountId, Long statementId, int revenue);
}
