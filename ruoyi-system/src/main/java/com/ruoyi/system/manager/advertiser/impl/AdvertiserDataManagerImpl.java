package com.ruoyi.system.manager.advertiser.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.IsDefaultEnum;
import com.ruoyi.common.enums.advert.AdvertCostBillingTypeEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.advert.AdvertDaySumDataBo;
import com.ruoyi.system.bo.advert.AdvertOrientLandpageBo;
import com.ruoyi.system.bo.advert.OrientDayDataBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeCategorySumBo;
import com.ruoyi.system.bo.landpage.ConvDataBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity;
import com.ruoyi.system.entity.datashow.AdvertDayData;
import com.ruoyi.system.manager.advertiser.AdvertiserDataManager;
import com.ruoyi.system.req.advertiser.data.CrmAdvertiserCpcDataReq;
import com.ruoyi.system.req.advertiser.AdvertiserCpcDataReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeCategoryRecordService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.datasource.AdvertDayConsumeDataService;
import com.ruoyi.system.service.datasource.AdvertDayDataService;
import com.ruoyi.system.service.datasource.AdvertSlotConvDayDataService;
import com.ruoyi.system.service.datasource.OrientHourDataService;
import com.ruoyi.system.service.engine.cache.LandpageCacheService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.manager.AgentService;
import com.ruoyi.system.util.LandpageUtil;
import com.ruoyi.system.vo.advertiser.finance.CrmAdvertiserCpcDataListVO;
import com.ruoyi.system.vo.advertiser.finance.WisAdvertiserCpcDataListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.core.page.TableSupport.PAGE_NUM;
import static com.ruoyi.common.core.page.TableSupport.PAGE_SIZE;
import static com.ruoyi.common.enums.account.AccountMainType.isCrmUser;
import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * 广告主数据相关manager
 *
 * <AUTHOR>
 * @date 2022/4/7 7:54 下午
 */
@Service
public class AdvertiserDataManagerImpl implements AdvertiserDataManager {

    @Autowired
    private AdvertDayDataService advertDayDataService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private AdvertiserConsumeCategoryRecordService advertiserConsumeCategoryRecordService;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Autowired
    private AdvertDayConsumeDataService advertDayConsumeDataService;

    @Autowired
    private LandpageCacheService landpageCacheService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AdvertSlotConvDayDataService advertSlotConvDayDataService;

    @Autowired
    private OrientHourDataService orientHourDataService;

    @Override
    public PageInfo<WisAdvertiserCpcDataListVO> selectCpcDataList(AdvertiserCpcDataReq req, Boolean isExport) {
        List<Long> advertIds = advertService.selectAdvertIdsByAdvertiserId(req.getAdvertiserId());
        if (CollectionUtils.isEmpty(advertIds)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }
        req.setAdvertIds(advertIds);

        // 剔除不展示的日期
        List<Date> dateList = dspAdvertiserConsumeRecordService.selectInvisibleDateList(req.getAdvertiserId());
        req.setInvisibleDateList(dateList);

        // 离线广告主不展示当日数据
        boolean isOfflineData = whitelistService.contains(OFFLINE_DATA_ADVERTISER, req.getAdvertiserId());
        if (isOfflineData) {
            Date endDate = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -1);
            req.setEndDate(null == req.getEndDate() || req.getEndDate().after(endDate) ? endDate : req.getEndDate());
        }

        if (!isExport) {
            TableSupport.startPage();
        }
        List<AdvertDaySumDataBo> advertDaySumDataBos = advertDayDataService.selectAdvertDaySumData(req);
        if (CollectionUtils.isEmpty(advertDaySumDataBos)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        List<AdvertiserConsumeCategorySumBo> categorySumBos = advertiserConsumeCategoryRecordService.selectSumByDateAndAccountId(req.getAdvertiserId(), req.getStartDate(), req.getEndDate(), AdvertCostBillingTypeEnum.CPC.getType());
        Map<String, Integer> dateSumMap = categorySumBos.stream().collect(Collectors.toMap(data -> DateUtil.formatDate(data.getCurDate()), AdvertiserConsumeCategorySumBo::getConsumeAmount));
        // 离线数据广告主展示调整的计费点击和消费金额
        Map<Date, DspAdvertiserConsumeRecordEntity> consumeRecordMap = dspAdvertiserConsumeRecordService.selectVisibleDataMap(req.getStartDate(), req.getEndDate(), req.getAdvertiserId());
        // 后端转化数据
        Map<Date, ConvDataBo> convMap = advertSlotConvDayDataService.countByDateAndAdvertIds2(req.getStartDate(), req.getEndDate(), req.getAdvertIds());

        return PageInfoUtils.dto2Vo(advertDaySumDataBos, data -> {
            WisAdvertiserCpcDataListVO vo = BeanUtil.copyProperties(data, WisAdvertiserCpcDataListVO.class);
            vo.setConsumeAmount(NumberUtils.defaultInt(dateSumMap.get(DateUtil.formatDate(data.getCurDate()))));
            Optional.ofNullable(consumeRecordMap.get(data.getCurDate())).ifPresent(record -> {
                vo.setBillingClickPv(record.getBillingClickPv());
                vo.setBillingClickUv(record.getBillingClickUv());
                vo.setConsumeAmount(record.getConsumeAmount());
            });
            Optional.ofNullable(convMap.get(data.getCurDate())).ifPresent(conv -> {
                vo.setPayCost(NumberUtils.calculateRate(vo.getConsumeAmount(), conv.getPay() * 100));
            });
            vo.setLpClickCost(NumberUtils.calculateRate(vo.getConsumeAmount(),vo.getLpClickPv()*100));
            return vo;
        });
    }

    @Override
    public PageInfo<WisAdvertiserCpcDataListVO> selectCpcLandpageDataList(AdvertiserCpcDataReq req, Boolean isExport) {
        List<Long> advertIds = advertService.selectAdvertIdsByAdvertiserId(req.getAdvertiserId());
        if (CollectionUtils.isEmpty(advertIds)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        // 剔除不展示的日期
        List<Date> invisibleDateList = dspAdvertiserConsumeRecordService.selectInvisibleDateList(req.getAdvertiserId());

        // 离线广告主不展示当日数据
        boolean isOfflineData = whitelistService.contains(OFFLINE_DATA_ADVERTISER, req.getAdvertiserId());
        if (isOfflineData) {
            Date endDate = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -1);
            req.setEndDate(null == req.getEndDate() || req.getEndDate().after(endDate) ? endDate : req.getEndDate());
        } else {
            // 离线转实时广告主，不展示转换那天之前的数据
            AccountExtInfo extInfo = accountService.selectAccountExtInfoById(req.getAdvertiserId());
            if (null != extInfo && StringUtils.isNotBlank(extInfo.getOtoDate())) {
                Date startDate = DateUtil.parseDate(extInfo.getOtoDate());
                req.setStartDate(null == req.getStartDate() || req.getStartDate().before(startDate) ? startDate : req.getStartDate());
            }
        }

        List<OrientDayDataBo> orientDataList = orientHourDataService.selectOrientDataForLandpage(advertIds, req.getStartDate(), req.getEndDate(), invisibleDateList);
        if (CollectionUtils.isEmpty(orientDataList)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        // 查询广告主链接
        Map<Pair<Long, Long>, String> landpageMap = getAdvertLandpageUrl(ListUtils.mapToList(orientDataList, OrientDayDataBo::getAdvertId));

        // 汇总数据
        Map<String, WisAdvertiserCpcDataListVO> dataMap = new HashMap<>();
        orientDataList.forEach(orientData -> {
            String landpageUrl = landpageMap.getOrDefault(Pair.of(orientData.getAdvertId(), orientData.getOrientId()), "");
            String key = DateUtil.formatDate(orientData.getCurDate()) + "_" + landpageUrl;
            WisAdvertiserCpcDataListVO dataVo = dataMap.get(key);
            if (null == dataVo) {
                dataVo = new WisAdvertiserCpcDataListVO();
                dataVo.setCurDate(orientData.getCurDate());
                dataVo.setLandpageUrl(landpageUrl);
                dataVo.setBillingClickPv(0);
                dataVo.setBillingClickUv(0);
                dataVo.setLpClickPv(0);
                dataVo.setLpClickUv(0);
                dataVo.setConsumeAmount(0);
                dataVo.setPay(0);
            }
            dataVo.setBillingClickPv(dataVo.getBillingClickPv() + orientData.getBillingClickPv());
            dataVo.setBillingClickUv(dataVo.getBillingClickUv() + orientData.getBillingClickUv());
            dataVo.setLpClickPv(dataVo.getLpClickPv() + orientData.getLpClickPv());
            dataVo.setLpClickUv(dataVo.getLpClickUv() + orientData.getLpClickUv());
            dataVo.setConsumeAmount(dataVo.getConsumeAmount() + orientData.getConsume());
            dataVo.setLpClickCost(NumberUtils.calculateRate(dataVo.getConsumeAmount(),dataVo.getLpClickPv()*100));
            dataVo.setPay(dataVo.getPay() + orientData.getPay());
            dataVo.setPayCost(NumberUtils.calculateRate(dataVo.getConsumeAmount(), dataVo.getPay() * 100));
            dataMap.put(key, dataVo);
        });

        // 排序
        List<WisAdvertiserCpcDataListVO> dataList = dataMap.values().stream().sorted((o1, o2) -> {
            int c = o2.getCurDate().compareTo(o1.getCurDate());
            return c != 0 ? c : (o2.getBillingClickPv().compareTo(o1.getBillingClickPv()));
        }).collect(Collectors.toList());
        int total = dataList.size();
        // 导出分页
        if (!isExport) {
            int pageNum = ServletUtils.getParameterToInt(PAGE_NUM);
            int pageSize = ServletUtils.getParameterToInt(PAGE_SIZE);
            dataList = ListUtil.page(pageNum - 1, pageSize, dataList);

            // 构造分页数据
            Page<WisAdvertiserCpcDataListVO> page = new Page<>(pageNum, pageSize);
            page.setTotal(total);
            PageInfo<WisAdvertiserCpcDataListVO> result = new PageInfo<>(page);
            result.setList(dataList);
            return result;
        } else {
            return PageInfo.of(dataList);
        }
    }

    @Override
    public PageInfo<CrmAdvertiserCpcDataListVO> batchSelectCpcDataList(CrmAdvertiserCpcDataReq req, Boolean isExport) {
        List<Long> advertiserIds = req.getAdvertiserIds();
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }
        // 查询广告
        Map<Long, Long> advertAdvertiserMap = advertService.selectAdvertIdMapByAdvertiserIds(advertiserIds);
        if (MapUtil.isEmpty(advertAdvertiserMap)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }
        List<Long> advertIds = new ArrayList<>(advertAdvertiserMap.keySet());

        // 查询广告数据
        AdvertDayData param = new AdvertDayData();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());
        param.setAdvertIds(advertIds);
        List<AdvertDayData> advertDataList = advertDayDataService.selectAdvertDayDataList(param);
        if (CollectionUtils.isEmpty(advertDataList)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        // 离线广告主
        List<Long> offlineAdvertiserIds = whitelistService.list(OFFLINE_DATA_ADVERTISER, Long.class);
        // 不可见日期
        Map<Long, List<Date>> invisibleDateMap = dspAdvertiserConsumeRecordService.selectInvisibleDateMap(advertiserIds);
        Date today = DateUtil.beginOfDay(new Date());
        boolean isCrmUser = isCrmUser(SecurityUtils.getLoginUser().getMainType());

        // 查询广告消耗
        Map<String, Integer> consumeMap = advertDayConsumeDataService.selectConsumeByDateAndAdvertIds(req.getStartDate(), req.getEndDate(), advertIds);
        // 查询广告主名称
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(advertiserIds);
        // 离线数据广告主展示调整的计费点击和消费金额
        Map<String, DspAdvertiserConsumeRecordEntity> consumeRecordMap = dspAdvertiserConsumeRecordService.selectMap(req.getStartDate(), req.getEndDate(), advertiserIds);
        // 查询代理商
        Map<Long, Account> agentMap = agentService.selectAgentMap(advertiserIds);
        // 查询后端转化数据
        Map<String, ConvDataBo> convMap = advertSlotConvDayDataService.countByDateAndAdvertIds(req.getStartDate(), req.getEndDate(), advertIds);

        // 汇总数据
        Map<String, CrmAdvertiserCpcDataListVO> dataMap = new HashMap<>();
        advertDataList.forEach(advertData -> {
            Long advertId = advertData.getAdvertId();
            Long advertiserId = advertAdvertiserMap.get(advertId);

            // 不可见日期判断
            if (null != invisibleDateMap.get(advertiserId) && invisibleDateMap.get(advertiserId).contains(advertData.getCurDate())
                    || offlineAdvertiserIds.contains(advertiserId) && !advertData.getCurDate().before(today)) {
                return;
            }

            // 组装数据
            String date = DateUtil.formatDate(advertData.getCurDate());
            String key = date + "_" + advertiserId;
            CrmAdvertiserCpcDataListVO dataVo = dataMap.get(key);
            if (null == dataVo) {
                dataVo = new CrmAdvertiserCpcDataListVO();
                dataVo.setCurDate(advertData.getCurDate());
                dataVo.setAdvertiserId(advertiserId);
                dataVo.setAdvertiserName(advertiserNameMap.get(advertiserId));
                dataVo.setBillingClickPv(0);
                dataVo.setBillingClickUv(0);
                dataVo.setLpExposurePv(0);
                dataVo.setLpExposureUv(0);
                dataVo.setLpClickPv(0);
                dataVo.setLpClickUv(0);
                dataVo.setConsumeAmount(0);
                dataVo.setPay(0);

                Account agent = agentMap.get(advertiserId);
                if (null != agent) {
                    dataVo.setAgentId(agent.getId());
                    dataVo.setAgentName(agent.getCompanyName());
                }
            }
            dataVo.setBillingClickPv(dataVo.getBillingClickPv() + advertData.getBillingClickPv());
            dataVo.setBillingClickUv(dataVo.getBillingClickUv() + advertData.getBillingClickUv());
            if (isCrmUser) {
                dataVo.setLpExposurePv(dataVo.getLpExposurePv() + advertData.getLpExposurePv());
                dataVo.setLpExposureUv(dataVo.getLpExposureUv() + advertData.getLpExposureUv());
            }
            dataVo.setLpClickPv(dataVo.getLpClickPv() + advertData.getLpClickPv());
            dataVo.setLpClickUv(dataVo.getLpClickUv() + advertData.getLpClickUv());
            dataVo.setConsumeAmount(dataVo.getConsumeAmount() + consumeMap.getOrDefault(advertId + "-" + date, 0));

            // 离线广告主调整的计费点击和消费金额
            DspAdvertiserConsumeRecordEntity consumeRecord = consumeRecordMap.get(date + "_" + advertiserId);
            if (null != consumeRecord) {
                dataVo.setBillingClickPv(consumeRecord.getBillingClickPv());
                dataVo.setBillingClickUv(consumeRecord.getBillingClickUv());
                dataVo.setConsumeAmount(consumeRecord.getConsumeAmount());
            }
            dataVo.setLpClickCost(NumberUtils.calculateRate(dataVo.getConsumeAmount(), dataVo.getLpClickPv() * 100));
            ConvDataBo conv = convMap.get(advertId + "-" + date);
            if (null != conv) {
                dataVo.setPay(dataVo.getPay() + NumberUtils.defaultInt(conv.getPay()));
            }
            dataVo.setPayCost(NumberUtils.calculateRate(dataVo.getConsumeAmount(), dataVo.getPay() * 100));
            dataMap.put(key, dataVo);
        });

        // 排序
        List<CrmAdvertiserCpcDataListVO> dataList = dataMap.values().stream().sorted((o1, o2) -> {
            int c = o2.getCurDate().compareTo(o1.getCurDate());
            return c != 0 ? c : (o2.getBillingClickPv().compareTo(o1.getBillingClickPv()));
        }).collect(Collectors.toList());
        int total = dataList.size();
        // 导出分页
        if (!isExport) {
            int pageNum = ServletUtils.getParameterToInt(PAGE_NUM);
            int pageSize = ServletUtils.getParameterToInt(PAGE_SIZE);
            dataList = ListUtil.page(pageNum - 1, pageSize, dataList);

            // 构造分页数据
            Page<CrmAdvertiserCpcDataListVO> page = new Page<>(pageNum, pageSize);
            page.setTotal(total);
            PageInfo<CrmAdvertiserCpcDataListVO> result = new PageInfo<>(page);
            result.setList(dataList);
            return result;
        } else {
            return PageInfo.of(dataList);
        }
    }

    @Override
    public PageInfo<CrmAdvertiserCpcDataListVO> batchSelectCpcLandpageDataList(CrmAdvertiserCpcDataReq req, Boolean isExport) {
        List<Long> advertiserIds = req.getAdvertiserIds();
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }
        // 查询广告
        Map<Long, Long> advertAdvertiserMap = advertService.selectAdvertIdMapByAdvertiserIds(advertiserIds);
        if (MapUtil.isEmpty(advertAdvertiserMap)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }
        List<Long> advertIds = new ArrayList<>(advertAdvertiserMap.keySet());

        // 查询广告配置数据
        List<OrientDayDataBo> orientDataList = orientHourDataService.selectOrientDataForLandpage(advertIds, req.getStartDate(), req.getEndDate(), null);
        if (CollectionUtils.isEmpty(orientDataList)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        // 离线广告主
        List<Long> offlineAdvertiserIds = whitelistService.list(OFFLINE_DATA_ADVERTISER, Long.class);
        // 不可见日期
        Map<Long, List<Date>> invisibleDateMap = dspAdvertiserConsumeRecordService.selectInvisibleDateMap(advertiserIds);
        Date today = DateUtil.beginOfDay(new Date());
        boolean isCrmUser = isCrmUser(SecurityUtils.getLoginUser().getMainType());

        // 查询广告主链接
        Map<Pair<Long, Long>, String> landpageMap = getAdvertLandpageUrl(advertIds);
        // 查询广告主名称
        Map<Long, String> advertiserNameMap = advertiserService.selectAdvertiserNameMap(advertiserIds);
        // 广告配置的链接
        Map<Pair<Long, Long>, String> orientLandpageMap = isCrmUser ? getOrientLandpageUrl(advertIds) : Collections.emptyMap();
        // 离线数据广告主展示调整的计费点击和消费金额
        Map<String, DspAdvertiserConsumeRecordEntity> consumeRecordMap = dspAdvertiserConsumeRecordService.selectMap(req.getStartDate(), req.getEndDate(), advertiserIds);
        // 查询代理商
        Map<Long, Account> agentMap = agentService.selectAgentMap(advertiserIds);
        // 广告主离线转实时时间
        Map<Long, Date> otoDateMap = getOtoDateMap(advertiserIds);

        // 汇总数据
        Map<String, CrmAdvertiserCpcDataListVO> dataMap = new HashMap<>();
        Map<String, CrmAdvertiserCpcDataListVO> originAdvertiserDataMap = new HashMap<>();
        orientDataList.forEach(orientData -> {
            Long advertId = orientData.getAdvertId();
            Long advertiserId = advertAdvertiserMap.get(advertId);
            Date curDate = orientData.getCurDate();
            String date = DateUtil.formatDate(orientData.getCurDate());

            // 统计广告主维度原计费点击数据
            CrmAdvertiserCpcDataListVO advertiserDataVo = originAdvertiserDataMap.get(date + "_" + advertiserId);
            if (null == advertiserDataVo) {
                advertiserDataVo = new CrmAdvertiserCpcDataListVO();
                advertiserDataVo.setCurDate(orientData.getCurDate());
                advertiserDataVo.setAdvertiserId(advertiserId);
                advertiserDataVo.setBillingClickPv(0);
                advertiserDataVo.setBillingClickUv(0);
                advertiserDataVo.setConsumeAmount(0);
            }
            advertiserDataVo.setBillingClickPv(advertiserDataVo.getBillingClickPv() + orientData.getBillingClickPv());
            advertiserDataVo.setBillingClickUv(advertiserDataVo.getBillingClickUv() + orientData.getBillingClickUv());
            advertiserDataVo.setConsumeAmount(advertiserDataVo.getConsumeAmount() + orientData.getConsume());
            originAdvertiserDataMap.put(date + "_" + advertiserId, advertiserDataVo);

            // 不可见日期判断
            if (null != invisibleDateMap.get(advertiserId) && invisibleDateMap.get(advertiserId).contains(curDate)
                    || offlineAdvertiserIds.contains(advertiserId) && !curDate.before(today)) {
                return;
            }
            // 离线转实时广告主判断
            if (!offlineAdvertiserIds.contains(advertiserId) && otoDateMap.containsKey(advertiserId) && curDate.before(otoDateMap.get(advertiserId))) {
                return;
            }
            // 过滤广告主落地页
            String landpageUrl = landpageMap.getOrDefault(Pair.of(advertId, orientData.getOrientId()), "");
            if (StringUtils.isNotBlank(req.getLandpageUrl()) && !StrUtil.containsIgnoreCase(landpageUrl, req.getLandpageUrl())) {
                return;
            }

            // 组装数据
            String key = date + "_" + advertiserId + "_" + landpageUrl;
            CrmAdvertiserCpcDataListVO dataVo = dataMap.get(key);
            if (null == dataVo) {
                dataVo = new CrmAdvertiserCpcDataListVO();
                dataVo.setCurDate(curDate);
                dataVo.setAdvertiserId(advertiserId);
                dataVo.setAdvertiserName(advertiserNameMap.get(advertiserId));
                dataVo.setLandpageUrl(landpageUrl);
                dataVo.setBillingClickPv(0);
                dataVo.setBillingClickUv(0);
                dataVo.setLpExposurePv(0);
                dataVo.setLpExposureUv(0);
                dataVo.setLpClickPv(0);
                dataVo.setLpClickUv(0);
                dataVo.setConsumeAmount(0);
                dataVo.setPay(0);
                if (StringUtils.isBlank(landpageUrl) || Objects.equals(orientLandpageMap.get(Pair.of(advertId, orientData.getOrientId())), landpageUrl)) {
                    dataVo.setHasPreLandpage(0);
                } else {
                    dataVo.setHasPreLandpage(1);
                }
                Account agent = agentMap.get(advertiserId);
                if (null != agent) {
                    dataVo.setAgentId(agent.getId());
                    dataVo.setAgentName(agent.getCompanyName());
                }
            }
            dataVo.setBillingClickPv(dataVo.getBillingClickPv() + orientData.getBillingClickPv());
            dataVo.setBillingClickUv(dataVo.getBillingClickUv() + orientData.getBillingClickUv());
            if (isCrmUser) {
                dataVo.setLpExposurePv(dataVo.getLpExposurePv() + orientData.getLpExposurePv());
                dataVo.setLpExposureUv(dataVo.getLpExposureUv() + orientData.getLpExposureUv());
            }
            dataVo.setLpClickPv(dataVo.getLpClickPv() + orientData.getLpClickPv());
            dataVo.setLpClickUv(dataVo.getLpClickUv() + orientData.getLpClickUv());
            dataVo.setConsumeAmount(dataVo.getConsumeAmount() + orientData.getConsume());
            dataVo.setLpClickCost(NumberUtils.calculateRate(dataVo.getConsumeAmount(), dataVo.getLpClickPv() * 100));
            dataVo.setPay(dataVo.getPay() + orientData.getPay());
            dataVo.setPayCost(NumberUtils.calculateRate(dataVo.getConsumeAmount(), dataVo.getPay() * 100));
            dataMap.put(key, dataVo);
        });

        // 兼容离线广告主数据，按比例处理
        dataMap.values().forEach(dataVo -> {
            String date = DateUtil.formatDate(dataVo.getCurDate());
            // 修正后的数据
            DspAdvertiserConsumeRecordEntity consumeRecord = consumeRecordMap.get(date + "_" + dataVo.getAdvertiserId());
            if (null != consumeRecord) {
                // 获取原先广告主数据
                CrmAdvertiserCpcDataListVO originData = originAdvertiserDataMap.get(date + "_" + dataVo.getAdvertiserId());
                // 计算比例
                if (null != originData.getBillingClickPv() && originData.getBillingClickPv() > 0) {
                    dataVo.setBillingClickPv((int) (consumeRecord.getBillingClickPv().longValue() * dataVo.getBillingClickPv() / originData.getBillingClickPv()));
                    dataVo.setLpExposurePv((int) (consumeRecord.getBillingClickPv().longValue() * dataVo.getLpExposurePv() / originData.getBillingClickPv()));
                    dataVo.setLpClickPv((int) (consumeRecord.getBillingClickPv().longValue() * dataVo.getLpClickPv() / originData.getBillingClickPv()));
                } else {
                    dataVo.setBillingClickPv(null);
                    dataVo.setLpExposurePv(null);
                    dataVo.setLpClickPv(null);
                }
                if (null != originData.getBillingClickUv() && originData.getBillingClickUv() > 0) {
                    dataVo.setBillingClickUv((int) (consumeRecord.getBillingClickUv().longValue() * dataVo.getBillingClickUv() / originData.getBillingClickUv()));
                    dataVo.setLpExposureUv((int) (consumeRecord.getBillingClickUv().longValue() * dataVo.getLpExposureUv() / originData.getBillingClickUv()));
                    dataVo.setLpClickUv((int) (consumeRecord.getBillingClickUv().longValue() * dataVo.getLpClickUv() / originData.getBillingClickUv()));
                } else {
                    dataVo.setBillingClickUv(null);
                    dataVo.setLpExposureUv(null);
                    dataVo.setLpClickUv(null);
                }
                if (null != originData.getConsumeAmount() && originData.getConsumeAmount() > 0) {
                    dataVo.setConsumeAmount((int) (consumeRecord.getConsumeAmount().longValue() * dataVo.getConsumeAmount() / originData.getConsumeAmount()));
                } else {
                    dataVo.setConsumeAmount(null);
                }
                dataVo.setLpClickCost(NumberUtils.calculateRate(dataVo.getConsumeAmount(), dataVo.getLpClickPv() * 100));
                dataVo.setPayCost(NumberUtils.calculateRate(dataVo.getConsumeAmount(), dataVo.getPay() * 100));
            }
        });

        // 排序分页
        List<CrmAdvertiserCpcDataListVO> dataList = dataMap.values().stream().sorted((o1, o2) -> {
            int c = o2.getCurDate().compareTo(o1.getCurDate());
            return c != 0 ? c : (o2.getBillingClickPv().compareTo(o1.getBillingClickPv()));
        }).collect(Collectors.toList());
        int total = dataList.size();

        if (!isExport) {
            int pageNum = ServletUtils.getParameterToInt(PAGE_NUM);
            int pageSize = ServletUtils.getParameterToInt(PAGE_SIZE);
            dataList = ListUtil.page(pageNum - 1, pageSize, dataList);

            // 构造分页数据
            Page<CrmAdvertiserCpcDataListVO> page = new Page<>(pageNum, pageSize);
            page.setTotal(total);
            PageInfo<CrmAdvertiserCpcDataListVO> result = new PageInfo<>(page);
            result.setList(dataList);
            return result;
        } else {
            return PageInfo.of(dataList);
        }
    }

    /**
     * 查询广告配置的落地页链接
     *
     * @param advertIds 广告主ID列表
     * @return 广告主ID-广告主链接映射
     */
    private Map<Pair<Long, Long>, String> getOrientLandpageUrl(List<Long> advertIds) {
        List<AdvertOrientation> list = advertOrientationService.selectListByAdvertIds(advertIds);
        Map<Pair<Long, Long>, String> map = list.stream().collect(Collectors.toMap(o -> Pair.of(o.getAdvertId(), o.getId()), AdvertOrientation::getLandpageUrl, (o, n) -> n));
        list.stream().filter(o -> IsDefaultEnum.isDefault(o.getIsDefault())).forEach(o -> map.put(Pair.of(o.getAdvertId(), o.getId()), o.getLandpageUrl()));
        return map;
    }

    /**
     * 查询广告主链接
     *
     * @param advertIds 广告ID列表
     * @return <广告ID,配置ID>-广告链接映射
     */
    private Map<Pair<Long, Long>, String> getAdvertLandpageUrl(List<Long> advertIds) {
        List<AdvertOrientLandpageBo> orientList = advertOrientationService.selectLandpageByAdvertIds(advertIds);
        if (CollectionUtils.isEmpty(orientList)) {
            return Collections.emptyMap();
        }

        Map<Pair<Long, Long>, String> map = new HashMap<>();
        orientList.forEach(orient -> {
            Pair<Long, Long> key = Pair.of(orient.getAdvertId(), orient.getOrientId());
            if (!LandpageUtil.hasLpk(orient.getLandpageUrl())) {
                if (LandpageUtil.isQuickApp(orient.getLandpageUrl())) {
                    map.put(key, StrUtil.subAfter(orient.getLandpageUrl(), "lp=", true));
                } else {
                    map.put(key, orient.getLandpageUrl());
                }
                return;
            }
            String lpk = LandpageUtil.extractLpk(orient.getLandpageUrl());
            String targetLandpage = landpageCacheService.selectTargetLandpageCache(lpk);
            if (!StrUtil.containsIgnoreCase(targetLandpage, "/land/landResult")) {
                map.put(key, targetLandpage);
            }
        });
        return map;
    }

    /**
     * 批量查询广告主的离线转实时时间
     */
    private Map<Long, Date> getOtoDateMap(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }
        Map<Long, Date> dateMap = new HashMap<>(advertiserIds.size());
        List<Account> list = accountService.selectListByIds(advertiserIds);
        for (Account account : list) {
            Optional.ofNullable(JSON.parseObject(account.getExtInfo(), AccountExtInfo.class)).ifPresent(extInfo -> {
                if (null != extInfo.getOtoDate()) {
                    dateMap.put(account.getId(), DateUtil.parseDate(extInfo.getOtoDate()));
                }
            });
        }
        return dateMap;
    }
}
