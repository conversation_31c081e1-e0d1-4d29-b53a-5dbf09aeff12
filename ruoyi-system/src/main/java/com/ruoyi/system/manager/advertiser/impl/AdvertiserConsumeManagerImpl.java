package com.ruoyi.system.manager.advertiser.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.InnerLogUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeBo;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserBalanceEntity;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeDetailRecordEntity;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserFianceStatisticsRecordEntity;
import com.ruoyi.system.manager.advertiser.AdvertiserConsumeManager;
import com.ruoyi.system.req.advertiser.finance.AdvertiserBalanceUpdateReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordUpdateReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeCategoryRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordDetailService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserFianceStatisticsRecordService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static cn.hutool.core.util.BooleanUtil.isTrue;
import static com.ruoyi.common.enums.InnerLogType.ADVERTISER_CONSUME;
import static com.ruoyi.common.enums.advert.AdvertCostBillingTypeEnum.CPC;
import static com.ruoyi.common.enums.advert.AdvertCostBillingTypeEnum.LANDPAGE_CONVERT;
import static com.ruoyi.common.enums.advert.AdvertStatusEnum.INVALID_WITHOUT_BALANCE;

/**
 * 广告主消费Manager实现
 *
 * <AUTHOR>
 * @date 2022/3/22
 */
@Slf4j
@Service
public class AdvertiserConsumeManagerImpl implements AdvertiserConsumeManager {

    @Autowired
    private AdvertiserConsumeRecordService advertiserConsumeRecordService;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private AdvertiserFianceStatisticsRecordService advertiserFianceStatisticsRecordService;

    @Autowired
    private AdvertiserConsumeRecordDetailService advertiserConsumeRecordDetailService;

    @Autowired
    private AdvertiserConsumeCategoryRecordService advertiserConsumeCategoryRecordService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    public TransactionTemplate transactionTemplate;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private RedisCache redisCache;

    @Override
    public void consume(AdvertiserConsumeBo consumeBo) {
        if (null == consumeBo || null == consumeBo.getAccountId() || NumberUtils.isNullOrLteZero(consumeBo.getConsumeAmount())) {
            throw new CustomException("参数异常");
        }

        // 打印日志
        JSONObject logJson = new JSONObject();
        logJson.put("accountId", consumeBo.getAccountId());
        logJson.put("consumeAmount", consumeBo.getConsumeAmount());
        logJson.put("consumeType", consumeBo.getConsumeType());
        logJson.put("recordId", consumeBo.getRecordId());
        logJson.put("remark", consumeBo.getRemark());
        logJson.put("billingType", LANDPAGE_CONVERT.getType());
        InnerLogUtils.log(ADVERTISER_CONSUME, logJson);

        // 新增消费明细
        AdvertiserConsumeDetailRecordEntity detailRecord = BeanUtil.copyProperties(consumeBo, AdvertiserConsumeDetailRecordEntity.class);
        int detailResult = advertiserConsumeRecordDetailService.insert(detailRecord);
        if (detailResult == 0 || null == detailRecord.getId()) {
            log.error("广告主消费失败, 新增消费明细失败, param={}", JSON.toJSONString(consumeBo));
            throw new CustomException("消费失败");
        }

        // 查询账户余额
        AdvertiserBalanceEntity balance = advertiserBalanceService.selectOrCreate(consumeBo.getAccountId());
        if (null == balance) {
            log.error("广告主消费失败,未查到账户余额, param={}", JSON.toJSONString(consumeBo));
            throw new CustomException("消费失败");
        }
        // 当广告主余额不足提醒
        remindAdvertiserAmount(balance.getAccountId(), balance.getTotalAmount());

//        不限制余额为负
//        if (balance.getTotalAmount() < consumeBo.getConsumeAmount()) {
//            log.error("广告主消费失败,账户余额不足, param={}", JSON.toJSONString(consumeBo));
//            throw new CustomException("消费失败");
//        }

        // 查询财务汇总记录
        Date curDate = DateUtil.beginOfDay(new Date());
        AdvertiserFianceStatisticsRecordEntity statisticsRecord = advertiserFianceStatisticsRecordService.select(consumeBo.getAccountId(), curDate);
        if (null == statisticsRecord) {
            try {
                int result = advertiserFianceStatisticsRecordService.insert(consumeBo.getAccountId(), curDate, balance.getCashAmount(), balance.getRebateAmount());
                if (result < 1) {
                    log.error("广告主消费失败,新增广告主财务汇总记录失败, param={}", JSON.toJSONString(consumeBo));
                    throw new CustomException("消费失败");
                }
            } catch (DuplicateKeyException e) {
                log.error("AdvertiserFianceStatisticsRecordEntity 插入冲突");
            }
            statisticsRecord = advertiserFianceStatisticsRecordService.select(consumeBo.getAccountId(), curDate);
        }
        if (null == statisticsRecord) {
            log.error("广告主消费失败,新增广告主财务汇总记录失败, param={}", JSON.toJSONString(consumeBo));
            throw new CustomException("消费失败");
        }

        boolean success = consume(consumeBo.getAccountId(), consumeBo.getConsumeAmount(), balance, statisticsRecord.getId(), detailRecord.getId(), 0, curDate, LANDPAGE_CONVERT.getType());
        if (!success) {
            throw new CustomException("消费失败");
        }
    }

    @Override
    public void consumeRetry(AdvertiserConsumeDetailRecordEntity detailRecord) {
        Long accountId = detailRecord.getAccountId();

        // 查询账户余额
        AdvertiserBalanceEntity balance = advertiserBalanceService.selectOrCreate(accountId);
        if (null == balance) {
            log.error("广告主消费重试失败,未查到账户余额, param={}", JSON.toJSONString(detailRecord));
            throw new CustomException("消费重试失败");
        }

        // 查询财务汇总记录
        Date curDate = DateUtil.beginOfDay(detailRecord.getGmtCreate());
        AdvertiserFianceStatisticsRecordEntity statisticsRecord = advertiserFianceStatisticsRecordService.select(accountId, curDate);
        if (null == statisticsRecord) {
            try {
                int result = advertiserFianceStatisticsRecordService.insert(accountId, curDate, balance.getCashAmount(), balance.getRebateAmount());
                if (result < 1) {
                    log.error("广告主消费重试失败,新增广告主财务汇总记录失败, param={}", JSON.toJSONString(detailRecord));
                    throw new CustomException("消费失败");
                }
            } catch (DuplicateKeyException e) {
                log.error("AdvertiserFianceStatisticsRecordEntity 插入冲突");
            }
            statisticsRecord = advertiserFianceStatisticsRecordService.select(accountId, curDate);
        }
        if (null == statisticsRecord) {
            log.error("广告主消费重试失败,新增广告主财务汇总记录失败, param={}", JSON.toJSONString(detailRecord));
            throw new CustomException("消费失败");
        }

        boolean success = consume(accountId, detailRecord.getConsumeAmount(), balance, statisticsRecord.getId(), detailRecord.getId(), 1, curDate, LANDPAGE_CONVERT.getType());
        if (!success) {
            throw new CustomException("消费失败");
        }
    }

    @Override
    public boolean cpcConsume(Date curDate, Long accountId, Long advertId, Integer consumeAmount) {
        if (null == accountId || NumberUtils.isNullOrLteZero(consumeAmount)) {
            return false;
//            throw new CustomException("参数异常");
        }

        // 查询账户余额
        AdvertiserBalanceEntity balance = advertiserBalanceService.selectOrCreate(accountId);
        if (null == balance) {
            log.error("广告主消费失败,未查到账户余额, accountId={}", accountId);
            return false;
//            throw new CustomException("消费失败");
        }
        Integer totalAmount = balance.getTotalAmount();
        // 扣减运营修改的差额
        totalAmount -= dspAdvertiserConsumeRecordService.consumeOffsetForCrm(accountId);
        // 接近无余额的情况下停止发券
        if (totalAmount <= consumeAmount * 2) {
            stopAdvertServing(accountId, advertId, totalAmount);
        }
        // 当广告主余额不足提醒
        remindAdvertiserAmount(accountId, totalAmount);
        // 日志记录
        log.info("cpcConsume, accountId={}, advertId={}, balance={}, totalAmount={}, consumeAmount={}",
                accountId, advertId, balance.getTotalAmount(), totalAmount, consumeAmount);

        // 查询财务汇总记录
        Long statisticsRecordId = getAdvertiserFianceStatisticsRecordId(curDate, accountId, balance);
        if (null == statisticsRecordId) {
            log.error("广告主消费失败,新增广告主财务汇总记录失败, accountId={}, consumeAmount={}", accountId, consumeAmount);
            return false;
//            throw new CustomException("消费失败");
        }

        return consume(accountId, consumeAmount, balance, statisticsRecordId, 0L, 0, curDate, CPC.getType());
    }

    /**
     * 消费核心逻辑
     */
    private boolean consume(Long accountId, Integer consumeAmount, AdvertiserBalanceEntity balance,
                         Long statisticsRecordId, Long detailRecordId, Integer retryTimes, Date curDate,
                         Integer billingType) {
        Boolean success = transactionTemplate.execute(status -> {
            try {
                // 更新账户余额
                AdvertiserBalanceUpdateReq updateBalanceReq = new AdvertiserBalanceUpdateReq();
                updateBalanceReq.setId(balance.getId());

                // 先扣返货余额，再扣现金余额
                if (balance.getRebateAmount() >= consumeAmount) {
                    updateBalanceReq.setRebateAmountAdd(-consumeAmount);
                } else if (balance.getRebateAmount() > 0) {
                    updateBalanceReq.setRebateAmountAdd(-balance.getRebateAmount());
                    updateBalanceReq.setCashAmountAdd(-(consumeAmount - balance.getRebateAmount()));
                } else {
                    updateBalanceReq.setCashAmountAdd(-consumeAmount);
                }
                int result = advertiserBalanceService.updateBalance(updateBalanceReq);
                if (result == 0) {
                    log.error("广告主消费失败,更新账户余额失败, accountId={}, consumeAmount={}, detailRecordId={}", accountId, consumeAmount, detailRecordId);
                    status.setRollbackOnly();
                    return false;
                }

                // 新增消费记录
                result = advertiserConsumeRecordService.update(accountId, curDate, consumeAmount);
                if (result == 0) {
                    log.error("广告主消费失败,更新消费记录失败, accountId={}, consumeAmount={}, detailRecordId={}", accountId, consumeAmount, detailRecordId);
                    status.setRollbackOnly();
                    return false;
                }

                // 更新广告主财务汇记录
                AdvertiserFianceStatisticsRecordUpdateReq updateFinanceReq = new AdvertiserFianceStatisticsRecordUpdateReq();
                updateFinanceReq.setId(statisticsRecordId);
                updateFinanceReq.setConsumeAmountAdd(consumeAmount);
                updateFinanceReq.setCashBalanceAdd(updateBalanceReq.getCashAmountAdd());
                updateFinanceReq.setRebateBalanceAdd(updateBalanceReq.getRebateAmountAdd());
                result = advertiserFianceStatisticsRecordService.update(updateFinanceReq);
                if (result == 0) {
                    log.error("广告主消费失败,更新广告主财务汇记录失败, accountId={}, consumeAmount={}, detailRecordId={}", accountId, consumeAmount, detailRecordId);
                    status.setRollbackOnly();
                    return false;
                }

                // 落地页转化,更新消费记录明细
                if (Objects.equals(billingType, LANDPAGE_CONVERT.getType())) {
                    AdvertiserConsumeDetailRecordEntity updateDetail = new AdvertiserConsumeDetailRecordEntity();
                    updateDetail.setId(detailRecordId);
                    updateDetail.setIsDone(1);
                    updateDetail.setDoneTime(new Date());
                    updateDetail.setRetryTimes(retryTimes);
                    result = advertiserConsumeRecordDetailService.update(updateDetail);
                    if (result == 0) {
                        log.error("广告主消费失败,更新消费记录明细失败, accountId={}, consumeAmount={}, detailRecordId={}", accountId, consumeAmount, detailRecordId);
                        status.setRollbackOnly();
                        return false;
                    }
                }

                // 更新结算分类消费记录
                result = advertiserConsumeCategoryRecordService.update(accountId, curDate, billingType, consumeAmount);
                if (result == 0) {
                    log.error("广告主消费失败,更新结算消费记录失败, accountId={}, billingType={}, consumeAmount={}, detailRecordId={}", accountId, billingType, consumeAmount, detailRecordId);
                    status.setRollbackOnly();
                    return false;
                }

                return true;
            } catch (Exception e) {
                log.error("广告主消费异常, accountId={}, consumeAmount={}, detailRecordId={}", accountId, consumeAmount, detailRecordId, e);
                status.setRollbackOnly();
                return false;
            }
        });
        return isTrue(success);
    }

    /**
     * 无预算，关闭广告投放
     */
    private void stopAdvertServing(Long accountId, Long advertId, Integer totalAmount) {
        RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K042.join(accountId, advertId), 60);
        if (null == lock) {
            return;
        }
        advertService.updateAdvertStatus(advertId, INVALID_WITHOUT_BALANCE.getStatus());
        log.info("广告主无余额，关闭广告投放, advertiserId={}, advertId={}, balance={}", accountId, advertId, totalAmount);

        GlobalThreadPool.executorService.submit(() -> {
            String sb = "广告主无余额，停止投放\n" +
                    "\n广告主ID: " + accountId +
                    "\n广告主名称: " + advertiserService.selectAdvertiserName(accountId) +
                    "\n广告ID: " + advertId +
                    "\n广告名称: " + advertService.selectAdvertNameById(advertId);
            DingRobotUtil.sendText(DingWebhookConfig.getBalanceAlert(), sb);
        });
    }

    /**
     * 广告主余额不足提醒
     *
     * @param accountId 广告主ID
     * @param totalAmount 余额
     */
    private void remindAdvertiserAmount(Long accountId, Integer totalAmount) {
        // 提醒阈值:5000元
        if (totalAmount > 500000) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            // 一天提醒一次(小于等于-10元/2000元/5000元)
            Integer threshold = totalAmount <= -1000 ? -10 : (totalAmount <= 200000 ? 2000 : 5000);
            RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K045.join(accountId, DateUtil.formatDate(new Date()), threshold), 86400);
            if (null == lock) {
                return;
            }
            String sb ="广告主ID: " + accountId +
                    "\n广告主名称: " + advertiserService.selectAdvertiserName(accountId) +
                    "\n当前余额: " + NumberUtils.fenToYuan(totalAmount);
            DingRobotUtil.sendText(DingWebhookConfig.getBalanceAlert(), sb);
        });
    }

    /**
     * 通过缓存获取广告主财务汇总记录ID
     */
    private Long getAdvertiserFianceStatisticsRecordId(Date date, Long accountId, AdvertiserBalanceEntity balance) {
        String key = EngineRedisKeyFactory.K022.join("AdvertiserFianceStatisticsRecordEntity", DateUtil.formatDate(date), accountId);
        Long recordId = redisCache.getCacheObject(key);
        if (null != recordId) {
            return recordId;
        }
        // 查询并初始数据
        AdvertiserFianceStatisticsRecordEntity record = advertiserFianceStatisticsRecordService.select(accountId, date);
        if (null == record) {
            try {
                int result = advertiserFianceStatisticsRecordService.insert(accountId, date, balance.getCashAmount(), balance.getRebateAmount());
                if (result < 1) {
                    log.error("广告主消费失败,新增广告主财务汇总记录失败, accountId={}", accountId);
                    return null;
                }
            } catch (DuplicateKeyException e) {
                log.error("AdvertiserFianceStatisticsRecordEntity 插入冲突");
            }
            record = advertiserFianceStatisticsRecordService.select(accountId, date);
        }
        redisCache.setCacheObject(key, record.getId(), 1, TimeUnit.DAYS);
        return record.getId();
    }
}
