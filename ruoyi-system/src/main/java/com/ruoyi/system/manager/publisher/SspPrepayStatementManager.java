package com.ruoyi.system.manager.publisher;

import com.github.pagehelper.PageInfo;
import com.ruoyi.system.vo.ssp.finance.PrepayStatementInfoVO;
import com.ruoyi.system.vo.ssp.finance.PrepayStatementVO;

/**
 * 媒体预付款结算单Manager接口
 *
 * <AUTHOR>
 * @date 2022/08/03
 */
public interface SspPrepayStatementManager {

    /**
     * 查询媒体预付款结算单明细
     *
     * @param accountId 媒体账号ID
     * @return 预付款结算单明细
     */
    PageInfo<PrepayStatementVO> selectPrepayStatementList(Long accountId);

    /**
     * 查询媒体预付款结算单明细
     *
     * @param accountId 媒体账号ID
     * @param statementId 结算单号
     * @return 预付款结算单详情
     */
    PrepayStatementInfoVO getPrepayStatementInfo(Long accountId, Long statementId);
}
