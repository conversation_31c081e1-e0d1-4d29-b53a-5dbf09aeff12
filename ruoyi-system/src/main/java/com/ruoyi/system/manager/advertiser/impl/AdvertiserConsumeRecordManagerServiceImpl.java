package com.ruoyi.system.manager.advertiser.impl;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordHistoryEntity;
import com.ruoyi.system.manager.advertiser.AdvertiserConsumeRecordManagerService;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeUpdateReq;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordHistoryService;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 广告主消费记录Manager实现
 *
 * <AUTHOR>
 * @date 2022/07/15
 */
@Slf4j
@Service
public class AdvertiserConsumeRecordManagerServiceImpl implements AdvertiserConsumeRecordManagerService {

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Autowired
    private DspAdvertiserConsumeRecordHistoryService dspAdvertiserConsumeRecordHistoryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateConsumeRecord(AdvertiserConsumeUpdateReq req) {
        LoginUser user = SecurityUtils.getLoginUser();

        DspAdvertiserConsumeRecordHistoryEntity history = new DspAdvertiserConsumeRecordHistoryEntity();
        history.setAdvertiserId(req.getAdvertiserId());
        history.setCurDate(req.getCurDate());
        history.setSource(1);
        history.setOperatorId(user.getCrmAccountId());

        DspAdvertiserConsumeRecordEntity record = dspAdvertiserConsumeRecordService.selectByAdvertiserIdAndDate(req.getAdvertiserId(), req.getCurDate());
        if (null != record) {
            history.setBeforeEdit(JSON.toJSONString(record));
            record.setBillingClickPv(req.getBillingClickPv());
            record.setBillingClickUv(req.getBillingClickUv());
            record.setConsumeAmount(req.getConsumeAmount());
            record.setIsVisible(1);
            record.setOperatorId(user.getCrmAccountId());
            dspAdvertiserConsumeRecordService.updateById(record);
        } else {
            history.setBeforeEdit("");
            record = new DspAdvertiserConsumeRecordEntity();
            record.setCurDate(req.getCurDate());
            record.setAdvertiserId(req.getAdvertiserId());
            record.setBillingClickPv(req.getBillingClickPv());
            record.setBillingClickUv(req.getBillingClickUv());
            record.setConsumeAmount(req.getConsumeAmount());
            record.setIsVisible(1);
            record.setOperatorId(user.getCrmAccountId());
            dspAdvertiserConsumeRecordService.insert(record);
        }
        history.setAfterEdit(JSON.toJSONString(record));
        dspAdvertiserConsumeRecordHistoryService.insert(history);
    }
}
