package com.ruoyi.system.manager.common.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.common.MapConfigEnum;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserDaySumDataBo;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.manager.common.CheckManager;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.datasource.AdvertDayDataService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.util.DingRobotUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 检查manager接口实现
 *
 * <AUTHOR>
 * @date 2022/08/15
 */
@Service
public class CheckManagerImpl implements CheckManager {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertDayDataService advertDayDataService;

    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertiserService advertiserService;

    @Override
    public void checkAdvertiserConvLimit() {
        Date today = DateUtil.beginOfDay(new Date());
        String dateStr = DateUtil.today();
        Map<Long, Integer> advertiserConvMap = mapConfigService.getMap(MapConfigEnum.ADVERTISER_CONV_LIMIT_MAP, Long.class, Integer.class);
        List<Long> advertiserIds = advertiserConvMap.entrySet().stream().filter(e-> NumberUtils.isNonNullAndGtZero(e.getValue())).map(Map.Entry::getKey).distinct().collect(Collectors.toList());
        Map<String, AdvertiserDaySumDataBo> advertiserDayDataMap = advertDayDataService.groupByDateAndAdvertiserId(today, today, advertiserIds);
        for (Long advertiserId : advertiserIds) {
            Integer convLimit = advertiserConvMap.get(advertiserId);
            Optional.ofNullable(advertiserDayDataMap.get(dateStr + "_" + advertiserId)).ifPresent(data -> {
                if (null != data.getLpClickPv() && data.getLpClickPv() >= convLimit) {
                    RedisLock lock = redisAtomicClient.getLock(CrmRedisKeyFactory.K025.join(dateStr, advertiserId), 7200);
                    if (null != lock) {
                        Advert param = new Advert();
                        param.setServingSwitch(1);
                        param.setAdvertStatus(0);
                        param.setAdvertiserId(advertiserId);
                        List<Advert> adverts = advertService.selectAdvertList(param);
                        if (CollectionUtils.isNotEmpty(adverts)) {
                            // 发送钉钉通知
                            StringBuilder sb = new StringBuilder();
                            sb.append("广告投放暂停\n\n")
                                    .append("广告主ID：").append(advertiserId).append("\n")
                                    .append("广告主名称：").append(advertiserService.selectAdvertiserName(advertiserId)).append("\n")
                                    .append("转化数：").append(data.getLpClickPv()).append("\n")
                                    .append("转化达到上限，暂停以下广告投放，如需继续，请手动开启\n")
                                    .append(adverts.stream().map(advert -> advert.getId() + "-" + advert.getAdvertName()).collect(Collectors.joining("\n")));
                            DingRobotUtil.sendText(DingWebhookConfig.getBizAlert(), sb.toString());

                            // 关闭广告
                            adverts.forEach(advert -> {
                                Advert updateAdvert = new Advert();
                                updateAdvert.setId(advert.getId());
                                updateAdvert.setServingSwitch(SwitchStatusEnum.OFF.getStatus());
                                advertService.updateAdvertStatus(updateAdvert);
                            });
                        }
                    }
                }
            });
        }
    }
}
