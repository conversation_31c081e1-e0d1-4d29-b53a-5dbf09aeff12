package com.ruoyi.system.manager.advertiser.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserBalanceEntity;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserFianceStatisticsRecordEntity;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserRechargeRecordEntity;
import com.ruoyi.system.manager.advertiser.AdvertiserRechargeManager;
import com.ruoyi.system.req.advertiser.finance.AdvertiserBalanceUpdateReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordUpdateReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeApplyReq;
import com.ruoyi.system.req.agent.finance.WisAgentRechargeReq;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserFianceStatisticsRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserRechargeRecordService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.AgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum.ONLINE;
import static com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum.REBATE;
import static com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum.TRANSFER_ONLINE;
import static com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum.TRANSFER_REBATE;
import static com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum.isRecharge;
import static com.ruoyi.common.enums.advertiser.AdvertiserRechargeTypeEnum.isTransfer;
import static com.ruoyi.common.enums.advertiser.RechargeAuditStatus.APPROVE;
import static com.ruoyi.common.enums.advertiser.RechargeAuditStatus.READY;
import static com.ruoyi.common.enums.advertiser.RechargeAuditStatus.REFUSE;
import static com.ruoyi.common.enums.advertiser.RechargeAuditStatus.isReadyToAudit;

/**
 * 广告主充值Manager实现
 *
 * <AUTHOR>
 * @date 2022/3/21 2:07 下午
 */
@Slf4j
@Service
public class AdvertiserRechargeManagerImpl implements AdvertiserRechargeManager {

    @Autowired
    private AdvertiserRechargeRecordService advertiserRechargeRecordService;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private AdvertiserFianceStatisticsRecordService advertiserFianceStatisticsRecordService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AccountService accountService;

    @Override
    public int apply(AdvertiserRechargeApplyReq req) {
        if (null == req || null == req.getAccountId() || !isRecharge(req.getRechargeType())) {
            throw new CustomException("参数异常");
        }
        Account account = accountService.selectAccountById(req.getAccountId());
        if (null == account || (!isAdvertiser(account.getMainType()) && !isAgent(account.getMainType()))) {
            throw new CustomException("请选择有效的广告主/代理商");
        }

        if (Objects.equals(ONLINE.getType(), req.getRechargeType()) && NumberUtils.isNullOrLteZero(req.getRechargeAmount())) {
            throw new CustomException(ONLINE.getDesc() + "的充值金额必须为正数");
        } else if (Objects.equals(req.getRechargeAmount(), 0)) {
            throw new CustomException("充值金额不能为0");
        }
        // 代理商充值校验，充值后余额不能为负
        // 有个漏洞:申请多笔，每笔都是正常金额，但是审核滞后，所以会导致审核之后余额为负
        if (isAgent(account.getMainType())) {
            AdvertiserBalanceEntity balance = advertiserBalanceService.selectByAccountId(req.getAccountId());
            if (null == balance && req.getRechargeAmount() < 0
                    || null != balance && balance.getTotalAmount() + req.getRechargeAmount() < 0) {
                throw new CustomException("该账户余额不足，请重新输入金额");
            }
        }

        // 新增充值记录
        AdvertiserRechargeRecordEntity record = new AdvertiserRechargeRecordEntity();
        record.setAccountId(req.getAccountId());
        record.setRechargeType(req.getRechargeType());
        record.setRechargeAmount(req.getRechargeAmount());
        record.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
        record.setRemark(req.getRemark());
        record.setAuditStatus(READY.getStatus());
        return advertiserRechargeRecordService.insert(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int auditApprove(Long rechargeId, String auditReason) {
        int result;
        AdvertiserRechargeRecordEntity record = advertiserRechargeRecordService.selectById(rechargeId);
        if (null == record) {
            throw new CustomException("未查询到充值申请记录");
        }
        if (!isReadyToAudit(record.getAuditStatus())) {
            throw new CustomException("充值申请已经被审核");
        }

        // 查询账户余额
        Long accountId = record.getAccountId();
        AdvertiserBalanceEntity balance = advertiserBalanceService.selectOrCreate(accountId);
        if (null == balance) {
            log.error("广告主充值失败, 未查到账户余额, accountId={}", accountId);
            throw new CustomException("审核失败");
        }
        // 查询财务汇总记录
        Date curDate = DateUtil.beginOfDay(new Date());
        AdvertiserFianceStatisticsRecordEntity statisticsRecord = advertiserFianceStatisticsRecordService.select(accountId, curDate);
        if (null == statisticsRecord) {
            try {
                result = advertiserFianceStatisticsRecordService.insert(accountId, curDate, balance.getCashAmount(), balance.getRebateAmount());
                if (result < 1) {
                    log.error("广告主充值失败, 新增广告主财务汇总记录失败, accountId={}, rechargeId={}", accountId, rechargeId);
                    throw new CustomException("审核失败");
                }
            } catch (DuplicateKeyException e) {
                log.error("AdvertiserFianceStatisticsRecordEntity 插入冲突");
            }
            statisticsRecord = advertiserFianceStatisticsRecordService.select(accountId, curDate);
        }
        if (null == statisticsRecord) {
            log.error("广告主充值失败, 新增广告主财务汇总记录失败, accountId={}, rechargeId={}", accountId, rechargeId);
            throw new CustomException("审核失败");
        }

        // 更新账户余额
        AdvertiserBalanceUpdateReq updateBalanceReq = new AdvertiserBalanceUpdateReq();
        updateBalanceReq.setId(balance.getId());
        if (Objects.equals(record.getRechargeType(), ONLINE.getType())) {
            updateBalanceReq.setCashAmountAdd(record.getRechargeAmount());
        } else if (Objects.equals(record.getRechargeType(), REBATE.getType())) {
            updateBalanceReq.setRebateAmountAdd(record.getRechargeAmount());
        }
        result = advertiserBalanceService.updateBalance(updateBalanceReq);
        if (result == 0) {
            log.error("广告主充值失败, 更新账户余额失败, accountId={}, rechargeId={}", accountId, rechargeId);
            throw new CustomException("审核失败");
        }

        // 更新充值记录
        record.setAuditStatus(APPROVE.getStatus());
        record.setAuditorId(SecurityUtils.getLoginUser().getCrmAccountId());
        record.setAuditTime(new Date());
        record.setAuditReason(auditReason);
        result = advertiserRechargeRecordService.updateById(record);
        if (result == 0) {
            log.error("广告主充值失败, 更新充值记录失败, accountId={}, rechargeId={}", accountId, rechargeId);
            throw new CustomException("审核失败");
        }

        // 更新广告主财务汇记录
        AdvertiserFianceStatisticsRecordUpdateReq updateFinanceReq = new AdvertiserFianceStatisticsRecordUpdateReq();
        updateFinanceReq.setId(statisticsRecord.getId());
        updateFinanceReq.setRechargeAmountAdd(record.getRechargeAmount());
        updateFinanceReq.setCashBalanceAdd(updateBalanceReq.getCashAmountAdd());
        updateFinanceReq.setRebateBalanceAdd(updateBalanceReq.getRebateAmountAdd());
        result = advertiserFianceStatisticsRecordService.update(updateFinanceReq);
        if (result == 0) {
            log.error("广告主充值失败, 更新广告主财务汇记录, accountId={}, rechargeId={}", accountId, rechargeId);
            throw new CustomException("审核失败");
        }
        return result;
    }

    @Override
    public int auditRefuse(Long rechargeId, String auditReason) {
        AdvertiserRechargeRecordEntity record = advertiserRechargeRecordService.selectById(rechargeId);
        if (null == record) {
            throw new CustomException("未查询到充值申请记录");
        }
        if (!isReadyToAudit(record.getAuditStatus())) {
            throw new CustomException("充值申请已经被审核");
        }
        record.setAuditStatus(REFUSE.getStatus());
        record.setAuditorId(SecurityUtils.getLoginUser().getCrmAccountId());
        record.setAuditReason(auditReason);
        record.setAuditTime(new Date());
        return advertiserRechargeRecordService.updateById(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transfer(WisAgentRechargeReq req) {
        if (null == req || null == req.getAgentId() || null == req.getAdvertiserId() || !isTransfer(req.getRechargeType())) {
            throw new CustomException("参数异常");
        }
        if (Objects.equals(req.getRechargeAmount(), 0)) {
            throw new CustomException("充值金额不能为0");
        }
        Date curDate = DateUtil.beginOfDay(new Date());
        Long agentId = req.getAgentId();
        Long advertiserId = req.getAdvertiserId();
        Integer rechargeAmount = req.getRechargeAmount();

        // 校验代理商与广告主关系
        if (!Objects.equals(agentService.selectAgentIdByAdvertiserId(advertiserId), agentId)) {
            throw new CustomException("代理商未代理该广告主");
        }

        // 查询代理商余额
        AdvertiserBalanceEntity agentBalance = advertiserBalanceService.selectOrCreate(agentId);
        if (null == agentBalance) {
            log.error("代理商划账失败,未查到代理商账户余额, req={}", JSON.toJSONString(req));
            throw new CustomException("代理商无余额，充值失败");
        }
        if (rechargeAmount > 0 && agentBalance.getTotalAmount() < rechargeAmount) {
            throw new CustomException("代理商余额不足，充值失败");
        }

        // 查询广告主账户余额
        AdvertiserBalanceEntity advertiserBalance = advertiserBalanceService.selectOrCreate(advertiserId);
        if (null == advertiserBalance) {
            log.error("代理商划账失败,未查到广告主账户余额, req={}", JSON.toJSONString(req));
            throw new CustomException("充值失败");
        }
        if (rechargeAmount < 0 && advertiserBalance.getTotalAmount() < -rechargeAmount) {
            throw new CustomException("广告主余额不足，充值失败");
        }

        // 查询代理商财务汇总记录
        AdvertiserFianceStatisticsRecordEntity agentStatisticsRecord = advertiserFianceStatisticsRecordService.select(agentId, curDate);
        if (null == agentStatisticsRecord) {
            try {
                int result = advertiserFianceStatisticsRecordService.insert(agentId, curDate, agentBalance.getCashAmount(), agentBalance.getRebateAmount());
                if (result < 1) {
                    log.error("代理商划账失败,新增代理商财务汇总记录失败, req={}", JSON.toJSONString(req));
                    throw new CustomException("充值失败");
                }
            } catch (DuplicateKeyException e) {
                log.error("AdvertiserFianceStatisticsRecordEntity 插入冲突");
            }
            agentStatisticsRecord = advertiserFianceStatisticsRecordService.select(agentId, curDate);
        }
        if (null == agentStatisticsRecord) {
            log.error("代理商划账失败,新增代理商财务汇总记录失败, req={}", JSON.toJSONString(req));
            throw new CustomException("充值失败");
        }
        // 查询广告主财务汇总记录
        AdvertiserFianceStatisticsRecordEntity advertiserStatisticsRecord = advertiserFianceStatisticsRecordService.select(advertiserId, curDate);
        if (null == advertiserStatisticsRecord) {
            try {
                int result = advertiserFianceStatisticsRecordService.insert(advertiserId, curDate, advertiserBalance.getCashAmount(), advertiserBalance.getRebateAmount());
                if (result < 1) {
                    log.error("代理商划账失败,新增广告主财务汇总记录失败, req={}", JSON.toJSONString(req));
                    throw new CustomException("充值失败");
                }
            } catch (DuplicateKeyException e) {
                log.error("AdvertiserFianceStatisticsRecordEntity 插入冲突");
            }
            advertiserStatisticsRecord = advertiserFianceStatisticsRecordService.select(advertiserId, curDate);
        }
        if (null == advertiserStatisticsRecord) {
            log.error("代理商划账失败,新增广告主财务汇总记录失败, req={}", JSON.toJSONString(req));
            throw new CustomException("充值失败");
        }

        // 更新代理商账户余额
        AdvertiserBalanceUpdateReq updateAgentBalanceReq = new AdvertiserBalanceUpdateReq();
        updateAgentBalanceReq.setId(agentBalance.getId());
        if (rechargeAmount > 0) {
            // 资金流向:代理商(优先返货扣)->广告主
            if (rechargeAmount <= agentBalance.getRebateAmount()) {
                updateAgentBalanceReq.setRebateAmountAdd(-rechargeAmount);
            } else {
                updateAgentBalanceReq.setRebateAmountAdd(-agentBalance.getRebateAmount());
                updateAgentBalanceReq.setCashAmountAdd(-(rechargeAmount - agentBalance.getRebateAmount()));
            }
        } else {
            // 资金流向:广告主->代理商
            if (Objects.equals(req.getRechargeType(), TRANSFER_ONLINE.getType())) {
                updateAgentBalanceReq.setCashAmountAdd(-rechargeAmount);
            } else if (Objects.equals(req.getRechargeType(), TRANSFER_REBATE.getType())) {
                updateAgentBalanceReq.setRebateAmountAdd(-rechargeAmount);
            }
        }
        if (advertiserBalanceService.updateBalance(updateAgentBalanceReq) < 1) {
            log.error("代理商划账失败,更新代理商账户余额失败, param={}", JSON.toJSONString(req));
            throw new CustomException("充值失败");
        }
        // 更新广告主账户余额
        AdvertiserBalanceUpdateReq updateAdvertiserBalanceReq = new AdvertiserBalanceUpdateReq();
        updateAdvertiserBalanceReq.setId(advertiserBalance.getId());
        if (rechargeAmount > 0) {
            // 资金流向:代理商->广告主
            if (Objects.equals(req.getRechargeType(), TRANSFER_ONLINE.getType())) {
                updateAdvertiserBalanceReq.setCashAmountAdd(rechargeAmount);
            } else if (Objects.equals(req.getRechargeType(), TRANSFER_REBATE.getType())) {
                updateAdvertiserBalanceReq.setRebateAmountAdd(rechargeAmount);
            }
        } else {
            // 资金流向:广告主(优先返货扣)->代理商
            if (-rechargeAmount <= advertiserBalance.getRebateAmount()) {
                updateAdvertiserBalanceReq.setRebateAmountAdd(rechargeAmount);
            } else {
                updateAdvertiserBalanceReq.setRebateAmountAdd(-advertiserBalance.getRebateAmount());
                updateAdvertiserBalanceReq.setCashAmountAdd(rechargeAmount + advertiserBalance.getRebateAmount());
            }
        }
        if (advertiserBalanceService.updateBalance(updateAdvertiserBalanceReq) < 1) {
            log.error("代理商划账失败,更新广告主账户余额失败, req={}", JSON.toJSONString(req));
            throw new CustomException("充值失败");
        }

        // 新增充值记录
        AdvertiserRechargeRecordEntity record = new AdvertiserRechargeRecordEntity();
        record.setAccountId(advertiserId);
        record.setSourceAccountId(agentId);
        record.setRechargeType(req.getRechargeType());
        record.setRechargeAmount(req.getRechargeAmount());
        record.setRemark(req.getRemark());
        record.setAuditStatus(APPROVE.getStatus());
        if (advertiserRechargeRecordService.insert(record) < 1) {
            log.error("代理商划账失败,新增充值记录失败, req={}", JSON.toJSONString(req));
            throw new CustomException("充值失败");
        }

        // 更新代理商财务汇记录
        AdvertiserFianceStatisticsRecordUpdateReq updateAgentFinanceReq = new AdvertiserFianceStatisticsRecordUpdateReq();
        updateAgentFinanceReq.setId(agentStatisticsRecord.getId());
        updateAgentFinanceReq.setRechargeAmountAdd(-rechargeAmount);
        updateAgentFinanceReq.setCashBalanceAdd(updateAgentBalanceReq.getCashAmountAdd());
        updateAgentFinanceReq.setRebateBalanceAdd(updateAgentBalanceReq.getRebateAmountAdd());
        advertiserFianceStatisticsRecordService.update(updateAgentFinanceReq);

        // 更新广告主财务汇记录
        AdvertiserFianceStatisticsRecordUpdateReq updateAdvertiserFinanceReq = new AdvertiserFianceStatisticsRecordUpdateReq();
        updateAdvertiserFinanceReq.setId(advertiserStatisticsRecord.getId());
        updateAdvertiserFinanceReq.setRechargeAmountAdd(rechargeAmount);
        updateAdvertiserFinanceReq.setCashBalanceAdd(updateAdvertiserBalanceReq.getCashAmountAdd());
        updateAdvertiserFinanceReq.setRebateBalanceAdd(updateAdvertiserBalanceReq.getRebateAmountAdd());
        advertiserFianceStatisticsRecordService.update(updateAdvertiserFinanceReq);
    }
}
