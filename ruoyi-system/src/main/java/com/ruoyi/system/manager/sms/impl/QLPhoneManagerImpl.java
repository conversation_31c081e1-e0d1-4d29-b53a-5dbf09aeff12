package com.ruoyi.system.manager.sms.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.SspRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.manager.sms.QLPhoneManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 圈量接口实现
 *
 * <AUTHOR>
 * @date 2023/5/10 11:36
 */
@Slf4j
@Service
public class QLPhoneManagerImpl implements QLPhoneManager {

    @Autowired
    private RedisCache redisCache;

    /**
     * 圈量AppKey
     */
    private static final String APP_KEY = "co24e9bf7550a94c8b";
    /**
     * 圈量AppSecret
     */
    private static final String APP_SECRET = "k1CIAC9HypZ2RXDJdJ6tIP8IlTPRVAIlH1bbSB5A0CPKdTxhx7zd";
    /**
     * 圈量规则ID
     */
    private static final String RULE_ID = "8081";

    private static final String API_HOST = "https://s1.xunjinet.com.cn";

    @Override
    public String getAccessToken() {
        String key = SspRedisKeyFactory.K006.join(APP_KEY);
        String value = redisCache.getCacheObject(key);
        if (StringUtils.isNotBlank(value)) {
            return value;
        }

        JSONObject body = new JSONObject();
        body.put("app_key", APP_KEY);
        body.put("app_secret", APP_SECRET);
        String resp = HttpUtil.post(API_HOST+"/gateway/jzopen/GetAccessToken", body.toString());
        JSONObject result = JSON.parseObject(resp);
        if (null != result && Objects.equals(result.getInteger("errcode"), 0)
                && result.containsKey("data")) {
            JSONObject data = result.getJSONObject("data");
            String accessToken = data.getString("access_token");
            if (StringUtils.isNotBlank(accessToken)) {
                redisCache.setCacheObject(key, accessToken, data.getIntValue("expires_in"), TimeUnit.SECONDS);
                return accessToken;
            }
        }
        return null;
    }

    @Override
    public void removeAccessToken() {
        String key = SspRedisKeyFactory.K006.join(APP_KEY);
        redisCache.deleteObject(key);
    }

    @Override
    public Integer addExtUserByRule(List<String> phones) {
        if(CollectionUtils.isEmpty(phones)){
            return null;
        }

        JSONObject body = new JSONObject();
        body.put("rule_id", RULE_ID);
        List<JSONObject> addList = phones.stream().map(phone -> {
            JSONObject item = new JSONObject();
            item.put("phone", phone);
            return item;
        }).collect(Collectors.toList());

        body.put("add_list", addList);

        String resp = HttpUtil.createPost(API_HOST+"/gateway/jzopen/AddContactByRule")
                .header("Token", getAccessToken())
                .body(body.toString())
                .execute().body();
        log.info("圈量API调用, AddExtUserByRule, req={}, resp={}", body, resp);
        JSONObject result = JSON.parseObject(resp);
        if (null != result) {
            Integer errcode = result.getInteger("errcode");
            if(Objects.equals(errcode,0)){
                return errcode;
            }
            log.error("调圈量加好友接口异常,errcode:{}",errcode);
            return result.getInteger("errcode");
        }
        return null;
    }
}
