package com.ruoyi.system.manager.advert.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.domain.adengine.AdvertOcpcCacheDto;
import com.ruoyi.system.entity.datashow.AdvertQuarterDataEntity;
import com.ruoyi.system.manager.advert.OcpcManager;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.datasource.AdvertQuarterDataService;
import com.ruoyi.system.service.engine.cache.AdvertCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.ruoyi.common.enums.InnerLogType.ADVERT_BILLING;
import static com.ruoyi.common.enums.InnerLogType.CONVERT_EVENT;
import static com.ruoyi.common.enums.InnerLogType.LANDPAGE_CLICK;
import static com.ruoyi.common.enums.advert.ConvType.PAY;
import static com.ruoyi.common.enums.advert.OcpcConvTypeEnum.isConversion;
import static com.ruoyi.common.enums.advert.OcpcConvTypeEnum.isPay;
import static com.ruoyi.common.enums.common.MapConfigEnum.ADVERT_OCPC_CONV_MAP;

/**
 * OCPC Manager接口实现
 *
 * <AUTHOR>
 * @date 2022/10/24
 */
@Service
public class OcpcManagerImpl implements OcpcManager {

    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private AdvertQuarterDataService advertQuarterDataService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertCacheService advertCacheService;

    @Override
    public Integer calculateOcpcPrice(Long advertId, Integer ocpcConvType, Integer ocpcConvCost) {
        Date today = DateUtil.beginOfDay(new Date());

        // 查询广告数据
        AdvertQuarterDataEntity todayData = advertQuarterDataService.selectByAdvertAndDate(advertId, today);
        if (null == todayData) {
            return null;
        }

        // OCPC开启条件:转化达到阈值，默认10
        int convThreshold = Math.max(NumberUtils.defaultInt(mapConfigService.getValue(ADVERT_OCPC_CONV_MAP, advertId, Integer.class), 10), 1);
        int convCount = getConvCount(todayData, ocpcConvType);
        if (convCount < convThreshold) {
            return null;
        }

        // 计算预估出价
        return convCount * NumberUtils.defaultInt(ocpcConvCost) / todayData.getBillingClickPv();
    }

    @Override
    public Integer calculateOcpcPrice(Long orientId, Double pCtr, Double pCvr) {
        if (null == orientId || pCtr < 0.0001) {
            return null;
        }

        // OCPC配置
        AdvertOcpcCacheDto ocpcConfig = advertCacheService.queryAdvertOcpcCache(orientId);
        if (null == ocpcConfig.getOcpcConvType() || null == ocpcConfig.getOcpcConvCost() || null == ocpcConfig.getUnitPrice()) {
            return null;
        }
        int ocpcConvCost = NumberUtils.defaultInt(ocpcConfig.getOcpcConvCost());
        Integer ocpcConvType = ocpcConfig.getOcpcConvType();
        Integer unitPrice = ocpcConfig.getUnitPrice();

        // OCPC开启条件:转化达到阈值，默认5
        String dateStr = DateUtil.formatDate(new Date());
        int convCount = getConvCount(dateStr, orientId, ocpcConvType);
        if (convCount < ocpcConfig.getConvThreshold()) {
            return null;
        }

        // 查询广告消耗
        Long consume = NumberUtils.defaultLong(redisAtomicClient.getLong(EngineRedisKeyFactory.K108.join(dateStr, orientId, ADVERT_BILLING.getType())));

        // 计算预估出价
        // oCPC单价 = (考核成本 * (今日累计转化 + 预估CTR * 预估CVR) - 今日消耗) / 预估CTR
        // 单价在 0.5 ~ 2 倍区间调整
        int ocpcPrice = (int) ((ocpcConvCost * (convCount + pCtr * pCvr) - consume) / pCtr);
        return Math.min(2 * unitPrice, Math.max(Math.max(unitPrice / 2, 1), ocpcPrice));
    }

    @Override
    public int getConvCount(AdvertQuarterDataEntity advertData, Integer ocpcConvType) {
        if (null == advertData || null == ocpcConvType) {
            return 0;
        }
        Integer convCount = null;
        if (isConversion(ocpcConvType)) {
            convCount = advertData.getLpClickUv();
        } else if (isPay(ocpcConvType)) {
            convCount = advertData.getPay();
        }
        return NumberUtils.defaultInt(convCount);
    }

    @Override
    public int getConvCount(String dateStr, Long orientId, Integer ocpcConvType) {
        if (null == orientId || null == ocpcConvType) {
            return 0;
        }
        Long convCount = null;
        if (isConversion(ocpcConvType)) {
            convCount = redisAtomicClient.getLong(EngineRedisKeyFactory.K108.join(dateStr, orientId, LANDPAGE_CLICK.getType()));
        } else if (isPay(ocpcConvType)) {
            convCount = redisAtomicClient.getLong(EngineRedisKeyFactory.K108.join(dateStr, orientId, CONVERT_EVENT.getType(), PAY.getType()));
        }
        return NumberUtils.defaultLong(convCount).intValue();
    }
}
