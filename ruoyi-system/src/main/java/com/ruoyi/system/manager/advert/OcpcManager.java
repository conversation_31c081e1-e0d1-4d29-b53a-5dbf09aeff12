package com.ruoyi.system.manager.advert;

import com.ruoyi.system.entity.datashow.AdvertQuarterDataEntity;

/**
 * OCPC manager
 *
 * <AUTHOR>
 * @date 2022/10/24
 */
public interface OcpcManager {

    /**
     * 计算OCPC出价
     *
     * @param advertId 广告ID
     * @param ocpcConvType OCPC转化类型
     * @param ocpcConvCost OCPC转化成本
     * @return OCPC出价，null即不满足OCPC条件
     */
    Integer calculateOcpcPrice(Long advertId, Integer ocpcConvType, Integer ocpcConvCost);

    /**
     * 计算OCPC出价
     *
     * @param orientId 配置ID
     * @param pCtr 预估CTR
     * @param pCvr 预估CVR
     * @return OCPC出价，null即不满足OCPC条件
     */
    Integer calculateOcpcPrice(Long orientId, Double pCtr, Double pCvr);

    /**
     * 根据OCPC转化类型获取转化量
     *
     * @param advertData 最近半小时广告数据
     * @param ocpcConvType OCPC转化类型
     * @return 转化量
     */
    int getConvCount(AdvertQuarterDataEntity advertData, Integer ocpcConvType);

    /**
     * 根据OCPC转化类型获取转化量
     *
     * @param dateStr 日期
     * @param orientId 配置ID
     * @param ocpcConvType OCPC转化类型
     * @return 转化量
     */
    int getConvCount(String dateStr, Long orientId, Integer ocpcConvType);
}
