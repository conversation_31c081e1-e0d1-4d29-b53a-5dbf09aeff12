package com.ruoyi.system.manager.sms;

import java.util.List;

/**
 * 圈量接口(简约版)
 *
 * <AUTHOR>
 * @date 2023/5/10 11:30
 */
public interface QLPhoneManager {

    /**
     * 获取圈量AccessToken
     *
     * @return 圈量AccessToken
     */
    String getAccessToken();

    /**
     * 清除圈量AccessToken
     */
    void removeAccessToken();

    /**
     * 圈量添加好友
     *
     * @param phones 手机号列表
     * @return 错误码
     */
    Integer addExtUserByRule(List<String> phones);
}
