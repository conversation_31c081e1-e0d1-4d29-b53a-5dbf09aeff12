package com.ruoyi.system.manager.sms.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.http.HttpClientUtil;
import com.ruoyi.common.utils.youku.Md5Util;
import com.ruoyi.system.manager.sms.CodeSmsManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 验证码短信管理接口实现
 *
 * <AUTHOR>
 * @date 2023/04/24
 */
@Slf4j
@Service
public class CodeSmsManagerImpl implements CodeSmsManager {

    private static final String CHUANGLAN_SEND_URL = "https://smssh1.253.com/msg/v1/send/json";

    /**
     * 发送短信地址(云智信)
     */
    private static final String YUNZHIXIN_SEND_URL = "http://api.yunzhixin.com:11140/txp/sms/send";
    /**
     * 配置您申请的tradeKey
     */
    private static final String TRADE_KEY = "389f4e3a6aba413f9050f2210c9a6bc9";
    /**
     * 返回码字符
     */
    private static final String RETURN_CODE = "return_code";
    /**
     * 返回成功时返回码的值
     */
    private static final String SUCCESS = "0000";

    @Override
    public boolean sendByChuanglan(String phone, String code) {
        JSONObject param = new JSONObject();
        param.put("account", "YZM8922582");//API账号
        param.put("password", "sasOIB12xud694");//API密码
        param.put("msg", StrUtil.format("【传道学院】验证码：{}。有效期15分钟，切勿转发或告知他人。如非本人操作请忽略。", code));//短信内容
        param.put("phone", phone);//手机号
        param.put("report", "true");//是否需要状态报告
//        json.put("extend", "1");//自定义扩展码
        String resp = HttpUtil.post(CHUANGLAN_SEND_URL, param.toString());
        log.info("验证码发送-创蓝云智, phone={}, code={}, resp={}", phone, code, resp);
        JSONObject result = JSON.parseObject(resp);
        if (null != result && Objects.equals(result.getInteger("code"), 0)) {
            return true;
        }
        log.error("验证码发送失败-创蓝云智, phone={}, code={}, resp={}", phone, code, resp);
        return true;

//        //查询余额
//        String balanceUrl = "https://smssh1.253.com/msg/balance/json";
//        Map map1 = new HashMap();
//        map1.put("account", "N*******");
//        map1.put("password", "************");
//        JSONObject js1 = (JSONObject) JSONObject.toJSON(map1);
//        System.out.println(sendSmsByPost(balanceUrl, js1.toString()));
    }

    @Override
    public boolean sendByYunZhiXin(String phone,String code,String orderId) {
        //组装请求参数
        Map<String, String> smsRequestParam = new HashMap<>(16);
        //用户编号，注册www.yunzhixin.com的手机号码
        smsRequestParam.put("account", "***********");
        //需要发送的手机号
        smsRequestParam.put("mobile", phone);
        //商户提交的订单号（商户保证其唯一性）
        smsRequestParam.put("order_id",orderId+ System.currentTimeMillis());
        //用户服务器时间戳(13位),JDK8版本可以使用String.valueOf(Instant.now().toEpochMilli())获取,JDK7版本可以使用String.valueOf(System.currentTimeMillis())获取
        smsRequestParam.put("time", String.valueOf(Instant.now().toEpochMilli()));
        //模板编号
        smsRequestParam.put("tpl_id", "TP23071117");
        //短信所需传入的参数
        smsRequestParam.put("params", "code:"+code);
        StringBuffer source = new StringBuffer(50).append(smsRequestParam.get("mobile")).append("|").append(smsRequestParam.get(
                "account")).append("|").append(smsRequestParam.get("time")).append("|").append(smsRequestParam.get("tpl_id")).append("#").append(TRADE_KEY);
        String signKey = Md5Util.MD5(source.toString()).toUpperCase();
        //生成签名,组装参数
        smsRequestParam.put("sign", signKey);
        //发起短信发送请求
        String response = HttpClientUtil.sendPost(YUNZHIXIN_SEND_URL, smsRequestParam);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            //解析返回json字符串,成功时打印订单号,失败时打印返回错误码
            JsonNode jsonNode = objectMapper.readTree(response);
            if (!StringUtils.equals(jsonNode.get(RETURN_CODE).asText(), SUCCESS)) {
                log.error("云智信短信发送异常phone:{}:return_code:{}", phone, jsonNode.get(RETURN_CODE).asText());
                return false;
            }
        } catch (IOException e) {
            log.error("解析云智信短信接口返回值异常:{},phone:{}",e.getMessage(),phone);
        }
        return true;
    }
}