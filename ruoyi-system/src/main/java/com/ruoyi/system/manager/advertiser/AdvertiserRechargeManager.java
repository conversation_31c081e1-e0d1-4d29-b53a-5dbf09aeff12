package com.ruoyi.system.manager.advertiser;

import com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeApplyReq;
import com.ruoyi.system.req.agent.finance.WisAgentRechargeReq;

/**
 * 广告主充值Manager
 *
 * <AUTHOR>
 * @date 2022/3/21 2:07 下午
 */
public interface AdvertiserRechargeManager {

    /**
     * 申请
     *
     * @param req 参数
     * @return 结果
     */
    int apply(AdvertiserRechargeApplyReq req);

    /**
     * 审核通过
     *
     * @param rechargeId 充值ID
     * @param auditReason 通过理由
     * @return 结果
     */
    int auditApprove(Long rechargeId, String auditReason);

    /**
     * 审核拒绝
     *
     * @param rechargeId 充值ID
     * @param auditReason 拒绝理由
     * @return 结果
     */
    int auditRefuse(Long rechargeId, String auditReason);

    /**
     * 代理商划账
     *
     * @param req 参数
     */
    void transfer(WisAgentRechargeReq req);
}
