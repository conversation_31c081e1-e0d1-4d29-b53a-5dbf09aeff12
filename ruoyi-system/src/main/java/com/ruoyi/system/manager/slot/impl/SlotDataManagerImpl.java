package com.ruoyi.system.manager.slot.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.ruoyi.common.enums.ConfirmStatusEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slotcharge.SlotChargeEntity;
import com.ruoyi.system.manager.slot.SlotDataManager;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.slotcharge.SlotChargeService;
import com.ruoyi.system.vo.slotcharge.SlotChargeDataVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 广告位数据相关manager
 *
 * <AUTHOR>
 * @date 2022/3/17 10:50 上午
 */
@Service
public class SlotDataManagerImpl implements SlotDataManager {

    @Autowired
    private SlotChargeService slotChargeService;

    @Autowired
    private SlotDataService slotDataService;
    @Autowired
    private AppMonthDataService appMonthDataService;
    @Autowired
    private SlotService slotService;

    @Override
    public SlotChargeDataVO selectSlotChargeDataByDate(Long slotId, Date date) {
        date = DateUtil.beginOfDay(date);
        SlotChargeEntity chargeEntity = slotChargeService.selectBySlotIdAndDate(slotId, date);
        SlotData slotData = slotDataService.selectBySlotIdAndDate(slotId, date);
        SlotChargeDataVO vo = new SlotChargeDataVO();
        if(Objects.nonNull(chargeEntity)){
            vo.setChargeType(chargeEntity.getChargeType());
            vo.setChargePrice(chargeEntity.getChargePrice());
        }
        if(Objects.nonNull(slotData)){
            vo.setAppRevenue(slotData.getAppRevenue());
            vo.setNhCost(slotData.getNhCost());
            vo.setSlotRequestPv(slotData.getSlotRequestPv());
            vo.setSlotRequestUv(slotData.getSlotRequestUv());
            vo.setOuterCost(slotData.getOuterCost());
        }
        return vo;
    }

    @Override
    public List<SlotChargeDataVO> selectSlotChargeDataByMonth(Long slotId, Date date) {
        Date today = new Date();
        Date startDate = DateUtil.beginOfMonth(date);
        Date endDate = Objects.equals(DateUtils.dateTimeMonth(date),DateUtils.dateTimeMonth(today)) ? DateUtils.addDays(today,-1) : DateUtil.endOfMonth(date);
        List<SlotChargeEntity> chargeEntities = slotChargeService.selectListBySlotIdsAndDateRange(Lists.newArrayList(slotId), startDate,endDate);
        Map<Date, SlotChargeEntity> chargeEntityMap = chargeEntities.stream().collect(Collectors.toMap(SlotChargeEntity::getCurDate, Function.identity(), (v1, v2) -> v1));
        Slot slot = slotService.selectSimpleSlotById(slotId);
        Long appId = slot.getAppId();
        SlotData slotData = new SlotData();
        slotData.setSlotId(slotId);
        slotData.setStartDate(startDate);
        slotData.setEndDate(endDate);
        List<SlotData> slotDataList = slotDataService.selectAllSlotDataList(slotData);
        Map<Date, SlotData> slotDataMap = slotDataList.stream().collect(Collectors.toMap(SlotData::getCurDate, Function.identity(), (v1, v2) -> v1));
        AppMonthDataEntity monthDataEntity = appMonthDataService.selectByAppIdAndMonth(appId, DateUtils.dateTimeMonth(date));
        boolean canEdit = true;
        if(Objects.nonNull(monthDataEntity) && !Objects.equals(monthDataEntity.getConfirmStatus(), ConfirmStatusEnum.NO_CONFIRM.getStatus())){
            //已经确认的月账单无法修改
            canEdit = false;
        }
        //计算两个日期之间相差天数
        int daySpan = (int) DateUtil.betweenDay(startDate,endDate, true) + 1;
        boolean finalCanEdit = canEdit;
        List<SlotChargeDataVO> dataVOS = IntStream.range(0, daySpan).boxed().map(amount -> {
            Date curDate = DateUtils.addDays(startDate, amount);
            SlotChargeEntity chargeEntity = chargeEntityMap.get(curDate);
            SlotChargeDataVO vo = new SlotChargeDataVO();
            if (Objects.nonNull(chargeEntity)) {
                vo.setChargeType(chargeEntity.getChargeType());
                vo.setChargePrice(chargeEntity.getChargePrice());
            }
            SlotData data = slotDataMap.get(curDate);
            if (Objects.nonNull(data)) {
                vo.setAppRevenue(data.getAppRevenue());
                vo.setNhCost(data.getNhCost());
                vo.setSlotRequestPv(data.getSlotRequestPv());
                vo.setSlotRequestUv(data.getSlotRequestUv());
                vo.setOuterCost(data.getOuterCost());
            }
            vo.setCurDate(curDate);
            vo.setCanEdit(finalCanEdit);
            return vo;
        }).collect(Collectors.toList());
        Collections.reverse(dataVOS);
        return dataVOS;
    }
}
