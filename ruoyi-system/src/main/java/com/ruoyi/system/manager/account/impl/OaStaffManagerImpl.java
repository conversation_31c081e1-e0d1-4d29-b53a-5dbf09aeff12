package com.ruoyi.system.manager.account.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.bo.account.OaStaffBo;
import com.ruoyi.system.entity.oa.department.DepartmentEntity;
import com.ruoyi.system.entity.oa.permission.PostPermissionEntity;
import com.ruoyi.system.entity.oa.permission.StaffPermissionEntity;
import com.ruoyi.system.entity.oa.post.PostEntity;
import com.ruoyi.system.entity.oa.staff.StaffInfoEntity;
import com.ruoyi.system.entity.oa.user.UserEntity;
import com.ruoyi.system.manager.account.OaStaffManger;
import com.ruoyi.system.service.oa.department.DepartmentService;
import com.ruoyi.system.service.oa.permission.PermissionService;
import com.ruoyi.system.service.oa.permission.PostPermissionService;
import com.ruoyi.system.service.oa.permission.StaffPermissionService;
import com.ruoyi.system.service.oa.post.PostService;
import com.ruoyi.system.service.oa.staff.StaffInfoService;
import com.ruoyi.system.service.oa.user.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * OA员工Manager接口实现
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
@Service
public class OaStaffManagerImpl implements OaStaffManger {

    @Autowired
    private UserService userService;

    @Autowired
    private StaffInfoService staffInfoService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private PostService postService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private PostPermissionService postPermissionService;

    @Autowired
    private StaffPermissionService staffPermissionService;

    @Override
    public OaStaffBo selectByEmail(String email) {
        // 查询OA的用户ID
        UserEntity oaUser = userService.selectByEmail(email);
        if (null == oaUser) {
            return null;
        }

        OaStaffBo oaStaff = new OaStaffBo();
        oaStaff.setUserId(oaUser.getId());
        oaStaff.setEmail(email);

        // 查询OA的员工信息
        StaffInfoEntity staffInfo = staffInfoService.selectByUserId(oaUser.getId());
        if (null == staffInfo) {
            return oaStaff;
        }
        // 查询部门信息
        DepartmentEntity department = departmentService.selectById(staffInfo.getDepartmentId());
        oaStaff.setDepartment(department);
        // 查询职位
        PostEntity post = postService.selectById(staffInfo.getPostId());
        oaStaff.setPost(post);
        // 查询OA权限
        Set<String> oaPermissionKeys = selectOaPermission(staffInfo.getId(), staffInfo.getPostId());
        oaStaff.setOaPermissionKeys(oaPermissionKeys);
        return oaStaff;
    }

    @Override
    public Map<String, String> selectIdDepartmentMapByEmails(List<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return Collections.emptyMap();
        }
        // 查询OA的用户ID
        Map<String, Long> emailMap = userService.selectMapByEmails(emails);
        // 查询OA的员工的部门
        Map<Long, Long> staffMap = staffInfoService.selectDepartmentMapByUserIds(new ArrayList<>(emailMap.values()));
        Map<Long, String> departmentMap = departmentService.selectDepartmentKeyMap(new ArrayList<>(staffMap.values()));
        // 邮箱-部门Key映射
        Map<String, String> map = new HashMap<>();
        emails.forEach(email ->
                Optional.ofNullable(departmentMap.get(staffMap.getOrDefault(emailMap.getOrDefault(email, 0L), 0L)))
                        .ifPresent(department -> map.put(email, department))
        );
        return map;
    }

    @Override
    public Map<String, String> selectIdPostMapByEmails(List<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return Collections.emptyMap();
        }
        // 查询OA的用户ID
        Map<String, Long> emailMap = userService.selectMapByEmails(emails);
        // 查询OA的员工的部门
        Map<Long, Long> staffMap = staffInfoService.selectPostMapByUserIds(new ArrayList<>(emailMap.values()));
        Map<Long, String> postMap = postService.selectPostNameMap(new ArrayList<>(staffMap.values()));
        // 邮箱-部门Key映射
        Map<String, String> map = new HashMap<>();
        emails.forEach(email ->
                Optional.ofNullable(postMap.get(staffMap.getOrDefault(emailMap.getOrDefault(email, 0L), 0L)))
                        .ifPresent(post -> map.put(email, post))
        );
        return map;
    }

    @Override
    public String selectEmailByPhone(String phone) {
        StaffInfoEntity staffInfoEntity = staffInfoService.selectByPhone(phone);
        if(Objects.isNull(staffInfoEntity)){
            return null;
        }
        UserEntity userEntity = userService.selectById(staffInfoEntity.getUserId());
        if(Objects.isNull(userEntity)){
            return null;
        }
        return userEntity.getEmail();
    }

    @Override
    public List<String> selectUserIdsByDepartmentKey(List<String> departmentKeys) {
        if (CollectionUtils.isEmpty(departmentKeys)) {
            return Collections.emptyList();
        }
        List<Long> departmentIds = departmentService.selectDepartmentIds(departmentKeys);
        return staffInfoService.selectEmailByDepartmentIds(departmentIds);
    }

    /**
     * 查询OA配置的权限
     *
     * @return 权限key集合
     */
    private Set<String> selectOaPermission(Long staffId, Long postId) {
        List<Long> permissionIds = new ArrayList<>();
        PostPermissionEntity postPermission = postPermissionService.selectByPostIdAndSystemId(postId, 1L);
        if (null != postPermission && JSONUtil.isTypeJSONArray(postPermission.getPermissions())) {
            permissionIds.addAll(JSONObject.parseArray(postPermission.getPermissions(), Long.class));
        }
        StaffPermissionEntity staffPermission = staffPermissionService.selectByStaffIdAndSystemId(staffId, 1L);
        if (null != staffPermission && JSONUtil.isTypeJSONArray(staffPermission.getPermissions())) {
            permissionIds.addAll(JSONObject.parseArray(staffPermission.getPermissions(), Long.class));
        }
        return permissionService.selectPermissionKeyByIds(permissionIds);
    }
}
