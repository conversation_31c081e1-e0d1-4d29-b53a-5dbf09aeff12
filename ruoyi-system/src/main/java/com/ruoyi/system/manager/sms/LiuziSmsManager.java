package com.ruoyi.system.manager.sms;

import com.ruoyi.system.vo.sms.SmsTemplateVO;

import java.util.List;

/**
 * 留资短信管理
 *
 * <AUTHOR>
 * @date 2022/12/5 15:23
 */
public interface LiuziSmsManager {

    /**
     * 查询所有短信模版
     */
    List<SmsTemplateVO> selectAllTemplate();

    /**
     * 发送助通短信
     * @param mobile
     * @param tpId
     * @param content
     * @return 消息id
     */
    String sendSmsZhuTong(String mobile, Long tpId, String content, Long recordId);

    /**
     * 发送掌榕短信
     * @param mobile
     * @param content
     * @return
     */
    String sendSmsZhangRong(String mobile,String content);

    /**
     * 发送瑞濯短信
     * @param mobile
     * @param content
     * @return
     */
    String sendSmsRuiZhuo(String mobile,String content);
    /**
     * 发送百悟短信
     * @param mobile
     * @param content
     * @param id
     * @return
     */
    String sendSmsBaiWu(String mobile,String content,Long id);

    /**
     * 发送传臻短信
     *
     * @param mobile
     * @param content
     * @return
     */
    String sendSmsChuanZhen(String mobile,String content);
    /**
     * 发送枫雪云短信
     *
     * @param mobile
     * @param content
     * @return
     */
    String sendSmsFengXueYun(String mobile,String content);
    /**
     * 发送赣安短信
     *
     * @param mobile
     * @param content
     * @return
     */
    String sendSmsGanAn(String mobile,String content);

    /**
     * 更新留资短信消息ID
     * @param id
     * @param msgId
     */
    boolean updateLiuziSmsMsgId(Long id,String msgId);
}
