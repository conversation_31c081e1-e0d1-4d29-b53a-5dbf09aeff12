package com.ruoyi.system.manager.publisher.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.account.OaStaffBo;
import com.ruoyi.system.entity.account.finance.AccountPrepayApplyRecordEntity;
import com.ruoyi.system.entity.account.finance.AccountPrepayStatementRecordEntity;
import com.ruoyi.system.entity.account.finance.AccountRevenueEntity;
import com.ruoyi.system.entity.qualification.AccountQualificationEntity;
import com.ruoyi.system.manager.account.OaStaffManger;
import com.ruoyi.system.manager.publisher.PublisherPrepayManager;
import com.ruoyi.system.req.publisher.prepay.PrepayApplyReq;
import com.ruoyi.system.req.publisher.prepay.PrepayAuditReq;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.finance.AccountRevenueService;
import com.ruoyi.system.service.finance.WithdrawRecordService;
import com.ruoyi.system.service.publisher.prepay.AccountPrepayApplyRecordService;
import com.ruoyi.system.service.publisher.prepay.AccountPrepayStatementRecordService;
import com.ruoyi.system.service.qualification.AccountQualificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.hutool.core.util.BooleanUtil.isTrue;
import static com.ruoyi.common.constant.OaConstants.FINANCE_AUDITOR_LIST;
import static com.ruoyi.common.constant.OaConstants.PERMISSION_PREPAY_BUSINESS_AUDIT;
import static com.ruoyi.common.constant.OaConstants.PERMISSION_PREPAY_DEPARTMENT_AUDIT;
import static com.ruoyi.common.enums.publisher.PayType.isPrepay;
import static com.ruoyi.common.enums.publisher.PrepayAuditStatus.IN_AUDIT;
import static com.ruoyi.common.enums.publisher.PrepayAuditStatus.PASSED;
import static com.ruoyi.common.enums.publisher.PrepayAuditStatus.REFUSED;
import static com.ruoyi.common.enums.publisher.PrepayAuditStatus.isAuditPassed;
import static com.ruoyi.common.enums.publisher.PrepayAuditStatus.isInAudit;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.APPROVE;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.CEO_READY;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.CEO_REFUSE;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.FINANCE_READY;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.FINANCE_REFUSE;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.LEADER_READY;
import static com.ruoyi.common.enums.publisher.PrepayComplexAuditStatus.LEADER_REFUSE;

/**
 * 媒体预付款Manager接口实现
 *
 * <AUTHOR>
 * @date 2022/07/29
 */
@Service
public class PublisherPrepayManagerImpl implements PublisherPrepayManager {

    @Autowired
    private AccountPrepayApplyRecordService accountPrepayApplyRecordService;

    @Autowired
    private AccountPrepayStatementRecordService accountPrepayStatementRecordService;

    @Autowired
    private AccountRevenueService accountRevenueService;

    @Autowired
    private AccountQualificationService accountQualificationService;

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private WithdrawRecordService withdrawRecordService;

    @Autowired
    private OaStaffManger oaStaffManger;

    @Autowired
    public TransactionTemplate transactionTemplate;

    @Override
    public void prepayApply(PrepayApplyReq req) {
        AccountRevenueEntity accountRevenue = accountRevenueService.selectByAccountId(req.getAccountId());
        if (null == accountRevenue || !isPrepay(accountRevenue.getPayType())) {
            throw new CustomException("申请失败，非预付款类型媒体");
        }
        // 查询媒体账号资质
        AccountQualificationEntity qualification = accountQualificationService.selectByAccountId(req.getAccountId());
        if (null == qualification) {
            throw new CustomException("申请失败，请完善媒体资质信息");
        }
        // 媒体欠款金额=历史预付款金额-媒体总收益(日账单维度)+审核中已通过的提现金额
        Long appRevenue = slotDataService.sumAppRevenueByAccountId(req.getAccountId());
        Integer withdrawAmount = withdrawRecordService.sumWithdrawAmountByAccountId(req.getAccountId());
        Long prepayAmount = Math.max(accountRevenue.getPrepayAmount() - appRevenue.intValue() + withdrawAmount, 0L);

        AccountPrepayApplyRecordEntity record = new AccountPrepayApplyRecordEntity();
        record.setAccountId(req.getAccountId());
        record.setApplyPrepayAmount(req.getApplyPrepayAmount());
        record.setPrepayAmount(prepayAmount);
        record.setBankAccount(qualification.getBankAccount());
        record.setBankName(qualification.getBankName());
        record.setBankAccountName(qualification.getBankAccountName());
        record.setInvoiceList(JSON.toJSONString(CollUtil.defaultIfEmpty(req.getInvoiceList(), Collections.emptyList())));
        record.setRemark(req.getRemark());
        record.setAuditStatus(IN_AUDIT.getStatus());
        record.setApplicantId(SecurityUtils.getLoginUser().getCrmAccountId());
        record.setPrepaySubjectList(JSONObject.toJSONString(req.getPrepaySubjectList().stream()
                                            .sorted().distinct().collect(Collectors.toList())));
        accountPrepayApplyRecordService.insert(record);
    }

    @Override
    public void prepayAudit(PrepayAuditReq req) {
        AccountPrepayApplyRecordEntity record = accountPrepayApplyRecordService.selectById(req.getRecordId());
        if (null == record || !isInAudit(record.getAuditStatus())) {
            throw new CustomException("审核异常，无效的申请记录或已审核");
        }
        AccountRevenueEntity accountRevenue = accountRevenueService.selectByAccountId(record.getAccountId());
        if (null == accountRevenue || !isPrepay(accountRevenue.getPayType())) {
            throw new CustomException("审核异常，非预付款类型媒体");
        }

        // 审核权限判断
        LoginUser user = SecurityUtils.getLoginUser();
        OaStaffBo staff = oaStaffManger.selectByEmail(user.getEmail());
        String postKey = null != staff.getPost() ? staff.getPost().getPostKey() : "";
        Integer complexAuditStatus = NumberUtils.defaultInt(record.getComplexAuditStatus(), record.getAuditStatus());
        if (!(user.isAdmin() && isInAudit(record.getAuditStatus())
                || Objects.equals(complexAuditStatus, LEADER_READY.getStatus()) && staff.getOaPermissionKeys().contains(PERMISSION_PREPAY_DEPARTMENT_AUDIT)
                || Objects.equals(complexAuditStatus, CEO_READY.getStatus()) && staff.getOaPermissionKeys().contains(PERMISSION_PREPAY_BUSINESS_AUDIT)
                || Objects.equals(complexAuditStatus, FINANCE_READY.getStatus()) && FINANCE_AUDITOR_LIST.contains(postKey))) {
            throw new CustomException("审核失败，无审核权限");
        }

        Boolean auditResult = transactionTemplate.execute(status -> {
            AccountPrepayApplyRecordEntity updateRecord = new AccountPrepayApplyRecordEntity();
            updateRecord.setId(req.getRecordId());
            updateRecord.setAuditorId(user.getCrmAccountId());
            updateRecord.setAuditTime(new Date());
            if (!isAuditPassed(req.getAuditStatus())) {
                updateRecord.setAuditReason(req.getAuditReason());
            }

            // 运营/商务Leader审核
            if (Objects.equals(complexAuditStatus, LEADER_READY.getStatus())) {
                updateRecord.setAuditStatus(isAuditPassed(req.getAuditStatus()) ? IN_AUDIT.getStatus() : REFUSED.getStatus());
                updateRecord.setComplexAuditStatus(isAuditPassed(req.getAuditStatus()) ? CEO_READY.getStatus() : LEADER_REFUSE.getStatus());
                updateRecord.setLeaderAuditorId(updateRecord.getAuditorId());
                updateRecord.setLeaderAuditTime(updateRecord.getAuditTime());
                updateRecord.setLeaderAuditReason(req.getAuditReason());
                accountPrepayApplyRecordService.updateById(updateRecord);
                return true;
            }
            // CEO/总裁审核
            if (Objects.equals(complexAuditStatus, CEO_READY.getStatus())) {
                updateRecord.setAuditStatus(isAuditPassed(req.getAuditStatus()) ? IN_AUDIT.getStatus() : REFUSED.getStatus());
                updateRecord.setComplexAuditStatus(isAuditPassed(req.getAuditStatus()) ? FINANCE_READY.getStatus() : CEO_REFUSE.getStatus());
                updateRecord.setCeoAuditorId(updateRecord.getAuditorId());
                updateRecord.setCeoAuditTime(updateRecord.getAuditTime());
                updateRecord.setCeoAuditReason(req.getAuditReason());
                accountPrepayApplyRecordService.updateById(updateRecord);
                return true;
            }

            // 财务审核
            updateRecord.setAuditStatus(req.getAuditStatus());
            updateRecord.setComplexAuditStatus(isAuditPassed(req.getAuditStatus()) ? APPROVE.getStatus() : FINANCE_REFUSE.getStatus());
            updateRecord.setFinanceAuditorId(updateRecord.getAuditorId());
            updateRecord.setFinanceAuditTime(updateRecord.getAuditTime());
            updateRecord.setFinanceAuditReason(req.getAuditReason());
            accountPrepayApplyRecordService.updateById(updateRecord);

            // 更新账户预付款
            if (isAuditPassed(req.getAuditStatus())) {
                boolean result = accountRevenueService.addPrepayAmount(record.getAccountId(), record.getApplyPrepayAmount());
                if (result && accountRevenue.getWithdrawableAmount() > 0) {
                    // 扣减可提现金额
                    Integer deductAmount = Math.min(accountRevenue.getWithdrawableAmount(), record.getApplyPrepayAmount());
                    result = accountRevenueService.deductionWithdrawAmount(record.getAccountId(), deductAmount);
                    if (!result) {
                        status.setRollbackOnly();
                        return false;
//                        throw new CustomException("扣减可提现金额失败");
                    }
                    // 新增预付款结算记录(结算单ID=0表示可提现金额扣减)
                    AccountPrepayStatementRecordEntity statementRecord = new AccountPrepayStatementRecordEntity();
                    statementRecord.setAccountId(record.getAccountId());
                    statementRecord.setPrepayRecordId(record.getId());
                    statementRecord.setStatementId(0L);
                    statementRecord.setAmount(deductAmount);
                    result = accountPrepayStatementRecordService.insert(statementRecord);
                }
                if (!result) {
                    status.setRollbackOnly();
                    return false;
//                    throw new CustomException("审核失败");
                }
            }
            return true;
        });
        if (!isTrue(auditResult)) {
            throw new CustomException("审核失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer prepayStatement(Long accountId, Long statementId, int revenue) {
        if (null == accountId || null == statementId || revenue <= 0) {
            return revenue;
        }
        // 判断是否预付款媒体
        AccountRevenueEntity accountRevenue = accountRevenueService.selectByAccountId(accountId);
        if (null == accountRevenue || !isPrepay(accountRevenue.getPayType())) {
            return revenue;
        }
        // 查询预付款记录
        List<AccountPrepayApplyRecordEntity> prepayRecords = accountPrepayApplyRecordService.selectListByAccountIdAndAuditStatus(accountId, PASSED.getStatus());
        // 查询预付款记录结算映射
        Map<Long, Integer> prepayRecordStatementMap = accountPrepayStatementRecordService.sumAmountGroupByPrepayRecordId(accountId);
        // 遍历预付款记录结算
        for (AccountPrepayApplyRecordEntity prepayRecord : prepayRecords) {
            if (revenue <= 0) {
                break;
            }
            int statementAmountSum = prepayRecordStatementMap.getOrDefault(prepayRecord.getId(), 0);
            if (statementAmountSum >= prepayRecord.getApplyPrepayAmount()) {
                continue;
            }
            // 计算预付款记录剩余可计算金额
            int statementAmount = Math.min(revenue, prepayRecord.getApplyPrepayAmount() - statementAmountSum);
            // 新增预付款结算记录
            AccountPrepayStatementRecordEntity record = new AccountPrepayStatementRecordEntity();
            record.setAccountId(accountId);
            record.setPrepayRecordId(prepayRecord.getId());
            record.setStatementId(statementId);
            record.setAmount(statementAmount);
            boolean result = accountPrepayStatementRecordService.insert(record);
            if (!result) {
                throw new CustomException("新增预付款结算记录失败");
            }
            revenue -= statementAmount;
        }
        return revenue;
    }
}
