package com.ruoyi.system.manager.advertiser.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.manager.advertiser.AdvertiserManager;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.util.DingRobotUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.common.WhitelistType.OFFLINE_DATA_ADVERTISER;

/**
 * 广告主manager实现
 *
 * <AUTHOR>
 * @date 2022/11/22
 */
@Service
public class AdvertiserManagerImpl implements AdvertiserManager {

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private AccountService accountService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOfflineList(List<Long> advertiserIds) {
        // 查询当前离线广告主
        List<Long> oldList = whitelistService.list(OFFLINE_DATA_ADVERTISER, Long.class);

        // 筛选出新增的离线广告主
        List<Long> addIds = new ArrayList<>(advertiserIds);
        addIds.removeAll(oldList);

        // 筛选出删除的离线广告主
        String today = DateUtil.today();
        List<Long> removeIds = new ArrayList<>(oldList);
        removeIds.removeAll(advertiserIds);
        List<Account> removeAccountList = accountService.selectListByIds(removeIds);
        for (Account account : removeAccountList) {
            // 更新离线转实时时间
            Optional.ofNullable(JSON.parseObject(account.getExtInfo(), AccountExtInfo.class)).ifPresent(extInfo -> {
                extInfo.setOtoDate(today);
                Account updateAccount = new Account();
                updateAccount.setId(account.getId());
                updateAccount.setExtInfo(JSON.toJSONString(extInfo));
                accountService.updateAccount(updateAccount);
            });
        }

        // 更新白名单
        whitelistService.update(OFFLINE_DATA_ADVERTISER, advertiserIds);

        // 钉钉通知
        if (CollectionUtils.isNotEmpty(addIds)) {
            String sb = "广告主转离线\n" +
                    accountService.selectListByIds(addIds).stream().map(s -> "\n" + s.getId() + "-" + s.getCompanyName()).collect(Collectors.joining());
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb);
        }
        if (CollectionUtils.isNotEmpty(removeIds)) {
            String sb = "广告主离线转实时\n" +
                    removeAccountList.stream().map(s -> "\n" + s.getId() + "-" + s.getCompanyName()).collect(Collectors.joining());
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sb);
        }
    }
}
