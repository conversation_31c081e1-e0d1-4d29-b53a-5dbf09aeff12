package com.ruoyi.system.manager.publisher;

import com.ruoyi.system.vo.publisher.prepay.PublisherPrepayVO;
import com.ruoyi.system.vo.publisher.PublisherVO;

import java.util.List;

/**
 * 媒体Manager接口
 *
 * <AUTHOR>
 * @date 2022/07/29
 */
public interface PublisherManager {

    /**
     * 获取媒体信息
     *
     * @param accountId 媒体账号ID
     * @return 媒体信息
     */
    PublisherVO getInfo(Long accountId);

    /**
     * 获取预付款媒体列表(用于媒体月账单生成)
     *
     * @return 预付款媒体列表
     */
    List<PublisherPrepayVO> getPrepayPublisher();

    /**
     * 获取预付款媒体列表(用于预付款申请)
     *
     * @param accountIds 媒体账号ID列表
     * @return 预付款媒体列表
     */
    List<PublisherPrepayVO> getPrepayPublisherForApply(List<Long> accountIds);

    /**
     * 更新媒体付款类型
     *
     * @param accountId 媒体账号ID
     * @param payType 付款类型
     * @return 更新结果
     */
    int updatePayType(Long accountId, Integer payType);

    /**
     * 计算媒体预付款欠款金额
     *
     * @param accountId 媒体账号ID
     * @param prepayAmount 预付款金额
     * @return 预付款欠款金额
     */
    Long calculatePrepayDebtAmount(Long accountId, Long prepayAmount);
}
