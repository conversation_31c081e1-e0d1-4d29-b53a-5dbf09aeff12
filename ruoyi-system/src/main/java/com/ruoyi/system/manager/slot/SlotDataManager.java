package com.ruoyi.system.manager.slot;

import com.ruoyi.system.vo.slotcharge.SlotChargeDataVO;

import java.util.Date;
import java.util.List;

/**
 * 广告位数据相关manager
 *
 * <AUTHOR>
 * @date 2022/3/17 10:49 上午
 */
public interface SlotDataManager {

    /**
     * 根据广告位id和日期查询广告位计费数据
     *
     * @param slotId 广告位ID
     * @param date 日期
     * @return 广告位计费数据
     */
    SlotChargeDataVO selectSlotChargeDataByDate(Long slotId, Date date);
    /**
     * 根据广告位id和月份查询广告位计费数据
     *
     * @param slotId 广告位ID
     * @param date 日期
     * @return 广告位计费数据
     */
    List<SlotChargeDataVO> selectSlotChargeDataByMonth(Long slotId, Date date);
}
