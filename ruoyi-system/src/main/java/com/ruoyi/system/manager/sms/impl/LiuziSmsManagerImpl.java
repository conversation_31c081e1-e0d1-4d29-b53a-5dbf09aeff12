package com.ruoyi.system.manager.sms.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpClientUtil;
import com.ruoyi.system.bo.sms.SmsSendResultBaiWuBO;
import com.ruoyi.system.bo.sms.SmsSendResultChuanZhenBO;
import com.ruoyi.system.bo.sms.SmsSendResultRuiZhuoBO;
import com.ruoyi.system.bo.sms.SmsSendResultZhangRongBO;
import com.ruoyi.system.bo.sms.SmsSendResultZhuTongBO;
import com.ruoyi.system.bo.sms.SmsTemplateBO;
import com.ruoyi.system.bo.sms.SmsTemplateListBO;
import com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity;
import com.ruoyi.system.entity.sms.SmsTemplateEntity;
import com.ruoyi.system.manager.sms.LiuziSmsManager;
import com.ruoyi.system.service.sms.LiuziSmsSendRecordService;
import com.ruoyi.system.service.sms.SmsTemplateService;
import com.ruoyi.system.vo.sms.SmsTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 留资短信服务
 * <AUTHOR>
 * @date 2022/12/5 15:24
 */
@Slf4j
@Service
public class LiuziSmsManagerImpl implements LiuziSmsManager {

    @Autowired
    private SmsTemplateService smsTemplateService;

    @Autowired
    private LiuziSmsSendRecordService liuziSmsSendRecordService;

    @Override
    public List<SmsTemplateVO> selectAllTemplate() {
        SmsTemplateListBO smsTemplateListBO = getSmsTemplateListBO(1);
        if(CollectionUtils.isEmpty(smsTemplateListBO.getTemplates())){
            return Collections.emptyList();
        }
        List<SmsTemplateBO> resultList = smsTemplateListBO.getTemplates();
        int totalPage = smsTemplateListBO.getPage().getTotalPage();
        if(totalPage > 1){
            for(int i = 2;i<=totalPage ;i++){
                resultList.addAll(getSmsTemplateListBO(i).getTemplates());
            }
        }
        List<SmsTemplateEntity> updateList = resultList.stream().map(bo -> {
            SmsTemplateEntity entity = new SmsTemplateEntity();
            entity.setContent(bo.getTemContent());
            entity.setTpId(bo.getTemId());
            return entity;
        }).collect(Collectors.toList());
        smsTemplateService.batchInsertOrUpdate(updateList);
        return BeanUtil.copyToList(updateList,SmsTemplateVO.class);
    }

    @Override
    public String sendSmsZhuTong(String mobile, Long tpId, String content,Long recordId) {

        String signature = ReUtil.get("【[\\\\w|\\u4e00-\\u9fa5]{2,10}】",content,0);
        String tkey = (System.currentTimeMillis()/1000) +"";

        JSONObject param = new JSONObject();
        param.put("username","NHyx");
        param.put("password", SecureUtil.md5(SecureUtil.md5("nuohe2022DXAPI")+tkey));
        param.put("tKey",tkey);
        param.put("signature",signature);
        param.put("tpId",tpId);
        param.put("extend",recordId+"");
        JSONArray list = new JSONArray();
        JSONObject mobileObj = new JSONObject();
        mobileObj.put("mobile",mobile);
        list.add(mobileObj);
        param.put("records",list);
        String response = HttpClientUtil.sendPostJson("https://api.mix2.zthysms.com/v2/sendSmsTp", param.toString());
        SmsSendResultZhuTongBO smsSendResultZhuTongBO = JSON.parseObject(response, SmsSendResultZhuTongBO.class);

        if(Objects.nonNull(smsSendResultZhuTongBO) && Objects.equals(smsSendResultZhuTongBO.getCode(),200)){
            return smsSendResultZhuTongBO.getMsgId();
        }
        log.error("助通短信发送失败,mobile:{},tpId:{},code:{},msg:{}",mobile,tpId, smsSendResultZhuTongBO.getCode(), smsSendResultZhuTongBO.getMsg());
        return "";
    }

    @Override
    public String sendSmsZhangRong(String mobile, String content) {
        String signature = ReUtil.get("【[\\\\w|\\u4e00-\\u9fa5]{2,10}】",content,0);

//        JSONObject param = new JSONObject();
        Map<String,Object> param = new HashMap();
        param.put("accesskey","yg87V5GZmCP8MSIw");
        param.put("secret", "mAq1uGaXGdHDn28oFpQqduPA4ZZe5pHu");
        param.put("sign",signature);
        param.put("mobile",mobile);
        param.put("content",content.replace(signature,""));
        String response = HttpRequest.post("http://api.1cloudsp.com/api/v2/send").form(param).header("Content-Type","application/x-www-form-urlencoded").execute().body();
        SmsSendResultZhangRongBO smsSendResultBO = JSON.parseObject(response, SmsSendResultZhangRongBO.class);

        if(Objects.nonNull(smsSendResultBO) && Objects.equals(smsSendResultBO.getCode(),0)){
            return smsSendResultBO.getBatchId();
        }
        log.error("掌榕短信发送失败,mobile:{},code:{},msg:{}",mobile, smsSendResultBO.getCode(), smsSendResultBO.getMsg());
        return "";
    }

    @Override
    public String sendSmsRuiZhuo(String mobile, String content) {
        JSONObject param = new JSONObject();
        param.put("action","send");
        param.put("account", "330027");
        param.put("password","4xRbdB");
        param.put("mobile",mobile);
        param.put("content",content);
        param.put("extno","*************");
        param.put("rt","json");
        String response = HttpClientUtil.sendPostJson("http://47.108.147.166:7862/smsv2", param.toString());
        SmsSendResultRuiZhuoBO smsSendResultBO = JSON.parseObject(response, SmsSendResultRuiZhuoBO.class);

        if(Objects.nonNull(smsSendResultBO) && Objects.equals(smsSendResultBO.getStatus(),0)){
            return smsSendResultBO.getList().get(0).getMid();
        }
        log.error("瑞濯短信发送失败,mobile:{},status:{},list:{}",mobile, smsSendResultBO.getStatus(), JSONArray.toJSONString(smsSendResultBO.getList()));
        return "";
    }

    @Override
    public String sendSmsBaiWu(String mobile, String content, Long id) {
        String msgId = System.currentTimeMillis() + "";
        JSONObject param = new JSONObject();
        param.put("account","nh1209");
        param.put("transactionId", msgId);
        param.put("password",SecureUtil.md5("nh1209"+"uydv6o"+msgId));
        JSONArray list = new JSONArray();
        JSONObject mobileObj = new JSONObject();
        mobileObj.put("mobile",mobile);
        mobileObj.put("content",content);
        mobileObj.put("uuid",id);
        list.add(mobileObj);
        param.put("list",list);
        String response = HttpClientUtil.sendPostJson("http://plate.hbsmservice.com:8080/sms/v2/send-different", param.toString());
        if(StringUtils.isBlank(response) || Objects.equals(response,"null")){
            //出现问题在此请求
            response = HttpClientUtil.sendPostJson("http://plate.hbsmservice.com:8080/sms/v2/send-different", param.toString());
        }
        SmsSendResultBaiWuBO smsSendResultBO = JSON.parseObject(response, SmsSendResultBaiWuBO.class);

        if(Objects.nonNull(smsSendResultBO) && CollectionUtils.isEmpty(smsSendResultBO.getFailList())){
            return smsSendResultBO.getTransactionId();
        }

        log.error("百悟短信发送失败,mobile:{},response:{},",mobile, response);
        return "";
    }

    @Override
    public String sendSmsChuanZhen(String mobile, String content) {

        Map<String,Object> param = new HashMap();
        param.put("SpCode","835");
        param.put("LoginName", "HZNHYDJY");
        param.put("Password","v9522q@@n");
        param.put("MessageContent",content);
        param.put("UserNumber",mobile);
        String response = HttpRequest.post("http://47.103.122.122:8513/sms/Api/ReturnJson/Send.do").form(param).header("Content-Type","application/x-www-form-urlencoded").execute().body();
        SmsSendResultChuanZhenBO smsSendResultBO = JSON.parseObject(response, SmsSendResultChuanZhenBO.class);

        if(Objects.nonNull(smsSendResultBO) && Objects.equals(smsSendResultBO.getResult(),"0")){
            return smsSendResultBO.getTaskid();
        }
        log.error("传臻短信发送失败,mobile:{},result:{},desc:{}",mobile, smsSendResultBO.getResult(), smsSendResultBO.getDescription());
        return "";
    }

    @Override
    public String sendSmsFengXueYun(String mobile, String content) {
        String extno="10690";
        JSONObject param = new JSONObject();
        param.put("action","send");
        param.put("account", "975536");
        param.put("password","ihSHw8CeVn7M");
        param.put("extno",extno);
        param.put("mobile",mobile);
        param.put("content",content);
        String response = HttpClientUtil.sendPostJson("http://**************:7862/smsv2", param.toString());
        JSONObject jsonObject = JSON.parseObject(response);
        if(jsonObject.getInteger("status") == 0 && CollectionUtils.isNotEmpty(jsonObject.getJSONArray("list"))){
            JSONArray list = jsonObject.getJSONArray("list");
            JSONObject data = (JSONObject)list.get(0);
            return data.getString("mid");
        }
        log.error("枫雪云短信发送失败,mobile:{},response:{},",mobile, response);
        return "";
    }

    @Override
    public String sendSmsGanAn(String mobile, String content) {
        Map<String,Object> param = new HashMap();
        long ts = System.currentTimeMillis();
        String userid = "350329";
        param.put("userid",userid);
        param.put("ts", ts);
        param.put("sign",SecureUtil.md5(userid+ts+"cb8fb12962f749a78998ca2940d148b9"));
        param.put("mobile",mobile);
        param.put("msgcontent",content);
        String response = HttpRequest.post("http://39.98.184.179:8081/api/sms/send").form(param).header("Content-Type","application/x-www-form-urlencoded").execute().body();
        JSONObject result = JSONObject.parseObject(response);
        log.info("赣安短信发送结果:{}",result);
        if(Objects.equals(result.getString("code"),"0")){
            return result.getJSONObject("data").getString("taskid");
        }
        log.error("赣安短信发送失败,mobile:{},result:{}",mobile, response);
        return "";
    }

    @Override
    public boolean updateLiuziSmsMsgId(Long id, String msgId) {
        if(StringUtils.isBlank(msgId)){
            //删除保存的发送记录
            liuziSmsSendRecordService.deleteById(id);
            return false;
        }
        //更新消息id
        LiuziSmsSendRecordEntity updateEntity = new LiuziSmsSendRecordEntity();
        updateEntity.setId(id);
        updateEntity.setMsgId(msgId);
        return liuziSmsSendRecordService.updateById(updateEntity);
    }

    private SmsTemplateListBO getSmsTemplateListBO(Integer page) {
        String tkey = (System.currentTimeMillis()/1000) +"";

        JSONObject param = new JSONObject();
        param.put("username","NHyx");
        param.put("password", SecureUtil.md5(SecureUtil.md5("nuohe2022DXAPI")+tkey));
        param.put("tkey",tkey);
        param.put("status","2");
        param.put("page",page);
        param.put("size",100);
        String response = HttpClientUtil.sendPostJson("https://api.mix2.zthysms.com/sms/v2/template/list", param.toString());
        SmsTemplateListBO smsTemplateListBO = JSON.parseObject(response, SmsTemplateListBO.class);
        return smsTemplateListBO;
    }
}
