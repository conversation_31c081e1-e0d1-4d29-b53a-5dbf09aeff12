package com.ruoyi.system.manager.account;

import com.ruoyi.system.bo.account.DataPermissionBo;

/**
 * 账号数据权限Manager
 *
 * <AUTHOR>
 * @date 2022/06/22
 */
public interface DataPermissionManager {

    /**
     * 查询媒体/广告主账号权限
     *
     * @return 账号数据权限BO
     */
    DataPermissionBo selectAccount();

    /**
     * 查询私域账号权限
     *
     * @return 账号数据权限BO
     */
    DataPermissionBo selectPrivateAccount();

    /**
     * 查询媒体权限
     *
     * @return 账号数据权限BO
     */
    DataPermissionBo selectApp();

    /**
     * 查询广告位权限
     *
     * @return 账号数据权限BO
     */
    DataPermissionBo selectSlot();

    /**
     * 查询广告权限
     *
     * @return 账号数据权限BO
     */
    DataPermissionBo selectAdvert();
}
