package com.ruoyi.system.manager.liuzi.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.PromotePageData;
import com.alipay.api.domain.PromotePageDetail;
import com.alipay.api.domain.PromotePagePropertyInstance;
import com.alipay.api.request.AlipayDataDataserviceAdPromotepageBatchqueryRequest;
import com.alipay.api.request.AlipayDataDataserviceAdPromotepageDownloadRequest;
import com.alipay.api.response.AlipayDataDataserviceAdPromotepageBatchqueryResponse;
import com.alipay.api.response.AlipayDataDataserviceAdPromotepageDownloadResponse;
import com.google.common.collect.Lists;
import com.ruoyi.common.config.AlipayProperties;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.QwFriendStatusEnum;
import com.ruoyi.common.enums.SmsChannelEnum;
import com.ruoyi.common.enums.common.SysConfigKeyEnum;
import com.ruoyi.common.enums.landpage.LiuziLandPageTypeEnum;
import com.ruoyi.common.enums.landpage.LiuziSmsConfigTypeEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.sms.LiuziSmsConfigBO;
import com.ruoyi.system.bo.sms.LiuziSmsQwTaskBo;
import com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord;
import com.ruoyi.system.entity.landpage.Landpage;
import com.ruoyi.system.entity.landpage.LandpageLiuziAlipayEntity;
import com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity;
import com.ruoyi.system.entity.sms.SmsTemplateEntity;
import com.ruoyi.system.entity.system.SysConfig;
import com.ruoyi.system.manager.liuzi.AsyncAlipayLandpageFormDataManager;
import com.ruoyi.system.manager.sms.LiuziSmsManager;
import com.ruoyi.system.manager.sms.QLV2PhoneManager;
import com.ruoyi.system.service.landpage.LandpageLibraryService;
import com.ruoyi.system.service.landpage.LandpageLiuziAlipayService;
import com.ruoyi.system.service.landpage.LiuziLandpageFormRecordService;
import com.ruoyi.system.service.sms.LiuziSmsSendRecordService;
import com.ruoyi.system.service.sms.SmsTemplateService;
import com.ruoyi.system.service.system.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/7 10:55
 */
@Slf4j
@Service
public class AsyncAlipayLandpageFormDataManagerImpl implements AsyncAlipayLandpageFormDataManager {

    private Long pageSize = 1000L;

    @Autowired
    private LandpageLiuziAlipayService landpageLiuziAlipayService;

    @Autowired
    private LiuziLandpageFormRecordService liuziLandpageFormRecordService;

    @Autowired
    private LiuziSmsManager liuziSmsManager;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private SmsTemplateService smsTemplateService;

    @Autowired
    private LiuziSmsSendRecordService liuziSmsSendRecordService;

    @Autowired
    private AlipayProperties alipayProperties;

    @Autowired
    private LandpageLibraryService landpageLibraryService;

    @Autowired
    private QLV2PhoneManager qlPhoneManager;

    @Autowired
    private RedisCache redisCache;

    @Override
    public void asyncAlipayData(Date curDate) {
        alipayProperties.getConfigs().forEach(config -> {
            AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", config.getAppId(), config.getPrivateKey(), "json", "UTF-8", config.getAlipayPublicKey(), "RSA2");
            List<Long> pageIds = asyncAndGetPageIds(alipayClient, 1,config.getBizToken(),config.getPrincipalTag());
            pageIds.forEach(pageId -> {
                asyncFormRecord(alipayClient, 1, pageId, curDate,config.getBizToken(),config.getPrincipalTag());
            });
        });
    }

    public void asyncFormRecord(AlipayClient alipayClient, Integer pageNo, Long pageId, Date curDate,String bizToken,String principalTag) {
        Long total = 1001L; //因为每次查1000条，所以设置1001，可以多查一次
        while (total > pageNo * pageSize) {
            try {
                AlipayDataDataserviceAdPromotepageDownloadResponse response = getFormRecord(alipayClient, pageNo++, pageId, curDate,bizToken,principalTag);
                if (Objects.nonNull(response) && response.isSuccess()) {
                    total = response.getTotal();
                    List<PromotePageData> list = response.getList();
                    //过滤已记录的表单
                    if (CollectionUtils.isEmpty(list)) {
                        continue;
                    }
                    List<String> orderIds = list.stream().map(PromotePageData::getBizNo).collect(Collectors.toList());
                    orderIds = liuziLandpageFormRecordService.selectOrderIdsByOrderIds(orderIds);
                    List<String> finalOrderIds = orderIds;
                    list.stream().filter(data -> !finalOrderIds.contains(data.getBizNo())).forEach(data -> {
                        // 落地页转化记录
                        List<PromotePagePropertyInstance> propertyList = data.getPropertyList();
                        Map<String, String> propertyMap = propertyList.stream().collect(Collectors.toMap(PromotePagePropertyInstance::getKey, PromotePagePropertyInstance::getValue));
                        LiuziLandpageFormRecord record = new LiuziLandpageFormRecord();
                        record.setPhone(propertyMap.get("phone"));
                        record.setName(propertyMap.get("name"));
                        record.setAdvertId(pageId);
                        record.setOrderId(data.getBizNo());
                        record.setConsumerId(0L); //外部留资数据没有用户id
                        record.setAppId(0L);
                        record.setSlotId(0L);
                        record.setIp("");
                        record.setReferer("");
                        record.setLandpageType(LiuziLandPageTypeEnum.ALIPAY.getType());
                        liuziLandpageFormRecordService.insertLandpageFormRecord(record);
                        SysConfig config = sysConfigService.selectByKey(SysConfigKeyEnum.LIUZI_SEND_QIWEI_FRIEND.getKey());
                        if(Objects.nonNull(config) && Objects.equals(config.getConfigValue(),"true")){
                            //加企微好友
                            addUserByQw(propertyMap.get("phone"));
                        }else{
                            //发送短信
                            sendSmsContent(LiuziLandPageTypeEnum.ALIPAY.getType(),propertyMap.get("phone"), pageId, record.getId(),null);
                        }
                    });
                } else {
                    log.error("同步支付宝推广页留资数据失败");
                }
            } catch (Exception e) {
                log.error("同步支付宝推广页留资数据异常,e:", e);
            }
        }
    }

    @Override
    public void sendSmsContent(Integer landpageType,String mobile, Long pageId, Long recordId,String landpageUrl) {
        if(!Validator.isMobile(mobile)){
            return;
        }
        LiuziSmsConfigBO.LiuziSmsConfigInfoBO smsConfig = getTpContent(landpageType,pageId,landpageUrl);
        if (null == smsConfig || StringUtils.isBlank(smsConfig.getContent())) {
            log.info("短信发送配置未开启");
            return;
        }
        String content = smsConfig.getContent();

        //根据模版id查询模版内容，提取出签名
        List<SmsTemplateEntity> contentList = smsTemplateService.selectListByContent(content);
        if (CollectionUtils.isEmpty(contentList)) {
            log.error("短信内容配置不存在,content:{}", content);
            return;
        }

        // 延迟处理企微加好友
        if (BooleanUtil.isTrue(smsConfig.getQwSwitch())) {
            LiuziSmsQwTaskBo taskBo = new LiuziSmsQwTaskBo();
            taskBo.setRecordId(recordId);
            taskBo.setPhone(mobile);
            taskBo.setCommitTime(new Date());
            redisCache.addCacheSet(EngineRedisKeyFactory.K071.toString(), JSON.toJSONString(taskBo));
        }

        //发送规则，优先级：传臻>赣安>枫雪云
        List<Integer> sendChannels = contentList.stream().map(SmsTemplateEntity::getType).collect(Collectors.toList());
        if(sendChannels.contains(SmsChannelEnum.CHUAN_ZHEN.getType())) {
            sendChannels = Lists.newArrayList(SmsChannelEnum.CHUAN_ZHEN.getType());
        }else if(sendChannels.contains(SmsChannelEnum.GAN_AN.getType())) {
            sendChannels = Lists.newArrayList(SmsChannelEnum.GAN_AN.getType());
        }else if(sendChannels.contains(SmsChannelEnum.FENG_XUE_YUN.getType())) {
            sendChannels = Lists.newArrayList(SmsChannelEnum.FENG_XUE_YUN.getType());
        }else{
            return;
        }
        List<Integer> finalSendChannels = sendChannels;
        List<LiuziSmsSendRecordEntity> entities = contentList.stream().filter(entity -> finalSendChannels.contains(entity.getType())).map(entity -> {
            //保存发送记录
            LiuziSmsSendRecordEntity recordEntity = new LiuziSmsSendRecordEntity();
            recordEntity.setContent(content);
            recordEntity.setLiuziRecordId(recordId);
            recordEntity.setTpId(entity.getTpId());
            recordEntity.setType(entity.getType());
            recordEntity.setMsgId("");
            liuziSmsSendRecordService.insert(recordEntity);
            return recordEntity;
        }).collect(Collectors.toList());


        if(CollectionUtils.isEmpty(entities)){
            return;
        }

        entities.forEach(entity -> {
            String msgId = "";
            if (Objects.equals(entity.getType(), SmsChannelEnum.ZHU_TONG.getType())) {
                msgId = liuziSmsManager.sendSmsZhuTong(mobile, entity.getTpId(), entity.getContent(), entity.getId());
            } else if (Objects.equals(entity.getType(), SmsChannelEnum.ZHANG_RONG.getType())) {
                msgId = liuziSmsManager.sendSmsZhangRong(mobile, entity.getContent());
            } else if (Objects.equals(entity.getType(), SmsChannelEnum.RUI_ZHUO.getType())) {
                msgId = liuziSmsManager.sendSmsRuiZhuo(mobile, entity.getContent());
            } else if (Objects.equals(entity.getType(), SmsChannelEnum.BAI_WU.getType())) {
                msgId = liuziSmsManager.sendSmsBaiWu(mobile, entity.getContent(),entity.getId());
            } else if (Objects.equals(entity.getType(), SmsChannelEnum.CHUAN_ZHEN.getType())) {
                msgId = liuziSmsManager.sendSmsChuanZhen(mobile, entity.getContent());
            } else if (Objects.equals(entity.getType(), SmsChannelEnum.GAN_AN.getType())) {
                msgId = liuziSmsManager.sendSmsGanAn(mobile, entity.getContent());
            } else if (Objects.equals(entity.getType(), SmsChannelEnum.FENG_XUE_YUN.getType())) {
                msgId = liuziSmsManager.sendSmsFengXueYun(mobile, entity.getContent());
            }

            liuziSmsManager.updateLiuziSmsMsgId(entity.getId(), msgId);
        });

    }

    @Override
    public void addUserByQw(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return;
        }

        // 加企微好友
        Integer code = qlPhoneManager.addExtUserByRule(Lists.newArrayList(mobile));
        if(Objects.nonNull(code)){
            // 更新发起加企微状态
            liuziLandpageFormRecordService.updateFriendStatus(mobile, QwFriendStatusEnum.APPLY.getStatus());
        }
    }

    /**
     * 解析出要发送的短信模版
     *
     * @param pageId
     * @return
     */
    private Long getTpId(Long pageId) {
        //查询短信配置
        SysConfig sysConfig = sysConfigService.selectByKey(SysConfigKeyEnum.LIUZI_SMS_CONFIG.getKey());
        if (Objects.isNull(sysConfig)) {
            return 0L;
        }
        LiuziSmsConfigBO liuziSmsConfigBO = JSONObject.parseObject(sysConfig.getConfigValue(), LiuziSmsConfigBO.class);
        if (Objects.isNull(liuziSmsConfigBO) || BooleanUtils.isNotTrue(liuziSmsConfigBO.getOpen())) {
            return 0L;
        }
        List<LiuziSmsConfigBO.LiuziSmsConfigInfoBO> config = liuziSmsConfigBO.getConfig();
        config = config.stream().filter(bo -> Objects.equals(bo.getLandpageType(), LiuziLandPageTypeEnum.ALIPAY.getType())).collect(Collectors.toList());
        Long defaultTpId = 0L;//配置里面必须会有默认配置
        Long tpId = 0L;
        for (LiuziSmsConfigBO.LiuziSmsConfigInfoBO bo : config) {
            if (Objects.equals(bo.getConfigType(), LiuziSmsConfigTypeEnum.DEFAULT.getType())) {
                defaultTpId = bo.getTpId();
            } else if (bo.getAdvertIds().contains(pageId)) {
                tpId = bo.getTpId();
            }
        }
        if (NumberUtils.isNullOrLteZero(tpId)) {
            tpId = defaultTpId;
        }
        return tpId;
    }

    /**
     * 解析出要发送的短信模版内容
     *
     * @param pageId
     * @return
     */
    private LiuziSmsConfigBO.LiuziSmsConfigInfoBO getTpContent(Integer landpageType,Long pageId,String landpageUrl) {
        if(Objects.equals(landpageType,LiuziLandPageTypeEnum.NUO_HE.getType())){
            Landpage landpage = landpageLibraryService.selectByUrl(landpageUrl);
            if(Objects.isNull(landpage)){
                return null;
            }
            pageId = landpage.getId();
        }
        //查询短信配置
        SysConfig sysConfig = sysConfigService.selectByKey(SysConfigKeyEnum.LIUZI_SMS_CONFIG.getKey());
        if (Objects.isNull(sysConfig)) {
            return null;
        }
        LiuziSmsConfigBO liuziSmsConfigBO = JSONObject.parseObject(sysConfig.getConfigValue(), LiuziSmsConfigBO.class);
        if (Objects.isNull(liuziSmsConfigBO) || BooleanUtils.isNotTrue(liuziSmsConfigBO.getOpen())) {
            return null;
        }
        List<LiuziSmsConfigBO.LiuziSmsConfigInfoBO> config = liuziSmsConfigBO.getConfig();
        config = config.stream().filter(bo -> Objects.equals(bo.getLandpageType(), landpageType)).collect(Collectors.toList());
        for (LiuziSmsConfigBO.LiuziSmsConfigInfoBO bo : config) {
            if (bo.getAdvertIds().contains(pageId)) {
                return bo;
            }
        }
        return null;
    }

    public AlipayDataDataserviceAdPromotepageDownloadResponse getFormRecord(AlipayClient alipayClient, Integer pageNo, Long pageId, Date curDate,String bizToken,String principalTag) {
        AlipayDataDataserviceAdPromotepageDownloadRequest request = new AlipayDataDataserviceAdPromotepageDownloadRequest();
        JSONObject adObj = new JSONObject();
        adObj.put("biz_token", bizToken);
        adObj.put("principal_tag", principalTag);
        adObj.put("promote_page_id", pageId);
        adObj.put("start_date", DateUtil.formatDate(curDate));
        adObj.put("end_date", DateUtil.formatDate(curDate));
        adObj.put("page_no", pageNo);
        adObj.put("page_size", pageSize);
        request.setBizContent(adObj.toJSONString());

        try {
            return alipayClient.execute(request);
        } catch (AlipayApiException e) {
            log.error("同步支付宝推广页留资数据异常,e:", e);
        }
        return null;

    }

    /**
     * 同步并获取所有推广页id列表
     *
     * @return
     */
    public List<Long> asyncAndGetPageIds(AlipayClient alipayClient, Integer pageNo,String bizToken,String principalTag) {
        Long total = pageSize + 1;
        List<Long> pageIds = new ArrayList<>();
        while (total > pageNo * pageSize) {
            try {
                AlipayDataDataserviceAdPromotepageBatchqueryResponse response = getAlipayPageIds(alipayClient, pageNo++,bizToken,principalTag);
                if (Objects.nonNull(response) && response.isSuccess()) {
                    total = response.getTotal();
                    List<PromotePageDetail> list = response.getList();
                    if(CollectionUtils.isEmpty(list)){
                        return pageIds;
                    }
                    List<LandpageLiuziAlipayEntity> updateLists = list.stream().map(page -> {

                        pageIds.add(page.getId());

                        LandpageLiuziAlipayEntity entity = new LandpageLiuziAlipayEntity();
                        entity.setLandpageId(page.getId());
                        entity.setLandpageName(page.getName());
                        return entity;
                    }).collect(Collectors.toList());
                    landpageLiuziAlipayService.batchInsertOrUpdate(updateLists);

                } else {
                    log.error("同步支付宝推广页失败");
                }
            } catch (Exception e) {
                log.error("同步支付宝推广页异常,e:", e);
            }
        }

        return pageIds;
    }

    /**
     * 获取支付宝推广页列表
     *
     * @param alipayClient
     * @param pageNo
     * @return
     */
    public AlipayDataDataserviceAdPromotepageBatchqueryResponse getAlipayPageIds(AlipayClient alipayClient, Integer pageNo,String bizToken,String principalTag) {
        AlipayDataDataserviceAdPromotepageBatchqueryRequest request = new AlipayDataDataserviceAdPromotepageBatchqueryRequest();
        JSONObject adObj = new JSONObject();
        adObj.put("biz_token", bizToken);
        adObj.put("principal_tag", principalTag);
//        adObj.put("type", "COLLECT_INFO");
        adObj.put("page_no", pageNo);
        adObj.put("page_size", 1000);
        request.setBizContent(adObj.toJSONString());
        try {
            return alipayClient.execute(request);
        } catch (AlipayApiException e) {
            log.error("同步支付宝推广页异常,e:", e);
        }
        return null;
    }
}
