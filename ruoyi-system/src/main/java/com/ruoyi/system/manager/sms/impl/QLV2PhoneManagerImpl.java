package com.ruoyi.system.manager.sms.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.SspRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.manager.sms.QLV2PhoneManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 圈量接口实现
 *
 * <AUTHOR>
 * @date 2023/7/10
 */
@Slf4j
@Service
public class QLV2PhoneManagerImpl implements QLV2PhoneManager {

    /**
     * 圈量AppKey
     */
    private static final String APP_KEY = "co4620b3fed19a481c";
    /**
     * 圈量AppSecret
     */
    private static final String APP_SECRET = "WgmB92dkEjd6D8EuDsDjW5tlw4LtiLK88WeMWSxtM2mG4HdLCrmB";
    /**
     * 圈量规则ID
     */
    private static final String RULE_ID = "qwhale_friends_auto_add_rule_02deef3675564705ae4532b36a30f3c1";
    /**
     * 圈量接口地址
     */
    private static final String API_HOST = "https://api.xunjinet.com.cn";

    @Autowired
    private RedisCache redisCache;

    @Override
    public String getAccessToken() {
        String key = SspRedisKeyFactory.K006.join(APP_KEY);
        String value = redisCache.getCacheObject(key);
        if (StringUtils.isNotBlank(value)) {
            return value;
        }

        JSONObject body = new JSONObject();
        body.put("app_key", APP_KEY);
        body.put("app_secret", APP_SECRET);
        String resp = HttpUtil.post(API_HOST + "/gateway/qopen/GetAccessToken", body.toString());
        JSONObject result = JSON.parseObject(resp);
        if (null != result && Objects.equals(result.getInteger("errcode"), 0) && result.containsKey("data")) {
            JSONObject data = result.getJSONObject("data");
            if (result.containsKey("data")) {
                data = data.getJSONObject("data");
                String accessToken = data.getString("access_token");
                if (StringUtils.isNotBlank(accessToken)) {
                    redisCache.setCacheObject(key, accessToken, data.getIntValue("expires_in"), TimeUnit.SECONDS);
                    return accessToken;
                }
            }
        }
        return null;
    }

    @Override
    public Integer addExtUserByRule(List<String> phones) {
        if (CollectionUtils.isEmpty(phones)) {
            return null;
        }

        JSONObject body = new JSONObject();
        body.put("rule_id", RULE_ID);
        body.put("ext_user_list", phones.stream().map(phone -> {
            JSONObject item = new JSONObject();
            item.put("mobile", phone);
            return item;
        }).collect(Collectors.toList()));

        String resp = HttpUtil.createPost(API_HOST + "/gateway/qopen/AddExtUserByRule")
                .header("Token", getAccessToken())
                .body(body.toString())
                .execute().body();
        log.info("圈量V2调用, AddExtUserByRule, req={}, resp={}", body, resp);
        JSONObject result = JSON.parseObject(resp);
        if (null != result) {
            Integer errcode = result.getInteger("errcode");
            if (Objects.equals(errcode, 0)) {
                return errcode;
            }
            log.error("圈量V2加好友接口异常, errcode:{}", errcode);
            return result.getInteger("errcode");
        }
        return null;
    }
}
