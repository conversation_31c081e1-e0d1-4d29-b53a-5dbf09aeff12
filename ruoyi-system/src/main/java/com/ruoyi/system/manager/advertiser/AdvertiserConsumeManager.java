package com.ruoyi.system.manager.advertiser;

import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeBo;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeDetailRecordEntity;

import java.util.Date;

/**
 * 广告主消费Manager
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
public interface AdvertiserConsumeManager {

    /**
     * 消费
     *
     * @param consumeBo 参数
     */
    void consume(AdvertiserConsumeBo consumeBo);

    /**
     * 消费重试
     *
     * @param detailRecord 消费记录明细
     */
    void consumeRetry(AdvertiserConsumeDetailRecordEntity detailRecord);

    /**
     * CPC消费
     *
     * @param curDate 日期
     * @param accountId 广告主ID
     * @param advertId 广告ID
     * @param consumeAmount 消费金额(分)
     * @return 是否成功
     */
    boolean cpcConsume(Date curDate, Long accountId, Long advertId, Integer consumeAmount);
}
