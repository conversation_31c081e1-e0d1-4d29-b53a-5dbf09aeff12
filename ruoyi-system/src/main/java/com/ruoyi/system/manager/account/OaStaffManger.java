package com.ruoyi.system.manager.account;

import com.ruoyi.system.bo.account.OaStaffBo;

import java.util.List;
import java.util.Map;

/**
 * OA员工Manager接口
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
public interface OaStaffManger {

    /**
     * 根据邮箱查询OA员工信息
     *
     * @param email 邮箱
     * @return OA员工信息
     */
    OaStaffBo selectByEmail(String email);

    /**
     * 根据邮箱批量查询OA员工部门
     *
     * @param emails 邮箱列表
     * @return 邮箱-部门Key映射
     */
    Map<String, String> selectIdDepartmentMapByEmails(List<String> emails);
    /**
     * 根据邮箱批量查询oa员工职位
     */
    Map<String, String> selectIdPostMapByEmails(List<String> emails);

    /**
     * 根据用户手机号获取邮箱
     * @param phone
     * @return
     */
    String selectEmailByPhone(String phone);

    /**
     * 查询部门下的员工邮箱列表
     *
     * @param departmentKeys 部门Key列表
     * @return 员工邮箱列表
     */
    List<String> selectUserIdsByDepartmentKey(List<String> departmentKeys);
}
