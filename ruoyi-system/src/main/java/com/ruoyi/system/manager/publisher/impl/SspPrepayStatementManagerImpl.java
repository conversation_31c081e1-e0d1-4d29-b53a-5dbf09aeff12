package com.ruoyi.system.manager.publisher.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.manager.publisher.SspPrepayStatementManager;
import com.ruoyi.system.mapper.appdata.AppMonthDataMapper;
import com.ruoyi.system.req.datashow.AppMonthDataReq;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.qualification.AccountQualificationService;
import com.ruoyi.system.vo.qualification.QualificationInfoVO;
import com.ruoyi.system.vo.ssp.finance.PrepayStatementInfoVO;
import com.ruoyi.system.vo.ssp.finance.PrepayStatementItemVO;
import com.ruoyi.system.vo.ssp.finance.PrepayStatementVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.ruoyi.common.enums.publisher.PayType.PREPAY;

/**
 * 媒体预付款结算单Manager接口实现
 *
 * <AUTHOR>
 * @date 2022/07/29
 */
@Service
public class SspPrepayStatementManagerImpl implements SspPrepayStatementManager {

    @Autowired
    private AppMonthDataMapper appMonthDataMapper;

    @Autowired
    private AppService appService;

    @Autowired
    private AccountQualificationService accountQualificationService;

    @Override
    public PageInfo<PrepayStatementVO> selectPrepayStatementList(Long accountId) {
        if (null == accountId) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        TableSupport.startPage();
        AppMonthDataReq req = new AppMonthDataReq();
        req.setPayType(PREPAY.getType());
        req.setAccountIds(Collections.singletonList(accountId));
        List<AppMonthDataEntity> list = appMonthDataMapper.selectAppMonthList(req);
        if (CollectionUtils.isEmpty(list)) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }
        return PageInfoUtils.dto2Vo(list, data -> {
            PrepayStatementVO statement = BeanUtil.copyProperties(data, PrepayStatementVO.class);
            statement.setStatementId(data.getId());
            return statement;
        });
    }

    @Override
    public PrepayStatementInfoVO getPrepayStatementInfo(Long accountId, Long statementId) {
        if (null == accountId || null == statementId) {
            return null;
        }
        AppMonthDataEntity appMonthData = appMonthDataMapper.selectById(statementId);
        if (null == appMonthData || !Objects.equals(appMonthData.getAccountId(), accountId)) {
            return null;
        }

        PrepayStatementItemVO item = new PrepayStatementItemVO();
        item.setMonthDate(appMonthData.getMonthDate());
        item.setAppId(appMonthData.getAppId());
        item.setAppName(appService.selectAppNameById(appMonthData.getAppId()));
        item.setAppRevenue(appMonthData.getAppRevenue());

        PrepayStatementInfoVO info = new PrepayStatementInfoVO();
        info.setMonth(appMonthData.getMonthDate() % 100);
        info.setAppRevenue(appMonthData.getAppRevenue());
        // 预付款信息
        info.setPayType(appMonthData.getPayType());
        info.setPrepayAmount(appMonthData.getPrepayAmount());
        // 媒体资质信息
        QualificationInfoVO qualification = accountQualificationService.selectQualificationInfoByAccountId(accountId);
        info.setQualificationInfo(qualification);
        // 结算单明细
        info.setItems(Collections.singletonList(item));
        return info;
    }
}
