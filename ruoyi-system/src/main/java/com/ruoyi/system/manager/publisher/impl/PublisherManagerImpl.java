package com.ruoyi.system.manager.publisher.impl;

import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.system.entity.account.finance.AccountRevenueEntity;
import com.ruoyi.system.entity.qualification.AccountQualificationEntity;
import com.ruoyi.system.manager.publisher.PublisherManager;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.finance.WithdrawRecordService;
import com.ruoyi.system.service.publisher.prepay.AccountPrepayApplyRecordService;
import com.ruoyi.system.service.publisher.prepay.AccountPrepayStatementRecordService;
import com.ruoyi.system.service.finance.AccountRevenueService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.qualification.AccountQualificationService;
import com.ruoyi.system.vo.publisher.prepay.PublisherPrepayVO;
import com.ruoyi.system.vo.publisher.PublisherVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.publisher.PayType.isPrepay;
import static com.ruoyi.common.enums.publisher.PrepayAuditStatus.IN_AUDIT;

/**
 * 媒体Manager接口实现
 *
 * <AUTHOR>
 * @date 2022/07/29
 */
@Service
public class PublisherManagerImpl implements PublisherManager {

    @Autowired
    private AccountService accountService;

    @Autowired
    private AccountRevenueService accountRevenueService;

    @Autowired
    private AccountQualificationService accountQualificationService;

    @Autowired
    private AccountPrepayApplyRecordService accountPrepayApplyRecordService;

    @Autowired
    private AccountPrepayStatementRecordService accountPrepayStatementRecordService;

    @Autowired
    private AppMonthDataService appMonthDataService;

    @Autowired
    private WithdrawRecordService withdrawRecordService;

    @Autowired
    private SlotDataService slotDataService;

    @Override
    public PublisherVO getInfo(Long accountId) {
        PublisherVO publisher = new PublisherVO();
        publisher.setAccountId(accountId);
        publisher.setPayType(0);
        // 账号信息
        Optional.ofNullable(accountService.selectAccountById(accountId)).ifPresent(account -> {
            publisher.setCompanyName(account.getCompanyName());
            publisher.setEmail(account.getEmail());
        });
        // 预付款信息
        Optional.ofNullable(accountRevenueService.selectByAccountId(accountId)).ifPresent(account -> {
            publisher.setPayType(account.getPayType());
        });
        return publisher;
    }

    @Override
    public List<PublisherPrepayVO> getPrepayPublisher() {
        List<AccountRevenueEntity> accountRevenueList = accountRevenueService.selectPrepayAccountRevenue();
        if (CollectionUtils.isEmpty(accountRevenueList)) {
            return Collections.emptyList();
        }
        List<Long> accountIds = ListUtils.mapToList(accountRevenueList, AccountRevenueEntity::getAccountId);
        Map<Long, Integer> prepayStatementAmountMap = accountPrepayStatementRecordService.sumAmountByAccountIds(accountIds);
        return accountRevenueList.stream().map(accountRevenue -> {
            Long accountId = accountRevenue.getAccountId();
            PublisherPrepayVO publisherPrepay = new PublisherPrepayVO();
            publisherPrepay.setAccountId(accountId);
            publisherPrepay.setPrepayAmount(accountRevenue.getPrepayAmount() - prepayStatementAmountMap.getOrDefault(accountId, 0));
            return publisherPrepay;
        }).collect(Collectors.toList());
    }

    @Override
    public List<PublisherPrepayVO> getPrepayPublisherForApply(List<Long> accountIds) {
        List<AccountRevenueEntity> accountRevenueList = accountRevenueService.selectPrepayAccountRevenue(accountIds);
        if (CollectionUtils.isEmpty(accountRevenueList)) {
            return Collections.emptyList();
        }
        accountIds = ListUtils.mapToList(accountRevenueList, AccountRevenueEntity::getAccountId);
        Map<Long, String> companyNameMap = accountService.selectCompanyNameMap(accountIds);
        Map<Long, Long> appRevenueMap = slotDataService.sumAppRevenueByAccountId(accountIds);
        Map<Long, Integer> withdrawAmountMap = withdrawRecordService.sumWithdrawAmountByAccountId(accountIds);
        Map<Long, AccountQualificationEntity> qualificationMap = accountQualificationService.selectMapByAccountIds(accountIds);
        return accountRevenueList.stream().map(accountRevenue -> {
            Long accountId = accountRevenue.getAccountId();
            PublisherPrepayVO publisherPrepay = new PublisherPrepayVO();
            publisherPrepay.setAccountId(accountId);
            publisherPrepay.setCompanyName(companyNameMap.get(accountId));
            publisherPrepay.setPrepayAmount(Math.max(accountRevenue.getPrepayAmount() - appRevenueMap.getOrDefault(accountId, 0L).intValue() + withdrawAmountMap.getOrDefault(accountId, 0), 0));
            Optional.ofNullable(qualificationMap.get(accountId)).ifPresent(qualification -> {
                publisherPrepay.setBankAccount(qualification.getBankAccount());
                publisherPrepay.setBankName(qualification.getBankName());
                publisherPrepay.setBankAccountName(qualification.getBankAccountName());
            });
            return publisherPrepay;
        }).collect(Collectors.toList());
    }

    @Override
    public int updatePayType(Long accountId, Integer payType) {
        AccountRevenueEntity revenue = accountRevenueService.selectByAccountId(accountId);
        if (null != revenue && isPrepay(revenue.getPayType())) {
            Long prepayDebtAmount = calculatePrepayDebtAmount(accountId, revenue.getPrepayAmount());
            if (prepayDebtAmount > 0) {
                throw new CustomException("该账号的欠款金额大于0，不能修改为后付款媒体哦");
            }
            int applyCount = accountPrepayApplyRecordService.countByAccountIdAndAuditStatus(accountId, IN_AUDIT.getStatus());
            if (applyCount > 0) {
                throw new CustomException("该账号有预付款申请待审核，不能修改为后付款媒体哦");
            }
            int noConfirmStatementCount = appMonthDataService.countNoConfirmByAccountId(accountId);
            if (noConfirmStatementCount > 0) {
                throw new CustomException("该账号有未确认的结算单，不能修改为后付款媒体哦");
            }
        }
        return accountRevenueService.updatePayType(accountId, payType);
    }

    @Override
    public Long calculatePrepayDebtAmount(Long accountId, Long prepayAmount) {
        // 查询预付款累计结算金额
        Integer statementAmount = accountPrepayStatementRecordService.sumAmountByAccountId(accountId);
        // 预付款欠款金额 = 预付款金额 - 预付款累计结算金额
        return prepayAmount - statementAmount;
    }
}
