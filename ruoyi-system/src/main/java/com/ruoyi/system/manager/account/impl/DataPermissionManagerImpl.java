package com.ruoyi.system.manager.account.impl;

import com.google.common.collect.Lists;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.account.OaStaffBo;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.manager.account.OaStaffManger;
import com.ruoyi.system.mapper.manager.AdvertMapper;
import com.ruoyi.system.mapper.manager.AppMapper;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.AccountService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.ruoyi.common.constant.OaConstants.PERMISSION_DEPARTMENT_DATA;
import static com.ruoyi.common.constant.OaConstants.PERMISSION_TOTAL_ACCOUNT;
import static com.ruoyi.common.constant.OaConstants.PERMISSION_TOTAL_PRIVATE_ACCOUNT;
import static com.ruoyi.common.enums.account.AccountMainType.isCrmUser;
import static com.ruoyi.common.enums.account.DataPermissionType.PARTIAL;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;
import static com.ruoyi.system.bo.account.DataPermissionBo.FULL_PERMISSION;
import static com.ruoyi.system.bo.account.DataPermissionBo.NONE_PERMISSION;

@Service
public class DataPermissionManagerImpl implements DataPermissionManager {

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private AppMapper appMapper;

    @Autowired
    private AdvertMapper advertMapper;

    @Autowired
    private AccountRelationService accountRelationService;

    @Autowired
    private OaStaffManger oaStaffManger;

    @Autowired
    private AccountService accountService;


    @Override
    public DataPermissionBo selectAccount() {
        LoginUser user = SecurityUtils.getLoginUser();
        // 非CRM用户无权限
        if (!isCrmUser(user.getMainType())) {
            return NONE_PERMISSION;
        }
        // 查询OA员工信息
        OaStaffBo staff = oaStaffManger.selectByEmail(user.getEmail());
        if (null == staff || null == staff.getDepartment() || null == staff.getPost()) {
            return NONE_PERMISSION;
        }
        // 超级管理员有全部权限
        if (user.isAdmin()) {
            return FULL_PERMISSION;
        }
        // OA自定义权限-广告媒体账号数据
        if (staff.getOaPermissionKeys().contains(PERMISSION_TOTAL_ACCOUNT)) {
            return FULL_PERMISSION;
        }
        // OA自定义权限-私域账号数据
        List<Long> accountIds = new ArrayList<>();
        if (staff.getOaPermissionKeys().contains(PERMISSION_TOTAL_PRIVATE_ACCOUNT)) {
            accountIds.addAll(accountService.selectPrivateAccountIds());
            accountIds.addAll(accountService.selectSspPrivateAccountIds());
        }

        // 除了有部门数据权限, 其他人只有自己的权限
        List<Long> userIds = new ArrayList<>();
        userIds.add(user.getCrmAccountId());
        // 部门数据权限判断
        if (staff.getOaPermissionKeys().contains(PERMISSION_DEPARTMENT_DATA)) {
            userIds.addAll(selectUserIdsByDepartment(Collections.singletonList(staff.getDepartment().getDepartmentKey())));
        }
        // 查询员工所负责的媒体/广告主的权限
        accountIds.addAll(accountRelationService.selectBySrcAccountIds(userIds));
        return new DataPermissionBo(PARTIAL.getType(), accountIds);
    }

    @Override
    public DataPermissionBo selectPrivateAccount() {
        LoginUser user = SecurityUtils.getLoginUser();
        // 非CRM用户无权限
        if (!isCrmUser(user.getMainType())) {
            return NONE_PERMISSION;
        }
        // 查询OA员工信息
        OaStaffBo staff = oaStaffManger.selectByEmail(user.getEmail());
        if (null == staff || null == staff.getDepartment() || null == staff.getPost()) {
            return NONE_PERMISSION;
        }
        // 超级管理员有全部权限
        if (user.isAdmin()) {
            return FULL_PERMISSION;
        }
        // OA自定义权限
        if (staff.getOaPermissionKeys().contains(PERMISSION_TOTAL_PRIVATE_ACCOUNT)) {
            return FULL_PERMISSION;
        }
        // 除了有部门数据权限, 其他人只有自己的权限
        List<Long> userIds = new ArrayList<>();
        userIds.add(user.getCrmAccountId());
        // 部门数据权限判断
        if (staff.getOaPermissionKeys().contains(PERMISSION_DEPARTMENT_DATA)) {
            userIds.addAll(selectUserIdsByDepartment(Collections.singletonList(staff.getDepartment().getDepartmentKey())));
        }
        // 员工只有所负责的媒体/广告主的权限
        List<Long> accountIds = accountRelationService.selectBySrcAccountIds(userIds);
        return new DataPermissionBo(PARTIAL.getType(), accountIds);
    }

    @Override
    public DataPermissionBo selectApp() {
        DataPermissionBo permission = selectAccount();
        if (hasPartialPermission(permission.getType()) && CollectionUtils.isNotEmpty(permission.getValues())) {
            List<Long> appIds = appMapper.selectAppIdByAccountIds(permission.getValues());
            return new DataPermissionBo(PARTIAL.getType(), appIds);
        }
        return permission;
    }

    @Override
    public DataPermissionBo selectSlot() {
        DataPermissionBo permission = selectAccount();
        if (hasPartialPermission(permission.getType()) && CollectionUtils.isNotEmpty(permission.getValues())) {
            List<Long> slotIds = slotMapper.selectSlotIdByAccountIds(permission.getValues());
            return new DataPermissionBo(PARTIAL.getType(), slotIds);
        }
        return permission;
    }

    @Override
    public DataPermissionBo selectAdvert() {
        DataPermissionBo permission = selectAccount();
        if (hasPartialPermission(permission.getType()) && CollectionUtils.isNotEmpty(permission.getValues())) {
            List<Long> advertIds = advertMapper.selectAdvertIdByAdvertiserIds(permission.getValues());
            return new DataPermissionBo(PARTIAL.getType(), advertIds);
        }
        return permission;
    }

    /**
     * 查询部门下的员工ID列表
     *
     * @param departmentKeys 部门Key列表
     * @return 员工ID列表
     */
    private List<Long> selectUserIdsByDepartment(List<String> departmentKeys) {
        if (CollectionUtils.isEmpty(departmentKeys)) {
            return Collections.emptyList();
        }
        List<String> emails = oaStaffManger.selectUserIdsByDepartmentKey(departmentKeys);
        return accountService.selectCrmUserIdsByEmail(emails);
    }
}
