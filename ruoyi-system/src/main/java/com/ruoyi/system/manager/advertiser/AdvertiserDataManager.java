package com.ruoyi.system.manager.advertiser;

import com.github.pagehelper.PageInfo;
import com.ruoyi.system.req.advertiser.data.CrmAdvertiserCpcDataReq;
import com.ruoyi.system.req.advertiser.AdvertiserCpcDataReq;
import com.ruoyi.system.vo.advertiser.finance.CrmAdvertiserCpcDataListVO;
import com.ruoyi.system.vo.advertiser.finance.WisAdvertiserCpcDataListVO;

/**
 * 广告主数据相关manager
 *
 * <AUTHOR>
 * @date 2022/4/7 7:53 下午
 */
public interface AdvertiserDataManager {

    /**
     * 获取广告主cpc数据(日期维度)
     *
     * @param req 请求参数
     * @param isExport 是否是导出
     * @return 广告主cpc数据列表
     */
    PageInfo<WisAdvertiserCpcDataListVO> selectCpcDataList(AdvertiserCpcDataReq req,Boolean isExport);

    /**
     * 获取广告主cpc数据(落地页维度)
     *
     * @param req 请求参数
     * @param isExport 是否是导出
     * @return 广告主cpc数据列表
     */
    PageInfo<WisAdvertiserCpcDataListVO> selectCpcLandpageDataList(AdvertiserCpcDataReq req, Boolean isExport);

    /**
     * 批量获取广告主cpc数据(日期维度)
     *
     * @param req 请求参数
     * @param isExport 是否是导出
     * @return 广告主cpc数据列表
     */
    PageInfo<CrmAdvertiserCpcDataListVO> batchSelectCpcDataList(CrmAdvertiserCpcDataReq req, Boolean isExport);

    /**
     * 批量获取广告主cpc数据(落地页维度)
     *
     * @param req 请求参数
     * @param isExport 是否是导出
     * @return 广告主cpc数据列表
     */
    PageInfo<CrmAdvertiserCpcDataListVO> batchSelectCpcLandpageDataList(CrmAdvertiserCpcDataReq req, Boolean isExport);
}
