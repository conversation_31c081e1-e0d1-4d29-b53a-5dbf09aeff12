package com.ruoyi.system.manager.account.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.account.AccountPermissionBo;
import com.ruoyi.system.entity.oa.permission.PostPermissionEntity;
import com.ruoyi.system.entity.oa.permission.StaffPermissionEntity;
import com.ruoyi.system.entity.oa.staff.StaffInfoEntity;
import com.ruoyi.system.entity.permission.PermissionEntity;
import com.ruoyi.system.manager.account.AccountPermissionManager;
import com.ruoyi.system.service.oa.permission.PostPermissionService;
import com.ruoyi.system.service.oa.permission.StaffPermissionService;
import com.ruoyi.system.service.oa.staff.StaffInfoService;
import com.ruoyi.system.service.oa.user.UserService;
import com.ruoyi.system.service.permission.SspPermissionService;
import com.ruoyi.system.service.permission.PostPermissionRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 账号权限Manager接口实现
 *
 * <AUTHOR>
 * @date 2022/6/23 3:28 下午
 */
@Service
public class AccountPermissionManagerImpl implements AccountPermissionManager {

    @Autowired
    private UserService userService;

    @Autowired
    private StaffInfoService staffInfoService;

    @Autowired
    private PostPermissionRelationService postPermissionRelationService;

    @Autowired
    private SspPermissionService sspPermissionService;

    @Autowired
    private PostPermissionService postPermissionService;

    @Autowired
    private StaffPermissionService staffPermissionService;

    @Autowired
    private com.ruoyi.system.service.oa.permission.PermissionService oaPermissionService;

    @Override
    public AccountPermissionBo selectByCurAccount() {
        AccountPermissionBo bo = new AccountPermissionBo();
        bo.setAdminType(SecurityUtils.getLoginUser().getAdminType());
        if(SecurityUtils.isAdmin()){
            //管理员拥有全部权限，直接返回
            return bo;
        }
        String email = SecurityUtils.getLoginUser().getEmail();
        bo.setPermissionList(getPermissionList(email));
        return bo;
    }

    /**
     * 获取职位对应的权限列表
     *
     * @param postId 职位ID
     * @return 权限列表
     */
    private Set<String> getPostPermissionList(Long postId) {
        List<Long> permissionIds = postPermissionRelationService.selectPermissionIdsByPostId(postId);
        List<PermissionEntity> permissions = sspPermissionService.selectByIds(permissionIds);
        return permissions.stream().map(PermissionEntity::getPermissionKey).collect(Collectors.toSet());
    }

    /**
     * 获取用户的所有权限列表
     */
    private Set<String> getPermissionList(String email){
        Long userId = userService.selectUserIdByEmail(email);
        StaffInfoEntity staff = staffInfoService.selectByUserId(userId);
        if(Objects.isNull(staff)){
            return Collections.emptySet();
        }
        Long staffId = staff.getId();


        Set<String> voList = new HashSet<>();
        // 查询职位权限
        PostPermissionEntity postPermission = postPermissionService.selectByPostIdAndSystemId(staff.getPostId(), 1L);
        if(Objects.nonNull(postPermission)){
            voList.addAll(getPermissionKeyList(postPermission.getPermissions()));
        }
        // 查询职员权限
        StaffPermissionEntity staffPermissionEntity = staffPermissionService.selectByStaffIdAndSystemId(staffId, 1L);
        if(Objects.nonNull(staffPermissionEntity)){
            voList.addAll(getPermissionKeyList(staffPermissionEntity.getPermissions()));
        }
        return voList;
    }

    private Set<String> getPermissionKeyList(String permissions){
        if (!JSONUtil.isTypeJSONArray(permissions)) {
            return new HashSet<>();
        }
        List<Long> permissionIds = JSONArray.parseArray(permissions, Long.class);
        return oaPermissionService.selectPermissionKeyByIds(permissionIds);
    }
}
