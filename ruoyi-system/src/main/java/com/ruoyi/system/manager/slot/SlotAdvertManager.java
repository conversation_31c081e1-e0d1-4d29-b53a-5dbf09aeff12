package com.ruoyi.system.manager.slot;

import com.ruoyi.system.vo.slot.SlotAdvertServeVO;
import com.ruoyi.system.vo.slot.SlotAdvertVO;

import java.util.List;
import java.util.Map;

/**
 * 广告位广告Manager接口
 *
 * <AUTHOR>
 * @date 2022/05/10
 */
public interface SlotAdvertManager {

    /**
     * 查询广告位能投放的广告
     *
     * @param slotId 广告位ID
     * @param canServe 是否可投放
     * @return 广告列表
     */
    List<SlotAdvertVO> queryAdvertBySlot(Long slotId, Integer canServe);

    /**
     * 查询广告位的广告配置可投放状态
     *
     * @param slotId 广告位ID
     * @param orientIds 配置ID列表
     * @return 配置ID-可投状态映射
     */
    Map<Long, SlotAdvertServeVO> queryAdvertOrientServeMap(Long slotId, List<Long> orientIds);
}
