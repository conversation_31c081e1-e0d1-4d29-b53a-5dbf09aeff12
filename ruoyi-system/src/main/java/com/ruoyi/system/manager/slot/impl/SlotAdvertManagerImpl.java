package com.ruoyi.system.manager.slot.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.enums.advert.AdvertCategory;
import com.ruoyi.common.enums.advert.AdvertStatusEnum;
import com.ruoyi.common.enums.advert.LandpageTypeEnum;
import com.ruoyi.common.enums.advert.ServingHourEnum;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.system.bo.advert.AdvertExtInfo;
import com.ruoyi.system.bo.data.OrderDataBo;
import com.ruoyi.system.domain.advert.AdvertBannedApp;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.manager.slot.SlotAdvertManager;
import com.ruoyi.system.mapper.advert.AdvertBannedAppMapper;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.service.advertiser.AdvertiserBudgetService;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.advert.AdvertService;
import com.ruoyi.system.service.engine.cache.SlotDataCacheService;
import com.ruoyi.system.service.traffic.TrafficPackageService;
import com.ruoyi.system.vo.slot.SlotAdvertServeVO;
import com.ruoyi.system.vo.slot.SlotAdvertVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.advert.AdvertStatusEnum.isAdvertValid;

/**
 * 广告位广告Manager接口实现
 *
 * <AUTHOR>
 * @date 2022/05/10
 */
@Service
public class SlotAdvertManagerImpl implements SlotAdvertManager {

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    @Autowired
    private AdvertBannedAppMapper advertBannedAppMapper;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private TrafficPackageService trafficPackageService;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Autowired
    private AdvertiserBudgetService advertiserBudgetService;

    @Autowired
    private SlotDataCacheService slotDataCacheService;

    @Override
    public List<SlotAdvertVO> queryAdvertBySlot(Long slotId, Integer canServe) {
        // 查询广告位
        Slot slot = slotMapper.selectSlotById(slotId);
        if (null == slot) {
            return Collections.emptyList();
        }

        // 查询配置列表
        AdvertOrientation param = new AdvertOrientation();
        if (Objects.equals(canServe, 1)) {
            param.setServingSwitch(1);
        }
        List<AdvertOrientation> orients = advertOrientationService.selectAdvertOrientationList(param);
        List<Long> orientIds = ListUtils.mapToList(orients, AdvertOrientation::getId);

        // 屏蔽了媒体的配置ID集合
        Set<Long> bannedOrientSet = advertBannedAppMapper.selectByAppId(slot.getAppId()).stream().map(AdvertBannedApp::getOrientId).collect(Collectors.toSet());
        // 查询广告定向媒体映射
        Map<Long, Set<Long>> orientAppMap = advertOrientationService.selectAdvertOrientAppMapByOrientIds(orientIds);
        // 查询广告定向广告位映射
        Map<Long, Set<Long>> orientSlotMap = advertOrientationService.selectAdvertOrientSlotMapByOrientIds(orientIds);
        // 查询流量包定向
        Map<Long, Set<Long>> trafficMap = trafficPackageService.selectTrafficSlotByOrientIds(orientIds);

        // 过滤屏蔽定向的配置
        orients = orients.stream()
                .filter(orient -> !bannedOrientSet.contains(orient.getId()))
                .filter(orient -> (CollectionUtils.isEmpty(trafficMap.get(orient.getId())) && CollectionUtils.isEmpty(orientAppMap.get(orient.getId())))
                        || CollUtil.contains(trafficMap.get(orient.getId()), slot.getId())
                        || (CollUtil.contains(orientAppMap.get(orient.getId()), slot.getAppId()) && CollectionUtils.isEmpty(orientSlotMap.get(orient.getId())))
                        || CollUtil.contains(orientSlotMap.get(orient.getId()), slot.getId()))
                .collect(Collectors.toList());

        // 查询广告
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(ListUtils.mapToList(orients, AdvertOrientation::getAdvertId));

        // 查询广告主余额预算
        List<Long> advertiserIds = advertMap.values().stream().map(Advert::getAdvertiserId).distinct().collect(Collectors.toList());
        Map<Long, Integer> advertiserBalanceMap = advertiserBalanceService.selectAdvertiserBalanceMap(advertiserIds);
        Map<Long, Integer> consumeOffsetMap = dspAdvertiserConsumeRecordService.batchConsumeOffsetForCrm(advertiserIds);
        Map<Long, Long> advertiserBudgetMap = advertiserBudgetService.selectBudgetMapByAccountIds(advertiserIds);

        // 构造接口返回结果
        Date today = DateUtil.beginOfDay(new Date());
        Integer hour = DateUtil.thisHour(true);
        return orients.stream()
                .filter(orient -> advertMap.containsKey(orient.getAdvertId()))
                .map(orient -> {
                    SlotAdvertVO advertVO = new SlotAdvertVO();
                    advertVO.setOrientId(orient.getId());
                    advertVO.setOrientName(orient.getOrientName());
                    advertVO.setWeight(orient.getWeight());
                    advertVO.setOrderFactor(orient.getWeight() * orient.getUnitPrice());

                    Advert advert = advertMap.get(orient.getAdvertId());
                    advertVO.setAdvertId(advert.getId());
                    advertVO.setAdvertName(advert.getAdvertName());
                    advertVO.setUnitPrice(orient.getUnitPrice());
                    advertVO.setMilliUnitPrice(orient.getMilliUnitPrice());
                    advertVO.setAssessCost(advert.getAssessCost());
                    if (LandpageTypeEnum.isCustom(orient.getLandpageType())) {
                        advertVO.setLandpageUrl(orient.getLandpageUrl());
                    } else {
                        advertVO.setLandpageUrl(advert.getLandpageUrl());
                    }
                    if (Objects.equals(AdvertCategory.MINI_PROGRAM.getType(), advert.getAdvertCategory())) {
                        AdvertExtInfo extInfo = JSON.parseObject(advert.getExtInfo(), AdvertExtInfo.class);
                        Optional.ofNullable(extInfo).ifPresent(ext -> advertVO.setLandpageUrl("小程序 AppId:" + ext.getAppId() + ", AppPath:" + ext.getAppPath()));
                    }

                    // 广告主余额
                    Integer advertiserBalance = advertiserBalanceMap.get(advert.getAdvertiserId());
                    if (null != advertiserBalance && consumeOffsetMap.containsKey(advert.getAdvertiserId())) {
                        advertiserBalance -= consumeOffsetMap.get(advert.getAdvertiserId());
                    }
                    // 广告主预算
                    Long advertiserBudget = advertiserBudgetMap.get(advert.getAdvertiserId());

                    // 判断广告是否正常投放
                    advertVO.setCanServe(0);
                    if (SwitchStatusEnum.isSwitchOff(advert.getServingSwitch())) {
                        advertVO.setReason("广告开关关闭");
                    } else if (SwitchStatusEnum.isSwitchOff(orient.getServingSwitch())) {
                        advertVO.setReason("广告定向配置开关关闭");
                    } else if (null != advertiserBudget && advertiserBudget <= 0) {
                        advertVO.setReason("广告主日预算为0");
                    } else if (null != advert.getDailyBudget() && advert.getDailyBudget() <= 0) {
                        advertVO.setReason("广告日预算为0");
                    } else if (null != orient.getDailyBudget() && orient.getDailyBudget() <= 0) {
                        advertVO.setReason("广告定向配置日预算为0");
                    } else if (today.before(advert.getStartServingDate()) || today.after(DateUtil.endOfDay(advert.getStopServingDate()))) {
                        advertVO.setReason("当前不在广告投放周期内");
                    } else if (!ServingHourEnum.contains(orient.getServingHour(), hour)) {
                        advertVO.setReason("当前不在广告投放时段内");
                    } else if (null == advertiserBalance || advertiserBalance < orient.getUnitPrice()) {
                        advertVO.setReason("广告主余额不足");
                    } else if (!isAdvertValid(advert.getAdvertStatus())) {
                        advertVO.setReason(AdvertStatusEnum.getDescByStatus(advert.getAdvertStatus()));
                    } else {
                        advertVO.setCanServe(1);
                        advertVO.setReason("");
                    }
                    return advertVO;
                }).filter(advert -> null == canServe || Objects.equals(canServe, advert.getCanServe()))
                .peek(advert -> {
                    if (Objects.equals(canServe, 1)) {
                        // 预估arpu = 预估广告出价 × CTR
                        // 预估广告出价 = max(CVR * 考核成本, 广告出价)
                        OrderDataBo ctrCvr = slotDataCacheService.getSlotAdvertCtrCvrCache(slotId, advert.getAdvertId(), 1, "", "");
                        advert.setPCpc(null != ctrCvr ? ctrCvr.getCvr() * advert.getAssessCost() / 100 : 0);
                        advert.setCtr(null != ctrCvr ? ctrCvr.getCtr() : 0);
                        advert.setArpu(advert.getCtr() * Math.max(advert.getUnitPrice() / 100.0, advert.getPCpc()));
                    }
                })
                .sorted(Comparator.comparing(SlotAdvertVO::getOrderFactor).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, SlotAdvertServeVO> queryAdvertOrientServeMap(Long slotId, List<Long> orientIds) {
        if (CollectionUtils.isEmpty(orientIds)) {
            return Collections.emptyMap();
        }
        // 查询广告位
        Slot slot = slotMapper.selectSlotById(slotId);
        if (null == slot) {
            return Collections.emptyMap();
        }
        // 查询配置列表
        List<AdvertOrientation> orients = advertOrientationService.selectListByIds(orientIds);
        // 查询广告
        Map<Long, Advert> advertMap = advertService.selectAdvertMapByIds(ListUtils.mapToList(orients, AdvertOrientation::getAdvertId));
        // 屏蔽了媒体的配置ID集合
        Set<Long> bannedOrientSet = advertBannedAppMapper.selectByAppId(slot.getAppId()).stream().map(AdvertBannedApp::getOrientId).collect(Collectors.toSet());
        // 查询广告定向媒体映射
        Map<Long, Set<Long>> orientAppMap = advertOrientationService.selectAdvertOrientAppMapByOrientIds(orientIds);
        // 查询广告定向广告位映射
        Map<Long, Set<Long>> orientSlotMap = advertOrientationService.selectAdvertOrientSlotMapByOrientIds(orientIds);
        // 查询流量包定向
        Map<Long, Set<Long>> trafficMap = trafficPackageService.selectTrafficSlotByOrientIds(orientIds);

        // 查询广告主余额预算
        List<Long> advertiserIds = advertMap.values().stream().map(Advert::getAdvertiserId).distinct().collect(Collectors.toList());
        Map<Long, Integer> advertiserBalanceMap = advertiserBalanceService.selectAdvertiserBalanceMap(advertiserIds);
        Map<Long, Integer> consumeOffsetMap = dspAdvertiserConsumeRecordService.batchConsumeOffsetForCrm(advertiserIds);
        Map<Long, Long> advertiserBudgetMap = advertiserBudgetService.selectBudgetMapByAccountIds(advertiserIds);

        // 构造接口返回结果
        Date today = DateUtil.beginOfDay(new Date());
        Integer hour = DateUtil.thisHour(true);
        List<SlotAdvertServeVO> serveList = orients.stream()
                .map(orient -> {
                    SlotAdvertServeVO advertVO = new SlotAdvertServeVO();
                    advertVO.setOrientId(orient.getId());

                    Advert advert = advertMap.get(orient.getAdvertId());
                    advertVO.setAdvertId(advert.getId());

                    // 广告主余额
                    Integer advertiserBalance = advertiserBalanceMap.get(advert.getAdvertiserId());
                    if (null != advertiserBalance && consumeOffsetMap.containsKey(advert.getAdvertiserId())) {
                        advertiserBalance -= consumeOffsetMap.get(advert.getAdvertiserId());
                    }
                    // 广告主预算
                    Long advertiserBudget = advertiserBudgetMap.get(advert.getAdvertiserId());

                    // 判断广告是否正常投放
                    advertVO.setCanServe(0);

                    if (bannedOrientSet.contains(orient.getId())) {
                        advertVO.setReason("广告已屏蔽该媒体");
                    } else if (!((CollectionUtils.isEmpty(trafficMap.get(orient.getId())) && CollectionUtils.isEmpty(orientAppMap.get(orient.getId())))
                            || CollUtil.contains(trafficMap.get(orient.getId()), slot.getId())
                            || (CollUtil.contains(orientAppMap.get(orient.getId()), slot.getAppId()) && CollectionUtils.isEmpty(orientSlotMap.get(orient.getId())))
                            || CollUtil.contains(orientSlotMap.get(orient.getId()), slot.getId()))) {
                        advertVO.setReason("广告未定向该媒体/广告位");
                    } else if (SwitchStatusEnum.isSwitchOff(advert.getServingSwitch())) {
                        advertVO.setReason("广告开关关闭");
                    } else if (SwitchStatusEnum.isSwitchOff(orient.getServingSwitch())) {
                        advertVO.setReason("广告定向配置开关关闭");
                    } else if (null != advertiserBudget && advertiserBudget <= 0) {
                        advertVO.setReason("广告主日预算为0");
                    } else if (null != advert.getDailyBudget() && advert.getDailyBudget() <= 0) {
                        advertVO.setReason("广告日预算为0");
                    } else if (null != orient.getDailyBudget() && orient.getDailyBudget() <= 0) {
                        advertVO.setReason("广告定向配置日预算为0");
                    } else if (today.before(advert.getStartServingDate()) || today.after(DateUtil.endOfDay(advert.getStopServingDate()))) {
                        advertVO.setReason("当前不在广告投放周期内");
                    } else if (!ServingHourEnum.contains(orient.getServingHour(), hour)) {
                        advertVO.setReason("当前不在广告投放时段内");
                    } else if (null == advertiserBalance || advertiserBalance < orient.getUnitPrice()) {
                        advertVO.setReason("广告主余额不足");
                    } else if (!isAdvertValid(advert.getAdvertStatus())) {
                        advertVO.setReason(AdvertStatusEnum.getDescByStatus(advert.getAdvertStatus()));
                    } else {
                        advertVO.setCanServe(1);
                        advertVO.setReason("");
                    }
                    return advertVO;
                }).collect(Collectors.toList());
        return serveList.stream().collect(Collectors.toMap(SlotAdvertServeVO::getOrientId, Function.identity()));
    }
}
