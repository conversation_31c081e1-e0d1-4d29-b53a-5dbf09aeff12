package com.ruoyi.framework.config;

import com.ruoyi.framework.interceptor.BlockInterceptor;
import com.ruoyi.framework.interceptor.ConsumerInterceptor;
import com.ruoyi.framework.interceptor.RequestThreadLocalInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.framework.interceptor.RepeatSubmitInterceptor;

/**
 * 通用配置
 *
 * <AUTHOR>
 */
@Configuration
public class ResourcesConfig implements WebMvcConfigurer
{
    @Autowired
    private RepeatSubmitInterceptor repeatSubmitInterceptor;

    @Autowired
    private RequestThreadLocalInterceptor requestThreadLocalInterceptor;

    @Autowired
    private ConsumerInterceptor consumerInterceptor;

    @Autowired
    private BlockInterceptor blockInterceptor;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        /** 本地文件上传路径 */
        registry.addResourceHandler(Constants.RESOURCE_PREFIX + "/**").addResourceLocations("file:" + RuoYiConfig.getProfile() + "/");
    }

    /**
     * 自定义拦截规则
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(repeatSubmitInterceptor).addPathPatterns("/**");
        registry.addInterceptor(blockInterceptor).addPathPatterns("/act/**", "/st/**");
        registry.addInterceptor(requestThreadLocalInterceptor).addPathPatterns("/act/hd/getAdvert", "/act/hd/eventReport","/act/nh/getAdvert", "/act/nh/stat", "/act/nh/redirect", "/act/init", "/act/nh/record");
        registry.addInterceptor(consumerInterceptor).addPathPatterns("/act/hd/getAdvert", "/act/hd/eventReport", "/act/nh/getAdvert", "/act/nh/stat", "/act/nh/redirect", "/act/init", "/act/nh/record");
    }

    /**
     * 跨域配置
     */
    @Bean
    public CorsFilter corsFilter()
    {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        // 设置访问源地址
        config.addAllowedOrigin("*");
        // 设置访问源请求头
        config.addAllowedHeader("*");
        // 设置访问源请求方法
        config.addAllowedMethod("*");
        // 对接口配置跨域设置
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}
