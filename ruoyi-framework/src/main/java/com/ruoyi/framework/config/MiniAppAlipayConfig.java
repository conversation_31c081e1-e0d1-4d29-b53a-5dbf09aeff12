package com.ruoyi.framework.config;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * 支付宝小程序支付配置
 * <AUTHOR>
 * @date 2023/4/24 15:21
 */
@Slf4j
@Component
public class MiniAppAlipayConfig {

    @Bean
    public AlipayClient alipayClient(){
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setAppId("2021003190644404");
        alipayConfig.setPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCVcVP4JC+onn0CUWj+Jz7I6583wcNU5IkYBJmfVTJ0sFZYojr5Zb6Gqv7t4pWMnrBWZ6AKJvjAnQHyzAUJ9T8+AwaK7LYrfe7ypbM5oJwPq5BFBJwJpY2MNWxlJVN7RSwbTRG2xktR7miv8lPePMbDm8mHunr0O2asgp72oavR0OU5ZxzQkdKmi9pcocLenSt4tEIYFNKMSz+3qD6OI1igUXtk+EcQw1DxNAZd9w12gBdkbuO0Zf5AcuiRqqiHpoYSqwx4FRaG9VRyme97OpodPj3muHlWWk7AC3BSrkb86smj+k0wKfqzVGjRaXEPYEdhnB4Jcdxie1buK8zy/AtLAgMBAAECggEAJpPoz0fYHbk8IBsP9XwAEzHwGh6k5AdS9KZLWfxOWB0lTSAkBTmko3ME+Dfjzzcv4j5B7Bz/Xgmp9RmBu35Z4xI44CgXJscNpgnsaFXzs3/gcFCuTzczgRxRbRLjqBDFQA73Ia2yx+W5lKID0Hzz+o7+yVD0aJPk/2eAzb4ScskI5jEHAxtl3wndsYyRmcN65JzlWXr2mh6BZemaLM/yoxzx3qzCJUrHb//nRnVj63o20zgG13k+4fIPTH/94sJRL92E33PgEykdbWgIN2tiMarbSZNJLLARCxSEIdknoUH5BhP753vVMw5QwLuhWZqOHSX9UGzX9bhqgcrBeoqc4QKBgQDy6cMxcWWfxogk9AzfTVf4FO8bwxU32CRAFwtkg4qO9zTzOzvBKQNC/ZxKLBNBgHL3r3r6XDfgbQgcNBtQSgqrr5AB8R+JInodLBDmAs7IMx/VIgz0Or1GEzg51gx8/ZPiWBarlaANDy6a8ZsvYh6VxRrTdaHJGKldbafReo/ekwKBgQCdfm3R8sMbzrzpMsyWPSa+35Twur1yYE4qvynwfaCDZxUh9/F41KPOuBL6xkrmL8MQbqrx6qeenBx8yz79KoJm9XrntZWIcaDDoV1XjxB9fk3JorSeMmIVnE7Kh+5dKcZrsvOr10BoWMSw7SVqHt4heF5j7pntioSlyih55GbbaQKBgQCaQ8JmX/F96oMDlMmn9mJrdj/zjYyoDmfXG8tsZe1UpcHGxlNIdFdR7+WVZTBFZOGeUp7y6CXELPn9j0nZT2w/ZBs8YHj6ih6q8BU7PYU8ttr8a8d2Qud43TPT5w+/QPrtv6uGBFvEb2PVmqJHETUDQE2tBQNdxn+oib8N1ZwQaQKBgEK6SV9VotLKUgHf0wyolhzy79Da3d9y7EtyORu6rmJeL3b37ShLt6ejF++GSj+i5tu0d9sNl7082VkKaAKtmxhyU4OOzO9FbV7VpI2vgMYa2Gxg8nPAVwxe8Icg9p4kT/xbkqAxEAnl6lgn8d03fNfjfuKKOk+Ji9AOif2zBt1ZAoGBAK1ukSeqKWYgD+d/rk6KZXJiWytv+pZH9t0DdtPJrwFT420FVeKBRUlyTef+5h4E9ZSQNylGsqF1L9nubvqGJj21bk739Z3bN4UbtrBHzhns+5AQ28j+QBC0c735YLoOhe1coXSwIuL/0yqq2A9MrlRG/coVm7rHVns0rEgXV8X/");
        alipayConfig.setAlipayPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1ZQLMVM/1yiCIDcZvAOyoDAuAvpttW8IJhEScXpabV9CZknMdJUGT6DJH9hqwx8Tv6aw8nvksRSBlTCyHh1rcFmqTRvuLE81nDCVCYzk1CR05EZ0UyIL0QqdPwMG3eEpoNGPxVDLJbLMlO6gu33t3cXvPUO2wiVBPh2Zxw+tYjKEoQIhWB82f2JY8X52sSowX6zENmTYs6+5H9qDrRlmgGb059a3/LHBZN6ZcYc4T3GCsTXdQz6r/QRBMBJrsMBFtJLllhAqYheK1sjWHmQHvPSMV7zPECSx3QE8bSgiMwLAcNYTsUFXLXEG9j9QiFqLOKO+Ul76yP2ZtZ4iN4eUhQIDAQAB");
        //构造client
        try {
            return new DefaultAlipayClient(alipayConfig);
        } catch (AlipayApiException e) {
            log.error("支付宝小程序支付配置异常,e:",e);
            return null;
        }
    }
}