package com.ruoyi.framework.interceptor;

import com.ruoyi.common.core.local.RequestThreadLocal;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.consumer.ConsumerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class ConsumerInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private ConsumerService consumerService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        RequestThreadLocal local = RequestThreadLocal.get();
        if (null == local.getAppId() || StringUtils.isEmpty(local.getDeviceId())) {
            return true;
        }

        // 生成consumerId
        Long consumerId = consumerService.getOrCreateConsumer(local.getAppId(), local.getDeviceId());
        local.setConsumerId(consumerId);
        // 计算分流hash值(用于uv分流)
        local.setShuntHash(Math.abs(new Long(local.getConsumerId() / 10000).hashCode()));
        return true;
    }
}
