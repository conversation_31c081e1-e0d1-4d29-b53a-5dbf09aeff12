package com.ruoyi.framework.interceptor;

import com.ruoyi.common.utils.ip.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.ruoyi.common.constant.BizConstants.IP_BLACKLIST;
import static com.ruoyi.common.constant.BizConstants.UA_BLACKLIST;

@Slf4j
@Component
public class BlockInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            String ip = IpUtils.getIpAddr(request);
            String userAgent = request.getHeader("User-Agent");
            // IP拦截
            if (IP_BLACKLIST.contains(ip)) {
                return false;
            }
            // UserAgent拦截
            if (UA_BLACKLIST.contains(userAgent)) {
                return false;
            }
        } catch (Exception e) {
            log.error("访问拦截异常", e);
        }
        return true;
    }
}
