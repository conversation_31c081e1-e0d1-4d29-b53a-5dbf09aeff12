package com.ruoyi.framework.interceptor;

import com.ruoyi.common.core.local.RequestThreadLocal;
import com.ruoyi.common.utils.DeviceIdUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.domain.adengine.AppCacheDto;
import com.ruoyi.system.domain.adengine.SlotCacheDto;
import com.ruoyi.system.service.engine.cache.AppCacheService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class RequestThreadLocalInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private AppCacheService appCacheService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        RequestThreadLocal.clear();
        RequestThreadLocal local = RequestThreadLocal.get();

        local.setRequest(request);
        local.setResponse(response);

        local.setIp(IpUtils.getIpAddr(request));
        local.setAppKey(request.getParameter("appKey"));
        local.setDeviceId(request.getParameter("deviceId"));
        local.setSrid(request.getParameter("srid"));
        local.setSlotId(NumberUtils.parseLong(request.getParameter("slotId")));
        local.setActivityId(NumberUtils.parseLong(request.getParameter("activityId")));
        local.setPluginId(NumberUtils.parseLong(request.getParameter("pluginId")));
        local.setUserAgent(request.getHeader("User-Agent"));
        setAppInfo(local);
        setSlotInfo(local);
        setDeviceId(local);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        RequestThreadLocal.remove();
    }

    /**
     * 设置媒体信息
     */
    private void setAppInfo(RequestThreadLocal local) {
        AppCacheDto app = appCacheService.getAppCache(local.getAppKey());
        if (null != app) {
            local.setAppId(app.getId());
            local.setAccountId(app.getAccountId());
        }
    }

    /**
     * 查询广告位信息
     */
    private void setSlotInfo(RequestThreadLocal local) {
        SlotCacheDto slot = slotCacheService.getSlotCache(local.getSlotId());
        if (null != slot) {
            local.setDomainConfig(slot.getDomainConfig());
        }
    }

    /**
     * 设置deviceId
     */
    private void setDeviceId(RequestThreadLocal local) {
        if (StringUtils.isNotBlank(local.getDeviceId())) {
            // deviceId过长情况下的处理
            if (local.getDeviceId().length() > 128) {
                local.setDeviceId(Md5Utils.hash(local.getDeviceId()));
            }
            return;
        }

        // 生成设备号
        local.setDeviceId(DeviceIdUtils.generate(local.getRequest()));
    }
}
