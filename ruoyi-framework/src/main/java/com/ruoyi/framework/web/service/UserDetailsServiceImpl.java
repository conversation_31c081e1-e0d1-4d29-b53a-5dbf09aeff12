package com.ruoyi.framework.web.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.account.AccountStatusEnum;
import com.ruoyi.common.exception.BaseException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.oa.user.UserEntity;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.oa.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.ruoyi.common.enums.account.AccountMainType.AGENT;
import static com.ruoyi.common.enums.account.AccountMainType.isAdvertiser;
import static com.ruoyi.common.enums.account.AccountMainType.isAgent;
import static com.ruoyi.common.enums.account.AccountMainType.isCrmUser;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private UserService userService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        int splitter = username.lastIndexOf('#');
        Integer mainType = Integer.valueOf(username.substring(splitter + 1));
        username = username.substring(0, splitter);

        Account param = new Account();
        param.setMainType(mainType);
        param.setEmail(username);
        // CRM不支持公司名登录
        if (!isCrmUser(mainType)) {
            param.setCompanyName(username);
        }
        // 广告主/代理商不支持手机号登录
        if (!isAdvertiser(mainType) && !isAgent(mainType)) {
            param.setPhone(username);
        }
        Account account = accountService.selectAccountForLogin(param);
        if (null == account && isAdvertiser(mainType)) {
            // 查询是不是代理商
            param.setMainType(AGENT.getType());
            account = accountService.selectAccountForLogin(param);
            // 注意mainType已经改变
        }

        if (StringUtils.isNull(account)) {
            log.info("登录用户：{} 不存在.", username);
            throw new UsernameNotFoundException("登录用户：" + username + " 不存在");
        } else {
            // 账号状态判断
            switch (AccountStatusEnum.getByStatus(account.getStatus())) {
                case NORMAL:
                    break;
                case DISABLE:
                case DISABLE_AND_NO_AUTHORITY:
                    log.info("登录用户：{} 已被停用.", username);
                    throw new BaseException("对不起，您的账号：" + username + " 已停用");
                case NO_AUTHORITY:
                    log.info("登录用户：{} 无访问权限.", username);
                    throw new BaseException("对不起，您的账号：" + username + " 无访问权限");
                case AGENT_DISABLE:
                    log.info("登录用户：{} 已被代理商禁用.", username);
                    throw new BaseException("您的账号已被禁用，请联系代理商进行确认");
                default:
                    log.info("登录用户：{} 无法访问系统.", username);
                    throw new BaseException("对不起，您的账号：" + username + " 无法访问系统");
            }
        }

        LoginUser user = new LoginUser();
        user.setCrmAccountId(account.getId());
        user.setEmail(account.getEmail());
        user.setMainType(account.getMainType());
        user.setAdminType(account.getAdminType());
        user.setPassword(account.getPasswd());

        if (isCrmUser(account.getMainType())) {
            user.setUserName(account.getContact());
            //crm账号，查询oa账号id
            UserEntity userEntity = userService.selectByEmail(account.getEmail());
            if(Objects.nonNull(userEntity)){
                user.setUserId(userEntity.getId());
            }
        } else {
            user.setUserName(account.getCompanyName());
        }
        user.setPermissions(permissionService.getMenuPermission(user));
        return user;
    }
}
