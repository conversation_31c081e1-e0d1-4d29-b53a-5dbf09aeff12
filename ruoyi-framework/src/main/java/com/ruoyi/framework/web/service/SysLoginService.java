package com.ruoyi.framework.web.service;

import cn.hutool.extra.mail.MailUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.enums.account.SendEmailType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.exception.user.UserPasswordNotMatchException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.account.AccountMainType.isCrmUser;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SysLoginService {

    private static final String EMAIL_VERIFICATION_TEMPLATE = "<p>尊敬的用户：</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;欢迎您加入诺禾平台，您正在进行注册邮箱验证，" +
            "请于10分钟内在页面中输入最新验证码：<font color=\"red\"><b>%s</b></font>，完成邮箱绑定。</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;谢谢！</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;此邮件由系统发出，请勿直接回复。 如有疑问，请联系客服。</p>";
    private static final String QUICKAPP_EMAIL_VERIFICATION_TEMPLATE = "<p>尊敬的用户：</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;欢迎您加入花百科，您正在进行注册邮箱验证，" +
            "请于10分钟内在页面中输入最新验证码：<font color=\"red\"><b>%s</b></font>，完成邮箱绑定。</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;谢谢！</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;此邮件由系统发出，请勿直接回复。 如有疑问，请联系客服。</p>";

    private static final String EMAIL_RESETPWD_TEMPLATE = "<p>尊敬的用户：</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;欢迎您使用诺禾平台，您正在进行密码重置，" +
            "请于10分钟内在页面中输入最新验证码：<font color=\"red\"><b>%s</b></font>，完成密码重置。</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;谢谢！</p>\n" +
            "<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;此邮件由系统发出，请勿直接回复。 如有疑问，请联系客服。</p>";

    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    /**
     * 登录验证
     *
     * @param mainType 主体类型
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(Integer mainType, String username, String password, String code, String uuid) {
        // CRM登录需要验证码
        if (isCrmUser(mainType)) {
            String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
            String captcha = redisCache.getCacheObject(verifyKey);
            redisCache.deleteObject(verifyKey);
            if (captcha == null) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
//            if (!StrUtil.equalsIgnoreCase(code, captcha)) {
//                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
//                throw new CaptchaException();
//            }
        }

        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(username + "#" + mainType, password));
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new CustomException(e.getMessage());
            }
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        // 生成token
        return tokenService.createToken(loginUser);
    }
//
//    /**
//     * 记录登录信息
//     */
//    public void recordLoginInfo(SysUser user)
//    {
//        user.setLoginIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
//        user.setLoginDate(DateUtils.getNowDate());
//        userService.updateUserProfile(user);
//    }

    /**
     * 发送邮件验证码
     *
     * @param email 邮箱
     * @param type 类型
     */
    public void sendVerificationEmail(String email, SendEmailType type) {
        if (StringUtils.isBlank(email)) {
            return;
        }

        String emailMD5 = Md5Utils.hash(email);

        RedisLock lock = redisAtomicClient.getLock(Constants.REPEAT_KEY + emailMD5, 60);
        if (lock == null) {
            throw new CustomException("验证码邮件已发送，请稍后再试");
        }

        // 生成并缓存验证码（10分钟）
        String key = Constants.CAPTCHA_VERIFY_KEY + emailMD5;
        String code = RandomStringUtils.randomNumeric(6);
        redisCache.setCacheObject(key, code, Constants.EMAIL_VERIFICATION_EXPIRATION, TimeUnit.MINUTES);

        // 发送邮件
        String result;
        try {
            switch (type) {
                case RESETPWD:
                    result = MailUtil.send(email, "诺禾平台邮箱验证", String.format(EMAIL_RESETPWD_TEMPLATE, code) , true);
                    break;
                case QUICK_APP_REGISTER:
                    result = MailUtil.send(email, "花百科快应用邮箱验证", String.format(QUICKAPP_EMAIL_VERIFICATION_TEMPLATE, code) , true);
                    break;
                case REGISTER:
                default:
                    result = MailUtil.send(email, "诺禾平台邮箱验证", String.format(EMAIL_VERIFICATION_TEMPLATE, code) , true);
                    break;
            }
            log.info("发送邮件验证码, email={}, code={}, result={}", email, code, result);
        } catch (Exception e) {
            log.warn("发送邮件验证码异常, email={}", email, e);
        }
    }
}
