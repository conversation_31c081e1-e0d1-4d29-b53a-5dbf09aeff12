package com.ruoyi.framework.web.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TokenService {
    // 令牌自定义标识
    @Value("${token.header}")
    private String header;

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = getToken(request);
        if (StringUtils.isNotBlank(token)) {
            try {
                String content = SecurityUtils.decrypt(token);
                LoginUser loginUser = (LoginUser) JSONObject.parseObject(content, LoginUser.class);
                if(!Objects.equals(SpringEnvironmentUtils.getCurrentEnv(),loginUser.getEnv())){
                    return null;
                }

                return verifyToken(loginUser);
            } catch (Exception e) {
                if (!StrUtil.contains(request.getHeader("User-Agent"), "Windows NT")) {
                    log.error("获取LoginUser异常, uri={}, ip={}, userAgent={}, token={}", request.getRequestURI(),
                            IpUtils.getIpAddr(request), request.getHeader("User-Agent"), token, e);
                }
            }
        }
        return null;
    }

    private LoginUser verifyToken(LoginUser loginUser){
        long expireTime = loginUser.getTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime <= currentTime) {
            return null;
        }
        return loginUser;
    }

    /**
     * 创建令牌
     *
     * @param loginUser 用户信息
     * @return 令牌
     */
    public String createToken(LoginUser loginUser) {
        try {
            loginUser.setTime(System.currentTimeMillis() + 24 * 60 * 60 * 1000L);
            loginUser.setEnv(SpringEnvironmentUtils.getCurrentEnv());
            return SecurityUtils.encrypt(JSONObject.toJSONString(loginUser));
        } catch (Exception e) {
            log.error("token加密异常,e:",e);
            return null;
        }
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(header);
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }
}
