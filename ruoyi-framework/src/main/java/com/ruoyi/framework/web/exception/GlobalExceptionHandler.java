package com.ruoyi.framework.web.exception;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.exception.BaseException;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.exception.DemoModeException;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.firewall.RequestRejectedException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler
{
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 基础异常
     */
    @ExceptionHandler(BaseException.class)
    public Result baseException(BaseException e)
    {
        return ResultBuilder.fail(e.getMessage());
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(CustomException.class)
    public Result businessException(CustomException e)
    {
        if (StringUtils.isNull(e.getCode()))
        {
            return ResultBuilder.fail(e.getMessage());
        }
        return ResultBuilder.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public Result handlerNoFoundException(Exception e)
    {
        log.error(e.getMessage(), e);
        return ResultBuilder.fail(HttpStatus.NOT_FOUND, "路径不存在，请检查路径是否正确");
    }

    @ExceptionHandler(AccessDeniedException.class)
    public Result handleAuthorizationException(AccessDeniedException e)
    {
        log.error(e.getMessage());
        return ResultBuilder.fail(HttpStatus.FORBIDDEN, "没有权限，请联系管理员授权");
    }

    @ExceptionHandler(AccountExpiredException.class)
    public Result handleAccountExpiredException(AccountExpiredException e)
    {
        log.error(e.getMessage(), e);
        return ResultBuilder.fail(e.getMessage());
    }

    @ExceptionHandler(UsernameNotFoundException.class)
    public Result handleUsernameNotFoundException(UsernameNotFoundException e)
    {
        log.error(e.getMessage(), e);
        return ResultBuilder.fail(e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public Result handleException(Exception e) {
        if (StringUtils.containsAny(e.getMessage(), "not supported", "Required request body is missing")) {
            HttpServletRequest request = ServletUtils.getRequest();
            log.error("请求异常, msg={}, uri={}, ip={}, userAgent={}, referer={}, param={}", e.getMessage(), request.getRequestURI(),
                    IpUtils.getIpAddr(request), request.getHeader("User-Agent"), request.getHeader("referer"),
                    JSON.toJSONString(request.getParameterMap()));
        } else {
            log.error(e.getMessage(), e);
        }
        return ResultBuilder.fail("系统异常，请联系管理人员");
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public Result validatedBindException(BindException e)
    {
        log.error(e.getMessage(), e);
        String message = e.getAllErrors().get(0).getDefaultMessage();
        return ResultBuilder.fail(ErrorCode.ARGS);
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object validExceptionHandler(MethodArgumentNotValidException e)
    {
        log.error(e.getMessage(), e);
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return ResultBuilder.fail(message);
    }

    /**
     * 演示模式异常
     */
    @ExceptionHandler(DemoModeException.class)
    public Result demoModeException(DemoModeException e)
    {
        return ResultBuilder.fail("演示模式，不允许操作");
    }


    @ExceptionHandler(RequestRejectedException.class)
    public Result handleRequestRejectedException(RequestRejectedException ex) {
        log.error("Invalid request:{},ip:{},path:{}",ex.getMessage(),IpUtils.getIpAddr(ServletUtils.getRequest()),ServletUtils.getRequest().getRequestURI());
        return ResultBuilder.fail("系统繁忙");
    }
}
